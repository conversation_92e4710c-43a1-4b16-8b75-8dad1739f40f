import{r as a}from"./critical-DVX9Inzy.js";const t=({ageInMonths:t,onChange:o,onCommit:n,onValidationError:i})=>{const e=120,[r,s]=a.useState(t<=12?"months":"years"),[h,m]=a.useState((()=>0===t?"":"years"===r?Math.floor(t/12).toString():t.toString()));return a.useEffect((()=>{if(0===t)m("");else{const a="years"===r?Math.min(e,Math.max(0,Math.floor(t/12))):Math.min(12,Math.max(0,t));m(a.toString())}}),[t,r]),{unit:r,inputValue:h,handleUnitChange:a=>{const t=parseFloat(h);if(!isNaN(t))if("months"===a&&"years"===r){const a=12*t,i=Math.min(12,Math.max(0,a));m(i.toString()),o(i),n(i)}else if("years"===a&&"months"===r){const a=Math.floor(t/12),i=Math.min(e,Math.max(1,a));m(i.toString()),o(12*i),n(12*i)}s(a)},handleChange:a=>{m(a);const t=parseFloat(a);if(!isNaN(t)){let a=t;"months"===r?(a=Math.min(12,Math.max(0,t)),o(a)):(t>e?(i?.("Idade máxima aceita é 120 anos. Por favor, revise o valor inserido."),a=e,m(a.toString())):a=Math.min(e,Math.max(0,t)),o(12*a))}},handleBlur:()=>{const a=parseFloat(h);if(!isNaN(a)){let t=a;"months"===r?(t=Math.min(12,Math.max(0,a)),m(t.toString()),n(t)):(a>e?(i?.("Idade máxima aceita é 120 anos. Por favor, revise o valor inserido."),t=e):t=Math.min(e,Math.max(0,a)),m(t.toString()),n(12*t))}}}};export{t as u};
