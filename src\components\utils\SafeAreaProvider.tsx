import React, { useEffect } from 'react';
import { useSafeAreaCSS } from '@/hooks/useSafeAreas';
import { isCapacitor } from '@/utils/capacitorUtils';

/**
 * Componente que aplica safe areas globalmente via CSS custom properties
 */
export const SafeAreaProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { cssVars, isLoading } = useSafeAreaCSS();

  useEffect(() => {
    // Adicionar classe 'capacitor' ao body se estivermos no Capacitor
    if (isCapacitor()) {
      document.body.classList.add('capacitor');
    }

    if (!isCapacitor() || isLoading) return;

    // Aplicar CSS custom properties no :root
    const root = document.documentElement;
    Object.entries(cssVars).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    console.log('🛡️ Safe areas CSS aplicadas:', cssVars);

    // Cleanup não é necessário pois as propriedades CSS ficam ativas
  }, [cssVars, isLoading]);

  // Detectar teclado virtual e adicionar classe
  useEffect(() => {
    if (!isCapacitor()) return;

    const handleResize = () => {
      const viewportHeight = window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.screen.height;

      // Se o viewport é significativamente menor que a tela, provavelmente o teclado está aberto
      const keyboardOpen = viewportHeight < windowHeight * 0.75;

      if (keyboardOpen) {
        document.body.classList.add('keyboard-open');
      } else {
        document.body.classList.remove('keyboard-open');
      }
    };

    window.addEventListener('resize', handleResize);
    window.visualViewport?.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.visualViewport?.removeEventListener('resize', handleResize);
    };
  }, []);

  return <>{children}</>;
};
