import{j as e}from"./radix-core-6kBL75b5.js";import{r as t,b as s}from"./critical-DVX9Inzy.js";import{c as a,s as r,u as n,a as i,b as o,d as l,m as d,X as c,M as m,T as u,B as g,L as x,I as h,D as p,e as b,f,g as v,A as y,h as j}from"./index-Dq2DDcRF.js";import{m as w,d as N,u as k,a as C,H as S,T,f as E,M,b as _,S as D,c as I,e as R,g as U,h as $}from"./secureStorage-CKSgx_r0.js";import{a as L,u as q}from"./router-BAzpOxbo.js";import{A}from"./index-BBVJGnSw.js";import{P as O}from"./plus-IZJFu_xe.js";import{T as B}from"./trash-2-CQoB25P5.js";import{S as W}from"./send-BMd7KbiP.js";import{f as z}from"./date-vendor-BOcTQe0E.js";import{p as H}from"./pt-BR-a_BmBHfW.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./zoom-out-9hI107OK.js";import"./download-C_U0syau.js";import"./book-open-CQJyFt3x.js";import"./chevron-left-BRVekuj0.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=a("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),F=a("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),Q=new Map,G=s.memo((({content:s,isStreaming:a,messageId:r,isUser:n,isLoading:i,onResourcesClick:o,onResourcesDataDetected:l})=>{const{content:d,ragData:c}=t.useMemo((()=>{if(n||a)return{content:s,ragData:null};let e=(e=>{const t=e.match(/\*\*RAG_RESOURCES_BUTTON:(.*?)\*\*/)||e.match(/RAG_RESOURCES_BUTTON:(.*?)(?=\n|$)/);if(t)try{return{isValid:!0,data:JSON.parse(t[1])}}catch(s){return{isValid:!1,data:null}}return e.match(/\*\*?RAG_RESOURCES_BUTTON:(.*)$/s),{isValid:!1,data:null}})(s).data;return{content:s.replace(/\*\*RAG_RESOURCES_BUTTON:.*?\*\*/g,"").replace(/RAG_RESOURCES_BUTTON:.*?(?=\n|$)/g,"").replace(/\*\*?RAG_RESOURCES_BUTTON:.*$/gms,"").replace(/\n{3,}/g,"\n\n").trim().replace(/\$~~~SUGGESTIONS\$[\s\S]*?(?=\n\n|\n$|$)/g,"").trim(),ragData:e}}),[s,n,a]);t.useEffect((()=>{if(c&&l){const e={directMentions:c.directMentions||[],suggestions:c.suggestions||[],conductMentions:c.conductMentions||[],conductSuggestions:c.conductSuggestions||[],title:`Recursos encontrados (${c.count})`};l(e)}}),[c,l]);const m=t.useMemo((()=>{const e=`${r}_${d.length}_${a}`;if(Q.has(e))return Q.get(e);const t=d.replace(/^---+$/gm,"").replace(/^\*\*\*+$/gm,"").replace(/^___+$/gm,"").replace(/\n\s*\n\s*\n/g,"\n\n").trim(),s=R(t,a||!1);return Q.set(e,s),s}),[d,r,a]);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"leading-relaxed text-sm",style:{wordBreak:"break-word",overflowWrap:"break-word"},dangerouslySetInnerHTML:{__html:m}}),!n&&!a&&c&&c.count>0&&e.jsx("div",{className:"mt-3 pt-2 border-t border-gray-100 dark:border-gray-700",children:e.jsx("div",{className:"text-center",children:e.jsxs("button",{onClick:o,className:"inline-flex items-center gap-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 text-xs font-medium hover:bg-green-50 dark:hover:bg-green-900/20 px-2 py-1.5 rounded-lg transition-all duration-200",children:[e.jsx("span",{className:"text-sm",children:"📚"}),e.jsxs("span",{children:["Recursos da plataforma citados (",c.count,")"]})]})})})]})}),((e,t)=>e.content===t.content&&e.isStreaming===t.isStreaming&&e.messageId===t.messageId&&e.isUser===t.isUser&&e.isLoading===t.isLoading&&e.onResourcesClick===t.onResourcesClick&&e.onResourcesDataDetected===t.onResourcesDataDetected)),V=({className:s=""})=>{const[a,R]=t.useState(!1),[V,J]=t.useState(!1),[K,Y]=t.useState(1),[X,Z]=t.useState(!0),[ee,te]=t.useState(""),[se,ae]=t.useState(!1),[re,ne]=t.useState(!1),ie=L(),oe=q(),{user:le}=n();oe.pathname;const{questionNumber:de,totalQuestions:ce,sessionTitle:me,isInQuestionSession:ue,questionId:ge,specialty:xe,theme:he,focus:pe}=i(),{currentQuestion:be,sessionId:fe}=o(),[ve,ye]=t.useState(!1),[je,we]=t.useState(""),[Ne,ke]=t.useState(!1),[Ce,Se]=t.useState(""),[Te,Ee]=t.useState(null),[Me,_e]=t.useState(!1),[De,Ie]=t.useState(null),[Re,Ue]=t.useState(!1),[$e,Le]=t.useState(null),[qe,Ae]=t.useState(!1),[Oe,Be]=t.useState(!1),[We,ze]=t.useState(null),[He,Pe]=t.useState(null),[Fe,Qe]=t.useState(!1),[Ge,Ve]=t.useState([]),[Je,Ke]=t.useState(!1),{toast:Ye}=l(),Xe=t.useRef(null),{currentThreadId:Ze,threads:et,loadMessages:tt,saveMessage:st,createNewThread:at,findOrCreateContextualThread:rt,getConversationHistory:nt,loadThreads:it,historyIsLoading:ot,deleteThread:lt,deleteAllThreads:dt}=k(),ct=t.useCallback(((e,t)=>{}),[]),mt=t.useCallback(((e,t)=>{Le({type:"single",threadId:e,threadTitle:t}),Ue(!0)}),[]),ut=t.useCallback((()=>{Le({type:"all"}),Ue(!0)}),[]),gt=t.useCallback((async()=>{if($e&&!qe){Ae(!0);try{"single"===$e.type&&$e.threadId?await lt($e.threadId):"all"===$e.type&&await dt(),await it(!0),Ue(!1),Le(null)}catch(e){}finally{Ae(!1)}}}),[$e,qe,lt,dt,it]);t.useEffect((()=>{ue&&fe&&me?We&&We.sessionId===fe&&We.sessionTitle===me&&We.isInQuestionSession===ue||ze({sessionId:fe,sessionTitle:me,isInQuestionSession:ue}):ue||We&&ze(null)}),[ue,fe,me,We,Ze,He]);const xt=t.useMemo((()=>ue&&fe&&me?{sessionId:fe,sessionTitle:me,questionIndex:de-1,totalQuestions:ce,questionId:ge||"",specialty:xe||"",theme:he||"",focus:pe||""}:null),[ue,fe,me,de,ce,ge,xe,he,pe]),{messages:ht,sendMessage:pt,isLoading:bt,clearMessages:ft,setMessages:vt}=C({currentThreadId:He||Ze,saveToHistory:st,createNewThread:at,getConversationHistory:nt}),{messages:yt,sendContextualMessage:jt,isLoading:wt,clearMessages:Nt,setMessages:kt}=(({currentThreadId:e,saveToHistory:s,createNewThread:a,findOrCreateContextualThread:n,getConversationHistory:i,questionContext:o})=>{const[l,d]=t.useState([]),[c,m]=t.useState(!1),[u,g]=t.useState(!1),[x,h]=t.useState(null),p=t.useRef(null),b=t.useCallback((e=>{d((t=>[...t,e]))}),[]),f=t.useCallback(((e,t)=>{d((s=>s.map((s=>s.id===e?{...s,...t}:s))))}),[]),v=t.useCallback((async(e,t,a,r,l)=>{if(!e.trim()||c)return;const d=t||o;return d?await w("sendContextualMessage",(async()=>{p.current&&p.current.abort(),p.current=new AbortController;let t=r;if(!t&&(t=await n(d.sessionTitle,d.sessionId),!t))return void N.error("sendContextualMessage","Failed to find or create session thread");const o={id:Date.now().toString(),content:e.trim(),isUser:!0,timestamp:new Date};if(b(o),!(await s({id:o.id,content:o.content,isUser:!0,timestamp:o.timestamp},t)))return N.error("saving user message to history","Failed to save"),h("Erro ao salvar mensagem"),void m(!1);await new Promise((e=>setTimeout(e,100)));const l=await i(t);m(!0),g(!0),h(null),await y(e,d,t,l,a)})):void 0}),[c,b,f,e,a,s,i,o,n]),y=async(e,t,a,n,i)=>{const o=(Date.now()+1).toString();b({id:o,content:"",isUser:!1,timestamp:new Date,isStreaming:!0,isThinking:!0});try{const d=r.supabaseUrl,c=r.supabaseKey,m={message:e,userId:i,conversationHistory:n,questionContext:t,image_url:imageUrl},u=new AbortController,g=setTimeout((()=>u.abort()),3e4),x=await fetch(`${d}/functions/v1/dr-will-question-context`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`},body:JSON.stringify(m),signal:p.current?.signal});if(clearTimeout(g),!x.ok){const e=await x.text();throw new Error(`API error: ${x.status} - ${e}`)}const h=x.body?.getReader();if(!h)throw new Error("Response stream não disponível");const b=new TextDecoder;let v="",y="",j=!1;for(;;){const{done:e,value:t}=await h.read();if(e)break;const s=b.decode(t,{stream:!0}).split("\n");for(const a of s){if(""===a.trim())continue;if(!a.startsWith("data: "))continue;const e=a.slice(6);if("[DONE]"!==e)try{const t=JSON.parse(e);if(t.done)break;if(t.content&&(!0===t.isThinking?(v+=t.content,f(o,{content:v,isStreaming:!0,isThinking:!0})):(j||(j=!0),y+=t.content,f(o,{content:y,isStreaming:!0,isThinking:!1}))),t.error)throw new Error(t.error)}catch(l){}}}f(o,{content:y,isStreaming:!1,isThinking:!1}),y&&a&&(await s({id:o,content:y,isUser:!1,timestamp:new Date},a)||N.error("saving AI response to history","Failed to save"))}catch(d){let e="Erro inesperado. Tente novamente.";d.message?.includes("API error")?e="Erro de conexão com Dr. Will. Verifique sua internet e tente novamente.":d.message?.includes("timeout")?e="Dr. Will está demorando para responder. Tente uma pergunta mais simples.":d.message?.includes("rate limit")&&(e="Muitas perguntas simultâneas. Aguarde um momento antes de tentar novamente."),f(o,{content:`❌ **Erro**: ${e}\n\n💡 **Dicas para tentar novamente:**\n- Verifique sua conexão com a internet\n- Tente uma pergunta mais específica sobre a questão\n- Aguarde alguns segundos antes de tentar novamente\n\nSe o problema persistir, entre em contato com o suporte.`,isStreaming:!1}),h(e)}finally{m(!1),g(!1)}},j=t.useCallback((()=>{d([]),h(null)}),[]),k=t.useCallback((e=>{d(e)}),[]),C=t.useCallback((()=>{p.current&&p.current.abort()}),[]);return{messages:l,isLoading:c,isStreaming:u,error:x,sendContextualMessage:v,clearMessages:j,setMessages:k,cancelRequest:C,addMessage:b,updateMessage:f}})({currentThreadId:He||Ze,saveToHistory:st,createNewThread:at,findOrCreateContextualThread:rt,getConversationHistory:nt,questionContext:xt}),Ct=t.useRef(null),St=t.useRef({}),Tt=t.useRef(null),Et=()=>se?yt:ht,Mt=()=>se?wt:bt,_t=e=>e.includes("📚")&&(e.includes("Sessão Contextual")||e.includes("(")&&e.includes("q)")),Dt=e=>e?.metadata&&"object"==typeof e.metadata&&e.metadata.sessionId,It=()=>oe.pathname.startsWith("/questions/"),Rt=()=>It()&&ue&&fe,Ut=e=>{if(!e)return"Sessão";let t=e.replace("📚 ","");return t=t.replace(" - Sessão Contextual",""),t=t.replace(/\s*\(\d+q\)$/,""),t||"Sessão"},$t=t.useCallback((async()=>{if(le?.id)try{const e=new Date;e.setDate(e.getDate()-7);const{data:t,error:s}=await r.from("pedbook_chat_threads").select("id, title, created_at").eq("user_id",le.id).lt("created_at",e.toISOString());if(s)return;if(t&&t.length>0){const e=t.filter((e=>_t(e.title||"")));if(e.length>0){const t=e.map((e=>e.id));await r.from("pedbook_chat_history").delete().in("thread_id",t),await r.from("pedbook_chat_threads").delete().in("id",t)}}}catch(e){}}),[le?.id]);t.useEffect((()=>{if(le?.id){const e=setTimeout((()=>{$t()}),5e3);return()=>clearTimeout(e)}}),[le?.id,$t]),t.useEffect((()=>{a&&V&&le?.id&&it(),a&&He&&!Rt()&&Pe(null)}),[a,V,le?.id,it,He,oe.pathname]),t.useEffect((()=>{Q.size>100&&Q.clear()}),[Et().length]),t.useEffect((()=>{se?ft():Nt()}),[se,ft,Nt]),t.useEffect((()=>{se&&(ae(!1),ne(!0));const e=He?et?.find((e=>e.id===He)):null;e&&(_t(e.title||"")||Dt(e))&&Pe(null)}),[se,He,et]),t.useEffect((()=>{(async()=>{if(Ze&&!V)try{const e=await tt(Ze);if(e&&e.length>0){const t=e.map((e=>({id:e.id,content:e.content,isUser:e.isUser,timestamp:e.timestamp,isStreaming:!1,images:e.images,image_url:e.image_url})));vt(t),kt(t)}}catch(e){}})()}),[Ze,V,tt,vt,kt]),t.useEffect((()=>{const e=e=>{e.detail&&e.detail.mermaidCode&&(we(e.detail.mermaidCode),ye(!0))};if(document.addEventListener("openMermaidModal",e),window.expandMermaidDiagram=e=>{const t=document.getElementById(e+"-code");if(t)try{const e=JSON.parse(t.textContent||"");return we(e),void ye(!0)}catch(a){}const s=document.querySelectorAll('[id^="mermaid-"]');if(s.length>0){const e=Array.from(s).pop()?.id;if(e){const t=document.getElementById(e+"-code");if(t)try{const e=JSON.parse(t.textContent||"");return we(e),void ye(!0)}catch(a){}}}},void 0!==window.mermaid){const e=document.documentElement.classList.contains("dark");window.mermaid.initialize({startOnLoad:!0,theme:e?"dark":"default",themeVariables:e?{primaryColor:"#8b5cf6",primaryTextColor:"#f3f4f6",primaryBorderColor:"#6366f1",lineColor:"#9ca3af",background:"#1f2937",mainBkg:"#374151",secondBkg:"#4b5563",tertiaryColor:"#6b7280"}:{primaryColor:"#8b5cf6",primaryTextColor:"#1f2937",primaryBorderColor:"#6366f1",lineColor:"#6b7280"}})}return()=>{document.removeEventListener("openMermaidModal",e)}}),[]);const Lt=t.useRef(""),qt=t.useRef(!1),At=t.useRef(0);t.useEffect((()=>{const e=Et();if(!a||V||0===e.length)return;const t=e[e.length-1];t.isUser?(Lt.current=t.id,qt.current=!1,At.current=0,setTimeout((()=>{const e=St.current[t.id],s=Tt.current;if(e&&s){const t=e.offsetTop;s.scrollTo({top:t-80,behavior:"smooth"})}}),100)):!t.isStreaming||t.isUser||qt.current?t.isStreaming||t.isUser||!t.content||(qt.current=!0):setTimeout((()=>{const e=St.current[t.id],s=Tt.current;if(e&&s&&!qt.current){const t=e.offsetTop-80,a=s.scrollTop;if(Math.abs(a-t)<=30)return void(qt.current=!0);s.scrollTo({top:t,behavior:"smooth"})}}),100)}),[Et(),a,V,se]);const Ot=t.useCallback((e=>{Ie(e)}),[]),Bt=t.useCallback((()=>{De&&_e(!0)}),[De]);t.useEffect((()=>(window.openMedicationDialog=Bt,()=>{delete window.openMedicationDialog})),[Bt]);const Wt=async()=>{if(!ee.trim()&&0===Ge.length)return;if(Mt())return;if(It())return void Ye({title:"Dr. Will indisponível",description:"Este recurso está disponível apenas na plataforma MedEvo. Use o Dr. Will na página principal.",variant:"default"});if(se)return void Ye({title:"Modo contextual desabilitado",description:"Use o Dr. Will no modo geral para suas perguntas médicas.",variant:"default"});let e=le?.id;if(e||(e=localStorage.getItem("auth_user_id")),!e)return void ie("/");const t=ee;let s=[];if(Ge.length>0)try{Ke(!0);for(let t=0;t<Ge.length;t++){const a=Ge[t],r=U(e,a.file.name),n=await $("chat-images",r,a.file);s.push(n)}Ge.forEach((e=>{URL.revokeObjectURL(e.url)})),Ve([])}catch(a){return void Ye({title:"Erro ao enviar imagem",description:a.message||"Erro desconhecido",variant:"destructive"})}finally{Ke(!1)}te(""),j.floatingChatUserState({user:le?{id:le.id,email:le.email}:null,hasUser:!!le,userId:e,userType:typeof le,usedFallback:e!==le?.id}),j.floatingChatHooksState({currentThreadId:Ze,activeThreadId:He,hasThreads:!!et,threadsCount:et?.length||0,authState:{localStorage_userId:localStorage.getItem("auth_user_id"),localStorage_profile:localStorage.getItem("auth_profile")?"exists":"missing"}});try{const a=Rt();if(se&&!a)return ae(!1),ne(!1),He&&Pe(null),void(await pt(t,e,null,!0,s.length>0?s:void 0));if(He&&!a)return Pe(null),void(await pt(t,e,null,!0,s.length>0?s:void 0));const r=He?et?.find((e=>e.id===He)):null;if(r&&(_t(r.title||"")||Dt(r))&&!a)Pe(null),await pt(t,e,null,!0,s.length>0?s:void 0);else if(se&&a&&be){const a={questionNumber:de,totalQuestions:ce,sessionTitle:me,sessionId:fe||"",questionId:be.id,specialty:xe,theme:he,focus:pe,statement:be.statement||be.question_content,alternatives:be.alternatives||be.response_choices?.map(((e,t)=>({text:e,letter:String.fromCharCode(65+t)}))),correctAnswer:be.correct_answer?String.fromCharCode(65+("number"==typeof be.correct_answer?be.correct_answer:parseInt(be.correct_answer))):be.correct_choice?String.fromCharCode(65+("number"==typeof be.correct_choice?be.correct_choice:parseInt(be.correct_choice))):void 0,explanation:be.explanation,examYear:be.exam_year,examLocation:be.exam_location,assessmentType:be.assessment_type,knowledgeDomain:be.knowledge_domain,questionFormat:be.question_format,contentTags:be.content_tags,aiCommentary:be.ai_commentary};await jt(t,a,e,null,s.length>0?s:void 0)}else Fe?(ct("SEND",{mode:"force_new",reason:"new_chat_button"}),Qe(!1),await pt(t,e,null,!0,s.length>0?s:void 0)):Ze?(ct("SEND",{mode:"existing",thread:Ze.slice(0,8)}),await pt(t,e,Ze,!1,s.length>0?s:void 0)):(ct("SEND",{mode:"create_new",reason:"no_thread"}),await pt(t,e,null,!0,s.length>0?s:void 0))}catch(a){}},zt=async e=>{const t=et?.find((t=>t.id===e));ct("SELECT",{thread:e.slice(0,8),title:t?.title?.slice(0,30)||"untitled"});try{const t=await tt(e);if(t&&t.length>0){const e=t.map((e=>({id:e.id,content:e.content,isUser:e.isUser,timestamp:e.timestamp,isStreaming:!1})));vt(e),kt(e)}else ft(),Nt();J(!1),setTimeout((()=>{Tt.current&&Tt.current.scrollTo({top:0,behavior:"smooth"})}),200)}catch(s){}};t.useEffect((()=>{const e=e=>{e.detail&&e.detail.mermaidCode&&(we(e.detail.mermaidCode),ye(!0))},t=e=>{if(e.detail&&e.detail.tableData){const t=`\n          <div id="table-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">\n            <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">\n              <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">\n                <div class="flex items-center justify-between">\n                  <h2 class="text-xl font-bold">Tabela Completa</h2>\n                  <button onclick="closeTableDialog()" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-lg transition-colors">\n                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>\n                    </svg>\n                  </button>\n                </div>\n              </div>\n              <div class="p-6 overflow-auto max-h-[70vh]">\n                <div class="overflow-x-auto">\n                  <table class="w-full border-collapse border border-gray-300">\n                    <tbody>\n                      ${e.detail.tableData.map(((e,t)=>`\n                        <tr class="${0===t?"bg-gray-50 font-semibold":"hover:bg-gray-50"}">\n                          ${e.map((e=>`\n                            <td class="border border-gray-300 px-4 py-3 text-sm ${0===t?"font-bold text-gray-900":"text-gray-700"}">${e}</td>\n                          `)).join("")}\n                        </tr>\n                      `)).join("")}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          </div>\n        `;document.body.insertAdjacentHTML("beforeend",t),window.closeTableDialog=()=>{const e=document.getElementById("table-dialog");e&&e.remove()};const s=e=>{"Escape"===e.key&&(window.closeTableDialog(),document.removeEventListener("keydown",s))};document.addEventListener("keydown",s)}};return document.addEventListener("openMermaidModal",e),document.addEventListener("openTableModal",t),()=>{document.removeEventListener("openMermaidModal",e),document.removeEventListener("openTableModal",t),delete window.closeTableDialog}}),[]);const Ht=async(e,t)=>{Se(e);let s=null;if(t){const e=et?.find((e=>e.id===t));e&&e.metadata?.sessionId&&(s=e.metadata.sessionId)}s||(s=await(async e=>{let t=le?.id;if(t||(t=localStorage.getItem("auth_user_id")),!t)return null;try{const{data:s,error:a}=await r.from("study_sessions").select("id, title").eq("user_id",t).ilike("title",`%${e}%`).order("started_at",{ascending:!1}).limit(1);return a?null:s&&s.length>0?s[0].id:null}catch(s){return null}})(e)),Ee(s),ke(!0)};return"/dr-will"===oe.pathname?null:e.jsxs(e.Fragment,{children:[e.jsx(A,{children:a&&e.jsx(d.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 z-[80] md:hidden",onClick:()=>R(!1)})}),e.jsx("div",{className:`floating-chat-button fixed bottom-24 right-3 md:bottom-6 md:right-6 z-[90] ${s}`,children:e.jsx(A,{children:a?e.jsx("div",{className:"fixed inset-0 flex items-center justify-center p-4 md:relative md:inset-auto md:flex-none md:p-0",children:e.jsxs(d.div,{initial:{opacity:0,scale:.8,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:20},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-600 overflow-hidden flex flex-col\r\n                           w-full max-w-[360px] h-full max-h-[85vh]\r\n                           md:w-[420px] md:h-[600px] md:max-h-none md:absolute md:bottom-0 md:right-0",children:[e.jsx("div",{className:"p-4 text-white flex-shrink-0 "+(se&&ue?"bg-gradient-to-r from-emerald-500 to-teal-600":"bg-gradient-to-r from-blue-500 to-purple-600"),children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3 cursor-pointer hover:bg-white hover:bg-opacity-10 rounded-lg p-2 transition-colors",onClick:()=>R(!1),title:"Clique para minimizar",children:[e.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-base",children:se&&ue?"🎯":"🧠"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-lg text-white",children:"Dr. Will"}),se&&ue&&e.jsx("div",{className:"text-xs text-white text-opacity-80",children:"Modo Contextual"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:e=>{e.stopPropagation(),Mt()||(J(!V),V||Y(1))},disabled:Mt(),className:"p-2 rounded-lg transition-colors "+(Mt()?"opacity-50 cursor-not-allowed":"hover:bg-white hover:bg-opacity-20"),title:Mt()?"Aguarde a resposta...":"Histórico",children:e.jsx(S,{className:"h-4 w-4"})}),e.jsx("button",{onClick:e=>{e.stopPropagation(),ct("NEW_CHAT",{previousThread:Ze?.slice(0,8)||"none",contextualMode:se}),ft(),Nt(),J(!1),ae(!1),ne(!1),Qe(!0)},className:"p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors",title:"Nova conversa",children:e.jsx(O,{className:"h-4 w-4"})}),e.jsx("button",{onClick:e=>{e.stopPropagation(),ie(Ze?`/dr-will?thread=${Ze}`:"/dr-will"),R(!1)},className:"p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors",title:"Abrir chat completo",children:e.jsx(F,{className:"h-4 w-4"})}),e.jsx("button",{onClick:e=>{e.stopPropagation(),R(!1)},className:"p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors",children:e.jsx(c,{className:"h-4 w-4"})})]})]})}),ue&&e.jsx("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-600 px-4 py-3 border-b border-white border-opacity-20",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-7 h-7 bg-white bg-opacity-20 rounded-lg flex items-center justify-center backdrop-blur-sm",children:"📝"}),e.jsxs("div",{children:[e.jsxs("div",{className:"text-white font-semibold text-sm",children:["Questão ",de," de ",ce]}),e.jsx("div",{className:"text-white text-opacity-80 text-xs",children:me})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>(()=>{if(!be)return;const e=`\n      <div id="question-info-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">\n        <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">\n          <div class="bg-gradient-to-r from-emerald-500 to-teal-600 p-6 text-white">\n            <div class="flex items-center justify-between">\n              <div class="flex items-center gap-3">\n                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">\n                  📝\n                </div>\n                <div>\n                  <h3 class="text-xl font-bold">Questão ${de} de ${ce}</h3>\n                  <p class="text-emerald-100 text-sm">${me}</p>\n                </div>\n              </div>\n              <button onclick="document.getElementById('question-info-dialog').remove()" class="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">\n                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>\n                </svg>\n              </button>\n            </div>\n          </div>\n\n          <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">\n            <div class="space-y-6">\n              ${xe||he||pe?`\n                <div>\n                  <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">\n                    <span class="w-2 h-2 bg-emerald-500 rounded-full"></span>\n                    Contexto da Questão\n                  </h4>\n                  <div class="bg-gray-50 rounded-lg p-4 space-y-2">\n                    ${xe?`<div class="flex items-center gap-2"><span class="font-medium text-gray-700">Especialidade:</span> <span class="text-gray-600">${xe}</span></div>`:""}\n                    ${he?`<div class="flex items-center gap-2"><span class="font-medium text-gray-700">Tema:</span> <span class="text-gray-600">${he}</span></div>`:""}\n                    ${pe?`<div class="flex items-center gap-2"><span class="font-medium text-gray-700">Foco:</span> <span class="text-gray-600">${pe}</span></div>`:""}\n                  </div>\n                </div>\n              `:""}\n\n              <div class="bg-blue-50 rounded-lg p-4">\n                <div class="text-sm text-blue-800">\n                  💡 <strong>Dica:</strong> Use o modo contextual para fazer perguntas específicas sobre esta questão.\n                  O Dr. Will terá acesso a todas essas informações para dar respostas mais precisas.\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">\n            <div class="flex items-center justify-end">\n              <button onclick="document.getElementById('question-info-dialog').remove()" class="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors">\n                Fechar\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    `;document.body.insertAdjacentHTML("beforeend",e);const t=document.getElementById("question-info-dialog");t&&t.addEventListener("click",(e=>{e.target===t&&t.remove()}))})(),className:"p-2 bg-white bg-opacity-20 text-white hover:bg-opacity-30 rounded-lg transition-all duration-200",title:"Ver detalhes da questão",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("button",{onClick:async()=>{if(!ue&&!se)return;if(Mt())return;const e=!se;if(ae(e),ue&&!e&&ne(!0),He||Ze){const e=He||Ze;try{setTimeout((async()=>{await zt(e)}),100)}catch(t){}}},disabled:!ue&&!se||Mt(),className:"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 "+(Mt()?"bg-white bg-opacity-10 text-white text-opacity-50 cursor-not-allowed":se?"bg-white text-emerald-600 shadow-md":ue?"bg-white bg-opacity-20 text-white hover:bg-opacity-30":"bg-white bg-opacity-10 text-white text-opacity-50 cursor-not-allowed"),title:Mt()?"Aguarde a resposta...":ue||se?se?"Desativar modo contextual":"Ativar modo contextual":"Modo contextual disponível apenas durante questões",children:se?"🎯 Contextual":"💬 Geral"})]})]})}),e.jsx("div",{className:"flex-1 flex flex-col overflow-hidden",children:V?e.jsxs("div",{className:"flex-1 overflow-y-auto",children:[e.jsxs("div",{className:"p-3 border-b border-gray-200 dark:border-gray-600",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"Conversas Anteriores"}),et&&et.length>0&&e.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600",children:e.jsx("button",{onClick:ut,disabled:Mt()||qe,className:"w-full px-3 py-2 text-xs font-medium rounded-lg transition-all duration-200 "+(Mt()||qe?"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed":"bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/50 border border-red-200 dark:border-red-700"),children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(B,{className:"w-3 h-3"}),qe?"Deletando...":"Deletar Todas as Conversas"]})})})]}),ot?e.jsx("div",{className:"space-y-3 p-4",children:[1,2,3].map((t=>e.jsxs("div",{className:"flex items-center gap-3 animate-pulse",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-300 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-300 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t)))}):et&&et.length>0?(()=>{const t=et.filter((e=>!_t(e.title||""))),s=Math.ceil(t.length/10),a=10*(K-1),r=a+10,n=t.slice(a,r);return e.jsxs(e.Fragment,{children:[n.map((t=>e.jsx(d.div,{whileHover:{backgroundColor:"#f8fafc"},onClick:()=>{const e=We||{isInQuestionSession:ue,sessionId:fe};if(!Mt())if(_t(t.title||"")){if(!e.isInQuestionSession){const e=Ut(t.title||"")||"Sessão";return void Ht(e,t.id)}const s=t.metadata?.sessionId,a=e.sessionId;if(s&&a&&s!==a){const e=Ut(t.title||"")||"Sessão";return void Ht(e,t.id)}ae(!0),setTimeout((()=>zt(t.id)),100)}else ae(!1),zt(t.id)},className:"p-3 border-b border-gray-100 dark:border-gray-600 transition-colors "+(Mt()?"opacity-50 cursor-not-allowed":_t(t.title||"")&&!ue?"opacity-50 cursor-pointer":"cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"),children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm "+(_t(t.title||"")?"bg-gradient-to-br from-emerald-500 to-teal-600":"bg-gradient-to-br from-blue-500 to-purple-600"),children:_t(t.title||"")?"🎯":"🧠"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h5",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm truncate",children:t.title||"Conversa com Dr. Will"}),_t(t.title||"")&&e.jsx("span",{className:"px-2 py-0.5 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 text-xs rounded-full",children:"Contextual"})]}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[(()=>{try{if(t.createdAt){const e=new Date(t.createdAt);if(!isNaN(e.getTime()))return z(e,{addSuffix:!0,locale:H})}return"Recente"}catch{return"Recente"}})(),_t(t.title||"")&&(ue?(()=>{const s=t.metadata?.sessionId,a=xt?.sessionId;return s&&a&&s!==a?e.jsx("span",{className:"ml-2 text-red-500",children:"• Sessão diferente"}):s&&a&&s===a?e.jsx("span",{className:"ml-2 text-green-500",children:"• Sessão atual"}):e.jsx("span",{className:"ml-2 text-gray-500",children:"• Sessão"})})():e.jsx("span",{className:"ml-2 text-orange-500",children:"• Sem contexto ativo"}))]})]}),e.jsx("button",{onClick:e=>{e.stopPropagation(),mt(t.id,t.title||"Conversa com Dr. Will")},disabled:Mt()||qe,className:"p-1 rounded-full transition-colors "+(Mt()||qe?"text-gray-300 cursor-not-allowed":"text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30"),title:"Deletar conversa",children:e.jsx(P,{className:"w-4 h-4"})})]})},t.id))),s>1&&e.jsx("div",{className:"p-3 border-t border-gray-100 dark:border-gray-600 bg-gray-50 dark:bg-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Página ",K," de ",s," • ",t.length," conversas"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>Y(K-1),disabled:1===K||Mt(),className:"px-2 py-1 text-xs rounded transition-colors "+(1===K||Mt()?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-white dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 border border-gray-200 dark:border-gray-500"),children:"←"}),e.jsx("span",{className:"px-2 py-1 text-xs text-gray-600",children:K}),e.jsx("button",{onClick:()=>Y(K+1),disabled:K===s||Mt(),className:"px-2 py-1 text-xs rounded transition-colors "+(K===s||Mt()?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-white dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 border border-gray-200 dark:border-gray-500"),children:"→"})]})]})}),(He||Ze)&&e.jsx("div",{className:"p-3 border-t border-gray-100 dark:border-gray-600 bg-blue-50 dark:bg-blue-900/30",children:e.jsx("button",{onClick:()=>{Mt()||J(!1)},disabled:Mt(),className:"w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 "+(Mt()?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-blue-500 text-white hover:bg-blue-600 shadow-sm"),children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Voltar à Conversa Atual"]})})})]})})():(et&&et.length,e.jsxs("div",{className:"p-4 text-center text-gray-500",children:[e.jsx(m,{className:"h-8 w-8 mx-auto mb-2 text-gray-300"}),e.jsx("p",{className:"text-sm",children:"Nenhuma conversa anterior"})]}))]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{ref:Tt,className:"flex-1 overflow-y-auto p-4 space-y-4",children:[0===Et().length?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4",children:"🧠"}),e.jsxs("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2",children:["Olá! Sou o Dr. Will ",se&&ue&&"🎯"]}),e.jsx("p",{className:"text-sm text-gray-600",children:se&&ue?`Estou acompanhando sua sessão "${me}" - questão ${de} de ${ce}!`:"Como posso ajudar hoje? Posso ajudar com casos clínicos, estudos e muito mais. "}),se&&ue&&e.jsx("div",{className:"mt-3 p-3 bg-emerald-50 dark:bg-emerald-900/30 rounded-lg border border-emerald-200 dark:border-emerald-700",children:e.jsxs("div",{className:"text-xs text-emerald-700 dark:text-emerald-300",children:[e.jsx("div",{className:"font-medium mb-1",children:"📚 Sessão Contextual Ativa"}),e.jsx("div",{children:"Todas as suas perguntas serão respondidas com base na questão atual. Posso ajudar com:"}),e.jsxs("ul",{className:"mt-1 ml-3 list-disc text-emerald-600",children:[e.jsx("li",{children:"Explicação do enunciado atual"}),e.jsx("li",{children:"Análise das alternativas"}),e.jsx("li",{children:"Conceitos relacionados"}),e.jsx("li",{children:"Comparação com questões anteriores da sessão"})]}),e.jsx("div",{className:"mt-2 text-emerald-600 font-medium",children:"💡 Histórico mantido para toda a sessão!"})]})})]}):Et().map((t=>e.jsx(d.div,{ref:e=>St.current[t.id]=e,initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex "+(t.isUser?"justify-end":"justify-start"),children:t.isThinking?e.jsx("div",{className:"w-full",children:e.jsx(T,{content:t.content,isStreaming:t.isStreaming,formatThinkingContent:E,compact:!0})}):e.jsxs("div",{className:(t.isUser?"max-w-[85%] bg-blue-500 text-white rounded-2xl rounded-br-md":"w-full bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-2xl rounded-bl-md border border-gray-200 dark:border-gray-600")+" px-3 py-2 shadow-sm",children:[t.isUser?e.jsxs("div",{children:[t.images&&t.images.length>0&&e.jsx(M,{images:t.images,className:"mb-2"}),e.jsx("div",{className:"text-sm whitespace-pre-wrap break-words",children:t.content})]}):e.jsx(G,{content:t.content,isStreaming:t.isStreaming,messageId:t.id,isUser:t.isUser,isLoading:Mt(),onResourcesClick:Bt,onResourcesDataDetected:Ot}),t.isStreaming&&!t.isThinking&&!t.isUser&&e.jsxs("div",{className:"flex items-center gap-1 mt-2",children:[e.jsx("div",{className:"w-2 h-2 bg-current rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-current rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-current rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})},t.id))),e.jsx("div",{ref:Ct})]}),X&&He&&et?.find((e=>e.id===He&&_t(e.title||"")))&&!ue&&e.jsxs("div",{className:"border-t border-gray-200 dark:border-gray-600 p-3 bg-orange-50 dark:bg-orange-900/30",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2 text-orange-700 dark:text-orange-300 text-sm",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z"})}),e.jsx("span",{className:"font-medium",children:"Fora do contexto de estudos"})]}),e.jsx("button",{onClick:()=>Z(!1),className:"text-orange-500 hover:text-orange-700 p-1",children:e.jsx(c,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-xs text-orange-600 mt-1",children:"Ao enviar uma mensagem, será iniciada uma nova conversa com Dr. Will."})]}),e.jsxs("div",{className:"border-t border-gray-200 dark:border-gray-600 p-4 flex-shrink-0",children:[Ge.length>0&&e.jsx("div",{className:"mb-3 flex gap-2 flex-wrap",children:Ge.map(((t,s)=>e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:t.url,alt:`Preview ${s+1}`,className:"w-12 h-12 object-cover rounded-lg border border-gray-300"}),e.jsx("button",{onClick:()=>(e=>{Ve((t=>{const s=[...t];return URL.revokeObjectURL(s[e].url),s.splice(e,1),s}))})(s),className:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600",children:"×"})]},s)))}),It()&&e.jsx("div",{className:"mb-3 p-3 bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-orange-500",children:"🔒"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-orange-800 dark:text-orange-200",children:"Dr. Will indisponível"}),e.jsx("p",{className:"text-xs text-orange-600 dark:text-orange-300",children:"Disponível apenas na plataforma MedEvo"})]})]})}),se&&!It()&&e.jsx("div",{className:"mb-3 p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-gray-500",children:"🚫"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-800 dark:text-gray-200",children:"Modo contextual desabilitado"}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Use o Dr. Will no modo geral"})]})]})}),e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(u,{value:ee,onChange:e=>te(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),Wt())},placeholder:It()?"Dr. Will disponível apenas na MedEvo...":se?"Modo contextual desabilitado...":"Digite sua pergunta...",disabled:Mt()||Je||It()||se,className:"text-sm min-h-[44px] max-h-[120px] resize-none pr-10"}),!(se&&ue)&&e.jsx("input",{type:"file",accept:"image/*",onChange:async e=>{const t=e.target.files;if(!t||0===t.length)return;const s=[];for(let a=0;a<Math.min(t.length,3-Ge.length);a++){const e=t[a];if(e.type.startsWith("image/")){const t=URL.createObjectURL(e);s.push({url:t,file:e})}}Ve((e=>[...e,...s])),Xe.current&&(Xe.current.value="")},ref:Xe,className:"hidden",multiple:!0}),!(se&&ue)&&e.jsx(g,{type:"button",onClick:()=>Xe.current?.click(),disabled:Mt()||Je||Ge.length>=3,variant:"ghost",size:"sm",className:"absolute right-1 bottom-1 w-7 h-7 p-0 text-gray-400 hover:text-gray-600",children:Je?e.jsx(x,{className:"w-3 h-3 animate-spin text-blue-500"}):e.jsx(h,{className:"w-3 h-3"})})]}),e.jsx(g,{onClick:()=>Wt(),disabled:!ee.trim()&&0===Ge.length||Mt()||It()||se,size:"sm",className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:e.jsx(W,{className:"h-4 w-4"})})]})]})]})})]})}):e.jsxs(d.button,{initial:{scale:0},animate:{scale:1},whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{le?.id?R(!0):Be(!0)},className:"text-white w-9 h-9 md:w-auto md:h-auto md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 relative flex items-center justify-center "+(le?.id?se&&ue?"bg-gradient-to-r from-emerald-500 to-teal-600":"bg-gradient-to-r from-blue-500 to-purple-600":"bg-gradient-to-r from-gray-400 to-gray-500 cursor-pointer"),title:le?.id?"Abrir Dr. Will":"Clique para fazer login e usar o Dr. Will",children:[e.jsx("div",{className:"text-sm md:text-2xl",children:se&&ue?"🎯":"🧠"}),se&&ue?e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 md:-top-1 md:-right-1 md:w-5 md:h-5 bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-xs md:text-xs",children:de||"?"})}):et&&et.length>0||Et().length>0?e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 md:-top-1 md:-right-1 md:w-5 md:h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-xs md:text-xs",children:et?Math.min(et.length,9):"!"})}):null,e.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 w-2 h-2 md:-bottom-1 md:-right-1 md:w-4 md:h-4 border border-white md:border-2 rounded-full "+(se&&ue?"bg-orange-500":"bg-green-500")}),Mt()&&e.jsx("div",{className:"absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-75"})]})})}),e.jsx(_,{isOpen:ve,onClose:()=>ye(!1),mermaidCode:je}),e.jsx(D,{open:Ne,onOpenChange:ke,sessionTitle:Ce,sessionId:Te}),De&&e.jsx(I,{open:Me,onOpenChange:_e,directMentions:De.directMentions,suggestions:De.suggestions,conductMentions:De.conductMentions,conductSuggestions:De.conductSuggestions,title:De.title}),e.jsx(p,{open:Re,onOpenChange:Ue,children:e.jsxs(b,{className:"sm:max-w-md",children:[e.jsx(f,{children:e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"w-5 h-5 text-red-500"}),"Confirmar Exclusão"]})}),e.jsx("div",{className:"py-4",children:"single"===$e?.type?e.jsxs("p",{className:"text-sm text-gray-600",children:["Tem certeza que deseja deletar a conversa"," ",e.jsxs("span",{className:"font-medium",children:['"',$e.threadTitle,'"']}),"?",e.jsx("br",{}),e.jsx("span",{className:"text-red-600 font-medium",children:"Esta ação não pode ser desfeita."})]}):e.jsxs("p",{className:"text-sm text-gray-600",children:["Tem certeza que deseja deletar"," ",e.jsx("span",{className:"font-medium text-red-600",children:"todas as suas conversas"}),"?",e.jsx("br",{}),e.jsx("span",{className:"text-red-600 font-medium",children:"Esta ação não pode ser desfeita."})]})}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx(g,{variant:"outline",onClick:()=>Ue(!1),disabled:qe,children:"Cancelar"}),e.jsx(g,{variant:"destructive",onClick:gt,disabled:qe,className:"min-w-[100px]",children:qe?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Deletando..."]}):"Deletar"})]})]})}),e.jsx(y,{open:Oe,onOpenChange:Be,hidden:!0})]})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */export{V as FloatingChatButton,V as default};
