import{u as e}from"./query-vendor-B-7l6Nb3.js";import{s as t}from"./index-CNG-Xj2g.js";const i="medical_news";function r(r){const{limit:n=10,offset:o=0,category:s,searchTerm:a,isPediDrop:c}=r||{};return e({queryKey:["newsletters",n,o,s,a,c],queryFn:async()=>{const e=c?"id, title, summary, pub_date, category, source, link, image_url, main_topic, reading_time, practical_application, clinical_pearls, attention_points, scientific_references, content_sections, is_pedidrop":"id, title, summary, pub_date, category, source, link, image_url";let r=t.from(i).select(e).order("pub_date",{ascending:!1}).range(o,o+n-1);if(void 0!==c&&(r=r.eq("is_pedidrop",c)),s&&(r=r.eq("category",s)),a){const e=c?`title.ilike.%${a}%,summary.ilike.%${a}%,main_topic.ilike.%${a}%,attention_points.ilike.%${a}%`:`title.ilike.%${a}%,summary.ilike.%${a}%`;r=r.or(e)}const{data:u,error:l}=await r;if(l)throw new Error(l.message);return u},staleTime:3e5,gcTime:9e5,refetchOnWindowFocus:!1,refetchOnMount:!1,retry:1})}function n(r){const{category:n,searchTerm:o,isPediDrop:s}=r||{};return e({queryKey:["newsletters-count",n,o,s],queryFn:async()=>{let e=t.from(i).select("id",{count:"exact",head:!0});if(void 0!==s&&(e=e.eq("is_pedidrop",s)),n&&(e=e.eq("category",n)),o){const t=s?`title.ilike.%${o}%,summary.ilike.%${o}%,main_topic.ilike.%${o}%,attention_points.ilike.%${o}%`:`title.ilike.%${o}%,summary.ilike.%${o}%`;e=e.or(t)}const{count:r,error:a}=await e;if(a)throw new Error(a.message);return r||0},staleTime:6e5,gcTime:18e5,refetchOnWindowFocus:!1,refetchOnMount:!1,retry:1})}function o(){return e({queryKey:["newsletter-categories"],queryFn:async()=>{const{data:e,error:i}=await t.rpc("get_news_categories");if(i)throw new Error(i.message);return e||[]},staleTime:36e5,gcTime:864e5,refetchOnWindowFocus:!1,refetchOnMount:!1,retry:1})}export{r as a,n as b,o as u};
