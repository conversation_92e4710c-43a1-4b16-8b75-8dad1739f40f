import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-DksBl_i3.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-D9amGMlQ.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-BkFd5qSK.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./target-Cj27UDYs.js";import"./zap-DULtmWB8.js";import"./book-open-CzUd5kBy.js";import"./star-BlLX_9hT.js";import"./circle-help-DFUIKtE9.js";import"./instagram-CuaDlQAQ.js";import"./collapsible-B6HfSnGs.js";import"./accordion-e_2-7cSr.js";import"./PatientInfoSection-DRcmZNkq.js";import"./scale-DPUkT8Aa.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-DUb3rnP_.js";import"./user-GwV7rf-d.js";import"./DosageDisplay-B67kyXO8.js";import"./alert-DF0vYpCj.js";import"./plus-xou7mjLU.js";import"./lightbulb-Bt215yWD.js";import"./external-link-C-xrS7_q.js";import"./stethoscope-VjhNhcDw.js";import"./syringe-CShQz8NW.js";import"./wind-B0PVV5NO.js";import"./bug-DnXwJCtk.js";import"./chevron-left-Cmyma3W5.js";import"./house-D42jAwMx.js";function t(){return r.jsx(o,{})}export{t as default};
