import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-BtKMq0bk.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-DQuOk0R3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-BmzagX2Z.js";import"./FeedbackTrigger-Bwp4oswe.js";import"./rocket-Czd-at64.js";import"./target-CuA2iBUH.js";import"./zap-B06nQ-rd.js";import"./book-open-gHhE7Hhk.js";import"./star-CaqDe8as.js";import"./circle-help-CTIYt4iy.js";import"./instagram-DOFGyRt3.js";import"./collapsible-B6HfSnGs.js";import"./accordion-B2n-mSq0.js";import"./PatientInfoSection-BxP5WXoY.js";import"./scale-TvLu6nmW.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-B1-Ir37l.js";import"./user-BHr1Xpy_.js";import"./DosageDisplay-Cvmr2vYy.js";import"./alert-W1it20m_.js";import"./plus-Bojeiru-.js";import"./lightbulb-B9h4qqax.js";import"./external-link-DyRzLSkQ.js";import"./stethoscope-DpPTC3SB.js";import"./syringe-Ce56EEU5.js";import"./wind-CyT7iq66.js";import"./bug-CS708cXG.js";import"./chevron-left-CoYBC5uX.js";import"./house-owiQzdQR.js";function t(){return r.jsx(o,{})}export{t as default};
