import{j as e}from"./radix-core-6kBL75b5.js";import{j as a,B as s,ac as r,al as t,X as i,ao as l,a6 as d,k as o,U as n,V as m,aL as c,aH as x,S as u,W as p,n as g,a9 as b,ab as h,a8 as f}from"./index-DQuOk0R3.js";import j from"./Footer-BmzagX2Z.js";import{r as N}from"./critical-DVX9Inzy.js";import{C as v}from"./chart-column-vN7hGEkr.js";import{R as k}from"./refresh-cw-CNxrwJvE.js";import{Z as y}from"./zap-B06nQ-rd.js";import{L as w}from"./lightbulb-B9h4qqax.js";import{U as A}from"./user-BHr1Xpy_.js";import{S as R}from"./stethoscope-DpPTC3SB.js";import{W as P}from"./wind-CyT7iq66.js";import{R as L}from"./ruler-DpMNZvk_.js";import{B as C}from"./book-open-gHhE7Hhk.js";import{a as I}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-Bwp4oswe.js";import"./rocket-Czd-at64.js";import"./target-CuA2iBUH.js";import"./star-CaqDe8as.js";import"./circle-help-CTIYt4iy.js";import"./instagram-DOFGyRt3.js";const S=[{id:"atropine",name:"Atropina",category:"premedication",indication:"NÃO É ROTINA em >1 ano. Indicado em: neonatos/lactentes <1 ano, uso de succinilcolina, bradicardia, instabilidade hemodinâmica",doseFormula:"0.02 mg/kg",presentations:[{concentration:"0.5 mg/mL",volume:"1 mL",form:"Ampola"}],contraindications:["Taquiarritmia não controlada"],sideEffects:["Taquicardia","Pele seca","Rubor","Midríase","Risco de arritmia se dose alta"],specialConsiderations:["Em crianças >1 ano: NÃO é obrigatório - usar apenas se bradicardia, instabilidade ou succinilcolina","Para RN/lactentes, diluir a 0.05 mg/mL (ex: 1 mL de 0.5 mg/mL + 9 mL SF 0.9%)","Dose mínima: 0.1 mg","Dose máxima: 0.5 mg criança, 1 mg adolescente"],ageRestrictions:{maxAge:12,unit:"months"}},{id:"lidocaine",name:"Lidocaína",category:"premedication",indication:"NÃO É ROTINA. Indicado apenas em: TCE/risco de ↑PIC, laringoespasmo, broncoespasmo. Evitar uso indiscriminado",doseFormula:"1-1.5 mg/kg",presentations:[{concentration:"10 mg/mL",volume:"5 mL",form:"Solução 1%"},{concentration:"20 mg/mL",volume:"5 mL",form:"Solução 2%"}],contraindications:["Bloqueios AV","Alergia a anestésicos locais"],sideEffects:["Hipotensão","Bradicardia","Convulsões em doses altas"],specialConsiderations:["USAR APENAS SE HOUVER INDICAÇÃO CLÍNICA - NÃO é rotina para toda IOT","Evitar em instabilidade hemodinâmica/hipotensão","Pode diluir 1:1 com SF se administração lenta ou volume pequeno"]},{id:"ketamine",name:"Cetamina",category:"sedation",indication:"Instabilidade hemodinâmica, broncoespasmo, choque",doseFormula:"1-2 mg/kg IV (até 4 mg/kg em casos especiais)",presentations:[{concentration:"50 mg/mL",volume:"10 mL",form:"Ampola"}],contraindications:["TCE grave","HIC","Glaucoma/trauma ocular"],sideEffects:["Hipersecreção","Laringoespasmo","Hipertensão","Alucinações"],specialConsiderations:["Atropina prévia reduz secreção","Associar benzodiazepínico se necessário","Para <20 mg, diluir a 10 mg/mL (ex: 1 mL + 4 mL SF)","IM: 4-5 mg/kg"]},{id:"etomidate",name:"Etomidato",category:"sedation",indication:"Instabilidade hemodinâmica, politrauma, HIC",doseFormula:"0.2-0.3 mg/kg",presentations:[{concentration:"2 mg/mL",volume:"10 mL",form:"Frasco"}],contraindications:["Sepse com choque refratário","Insuficiência adrenal"],sideEffects:["Supressão adrenal","Mioclonia","Náusea"],specialConsiderations:["Usar puro","Início rápido: 30-60 s","Duração curta: 3-5 min"]},{id:"propofol",name:"Propofol",category:"sedation",indication:"Paciente estável, indução eletiva",doseFormula:"1-2 mg/kg",presentations:[{concentration:"10 mg/mL",volume:"20 mL",form:"Frasco"},{concentration:"10 mg/mL",volume:"50 mL",form:"Frasco"}],contraindications:["Instabilidade hemodinâmica"],sideEffects:["Hipotensão","Depressão respiratória","Dor na punção"],specialConsiderations:["Usar puro","Início muito rápido: 15-30 s","Duração: 5-10 min","Off label em <1 ano"],ageRestrictions:{minAge:1,unit:"years"}},{id:"midazolam",name:"Midazolam",category:"sedation",indication:"Combinação sedativa/amnésica, status convulsivo",doseFormula:"0.1-0.3 mg/kg (reduzir 50% se instável)",presentations:[{concentration:"5 mg/mL",volume:"3 mL",form:"Ampola"}],contraindications:["Hipersensibilidade a benzodiazepínicos"],sideEffects:["Hipotensão","Depressão respiratória"],specialConsiderations:["Recomendada diluição (5 mg/mL → 1 mg/mL com SF)","Infusão IV lenta, 2-5 min","Nunca bolus rápido"]},{id:"fentanyl",name:"Fentanil",category:"sedation",indication:"Analgesia, atenuação resposta adrenérgica/IOT",doseFormula:"1-3 mcg/kg (2 mcg/kg comum)",presentations:[{concentration:"50 mcg/mL",volume:"2 mL",form:"Ampola"}],contraindications:["Hipersensibilidade a opioides"],sideEffects:["Depressão respiratória","Rigidez torácica"],specialConsiderations:["Para RN/lactente, diluir a 10 mcg/mL (ex: 1 mL + 4 mL SF)","Infusão IV lenta (≥30 s, ideal 1-2 min)","Risco de rigidez torácica se administração rápida"]},{id:"alfentanil",name:"Alfentanila",category:"sedation",indication:"Analgesia durante IOT (apenas adultos)",doseFormula:"10-30 mcg/kg",presentations:[{concentration:"500 mcg/mL",volume:"2 mL",form:"Ampola"}],contraindications:["Hipersensibilidade a opioides"],sideEffects:["Depressão respiratória","Rigidez torácica"],specialConsiderations:["Apenas para adultos","Infusão IV lenta","Início rápido: 1-2 min","Duração: 15-30 min"]},{id:"remifentanil",name:"Remifentanila",category:"sedation",indication:"Analgesia durante IOT (apenas adultos)",doseFormula:"0.5-1 mcg/kg",presentations:[{concentration:"50 mcg/mL",volume:"2 mL",form:"Ampola"}],contraindications:["Hipersensibilidade a opioides"],sideEffects:["Depressão respiratória","Rigidez torácica"],specialConsiderations:["Apenas para adultos","Infusão IV lenta","Início rápido: 1-2 min","Duração ultra-curta: 5-10 min"]},{id:"succinylcholine",name:"Succinilcolina (Suxametônio)",category:"neuromuscular_block",indication:"SRI, emergência, início ultrarrápido",doseFormula:"RN/lactente: 2 mg/kg IV; Criança (>2 anos): 1.0 mg/kg IV",presentations:[{concentration:"10 mg/mL",volume:"10 mL",form:"Pó 100 mg/reconstituir"},{concentration:"50 mg/mL",volume:"2 mL",form:"Ampola"}],contraindications:["Hipertermia maligna","Doenças neuromusculares","Rabdomiólise","Queimadura >48h","Histórico familiar de hipertermia maligna"],sideEffects:["Bradicardia","Hipercalemia","Fasciculações","Aumento PIC"],specialConsiderations:["IM: 4-5 mg/kg (sem acesso IV)","Início: 30-60 s","Duração: 4-10 min","Risco hipercalemia em doenças neuromusculares"]},{id:"rocuronium",name:"Rocurônio",category:"neuromuscular_block",indication:"SRI, alternativa à succinilcolina",doseFormula:"1.0 mg/kg IV (RSI padrão)",presentations:[{concentration:"10 mg/mL",volume:"5 mL",form:"Frasco"}],contraindications:["Hipersensibilidade ao rocurônio"],sideEffects:["Taquicardia leve","Mínimas alterações hemodinâmicas"],specialConsiderations:["Usar puro","Início: 45-60 s","Duração: 30-60 min","Reversível com sugamadex"]},{id:"vecuronium",name:"Vecurônio",category:"neuromuscular_block",indication:"Prolongamento bloqueio, alternativa",doseFormula:"0.1 mg/kg (pode-se usar 0.2-0.3 mg/kg para início rápido)",presentations:[{concentration:"1 mg/mL",volume:"10 mL",form:"Pó 10 mg + 10 mL"}],contraindications:["Hipersensibilidade ao vecurônio"],sideEffects:["Pouco efeito cardiovascular"],specialConsiderations:["Início mais lento: 2-3 min","Duração: 30-45 min","Reconstituir conforme bula"]},{id:"atracurium",name:"Atracúrio",category:"neuromuscular_block",indication:"SRI em pacientes com insuficiência renal/hepática, contraindicação à succinilcolina/rocurônio",doseFormula:"0.5 mg/kg IV (adulto e pediatria)",presentations:[{concentration:"10 mg/mL",volume:"2.5 mL",form:"Ampola"},{concentration:"10 mg/mL",volume:"5 mL",form:"Ampola"}],contraindications:["Hipersensibilidade ao atracúrio","Asma grave (relativo)"],sideEffects:["Hipotensão transitória","Rubor","Liberação de histamina","Broncoespasmo (raro)"],specialConsiderations:["Usar puro em bólus","Início: 2-3 min","Duração: 20-35 min","Metabolismo via degradação de Hofmann (independente de fígado/renal)","Cuidado em asmáticos/alérgicos","Preferido em pacientes com disfunção orgânica"]},{id:"cisatracurium",name:"Cisatracúrio",category:"neuromuscular_block",indication:"SRI em instabilidade hemodinâmica, insuficiência renal/hepática",doseFormula:"0.2 mg/kg IV (adulto e pediatria)",presentations:[{concentration:"2 mg/mL",volume:"5 mL",form:"Ampola"}],contraindications:["Hipersensibilidade ao cisatracúrio"],sideEffects:["Mínimas alterações hemodinâmicas"],specialConsiderations:["Usar puro em bólus","Início: 2-3 min","Duração: 40-70 min","Metabolismo via degradação de Hofmann","Menos risco de liberação de histamina que atracúrio","Excelente escolha em instáveis hemodinâmicos","Custo mais elevado"]},{id:"pancuronium",name:"Pancurônio",category:"neuromuscular_block",indication:"SRI em situações de bloqueio prolongado, indisponibilidade de drogas de escolha",doseFormula:"0.08-0.1 mg/kg IV (adulto e pediatria)",presentations:[{concentration:"2 mg/mL",volume:"2 mL",form:"Ampola"},{concentration:"2 mg/mL",volume:"5 mL",form:"Ampola"}],contraindications:["Hipersensibilidade ao pancurônio","Insuficiência renal grave (relativo)"],sideEffects:["Taquicardia","Elevação da pressão arterial","Efeito vagolítico"],specialConsiderations:["Usar puro em bólus","Início: 2-3 min","Duração: 60-100 min (longa)","Depende da função renal para eliminação","Pode ser útil em cirurgias prolongadas","Cuidado em insuficiência renal"]}],M=({medicationId:l,weight:d,patientType:o,category:n,presentations:m,route:c,timing:x,doseFormula:u,contraindications:p,sideEffects:g,observations:b,isRecommended:h=!1,priority:f,showDetailedInfo:j=!0})=>{const[A,R]=N.useState(0),[P,L]=N.useState(!1),C=m[A],I=(e,a=1)=>e.toFixed(a).replace(".",","),[S,M]=C.name.split(" ("),D=`${S} (${M?M.replace(")",""):C.concentration})`,F=e=>{R(e)};return C?e.jsxs("div",{onClick:()=>L(!P),className:a("border-l-4 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200 rounded-r-lg cursor-pointer shadow-sm hover:shadow overflow-hidden",h?"border-l-slate-400 dark:border-l-slate-500":"border-l-slate-300 dark:border-l-slate-600"),children:[e.jsxs("div",{className:"p-3 sm:p-4",children:[e.jsxs("div",{className:"flex items-start justify-between gap-3 mb-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-semibold text-foreground text-base sm:text-lg mb-2",children:D}),m.length>1&&e.jsx("div",{className:"flex gap-1 flex-wrap",children:m.map(((a,r)=>e.jsx(s,{variant:A===r?"default":"outline",size:"sm",onClick:e=>{e.stopPropagation(),F(r)},className:"text-xs h-6 px-2 font-medium",children:a.concentration},r)))})]}),e.jsx("div",{className:"flex-shrink-0 p-1.5 bg-slate-600 rounded-full transition-transform duration-300 pointer-events-none",style:{transform:P?"rotate(180deg)":"rotate(0deg)"},children:e.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),e.jsx("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700",children:e.jsxs("div",{className:"text-sm sm:text-base font-semibold text-slate-700 dark:text-slate-300 leading-relaxed text-center sm:text-left",children:["Administrar ",e.jsx("span",{className:"bg-blue-100 dark:bg-blue-900/40 px-2 py-1 rounded font-bold text-blue-800 dark:text-blue-200",children:C.calculatedVolumeRange||`${I(C.calculatedVolume)} mL`})," (",e.jsx("span",{className:"bg-blue-100 dark:bg-blue-900/40 px-2 py-1 rounded font-bold text-blue-800 dark:text-blue-200",children:C.calculatedDoseRange||`${I(C.calculatedDose)} ${C.unit}`}),") ",C.calculatedAdministration?C.calculatedAdministration.replace(/^Administrar\s+/i,""):c]})})]}),P&&e.jsx("div",{className:"px-3 sm:px-4 pb-3 sm:pb-4",children:e.jsxs("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-3 space-y-3",children:[(C.indications||C.specialConsiderations||C.dilution)&&e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700",children:[e.jsxs("h4",{className:"font-medium text-slate-700 dark:text-slate-300 text-sm mb-2 flex items-center gap-2",children:[e.jsx(r,{className:"h-4 w-4"}),"Orientações Clínicas"]}),e.jsxs("div",{className:"space-y-2 text-xs",children:[C.indications&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-slate-600 dark:text-slate-400",children:"Indicações:"}),e.jsx("div",{className:"mt-1 space-y-1",children:C.indications.map(((a,s)=>e.jsx("div",{className:"text-slate-700 dark:text-slate-300 pl-3 border-l-2 border-blue-200 dark:border-blue-800",children:a},s)))})]}),C.specialConsiderations&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-slate-600 dark:text-slate-400",children:"Considerações:"}),e.jsx("div",{className:"mt-1 space-y-1",children:C.specialConsiderations.map(((a,s)=>e.jsx("div",{className:"text-slate-700 dark:text-slate-300 pl-3 border-l-2 border-amber-200 dark:border-amber-800",children:a},s)))})]}),C.dilution&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-slate-600 dark:text-slate-400",children:"Diluição:"}),e.jsx("div",{className:"mt-1 bg-blue-50 dark:bg-blue-900/20 p-2 rounded border border-blue-200 dark:border-blue-800",children:e.jsx("span",{className:"text-slate-700 dark:text-slate-300 font-medium",children:C.dilution})})]})]})]}),e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700",children:[e.jsxs("h4",{className:"font-medium text-slate-700 dark:text-slate-300 text-sm mb-2 flex items-center gap-2",children:[e.jsx(v,{className:"h-4 w-4"}),"Dados Técnicos"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-slate-600 dark:text-slate-400",children:"Fórmula:"}),e.jsx("span",{className:"font-medium text-slate-800 dark:text-slate-200 text-right",children:C.calculatedDoseFormula||u})]}),C.maxDose&&e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-slate-600 dark:text-slate-400",children:"Dose máx:"}),e.jsxs("span",{className:"font-bold text-red-600 dark:text-red-400",children:[I(C.maxDose)," ",C.unit]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-slate-600 dark:text-slate-400",children:"Concentração:"}),e.jsx("span",{className:"font-medium text-slate-800 dark:text-slate-200",children:C.concentration})]}),C.maxVolume&&e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-slate-600 dark:text-slate-400",children:"Vol. máx:"}),e.jsxs("span",{className:"font-bold text-red-600 dark:text-red-400",children:[I(C.maxVolume)," mL"]})]})]})]})]}),m.length>1&&e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700",children:[e.jsxs("h4",{className:"font-medium text-slate-700 dark:text-slate-300 text-sm mb-2 flex items-center gap-2",children:[e.jsx(k,{className:"h-4 w-4"}),"Outras Apresentações"]}),e.jsx("div",{className:"grid gap-2",children:m.map(((s,r)=>e.jsxs("div",{className:a("flex justify-between items-center p-3 rounded-lg cursor-pointer transition-all duration-200 text-xs border",A===r?"bg-slate-100 dark:bg-slate-700 border-slate-300 dark:border-slate-600 shadow-sm":"bg-white dark:bg-slate-700 border-slate-200 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 hover:shadow-sm"),onClick:e=>{e.stopPropagation(),F(r)},children:[e.jsx("div",{className:"font-medium text-slate-800 dark:text-slate-200",children:s.concentration}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"font-bold text-blue-600 dark:text-blue-400 text-sm",children:[I(s.calculatedVolume)," mL"]}),e.jsxs("div",{className:"text-slate-600 dark:text-slate-400",children:[I(s.calculatedDose)," ",s.unit]})]})]},r)))})]}),C.safetyAlerts&&C.safetyAlerts.length>0&&e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border border-red-200 dark:border-red-800",children:[e.jsxs("h4",{className:"font-medium text-red-700 dark:text-red-400 text-sm mb-2 flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4"}),"Alertas de Segurança"]}),e.jsx("div",{className:"space-y-1",children:C.safetyAlerts.map(((a,s)=>e.jsx("div",{className:"text-xs text-red-600 dark:text-red-300 pl-3 border-l-2 border-red-300 dark:border-red-700",children:a},s)))})]}),(C.contraindications&&C.contraindications.length>0||p.length>0)&&e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border border-red-200 dark:border-red-800",children:[e.jsxs("h4",{className:"font-medium text-red-700 dark:text-red-400 text-sm mb-2 flex items-center gap-2",children:[e.jsx(i,{className:"h-4 w-4"}),"Contraindicações"]}),e.jsx("div",{className:"space-y-1",children:(C.contraindications||p).map(((a,s)=>e.jsx("div",{className:"text-xs text-red-600 dark:text-red-300 pl-3 border-l-2 border-red-300 dark:border-red-700",children:a},s)))})]}),(C.sideEffects&&C.sideEffects.length>0||g.length>0)&&e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700",children:[e.jsxs("h4",{className:"font-medium text-slate-700 dark:text-slate-400 text-sm mb-2 flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Efeitos Colaterais"]}),e.jsx("div",{className:"space-y-1",children:(C.sideEffects||g).map(((a,s)=>e.jsx("div",{className:"text-xs text-slate-600 dark:text-slate-300 pl-3 border-l-2 border-slate-300 dark:border-slate-600",children:a},s)))})]}),b.length>0&&e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 border border-slate-200 dark:border-slate-700",children:[e.jsxs("h4",{className:"font-medium text-slate-700 dark:text-slate-400 text-sm mb-2 flex items-center gap-2",children:[e.jsx(w,{className:"h-4 w-4"}),"Orientações Gerais"]}),e.jsx("div",{className:"space-y-1",children:b.map(((a,s)=>e.jsx("div",{className:"text-xs text-slate-600 dark:text-slate-300 pl-3 border-l-2 border-slate-300 dark:border-slate-600",children:a},s)))})]})]})})]}):null},D={lidocaine:[{name:"Lidocaína (ampola 20 mg/mL)",concentration:"20 mg/mL",concentrationValue:20,unit:"mg",multiplier:1.5,doseRange:"1-2 mg/kg",displayType:"range",indications:["Reduzir resposta pressórica e PIC em TCE","Proteger contra laringoespasmo em asma/broncoespasmo"],specialConsiderations:["Sempre usar Lidocaína (para suprimir tosse), salvo contraindicações","Uso NÃO é rotina","Evitar em instabilidade hemodinâmica/hipotensão"],contraindications:["Bloqueios AV","Alergia a anestésicos locais","Instabilidade hemodinâmica"],sideEffects:["Hipotensão","Bradicardia","Convulsões (doses altas)"],dilution:"Não obrigatória; pode diluir 1:1 com SF se administração lenta"}],atropine:[{name:"Atropina (ampola 0,25 mg/mL)",concentration:"0,25 mg/mL",concentrationValue:.25,unit:"mg",multiplier:.02,divisor:.25,maxVolume:2,maxDose:.5,doseRange:"0,02 mg/kg",displayType:"range",indications:["Menores de 1 ano de idade","Se for utilizar Succinilcolina","Instabilidade hemodinâmica","Bradicardia vagal"],specialConsiderations:["Dose máxima: 0,5 mg/dose","Usar preferencialmente em <1 ano ou se uso de succinilcolina"],contraindications:["Taquiarritmia não controlada"],sideEffects:["Taquicardia","Pele seca","Rubor","Midríase","Risco de arritmia se dose alta"],dilution:"Para RN/lactentes, diluir a 0,05 mg/mL (ex: 1 mL de 0,5 mg/mL + 9 mL SF 0,9%)"},{name:"Atropina (ampola 0,5 mg/mL)",concentration:"0,5 mg/mL",concentrationValue:.5,unit:"mg",multiplier:.02,divisor:.5,maxVolume:1,maxDose:.5,doseRange:"0,02 mg/kg",displayType:"range",indications:["Menores de 1 ano de idade","Se for utilizar Succinilcolina","Instabilidade hemodinâmica","Bradicardia vagal"],specialConsiderations:["Dose máxima: 0,5 mg/dose","Usar preferencialmente em <1 ano ou se uso de succinilcolina"],contraindications:["Taquiarritmia não controlada"],sideEffects:["Taquicardia","Pele seca","Rubor","Midríase","Risco de arritmia se dose alta"],dilution:"Usar puro para >10 kg"}],ketamine:[{name:"Cetamina (ampola 50 mg/mL)",concentration:"50 mg/mL",concentrationValue:50,unit:"mg",multiplier:1.5,doseRange:"1-2 mg/kg",displayType:"range",indications:["Instabilidade hemodinâmica","Broncoespasmo","Choque","1ª linha para pediatria"],specialConsiderations:["Atropina prévia reduz secreção","Associar benzodiazepínico se necessário","Para <20 mg, diluir a 10 mg/mL"],contraindications:["TCE grave","HIC","Glaucoma/trauma ocular"],sideEffects:["Hipersecreção","Laringoespasmo","Hipertensão","Alucinações"],dilution:"Para <20 mg, diluir a 10 mg/mL (ex: 1 mL + 4 mL SF)"}],midazolam:[{name:"Midazolam (ampola 1,0 mg/mL)",concentration:"1,0 mg/mL",concentrationValue:1,unit:"mg",multiplier:.2,divisor:1,maxVolume:10,maxDose:10,doseRange:"0,1-0,3 mg/kg",displayType:"range",indications:["Combinação sedativa/amnésica","Status convulsivo","2ª linha"],specialConsiderations:["Dose máxima: 10 mg","Reduzir 50% se instável","Recomendada diluição"],contraindications:["Hipersensibilidade","Depressão respiratória grave"],sideEffects:["Hipotensão","Depressão respiratória","Amnésia"],dilution:"Recomendada (5 mg/mL → 1 mg/mL com SF)"},{name:"Midazolam (ampola 5,0 mg/mL)",concentration:"5,0 mg/mL",concentrationValue:5,unit:"mg",multiplier:.2,divisor:5,maxVolume:2,maxDose:10,doseRange:"0,1-0,3 mg/kg",displayType:"range",indications:["Combinação sedativa/amnésica","Status convulsivo","2ª linha"],specialConsiderations:["Dose máxima: 10 mg","Reduzir 50% se instável","Recomendada diluição"],contraindications:["Hipersensibilidade","Depressão respiratória grave"],sideEffects:["Hipotensão","Depressão respiratória","Amnésia"],dilution:"Recomendada (5 mg/mL → 1 mg/mL com SF)"}],fentanyl:[{name:"Fentanil (ampola 0,05 mg/mL)",concentration:"0,05 mg/mL",concentrationValue:50,unit:"mcg",multiplier:2,divisor:50,doseRange:"1-3 mcg/kg",displayType:"range",indications:["Analgesia potente","Sedação profunda","Combinação com hipnóticos"],specialConsiderations:["Dose máxima: 250 mcg","Administração lenta ≥60s","Ideal 1-2 min","Reduzir dose se instável"],contraindications:["Hipersensibilidade","Depressão respiratória grave"],sideEffects:["Depressão respiratória","Rigidez torácica","Bradicardia","Hipotensão"],dilution:"Para RN/lactentes: diluir para facilitar administração lenta"}],propofol:[{name:"Propofol (ampola 10 mg/mL)",concentration:"10 mg/mL",concentrationValue:10,unit:"mg",multiplier:1.5,divisor:10,doseRange:"1-2 mg/kg",displayType:"range",indications:["Sedação rápida","Manutenção anestésica","Broncodilatação"],specialConsiderations:["Off-label <1 ano","Administração lenta 30-60s","Efeito anticonvulsivante"],contraindications:["Alergia a ovo/soja","Instabilidade hemodinâmica grave"],sideEffects:["Hipotensão","Depressão respiratória","Dor na injeção","Apneia"],dilution:"Não obrigatória; pode usar puro"}],etomidate:[{name:"Etomidato (ampola 2 mg/mL)",concentration:"2 mg/mL",concentrationValue:2,unit:"mg",multiplier:.25,divisor:2,doseRange:"0,2-0,3 mg/kg",displayType:"range",indications:["Instabilidade hemodinâmica","TCE","Choque","Preservação da PA"],specialConsiderations:["Dose máxima: 30 mg","Estabilidade cardiovascular","Não usar em sepse"],contraindications:["Sepse","Insuficiência adrenal","Porfiria"],sideEffects:["Supressão adrenal","Mioclonias","Náuseas/vômitos"],dilution:"Pode usar puro ou diluir 1:1 com SF"}],rocuronium:[{name:"Rocurônio (ampola 10 mg/mL)",concentration:"10 mg/mL",concentrationValue:10,unit:"mg",multiplier:1,divisor:10,doseRange:"1.0 mg/kg",displayType:"fixed",indications:["Bloqueio neuromuscular","Intubação rápida","Alternativa à succinilcolina"],specialConsiderations:["Início: 60-90s","Duração: 30-60 min","Reversível com sugammadex"],contraindications:["Hipersensibilidade","Miastenia gravis"],sideEffects:["Bloqueio prolongado","Reações alérgicas raras"],dilution:"Usar puro"}],succinylcholine:[{name:"Succinilcolina (solução 10 mg/mL)",concentration:"10 mg/mL",concentrationValue:10,unit:"mg",multiplier:1,divisor:10,doseRange:"1.0 mg/kg",displayType:"fixed",indications:["Intubação rápida","Bloqueio neuromuscular ultra-rápido","Via aérea difícil"],specialConsiderations:["Início: 30-60s","Duração: 5-10 min","Sempre usar atropina","Diluir para RN/lactente"],contraindications:["Hipertermia maligna","Miopatias","Queimaduras extensas","Hipercalemia"],sideEffects:["Bradicardia","Fasciculações","Hipercalemia","Hipertermia maligna"],dilution:"Para RN/lactentes: diluir a 2 mg/mL (ex: 1 mL + 4 mL SF)"}],atracurium:[{name:"Atracúrio (ampola 10 mg/mL)",concentration:"10 mg/mL",concentrationValue:10,unit:"mg",multiplier:.5,divisor:10,doseRange:"0.5 mg/kg",displayType:"fixed",indications:["SRI em insuficiência renal/hepática","Contraindicação à succinilcolina/rocurônio","Bloqueio neuromuscular intermediário"],specialConsiderations:["Metabolismo via degradação de Hofmann","Independente de função renal/hepática","Cuidado em asmáticos","Início: 2-3 min","Duração: 20-35 min"],contraindications:["Hipersensibilidade ao atracúrio","Asma grave (relativo)"],sideEffects:["Hipotensão transitória","Rubor","Liberação de histamina","Broncoespasmo (raro)"],dilution:"Usar puro em bólus"}],cisatracurium:[{name:"Cisatracúrio (ampola 2 mg/mL)",concentration:"2 mg/mL",concentrationValue:2,unit:"mg",multiplier:.2,divisor:2,doseRange:"0.2 mg/kg",displayType:"fixed",indications:["SRI em instabilidade hemodinâmica","Insuficiência renal/hepática","Bloqueio neuromuscular seguro"],specialConsiderations:["Metabolismo via degradação de Hofmann","Menos liberação de histamina que atracúrio","Excelente em instáveis hemodinâmicos","Início: 2-3 min","Duração: 40-70 min","Custo mais elevado"],contraindications:["Hipersensibilidade ao cisatracúrio"],sideEffects:["Mínimas alterações hemodinâmicas"],dilution:"Usar puro em bólus"}],pancuronium:[{name:"Pancurônio (ampola 2 mg/mL)",concentration:"2 mg/mL",concentrationValue:2,unit:"mg",multiplier:.09,divisor:2,doseRange:"0.08-0.1 mg/kg",displayType:"range",indications:["SRI em bloqueio prolongado","Indisponibilidade de drogas de escolha","Cirurgias longas"],specialConsiderations:["Duração longa: 60-100 min","Depende da função renal","Pode causar taquicardia","Início: 2-3 min","Efeito vagolítico"],contraindications:["Hipersensibilidade ao pancurônio","Insuficiência renal grave (relativo)"],sideEffects:["Taquicardia","Elevação da pressão arterial","Efeito vagolítico"],dilution:"Usar puro em bólus"}],alfentanil:[{name:"Alfentanila (ampola 0,5 mg/mL)",concentration:"0,5 mg/mL",concentrationValue:.5,unit:"mg",multiplier:20,doseRange:"0,01-0,03 mg/kg",indications:["Analgesia potente (adultos)","Procedimentos curtos","Combinação com hipnóticos"],specialConsiderations:["Uso restrito a adultos","Início rápido","Duração intermediária"],contraindications:["Hipersensibilidade","Depressão respiratória grave","Uso pediátrico"],sideEffects:["Depressão respiratória","Rigidez torácica","Bradicardia"],dilution:"Pode usar puro ou diluir conforme necessário"}],remifentanil:[{name:"Remifentanila (ampola 0,05 mg/mL)",concentration:"0,05 mg/mL",concentrationValue:.05,unit:"mg",multiplier:75e-5,doseRange:"0,0005-0,001 mg/kg",indications:["Analgesia ultra-rápida (adultos)","Procedimentos muito curtos","Recuperação rápida"],specialConsiderations:["Uso restrito a adultos","Metabolismo ultra-rápido","Sem acúmulo"],contraindications:["Hipersensibilidade","Depressão respiratória grave","Uso pediátrico"],sideEffects:["Depressão respiratória","Rigidez torácica","Bradicardia","Hiperalgesia"],dilution:"Reconstituir conforme bula"}]},F=[{category:"Laringoscopia",items:["Laringoscópio com lâminas apropriadas (Miller e/ou Macintosh)","Verificar luz do laringoscópio","Lâminas de tamanhos alternativos"]},{category:"Tubos e acessórios",items:["Tubos orotraqueais (tamanhos ajustados para paciente)","Seringas para balonete (10 mL)","Gel lubrificante","Guia de tubo, mandril maleável","Fio-guia","Pinça de Magill"]},{category:"Aspiração",items:["Aspirador de secreções testado","Sondas de aspiração de vários calibres"]},{category:"Ventilação",items:["AMBU (balão autoinflável) com reservatório","Máscaras faciais de tamanhos apropriados","Máscara laríngea de vários tamanhos"]},{category:"Via aérea cirúrgica",items:["Kit de cricotireoidostomia","Agulha grossa + seringa para cricotireoidostomia"]},{category:"Fixação",items:["Fita/adesivo para fixação do tubo","Fixador específico para tubo"]},{category:"Monitorização",items:["Oximetria de pulso","Monitor cardíaco","Esfigmomanômetro","Capnografia (se disponível)"]}],E=[{name:"Máscara laríngea",sizes:["1","1.5","2","2.5","3","4","5"],indication:"Primeira alternativa em via aérea difícil"},{name:"Tubos de diâmetro menor",indication:"Reduzir 0.5mm do tamanho calculado"},{name:"Laringoscópio com lâmina curva",indication:"Alternativa à lâmina reta em pediatria"},{name:"Bougie/Guia de tubo",indication:"Facilitar passagem do tubo"},{name:"Videolaringoscópio",indication:"Melhor visualização em casos difíceis"},{name:"Kit de cricotireoidostomia",indication:"Via aérea cirúrgica de emergência"}],V={ageRange:"RN (0-1 mês)",respiratoryRate:{min:30,max:50,default:40},tidalVolume:{min:6,max:8,default:7},peep:{min:4,max:6,default:5},fio2:{initial:100,target:21},peakPressure:{max:25,target:20},ieRatio:"1:2",inspiratoryTime:{min:.8,max:1,default:.9}},T={ageRange:"Lactente (1-12 meses)",respiratoryRate:{min:25,max:35,default:30},tidalVolume:{min:6,max:8,default:7},peep:{min:4,max:6,default:5},fio2:{initial:100,target:21},peakPressure:{max:30,target:25},ieRatio:"1:2",inspiratoryTime:{min:.8,max:1.2,default:1}},z={ageRange:"Pré-escolar (1-5 anos)",respiratoryRate:{min:20,max:30,default:25},tidalVolume:{min:6,max:8,default:7},peep:{min:4,max:6,default:5},fio2:{initial:100,target:21},peakPressure:{max:30,target:25},ieRatio:"1:2",inspiratoryTime:{min:1,max:1.2,default:1.1}},O={ageRange:"Escolar (6-12 anos)",respiratoryRate:{min:16,max:25,default:20},tidalVolume:{min:6,max:8,default:7},peep:{min:4,max:6,default:5},fio2:{initial:100,target:21},peakPressure:{max:30,target:25},ieRatio:"1:2",inspiratoryTime:{min:1,max:1.2,default:1.1}},H={ageRange:"Adolescente (13-18 anos)",respiratoryRate:{min:12,max:16,default:14},tidalVolume:{min:6,max:8,default:7},peep:{min:5,max:8,default:5},fio2:{initial:100,target:21},peakPressure:{max:35,target:30},ieRatio:"1:2",inspiratoryTime:{min:1,max:1.2,default:1}},q=()=>{const[a,i]=N.useState(""),[b,h]=N.useState(""),[f,j]=N.useState("years"),[v,k]=N.useState(!1),[y,w]=N.useState("simple"),[I,q]=N.useState(!1),[$,B]=N.useState(!1),[U,G]=N.useState(""),[K,J]=N.useState(""),_=e=>{G(e),q(!0),setTimeout((()=>q(!1)),5e3)},Q=e=>{J(e),B(!0),setTimeout((()=>B(!1)),5e3)},[W,X]=N.useState(!1),[Y,Z]=N.useState(!1),[ee,ae]=N.useState(!1),[se,re]=N.useState(!1),[te,ie]=N.useState(!1),[le,de]=N.useState(!1),[oe,ne]=N.useState(!1),me=(()=>{const e="months"===f?(parseFloat(b)||0)/12:parseFloat(b)||0,s=parseFloat(a)||0;return e>0&&e<14?"pediatric":e>=18||s>=50?"adult":"pediatric"})(),ce={type:me,age:parseFloat(b)||0,ageUnit:f,weight:parseFloat(a)||0},xe=ce.weight>0&&ce.age>0?function(e){const{type:a,age:s,ageUnit:r}=e;if("adult"===a)return{tubeSize:8,tubeType:"cuffed",insertionDepth:23,laryngoscope:{type:"Macintosh",size:3},laryngealMask:{size:4},alternatives:[{tubeSize:7.5,insertionDepth:22},{tubeSize:8.5,insertionDepth:24}]};const t="months"===r?s/12:s;let i,l,d,o,n;return t<1?(i=3.5,l=10,d="Miller",o=t<.5?0:1,n=1):t<8?(i=Math.round(10*(t/4+4))/10,l=Math.round(3*i),d=t<2?"Miller":"Macintosh",o=t<2?1:2,n=t<2?1.5:2):(i=Math.round(10*(t/4+4))/10,l=Math.round(3*i),d="Macintosh",o=t<10?2:3,n=t<10?2.5:3),{tubeSize:i,tubeType:t<8?"uncuffed":"cuffed",insertionDepth:l,laryngoscope:{type:d,size:o},laryngealMask:{size:n},alternatives:[{tubeSize:i-.5,insertionDepth:l-1},{tubeSize:i+.5,insertionDepth:l+1}]}}(ce):null,ue=ce.weight>0&&ce.age>0?function(e){const{type:a,age:s,ageUnit:r}=e;if("adult"===a)return{mode:"VCV",tidalVolume:7,respiratoryRate:14,peep:5,fio2:100,peakPressure:30,ieRatio:"1:2",inspiratoryTime:1};const t="years"===r?12*s:s;let i;return i=t<=1?V:t<=12?T:t<=60?z:t<=144?O:H,{mode:"VCV",tidalVolume:i.tidalVolume.default,respiratoryRate:i.respiratoryRate.default,peep:i.peep.default,fio2:i.fio2.initial,peakPressure:i.peakPressure.target,ieRatio:i.ieRatio,inspiratoryTime:i.inspiratoryTime.default}}(ce):null,pe=e=>{const s=S.find((a=>a.id===e));if(!s)return null;const r="months"===f?(parseFloat(b)||0)/12:parseFloat(b)||0,t=((e,a,s)=>(D[e]||[]).map((e=>{const{volume:r,dose:t,doseFormula:i,administration:l,minVolume:d,maxVolume:o,minDose:n,maxDose:m,volumeRange:c,doseRange:x,calculationSteps:u,safetyAlerts:p}=((e,a,s)=>{let r=0,t=0,i=0,l="",d="Administrar por via intravenosa conforme protocolo";if(s.name.includes("Succinilcolina"))a<2?(r=2*e,t=r,i=r,l="2.0 mg/kg (RN/lactente)",d="Administrar em bólus intravenoso para relaxamento muscular despolarizante"):(r=Math.min(1*e,150),t=r,i=r,l="1.0 mg/kg (máx: 150 mg)",d="Administrar em bólus intravenoso para relaxamento muscular despolarizante");else if(s.name.includes("Atropina")){const s=.02*e;r=Math.max(s,.1),r=Math.min(r,3),t=.1,i=Math.min(.02*e,3),i<.1&&(i=.1),l="0,02 mg/kg (mín: 0,1 mg, máx: 3 mg)",d=a<1?"Administrar em bólus intravenoso como pré-medicação anticolinérgica em lactentes":"Administrar em bólus intravenoso se bradicardia ou uso de succinilcolina"}else if(s.name.includes("Propofol"))t=1*e,i=Math.min(2*e,200),r=1.5*e,l="1-2 mg/kg (máx: 200 mg)",d=a<1?"Administrar lentamente em 60 segundos (uso off-label em lactentes)":"Administrar em bólus intravenoso lento (30-60s) para indução anestésica";else if(s.name.includes("Midazolam"))t=.1*e,i=Math.min(.3*e,10),r=.2*e,l="0,1-0,3 mg/kg (máx: 10 mg)",d="Infundir lentamente por via intravenosa (60 segundos) para sedação consciente e ansiolise";else if(s.name.includes("Fentanil"))t=1*e,i=Math.min(3*e,250),r=2*e,l="1-3 mcg/kg (máx: 250 mcg)",d="Infundir lentamente por via intravenosa (mínimo 60 segundos) para analgesia potente",a<1&&(d="Infundir muito lentamente diluído para evitar rigidez torácica em lactentes");else if(s.name.includes("Cetamina"))t=1*e,i=Math.min(2*e,200),r=1.5*e,l="1-2 mg/kg (máx: 200 mg)",d="Administrar lentamente por via intravenosa (30-60s) para anestesia dissociativa";else if(s.name.includes("Etomidato"))a<18?(t=.2*e,i=Math.min(.4*e,20),r=Math.min(.3*e,15),l="0,2-0,4 mg/kg (máx: 20 mg)"):(t=.2*e,i=Math.min(.3*e,30),r=Math.min(.25*e,25),l="0,2-0,3 mg/kg (máx: 30 mg)"),d="Administrar em bólus intravenoso (30-60s) para indução anestésica com estabilidade hemodinâmica";else if(s.name.includes("Rocurônio"))r=Math.min(1*e,120),t=r,i=r,l="1.0 mg/kg (máx: 120 mg)",d="Administrar em bólus intravenoso para bloqueio neuromuscular não-despolarizante";else if(s.name.includes("Atracúrio"))r=Math.min(.5*e,50),t=r,i=r,l="0.5 mg/kg (máx: 50 mg)",d="Administrar por via intravenosa conforme protocolo";else if(s.name.includes("Cisatracúrio"))r=Math.min(.2*e,20),t=r,i=r,l="0.2 mg/kg (máx: 20 mg)",d="Administrar por via intravenosa conforme protocolo";else if(s.name.includes("Pancurônio"))t=Math.min(.08*e,8),i=Math.min(.1*e,10),r=(t+i)/2,l="0.08-0.1 mg/kg (máx: 10 mg)",d="Administrar por via intravenosa conforme protocolo";else if(s.name.includes("Lidocaína"))t=1*e,i=Math.min(2*e,200),r=1.5*e,l="1-2 mg/kg (máx: 200 mg)",d="Administrar em bólus intravenoso para supressão do reflexo de tosse durante laringoscopia";else if(s.doseRange){const a=s.doseRange.match(/([\d,\.]+)-([\d,\.]+)/);if(a)t=e*parseFloat(a[1].replace(",",".")),i=e*parseFloat(a[2].replace(",",".")),r=(t+i)/2,l=s.doseRange;else{const a=s.doseRange.match(/([\d,\.]+)\s*mg\/kg/);a?(r=e*parseFloat(a[1].replace(",",".")),t=.9*r,i=1.1*r,l=s.doseRange):(r=e*s.multiplier,t=.8*r,i=1.2*r,l=`${s.multiplier} ${s.unit}/kg`)}}else r=e*s.multiplier,t=.8*r,i=1.2*r,l=`${s.multiplier} ${s.unit}/kg`;const o=s.name.includes("Succinilcolina")||s.name.includes("Atropina")||s.name.includes("Propofol")||s.name.includes("Midazolam")||s.name.includes("Fentanil")||s.name.includes("Cetamina")||s.name.includes("Etomidato")||s.name.includes("Rocurônio")||s.name.includes("Atracúrio")||s.name.includes("Cisatracúrio")||s.name.includes("Pancurônio")||s.name.includes("Lidocaína");!o&&s.maxDose&&i>s.maxDose&&(i=s.maxDose,t>s.maxDose&&(t=s.maxDose),r>s.maxDose&&(r=s.maxDose));let n,m,c=r/s.concentrationValue,x=t/s.concentrationValue,u=i/s.concentrationValue;!o&&s.maxVolume&&u>s.maxVolume&&(u=s.maxVolume,x>s.maxVolume&&(x=s.maxVolume),c>s.maxVolume&&(c=s.maxVolume,r=c*s.concentrationValue)),x>u&&(x=u),t>i&&(t=i),"fixed"===s.displayType?(n=`${c.toFixed(1)} mL`,m=`${r.toFixed(1)} ${s.unit}`):(n=x.toFixed(1)===u.toFixed(1)?`${x.toFixed(1)} mL`:`${x.toFixed(1)}-${u.toFixed(1)} mL`,m=t.toFixed(1)===i.toFixed(1)?`${t.toFixed(1)} ${s.unit}`:`${t.toFixed(1)}-${i.toFixed(1)} ${s.unit}`);const p=`dose = ${e}kg × ${l} = ${r.toFixed(1)} ${s.unit}; volume = ${r.toFixed(1)} ${s.unit} ÷ ${s.concentrationValue} ${s.unit}/mL = ${c.toFixed(1)} mL`,g=[];return a<1&&(s.dilution&&g.push(`⚠️ DILUIÇÃO OBRIGATÓRIA: ${s.dilution}`),(s.name.includes("Fentanil")||s.name.includes("Alfentanila")||s.name.includes("Remifentanila"))&&g.push("⚠️ ADMINISTRAÇÃO ULTRA-LENTA: Administrar em 1-2 minutos para evitar rigidez torácica"),s.name.includes("Propofol")&&g.push("⚠️ OFF-LABEL: Uso em <1 ano é off-label - considerar alternativas")),s.maxDose&&r>=s.maxDose&&g.push(`⚠️ DOSE MÁXIMA: Atingiu dose máxima de ${s.maxDose} ${s.unit}`),c>10&&g.push("⚠️ VOLUME ALTO: Considerar diluição ou apresentação mais concentrada"),{volume:c,dose:r,doseFormula:l,administration:d,minVolume:x,maxVolume:u,minDose:t,maxDose:i,volumeRange:n,doseRange:m,calculationSteps:p,safetyAlerts:g.length>0?g:void 0}})(a,s,e);return{...e,calculatedVolume:r,calculatedDose:t,calculatedDoseFormula:i,calculatedAdministration:l,calculatedMinVolume:d,calculatedMaxVolume:o,calculatedMinDose:n,calculatedMaxDose:m,calculatedVolumeRange:c,calculatedDoseRange:x,calculationSteps:u,safetyAlerts:p}})))(e,parseFloat(a)||0,r);return{medicationId:e,weight:parseFloat(a)||0,patientType:me,category:s.category,presentations:t,route:"Endovenoso em bólus",timing:"Conforme protocolo",doseFormula:s.doseFormula||"Calculado automaticamente",contraindications:s.contraindications||[],sideEffects:s.sideEffects||[],observations:s.specialConsiderations||[],isRecommended:!0,ageInYears:r}};return e.jsxs("div",{className:"max-w-7xl mx-auto p-2 md:p-4 space-y-2 md:space-y-3",children:[e.jsxs("div",{className:"p-3 bg-white dark:bg-slate-800 border rounded-lg",children:[e.jsx("div",{className:"mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:e.jsxs("p",{className:"text-sm text-blue-800 dark:text-blue-200 font-medium flex items-center gap-2 justify-center",children:[e.jsx(r,{className:"h-4 w-4 flex-shrink-0"}),"Preencha peso e idade para calcular as doses"]})}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(l,{className:"text-sm font-medium mb-1 block",children:"Peso do paciente:"}),e.jsxs("div",{className:"relative",children:[e.jsx(d,{type:"number",value:a,onChange:e=>(e=>{i(e);const a=parseFloat(e);isNaN(a)||(a>200?(_("Peso máximo aceito é 200 kg. Para pacientes acima disso, calcule as doses usando o teto absoluto seguro para cada fármaco."),i(200..toString())):a<1&&a>0&&_("Peso mínimo recomendado é 1 kg (prematuros extremos). Abaixo disso, personalize manualmente as doses."))})(e.target.value),placeholder:"1-200 kg",className:"w-full h-10 text-sm pr-8",step:"0.1",min:"1",max:"200"}),e.jsx("span",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground",children:"kg"})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx(l,{className:"text-sm font-medium mb-1 block",children:"Idade:"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{type:"number",value:b,onChange:e=>(e=>{h(e);const a=parseFloat(e);isNaN(a)||("years"===f&&a>120?(Q("Idade máxima aceita é 120 anos. Por favor, revise o valor inserido."),h(120..toString())):"months"===f&&a>12&&(Q("Máximo 12 meses aceito."),h(12..toString())))})(e.target.value),placeholder:"years"===f?"0-120 anos":"0-12 meses",className:"flex-1 h-10 text-sm",step:"1",min:"0",max:"years"===f?120:12}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("button",{type:"button",onClick:()=>j("years"),className:"px-3 py-2 text-sm rounded "+("years"===f?"bg-primary text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"),children:"Anos"}),e.jsx("button",{type:"button",onClick:()=>j("months"),className:"px-3 py-2 text-sm rounded "+("months"===f?"bg-primary text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"),children:"Meses"})]})]})]})]}),b&&parseFloat(b)>0||a&&parseFloat(a)>0?e.jsxs("div",{className:"flex items-center justify-center gap-3 p-3 bg-primary/10 dark:bg-primary/20 rounded-lg",children:["pediatric"===me?e.jsx(o,{className:"h-5 w-5 text-primary"}):e.jsx(A,{className:"h-5 w-5 text-primary"}),e.jsxs("span",{className:"text-base font-semibold text-primary",children:["Protocolo: ","pediatric"===me?"Pediátrico":"Adulto"]})]}):e.jsx("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(r,{className:"h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-semibold text-blue-800 dark:text-blue-200",children:"Sistema de Classificação Automática"}),e.jsxs("div",{className:"text-xs text-blue-700 dark:text-blue-300 space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Pediátrico:"})," ","<","14 anos (independente do peso)"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Adulto:"})," ≥18 anos OU ≥50kg (para 14-17 anos)"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Transição:"})," 14-17 anos com ","<","50kg = Pediátrico"]})]})]})]})}),I&&e.jsxs("div",{className:"flex items-start gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:[e.jsx(t,{className:"h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-red-800 dark:text-red-200 leading-relaxed",children:U})]}),$&&e.jsxs("div",{className:"flex items-start gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:[e.jsx(t,{className:"h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-red-800 dark:text-red-200 leading-relaxed",children:K})]})]})]}),(!a||parseFloat(a)<=0)&&e.jsx("div",{className:"p-6 bg-gray-50 dark:bg-gray-800/50 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-3",children:[e.jsx("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:e.jsx(R,{className:"h-8 w-8 text-primary"})}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"Pronto para calcular as doses SRI"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4 max-w-md mx-auto",children:"Preencha o peso e idade do paciente acima para visualizar o protocolo completo de medicações para Sequência Rápida de Intubação."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 text-xs text-muted-foreground justify-center items-center",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-1 h-1 bg-green-500 rounded-full"}),"Cálculos automáticos por peso"]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-1 h-1 bg-green-500 rounded-full"}),"Doses máximas de segurança"]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-1 h-1 bg-green-500 rounded-full"}),"Protocolo pediátrico/adulto"]})]})]})]})}),a&&parseFloat(a)>0&&e.jsx("div",{className:"bg-white dark:bg-slate-800 border rounded-lg p-3 mb-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>w("simple"),className:"px-3 py-1.5 rounded-lg font-medium text-sm transition-all "+("simple"===y?"bg-blue-500 text-white shadow-md":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:"📋 Simples"}),e.jsx("button",{onClick:()=>w("complete"),className:"px-3 py-1.5 rounded-lg font-medium text-sm transition-all "+("complete"===y?"bg-blue-500 text-white shadow-md":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:"📚 Completa"})]})}),a&&parseFloat(a)>0&&e.jsxs("div",{className:"space-y-4",children:["simple"===y&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-white dark:bg-slate-800 border rounded-lg p-3",children:[e.jsx("h3",{className:"text-base font-bold text-slate-800 dark:text-slate-200 mb-2",children:"💊 Pré-medicação"}),e.jsxs("div",{className:"space-y-2",children:[D.lidocaine&&e.jsx(M,{...pe("lidocaine"),isRecommended:!0,priority:"pediatric"===me?"1ª linha":void 0}),"pediatric"===me&&D.atropine&&e.jsx(M,{...pe("atropine"),isRecommended:!1})]})]}),e.jsxs("div",{className:"bg-white dark:bg-slate-800 border rounded-lg p-3",children:[e.jsx("h3",{className:"text-base font-bold text-green-800 dark:text-green-200 mb-2",children:"🥇 Sedoanalgesia - 1ª Linha"}),e.jsxs("div",{className:"space-y-2",children:[D.ketamine&&e.jsx(M,{...pe("ketamine"),isRecommended:!0,priority:"1ª linha"}),"adult"===me&&D.propofol&&e.jsx(M,{...pe("propofol"),isRecommended:!0,priority:"1ª linha"})]})]}),e.jsxs("div",{className:"bg-white dark:bg-slate-800 border rounded-lg p-3",children:[e.jsx("h3",{className:"text-base font-bold text-orange-800 dark:text-orange-200 mb-2",children:"🥈 Sedoanalgesia - 2ª Linha"}),e.jsxs("div",{className:"space-y-2",children:[D.midazolam&&e.jsx(M,{...pe("midazolam"),priority:"2ª linha"}),D.fentanyl&&e.jsx(M,{...pe("fentanyl"),priority:"2ª linha"}),"adult"===me&&D.alfentanil&&e.jsx(M,{...pe("alfentanil"),priority:"2ª linha"}),"adult"===me&&D.remifentanil&&e.jsx(M,{...pe("remifentanil"),priority:"2ª linha"})]})]}),e.jsxs("div",{className:"bg-white dark:bg-slate-800 border rounded-lg p-3",children:[e.jsx("h3",{className:"text-base font-bold text-purple-800 dark:text-purple-200 mb-2",children:"⚡ Outras Opções"}),e.jsxs("div",{className:"space-y-2",children:["pediatric"===me&&D.propofol&&e.jsx(M,{...pe("propofol"),priority:"Outras opções"}),D.etomidate&&e.jsx(M,{...pe("etomidate"),priority:"Outras opções"})]})]}),e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-700",children:[e.jsxs("h3",{className:"text-base font-bold text-red-800 dark:text-red-200 flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full flex-shrink-0"}),e.jsx("span",{children:"🚨 Bloqueio Neuromuscular"})]}),e.jsxs("div",{className:"space-y-2",children:[D.succinylcholine&&e.jsx(M,{...pe("succinylcholine"),isRecommended:!0,priority:"1ª linha"}),D.rocuronium&&e.jsx(M,{...pe("rocuronium"),priority:"2ª linha"}),D.atracurium&&e.jsx(M,{...pe("atracurium"),priority:"Outras opções"}),D.cisatracurium&&e.jsx(M,{...pe("cisatracurium"),priority:"Outras opções"}),D.pancuronium&&e.jsx(M,{...pe("pancuronium"),priority:"Outras opções"})]})]})]}),"complete"===y&&e.jsxs(e.Fragment,{children:[e.jsx(n,{className:"border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 shadow-sm hover:shadow-md transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>X(!W),className:"cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-sm",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P1"})}),e.jsx("h3",{className:"text-base font-bold text-blue-800 dark:text-blue-200",children:"1º P - Preparação"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${W?"bg-slate-400":"bg-gray-300"} transition-colors duration-200`}),W?e.jsx(c,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}):e.jsx(x,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"})]})]}),e.jsx("div",{className:"text-xs text-slate-600 dark:text-slate-400 mt-1 leading-relaxed",children:"Material • Acesso • Monitor • Equipe"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P1"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2 leading-tight",children:"1º P - Preparação"}),e.jsx("div",{className:"text-xs lg:text-sm text-blue-600 dark:text-blue-400 leading-relaxed",children:"Material • Acesso • Monitor • Equipe"})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${W?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),W?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]})})]}),W&&e.jsxs("div",{className:"space-y-4 md:space-y-6 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:[xe?e.jsxs("div",{children:[e.jsxs("h4",{className:"text-lg font-bold text-blue-900 dark:text-blue-100 mb-4 flex items-center gap-3",children:[e.jsx(u,{className:"h-5 w-5 text-blue-600"}),"Materiais Calculados - ","adult"===me?"Adulto":"Pediátrico"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4",children:[e.jsxs("div",{className:"bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 p-3 md:p-4 rounded-xl border border-blue-300 dark:border-blue-700 shadow-sm",children:[e.jsx("p",{className:"text-xs md:text-sm font-semibold text-blue-900 dark:text-blue-200 mb-1",children:"Tubo Orotraqueal"}),e.jsxs("p",{className:"text-base md:text-lg font-bold text-blue-800 dark:text-blue-300",children:[xe.tubeSize," mm"]}),e.jsxs("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:["(",xe.tubeType,")"]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/40 dark:to-green-800/40 p-3 md:p-4 rounded-xl border border-green-300 dark:border-green-700 shadow-sm",children:[e.jsx("p",{className:"text-xs md:text-sm font-semibold text-green-900 dark:text-green-200 mb-1",children:"Profundidade"}),e.jsxs("p",{className:"text-base md:text-lg font-bold text-green-800 dark:text-green-300",children:[xe.insertionDepth," cm"]}),e.jsx("p",{className:"text-xs text-green-600 dark:text-green-400",children:"na comissura labial"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/40 p-3 md:p-4 rounded-xl border border-purple-300 dark:border-purple-700 shadow-sm",children:[e.jsx("p",{className:"text-xs md:text-sm font-semibold text-purple-900 dark:text-purple-200 mb-1",children:"Laringoscópio"}),e.jsx("p",{className:"text-base md:text-lg font-bold text-purple-800 dark:text-purple-300",children:xe.laryngoscope.type}),e.jsxs("p",{className:"text-xs text-purple-600 dark:text-purple-400",children:["Tamanho ",xe.laryngoscope.size]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/40 dark:to-orange-800/40 p-3 md:p-4 rounded-xl border border-orange-300 dark:border-orange-700 shadow-sm",children:[e.jsx("p",{className:"text-xs md:text-sm font-semibold text-orange-900 dark:text-orange-200 mb-1",children:"Máscara Laríngea"}),e.jsxs("p",{className:"text-base md:text-lg font-bold text-orange-800 dark:text-orange-300",children:["Tamanho ",xe.laryngealMask?.size]}),e.jsx("p",{className:"text-xs text-orange-600 dark:text-orange-400",children:"backup via aérea"})]})]})]}):e.jsxs("div",{className:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/30 p-6 rounded-xl border border-amber-200 dark:border-amber-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-amber-800 dark:text-amber-200 mb-3 flex items-center gap-3",children:[e.jsx(t,{className:"h-5 w-5 text-amber-600"}),"Dados Necessários"]}),e.jsx("p",{className:"text-amber-700 dark:text-amber-300",children:"Insira o peso e idade do paciente para calcular os materiais específicos."})]}),e.jsxs("div",{className:"bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-900/50 p-6 rounded-xl border border-slate-200 dark:border-slate-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-slate-800 dark:text-slate-200 mb-4 flex items-center gap-3",children:[e.jsx(p,{className:"h-5 w-5 text-green-600"}),"Equipamentos Obrigatórios"]}),e.jsx("div",{className:"space-y-5",children:F.map(((a,s)=>e.jsxs("div",{className:"bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-600",children:[e.jsx("h5",{className:"font-semibold text-slate-700 dark:text-slate-300 mb-3 text-sm uppercase tracking-wide",children:a.category}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:a.items.map(((a,s)=>e.jsxs("div",{className:"flex items-center gap-3 p-2 rounded-md hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full flex-shrink-0"}),e.jsx("span",{className:"text-sm text-slate-700 dark:text-slate-300 font-medium",children:a})]},s)))})]},s)))})]}),e.jsxs("div",{className:"bg-gradient-to-br from-amber-50 to-yellow-100 dark:from-amber-900/30 dark:to-yellow-900/30 p-6 rounded-xl border-l-4 border-amber-400 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-amber-800 dark:text-amber-200 mb-4 flex items-center gap-3",children:[e.jsx(t,{className:"h-5 w-5 text-amber-600"}),"Materiais para Via Aérea Difícil"]}),e.jsx("div",{className:"space-y-4",children:E.map(((a,s)=>e.jsx("div",{className:"bg-white dark:bg-amber-900/20 p-4 rounded-lg border border-amber-200 dark:border-amber-700 hover:shadow-md transition-shadow",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-3 h-3 bg-amber-500 rounded-full mt-1 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2 mb-2",children:[e.jsx("span",{className:"text-sm font-semibold text-amber-900 dark:text-amber-200",children:a.name}),a.sizes&&e.jsxs("span",{className:"text-xs bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 px-2 py-1 rounded-full",children:["Tamanhos: ",a.sizes.join(", ")]})]}),e.jsx("p",{className:"text-sm text-amber-700 dark:text-amber-300 leading-relaxed",children:a.indication})]})]})},s)))})]})]})]})}),e.jsx(n,{className:"border-2 border-green-200 dark:border-green-800 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>Z(!Y),className:"cursor-pointer hover:bg-green-100/30 dark:hover:bg-green-900/30 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P2"})}),e.jsx("h3",{className:"text-base font-bold text-green-900 dark:text-green-100",children:"2º P - Pré-oxigenação"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${Y?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),Y?e.jsx(c,{className:"h-5 w-5 text-green-600 dark:text-green-400"}):e.jsx(x,{className:"h-5 w-5 text-green-600 dark:text-green-400"})]})]}),e.jsxs("div",{className:"text-xs text-green-600 dark:text-green-400 leading-relaxed flex items-center gap-1",children:["O₂ 100% por 3-5 min • ",e.jsx(t,{className:"h-3 w-3 flex-shrink-0"})," OBRIGATÓRIA"]})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P2"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-green-900 dark:text-green-100 mb-2 leading-tight",children:"2º P - Pré-oxigenação"}),e.jsxs("div",{className:"text-xs lg:text-sm text-green-600 dark:text-green-400 leading-relaxed flex items-center gap-1",children:["O₂ 100% por 3-5 min • ",e.jsx(t,{className:"h-3 w-3 flex-shrink-0"})," OBRIGATÓRIA"]})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${Y?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),Y?e.jsx(c,{className:"h-5 w-5 text-green-600 dark:text-green-400"}):e.jsx(x,{className:"h-5 w-5 text-green-600 dark:text-green-400"})]})]})})]}),Y&&e.jsxs("div",{className:"space-y-4 md:space-y-6 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:[e.jsxs("div",{className:"bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/40 dark:to-red-800/40 p-6 rounded-xl border border-red-300 dark:border-red-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-red-800 dark:text-red-200 mb-4 flex items-center gap-3",children:[e.jsx(P,{className:"h-5 w-5 text-red-600"}),"Protocolo de Pré-oxigenação"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-red-900 dark:text-red-200",children:"Objetivo:"}),e.jsx("span",{className:"text-sm text-red-700 dark:text-red-300 bg-red-200 dark:bg-red-800 px-2 py-1 rounded",children:"SpO2 ≥95% e EtO2 ≥90%"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-red-900 dark:text-red-200",children:"Tempo:"}),e.jsx("span",{className:"text-sm text-red-700 dark:text-red-300 bg-red-200 dark:bg-red-800 px-2 py-1 rounded",children:"3-5 minutos"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-red-900 dark:text-red-200",children:"Fluxo:"}),e.jsx("span",{className:"text-sm text-red-700 dark:text-red-300 bg-red-200 dark:bg-red-800 px-2 py-1 rounded",children:"10-12 L/min O2 100%"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-red-900 dark:text-red-200",children:"Método:"}),e.jsx("span",{className:"text-sm text-red-700 dark:text-red-300 bg-red-200 dark:bg-red-800 px-2 py-1 rounded",children:"Máscara facial vedada"})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 p-5 rounded-xl border border-blue-300 dark:border-blue-700 shadow-sm",children:[e.jsx("h5",{className:"text-sm font-bold text-blue-900 dark:text-blue-200 mb-2",children:"Paciente Cooperativo"}),e.jsx("p",{className:"text-blue-700 dark:text-blue-300 text-sm leading-relaxed",children:"Respiração espontânea profunda por 3-5 minutos"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-yellow-100 to-yellow-200 dark:from-yellow-900/40 dark:to-yellow-800/40 p-5 rounded-xl border border-yellow-300 dark:border-yellow-700 shadow-sm",children:[e.jsx("h5",{className:"text-sm font-bold text-yellow-900 dark:text-yellow-200 mb-2",children:"Paciente Não-cooperativo"}),e.jsx("p",{className:"text-yellow-700 dark:text-yellow-300 text-sm leading-relaxed",children:"8 respirações profundas em 60 segundos"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800/50 dark:to-slate-900/50 p-5 rounded-xl border border-slate-300 dark:border-slate-700 shadow-sm",children:[e.jsx("h5",{className:"text-lg font-bold text-slate-800 dark:text-slate-200 mb-3",children:"Contraindicações Relativas:"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-sm text-slate-700 dark:text-slate-300",children:"Pneumotórax não drenado"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-sm text-slate-700 dark:text-slate-300",children:"Distensão gástrica importante"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-sm text-slate-700 dark:text-slate-300",children:"Vômitos ativos"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-sm text-slate-700 dark:text-slate-300",children:"Trauma facial grave"})]})]})]})]})]})}),e.jsx(n,{className:"border-2 border-red-200 dark:border-red-800 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950 dark:to-red-900 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>ae(!ee),className:"cursor-pointer hover:bg-red-100/30 dark:hover:bg-red-900/30 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P3"})}),e.jsx("h3",{className:"text-base font-bold text-red-900 dark:text-red-100",children:"3º P - Pré-tratamento"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${ee?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),ee?e.jsx(c,{className:"h-5 w-5 text-red-600 dark:text-red-400"}):e.jsx(x,{className:"h-5 w-5 text-red-600 dark:text-red-400"})]})]}),e.jsxs("div",{className:"text-xs text-red-600 dark:text-red-400 leading-relaxed flex items-center gap-1",children:[e.jsx(t,{className:"h-3 w-3 flex-shrink-0"})," NÃO É ROTINA • Lidocaína, Atropina"]})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P3"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-red-900 dark:text-red-100 mb-2 leading-tight",children:"3º P - Pré-tratamento"}),e.jsxs("div",{className:"text-xs lg:text-sm text-red-600 dark:text-red-400 leading-relaxed flex items-center gap-1",children:[e.jsx(t,{className:"h-3 w-3 flex-shrink-0"})," NÃO É ROTINA • Lidocaína, Atropina"]})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${ee?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),ee?e.jsx(c,{className:"h-5 w-5 text-red-600 dark:text-red-400"}):e.jsx(x,{className:"h-5 w-5 text-red-600 dark:text-red-400"})]})]})})]}),ee&&e.jsx("div",{className:"space-y-4 md:space-y-6 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:e.jsxs("div",{className:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/30 p-6 rounded-xl border border-amber-200 dark:border-amber-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-amber-800 dark:text-amber-200 mb-4 flex items-center gap-3",children:[e.jsx(t,{className:"h-5 w-5 text-amber-600"}),"Medicações de Pré-tratamento"]}),e.jsx("div",{className:"bg-amber-200 dark:bg-amber-800/50 p-3 rounded-lg mb-4",children:e.jsxs("p",{className:"text-sm text-amber-800 dark:text-amber-200 font-semibold flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 flex-shrink-0"}),"Usar apenas quando há indicação clínica específica"]})}),e.jsxs("div",{className:"space-y-4",children:[D.lidocaine&&e.jsx(M,{...pe("lidocaine"),isRecommended:!0,priority:"pediatric"===me?"1ª linha":void 0}),"pediatric"===me&&D.atropine&&e.jsx(M,{...pe("atropine"),isRecommended:!1})]})]})})]})}),e.jsx(n,{className:"border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>re(!se),className:"cursor-pointer hover:bg-blue-100/30 dark:hover:bg-blue-900/30 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P4"})}),e.jsx("h3",{className:"text-base font-bold text-blue-900 dark:text-blue-100",children:"4º P - Paralisia com Indução"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${se?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),se?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]}),e.jsx("div",{className:"text-xs text-blue-600 dark:text-blue-400 leading-relaxed",children:"adult"===me?"Ketamina, Propofol • Succinilcolina":"Ketamina, Midazolam • Succinilcolina"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P4"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2 leading-tight",children:"4º P - Paralisia com Indução"}),e.jsx("div",{className:"text-xs lg:text-sm text-blue-600 dark:text-blue-400 leading-relaxed",children:"adult"===me?"Ketamina, Propofol • Succinilcolina":"Ketamina, Midazolam • Succinilcolina"})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${se?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),se?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]})})]}),se&&e.jsxs("div",{className:"space-y-6 md:space-y-8 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:[e.jsxs("div",{className:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 p-6 rounded-xl border border-green-200 dark:border-green-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-lg font-bold text-green-800 dark:text-green-200 flex items-center gap-3",children:[e.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full shadow-sm"}),"1ª Linha - Opções Preferenciais"]}),e.jsx("p",{className:"text-sm text-green-700 dark:text-green-300 mt-1",children:"Medicações de primeira escolha para indução"})]}),e.jsxs("div",{className:"space-y-4",children:[D.ketamine&&e.jsx(M,{...pe("ketamine"),isRecommended:!0,priority:"1ª linha"}),"adult"===me&&D.propofol&&e.jsx(M,{...pe("propofol"),isRecommended:!0,priority:"1ª linha"})]})]}),e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 p-6 rounded-xl border border-slate-200 dark:border-slate-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-lg font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3",children:[e.jsx("div",{className:"w-4 h-4 bg-slate-400 rounded-full shadow-sm"}),"2ª Linha - Alternativas"]}),e.jsx("p",{className:"text-sm text-slate-600 dark:text-slate-400 mt-1",children:"Opções alternativas quando 1ª linha não disponível"})]}),e.jsxs("div",{className:"space-y-4",children:[D.midazolam&&e.jsx(M,{...pe("midazolam"),priority:"2ª linha"}),D.fentanyl&&e.jsx(M,{...pe("fentanyl"),priority:"2ª linha"}),"adult"===me&&D.alfentanil&&e.jsx(M,{...pe("alfentanil"),priority:"2ª linha"}),"adult"===me&&D.remifentanil&&e.jsx(M,{...pe("remifentanil"),priority:"2ª linha"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-900/50 p-6 rounded-xl border border-slate-200 dark:border-slate-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-lg font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3",children:[e.jsx("div",{className:"w-4 h-4 bg-slate-500 rounded-full shadow-sm"}),"Outras Opções - Situações Especiais"]}),e.jsx("p",{className:"text-sm text-slate-700 dark:text-slate-300 mt-1",children:"Para casos específicos ou contraindicações"})]}),e.jsxs("div",{className:"space-y-4",children:["pediatric"===me&&D.propofol&&e.jsx(M,{...pe("propofol"),priority:"Outras opções"}),D.etomidate&&e.jsx(M,{...pe("etomidate"),priority:"Outras opções"})]})]}),e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 p-6 rounded-xl border border-red-200 dark:border-red-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-lg font-bold text-red-800 dark:text-red-200 flex items-center gap-3",children:[e.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full shadow-sm flex-shrink-0"}),e.jsx("span",{className:"flex-1 min-w-0",children:"Bloqueio Neuromuscular - Paralisia Completa"})]}),e.jsx("div",{className:"bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg mt-2 border border-orange-200 dark:border-orange-800",children:e.jsxs("p",{className:"text-sm text-orange-800 dark:text-orange-200 font-semibold flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 flex-shrink-0"}),"Administrar IMEDIATAMENTE após a indução"]})})]}),e.jsxs("div",{className:"space-y-4",children:[D.succinylcholine&&e.jsx(M,{...pe("succinylcholine"),isRecommended:!0,priority:"1ª linha"}),D.rocuronium&&e.jsx(M,{...pe("rocuronium"),priority:"2ª linha"}),D.atracurium&&e.jsx(M,{...pe("atracurium"),priority:"Outras opções"}),D.cisatracurium&&e.jsx(M,{...pe("cisatracurium"),priority:"Outras opções"}),D.pancuronium&&e.jsx(M,{...pe("pancuronium"),priority:"Outras opções"})]})]})]})]})}),e.jsx(n,{className:"border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>ie(!te),className:"cursor-pointer hover:bg-blue-100/30 dark:hover:bg-blue-900/30 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P5"})}),e.jsx("h3",{className:"text-base font-bold text-blue-900 dark:text-blue-100",children:"5º P - Posicionamento"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${te?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),te?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]}),e.jsx("div",{className:"text-xs text-blue-600 dark:text-blue-400 leading-relaxed",children:"Posição olfativa • Cabeça elevada • Alinhamento"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P5"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2 leading-tight",children:"5º P - Posicionamento"}),e.jsx("div",{className:"text-xs lg:text-sm text-blue-600 dark:text-blue-400 leading-relaxed",children:"Posição olfativa • Cabeça elevada • Alinhamento"})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${te?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),te?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]})})]}),te&&e.jsxs("div",{className:"space-y-4 md:space-y-6 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:[e.jsxs("div",{className:"bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 p-6 rounded-xl border border-blue-300 dark:border-blue-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-3",children:[e.jsx(L,{className:"h-5 w-5 text-blue-600"}),"Posição Olfativa (Sniffing Position)"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-blue-900 dark:text-blue-200",children:"Objetivo:"}),e.jsx("span",{className:"text-sm text-blue-700 dark:text-blue-300 bg-blue-200 dark:bg-blue-800 px-2 py-1 rounded",children:"Alinhamento dos eixos"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-blue-900 dark:text-blue-200",children:"Técnica:"}),e.jsx("span",{className:"text-sm text-blue-700 dark:text-blue-300 bg-blue-200 dark:bg-blue-800 px-2 py-1 rounded",children:"Flexão + extensão"})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4",children:[e.jsxs("div",{className:"bg-slate-100 dark:bg-slate-700/50 p-5 rounded-xl border border-slate-200 dark:border-slate-600 shadow-sm",children:[e.jsx("h5",{className:"text-sm font-bold text-slate-800 dark:text-slate-200 mb-2",children:"Adultos"}),e.jsx("p",{className:"text-slate-600 dark:text-slate-400 text-sm leading-relaxed",children:"Coxim de 8-10 cm sob os ombros"})]}),e.jsxs("div",{className:"bg-slate-100 dark:bg-slate-700/50 p-5 rounded-xl border border-slate-200 dark:border-slate-600 shadow-sm",children:[e.jsxs("h5",{className:"text-sm font-bold text-slate-800 dark:text-slate-200 mb-2",children:["Crianças ","<","2 anos"]}),e.jsx("p",{className:"text-purple-700 dark:text-purple-300 text-sm leading-relaxed",children:"Coxim sob os ombros (cabeça proporcionalmente maior)"})]})]}),e.jsxs("div",{className:"bg-slate-100 dark:bg-slate-700/50 p-5 rounded-xl border border-slate-200 dark:border-slate-600 shadow-sm",children:[e.jsx("h5",{className:"text-lg font-bold text-slate-800 dark:text-slate-200 mb-3",children:"Situações Especiais:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-slate-400 rounded-full"}),e.jsxs("span",{className:"text-sm text-slate-600 dark:text-slate-400",children:[e.jsx("strong",{children:"Obesidade:"})," Posição rampa (head-up 25-30°)"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-slate-400 rounded-full"}),e.jsxs("span",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:[e.jsx("strong",{children:"Trauma cervical:"})," Imobilização manual em linha"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-yellow-600 rounded-full"}),e.jsxs("span",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:[e.jsx("strong",{children:"Gestante:"})," Decúbito lateral esquerdo 15°"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-yellow-600 rounded-full"}),e.jsxs("span",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:[e.jsxs("strong",{children:["Criança ","<","8 anos:"]})," Sem coxim (occipício proeminente)"]})]})]})]})]})]})}),e.jsx(n,{className:"border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>de(!le),className:"cursor-pointer hover:bg-blue-100/30 dark:hover:bg-blue-900/30 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P6"})}),e.jsx("h3",{className:"text-base font-bold text-blue-900 dark:text-blue-100",children:"6º P - Passagem do Tubo"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${le?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),le?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]}),e.jsxs("div",{className:"text-xs text-blue-600 dark:text-blue-400 leading-relaxed flex items-center gap-1",children:["Laringoscopia • ",e.jsx(t,{className:"h-3 w-3 flex-shrink-0"})," Máx 30s • Confirmar posição"]})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P6"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2 leading-tight",children:"6º P - Passagem do Tubo Orotraqueal"}),e.jsxs("div",{className:"text-xs lg:text-sm text-blue-600 dark:text-blue-400 leading-relaxed flex items-center gap-1",children:["Laringoscopia • ",e.jsx(t,{className:"h-3 w-3 flex-shrink-0"})," Máx 30s • Confirmar posição"]})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${le?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),le?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]})})]}),le&&e.jsxs("div",{className:"space-y-4 md:space-y-6 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:[xe?e.jsxs("div",{className:"bg-slate-50 dark:bg-slate-800/50 p-6 rounded-xl border border-slate-200 dark:border-slate-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-slate-800 dark:text-slate-200 mb-4 flex items-center gap-3",children:[e.jsx(g,{className:"h-5 w-5 text-slate-600 dark:text-slate-400"}),"Equipamentos Calculados - ","adult"===me?"Adulto":"Pediátrico"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4",children:[e.jsxs("div",{className:"bg-white dark:bg-slate-700/50 p-4 rounded-lg border border-slate-200 dark:border-slate-600 shadow-sm",children:[e.jsx("p",{className:"text-sm font-bold text-red-900 dark:text-red-200 mb-1",children:"Tubo Orotraqueal"}),e.jsxs("p",{className:"text-red-700 dark:text-red-300 font-mono text-lg",children:[xe.tubeSize," mm"]}),e.jsxs("p",{className:"text-xs text-red-600 dark:text-red-400",children:["(",xe.tubeType,")"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Profundidade"}),e.jsxs("p",{className:"text-red-700 dark:text-red-300 font-mono",children:[xe.insertionDepth," cm"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Laringoscópio"}),e.jsxs("p",{className:"text-red-700 dark:text-red-300 font-mono",children:[xe.laryngoscope.type," ",xe.laryngoscope.size]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Backup - ML"}),e.jsxs("p",{className:"text-red-700 dark:text-red-300 font-mono",children:["Tamanho ",xe.laryngealMask?.size]})]})]})]}):e.jsxs("div",{className:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/30 p-6 rounded-xl border border-amber-200 dark:border-amber-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-amber-800 dark:text-amber-200 mb-3 flex items-center gap-3",children:[e.jsx(t,{className:"h-5 w-5 text-amber-600"}),"Dados Necessários"]}),e.jsx("p",{className:"text-amber-700 dark:text-amber-300",children:"Insira o peso e idade do paciente para calcular os equipamentos específicos."})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 p-3 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-foreground mb-2",children:"Sequência de Intubação:"}),e.jsxs("div",{className:"text-sm text-muted-foreground space-y-1",children:[e.jsx("p",{children:"1. Verificar relaxamento muscular completo"}),e.jsx("p",{children:"2. Laringoscopia direta - visualizar cordas vocais"}),e.jsx("p",{children:"3. Inserir tubo através das cordas vocais"}),e.jsx("p",{children:"4. Insuflar cuff (se presente) - 20-30 cmH2O"}),e.jsx("p",{children:"5. Confirmar posição: capnografia + ausculta"}),e.jsx("p",{children:"6. Fixar tubo na profundidade calculada"})]})]}),e.jsxs("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:"Plano B - Via Aérea Difícil:"}),e.jsxs("div",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[e.jsxs("p",{children:["• Máscara laríngea tamanho ",xe?.laryngealMask?.size||"calculado"]}),e.jsx("p",{children:"• Ventilação com máscara facial"}),e.jsx("p",{children:"• Cricotireoidostomia (se indicada)"}),e.jsx("p",{children:"• Despertar o paciente (se possível)"})]})]})]})]})}),e.jsx(n,{className:"border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsxs(m,{className:"p-4 md:p-6",children:[e.jsxs("div",{onClick:()=>ne(!oe),className:"cursor-pointer hover:bg-blue-100/30 dark:hover:bg-blue-900/30 transition-all duration-200 rounded-lg p-2 -m-2",children:[e.jsxs("div",{className:"block sm:hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"P7"})}),e.jsx("h3",{className:"text-base font-bold text-blue-900 dark:text-blue-100",children:"7º P - Pós-intubação"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${oe?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),oe?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]}),e.jsx("div",{className:"text-xs text-blue-600 dark:text-blue-400 leading-relaxed",children:"Capnografia • Ausculta • Fixação • Sedação"})]}),e.jsx("div",{className:"hidden sm:block",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg flex-shrink-0",children:e.jsx("span",{className:"text-white font-bold text-base lg:text-lg",children:"P7"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg lg:text-xl xl:text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2 leading-tight",children:"7º P - Pós-intubação"}),e.jsx("div",{className:"text-xs lg:text-sm text-blue-600 dark:text-blue-400 leading-relaxed",children:"Capnografia • Ausculta • Fixação • Sedação"})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${oe?"bg-green-500":"bg-gray-300"} transition-colors duration-200`}),oe?e.jsx(c,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}):e.jsx(x,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})]})]})})]}),oe&&e.jsxs("div",{className:"space-y-4 md:space-y-6 px-4 md:px-6 pb-4 md:pb-6 pt-4 md:pt-6",children:[ue?e.jsxs("div",{className:"bg-gradient-to-br from-teal-100 to-teal-200 dark:from-teal-900/40 dark:to-teal-800/40 p-6 rounded-xl border border-teal-300 dark:border-teal-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-teal-800 dark:text-teal-200 mb-4 flex items-center gap-3",children:[e.jsx(P,{className:"h-5 w-5 text-teal-600"}),"Parâmetros Ventilatórios - ","adult"===me?"Adulto":"Pediátrico"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4",children:[e.jsxs("div",{className:"bg-white dark:bg-teal-900/20 p-4 rounded-lg border border-teal-200 dark:border-teal-600 shadow-sm",children:[e.jsx("p",{className:"text-sm font-bold text-teal-900 dark:text-teal-200 mb-1",children:"Modo"}),e.jsx("p",{className:"text-teal-700 dark:text-teal-300 font-mono text-lg",children:ue.mode})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Frequência"}),e.jsxs("p",{className:"text-green-700 dark:text-green-300 font-mono",children:[ue.respiratoryRate," irpm"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Volume Corrente"}),e.jsxs("p",{className:"text-green-700 dark:text-green-300 font-mono",children:[ue.tidalVolume," mL/kg"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"PEEP"}),e.jsxs("p",{className:"text-green-700 dark:text-green-300 font-mono",children:[ue.peep," cmH2O"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"FiO2"}),e.jsxs("p",{className:"text-green-700 dark:text-green-300 font-mono",children:[ue.fio2,"%"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-3 rounded border",children:[e.jsx("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Pressão Pico"}),e.jsxs("p",{className:"text-green-700 dark:text-green-300 font-mono",children:[ue.peakPressure," cmH2O"]})]})]})]}):e.jsxs("div",{className:"bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/30 p-6 rounded-xl border border-amber-200 dark:border-amber-700 shadow-sm -mx-8 sm:mx-0",children:[e.jsxs("h4",{className:"text-lg font-bold text-amber-800 dark:text-amber-200 mb-3 flex items-center gap-3",children:[e.jsx(t,{className:"h-5 w-5 text-amber-600"}),"Dados Necessários"]}),e.jsx("p",{className:"text-amber-700 dark:text-amber-300",children:"Insira o peso e idade do paciente para calcular os parâmetros ventilatórios específicos."})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-blue-800 dark:text-blue-200 mb-2",children:"Confirmação da Intubação:"}),e.jsxs("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[e.jsxs("p",{children:["1. ",e.jsx("strong",{children:"Capnografia:"})," Onda de CO2 presente (padrão ouro)"]}),e.jsxs("p",{children:["2. ",e.jsx("strong",{children:"Ausculta:"})," MV bilateral simétrico"]}),e.jsxs("p",{children:["3. ",e.jsx("strong",{children:"Inspeção:"})," Expansão torácica simétrica"]}),e.jsxs("p",{children:["4. ",e.jsx("strong",{children:"Radiografia:"})," Ponta do tubo 2-4 cm acima da carina"]})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 p-3 rounded-lg",children:[e.jsx("h5",{className:"font-medium text-foreground mb-2",children:"Cuidados Pós-intubação:"}),e.jsxs("div",{className:"text-sm text-muted-foreground space-y-1",children:[e.jsx("p",{children:"□ Fixar tubo adequadamente"}),e.jsx("p",{children:"□ Iniciar sedação contínua"}),e.jsx("p",{children:"□ Proteção gástrica (se indicada)"}),e.jsx("p",{children:"□ Controle da pressão arterial"}),e.jsx("p",{children:"□ Monitorização contínua"}),e.jsx("p",{children:"□ Gasometria arterial em 30 min"})]})]})]})]})}),e.jsx(n,{className:"border border-gray-200 bg-white dark:bg-slate-800",children:e.jsxs(m,{className:"p-4",children:[e.jsxs(s,{variant:"ghost",onClick:()=>k(!v),className:"w-full flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-slate-700",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-5 w-5 text-primary"}),e.jsx("span",{className:"font-semibold text-foreground",children:"Referências Científicas"})]}),v?e.jsx(c,{className:"h-4 w-4 text-muted-foreground"}):e.jsx(x,{className:"h-4 w-4 text-muted-foreground"})]}),v&&e.jsxs("div",{className:"mt-4 space-y-4 text-sm text-muted-foreground",children:[e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-semibold text-foreground mb-3",children:"Diretrizes e Protocolos"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"1."})," Sociedade Brasileira de Anestesiologia (SBA). Diretrizes para Sequência Rápida de Intubação em Pediatria. 2023."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"2."})," American Society of Anesthesiologists (ASA). Practice Guidelines for Management of the Difficult Airway. Anesthesiology. 2022;136(1):31-81."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"3."})," European Society of Anaesthesiology and Intensive Care (ESAIC). Guidelines for airway management in children. Eur J Anaesthesiol. 2022;39(4):267-287."]})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-semibold text-foreground mb-3",children:"Farmacologia e Dosagens"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"4."})," Cote CJ, Lerman J, Anderson BJ. A Practice of Anesthesia for Infants and Children. 6th ed. Elsevier; 2019."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"5."})," Davis PJ, Cladis FP, Motoyama EK. Smith's Anesthesia for Infants and Children. 9th ed. Mosby; 2017."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"6."})," Micromedex Healthcare Series. Truven Health Analytics. Dosing recommendations for pediatric anesthesia. 2023."]})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-semibold text-foreground mb-3",children:"Evidências Específicas"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"7."})," Ketamina: Green SM, Roback MG, Kennedy RM, Krauss B. Clinical practice guideline for emergency department ketamine dissociative sedation. Ann Emerg Med. 2011;57(5):449-461."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"8."})," Propofol: Bassett KE, Anderson JL, Pribble CG, Guenther E. Propofol for procedural sedation in children in the emergency department. Ann Emerg Med. 2003;42(6):773-782."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"9."})," Rocurônio vs Succinilcolina: Tran DT, Newton EK, Mount VA, et al. Rocuronium versus succinylcholine for rapid sequence intubation. Cochrane Database Syst Rev. 2015;(10):CD002788."]})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-semibold text-foreground mb-3",children:"Segurança e Contraindicações"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"10."})," Malignant Hyperthermia Association of the United States (MHAUS). Emergency therapy for malignant hyperthermia. 2023."]}),e.jsxs("p",{children:[e.jsx("strong",{children:"11."})," Sociedade Brasileira de Pediatria (SBP). Manual de Emergências Pediátricas. 4ª ed. 2022."]})]})]}),e.jsx("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border-l-4 border-blue-400",children:e.jsxs("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:[e.jsx("strong",{children:"Nota:"})," Esta calculadora é uma ferramenta de apoio clínico baseada em evidências científicas atuais. Sempre considere o contexto clínico individual, protocolos institucionais e sua experiência profissional na tomada de decisões."]})})]})]})})]})]})]})},$=()=>{const a=I();return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 pb-16 sm:pb-0",children:[e.jsx(b,{title:"Calculadora SRI - Sequência Rápida de IOT",description:"Calculadora para Sequência Rápida de Intubação Orotraqueal (SRI) com doses precisas para adultos e crianças",keywords:"SRI, intubação, IOT, sequência rápida, medicações, doses, pediatria"}),e.jsx(h,{}),e.jsxs("main",{className:"container mx-auto px-2 sm:px-4 py-4 sm:py-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4 sm:mb-6",children:[e.jsxs(s,{variant:"ghost",onClick:()=>a("/calculadoras"),className:"flex items-center gap-2 text-primary hover:text-primary/80",children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Voltar para Calculadoras"}),e.jsx("span",{className:"sm:hidden",children:"Voltar"})]}),e.jsxs("div",{className:"text-center flex-1 mx-4",children:[e.jsx("h1",{className:"text-lg sm:text-2xl font-bold text-foreground",children:"Calculadora SRI"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground hidden sm:block",children:"Sequência Rápida de Intubação Orotraqueal"})]}),e.jsx("div",{className:"w-[100px] sm:w-[180px]"})]}),e.jsx(q,{})]}),e.jsx(j,{})]})};export{$ as default};
