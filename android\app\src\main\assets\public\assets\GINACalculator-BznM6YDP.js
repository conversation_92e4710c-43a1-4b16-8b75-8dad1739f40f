import{j as a}from"./radix-core-6kBL75b5.js";import{r as e}from"./critical-DVX9Inzy.js";import{L as s}from"./router-BAzpOxbo.js";import{aM as r,aa as t,B as o,R as i,az as d,an as c}from"./index-CrSshpOb.js";import n from"./Footer-ClHMSbsi.js";import{C as m,a as l}from"./calculatorSEOData-DU9NV5AY.js";import{C as x}from"./chevron-left-CuYzwBha.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";const p=()=>{const p=l.gina,[j,u]=e.useState([]),h=j.length,g=0===(v=h)?{text:"Asma controlada",description:"Nenhum critério presente nas últimas 4 semanas",color:"text-green-600 dark:text-green-400"}:v<=2?{text:"Asma parcialmente controlada",description:"1 a 2 critérios presentes nas últimas 4 semanas",color:"text-yellow-600 dark:text-yellow-400"}:{text:"Asma não controlada",description:"3 a 4 critérios presentes nas últimas 4 semanas",color:"text-red-600 dark:text-red-400"};var v;return a.jsxs("div",{className:r.gradientBackground("min-h-screen flex flex-col"),children:[a.jsx(m,{...p}),a.jsx(t,{}),a.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:a.jsxs("div",{className:"max-w-3xl mx-auto space-y-8",children:[a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx(s,{to:"/calculadoras",children:a.jsx(o,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:a.jsx(x,{className:"h-5 w-5"})})}),a.jsx("h1",{className:r.gradientHeading("text-3xl"),children:"Controle da Asma (GINA)"})]}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avaliação do controle da asma baseada nos critérios GINA 2022 considerando os últimos 28 dias"}),a.jsxs(i,{className:r.card("p-6 space-y-6"),children:[[{id:"diurnal",label:"Apresentou sintomas diurnos mais de 2x por semana",description:"Sintomas como tosse, chiado, falta de ar durante o dia"},{id:"nocturnal",label:"Apresentou despertar noturno devido à asma",description:"Acordou durante a noite por causa dos sintomas"},{id:"reliever",label:"Usou beta-agonista de curta duração pelo menos 2x por semana",description:"Necessidade de medicação de alívio para os sintomas"},{id:"limitation",label:"Apresentou limitação das atividades diárias devido à asma",description:"Restrição em atividades normais por causa da asma"}].map((e=>a.jsxs("div",{className:"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50",children:[a.jsx(d,{id:e.id,checked:j.includes(e.id),onCheckedChange:a=>((a,e)=>{u((s=>e?[...s,a]:s.filter((e=>e!==a))))})(e.id,a)}),a.jsxs("div",{className:"space-y-1",children:[a.jsx(c,{htmlFor:e.id,className:"text-base font-medium cursor-pointer text-gray-800 dark:text-gray-200",children:e.label}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description})]})]},e.id))),a.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:a.jsxs("div",{className:"text-center space-y-4",children:[a.jsxs("div",{className:"text-4xl font-bold text-primary dark:text-blue-400",children:[h," critério",1!==h?"s":""]}),a.jsx("div",{className:`text-xl font-semibold ${g.color}`,children:g.text}),a.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto",children:[g.description,a.jsx("p",{className:"mt-2 text-xs",children:"Nota: Esta avaliação deve ser considerada em conjunto com outros parâmetros clínicos para decisões terapêuticas."})]})]})})]})]})}),a.jsx(n,{})]})};export{p as default};
