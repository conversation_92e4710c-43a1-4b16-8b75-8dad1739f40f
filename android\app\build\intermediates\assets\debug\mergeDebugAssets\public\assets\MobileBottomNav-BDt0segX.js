import{j as e}from"./radix-core-6kBL75b5.js";import{u as a,i as r,j as t,C as s,P as n,k as i,l,n as o,o as c,p as d,q as u,r as m,t as x,v as g,w as h,x as p,S as f,H as b,M as j,y as v,A as y,D as w,e as k,f as N,g as E,z as C,E as S,B as A,F as T,G as L,s as z}from"./index-CNG-Xj2g.js";import{r as F}from"./critical-DVX9Inzy.js";import{F as I}from"./FeedbackTrigger-ik6vfZ65.js";import{a as R,u as B}from"./router-BAzpOxbo.js";import{u as D}from"./use-mobile-DZ3cxmhN.js";import{C as U}from"./chevron-left-Ep4pqQcd.js";import{B as O}from"./bot-BYA-FVo1.js";import{H as P}from"./house-CQtCJU99.js";import{U as _}from"./user-DPHDn0jo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./rocket-BEoGgNr2.js";import"./target-Dul0NbVV.js";import"./zap-C4mKju26.js";import"./book-open-EV5sJdXr.js";import"./star-BUSksJJE.js";import"./circle-help-BbvIlE64.js";const H=F.memo((()=>{const H=R(),M=B(),{user:V,profile:q,signOut:W}=a(),{showNotification:X}=r(),[Y,G]=F.useState(!1),[$,J]=F.useState(!1),[K,Q]=F.useState(!1),[Z,ee]=F.useState(!1),[ae,re]=F.useState(!0),[te,se]=F.useState(!1),[ne,ie]=F.useState(null),le=F.useRef(0),oe=F.useRef(!0),ce=F.useRef(null),de=D();(function({minSwipeDistance:e=100,enableHapticFeedback:a=!0,onAuthRequired:r,checkAuth:t}={}){const s=R(),n=F.useRef(null),i=F.useRef(null),[l,o]=F.useState(!1),c=["/medicamentos/painel","/puericultura","/dr-will","/","/calculadoras","/flowcharts"],d=["/","/medicamentos","/medicamentos/painel","/puericultura","/dr-will","/calculadoras","/flowcharts","/condutas","/bulas","/interacoes","/cid","/plataformadeestudos","/feedback","/perfil","/busca","/configuracoes","/newsletters"],u=e=>d.includes(e);F.useEffect((()=>{const a=[".growth-curve-dialog",".image-viewer",".DialogContent",".dialog-content",'[data-state="open"]','[style*="transform"]','[style*="scale"]','[style*="translate"]',".cursor-grab",".cursor-grabbing",".embla",".carousel","img","svg","canvas",".growth-curves-container",".flowchart-container",".chart-container"],d=e=>{if(!e)return!1;let r=e;for(;r;){for(const e of a)try{if(r.matches(e))return!0}catch(t){}if("dialog"===r.getAttribute("role")||"true"===r.getAttribute("data-zoom")||"true"===r.getAttribute("data-draggable")||r.classList.contains("no-swipe"))return!0;r=r.parentElement}return!1},m=e=>{const a=window.location.pathname;return u(a)?d(e.target)?(n.current=null,void(i.current=null)):(n.current=e.touches[0].clientX,void(i.current=e.touches[0].clientY)):(n.current=null,void(i.current=null))},x=async a=>{if(null===n.current||null===i.current)return;const m=window.location.pathname;if(!u(m))return n.current=null,void(i.current=null);if(d(a.target))return n.current=null,void(i.current=null);const x=a.changedTouches[0].clientX,g=a.changedTouches[0].clientY,h=x-n.current,p=g-i.current;if(Math.abs(h)>2*Math.abs(p)&&Math.abs(h)>e){if(l)return;o(!0);const e=(e=>{const a=c.indexOf(e);if(-1!==a)return a;for(let r=0;r<c.length;r++){const a=c[r];if("/"!==a&&e.startsWith(a))return r}return c.indexOf("/")})(window.location.pathname);h>0?await(async e=>{if(e>0){const a=c[e-1];return"/dr-will"===a&&t&&!(await t())?!!r&&(r(),!0):(s(a),!0)}return!1})(e):await(async e=>{if(e<c.length-1){const a=c[e+1];return"/dr-will"===a&&t&&!(await t())?!!r&&(r(),!0):(s(a),!0)}return!1})(e),setTimeout((()=>{o(!1)}),300)}n.current=null,i.current=null};return document.addEventListener("touchstart",m,{passive:!0}),document.addEventListener("touchend",x,{passive:!0}),()=>{document.removeEventListener("touchstart",m),document.removeEventListener("touchend",x)}}),[s,e,a,l,r,t])})({enableHapticFeedback:!0,minSwipeDistance:80,onAuthRequired:()=>G(!0),checkAuth:async()=>{const{data:{session:e}}=await z.auth.getSession();return!!e}}),F.useEffect((()=>{const e=e=>{const a=e.target;["INPUT","TEXTAREA","SELECT"].includes(a.tagName)&&(oe.current=!0,re(!0))};let a=!1;const r=()=>{a||(requestAnimationFrame((()=>{(()=>{if(document.activeElement&&["INPUT","TEXTAREA","SELECT"].includes(document.activeElement.tagName)||te)return void(oe.current||(oe.current=!0,re(!0)));const e=document.getElementById("root"),a=(e&&document.body.classList.contains("android-safe-area")?e:window)===window?window.scrollY:e?.scrollTop||0;Math.abs(a-le.current)<10||(a>le.current&&a>100?oe.current&&(oe.current=!1,re(!1)):(a<le.current||a<=50)&&(oe.current||(oe.current=!0,re(!0))),le.current=a)})(),a=!1})),a=!0)},t=document.getElementById("root"),s=t&&document.body.classList.contains("android-safe-area")?t:window;return s.addEventListener("scroll",r,{passive:!0}),document.addEventListener("focusin",e),()=>{s.removeEventListener("scroll",r),document.removeEventListener("focusin",e)}}),[te]),F.useEffect((()=>{if("undefined"==typeof window||!window.visualViewport)return;const e=()=>{const e=window.innerHeight-window.visualViewport.height>150;se(e),e&&(oe.current=!0,re(!0))};let a;const r=()=>{clearTimeout(a),a=setTimeout(e,100)};return window.visualViewport.addEventListener("resize",r),()=>{window.visualViewport&&window.visualViewport.removeEventListener("resize",r),clearTimeout(a)}}),[]),F.useEffect((()=>{if(de&&!localStorage.getItem("hasSeenSwipeIndicator")){ee(!0);const e=setTimeout((()=>{ee(!1),localStorage.setItem("hasSeenSwipeIndicator","true")}),3e3);return()=>clearTimeout(e)}}),[de]);const ue=[{path:"/medicamentos/painel",icon:n,label:"Medicamentos"},{path:"/puericultura",icon:i,label:"Puericultura"},{path:"/dr-will",icon:O,label:"Dr. Will"},{path:"/calculadoras",icon:l,label:"Calculadoras"},{path:"/flowcharts",icon:o,label:"Fluxogramas"}],me=F.useCallback((e=>{if("/dr-will"===e&&!V)return ie(e),void G(!0);H(e)}),[V,H,G]),xe=e=>e?`https://www.gravatar.com/avatar/${L(e.toLowerCase().trim())}?d=mp`:"https://www.gravatar.com/avatar/1?d=mp",ge=t("nav-mobile fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-slate-900/95 sm:hidden mobile-nav-container","transition-transform duration-300 ease-in-out",!ae&&"translate-y-full",te&&"keyboard-visible");return e.jsxs(e.Fragment,{children:[Z&&de&&e.jsxs("div",{className:"fixed top-1/2 left-0 right-0 transform -translate-y-1/2 flex justify-between px-4 z-50 pointer-events-none",children:[e.jsxs("div",{className:"bg-white/80 dark:bg-slate-800/80 p-2 rounded-full shadow-lg flex items-center",children:[e.jsx(U,{className:"h-6 w-6 text-primary animate-pulse"}),e.jsx("span",{className:"text-xs ml-1 font-medium",children:"Deslize"})]}),e.jsxs("div",{className:"bg-white/80 dark:bg-slate-800/80 p-2 rounded-full shadow-lg flex items-center",children:[e.jsx("span",{className:"text-xs mr-1 font-medium",children:"Deslize"}),e.jsx(s,{className:"h-6 w-6 text-primary animate-pulse"})]})]}),e.jsx("div",{ref:ce,className:ge,style:{zIndex:te?5e4:50},role:"navigation","aria-label":"Navegação principal",children:e.jsxs("div",{className:"grid grid-cols-7 gap-1 relative py-1",children:[ue.slice(0,3).map((({path:a,icon:r,label:s})=>e.jsx("button",{onClick:()=>{me(a)},className:t("flex items-center justify-center p-2 rounded-lg transition-all duration-200",M.pathname===a?"text-primary bg-primary/10 dark:bg-primary/5":"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"),"aria-label":s,"aria-current":M.pathname===a?"page":void 0,children:e.jsx(r,{className:"h-6 w-6"})},a))),e.jsx("button",{onClick:()=>{H("/")},className:t("flex items-center justify-center relative","transition-all duration-300 py-1","/"===M.pathname?"text-primary":"text-gray-600 dark:text-gray-300"),"aria-label":"Início","aria-current":"/"===M.pathname?"page":void 0,children:e.jsx("div",{className:t("p-3 rounded-full border-2 bg-white dark:bg-slate-800 shadow-lg","hover:shadow-xl hover:scale-105 transition-all duration-300","active:scale-95 active:bg-gray-50 dark:active:bg-slate-700","/"===M.pathname?"border-primary text-primary":"border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300"),children:e.jsx(P,{className:"h-6 w-6"})})}),ue.slice(3).map((({path:a,icon:r,label:s})=>e.jsx("button",{onClick:()=>{me(a)},className:t("flex items-center justify-center p-2 rounded-lg transition-all duration-200",M.pathname===a?"text-primary bg-primary/10 dark:bg-primary/5":"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"),"aria-label":s,"aria-current":M.pathname===a?"page":void 0,children:e.jsx(r,{className:"h-6 w-6"})},a))),V?e.jsxs(c,{children:[e.jsx(d,{className:"flex items-center justify-center p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800",children:e.jsxs(u,{className:"h-6 w-6 ring-2 ring-primary/20",children:[e.jsx(m,{src:q?.avatar_url||xe(V.email||""),alt:q?.full_name||"User"}),e.jsx(x,{children:e.jsx(_,{className:"h-3 w-3"})})]})}),e.jsxs(g,{align:"end",className:t("w-64 p-2 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl","border border-gray-200/50 dark:border-slate-700/50 shadow-xl","rounded-xl animate-in fade-in-0 zoom-in-95 duration-200","z-[95]"),children:[e.jsx("div",{className:"px-3 py-2 mb-2",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(u,{className:"h-8 w-8 ring-2 ring-primary/20",children:[e.jsx(m,{src:q?.avatar_url||xe(V.email||""),alt:q?.full_name||"User"}),e.jsx(x,{className:"bg-primary/10 text-primary font-semibold",children:(q?.full_name||"U").charAt(0)})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 truncate",children:q?.full_name||"Usuário"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:V.email})]})]})}),e.jsx(h,{className:"bg-gray-200/50 dark:bg-slate-700/50"}),e.jsxs("div",{className:"space-y-1 py-1",children:[e.jsxs(p,{onClick:()=>{H("/settings")},className:t("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white","hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200","focus:bg-gray-100/80 dark:focus:bg-slate-800/80"),children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30",children:e.jsx(f,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Configurações"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Gerencie sua conta"})]})]}),e.jsxs(p,{onClick:()=>{Q(!0)},className:t("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white","hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200","focus:bg-gray-100/80 dark:focus:bg-slate-800/80"),children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30",children:e.jsx(b,{className:"h-4 w-4 text-green-600 dark:text-green-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Suporte"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Fale conosco via WhatsApp"})]})]}),e.jsxs(p,{onClick:()=>{J(!0)},className:t("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white","hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200","focus:bg-gray-100/80 dark:focus:bg-slate-800/80"),children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30",children:e.jsx(j,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Feedback Técnico"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Bugs e problemas"})]})]}),e.jsx(I,{variant:"menu"})]}),e.jsx(h,{className:"bg-gray-200/50 dark:bg-slate-700/50 my-2"}),e.jsxs(p,{onClick:async()=>{await W(),X({title:"Logout realizado com sucesso",description:"Você foi desconectado da sua conta.",type:"success",buttonText:"Continuar",onButtonClick:()=>H("/")})},className:t("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300","hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200","focus:bg-red-50 dark:focus:bg-red-900/20"),children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 dark:bg-red-900/30",children:e.jsx(v,{className:"h-4 w-4 text-red-600 dark:text-red-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Sair"}),e.jsx("p",{className:"text-xs text-red-500/70 dark:text-red-400/70",children:"Desconectar da conta"})]})]})]})]}):e.jsx("button",{onClick:()=>{ie(null),G(!0)},className:"flex items-center justify-center p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800","aria-label":"Entrar",children:e.jsx(u,{className:"h-6 w-6",children:e.jsx(x,{children:e.jsx(_,{className:"h-3 w-3"})})})})]})}),e.jsx(y,{open:Y,onOpenChange:G,hidden:!0,onSuccess:()=>{G(!1),ne?(H(ne),ie(null)):H("/")}}),e.jsx(w,{open:K,onOpenChange:Q,children:e.jsxs(k,{className:"sm:max-w-md",children:[e.jsxs(N,{className:"text-center",children:[e.jsx("div",{className:"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4",children:e.jsx(j,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),e.jsx(E,{className:"text-xl font-semibold",children:"Precisa de Ajuda?"}),e.jsx(C,{className:"text-gray-600 dark:text-gray-400",children:"Entre em contato conosco via WhatsApp para suporte rápido e personalizado."})]}),e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(S,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Número de Suporte"})]}),e.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"(64) 99319-8433"})]}),e.jsx("div",{className:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(j,{className:"h-5 w-5 text-green-600 dark:text-green-400 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-1",children:"Horário de Atendimento"}),e.jsxs("p",{className:"text-sm text-green-700 dark:text-green-400",children:["Segunda a Sexta: 8h às 18h",e.jsx("br",{}),"Sábado: 8h às 12h"]})]})]})})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(A,{variant:"outline",onClick:()=>Q(!1),className:"flex-1",children:"Cancelar"}),e.jsxs(A,{onClick:()=>{const e=`https://wa.me/5564993198433?text=${encodeURIComponent("Olá! Preciso de suporte com o PedBook.")}`;window.open(e,"_blank"),Q(!1)},className:"flex-1 bg-green-600 hover:bg-green-700 text-white",children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Abrir WhatsApp"]})]})]})}),e.jsx(w,{open:$,onOpenChange:e=>{J(e),e||(document.body.style.pointerEvents="auto")},children:e.jsxs(k,{className:"sm:max-w-[600px] p-0 dialog-content mobile-feedback-dialog",children:[e.jsx(E,{className:"sr-only",children:"Feedback"}),e.jsx(T,{})]})})]})}));export{H as default};
