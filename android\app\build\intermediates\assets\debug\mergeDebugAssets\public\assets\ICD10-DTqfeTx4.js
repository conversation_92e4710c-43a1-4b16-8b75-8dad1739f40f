import{j as e,S as a}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{a as r,u as i}from"./query-vendor-B-7l6Nb3.js";import{d as o,s as t,D as n,e as c,f as d,g as l,an as m,a5 as p,T as x,aw as g,B as u,j as h,aj as j,C as f}from"./index-D9amGMlQ.js";import{U as v}from"./upload-CdbFaiy_.js";import{P as y}from"./plus-xou7mjLU.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";function N({category:a,parentId:i,isOpen:h,onClose:j}){const[f,v]=s.useState({code_range:"",name:"",description:"",level:1}),[y,N]=s.useState(""),[b,C]=s.useState([]),{toast:_}=o(),w=r();s.useEffect((()=>{h&&a?(v({code_range:a.code_range||"",name:a.name||"",description:a.description||"",level:a.level||1}),k(a.id)):(v({code_range:"",name:"",description:"",level:i?2:1}),N(""),C([]))}),[h,a,i]);const k=async e=>{const{data:a,error:s}=await t.from("pedbook_icd10_codes").select("*").eq("category_id",e);if(!s&&a){C(a);const e=a.map((({code_range:e,name:a})=>({codigocid:e,nome:a})));N(JSON.stringify(e,null,2))}};return e.jsx(n,{open:h,onOpenChange:j,children:e.jsxs(c,{className:"max-w-2xl",children:[e.jsx(d,{children:e.jsx(l,{children:a?"Editar Categoria":"Nova Categoria"})}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(a){const{error:e}=await t.from("pedbook_icd10_categories").update({...f,parent_id:i}).eq("id",a.id);if(e)throw e;if(b.length>0){await t.from("pedbook_icd10_codes").delete().eq("category_id",a.id);const{error:e}=await t.from("pedbook_icd10_codes").insert(b.map((e=>({...e,category_id:a.id}))));if(e)throw e}_({title:"Categoria atualizada com sucesso!",description:`A categoria ${f.name} foi atualizada.`})}else{const{data:e,error:a}=await t.from("pedbook_icd10_categories").insert([{...f,parent_id:i}]).select().single();if(a)throw a;if(b.length>0&&e){const{error:a}=await t.from("pedbook_icd10_codes").insert(b.map((a=>({...a,category_id:e.id}))));if(a)throw a}_({title:"Categoria criada com sucesso!",description:`A categoria ${f.name} foi adicionada.`})}w.invalidateQueries({queryKey:["icd10-categories"]}),j()}catch(s){_({variant:"destructive",title:"Erro ao salvar categoria",description:s.message})}},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(m,{htmlFor:"code_range",children:"Código CID-10"}),e.jsx(p,{id:"code_range",value:f.code_range,onChange:e=>v({...f,code_range:e.target.value}),placeholder:"Ex: A00-A09",required:!0})]}),e.jsxs("div",{children:[e.jsx(m,{htmlFor:"name",children:"Nome"}),e.jsx(p,{id:"name",value:f.name,onChange:e=>v({...f,name:e.target.value}),required:!0})]}),e.jsxs("div",{children:[e.jsx(m,{htmlFor:"description",children:"Descrição (opcional)"}),e.jsx(x,{id:"description",value:f.description,onChange:e=>v({...f,description:e.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{children:"Códigos CID-10 (JSON)"}),e.jsx(g,{className:"h-[200px] w-full rounded-md border",children:e.jsx(x,{value:y,onChange:e=>N(e.target.value),placeholder:'[{"codigocid": "B90-B94", "nome": "Sequelas de doenças infecciosas e parasitárias"}]',className:"min-h-[200px] border-none"})}),e.jsx(u,{type:"button",variant:"secondary",onClick:()=>{try{const e=JSON.parse(y).map((e=>({code_range:e.codigocid,name:e.nome})));C(e),_({title:"Códigos importados com sucesso!",description:`${e.length} códigos foram importados.`})}catch(e){_({variant:"destructive",title:"Erro ao importar códigos",description:"Verifique se o formato do JSON está correto."})}},className:"w-full",children:"Importar Códigos"})]}),b.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs(m,{children:["Códigos Importados (",b.length,")"]}),e.jsx(g,{className:"h-[200px] w-full rounded-md border p-4",children:e.jsx("div",{className:"space-y-2",children:b.map(((a,s)=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded-lg bg-secondary/20",children:[e.jsx("span",{className:"font-medium",children:a.code_range}),e.jsx("span",{className:"text-sm text-muted-foreground",children:a.name})]},s)))})})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(u,{type:"button",variant:"outline",onClick:j,children:"Cancelar"}),e.jsx(u,{type:"submit",children:"Salvar"})]})]})]})})}function b({isOpen:a,onClose:i,parentId:m}){const[p,h]=s.useState(""),{toast:j}=o(),f=r();return e.jsx(n,{open:a,onOpenChange:i,children:e.jsxs(c,{className:"max-w-2xl",children:[e.jsx(d,{children:e.jsx(l,{children:"Importar Códigos CID-10"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(g,{className:"h-[400px] w-full rounded-md border",children:e.jsx(x,{value:p,onChange:e=>h(e.target.value),placeholder:'[{"codigocid": "A00-A09", "nome": "Doenças infecciosas intestinais"}]',className:"min-h-[400px] border-none"})}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(u,{variant:"outline",onClick:i,children:"Cancelar"}),e.jsx(u,{onClick:async()=>{try{const e=JSON.parse(p),{error:a}=await t.from("pedbook_icd10_categories").insert(e.map((e=>({code_range:e.codigocid,name:e.nome,level:m?2:1,parent_id:m}))));if(a)throw a;j({title:"Códigos importados com sucesso!",description:`${e.length} códigos foram importados.`}),f.invalidateQueries({queryKey:["icd10-categories"]}),i()}catch(e){j({variant:"destructive",title:"Erro ao importar códigos",description:e.message})}},children:"Importar"})]})]})]})})}const C=s.forwardRef((({...a},s)=>e.jsx("nav",{ref:s,"aria-label":"breadcrumb",...a})));C.displayName="Breadcrumb",s.forwardRef((({className:a,...s},r)=>e.jsx("ol",{ref:r,className:h("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...s}))).displayName="BreadcrumbList";const _=s.forwardRef((({className:a,...s},r)=>e.jsx("li",{ref:r,className:h("inline-flex items-center gap-1.5",a),...s})));_.displayName="BreadcrumbItem";const w=s.forwardRef((({asChild:s,className:r,...i},o)=>{const t=s?a:"a";return e.jsx(t,{ref:o,className:h("transition-colors hover:text-foreground",r),...i})}));function k(){const[a,r]=s.useState(null),[o,n]=s.useState(!1),[c,d]=s.useState(!1),[l,m]=s.useState(null),[p,x]=s.useState([]),{data:g}=i({queryKey:["icd10-categories",l],queryFn:async()=>{const e=t.from("pedbook_icd10_categories").select("*").order("code_range");l?e.eq("parent_id",l):e.is("parent_id",null);const{data:a,error:s}=await e;if(s)throw s;return a}});return e.jsxs("div",{className:"container mx-auto py-8 space-y-8 animate-fade-in",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h1",{className:"text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600",children:"Categorias CID-10"}),e.jsxs(C,{children:[e.jsx(_,{children:e.jsx(w,{onClick:()=>{m(null),x([])},className:"hover:text-primary transition-colors",children:"Início"})}),p.map(((a,s)=>e.jsx(_,{children:e.jsx(w,{onClick:()=>(e=>{const a=p.slice(0,e);x(a),m(0===e?null:a[e-1].id)})(s+1),className:"hover:text-primary transition-colors",children:a.name})},a.id)))]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(u,{onClick:()=>d(!0),className:"bg-gradient-to-r from-green-600 to-green-500 hover:opacity-90 transition-all duration-300",children:[e.jsx(v,{className:"mr-2 h-4 w-4"}),"Importar Códigos"]}),e.jsxs(u,{onClick:()=>{r(null),n(!0)},className:"bg-gradient-to-r from-primary to-blue-600 hover:opacity-90 transition-all duration-300",children:[e.jsx(y,{className:"mr-2 h-4 w-4"}),"Nova Categoria"]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g?.map((a=>e.jsxs("div",{onClick:()=>(async e=>{m(e.id),x([...p,{id:e.id,name:e.name}])})(a),className:"group relative bg-gradient-to-br from-white to-primary/5 rounded-xl p-6 space-y-3 border border-primary/10 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer backdrop-blur-sm animate-fade-in",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-primary",children:a.code_range}),e.jsx("p",{className:"text-gray-800 font-medium",children:a.name})]}),a.description&&e.jsxs("div",{className:"flex items-start gap-2 text-sm text-muted-foreground",children:[e.jsx(j,{className:"h-4 w-4 mt-0.5 shrink-0"}),e.jsx("p",{className:"line-clamp-2",children:a.description})]})]}),e.jsx(f,{className:"h-5 w-5 text-primary/40 group-hover:text-primary transition-colors"})]}),e.jsx(u,{variant:"ghost",size:"sm",className:"absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity",onClick:e=>{e.stopPropagation(),r(a),n(!0)},children:"Editar"})]},a.id)))}),e.jsx(N,{category:a,parentId:l,isOpen:o,onClose:()=>{n(!1),r(null)}}),e.jsx(b,{isOpen:c,onClose:()=>d(!1),parentId:l})]})}w.displayName="BreadcrumbLink",s.forwardRef((({className:a,...s},r)=>e.jsx("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:h("font-normal text-foreground",a),...s}))).displayName="BreadcrumbPage";export{k as default};
