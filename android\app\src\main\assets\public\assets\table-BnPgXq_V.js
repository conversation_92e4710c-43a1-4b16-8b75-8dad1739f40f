import{j as a}from"./radix-core-6kBL75b5.js";import{r as e}from"./critical-DVX9Inzy.js";import{j as s}from"./index-D9amGMlQ.js";const r=e.forwardRef((({className:e,...r},t)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:t,className:s("w-full caption-bottom text-sm",e),...r})})));r.displayName="Table";const t=e.forwardRef((({className:e,...r},t)=>a.jsx("thead",{ref:t,className:s("[&_tr]:border-b",e),...r})));t.displayName="TableHeader";const l=e.forwardRef((({className:e,...r},t)=>a.jsx("tbody",{ref:t,className:s("[&_tr:last-child]:border-0",e),...r})));l.displayName="TableBody",e.forwardRef((({className:e,...r},t)=>a.jsx("tfoot",{ref:t,className:s("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r}))).displayName="TableFooter";const o=e.forwardRef((({className:e,...r},t)=>a.jsx("tr",{ref:t,className:s("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r})));o.displayName="TableRow";const d=e.forwardRef((({className:e,...r},t)=>a.jsx("th",{ref:t,className:s("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...r})));d.displayName="TableHead";const m=e.forwardRef((({className:e,...r},t)=>a.jsx("td",{ref:t,className:s("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...r})));m.displayName="TableCell",e.forwardRef((({className:e,...r},t)=>a.jsx("caption",{ref:t,className:s("mt-4 text-sm text-muted-foreground",e),...r}))).displayName="TableCaption";export{r as T,t as a,o as b,d as c,l as d,m as e};
