import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{aM as s,aa as r,B as t,a7 as i,R as l,an as o,aT as c,aU as d,a5 as m}from"./index-DwykrzWu.js";import n from"./Footer-CEErUVD6.js";import{P as x}from"./PatientInfoSection-B0XzckOx.js";import{u as g}from"./useWeight-CatlFLFx.js";import{u as p}from"./useAge-C_36_Zbj.js";import{C as h,a as j}from"./calculatorSEOData-VJViyzb5.js";import{a as u}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-Cce_pfRl.js";import"./rocket-BVKdk9NC.js";import"./target-Bq7BybE4.js";import"./zap-sktuObTW.js";import"./book-open-vsXyzIQN.js";import"./star-CVUfjIpQ.js";import"./circle-help-DkV0sebI.js";import"./instagram-BgC8q_0n.js";import"./scale-ChsfrFo_.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-hvRXSQ6q.js";import"./user-JQIHs777.js";const b=e=>{switch(e){case"Magreza grave":case"Magreza":case"Obesidade":case"Obesidade grave":return"text-red-600";case"Eutrófico":return"text-green-600";case"Risco de sobrepeso":return"text-yellow-600";case"Sobrepeso":return"text-orange-600";default:return"text-gray-600"}},y=()=>{const y=j.imc,v=u(),{weight:f,setWeight:N,displayWeight:k,setTempWeight:w}=g(),{age:S,setAge:C,displayAge:M,setTempAge:A}=p(),[F,z]=a.useState(""),[O,I]=a.useState(""),[W,E]=a.useState(null);return e.jsxs("div",{className:s.gradientBackground("min-h-screen flex flex-col"),children:[e.jsx(h,{...y}),e.jsx(r,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(t,{variant:"ghost",size:"icon",onClick:()=>v("/calculadoras"),className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:e.jsx(i,{className:"h-5 w-5"})}),e.jsxs("div",{className:"text-center flex-1 space-y-2",children:[e.jsx("h1",{className:s.gradientHeading("text-3xl"),children:"Calculadora de IMC e Obesidade Pediátrica"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avalie o estado nutricional de crianças e adolescentes com base nas tabelas de Z-Score da OMS"})]})]}),e.jsxs(l,{className:s.card("p-6 space-y-6"),children:[e.jsx(x,{weight:k,onWeightChange:w,onWeightCommit:N,age:M,onAgeChange:A,onAgeCommit:C}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{className:"text-gray-800 dark:text-gray-200",children:"Sexo"}),e.jsxs(c,{value:O,onValueChange:I,className:"flex gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{value:"male",id:"male"}),e.jsx(o,{htmlFor:"male",className:"text-gray-700 dark:text-gray-300",children:"Masculino"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{value:"female",id:"female"}),e.jsx(o,{htmlFor:"female",className:"text-gray-700 dark:text-gray-300",children:"Feminino"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"height",className:"text-gray-800 dark:text-gray-200",children:"Altura (cm)"}),e.jsxs("div",{className:"relative",children:[e.jsx(m,{id:"height",type:"text",value:F,onChange:e=>(e=>{const a=e.replace(/[^0-9]/g,"");a.length>3||z(a)})(e.target.value),className:s.input(),placeholder:"Digite a altura em centímetros (ex: 150)"}),e.jsx("span",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500 dark:text-gray-400",children:"cm"})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Digite a altura em centímetros. Exemplo: para 1,50m digite 150"})]}),e.jsx(t,{onClick:()=>{if(!(f&&F&&O&&S))return;const e=parseInt(F,10);if(e<50)return;const a=e/100,s=parseFloat(f.toString()),r=S,t=s/(a*a),i=((e,a)=>{const s="male"===e?[[-.3053,13.4069,.0956],[-.2708,14.9441,.09027],[.1118,16.3195,.08677],[.0068,16.8987,.08495],[-.0727,17.1579,.08378],[-.137,17.2919,.08296],[-.1913,17.3422,.08234],[-.2385,17.3288,.08183],[-.2802,17.2647,.0814],[-.3176,17.1662,.08102],[-.3516,17.0488,.08068],[-.3828,16.9239,.08037],[-.4115,16.7981,.08009],[-.4382,16.6743,.07982],[-.463,16.5548,.07958],[-.4863,16.4409,.07935],[-.5082,16.3335,.07913],[-.5289,16.2329,.07892],[-.5484,16.1392,.07873],[-.5669,16.0528,.07854],[-.5846,15.9743,.07836],[-.6014,15.9039,.07818],[-.6174,15.8412,.07802],[-.6328,15.7852,.07786],[-.6187,16.0189,.07785],[-.584,15.98,.07792],[-.5497,15.9414,.078],[-.5166,15.9036,.07808],[-.485,15.8667,.07818],[-.4552,15.8306,.07829],[-.4274,15.7953,.07841],[-.4016,15.7606,.07854],[-.3782,15.7267,.07867],[-.3572,15.6934,.07882],[-.3388,15.661,.07897],[-.3231,15.6294,.07914],[-.3101,15.5988,.07931],[-.3,15.5693,.0795],[-.2927,15.541,.07969],[-.2884,15.514,.0799],[-.2869,15.4885,.08012],[-.2881,15.4645,.08036],[-.2919,15.442,.08061],[-.2981,15.421,.08087],[-.3067,15.4013,.08115],[-.3174,15.3827,.08144],[-.3303,15.3652,.08174],[-.3452,15.3485,.08205],[-.3622,15.3326,.08238],[-.3811,15.3174,.08272],[-.4019,15.3029,.08307],[-.4245,15.2891,.08343],[-.4488,15.2759,.0838],[-.4747,15.2633,.08418],[-.5019,15.2514,.08457],[-.5303,15.24,.08496],[-.5599,15.2291,.08536],[-.5905,15.2188,.08577],[-.6223,15.2091,.08617],[-.6552,15.2,.08659],[-.6892,15.1916,.087],[-.8886,15.2441,.09692],[-.7621,15.2616,.08414],[-.7856,15.2604,.08439],[-.8089,15.2605,.08464],[-.8322,15.2619,.0849],[-.8554,15.2645,.08516],[-.8785,15.2684,.08543],[-.9015,15.2737,.0857],[-.9243,15.2801,.08597],[-.9471,15.2877,.08625],[-.9697,15.2965,.08653],[-.9921,15.3062,.08682],[-1.0144,15.3169,.08711],[-1.0365,15.3285,.08741],[-1.0584,15.3408,.08771],[-1.0801,15.354,.08802],[-1.1017,15.3679,.08833],[-1.123,15.3825,.08865],[-1.1441,15.3978,.08898],[-1.1649,15.4137,.08931],[-1.1856,15.4302,.08964],[-1.206,15.4473,.08998],[-1.2261,15.465,.09033],[-1.246,15.4832,.09068],[-1.2656,15.5019,.09103],[-1.2849,15.521,.09139],[-1.304,15.5407,.09176],[-1.3228,15.5608,.09213],[-1.3414,15.5814,.09251],[-1.3596,15.6023,.09289],[-1.3776,15.6237,.09327],[-1.3953,15.6455,.09366],[-1.4126,15.6677,.09406],[-1.4297,15.6903,.09445],[-1.4464,15.7133,.09486],[-1.4629,15.7368,.09526],[-1.479,15.7606,.09567],[-1.4947,15.7848,.09609],[-1.5101,15.8094,.09651],[-1.5252,15.8344,.09693],[-1.5399,15.8597,.09735],[-1.5542,15.8855,.09778],[-1.5681,15.9116,.09821],[-1.5817,15.9381,.09864],[-1.5948,15.9651,.09907],[-1.6076,15.9925,.09951],[-1.6199,16.0205,.09994],[-1.6318,16.049,.10038],[-1.6433,16.0781,.10082],[-1.6544,16.1078,.10126],[-1.6651,16.1381,.1017],[-1.6753,16.1692,.10214],[-1.6851,16.2009,.10259],[-1.6944,16.2333,.10303],[-1.7032,16.2665,.10347],[-1.7116,16.3004,.10391],[-1.7196,16.3351,.10435],[-1.7271,16.3704,.10478],[-1.7341,16.4065,.10522],[-1.7407,16.4433,.10566],[-1.7468,16.4807,.10609],[-1.7525,16.5189,.10652],[-1.7578,16.5578,.10695],[-1.7626,16.5974,.10738],[-1.767,16.6376,.1078],[-1.771,16.6786,.10823],[-1.7745,16.7203,.10865],[-1.7777,16.7628,.10906],[-1.7804,16.8059,.10948],[-1.7828,16.8497,.10989],[-1.7847,16.8941,.1103],[-1.7862,16.9392,.1107],[-1.7873,16.985,.1111],[-1.7881,17.0314,.1115],[-1.7884,17.0784,.11189],[-1.7884,17.1262,.11228],[-1.788,17.1746,.11266],[-1.7873,17.2236,.11304],[-1.7861,17.2734,.11342],[-1.7846,17.324,.11379],[-1.7828,17.3752,.11415],[-1.7806,17.4272,.11451],[-1.778,17.4799,.11487],[-1.7751,17.5334,.11522],[-1.7719,17.5877,.11556],[-1.7684,17.6427,.1159],[-1.7645,17.6985,.11623],[-1.7604,17.7551,.11656],[-1.7559,17.8124,.11688],[-1.7511,17.8704,.1172],[-1.7461,17.9292,.11751],[-1.7408,17.9887,.11781],[-1.7352,18.0488,.11811],[-1.7293,18.1096,.11841],[-1.7232,18.171,.11869],[-1.7168,18.233,.11898],[-1.7102,18.2955,.11925],[-1.7033,18.3586,.11952],[-1.6962,18.4221,.11979],[-1.6888,18.486,.12005],[-1.6811,18.5502,.1203],[-1.6732,18.6148,.12055],[-1.6651,18.6795,.12079],[-1.6568,18.7445,.12102],[-1.6482,18.8095,.12125],[-1.6394,18.8746,.12148],[-1.6304,18.9398,.1217],[-1.6211,19.005,.12191],[-1.6116,19.0701,.12212],[-1.602,19.1351,.12233],[-1.5921,19.2,.12253],[-1.5821,19.2648,.12272],[-1.5719,19.3294,.12291],[-1.5615,19.3937,.1231],[-1.551,19.4578,.12328],[-1.5403,19.5217,.12346],[-1.5294,19.5853,.12363],[-1.5185,19.6486,.1238],[-1.5074,19.7117,.12396],[-1.4961,19.7744,.12412],[-1.4848,19.8367,.12428],[-1.4733,19.8987,.12443],[-1.4617,19.9603,.12458],[-1.45,20.0215,.12473],[-1.4382,20.0823,.12487],[-1.4263,20.1427,.12501],[-1.4143,20.2026,.12514],[-1.4022,20.2621,.12528],[-1.39,20.3211,.12541],[-1.3777,20.3796,.12554],[-1.3653,20.4376,.12567],[-1.3529,20.4951,.12579],[-1.3403,20.5521,.12591],[-1.3277,20.6085,.12603],[-1.3149,20.6644,.12615],[-1.3021,20.7197,.12627],[-1.2892,20.7745,.12638],[-1.2762,20.8287,.1265],[-1.2631,20.8824,.12661],[-1.2499,20.9355,.12672],[-1.2366,20.9881,.12683],[-1.2233,21.04,.12694],[-1.2098,21.0914,.12704],[-1.1962,21.1423,.12715],[-1.1826,21.1925,.12726],[-1.1688,21.2423,.12736],[-1.155,21.2914,.12746],[-1.141,21.34,.12756],[-1.127,21.388,.12767],[-1.1129,21.4354,.12777],[-1.0986,21.4822,.12787],[-1.0843,21.5285,.12797],[-1.0699,21.5742,.12807],[-1.0553,21.6193,.12816],[-1.0407,21.6638,.12826],[-1.026,21.7077,.12836],[-1.0112,21.751,.12845],[-.9962,21.7937,.12855],[-.9812,21.8358,.12864],[-.9661,21.8773,.12874],[-.9509,21.9182,.12883],[-.9356,21.9585,.12893],[-.9202,21.9982,.12902],[-.9048,22.0374,.12911],[-.8892,22.076,.1292],[-.8735,22.114,.1293],[-.8578,22.1514,.12939],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948]]:[[-.0631,13.3363,.09272],[.3448,14.5679,.09556],[.1749,15.7679,.09371],[.0643,16.3574,.09254],[-.0191,16.6703,.09166],[-.0864,16.8386,.09096],[-.1429,16.9083,.09036],[-.1916,16.902,.08984],[-.2344,16.8404,.08939],[-.2725,16.7406,.08898],[-.3068,16.6184,.08861],[-.3381,16.4875,.08828],[-.3667,16.3568,.08797],[-.3932,16.2311,.08768],[-.4177,16.1128,.08741],[-.4407,16.0028,.08716],[-.4623,15.9017,.08693],[-.4825,15.8096,.08671],[-.5017,15.7263,.0865],[-.5199,15.6517,.0863],[-.5372,15.5855,.08612],[-.5537,15.5278,.08594],[-.5695,15.4787,.08577],[-.5846,15.438,.0856],[-.5684,15.6881,.08454],[-.5684,15.659,.08452],[-.5684,15.6308,.08449],[-.5684,15.6037,.08446],[-.5684,15.5777,.08444],[-.5684,15.5523,.08443],[-.5684,15.5276,.08444],[-.5684,15.5034,.08448],[-.5684,15.4798,.08455],[-.5684,15.4572,.08467],[-.5684,15.4356,.08484],[-.5684,15.4155,.08506],[-.5684,15.3968,.08535],[-.5684,15.3796,.08569],[-.5684,15.3638,.08609],[-.5684,15.3493,.08654],[-.5684,15.3358,.08704],[-.5684,15.3233,.08757],[-.5684,15.3116,.08813],[-.5684,15.3007,.08872],[-.5684,15.2905,.08931],[-.5684,15.2814,.08991],[-.5684,15.2732,.09051],[-.5684,15.2661,.0911],[-.5684,15.2602,.09168],[-.5684,15.2556,.09227],[-.5684,15.2523,.09286],[-.5684,15.2503,.09345],[-.5684,15.2496,.09403],[-.5684,15.2502,.0946],[-.5684,15.2519,.09515],[-.5684,15.2544,.09568],[-.5684,15.2575,.09618],[-.5684,15.2612,.09665],[-.5684,15.2653,.09709],[-.5684,15.2698,.0975],[-.5684,15.2747,.09789],[-.8886,15.2441,.09692],[-.9068,15.2434,.09738],[-.9248,15.2433,.09783],[-.9427,15.2438,.09829],[-.9605,15.2448,.09875],[-.978,15.2464,.0992],[-.9954,15.2487,.09966],[-1.0126,15.2516,.10012],[-1.0296,15.2551,.10058],[-1.0464,15.2592,.10104],[-1.063,15.2641,.10149],[-1.0794,15.2697,.10195],[-1.0956,15.276,.10241],[-1.1115,15.2831,.10287],[-1.1272,15.2911,.10333],[-1.1427,15.2998,.10379],[-1.1579,15.3095,.10425],[-1.1728,15.32,.10471],[-1.1875,15.3314,.10517],[-1.2019,15.3439,.10562],[-1.216,15.3572,.10608],[-1.2298,15.3717,.10654],[-1.2433,15.3871,.107],[-1.2565,15.4036,.10746],[-1.2693,15.4211,.10792],[-1.2819,15.4397,.10837],[-1.2941,15.4593,.10883],[-1.306,15.4798,.10929],[-1.3175,15.5014,.10974],[-1.3287,15.524,.1102],[-1.3395,15.5476,.11065],[-1.3499,15.5723,.1111],[-1.36,15.5979,.11156],[-1.3697,15.6246,.11201],[-1.379,15.6523,.11246],[-1.388,15.681,.11291],[-1.3966,15.7107,.11335],[-1.4047,15.7415,.1138],[-1.4125,15.7732,.11424],[-1.4199,15.8058,.11469],[-1.427,15.8394,.11513],[-1.4336,15.8738,.11557],[-1.4398,15.909,.11601],[-1.4456,15.9451,.11644],[-1.4511,15.9818,.11688],[-1.4561,16.0194,.11731],[-1.4607,16.0575,.11774],[-1.465,16.0964,.11816],[-1.4688,16.1358,.11859],[-1.4723,16.1759,.11901],[-1.4753,16.2166,.11943],[-1.478,16.258,.11985],[-1.4803,16.2999,.12026],[-1.4823,16.3425,.12067],[-1.4838,16.3858,.12108],[-1.485,16.4298,.12148],[-1.4859,16.4746,.12188],[-1.4864,16.52,.12228],[-1.4866,16.5663,.12268],[-1.4864,16.6133,.12307],[-1.4859,16.6612,.12346],[-1.4851,16.71,.12384],[-1.4839,16.7595,.12422],[-1.4825,16.81,.1246],[-1.4807,16.8614,.12497],[-1.4787,16.9136,.12534],[-1.4763,16.9667,.12571],[-1.4737,17.0208,.12607],[-1.4708,17.0757,.12643],[-1.4677,17.1316,.12678],[-1.4642,17.1883,.12713],[-1.4606,17.2459,.12748],[-1.4567,17.3044,.12782],[-1.4526,17.3637,.12816],[-1.4482,17.4238,.12849],[-1.4436,17.4847,.12882],[-1.4389,17.5464,.12914],[-1.4339,17.6088,.12946],[-1.4288,17.6719,.12978],[-1.4235,17.7357,.13009],[-1.418,17.8001,.1304],[-1.4123,17.8651,.1307],[-1.4065,17.9306,.13099],[-1.4006,17.9966,.13129],[-1.3945,18.063,.13158],[-1.3883,18.1297,.13186],[-1.3819,18.1967,.13214],[-1.3755,18.2639,.13241],[-1.3689,18.3312,.13268],[-1.3621,18.3986,.13295],[-1.3553,18.466,.13321],[-1.3483,18.5333,.13347],[-1.3413,18.6006,.13372],[-1.3341,18.6677,.13397],[-1.3269,18.7346,.13421],[-1.3195,18.8012,.13445],[-1.3121,18.8675,.13469],[-1.3046,18.9335,.13492],[-1.297,18.9991,.13514],[-1.2894,19.0642,.13537],[-1.2816,19.1289,.13559],[-1.2739,19.1931,.1358],[-1.2661,19.2567,.13601],[-1.2583,19.3197,.13622],[-1.2504,19.382,.13642],[-1.2425,19.4437,.13662],[-1.2345,19.5045,.13681],[-1.2266,19.5647,.137],[-1.2186,19.624,.13719],[-1.2107,19.6824,.13738],[-1.2027,19.74,.13756],[-1.1947,19.7966,.13774],[-1.1867,19.8523,.13791],[-1.1788,19.907,.13808],[-1.1708,19.9607,.13825],[-1.1629,20.0133,.13841],[-1.1549,20.0648,.13858],[-1.147,20.1152,.13873],[-1.139,20.1644,.13889],[-1.1311,20.2125,.13904],[-1.1232,20.2595,.1392],[-1.1153,20.3053,.13934],[-1.1074,20.3499,.13949],[-1.0996,20.3934,.13963],[-1.0917,20.4357,.13977],[-1.0838,20.4769,.13991],[-1.076,20.517,.14005],[-1.0681,20.556,.14018],[-1.0603,20.5938,.14031],[-1.0525,20.6306,.14044],[-1.0447,20.6663,.14057],[-1.0368,20.7008,.1407],[-1.029,20.7344,.14082],[-1.0212,20.7668,.14094],[-1.0134,20.7982,.14106],[-1.0055,20.8286,.14118],[-.9962,21.7937,.12855],[-.9812,21.8358,.12864],[-.9661,21.8773,.12874],[-.9509,21.9182,.12883],[-.9356,21.9585,.12893],[-.9202,21.9982,.12902],[-.9048,22.0374,.12911],[-.8892,22.076,.1292],[-.8735,22.114,.1293],[-.8578,22.1514,.12939],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948],[-.8419,22.1883,.12948]],r=s[Math.min(Math.floor(a),s.length-1)];return r&&3===r.length?r:[0,16,.1]})(O,r),l=((e,a)=>{const[s,r,t]=a;return(Math.pow(e/r,s)-1)/(s*t)})(t,i),o=((e,a)=>a<-3?"Magreza grave":a<-2?"Magreza":a<=1?"Eutrófico":a<=2?e<5?"Risco de sobrepeso":"Sobrepeso":a<=3?e<5?"Sobrepeso":"Obesidade":e<5?"Obesidade":"Obesidade grave")(Math.floor(r/12),l);E({bmi:t,zScore:l,classification:o})},className:"w-full",disabled:!f||!F||!O||!S||parseInt(F,10)<50,children:"Calcular"})]}),W&&e.jsx("div",{className:"space-y-4 p-4 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-white dark:bg-slate-700/50 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"IMC"}),e.jsxs("div",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[W.bmi.toFixed(1).replace(".",",")," kg/m²"]})]}),e.jsxs("div",{className:"text-center p-4 bg-white dark:bg-slate-700/50 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Z-Score"}),e.jsx("div",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:W.zScore.toFixed(2).replace(".",",")})]}),e.jsxs("div",{className:"text-center p-4 bg-white dark:bg-slate-700/50 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Classificação"}),e.jsx("div",{className:`text-xl font-semibold ${b(W.classification)}`,children:W.classification})]})]})})]})]})}),e.jsx(n,{})]})};export{y as default};
