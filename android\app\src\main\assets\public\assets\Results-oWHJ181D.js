import{j as e}from"./radix-core-6kBL75b5.js";import{r}from"./critical-DVX9Inzy.js";import{c as s,d as a,s as t,R as d,aJ as l,W as o,Z as i,U as n,ao as c,B as m,aB as x,aa as p,L as b}from"./index-DwykrzWu.js";import{T as h}from"./target-Bq7BybE4.js";import{C as g}from"./clock-v9u4hrAh.js";import{A as u}from"./award-BedCUPQR.js";import{B as j}from"./book-open-vsXyzIQN.js";import{a as y,c as N}from"./router-BAzpOxbo.js";import{E as v}from"./external-link-YG2Q46QJ.js";import{R as f}from"./rotate-ccw-D8YeinyV.js";import{A as k,T as w}from"./FeedbackTrigger-Cce_pfRl.js";import{H as _}from"./house-DCEvqY-i.js";import M from"./Footer-CEErUVD6.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./rocket-BVKdk9NC.js";import"./zap-sktuObTW.js";import"./star-CVUfjIpQ.js";import"./circle-help-DkV0sebI.js";import"./instagram-BgC8q_0n.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=s("BookmarkCheck",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2Z",key:"169p4p"}],["path",{d:"m9 10 2 2 4-4",key:"1gnqz4"}]]),C=(e,r)=>e?Object.entries(e).reduce(((e,[s,a])=>({...e,[s]:{name:r[s]||"Unknown",correct:a.correct,total:a.total}})),{}):{},T=({title:r,value:s,icon:a,trend:t,className:l})=>e.jsx(d,{className:`stat-card p-5 ${l||""}`,children:e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"mb-3 bg-primary/10 p-3 rounded-full",children:e.jsx("div",{className:"text-primary",children:a})}),e.jsxs("div",{className:"space-y-1 w-full",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r}),e.jsx("h3",{className:"text-2xl font-bold text-gray-800 dark:text-gray-200",children:s}),t&&e.jsxs("p",{className:"text-sm mt-2 "+(t.isPositive?"text-green-600":"text-red-600"),children:[t.isPositive?"+":"-",t.value,"% desde semana passada"]})]})]})}),A=({stats:r,totalQuestions:s})=>{const a=(r.correct_answers||0)+(r.incorrect_answers||0),t=a>0?Math.round(r.correct_answers/a*100):0,m=e=>e>=80?"bg-green-500":e>=60?"bg-emerald-500":e>=40?"bg-yellow-500":e>=20?"bg-orange-500":"bg-red-500";return e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",children:[e.jsx(T,{title:"Total de Questões",value:s,icon:e.jsx(l,{className:"w-6 h-6 text-blue-500"}),className:"hover:scale-105 transition-transform border border-blue-100 dark:border-blue-700"}),e.jsx(T,{title:"Taxa de Acerto",value:`${t}%`,icon:e.jsx(h,{className:"w-6 h-6 text-green-500"}),className:"hover:scale-105 transition-transform border border-green-100 dark:border-green-700"}),e.jsx(T,{title:"Tempo Médio",value:`${Math.round(r.time_spent/s)}s`,icon:e.jsx(g,{className:"w-6 h-6 text-purple-500"}),className:"hover:scale-105 transition-transform border border-purple-100 dark:border-purple-700"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs(d,{className:"hover:shadow-md transition-shadow border border-blue-100 dark:border-blue-700",children:[e.jsx(o,{className:"bg-blue-50/50 dark:bg-blue-900/30 border-b border-blue-100 dark:border-blue-700",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{className:"h-5 w-5 text-blue-500 dark:text-blue-400"}),e.jsx(i,{className:"text-lg text-blue-700 dark:text-blue-300",children:"Especialidades"})]})}),e.jsxs(n,{className:"space-y-4 pt-4",children:[Object.entries(r.by_specialty||{}).map((([r,s])=>e.jsxs("div",{className:"animate-fade-in-up",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1.5",children:[e.jsx("span",{className:"font-medium text-gray-700 dark:text-gray-300 truncate max-w-[70%]",title:s.name,children:s.name}),e.jsxs("span",{className:"font-medium text-blue-600",children:[Math.round(s.correct/s.total*100),"%"]})]}),e.jsx(c,{value:s.correct/s.total*100,className:`h-2 ${m(s.correct/s.total*100)}`})]},r))),0===Object.keys(r.by_specialty||{}).length&&e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 text-center py-2",children:"Nenhuma informação disponível"})]})]}),e.jsxs(d,{className:"hover:shadow-md transition-shadow border border-green-100 dark:border-green-700",children:[e.jsx(o,{className:"bg-green-50/50 dark:bg-green-900/30 border-b border-green-100 dark:border-green-700",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(j,{className:"h-5 w-5 text-green-500 dark:text-green-400"}),e.jsx(i,{className:"text-lg text-green-700 dark:text-green-300",children:"Temas"})]})}),e.jsxs(n,{className:"space-y-4 pt-4",children:[Object.entries(r.by_theme||{}).map((([r,s])=>e.jsxs("div",{className:"animate-fade-in-up",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1.5",children:[e.jsx("span",{className:"font-medium text-gray-700 dark:text-gray-300 truncate max-w-[70%]",title:s.name,children:s.name}),e.jsxs("span",{className:"font-medium text-green-600",children:[Math.round(s.correct/s.total*100),"%"]})]}),e.jsx(c,{value:s.correct/s.total*100,className:`h-2 ${m(s.correct/s.total*100)}`})]},r))),0===Object.keys(r.by_theme||{}).length&&e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 text-center py-2",children:"Nenhuma informação disponível"})]})]}),e.jsxs(d,{className:"hover:shadow-md transition-shadow border border-purple-100 dark:border-purple-700",children:[e.jsx(o,{className:"bg-purple-50/50 dark:bg-purple-900/30 border-b border-purple-100 dark:border-purple-700",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(E,{className:"h-5 w-5 text-purple-500 dark:text-purple-400"}),e.jsx(i,{className:"text-lg text-purple-700 dark:text-purple-300",children:"Focos"})]})}),e.jsxs(n,{className:"space-y-4 pt-4",children:[Object.entries(r.by_focus||{}).map((([r,s])=>e.jsxs("div",{className:"animate-fade-in-up",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1.5",children:[e.jsx("span",{className:"font-medium text-gray-700 dark:text-gray-300 truncate max-w-[70%]",title:s.name,children:s.name}),e.jsxs("span",{className:"font-medium text-purple-600",children:[Math.round(s.correct/s.total*100),"%"]})]}),e.jsx(c,{value:s.correct/s.total*100,className:`h-2 ${m(s.correct/s.total*100)}`})]},r))),0===Object.keys(r.by_focus||{}).length&&e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 text-center py-2",children:"Nenhuma informação disponível"})]})]})]})]})},O=({stats:r})=>{const s=y(),a=r.correct_answers+r.incorrect_answers,t=a>0?Math.round(r.correct_answers/a*100):0;return e.jsxs("div",{className:"container mx-auto px-3 md:px-4 py-4 md:py-8 space-y-4 md:space-y-8 max-w-4xl",children:[e.jsxs("div",{className:"rounded-xl bg-gradient-to-r from-primary/5 via-primary/20 to-primary/5 backdrop-blur-sm border border-primary/20 p-4 md:p-8 text-center shadow-md",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold gradient-text bg-gradient-to-r from-primary to-violet-500 bg-clip-text text-transparent",children:"Parabéns!"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mt-2 text-base md:text-lg",children:"Você completou esta sessão de estudos"})]}),e.jsx("div",{className:"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl p-1 shadow-xl",children:e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-6 md:p-8",children:e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 px-4 py-2 rounded-full border border-green-200 dark:border-green-700",children:[e.jsx("span",{className:"text-2xl",children:"🚀"}),e.jsx("span",{className:"text-sm font-bold text-gray-700 dark:text-gray-300",children:"Próximo Nível"})]}),e.jsxs("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100",children:["Pronto para ",e.jsx("span",{className:"text-blue-600",children:"todas as especialidades?"})]}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed",children:["Você dominou a pediatria! Agora explore o universo completo do MedEvo com",e.jsx("strong",{children:" todas as especialidades médicas"})," e recursos premium."]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-6",children:[e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/30 p-4 rounded-xl border border-blue-200 dark:border-blue-700",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🏥"}),e.jsx("div",{className:"font-semibold text-gray-900 dark:text-gray-100 text-sm",children:"5 Grandes Especialidades"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Explore as principais áreas médicas"})]}),e.jsxs("div",{className:"bg-purple-50 dark:bg-purple-900/30 p-4 rounded-xl border border-purple-200 dark:border-purple-700",children:[e.jsx("div",{className:"text-2xl mb-2",children:"📚"}),e.jsx("div",{className:"font-semibold text-gray-900 dark:text-gray-100 text-sm",children:"100+ Mil Questões"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Cronograma personalizado"})]}),e.jsxs("div",{className:"bg-green-50 dark:bg-green-900/30 p-4 rounded-xl border border-green-200 dark:border-green-700",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🤖"}),e.jsx("div",{className:"font-semibold text-gray-900 dark:text-gray-100 text-sm",children:"Dr Will - Sua IA"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Te acompanha em tempo real"})]})]}),e.jsx("div",{className:"pt-4 w-full flex justify-center",children:e.jsxs(m,{onClick:()=>window.open("https://medevo.com.br","_blank"),size:"lg",className:"w-full max-w-sm bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-4 text-sm md:text-base rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:[e.jsx(v,{className:"h-4 w-4 md:h-5 md:w-5 mr-2 flex-shrink-0"}),e.jsx("span",{className:"hidden sm:inline",children:"Explorar MedEvo Completo"}),e.jsx("span",{className:"sm:hidden",children:"Explorar MedEvo"}),e.jsx("span",{className:"ml-2 text-xs bg-white/20 px-2 py-1 rounded-full flex-shrink-0",children:"GRÁTIS"})]})})]})})}),e.jsx(d,{className:"bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden animate-fade-in-up",children:e.jsxs(n,{className:"p-4 md:p-6",children:[e.jsx("h2",{className:"text-lg md:text-xl font-bold text-gray-800 dark:text-gray-200 mb-4 md:mb-6 text-center border-b dark:border-gray-600 pb-3",children:"Desempenho Geral"}),e.jsxs("div",{className:"space-y-4 md:space-y-8",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4 md:p-5 rounded-lg border border-blue-100 dark:border-blue-700",children:[e.jsxs("div",{className:"flex flex-col items-center mb-3",children:[e.jsx("span",{className:"uppercase text-xs font-semibold tracking-wider text-blue-500 mb-1",children:"Taxa de Acerto"}),e.jsxs("div",{className:"relative",children:[e.jsxs("span",{className:"text-3xl md:text-4xl font-bold text-blue-600",children:[t,"%"]}),t>=70&&e.jsx("span",{className:"absolute -top-1 -right-4 md:-right-6 text-yellow-500",children:"★"})]})]}),e.jsx(c,{value:t,className:"h-3 bg-blue-100 dark:bg-blue-800",style:{background:"linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1))"}}),e.jsxs("div",{className:"flex justify-between mt-3 text-xs md:text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-400"}),e.jsxs("span",{className:"text-gray-600",children:["Corretas: ",r.correct_answers]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-400"}),e.jsxs("span",{className:"text-gray-600",children:["Incorretas: ",r.incorrect_answers]})]})]})]}),e.jsxs("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/30 dark:to-pink-900/30 p-4 md:p-5 rounded-lg border border-purple-100 dark:border-purple-700",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"uppercase text-xs font-semibold tracking-wider text-purple-500 mb-1",children:"Tempo Médio por Questão"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-2xl md:text-3xl font-bold text-purple-600",children:a>0?Math.round(r.time_spent/a):0}),e.jsx("span",{className:"text-sm text-purple-500 mt-1",children:"segundos"})]})]}),e.jsx("div",{className:"h-2 w-full bg-purple-100 dark:bg-purple-800 rounded-full mt-4 overflow-hidden",children:e.jsx("div",{className:"h-full bg-purple-400 rounded-full",style:{width:`${a>0?Math.min(100,Math.round(r.time_spent/a)/60*100):0}%`,transition:"width 1s ease-in-out"}})}),e.jsx("p",{className:"text-xs text-center text-gray-500 mt-3",children:a>0?Math.round(r.time_spent/a)<30?"Excelente tempo de resposta!":Math.round(r.time_spent/a)<45?"Bom tempo de resposta!":"Continue praticando para melhorar seu tempo!":"Nenhuma questão respondida"})]})]})]})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 p-4 md:p-6 space-y-4 md:space-y-8 animate-fade-in-up",children:[e.jsx("h2",{className:"text-lg md:text-xl font-bold text-gray-800 dark:text-gray-200 mb-4",children:"Análise Detalhada"}),e.jsx(A,{stats:{correct_answers:r.correct_answers,incorrect_answers:r.incorrect_answers,time_spent:r.time_spent,by_specialty:r.by_specialty,by_theme:r.by_theme,by_focus:r.by_focus},totalQuestions:a})]}),e.jsx("div",{className:"pt-4 md:pt-6",children:e.jsx(d,{className:"bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-900/30 dark:via-gray-800 dark:to-purple-900/30 border-2 border-blue-100 dark:border-blue-700",children:e.jsxs(n,{className:"p-4 md:p-8",children:[e.jsxs("div",{className:"text-center mb-4 md:mb-6",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2",children:[e.jsx(x,{className:"h-5 w-5 md:h-6 md:w-6 text-blue-500"}),e.jsx("h3",{className:"text-lg md:text-2xl font-bold",children:"Continue Sua Jornada de Estudos"}),e.jsx(x,{className:"h-5 w-5 md:h-6 md:w-6 text-purple-500"})]}),e.jsx("p",{className:"text-gray-600 text-sm md:text-lg",children:"Escolha como deseja prosseguir com seus estudos em pediatria"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4",children:[e.jsxs(m,{onClick:()=>s("/estudos/filtros"),className:"group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-300 text-white shadow-lg hover:shadow-xl p-4 md:p-6 h-auto flex-col gap-2 md:gap-3 rounded-xl",children:[e.jsx(f,{className:"h-6 w-6 md:h-8 md:w-8 transition-transform group-hover:rotate-12"}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold text-base md:text-lg",children:"Fazer Mais Questões"}),e.jsx("div",{className:"text-blue-100 text-xs md:text-sm",children:"Continue praticando pediatria"})]}),e.jsx(k,{className:"h-3 w-3 md:h-4 md:w-4 transition-transform group-hover:translate-x-1"})]}),e.jsxs(m,{onClick:()=>s("/"),variant:"outline",className:"group border-2 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 p-4 md:p-6 h-auto flex-col gap-2 md:gap-3 rounded-xl",children:[e.jsx(_,{className:"h-6 w-6 md:h-8 md:w-8 text-gray-600 group-hover:text-blue-600 transition-colors"}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold text-base md:text-lg text-gray-800 dark:text-gray-200",children:"Página Inicial"}),e.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-xs md:text-sm",children:"Voltar ao PedBook"})]}),e.jsx(k,{className:"h-3 w-3 md:h-4 md:w-4 text-gray-400 group-hover:text-blue-600 transition-all group-hover:translate-x-1"})]}),e.jsxs(m,{onClick:()=>window.open("https://medevo.com.br","_blank"),variant:"outline",className:"group border-2 border-purple-200 dark:border-purple-600 hover:border-purple-300 dark:hover:border-purple-500 bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 p-4 md:p-6 h-auto flex-col gap-2 md:gap-3 rounded-xl",children:[e.jsx(v,{className:"h-6 w-6 md:h-8 md:w-8 text-purple-600 transition-transform group-hover:scale-110"}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold text-base md:text-lg text-gray-800 dark:text-gray-200",children:"Acesso Rápido"}),e.jsx("div",{className:"text-purple-600 text-xs md:text-sm",children:"MedEvo Completo"})]}),e.jsx(k,{className:"h-3 w-3 md:h-4 md:w-4 text-purple-400 group-hover:text-purple-600 transition-all group-hover:translate-x-1"})]})]}),e.jsx("div",{className:"mt-6 p-4 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/30 dark:to-orange-900/30 border border-amber-200 dark:border-amber-700 rounded-xl",children:e.jsxs("p",{className:"text-center text-amber-800 dark:text-amber-300 font-medium text-sm md:text-base",children:["🎯 ",e.jsx("strong",{children:"Dica:"})," A prática constante é a chave para o sucesso na residência médica!"]})})]})})})]})},F=()=>{const{sessionId:s}=N(),{stats:l,isLoading:o}=(e=>{const[s,d]=r.useState(null),[l,o]=r.useState(!0),{toast:i}=a();return r.useEffect((()=>{e&&(async e=>{try{const{data:r,error:s}=await t.rpc("get_session_statistics",{p_session_id:e});if(s)throw s;if(!r?.length)throw new Error("No statistics found");const a=r[0],l=[...Object.keys(a.by_specialty||{}),...Object.keys(a.by_theme||{}),...Object.keys(a.by_focus||{})],{data:o}=await t.from("study_categories").select("id, name, type").in("id",l),i=(o||[]).reduce(((e,r)=>({...e,[r.id]:r.name})),{}),n={time_spent:a.avg_response_time||0,correct_answers:a.total_correct||0,incorrect_answers:a.total_incorrect||0,by_theme:C(a.by_theme,i),by_specialty:C(a.by_specialty,i),by_focus:C(a.by_focus,i)};d(n)}catch(r){i({title:"Erro ao carregar estatísticas",description:r.message,variant:"destructive"})}finally{o(!1)}})(e)}),[e,i]),{stats:s,isLoading:l}})(s||null);return o?e.jsxs(e.Fragment,{children:[e.jsx(p,{}),e.jsx("div",{className:"container mx-auto px-4 pt-16 md:py-8 max-w-4xl",children:e.jsxs("div",{className:"min-h-[60vh] flex flex-col items-center justify-center",children:[e.jsx("div",{className:"rounded-full bg-primary/10 p-4 mb-4",children:e.jsx(b,{className:"h-8 w-8 text-primary animate-spin"})}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-lg",children:"Carregando resultados..."})]})}),e.jsx(M,{})]}):l?e.jsxs(e.Fragment,{children:[e.jsx(p,{}),e.jsx("div",{className:"pt-12 md:pt-16 pb-16 md:pb-20 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800",children:e.jsx(O,{stats:l})}),e.jsx(M,{})]}):e.jsxs(e.Fragment,{children:[e.jsx(p,{}),e.jsx("div",{className:"container mx-auto px-4 pt-16 md:py-8 max-w-4xl",children:e.jsx(d,{className:"p-12 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"rounded-full bg-gray-200 dark:bg-gray-600 p-4 inline-block mb-4",children:e.jsx(w,{className:"h-8 w-8 text-gray-400 dark:text-gray-500"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-600 dark:text-gray-300 mb-2",children:"Nenhum resultado encontrado"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-lg",children:"Não foi possível encontrar estatísticas para esta sessão de estudos."})]})})}),e.jsx(M,{})]})};export{F as Results,F as default};
