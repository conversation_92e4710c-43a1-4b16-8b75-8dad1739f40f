import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{c as t,D as a,e as r,f as i,g as o,z as n,a1 as c,a2 as d,a3 as l,a4 as m,a5 as u,a6 as h,a7 as p,ao as x,aA as g,B as j,L as f,s as b,Q as y,d as v,aK as _,T as w,U as N,u as k,R as C,ar as S,as as z,at as E,au as I,ax as q,ae as V,af as L,ag as O,ah as F,aj as T,b7 as U,I as A,Y as D,_ as R,a0 as P,V as M}from"./index-CNG-Xj2g.js";import{a as K,u as $}from"./query-vendor-B-7l6Nb3.js";import{A as B,a as Q,b as H,c as G,d as W,e as Y,f as X,g as J}from"./alert-dialog-2iES_sbM.js";import{c as Z,b as ee,d as se,s as te,e as ae}from"./form-vendor-rYZw_ur7.js";import{U as re}from"./upload-DYkAbgkr.js";import{A as ie,s as oe}from"./slugify-BHWK7sxP.js";import{d as ne}from"./supabase-vendor-qi_Ptfv-.js";import{u as ce,S as de,L as le,U as me,I as ue,E as he}from"./editor-vendor-0G6QaH11.js";import{u as pe,C as xe,a as ge,b as je,D as fe,c as be,S as ye,v as ve,d as _e,s as we,K as Ne,P as ke}from"./dnd-vendor-DTrLYOhb.js";import{A as Ce}from"./arrow-down-BZwlVSqZ.js";import{P as Se}from"./plus-5xdDX9-5.js";import{T as ze}from"./trash-2-2OmjJUoy.js";import{H as Ee,L as Ie}from"./list-ordered-6A3qXj38.js";import{H as qe,L as Ve}from"./link-BmvMuAKJ.js";import{v as Le}from"./v4-OjsI5tD8.js";import{W as Oe}from"./wand-sparkles-Cs1pC1J4.js";import{B as Fe,I as Te}from"./italic-DXnGqOME.js";import{L as Ue}from"./list-Dj27luz0.js";import{S as Ae}from"./square-pen-D91EbxD5.js";import{T as De}from"./trash-iljEoaO0.js";import"./router-BAzpOxbo.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Re=t("Grip",[["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"19",cy:"5",r:"1",key:"w8mnmm"}],["circle",{cx:"5",cy:"5",r:"1",key:"lttvr7"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}],["circle",{cx:"19",cy:"19",r:"1",key:"shf9b7"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]]),Pe=t("ImagePlus",[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]]);
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function Me({category:t,onOpenChange:v,open:_}){const w=K(),[N,k]=s.useState(null),[C,S]=s.useState(t?.image_url||null),[z,E]=s.useState(!1),[I,q]=s.useState(!1),V=Z({defaultValues:{name:t?.name||"",description:t?.description||"",slug:t?.slug||"",coming_soon:t?.coming_soon||!1}});s.useEffect((()=>{t&&(V.reset({name:t.name,description:t.description||"",slug:t.slug,coming_soon:t.coming_soon||!1}),S(t.image_url||null),k(null))}),[t,V]);const L=async e=>{if(!N)return null;const s=`${e}.${N.name.split(".").pop()}`,{error:t}=await b.storage.from("category-icons").upload(s,N,{upsert:!0});if(t)throw t;const{data:a}=b.storage.from("category-icons").getPublicUrl(s);return a.publicUrl};return e.jsxs(a,{open:_,onOpenChange:v,children:[e.jsxs(r,{children:[e.jsxs(i,{children:[e.jsx(o,{children:t?"Editar Categoria":"Nova Categoria"}),e.jsx(n,{children:t?"Edite as informações da categoria abaixo:":"Crie uma nova categoria"})]}),e.jsx(c,{...V,children:e.jsxs("form",{onSubmit:V.handleSubmit((async e=>{try{q(!0);let s=t?.image_url;if(t?.id){N&&(s=await L(t.id));const{error:a}=await b.from("pedbook_conducts_categories").update({name:e.name,description:e.description,slug:e.slug,image_url:s,coming_soon:e.coming_soon,updated_at:(new Date).toISOString()}).eq("id",t.id);if(a)throw a;y({title:"Categoria atualizada com sucesso"})}else{const{data:t,error:a}=await b.from("pedbook_conducts_categories").insert([{name:e.name,description:e.description,slug:e.slug,coming_soon:e.coming_soon}]).select().single();if(a)throw a;if(N&&t){s=await L(t.id);const{error:e}=await b.from("pedbook_conducts_categories").update({image_url:s}).eq("id",t.id);if(e)throw e}y({title:"Categoria criada com sucesso"})}w.invalidateQueries({queryKey:["conducts-categories"]}),v(!1)}catch(s){y({title:"Erro",description:"Ocorreu um erro ao salvar a categoria",variant:"destructive"})}finally{q(!1)}})),className:"space-y-4",children:[e.jsx(d,{control:V.control,name:"name",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Nome"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:V.control,name:"description",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Descrição"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:V.control,name:"slug",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Slug"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"image",children:"Imagem"}),e.jsxs("div",{className:"mt-1 flex items-center gap-4",children:[C&&e.jsx("img",{src:C,alt:"Category preview",className:"h-16 w-16 object-cover rounded-lg"}),e.jsxs(x,{htmlFor:"image-upload",className:"cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(re,{className:"h-4 w-4 mr-2"}),"Escolher arquivo"]}),e.jsx(h,{id:"image-upload",type:"file",accept:"image/*",onChange:e=>{const s=e.target.files?.[0];if(s){k(s);const e=new FileReader;e.onloadend=()=>{S(e.result)},e.readAsDataURL(s)}},className:"hidden"})]})]}),e.jsx(d,{control:V.control,name:"coming_soon",render:({field:s})=>e.jsxs(l,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(u,{children:e.jsx(g,{checked:s.value,onCheckedChange:s.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(m,{children:"Em breve"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Marque esta opção para indicar que esta categoria estará disponível em breve"})]})]})}),e.jsxs("div",{className:"flex justify-end gap-2",children:[t&&e.jsx(j,{type:"button",variant:"destructive",onClick:()=>E(!0),children:"Excluir"}),e.jsx(j,{type:"submit",disabled:I,children:I?e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):t?"Salvar":"Criar"})]})]})})]}),z&&e.jsx(B,{open:z,onOpenChange:E,children:e.jsxs(Q,{children:[e.jsxs(H,{children:[e.jsx(G,{children:"Excluir Categoria"}),e.jsxs(W,{children:['Tem certeza de que deseja excluir a categoria "',t?.name,'"?']})]}),e.jsxs(Y,{children:[e.jsx(X,{children:"Cancelar"}),e.jsx(J,{onClick:async()=>{try{if(!t?.id)return;const{data:e,error:s}=await b.from("pedbook_conducts_topics").select("id").eq("category_id",t.id);if(s)throw s;if(e&&e.length>0){const s=e.map((e=>e.id)),{error:a}=await b.from("pedbook_conducts_optimized").delete().in("topic_id",s);if(a)throw a;const{error:r}=await b.from("pedbook_conducts_summaries").delete().in("topic_id",s);if(r)throw r;const{error:i}=await b.from("pedbook_conducts_content").delete().in("topic_id",s);if(i)throw i;const{error:o}=await b.from("pedbook_conducts_feedback").delete().in("summary_id",s);if(o)throw o;const{error:n}=await b.from("pedbook_conducts_topics").delete().eq("category_id",t.id);if(n)throw n}const{error:a}=await b.from("pedbook_conducts_categories").delete().eq("id",t.id);if(a)throw a;y({title:"Categoria removida com sucesso"}),w.invalidateQueries({queryKey:["conducts-categories"]}),E(!1),v(!1)}catch(e){y({title:"Erro ao remover categoria",description:e.message||"Ocorreu um erro ao remover a categoria",variant:"destructive"})}},children:"Excluir"})]})]})})]})}const Ke=({onImageInserted:t,children:n})=>{const[c,d]=s.useState(!1),[l,m]=s.useState(!1),[u,p]=s.useState(null),[y,N]=s.useState(""),[k,C]=s.useState(""),[S,z]=s.useState(""),[E,I]=s.useState(!1),{toast:q}=v();return e.jsxs(a,{open:c,onOpenChange:e=>{d(e),e||(p(null),N(""),C(""),z(""),I(!1))},children:[e.jsx(_,{asChild:!0,children:n||e.jsxs(j,{variant:"outline",size:"sm",children:[e.jsx(Pe,{className:"w-4 h-4 mr-2"}),"Adicionar Imagem"]})}),e.jsxs(r,{className:"max-w-md",children:[e.jsx(i,{children:e.jsx(o,{children:"Adicionar Imagem"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(x,{htmlFor:"image-file",children:"Imagem *"}),e.jsx(h,{id:"image-file",type:"file",accept:"image/*",onChange:e=>{const s=e.target.files?.[0];if(s){p(s);const e=URL.createObjectURL(s);N(e)}},className:"mt-1"})]}),y&&e.jsx("div",{className:"text-center",children:e.jsx("img",{src:y,alt:"Preview",className:"max-w-full h-32 object-cover rounded-md mx-auto"})}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"image-title",children:"Título da Imagem"}),e.jsx(h,{id:"image-title",value:k,onChange:e=>C(e.target.value),placeholder:"Ex: Lesão descamativa com bolhas flácidas na mão do RN",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"image-source",children:"Fonte"}),e.jsx(w,{id:"image-source",value:S,onChange:e=>z(e.target.value),placeholder:"Ex: CNX OpenStax, licenciada sob domínio CC BY-SA 4.0",className:"mt-1 h-20"})]}),e.jsxs("div",{className:"flex items-center space-x-2 p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg",children:[e.jsx(g,{id:"global-image",checked:E,onCheckedChange:e=>I(e)}),e.jsxs("div",{className:"grid gap-1.5 leading-none",children:[e.jsx(x,{htmlFor:"global-image",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-900 dark:text-gray-100",children:"🖼️ Imagem Global"}),e.jsx("p",{className:"text-xs text-purple-600 dark:text-purple-400",children:"Marque para que esta imagem apareça na galeria de imagens do resumo"})]})]}),e.jsxs("div",{className:"flex gap-2 pt-4",children:[e.jsx(j,{variant:"outline",onClick:()=>d(!1),className:"flex-1",children:"Cancelar"}),e.jsx(j,{onClick:async()=>{if(u)try{m(!0);const e=URL.createObjectURL(u),s=document.createElement("img");await new Promise(((t,a)=>{s.onload=t,s.onerror=a,s.src=e}));const a=document.createElement("canvas");a.width=s.width,a.height=s.height;const r=a.getContext("2d");if(!r)throw new Error("Could not get canvas context");r.drawImage(s,0,0),URL.revokeObjectURL(e);const i=await new Promise((e=>{a.toBlob((s=>e(s)),"image/webp",.8)})),o=`${crypto.randomUUID()}.webp`,{data:n,error:c}=await b.storage.from("category-icons").upload(o,i);if(c)throw c;const{data:{publicUrl:l}}=b.storage.from("category-icons").getPublicUrl(o);let h=`<ImageWithCaption src="${l}"`;k.trim()&&(h+=` title="${k.trim()}"`),S.trim()&&(h+=` source="${S.trim()}"`),E&&(h+=' global="true"'),h+=" />",t(h),p(null),N(""),C(""),z(""),I(!1),d(!1),q({title:"Sucesso",description:"Imagem inserida com sucesso!"})}catch(e){q({title:"Erro ao enviar imagem",description:"Ocorreu um erro ao fazer upload da imagem",variant:"destructive"})}finally{m(!1)}else q({title:"Erro",description:"Selecione uma imagem primeiro",variant:"destructive"})},disabled:!u||l,className:"flex-1",children:l?e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"w-4 h-4 mr-2 animate-spin"}),"Enviando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(re,{className:"w-4 h-4 mr-2"}),"Inserir"]})})]})]})]})]})};function $e({id:s,type:t,content:a,textContent:r,onUpdate:i,onDelete:o,onAddSubsection:n,onMoveUp:c,onMoveDown:d,isFirst:l,isLast:m}){const{attributes:u,listeners:h,setNodeRef:p,transform:x,transition:g}=pe({id:s}),f={transform:xe.Transform.toString(x),transition:g};return e.jsx("div",{ref:p,style:f,className:"space-y-2",children:e.jsx(N,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("button",{className:"cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded",...u,...h,children:e.jsx(Re,{className:"h-4 w-4 text-gray-400"})}),e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsx(w,{value:a,onChange:e=>i(s,e.target.value,"content"),placeholder:"title"===t?"Digite o título...":"Digite o subtítulo...",className:"resize-none"}),e.jsx(w,{value:r,onChange:e=>i(s,e.target.value,"textContent"),placeholder:"Digite o conteúdo...",className:"resize-none min-h-[100px]"})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[!l&&e.jsx(j,{onClick:c,variant:"ghost",size:"icon",className:"h-8 w-8",children:e.jsx(ie,{className:"h-4 w-4"})}),!m&&e.jsx(j,{onClick:d,variant:"ghost",size:"icon",className:"h-8 w-8",children:e.jsx(Ce,{className:"h-4 w-4"})}),"title"===t&&e.jsx(j,{onClick:n,variant:"ghost",size:"icon",className:"h-8 w-8",children:e.jsx(Se,{className:"h-4 w-4"})}),e.jsx(j,{onClick:()=>o(s),variant:"destructive",size:"icon",className:"h-8 w-8",children:e.jsx(ze,{className:"h-4 w-4"})})]})]})})})}function Be({initialContent:t,onChange:a}){const[r,i]=s.useState(t?.sections||[]),o=ge(je(ke),je(Ne,{coordinateGetter:we})),n=(e,s)=>t=>{t.preventDefault(),t.stopPropagation();const o={id:Le(),type:e,content:"",textContent:"",children:[],order:r.length,parentId:s};i(s?e=>{const t=[...e],r=t.findIndex((e=>e.id===s));return-1!==r&&t[r].children.push(o),a({sections:t}),t}:e=>{const s=[...e,o];return a({sections:s}),s})},c=(e,s,t)=>{i((r=>{const i=r.map((a=>a.id===e?{...a,[t]:s}:{...a,children:a.children.map((a=>a.id===e?{...a,[t]:s}:a))}));return a({sections:i}),i}))},d=e=>{i((s=>{const t=s.filter((s=>s.id!==e)).map((s=>({...s,children:s.children.filter((s=>s.id!==e))})));return a({sections:t}),t}))},l=(e,s)=>{i((t=>{const r=t.findIndex((s=>s.id===e));if(-1===r)return t;const i="up"===s?r-1:r+1;if(i<0||i>=t.length)return t;const o=_e(t,r,i);return a({sections:o}),o}))};return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-2 mb-4",children:[e.jsxs(j,{onClick:n("title"),variant:"outline",size:"sm",type:"button",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"w-4 h-4"}),"Adicionar Título"]}),e.jsxs(j,{onClick:()=>{const e=window.getSelection();if(!e||0===e.rangeCount)return;const s=e.toString().trim();if(!s)return;const t={id:Le(),type:"title",content:`<strong>##. ${s}</strong>`,textContent:s,children:[],order:r.length};i((e=>{const s=[...e,t];return a({sections:s}),s}))},variant:"outline",size:"sm",type:"button",className:"flex items-center gap-2",children:[e.jsx(Ee,{className:"w-4 h-4"}),"Formatar como Título"]}),e.jsxs(j,{onClick:()=>{const e=window.getSelection();if(!e||0===e.rangeCount)return;const s=e.toString().trim();if(!s)return;const t={id:Le(),type:"subtitle",content:`<strong>##; ${s}</strong>`,textContent:s,children:[],order:r.length};i((e=>{const s=[...e,t];return a({sections:s}),s}))},variant:"outline",size:"sm",type:"button",className:"flex items-center gap-2",children:[e.jsx(qe,{className:"w-4 h-4"}),"Formatar como Subtítulo"]})]}),e.jsx(fe,{sensors:o,collisionDetection:be,onDragEnd:e=>{const{active:s,over:t}=e;s.id!==t.id&&i((e=>{const r=e.findIndex((e=>e.id===s.id)),i=e.findIndex((e=>e.id===t.id)),o=_e(e,r,i);return a({sections:o}),o}))},children:e.jsx(ye,{items:r.map((e=>e.id)),strategy:ve,children:e.jsx("div",{className:"space-y-4",children:r.map(((s,t)=>e.jsx($e,{id:s.id,type:s.type,content:s.content,textContent:s.textContent,onUpdate:c,onDelete:d,onAddSubsection:n("subtitle",s.id),onMoveUp:()=>l(s.id,"up"),onMoveDown:()=>l(s.id,"down"),isFirst:0===t,isLast:t===r.length-1},s.id)))})})})]})}const Qe=ee({name:te().min(1,"Nome é obrigatório"),description:te().optional(),slug:te().optional(),icon:te().optional(),category_id:te().uuid("Categoria inválida"),parent_id:te().optional(),image_url:te().optional(),summary_title:te().optional(),summary_content:te().optional(),format_type:ae(["standard","simple","optimized"]).default("standard"),conducts_content:te().optional(),treatment_content:te().optional(),has_treatment:se().default(!1),published:se().default(!0)}),He=({editor:s})=>{if(!s)return null;const t=e=>s=>{s.preventDefault(),s.stopPropagation(),e()};return e.jsxs("div",{className:"flex items-center gap-1 border-b p-2",children:[e.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleBold().run())),className:s.isActive("bold")?"bg-muted":"",children:e.jsx(Fe,{className:"h-4 w-4"})}),e.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleItalic().run())),className:s.isActive("italic")?"bg-muted":"",children:e.jsx(Te,{className:"h-4 w-4"})}),e.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>{const e=window.prompt("URL:");e&&s.chain().focus().setLink({href:e}).run()})),className:s.isActive("link")?"bg-muted":"",children:e.jsx(Ve,{className:"h-4 w-4"})}),e.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleBulletList().run())),className:s.isActive("bulletList")?"bg-muted":"",children:e.jsx(Ue,{className:"h-4 w-4"})}),e.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleOrderedList().run())),className:s.isActive("orderedList")?"bg-muted":"",children:e.jsx(Ie,{className:"h-4 w-4"})}),e.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>{const e=window.prompt("URL da imagem:");e&&s.chain().focus().setImage({src:e}).run()})),children:e.jsx(A,{className:"h-4 w-4"})})]})};function Ge({topic:t,categoryId:x,onOpenChange:g,open:_}){const w=K(),{user:N}=k(),{data:D}=$({queryKey:["subcategories",x],queryFn:async()=>{if(!x)return[];const{data:e,error:s}=await b.from("pedbook_conducts_topics").select("id, name").eq("category_id",x).eq("is_subcategory",!0).is("parent_id",null).order("name");if(s)throw s;return e||[]},enabled:!!x}),[R,P]=s.useState(!1),[M,B]=s.useState("basic"),[Q,H]=s.useState(!1),G=s.useRef(null),{formatContent:W,isFormatting:Y}=(()=>{const[e,t]=s.useState(!1),a=ne.useSupabaseClient(),{toast:r}=v();return{formatContent:async e=>{if(!e||0===e.trim().length)return r({variant:"destructive",title:"Erro",description:"Não há conteúdo para formatar."}),null;if(e.trim().length<10)return r({variant:"destructive",title:"Erro",description:"O conteúdo deve ter pelo menos 10 caracteres para ser formatado."}),null;t(!0);try{const{data:s,error:t}=await a.functions.invoke("format-content",{body:{content:e.trim()}});if(t)throw new Error(t.message||"Erro ao chamar função de formatação");if(!s||!s.success)throw new Error(s?.error||s?.details||"Erro desconhecido na formatação");return r({title:"Sucesso!",description:"Conteúdo formatado com sucesso pela IA.",variant:"default"}),s.formattedContent}catch(s){let e="Erro inesperado ao formatar conteúdo";return s instanceof Error&&(e=s.message.includes("quota")||s.message.includes("429")||s.message.includes("Too Many Requests")?"Limite diário da IA atingido. Tente novamente amanhã ou considere fazer upgrade do plano.":s.message.includes("network")||s.message.includes("fetch")?"Erro de conexão. Verifique sua internet e tente novamente.":s.message.includes("timeout")?"Tempo limite excedido. Tente novamente com um texto menor.":s.message),r({variant:"destructive",title:"Erro na formatação",description:e}),null}finally{t(!1)}},isFormatting:e}})(),X=s.useRef(null),J=Z({resolver:C(Qe),defaultValues:{name:"",description:"",slug:"",icon:"",category_id:x,parent_id:"none",image_url:"",summary_title:"",summary_content:"",format_type:"standard",conducts_content:"",treatment_content:"",has_treatment:!1,published:!0},mode:"onSubmit"});X.current=J;const ee=ce({extensions:[de,le.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-500 underline"}}),me,ue.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg my-4"}})],content:"",onUpdate:s.useCallback((({editor:e})=>{const s=e.getHTML();X.current?.setValue("summary_content",s,{shouldDirty:!1,shouldTouch:!0,shouldValidate:!1})}),[])});s.useEffect((()=>{_&&(t?J.reset({name:t.name,description:t.description||"",slug:t.slug,icon:t.icon||"",category_id:t.category_id,parent_id:t.parent_id||"none",image_url:t.image_url||"",published:t.published||!0,summary_title:"",summary_content:"",format_type:"standard",conducts_content:"",treatment_content:"",has_treatment:!1}):J.reset({name:"",description:"",slug:"",icon:"",category_id:x,parent_id:t?.parent_id||"none",image_url:"",summary_title:"",summary_content:"",format_type:"standard",conducts_content:"",treatment_content:"",has_treatment:!1,published:!0}),B("basic"))}),[_,t,x,J]),s.useEffect((()=>{t?.id&&_&&(async()=>{try{const{data:e,error:s}=await b.from("pedbook_conducts_optimized").select("*").eq("topic_id",t.id).maybeSingle();if(e&&!s)return J.setValue("summary_title",e.title||""),J.setValue("format_type","optimized"),J.setValue("conducts_content",e.conducts_content||""),J.setValue("treatment_content",e.treatment_content||""),J.setValue("has_treatment",e.has_treatment||!1),void J.setValue("published",!1!==e.published);const{data:a,error:r}=await b.from("pedbook_conducts_summaries").select("*").eq("topic_id",t.id).maybeSingle();a&&!r?(J.setValue("summary_title",a.title||""),J.setValue("summary_content",a.content||""),J.setValue("format_type",a.format_type||"standard"),J.setValue("published",!1!==a.published),ee&&a.content&&ee.commands.setContent(a.content)):(J.setValue("format_type","standard"),J.setValue("published",!0))}catch(e){}})()}),[t?.id,J,ee,_]),s.useEffect((()=>{x&&J.setValue("category_id",x)}),[x,J]);const se=async e=>{try{if(!e.target.files||0===e.target.files.length)return;const s=e.target.files[0];P(!0);const t=URL.createObjectURL(s),a=document.createElement("img");await new Promise(((e,s)=>{a.onload=e,a.onerror=s,a.src=t}));const r=document.createElement("canvas");r.width=a.width,r.height=a.height;const i=r.getContext("2d");if(!i)throw new Error("Could not get canvas context");i.drawImage(a,0,0),URL.revokeObjectURL(t);const o=await new Promise((e=>{r.toBlob((s=>e(s)),"image/webp",.8)})),n=`${crypto.randomUUID()}.webp`,{data:c,error:d}=await b.storage.from("category-icons").upload(n,o);if(d)throw d;const{data:{publicUrl:l}}=b.storage.from("category-icons").getPublicUrl(n);"image-upload"===e.target.id?J.setValue("image_url",l):"image-upload-content"===e.target.id&&ee&&ee.chain().focus().setImage({src:l}).run(),y({title:"Imagem enviada com sucesso"})}catch(s){y({title:"Erro ao enviar imagem",description:"Ocorreu um erro ao fazer upload da imagem",variant:"destructive"})}finally{P(!1)}},te=e=>{ee&&ee.chain().focus().insertContent(e+"\n\n").run()},ae=(e,s)=>{const t=(J.getValues(s)||"")+"\n\n"+e+"\n\n";J.setValue(s,t)},re=async e=>{const s=J.getValues(e)||"";if(!s.trim())return void y({variant:"destructive",title:"Erro",description:"Não há conteúdo para formatar. Digite algum texto primeiro."});const t=await W(s);t&&(J.setValue(e,t),y({title:"Sucesso!",description:"Conteúdo formatado pela IA aplicado com sucesso."}))},ie=s.useCallback((e=>{let s="";e.sections.forEach((e=>{s+=`<h2><strong>##. ${e.content}</strong></h2>\n\n`,e.children.forEach((e=>{s+=`<h3><strong>##; ${e.content}</strong></h3>\n\n`}))})),ee?.commands.setContent(s),X.current?.setValue("summary_content",s,{shouldDirty:!1,shouldTouch:!0,shouldValidate:!1})}),[ee?.commands]),pe=()=>{B("basic"),J.reset(),g(!1)};return e.jsx(a,{open:_,onOpenChange:e=>{e||pe()},children:e.jsxs(r,{ref:G,className:"max-w-3xl",children:[e.jsxs(i,{children:[e.jsx(o,{children:t?"Editar Tópico":"Novo Tópico"}),e.jsx(n,{children:t?"Edite os detalhes do tópico":"Crie um novo tópico"})]}),e.jsxs(S,{value:M,onValueChange:B,children:[e.jsxs(z,{className:"grid w-full grid-cols-2",children:[e.jsx(E,{value:"basic",children:"Informações Básicas"}),e.jsx(E,{value:"summary",children:"Resumo"})]}),e.jsx(c,{...J,children:e.jsxs("form",{onSubmit:J.handleSubmit((async e=>{try{if(Q)return;if(H(!0),!N)return void y({title:"Erro de autenticação",description:"Você precisa estar autenticado para realizar esta ação",variant:"destructive"});if(!e.category_id)throw new Error("Categoria é obrigatória");const s=e.slug||oe(e.name),a={name:e.name,description:e.description,slug:s,icon:e.icon,category_id:e.category_id,parent_id:e.parent_id&&"none"!==e.parent_id?e.parent_id:null,is_subcategory:!1,image_url:e.image_url};let r=t?.id;if(t?.id){const{error:e}=await b.from("pedbook_conducts_topics").update(a).eq("id",t.id);if(e)throw e;y({title:"Tópico atualizado com sucesso"})}else{const{data:e,error:s}=await b.from("pedbook_conducts_topics").insert([a]).select().single();if(s)throw s;r=e.id,y({title:"Tópico criado com sucesso"})}if("optimized"===e.format_type){if(e.conducts_content||e.treatment_content){const a={topic_id:r,title:e.summary_title||e.name,slug:s,conducts_content:e.conducts_content||"",treatment_content:e.treatment_content||"",format_type:"optimized",has_treatment:e.has_treatment||!1,published:e.published};if(t?.id){const{error:e}=await b.from("pedbook_conducts_optimized").update(a).eq("topic_id",r);if(e)throw e}else{const{error:e}=await b.from("pedbook_conducts_optimized").insert([a]);if(e)throw e}}}else if(e.summary_content){const a={topic_id:r,title:e.summary_title||e.name,content:e.summary_content,content_type:"markdown",format_type:e.format_type,slug:s,published:e.published};if(t?.id){const{error:e}=await b.from("pedbook_conducts_summaries").update(a).eq("topic_id",r);if(e)throw e}else{const{error:e}=await b.from("pedbook_conducts_summaries").insert([a]);if(e)throw e}}w.invalidateQueries({queryKey:["conducts-topics"]}),y({title:"Sucesso",description:t?.id?"Tópico atualizado com sucesso!":"Tópico criado com sucesso!"}),pe()}catch(s){y({title:"Erro",description:"Ocorreu um erro ao salvar o tópico",variant:"destructive"})}finally{H(!1)}})),className:"space-y-4",children:[e.jsx(I,{value:"basic",children:e.jsx(q,{className:"h-[70dvh] pr-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{control:J.control,name:"name",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Nome"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"description",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Descrição"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"slug",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Slug"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"icon",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Ícone"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"image_url",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Imagem"}),e.jsx(u,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{type:"file",accept:"image/*",onChange:se,className:"hidden",id:"image-upload"}),e.jsxs("label",{htmlFor:"image-upload",className:"flex items-center gap-2 px-4 py-2 border rounded-md cursor-pointer hover:bg-gray-50",children:[R?e.jsx(f,{className:"w-4 h-4 animate-spin"}):e.jsx(Pe,{className:"w-4 h-4"}),s.value?"Trocar imagem":"Adicionar imagem"]}),s.value&&e.jsx("img",{src:s.value,alt:"Preview",className:"w-16 h-16 object-cover rounded-md"}),e.jsx(h,{type:"hidden",...s})]})}),e.jsx(p,{})]})}),e.jsxs("div",{className:"border-t pt-4 space-y-4",children:[e.jsx("h4",{className:"font-medium text-sm text-gray-700 dark:text-gray-300",children:"Localização"}),e.jsx(d,{control:J.control,name:"parent_id",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Subcategoria Pai (opcional)"}),e.jsxs(V,{onValueChange:s.onChange,value:s.value,children:[e.jsx(u,{children:e.jsx(L,{children:e.jsx(O,{placeholder:"Selecione uma subcategoria pai"})})}),e.jsxs(F,{children:[e.jsx(T,{value:"none",children:"Nenhuma (tópico direto na categoria)"}),D?.map((s=>e.jsxs(T,{value:s.id,children:["📁 ",s.name]},s.id)))]})]}),e.jsx(U,{children:"Se selecionado, este tópico ficará dentro da subcategoria"}),e.jsx(p,{})]})})]})]})})}),e.jsx(I,{value:"summary",children:e.jsx(q,{className:"h-[70dvh] pr-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{control:J.control,name:"summary_title",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Título do Resumo"}),e.jsx(u,{children:e.jsx(h,{...s})}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"format_type",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Formato do Conteúdo"}),e.jsxs(V,{onValueChange:s.onChange,defaultValue:s.value,value:s.value,children:[e.jsx(u,{children:e.jsx(L,{children:e.jsx(O,{placeholder:"Selecione o formato"})})}),e.jsxs(F,{children:[e.jsx(T,{value:"standard",children:"Padrão (Editor Completo)"}),e.jsx(T,{value:"simple",children:"Simples (Marcadores ##. e ##;)"}),e.jsx(T,{value:"optimized",children:"Otimizado (Condutas + Tratamento)"})]})]}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"published",render:({field:s})=>e.jsxs(l,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{className:"text-base",children:"Publicar Resumo"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Tornar este resumo visível na listagem pública"})]}),e.jsx(u,{children:e.jsx("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300"})})]})}),"optimized"===J.watch("format_type")?e.jsxs(e.Fragment,{children:[e.jsx(d,{control:J.control,name:"conducts_content",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Conteúdo de Condutas e Manejos"}),e.jsx(u,{children:e.jsxs("div",{children:[e.jsx("textarea",{...s,className:"min-h-[300px] w-full rounded-md border border-input bg-white dark:bg-slate-800 px-3 py-2 text-sm text-foreground dark:text-gray-100 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700",placeholder:"Digite o conteúdo de condutas e manejos em markdown..."}),e.jsxs("div",{className:"border-t p-2 flex gap-2 bg-gray-50 dark:bg-gray-800 rounded-b-md",children:[e.jsx(Ke,{onImageInserted:e=>ae(e,"conducts_content"),children:e.jsxs(j,{variant:"outline",size:"sm",type:"button",children:[e.jsx(Pe,{className:"w-4 h-4 mr-2"}),"Adicionar Imagem"]})}),e.jsxs(j,{variant:"outline",size:"sm",type:"button",onClick:()=>re("conducts_content"),disabled:Y,children:[Y?e.jsx(f,{className:"w-4 h-4 mr-2 animate-spin"}):e.jsx(Oe,{className:"w-4 h-4 mr-2"}),Y?"Formatando...":"Formatar com IA"]})]})]})}),e.jsx(p,{})]})}),e.jsx(d,{control:J.control,name:"has_treatment",render:({field:s})=>e.jsxs(l,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{className:"text-base",children:"Incluir Tratamento"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Ativar seção de tratamento para este tópico"})]}),e.jsx(u,{children:e.jsx("input",{type:"checkbox",checked:s.value,onChange:s.onChange,className:"h-4 w-4 rounded border-gray-300"})})]})}),J.watch("has_treatment")&&e.jsx(d,{control:J.control,name:"treatment_content",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Conteúdo de Tratamento"}),e.jsx(u,{children:e.jsxs("div",{children:[e.jsx("textarea",{...s,className:"min-h-[300px] w-full rounded-md border border-input bg-white dark:bg-slate-800 px-3 py-2 text-sm text-foreground dark:text-gray-100 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700",placeholder:"Digite o conteúdo de tratamento em markdown..."}),e.jsxs("div",{className:"border-t p-2 flex gap-2 bg-gray-50 dark:bg-gray-800 rounded-b-md",children:[e.jsx(Ke,{onImageInserted:e=>ae(e,"treatment_content"),children:e.jsxs(j,{variant:"outline",size:"sm",type:"button",children:[e.jsx(Pe,{className:"w-4 h-4 mr-2"}),"Adicionar Imagem"]})}),e.jsxs(j,{variant:"outline",size:"sm",type:"button",onClick:()=>re("treatment_content"),disabled:Y,children:[Y?e.jsx(f,{className:"w-4 h-4 mr-2 animate-spin"}):e.jsx(Oe,{className:"w-4 h-4 mr-2"}),Y?"Formatando...":"Formatar com IA"]})]})]})}),e.jsx(p,{})]})})]}):e.jsx(d,{control:J.control,name:"summary_content",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Conteúdo do Resumo"}),e.jsx(u,{children:e.jsxs("div",{className:"min-h-[400px] rounded-md border-[0.5px]",children:[e.jsx(He,{editor:ee}),e.jsxs("div",{className:"p-3",children:[e.jsx(Be,{onChange:ie}),e.jsx(he,{editor:ee,className:"min-h-[350px] prose max-w-none mt-4"})]}),e.jsx("input",{type:"hidden",...s}),e.jsxs("div",{className:"border-t p-2 flex gap-2",children:[e.jsxs("label",{htmlFor:"image-upload-content",className:"flex items-center gap-2 px-3 py-2 border rounded-md cursor-pointer hover:bg-gray-50 text-sm",children:[R?e.jsx(f,{className:"w-4 h-4 animate-spin"}):e.jsx(A,{className:"w-4 h-4"}),"Imagem simples"]}),e.jsx("input",{type:"file",accept:"image/*",onChange:se,className:"hidden",id:"image-upload-content"}),e.jsx(Ke,{onImageInserted:te,children:e.jsxs(j,{variant:"outline",size:"sm",type:"button",children:[e.jsx(Pe,{className:"w-4 h-4 mr-2"}),"Imagem com título"]})})]})]})}),e.jsx(p,{})]})})]})})}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx(j,{type:"submit",disabled:Q,children:Q?e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):t?"Salvar":"Criar"})})]})})]})]})})}const We=ee({name:te().min(1,"Nome é obrigatório"),description:te().optional(),slug:te().optional(),icon:te().optional(),category_id:te().uuid("Categoria inválida")});function Ye({subcategory:t,categoryId:x,onOpenChange:g,open:f}){const v=K(),{user:_}=k(),w=Z({resolver:C(We),defaultValues:{name:"",description:"",slug:"",icon:"",category_id:x}});s.useEffect((()=>{f&&(t?w.reset({name:t.name,description:t.description||"",slug:t.slug,icon:t.icon||"",category_id:t.category_id}):w.reset({name:"",description:"",slug:"",icon:"",category_id:x}))}),[f,t,x,w]);const N=()=>{w.reset(),g(!1)};return e.jsx(a,{open:f,onOpenChange:g,children:e.jsxs(r,{className:"max-w-2xl",children:[e.jsxs(i,{children:[e.jsx(o,{children:t?.id?"Editar Subcategoria":"Nova Subcategoria"}),e.jsx(n,{children:t?.id?"Edite os dados da subcategoria":"Crie uma nova subcategoria"})]}),e.jsx(c,{...w,children:e.jsxs("form",{onSubmit:w.handleSubmit((async e=>{try{if(!_)return void y({title:"Erro",description:"Usuário não autenticado",variant:"destructive"});const s=e.slug||e.name.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),a={name:e.name,description:e.description,slug:s,icon:e.icon,category_id:e.category_id,parent_id:null,is_subcategory:!0};let r;if(r=t?.id?await b.from("pedbook_conducts_topics").update(a).eq("id",t.id).select().single():await b.from("pedbook_conducts_topics").insert(a).select().single(),r.error)throw r.error;v.invalidateQueries({queryKey:["conducts-topics"]}),y({title:"Sucesso",description:t?.id?"Subcategoria atualizada com sucesso!":"Subcategoria criada com sucesso!"}),N()}catch(s){y({title:"Erro",description:"Ocorreu um erro ao salvar a subcategoria",variant:"destructive"})}})),className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{control:w.control,name:"name",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Nome"}),e.jsx(u,{children:e.jsx(h,{...s,placeholder:"Ex: Doenças Exantemáticas"})}),e.jsx(p,{})]})}),e.jsx(d,{control:w.control,name:"description",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Descrição (opcional)"}),e.jsx(u,{children:e.jsx(h,{...s,placeholder:"Breve descrição da subcategoria"})}),e.jsx(p,{})]})}),e.jsx(d,{control:w.control,name:"slug",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Slug (opcional)"}),e.jsx(u,{children:e.jsx(h,{...s,placeholder:"doencas-exantematicas"})}),e.jsx(p,{})]})}),e.jsx(d,{control:w.control,name:"icon",render:({field:s})=>e.jsxs(l,{children:[e.jsx(m,{children:"Ícone (opcional)"}),e.jsx(u,{children:e.jsx(h,{...s,placeholder:"🦠"})}),e.jsx(p,{})]})})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx(j,{type:"button",variant:"outline",onClick:N,children:"Cancelar"}),e.jsxs(j,{type:"submit",children:[t?.id?"Atualizar":"Criar"," Subcategoria"]})]})]})})]})})}const Xe=()=>{const[t,a]=s.useState(!1),[r,i]=s.useState(!1),[o,n]=s.useState(),[c,d]=s.useState(),[l,m]=s.useState(),[u,h]=s.useState(),[p,x]=s.useState(""),[g,f]=s.useState(!1),[v,_]=s.useState(),[w,k]=s.useState(""),C=K(),{data:S,isLoading:z}=$({queryKey:["conducts-categories"],queryFn:async()=>{const{data:e,error:s}=await b.from("pedbook_conducts_categories").select("*").order("display_order",{ascending:!0,nullsFirst:!0});if(s)throw s;return e}}),{data:E,isLoading:I}=$({queryKey:["conducts-topics"],queryFn:async()=>{const{data:e,error:s}=await b.from("pedbook_conducts_topics").select("\n          *,\n          parent_topic:parent_id(id, name, is_subcategory),\n          child_topics:pedbook_conducts_topics!parent_id(id, name, is_subcategory, display_order)\n        ").order("display_order",{ascending:!0,nullsFirst:!0});if(s)throw s;return e}}),q=e=>{d(e),x(e.category_id),i(!0)},V=(e,s)=>{d(s?{id:"",name:"",description:null,icon:null,slug:"",category_id:e,parent_id:s,is_subcategory:!1,display_order:null}:void 0),x(e),i(!0)},L=(e,s)=>{if(!e)return[];const t=e.filter((e=>e.category_id===s)),a=t.filter((e=>e.is_subcategory&&!e.parent_id)),r=t.filter((e=>!e.is_subcategory&&!e.parent_id));return[...a.map((e=>({...e,children:t.filter((s=>s.parent_id===e.id))}))),...r].sort(((e,s)=>(e.display_order||0)-(s.display_order||0)))};return z||I?e.jsx("div",{className:"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-slate-900",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"})}):e.jsxs("div",{className:"container py-8 bg-gray-50 dark:bg-slate-900",children:[e.jsxs(N,{className:"dark:bg-slate-800 dark:border-slate-700",children:[e.jsx(D,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx(R,{className:"dark:text-white",children:"Manejos e Condutas"}),e.jsx(P,{className:"dark:text-gray-300",children:"Gerencie categorias e conteúdo de manejos e condutas"})]}),e.jsxs(j,{onClick:()=>{n(void 0),a(!0)},children:[e.jsx(Se,{className:"w-4 h-4 mr-2"}),"Nova Categoria"]})]})}),e.jsx(M,{children:e.jsx("div",{className:"grid gap-4",children:S?.map((s=>e.jsx(N,{className:"dark:bg-slate-800/70 dark:border-slate-700",children:e.jsx(M,{className:"p-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold dark:text-white",children:s.name}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground dark:text-gray-400",children:s.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(j,{variant:"outline",size:"icon",onClick:()=>(e=>{n(e),a(!0)})(s),className:"dark:border-slate-600 dark:hover:bg-slate-700",children:e.jsx(Ae,{className:"w-4 h-4"})}),e.jsx(j,{variant:"outline",size:"icon",onClick:()=>m(s),className:"dark:border-slate-600 dark:hover:bg-slate-700",children:e.jsx(De,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{className:"pl-4 space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-sm font-medium dark:text-gray-200",children:"Conteúdo"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>{return e=s.id,_(void 0),k(e),void f(!0);var e},className:"dark:hover:bg-slate-700",children:[e.jsx(Se,{className:"w-4 h-4 mr-2"}),"Nova Subcategoria"]}),e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>V(s.id),className:"dark:hover:bg-slate-700",children:[e.jsx(Se,{className:"w-4 h-4 mr-2"}),"Novo Tópico"]})]})]}),e.jsx("div",{className:"space-y-2",children:L(E||[],s.id).map((t=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center p-2 rounded-lg border "+(t.is_subcategory?"dark:border-blue-600 dark:bg-blue-900/20 border-blue-200 bg-blue-50":"dark:border-slate-700 dark:bg-slate-800"),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[t.is_subcategory&&e.jsx("span",{className:"text-blue-500 text-sm",children:"📁"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium dark:text-white",children:t.name}),t.description&&e.jsx("p",{className:"text-sm text-muted-foreground dark:text-gray-400",children:t.description}),t.is_subcategory&&e.jsx("span",{className:"text-xs text-blue-600 dark:text-blue-400",children:"Subcategoria"})]})]}),e.jsxs("div",{className:"flex gap-2",children:[t.is_subcategory&&e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>V(s.id,t.id),className:"dark:hover:bg-slate-700",children:[e.jsx(Se,{className:"w-3 h-3 mr-1"}),"Tópico"]}),e.jsx(j,{variant:"ghost",size:"icon",onClick:()=>{return t.is_subcategory?(_(e=t),k(e.category_id),void f(!0)):q(t);var e},className:"dark:hover:bg-slate-700",children:e.jsx(Ae,{className:"w-4 h-4"})}),e.jsx(j,{variant:"ghost",size:"icon",onClick:()=>h(t),className:"dark:hover:bg-slate-700",children:e.jsx(De,{className:"w-4 h-4"})})]})]}),t.is_subcategory&&t.children?.length>0&&e.jsx("div",{className:"ml-6 space-y-2",children:t.children.map((s=>e.jsxs("div",{className:"flex justify-between items-center p-2 rounded-lg border dark:border-slate-700 dark:bg-slate-800/50",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"└─"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium dark:text-white",children:s.name}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground dark:text-gray-400",children:s.description})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(j,{variant:"ghost",size:"icon",onClick:()=>q(s),className:"dark:hover:bg-slate-700",children:e.jsx(Ae,{className:"w-4 h-4"})}),e.jsx(j,{variant:"ghost",size:"icon",onClick:()=>h(s),className:"dark:hover:bg-slate-700",children:e.jsx(De,{className:"w-4 h-4"})})]})]},s.id)))})]},t.id)))})]})]})})},s.id)))})})]}),e.jsx(Me,{category:o,open:t,onOpenChange:a}),e.jsx(Ge,{topic:c,categoryId:p,open:r,onOpenChange:i}),e.jsx(Ye,{subcategory:v,categoryId:w,open:g,onOpenChange:f}),e.jsx(B,{open:!!l,onOpenChange:()=>m(void 0),children:e.jsxs(Q,{className:"dark:bg-slate-800 dark:border-slate-700",children:[e.jsxs(H,{children:[e.jsx(G,{className:"dark:text-white",children:"Você tem certeza?"}),e.jsx(W,{className:"dark:text-gray-300",children:"Esta ação não pode ser desfeita. Isso excluirá permanentemente a categoria e todos os seus tópicos."})]}),e.jsxs(Y,{children:[e.jsx(X,{className:"dark:bg-slate-700 dark:text-white dark:hover:bg-slate-600",children:"Cancelar"}),e.jsx(J,{onClick:()=>l&&(async e=>{try{const{data:s,error:t}=await b.from("pedbook_conducts_topics").select("id").eq("category_id",e.id);if(t)throw t;if(s&&s.length>0){const t=s.map((e=>e.id)),{error:a}=await b.from("pedbook_conducts_optimized").delete().in("topic_id",t);if(a)throw a;const{error:r}=await b.from("pedbook_conducts_summaries").delete().in("topic_id",t);if(r)throw r;const{error:i}=await b.from("pedbook_conducts_content").delete().in("topic_id",t);if(i)throw i;const{error:o}=await b.from("pedbook_conducts_feedback").delete().in("summary_id",t);if(o)throw o;const{error:n}=await b.from("pedbook_conducts_topics").delete().eq("category_id",e.id);if(n)throw n}const{error:a}=await b.from("pedbook_conducts_categories").delete().eq("id",e.id);if(a)throw a;y({title:"Categoria removida com sucesso"}),C.invalidateQueries({queryKey:["conducts-categories"]}),m(void 0)}catch(s){y({title:"Erro ao remover categoria",description:s.message||"Ocorreu um erro ao remover a categoria",variant:"destructive"})}})(l),children:"Continuar"})]})]})}),e.jsx(B,{open:!!u,onOpenChange:()=>h(void 0),children:e.jsxs(Q,{className:"dark:bg-slate-800 dark:border-slate-700",children:[e.jsxs(H,{children:[e.jsx(G,{className:"dark:text-white",children:"Você tem certeza?"}),e.jsx(W,{className:"dark:text-gray-300",children:"Esta ação não pode ser desfeita. Isso excluirá permanentemente o tópico e todo seu conteúdo."})]}),e.jsxs(Y,{children:[e.jsx(X,{className:"dark:bg-slate-700 dark:text-white dark:hover:bg-slate-600",children:"Cancelar"}),e.jsx(J,{onClick:()=>u&&(async e=>{try{const{error:s}=await b.from("pedbook_conducts_optimized").delete().eq("topic_id",e.id);if(s)throw s;const{error:t}=await b.from("pedbook_conducts_summaries").delete().eq("topic_id",e.id);if(t)throw t;const{error:a}=await b.from("pedbook_conducts_content").delete().eq("topic_id",e.id);if(a)throw a;const{error:r}=await b.from("pedbook_conducts_feedback").delete().eq("summary_id",e.id);if(r)throw r;const{error:i}=await b.from("pedbook_conducts_topics").delete().eq("id",e.id);if(i)throw i;y({title:"Tópico removido com sucesso"}),h(void 0),C.invalidateQueries({queryKey:["conducts-topics"]})}catch(s){y({title:"Erro",description:"Ocorreu um erro ao remover o tópico",variant:"destructive"})}})(u),children:"Continuar"})]})]})})]})};export{Xe as default};
