import{r as e,$ as t}from"./critical-DVX9Inzy.js";
/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},n.apply(this,arguments)}var a,r;(r=a||(a={})).Pop="POP",r.Push="PUSH",r.Replace="REPLACE";const o="popstate";function i(e){return void 0===e&&(e={}),function(e,t,r,i){void 0===i&&(i={});let{window:s=document.defaultView,v5Compat:p=!1}=i,d=s.history,f=a.Pop,m=null,v=g();function g(){return(d.state||{idx:null}).idx}function y(){f=a.Pop;let e=g(),t=null==e?null:e-v;v=e,m&&m({action:f,location:b.location,delta:t})}function x(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,n="string"==typeof e?e:h(e);return n=n.replace(/ $/,"%20"),l(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==v&&(v=0,d.replaceState(n({},d.state,{idx:v}),""));let b={get action(){return f},get location(){return function(e,t){let{pathname:n,search:a,hash:r}=e.location;return c("",{pathname:n,search:a,hash:r},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}(s,d)},listen(e){if(m)throw new Error("A history only accepts one active listener");return s.addEventListener(o,y),m=e,()=>{s.removeEventListener(o,y),m=null}},createHref:e=>function(e,t){return"string"==typeof t?t:h(t)}(0,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){f=a.Push;let n=c(b.location,e,t);v=g()+1;let r=u(n,v),o=b.createHref(n);try{d.pushState(r,"",o)}catch(i){if(i instanceof DOMException&&"DataCloneError"===i.name)throw i;s.location.assign(o)}p&&m&&m({action:f,location:b.location,delta:1})},replace:function(e,t){f=a.Replace;let n=c(b.location,e,t);v=g();let r=u(n,v),o=b.createHref(n);d.replaceState(r,"",o),p&&m&&m({action:f,location:b.location,delta:0})},go:e=>d.go(e)};return b}(0,0,0,e)}function l(e,t){if(!1===e||null==e)throw new Error(t)}function s(e,t){if(!e)try{throw new Error(t)}catch(n){}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function c(e,t,a,r){return void 0===a&&(a=null),n({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?p(t):t,{state:a,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function p(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}var d,f;function m(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let a=O(("string"==typeof t?p(t):t).pathname||"/",n);if(null==a)return null;let r=v(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]))?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(r);let o=null;for(let i=0;null==o&&i<r.length;++i){let e=U(a);o=P(r[i],e)}return o}(e,t,n)}function v(e,t,n,a){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===a&&(a="");let r=(e,r,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:r,route:e};i.relativePath.startsWith("/")&&(l(i.relativePath.startsWith(a),'Absolute route path "'+i.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(a.length));let s=N([a,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(l(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),v(e.children,t,u,s)),(null!=e.path||e.index)&&t.push({path:s,score:R(s,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let a of g(e.path))r(e,t,a);else r(e,t)})),t}function g(e){let t=e.split("/");if(0===t.length)return[];let[n,...a]=t,r=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===a.length)return r?[o,""]:[o];let i=g(a.join("/")),l=[];return l.push(...i.map((e=>""===e?o:[o,e].join("/")))),r&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}(f=d||(d={})).data="data",f.deferred="deferred",f.redirect="redirect",f.error="error";const y=/^:[\w-]+$/,x=3,b=2,C=1,w=10,E=-2,S=e=>"*"===e;function R(e,t){let n=e.split("/"),a=n.length;return n.some(S)&&(a+=E),t&&(a+=b),n.filter((e=>!S(e))).reduce(((e,t)=>e+(y.test(t)?x:""===t?C:w)),a)}function P(e,t,n){let{routesMeta:a}=e,r={},o="/",i=[];for(let l=0;l<a.length;++l){let e=a[l],n=l===a.length-1,s="/"===o?t:t.slice(o.length)||"/",u=L({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},s),c=e.route;if(!u)return null;Object.assign(r,u.params),i.push({params:r,pathname:N([o,u.pathname]),pathnameBase:j(N([o,u.pathnameBase])),route:c}),"/"!==u.pathnameBase&&(o=N([o,u.pathnameBase]))}return i}function L(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=function(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!0),s("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(a.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(a.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))"),[new RegExp(r,t?void 0:"i"),a]}(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let o=r[0],i=o.replace(/(.)\/+$/,"$1"),l=r.slice(1);return{params:a.reduce(((e,t,n)=>{let{paramName:a,isOptional:r}=t;if("*"===a){let e=l[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[a]=r&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function U(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return s(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function O(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&"/"!==a?null:e.slice(n)||"/"}function k(e,t,n,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function B(e,t){let n=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function T(e,t,a,r){let o;void 0===r&&(r=!1),"string"==typeof e?o=p(e):(o=n({},e),l(!o.pathname||!o.pathname.includes("?"),k("?","pathname","search",o)),l(!o.pathname||!o.pathname.includes("#"),k("#","pathname","hash",o)),l(!o.search||!o.search.includes("#"),k("#","search","hash",o)));let i,s=""===e||""===o.pathname,u=s?"/":o.pathname;if(null==u)i=a;else{let e=t.length-1;if(!r&&u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}i=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:n,search:a="",hash:r=""}="string"==typeof e?p(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:W(a),hash:D(r)}}(o,i),h=u&&"/"!==u&&u.endsWith("/"),d=(s||"."===u)&&a.endsWith("/");return c.pathname.endsWith("/")||!h&&!d||(c.pathname+="/"),c}const N=e=>e.join("/").replace(/\/\/+/g,"/"),j=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),W=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",D=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"",$=["post","put","patch","delete"];new Set($);const F=["get",...$];
/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},A.apply(this,arguments)}new Set(F);const M=e.createContext(null),_=e.createContext(null),I=e.createContext(null),H=e.createContext(null),J=e.createContext({outlet:null,matches:[],isDataRoute:!1}),z=e.createContext(null);function V(){return null!=e.useContext(H)}function K(){return V()||l(!1),e.useContext(H).location}function q(t){e.useContext(I).static||e.useLayoutEffect(t)}function G(){let{isDataRoute:t}=e.useContext(J);return t?function(){let{router:t}=function(){let t=e.useContext(M);return t||l(!1),t}(ae.UseNavigateStable),n=oe(re.UseNavigateStable),a=e.useRef(!1);return q((()=>{a.current=!0})),e.useCallback((function(e,r){void 0===r&&(r={}),a.current&&("number"==typeof e?t.navigate(e):t.navigate(e,A({fromRouteId:n},r)))}),[t,n])}():function(){V()||l(!1);let t=e.useContext(M),{basename:n,future:a,navigator:r}=e.useContext(I),{matches:o}=e.useContext(J),{pathname:i}=K(),s=JSON.stringify(B(o,a.v7_relativeSplatPath)),u=e.useRef(!1);return q((()=>{u.current=!0})),e.useCallback((function(e,a){if(void 0===a&&(a={}),!u.current)return;if("number"==typeof e)return void r.go(e);let o=T(e,JSON.parse(s),i,"path"===a.relative);null==t&&"/"!==n&&(o.pathname="/"===o.pathname?n:N([n,o.pathname])),(a.replace?r.replace:r.push)(o,a.state,a)}),[n,r,s,i,t])}()}const Q=e.createContext(null);function X(){let{matches:t}=e.useContext(J),n=t[t.length-1];return n?n.params:{}}function Y(t,n){let{relative:a}=void 0===n?{}:n,{future:r}=e.useContext(I),{matches:o}=e.useContext(J),{pathname:i}=K(),l=JSON.stringify(B(o,r.v7_relativeSplatPath));return e.useMemo((()=>T(t,JSON.parse(l),i,"path"===a)),[t,l,i,a])}function Z(){let t=function(){var t;let n=e.useContext(z),a=function(){let t=e.useContext(_);return t||l(!1),t}(),r=oe();return void 0!==n?n:null==(t=a.errors)?void 0:t[r]}(),n=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),a=t instanceof Error?t.stack:null;return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},n),a?e.createElement("pre",{style:{padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"}},a):null,null)}const ee=e.createElement(Z,null);class te extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?e.createElement(J.Provider,{value:this.props.routeContext},e.createElement(z.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ne(t){let{routeContext:n,match:a,children:r}=t,o=e.useContext(M);return o&&o.static&&o.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=a.route.id),e.createElement(J.Provider,{value:n},r)}var ae=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ae||{}),re=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(re||{});function oe(t){let n=function(){let t=e.useContext(J);return t||l(!1),t}(),a=n.matches[n.matches.length-1];return a.route.id||l(!1),a.route.id}const ie={};function le(t){let{to:n,replace:a,state:r,relative:o}=t;V()||l(!1);let{future:i,static:s}=e.useContext(I),{matches:u}=e.useContext(J),{pathname:c}=K(),h=G(),p=T(n,B(u,i.v7_relativeSplatPath),c,"path"===o),d=JSON.stringify(p);return e.useEffect((()=>h(JSON.parse(d),{replace:a,state:r,relative:o})),[h,d,o,a,r]),null}function se(t){return function(t){let n=e.useContext(J).outlet;return n?e.createElement(Q.Provider,{value:t},n):n}(t.context)}function ue(e){l(!1)}function ce(t){let{basename:n="/",children:r=null,location:o,navigationType:i=a.Pop,navigator:s,static:u=!1,future:c}=t;V()&&l(!1);let h=n.replace(/^\/*/,"/"),d=e.useMemo((()=>({basename:h,navigator:s,static:u,future:A({v7_relativeSplatPath:!1},c)})),[h,c,s,u]);"string"==typeof o&&(o=p(o));let{pathname:f="/",search:m="",hash:v="",state:g=null,key:y="default"}=o,x=e.useMemo((()=>{let e=O(f,h);return null==e?null:{location:{pathname:e,search:m,hash:v,state:g,key:y},navigationType:i}}),[h,f,m,v,g,y,i]);return null==x?null:e.createElement(I.Provider,{value:d},e.createElement(H.Provider,{children:r,value:x}))}function he(t){let{children:n,location:r}=t;return function(t,n){V()||l(!1);let{navigator:r}=e.useContext(I),{matches:o}=e.useContext(J),i=o[o.length-1],s=i?i.params:{};!i||i.pathname;let u=i?i.pathnameBase:"/";i&&i.route;let c,h=K();if(n){var d;let e="string"==typeof n?p(n):n;"/"===u||(null==(d=e.pathname)?void 0:d.startsWith(u))||l(!1),c=e}else c=h;let f=c.pathname||"/",v=f;if("/"!==u){let e=u.replace(/^\//,"").split("/");v="/"+f.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=m(t,{pathname:v}),y=function(t,n,a,r){var o;if(void 0===n&&(n=[]),void 0===a&&(a=null),void 0===r&&(r=null),null==t){var i;if(!a)return null;if(a.errors)t=a.matches;else{if(!(null!=(i=r)&&i.v7_partialHydration&&0===n.length&&!a.initialized&&a.matches.length>0))return null;t=a.matches}}let s=t,u=null==(o=a)?void 0:o.errors;if(null!=u){let e=s.findIndex((e=>e.route.id&&void 0!==(null==u?void 0:u[e.route.id])));e>=0||l(!1),s=s.slice(0,Math.min(s.length,e+1))}let c=!1,h=-1;if(a&&r&&r.v7_partialHydration)for(let e=0;e<s.length;e++){let t=s[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(h=e),t.route.id){let{loaderData:e,errors:n}=a,r=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||r){c=!0,s=h>=0?s.slice(0,h+1):[s[0]];break}}}return s.reduceRight(((t,r,o)=>{let i,l=!1,p=null,d=null;a&&(i=u&&r.route.id?u[r.route.id]:void 0,p=r.route.errorElement||ee,c&&(h<0&&0===o?(ie["route-fallback"]||(ie["route-fallback"]=!0),l=!0,d=null):h===o&&(l=!0,d=r.route.hydrateFallbackElement||null)));let f=n.concat(s.slice(0,o+1)),m=()=>{let n;return n=i?p:l?d:r.route.Component?e.createElement(r.route.Component,null):r.route.element?r.route.element:t,e.createElement(ne,{match:r,routeContext:{outlet:t,matches:f,isDataRoute:null!=a},children:n})};return a&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?e.createElement(te,{location:a.location,revalidation:a.revalidation,component:p,error:i,children:m(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):m()}),null)}(g&&g.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:N([u,r.encodeLocation?r.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:N([u,r.encodeLocation?r.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),o,void 0,void 0);return n&&y?e.createElement(H.Provider,{value:{location:A({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:a.Pop}},y):y}(pe(n),r)}function pe(t,n){void 0===n&&(n=[]);let a=[];return e.Children.forEach(t,((t,r)=>{if(!e.isValidElement(t))return;let o=[...n,r];if(t.type===e.Fragment)return void a.push.apply(a,pe(t.props.children,o));t.type!==ue&&l(!1),t.props.index&&t.props.children&&l(!1);let i={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(i.children=pe(t.props.children,o)),a.push(i)})),a}
/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},de.apply(this,arguments)}function fe(e,t){if(null==e)return{};var n,a,r={},o=Object.keys(e);for(a=0;a<o.length;a++)n=o[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}new Promise((()=>{}));const me=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ve=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"];try{window.__reactRouterVersion="6"}catch(Ue){}const ge=e.createContext({isTransitioning:!1}),ye=t.startTransition;function xe(t){let{basename:n,children:a,future:r,window:o}=t,l=e.useRef();null==l.current&&(l.current=i({window:o,v5Compat:!0}));let s=l.current,[u,c]=e.useState({action:s.action,location:s.location}),{v7_startTransition:h}=r||{},p=e.useCallback((e=>{h&&ye?ye((()=>c(e))):c(e)}),[c,h]);return e.useLayoutEffect((()=>s.listen(p)),[s,p]),e.createElement(ce,{basename:n,children:a,location:u.location,navigationType:u.action,navigator:s,future:r})}const be="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Ce=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,we=e.forwardRef((function(t,n){let a,{onClick:r,relative:o,reloadDocument:i,replace:s,state:u,target:c,to:p,preventScrollReset:d,viewTransition:f}=t,m=fe(t,me),{basename:v}=e.useContext(I),g=!1;if("string"==typeof p&&Ce.test(p)&&(a=p,be))try{let e=new URL(window.location.href),t=p.startsWith("//")?new URL(e.protocol+p):new URL(p),n=O(t.pathname,v);t.origin===e.origin&&null!=n?p=n+t.search+t.hash:g=!0}catch(Ue){}let y=function(t,n){let{relative:a}=void 0===n?{}:n;V()||l(!1);let{basename:r,navigator:o}=e.useContext(I),{hash:i,pathname:s,search:u}=Y(t,{relative:a}),c=s;return"/"!==r&&(c="/"===s?r:N([r,s])),o.createHref({pathname:c,search:u,hash:i})}(p,{relative:o}),x=function(t,n){let{target:a,replace:r,state:o,preventScrollReset:i,relative:l,viewTransition:s}=void 0===n?{}:n,u=G(),c=K(),p=Y(t,{relative:l});return e.useCallback((e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,a)){e.preventDefault();let n=void 0!==r?r:h(c)===h(p);u(t,{replace:n,state:o,preventScrollReset:i,relative:l,viewTransition:s})}}),[c,u,p,r,o,a,t,i,l,s])}(p,{replace:s,state:u,target:c,preventScrollReset:d,relative:o,viewTransition:f});return e.createElement("a",de({},m,{href:a||y,onClick:g||i?r:function(e){r&&r(e),e.defaultPrevented||x(e)},ref:n,target:c}))})),Ee=e.forwardRef((function(t,n){let{"aria-current":a="page",caseSensitive:r=!1,className:o="",end:i=!1,style:s,to:u,viewTransition:c,children:h}=t,p=fe(t,ve),d=Y(u,{relative:p.relative}),f=K(),m=e.useContext(_),{navigator:v,basename:g}=e.useContext(I),y=null!=m&&function(t,n){void 0===n&&(n={});let a=e.useContext(ge);null==a&&l(!1);let{basename:r}=function(){let t=e.useContext(M);return t||l(!1),t}(Se.useViewTransitionState),o=Y(t,{relative:n.relative});if(!a.isTransitioning)return!1;let i=O(a.currentLocation.pathname,r)||a.currentLocation.pathname,s=O(a.nextLocation.pathname,r)||a.nextLocation.pathname;return null!=L(o.pathname,s)||null!=L(o.pathname,i)}(d)&&!0===c,x=v.encodeLocation?v.encodeLocation(d).pathname:d.pathname,b=f.pathname,C=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;r||(b=b.toLowerCase(),C=C?C.toLowerCase():null,x=x.toLowerCase()),C&&g&&(C=O(C,g)||C);const w="/"!==x&&x.endsWith("/")?x.length-1:x.length;let E,S=b===x||!i&&b.startsWith(x)&&"/"===b.charAt(w),R=null!=C&&(C===x||!i&&C.startsWith(x)&&"/"===C.charAt(x.length)),P={isActive:S,isPending:R,isTransitioning:y},U=S?a:void 0;E="function"==typeof o?o(P):[o,S?"active":null,R?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let k="function"==typeof s?s(P):s;return e.createElement(we,de({},p,{"aria-current":U,className:E,ref:n,style:k,to:u,viewTransition:c}),"function"==typeof h?h(P):h)}));var Se,Re,Pe,Le;(Re=Se||(Se={})).UseScrollRestoration="useScrollRestoration",Re.UseSubmit="useSubmit",Re.UseSubmitFetcher="useSubmitFetcher",Re.UseFetcher="useFetcher",Re.useViewTransitionState="useViewTransitionState",(Le=Pe||(Pe={})).UseFetcher="useFetcher",Le.UseFetchers="useFetchers",Le.UseScrollRestoration="useScrollRestoration";export{xe as B,we as L,le as N,se as O,he as R,G as a,ue as b,X as c,Ee as d,K as u};
