import{j as e}from"./radix-core-6kBL75b5.js";import{r as a,b as r}from"./critical-DVX9Inzy.js";import{L as s}from"./router-BAzpOxbo.js";import{j as t,ak as i,aq as d,Z as n,al as l,X as o,D as c,aK as m,B as x,e as g,f as u,g as h,z as b,ad as p,a6 as j,L as v,P as y,ao as f,ae as N,af as k,ag as w,ah as C,aj as S,u as E,d as L,U as M,T as $,aD as T,s as q,a9 as P,ab as A,a8 as _}from"./index-CNG-Xj2g.js";import G from"./Footer-BgCSiPkf.js";import{A as I,b as D}from"./alert-BT_NObbd.js";import{S as F}from"./switch-DC7YtXgl.js";import{C as R}from"./circle-plus-CzupkUnX.js";import{H as z,A as O}from"./FeedbackTrigger-ik6vfZ65.js";import{T as B}from"./thumbs-up-DyNG_WLH.js";import{M as V}from"./meh-Di3s5ao1.js";import{T as H}from"./thumbs-down-TH6oKift.js";import{v as J}from"./v4-OjsI5tD8.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-ClgJ7H9i.js";import"./rocket-BEoGgNr2.js";import"./target-Dul0NbVV.js";import"./zap-C4mKju26.js";import"./book-open-EV5sJdXr.js";import"./star-BUSksJJE.js";import"./circle-help-BbvIlE64.js";const W=({interaction:a,isLast:r=!1})=>{if(!a.drugPair&&a.content)return e.jsx("div",{className:"text-gray-600 dark:text-gray-400",children:a.content});const s=(e=>{if(!e)return"";const a=/#{1,3}\s*Resumo/i;return a.test(e)?e.split(a)[0].trim():e})(a.recommendation);return e.jsx("div",{className:t("p-4 rounded-md",!r&&"mb-4",(()=>{switch(a.severity){case"Contraindicado":return"border-red-500 bg-red-50 dark:bg-red-950/30";case"Grave":return"border-purple-500 bg-purple-50 dark:bg-purple-950/30";case"Moderado":return"border-yellow-500 bg-yellow-50 dark:bg-yellow-950/30";case"Leve":case"Sem interação relevante":return"border-green-500 bg-green-50 dark:bg-green-950/30";default:return"border-blue-500 bg-blue-50 dark:bg-blue-950/30"}})(),"hover:shadow-md transition-all border-l-4"),children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-white/80 dark:bg-gray-800/40 p-3 rounded-md border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-0",children:[e.jsx("span",{className:"text-sm font-semibold text-gray-600 dark:text-gray-400",children:"Interação:"}),e.jsxs("div",{className:t("text-xs font-normal px-2 py-0.5 rounded-full flex items-center gap-1",(()=>{switch(a.severity){case"Contraindicado":return"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300";case"Grave":return"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300";case"Moderado":return"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300";case"Leve":case"Sem interação relevante":return"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300";default:return"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"}})()),children:[e.jsx("span",{className:"md:hidden",children:(()=>{switch(a.severity){case"Contraindicado":return e.jsx(o,{className:"h-5 w-5 text-red-600"});case"Grave":return e.jsx(l,{className:"h-5 w-5 text-purple-600"});case"Moderado":return e.jsx(n,{className:"h-5 w-5 text-yellow-600"});case"Leve":case"Sem interação relevante":return e.jsx(d,{className:"h-5 w-5 text-green-600"});default:return e.jsx(i,{className:"h-5 w-5 text-blue-600"})}})()}),e.jsx("span",{children:a.severity||"Desconhecido"})]})]}),e.jsx("div",{className:"font-semibold text-gray-800 dark:text-gray-200 break-words text-base",children:a.drugPair})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Mecanismo farmacológico"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm sm:text-base leading-relaxed",children:a.mechanism})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800/50 p-3 rounded-md border border-gray-100 dark:border-gray-800",children:[e.jsx("h5",{className:"font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Conduta clínica recomendada"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm sm:text-base leading-relaxed",children:s})]})]})})},Z=()=>e.jsxs(c,{children:[e.jsx(m,{asChild:!0,children:e.jsx(x,{variant:"link",className:"text-xs text-gray-500 dark:text-gray-400 h-auto p-0 underline",children:"O que significa cada nível?"})}),e.jsxs(g,{className:t("sm:max-w-md max-h-[85dvh] overflow-y-auto rounded-xl","scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent","w-[calc(100dvw-2rem)] max-w-[90dvw] sm:max-w-md"),children:[e.jsxs(u,{children:[e.jsx(h,{children:"Níveis de gravidade das interações"}),e.jsx(b,{children:"Entenda o significado de cada classificação de interação medicamentosa"})]}),e.jsxs("div",{className:"space-y-5 py-2 pr-1",children:[e.jsxs("div",{className:"flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",children:[e.jsx("div",{className:"bg-red-600 p-1.5 rounded-full mt-0.5 shadow-sm",children:e.jsx(o,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-red-700 dark:text-red-300",children:"Contraindicado"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Combinação não recomendada. O uso conjunto desses medicamentos pode resultar em riscos graves à saúde ou afetar significativamente a eficácia do tratamento."})]})]}),e.jsxs("div",{className:"flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",children:[e.jsx("div",{className:"bg-purple-600 p-1.5 rounded-full mt-0.5 shadow-sm",children:e.jsx(l,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-purple-700 dark:text-purple-300",children:"Grave"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Interação potencialmente perigosa que requer monitoramento rigoroso e pode exigir ajustes na dosagem ou horários de administração. Em alguns casos, alternativas terapêuticas devem ser consideradas."})]})]}),e.jsxs("div",{className:"flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",children:[e.jsx("div",{className:"bg-orange-500 p-1.5 rounded-full mt-0.5 shadow-sm",children:e.jsx(n,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-orange-700 dark:text-orange-300",children:"Moderado"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Interação clinicamente significativa que pode requerer monitoramento e possíveis ajustes de tratamento. Os benefícios geralmente superam os riscos com o acompanhamento adequado."})]})]}),e.jsxs("div",{className:"flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",children:[e.jsx("div",{className:"bg-green-500 p-1.5 rounded-full mt-0.5 shadow-sm",children:e.jsx(d,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-green-700 dark:text-green-300",children:"Leve/Sem interação"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Interação com mínimos efeitos clínicos ou sem interações relevantes documentadas. Geralmente não requer modificações no tratamento."})]})]}),e.jsxs("div",{className:"flex items-start gap-3 pt-3 mt-1 border-t border-gray-100 dark:border-gray-800 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",children:[e.jsx("div",{className:"bg-blue-500 p-1.5 rounded-full mt-0.5 shadow-sm",children:e.jsx(i,{className:"h-4 w-4 text-white"})}),e.jsx("div",{children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Como médico, é seu papel avaliar e discernir essas informações de acordo com a condição específica do paciente e o contexto clínico."})})]})]})]})]}),K=({searchTerm:s,setSearchTerm:i,isSearching:d,medicationOptions:n,handleAddMedication:l,selectedMedications:c,handleRemoveMedication:m})=>{const[x,g]=a.useState(!1);return r.useEffect((()=>{g(s.length>=2&&0===n.length&&!d)}),[s,n,d]),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-end mb-1",children:e.jsxs("div",{className:"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-xs px-2 py-0.5 rounded-full font-medium border border-purple-200 dark:border-purple-800 inline-flex items-center gap-1",children:[e.jsx("span",{className:"h-1.5 w-1.5 bg-purple-500 rounded-full animate-pulse"}),"BETA"]})}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"relative",children:[e.jsx(p,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(j,{type:"text",placeholder:"Digite o nome de um medicamento...",className:"pl-10 pr-10 transition-all focus:ring-2 focus:ring-primary/30",value:s,onChange:e=>i(e.target.value)}),d&&e.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2",children:e.jsx(v,{className:"h-4 w-4 animate-spin text-gray-400"})})]}),(n.length>0||x)&&e.jsx("div",{className:"absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto",children:n.length>0?n.map((a=>{const r=(s=a.value,c.includes(s));var s;return e.jsxs("button",{className:t("w-full text-left px-4 py-2.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between gap-2",r&&"opacity-50 bg-gray-50 dark:bg-gray-800"),onClick:()=>l(a.value),disabled:r,children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(R,{size:16,className:"text-primary"}),e.jsx("span",{children:a.label})]}),r&&e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Já adicionado"})]},`${a.id}-${a.value}`)})):e.jsx("button",{className:"w-full text-left px-4 py-2.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between",onClick:()=>{l(s),g(!1)},children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(R,{size:16,className:"text-primary"}),e.jsxs("span",{children:["Adicionar ",e.jsxs("strong",{children:['"',s,'"']})," manualmente"]})]})})})]}),c.length>0?e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsxs("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:["Medicamentos selecionados (",c.length,"/6)"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:c.map(((a,r)=>e.jsxs("div",{className:t("bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full px-3 py-1.5 flex items-center gap-1.5 border border-gray-200 dark:border-gray-700 shadow-sm transition-all hover:shadow-md",r%4==0&&"border-l-4 border-l-primary",r%4==1&&"border-l-4 border-l-secondary",r%4==2&&"border-l-4 border-l-green-500",r%4==3&&"border-l-4 border-l-amber-500"),children:[e.jsx(y,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:a}),e.jsx("button",{onClick:()=>m(r),className:"ml-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full p-1","aria-label":"Remover medicamento",children:e.jsx(o,{className:"h-3 w-3"})})]},`selected-${r}-${a}`)))})]}):e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border border-dashed border-gray-300 dark:border-gray-700 flex flex-col items-center justify-center",children:[e.jsx(y,{className:"h-10 w-10 text-gray-400 mb-2"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center",children:"Nenhum medicamento selecionado"}),e.jsx("p",{className:"text-xs text-gray-400 dark:text-gray-500 text-center mt-1",children:"Pesquise e selecione de 2 a 6 medicamentos para análise"})]})]})},Q=({patientData:a,handlePatientDataChange:r})=>e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"age",children:"Idade"}),e.jsx(j,{id:"age",placeholder:"Ex: 65",value:a.age,onChange:e=>r("age",e.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"gender",children:"Sexo"}),e.jsxs(N,{value:a.gender,onValueChange:e=>r("gender",e),children:[e.jsx(k,{children:e.jsx(w,{placeholder:"Selecione..."})}),e.jsxs(C,{children:[e.jsx(S,{value:"masculino",children:"Masculino"}),e.jsx(S,{value:"feminino",children:"Feminino"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"comorbidities",children:"Comorbidades"}),e.jsx(j,{id:"comorbidities",placeholder:"Ex: insuficiência renal, hipertensão",value:a.comorbidities,onChange:e=>r("comorbidities",e.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"clinical-status",children:"Estado clínico"}),e.jsx(j,{id:"clinical-status",placeholder:"Ex: grávida, lactante, idoso",value:a.clinicalStatus,onChange:e=>r("clinicalStatus",e.target.value)})]})]}),U=[{value:"excellent",label:"Excelente",icon:z,color:"text-blue-600",bgColor:"bg-blue-50 hover:bg-blue-100"},{value:"good",label:"Bom",icon:B,color:"text-green-500",bgColor:"bg-green-50 hover:bg-green-100"},{value:"regular",label:"Regular",icon:V,color:"text-yellow-500",bgColor:"bg-yellow-50 hover:bg-yellow-100"},{value:"poor",label:"Ruim",icon:H,color:"text-red-500",bgColor:"bg-red-50 hover:bg-red-100"}];function X({interactionId:r,medicationList:s,analysisResult:t}){const{user:i}=E();L();const[d,n]=a.useState(null),[l,o]=a.useState(null),[m,p]=a.useState(!1),[j,v]=a.useState(""),[y,f]=a.useState(null);a.useEffect((()=>{(async()=>{if(i)try{const{data:e,error:a}=await q.from("pedbook_conducts_feedback").select("rating").eq("summary_id",r).eq("user_id",i.id).eq("tipo_feedback","interactions").maybeSingle();if(a)return;e&&n(e.rating)}catch(e){}})()}),[i,r]);const N=async(e,a)=>{if(i)if(d)o("Você já avaliou esta análise anteriormente");else try{o("Enviando feedback...");const{error:d}=await q.from("pedbook_conducts_feedback").insert({summary_id:r,summary_title:t||s.join(", "),user_id:i.id,rating:e,comment:a||null,tipo_feedback:"interactions"});if(d){if("23505"===d.code)return void o("Você já avaliou esta análise anteriormente");throw d}n(e),o("Feedback enviado com sucesso!"),setTimeout((()=>{o(null)}),3e3)}catch(l){o(`Erro ao enviar feedback: ${l.message}`)}else o("É necessário estar logado para enviar feedback")};return r?e.jsxs("div",{className:"mt-6 border-t pt-6",children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900 dark:text-gray-100",children:d?"Sua avaliação":"Esta análise foi útil para você?"}),l&&e.jsx("div",{className:"text-sm px-4 py-2 rounded-md "+(l.includes("Erro")?"bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400":"bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"),children:l})]}),e.jsx("div",{className:"mt-4 flex justify-center gap-3",children:U.map((a=>{const r=d===a.value;return e.jsxs(M,{className:`flex flex-col items-center justify-center p-2 cursor-${d?"default":"pointer"} transition-all\n                ${r?`${a.bgColor} ring-2 ring-${a.color}`:"bg-gray-50"}\n                ${d?"":a.bgColor}\n                w-16 h-16`,onClick:()=>{return!d&&void("excellent"!==(e=a.value)?(f(e),p(!0)):N(e));var e},children:[e.jsx(a.icon,{className:`w-6 h-6 ${r?a.color:"text-gray-400"}`}),e.jsx("span",{className:"mt-1 text-xs font-medium "+(r?"text-gray-900":"text-gray-500"),children:a.label})]},a.value)}))}),e.jsx(c,{open:m,onOpenChange:p,children:e.jsxs(g,{className:"sm:max-w-[425px]",children:[e.jsxs(u,{children:[e.jsx(h,{children:"Deixe seu comentário"}),e.jsx(b,{children:"Sua opinião é valiosa para melhorarmos nossas análises de interações medicamentosas. Por favor, compartilhe qualquer observação ou sugestão."})]}),e.jsx("div",{className:"my-4",children:e.jsx($,{value:j,onChange:e=>v(e.target.value),placeholder:"Digite seu comentário aqui...",className:"min-h-[100px]"})}),e.jsxs(T,{children:[e.jsx(x,{variant:"outline",onClick:()=>{p(!1),y&&N(y)},children:"Enviar sem comentário"}),e.jsx(x,{onClick:()=>{y&&(N(y,j),p(!1),v(""),f(null))},children:"Enviar comentário"})]})]})})]}):null}const Y=()=>{const[r,c]=a.useState(""),[m,g]=a.useState([]),[u,h]=a.useState([]),[b,p]=a.useState([]),[j,N]=a.useState(!1),[k,w]=a.useState(!1),[C,S]=a.useState(null),[E,$]=a.useState([]),[T,R]=a.useState(!1),z=a.useRef(null),{toast:B}=L(),[V,H]=a.useState({age:"",gender:"",comorbidities:"",clinicalStatus:""}),[U,Y]=a.useState("");a.useEffect((()=>{if(C){const e=te(C);$(e)}else $([])}),[C]),a.useEffect((()=>(r.length>=3?(R(!0),z.current&&clearTimeout(z.current),z.current=setTimeout((async()=>{try{const e=r.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase(),{data:a,error:s}=await q.rpc("search_drug_interactions",{search_term:e});if(s)throw s;const t=(a||[]).map((e=>({id:e.id,value:e.active_ingredient,label:e.active_ingredient})));g(t)}catch(e){B({title:"Erro na busca",description:"Não foi possível buscar medicamentos. Tente novamente.",variant:"destructive"})}finally{R(!1)}}),500)):g([]),()=>{z.current&&clearTimeout(z.current)})),[r,B]);const ee=a=>{switch(a){case"Contraindicado":return e.jsx(o,{className:"h-6 w-6 text-white"});case"Grave":return e.jsx(l,{className:"h-6 w-6 text-white"});case"Moderado":return e.jsx(n,{className:"h-6 w-6 text-white"});case"Leve":case"Sem interação relevante":return e.jsx(d,{className:"h-6 w-6 text-white"});default:return e.jsx(i,{className:"h-6 w-6 text-white"})}},ae=e=>{switch(e){case"Contraindicado":return"bg-red-600";case"Grave":return"bg-purple-600";case"Moderado":return"bg-orange-500";case"Leve":case"Sem interação relevante":return"bg-green-500";default:return"bg-blue-500"}},re=e=>{switch(e){case"Contraindicado":return"border-red-200 dark:border-red-800";case"Grave":return"border-purple-200 dark:border-purple-800";case"Moderado":return"border-orange-200 dark:border-orange-800";case"Leve":case"Sem interação relevante":return"border-green-200 dark:border-green-800";default:return"border-blue-200 dark:border-blue-800"}},se=e=>{switch(e){case"Contraindicado":return"bg-red-50 dark:bg-red-950/20";case"Grave":return"bg-purple-50 dark:bg-purple-950/20";case"Moderado":return"bg-orange-50 dark:bg-orange-950/20";case"Leve":case"Sem interação relevante":return"bg-green-50 dark:bg-green-950/20";default:return"bg-blue-50 dark:bg-blue-950/20"}},te=e=>{const a=[];try{const r=/(#{2,4}\s+\d+\.\s+.*?)(?=#{2,4}\s+\d+\.|$)/gs;let s,t=[];for(;null!==(s=r.exec(e));)t.push(s[1].trim());if(0===t.length){const a=e.split(/#{2,4}\s+\d+\./);a.length>1&&(a.shift(),t=a.map(((e,a)=>`### ${a+1}. ${e.trim()}`)))}return t.forEach((e=>{const r=e.match(/#{2,4}\s+\d+\.\s+(.*?)(?=\n|$)/),s=r?r[1].trim():"Seção sem título",t=e.replace(/#{2,4}\s+\d+\.\s+.*?\n/,"");let i="Leve";s.includes("Contraindicada")?i="Contraindicado":s.includes("Grave")?i="Grave":s.includes("Moderada")?i="Moderado":(s.includes("Leve")||s.includes("Sem"))&&(i="Leve");const d=ie(t,i),n=0===d.filter((e=>e&&e.drugPair)).length;s.toLowerCase().includes("resumo")||a.push({title:s,interactions:d,severityLevel:i,isEmpty:n})})),a}catch(r){return[{title:"Erro na análise",interactions:[{content:"Houve um erro ao analisar o resultado. Por favor, tente novamente."}],severityLevel:"Leve",isEmpty:!1}]}},ie=(e,a)=>{const r=[];try{if(e.includes("Nenhuma interação"))return r.push({content:e.trim(),severity:a}),r;const s=/- \*\*Interação:\*\* \[(.*?)\]([\s\S]*?)(?=- \*\*Interação:\*\*|\*\*Nenhuma interação|$)/g;let t;for(;null!==(t=s.exec(e));){const e=t[1].trim(),s=t[0],i=s.match(/\*\*Gravidade:\*\* (.*?)(?=\n|$)/),d=s.match(/\*\*Mecanismo:\*\* ([\s\S]*?)(?=\*\*|$)/),n=s.match(/\*\*Conduta Clínica Recomendada:\*\* ([\s\S]*?)(?=\*\*|$)/),l=s.match(/\*\*Referência:\*\* ([\s\S]*?)(?=\*\*|$)/);let o=a;if(i){const e=i[1].trim();e.includes("Contraindicada")?o="Contraindicado":e.includes("Grave")?o="Grave":e.includes("Moderada")?o="Moderado":(e.includes("Leve")||e.includes("Sem interação"))&&(o="Leve")}const c=d?d[1].trim():"",m=n?n[1].trim():"",x=l?l[1].trim():"";r.push({drugPair:e,severity:o,mechanism:c,recommendation:m,reference:x})}if(0===r.length&&!e.includes("Nenhuma interação")){const s=e.match(/\[(.*?)\]/);s?r.push({drugPair:s[1],content:e.trim(),severity:a}):e.trim().length>0&&r.push({content:e.trim(),severity:a})}return r}catch(s){return[]}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsxs(P,{children:[e.jsx("title",{children:"PedBook | Interações Medicamentosas"}),e.jsx("meta",{name:"description",content:"Consulte interações entre medicamentos e evite combinações perigosas em pacientes pediátricos."})]}),e.jsx(A,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8 md:py-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-8",children:[e.jsx(s,{to:"/",children:e.jsx(x,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 hidden sm:flex dark:hover:bg-primary/20",children:e.jsx(_,{className:"h-5 w-5"})})}),e.jsxs("div",{className:"text-center flex-1 space-y-3",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100",children:"Análise de Interações Medicamentosas"}),e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"Identifique potenciais interações entre medicamentos"})]})]}),C?e.jsxs("div",{className:"space-y-6",children:[e.jsxs(M,{className:"overflow-hidden shadow-lg border border-gray-200 dark:border-gray-800",children:[e.jsx("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 p-4 border-b border-gray-100 dark:border-gray-800",children:e.jsxs("div",{className:"flex items-center justify-between flex-wrap gap-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-green-100 dark:bg-green-900/30 p-2 rounded-full",children:e.jsx(d,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),e.jsx("h2",{className:"text-xl font-medium",children:"Análise completada"})]}),e.jsx(x,{variant:"outline",className:"text-sm",onClick:()=>{h([]),p([]),S(null),$([]),H({age:"",gender:"",comorbidities:"",clinicalStatus:""}),N(!1),Y("")},children:"Nova análise"})]})}),e.jsxs("div",{className:"p-5",children:[e.jsx("div",{className:"flex flex-wrap gap-3 mb-5",children:u.map(((a,r)=>{const s=b.includes(a);return e.jsxs("div",{className:t("bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-3 py-2 rounded-full flex items-center gap-2 border border-gray-200 dark:border-gray-700 w-full sm:w-auto",s?"border-dashed border-amber-300 dark:border-amber-700":"",r%4==0&&"border-l-4 border-l-primary",r%4==1&&"border-l-4 border-l-secondary",r%4==2&&"border-l-4 border-l-green-500",r%4==3&&"border-l-4 border-l-amber-500"),children:[e.jsx(y,{className:"h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:a}),s&&e.jsx("span",{className:"text-xs text-amber-500 dark:text-amber-400 ml-1",children:"(manual)"})]},`result-med-${r}`)}))}),e.jsx("div",{className:"space-y-6",children:E&&E.filter((e=>e&&!e.isEmpty)).length>0?E.filter((e=>e&&!e.isEmpty)).map(((a,r)=>e.jsx(M,{className:t("overflow-hidden border transition-all hover:shadow-md",re(a.severityLevel)),children:e.jsxs("div",{className:se(a.severityLevel),children:[e.jsxs("div",{className:"flex items-center gap-4 p-4 border-b border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:`${ae(a.severityLevel)} p-2 rounded-full`,children:ee(a.severityLevel)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold",children:a.title}),e.jsxs("div",{className:"text-sm font-medium mt-0.5",children:[a.interactions.filter((e=>e&&e.drugPair)).length," interação(ões) encontrada(s)"]})]})]}),e.jsxs("div",{className:"p-5 space-y-4",children:[a.interactions&&a.interactions.filter((e=>e&&e.drugPair)).map(((s,t)=>e.jsx(W,{interaction:s,isLast:t===a.interactions.filter((e=>e&&e.drugPair)).length-1},`interaction-${r}-${t}`))),a.isEmpty&&a.interactions.length>0&&e.jsxs("div",{className:"text-gray-600 dark:text-gray-400",children:["Nenhuma interação ",a.severityLevel.toLowerCase()," identificada entre os medicamentos selecionados."]})]})]})},`section-${r}`))):e.jsx(M,{className:"overflow-hidden border border-green-200 dark:border-green-800",children:e.jsxs("div",{className:"bg-green-50 dark:bg-green-950/20",children:[e.jsxs("div",{className:"flex items-center gap-4 p-4 border-b border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:"bg-green-500 p-2 rounded-full",children:e.jsx(d,{className:"h-6 w-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold",children:"Sem interações relevantes"}),e.jsx("div",{className:"text-sm font-medium text-green-700 dark:text-green-300",children:"Compatível"})]})]}),e.jsx("div",{className:"p-5",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Não foram identificadas interações medicamentosas significativas entre os medicamentos selecionados."})})]})})}),U&&e.jsx(X,{interactionId:U,medicationList:u,analysisResult:C})]})]}),e.jsx("div",{className:"text-center mb-2",children:e.jsxs("div",{className:"inline-flex items-center gap-3 p-4 rounded-lg bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-900",children:[e.jsx(l,{className:"h-5 w-5 text-amber-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-amber-700 dark:text-amber-300",children:"Resultado gerado por Dr. Will (IA)"}),e.jsxs("p",{className:"text-amber-600 dark:text-amber-400 text-sm",children:[e.jsx("span",{className:"font-semibold",children:"Ferramenta em fase BETA"})," • Como médico, é seu papel discernir estas informações de acordo com o contexto clínico."]})]})]})}),e.jsxs(M,{className:"p-4",children:[e.jsx("h3",{className:"font-medium text-gray-700 dark:text-gray-300 text-center mb-3",children:"Legenda de severidade"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2.5 p-2.5 rounded-md bg-red-50 dark:bg-red-950/30 border border-red-100 dark:border-red-900",children:[e.jsx("div",{className:"bg-red-600 p-1.5 rounded-full",children:e.jsx(o,{className:"h-4 w-4 text-white"})}),e.jsx("span",{className:"font-medium text-red-700 dark:text-red-300",children:"Contraindicado"})]}),e.jsxs("div",{className:"flex items-center gap-2.5 p-2.5 rounded-md bg-orange-50 dark:bg-orange-950/30 border border-orange-100 dark:border-orange-900",children:[e.jsx("div",{className:"bg-orange-600 p-1.5 rounded-full",children:e.jsx(l,{className:"h-4 w-4 text-white"})}),e.jsx("span",{className:"font-medium text-orange-700 dark:text-orange-300",children:"Grave"})]}),e.jsxs("div",{className:"flex items-center gap-2.5 p-2.5 rounded-md bg-yellow-50 dark:bg-yellow-950/30 border border-yellow-100 dark:border-yellow-900",children:[e.jsx("div",{className:"bg-yellow-500 p-1.5 rounded-full",children:e.jsx(n,{className:"h-4 w-4 text-white"})}),e.jsx("span",{className:"font-medium text-yellow-700 dark:text-yellow-300",children:"Moderado"})]}),e.jsxs("div",{className:"flex items-center gap-2.5 p-2.5 rounded-md bg-blue-50 dark:bg-blue-950/30 border border-blue-100 dark:border-blue-900",children:[e.jsx("div",{className:"bg-green-500 p-1.5 rounded-full",children:e.jsx(d,{className:"h-4 w-4 text-white"})}),e.jsx("span",{className:"font-medium text-blue-700 dark:text-blue-300",children:"Leve/Sem interação"})]})]}),e.jsx("div",{className:"flex justify-center mt-3",children:e.jsx(Z,{})})]})]}):e.jsxs(M,{className:"overflow-hidden shadow-lg border border-gray-200 dark:border-gray-800 relative",children:[e.jsx("div",{className:"absolute top-0 right-0 left-0 flex justify-center",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-600 via-primary to-purple-500 text-white px-4 py-1 text-xs rounded-b-md shadow-md",children:"Ferramenta em fase BETA"})}),e.jsx("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/5 dark:to-transparent p-4 border-b border-gray-100 dark:border-gray-800 mt-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-primary/20 p-2 rounded-full",children:e.jsx(y,{className:"h-5 w-5 text-primary"})}),e.jsx("h2",{className:"text-xl font-medium",children:"Selecione os medicamentos"})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsx(K,{searchTerm:r,setSearchTerm:c,isSearching:T,medicationOptions:m,handleAddMedication:e=>{u.length>=6?B({title:"Limite excedido",description:"O limite máximo de 6 medicamentos foi atingido.",variant:"destructive"}):u.includes(e)?B({title:"Medicamento já adicionado",description:"Este medicamento já está na lista de selecionados.",variant:"warning"}):(!m.some((a=>a.value===e))&&(p((a=>[...a,e])),B({title:"Medicamento adicionado manualmente",description:"Este medicamento foi adicionado para análise."})),h([...u,e]),c(""))},selectedMedications:u,handleRemoveMedication:e=>{const a=u[e];b.includes(a)&&p((e=>e.filter((e=>e!==a))));const r=[...u];r.splice(e,1),h(r)}}),e.jsx("div",{className:"flex flex-col space-y-2",children:e.jsxs("div",{className:"flex items-center gap-2 bg-gray-50 dark:bg-gray-800/40 p-3 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx(F,{id:"advanced-mode",checked:j,onCheckedChange:N}),e.jsxs("div",{children:[e.jsx(f,{htmlFor:"advanced-mode",className:"font-medium",children:"Incluir dados clínicos do paciente"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-0.5",children:"Para uma análise mais personalizada e precisa"})]})]})}),j&&e.jsx("div",{className:"bg-gray-50 dark:bg-gray-800/30 rounded-lg p-4 border border-gray-200 dark:border-gray-700 animate-in fade-in-50 slide-in-from-top-5 duration-300",children:e.jsx(Q,{patientData:V,handlePatientDataChange:(e,a)=>{H((r=>({...r,[e]:a})))}})}),e.jsx(x,{className:"w-full gap-2 py-6",size:"lg",onClick:async()=>{if(u.length<2)return void B({title:"Medicamentos insuficientes",description:"Selecione pelo menos 2 medicamentos para análise",variant:"destructive"});w(!0),S(null);const e=J();Y(e);try{const e={medications:u.flatMap((e=>{return(a=e).includes("+")?a.split("+").map((e=>e.trim())):[a];var a})),mode:j?"advanced":"simple",originalMedicationsCount:u.length,manuallyAddedMedications:b,...j&&{patientData:V}},a=await fetch("https://bxedpdmgvgatjdfxgxij.supabase.co/functions/v1/drug-interactions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw new Error(`Erro na solicitação: ${a.status}`);const r=await a.json();S(r.result)}catch(a){B({title:"Erro na análise",description:"Não foi possível analisar as interações. Tente novamente.",variant:"destructive"}),Y("")}finally{w(!1)}},disabled:u.length<2||k,children:k?e.jsxs(e.Fragment,{children:[e.jsx(v,{className:"mr-2 h-5 w-5 animate-spin"}),"💡 Dr. Will está pensando..."]}):e.jsxs(e.Fragment,{children:["Analisar Interações",e.jsx(O,{className:"h-5 w-5"})]})}),e.jsxs(I,{variant:"default",className:"bg-blue-50/50 dark:bg-blue-950/10 border border-blue-200 dark:border-blue-800",children:[e.jsx(i,{className:"h-5 w-5 text-blue-500"}),e.jsxs(D,{className:"text-blue-700 dark:text-blue-300",children:["Selecione entre 2 e 6 medicamentos. Quanto mais específico for o nome do medicamento, mais precisa será a análise.",b.length>0&&e.jsxs("div",{className:"mt-2 text-amber-600 dark:text-amber-400 text-sm",children:[e.jsx("strong",{children:"Atenção:"})," Você adicionou medicamento(s) manualmente. A análise de interações com esses medicamentos pode ser menos precisa."]})]})]})]})]})]})}),e.jsx(G,{})]})};export{Y as default};
