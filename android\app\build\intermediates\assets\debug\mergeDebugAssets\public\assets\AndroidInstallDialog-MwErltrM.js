import{j as e}from"./radix-core-6kBL75b5.js";import{r as o}from"./critical-DVX9Inzy.js";import{D as a,e as s,f as t,g as r,z as i,B as n}from"./index-D89HBjcn.js";import{D as d}from"./download-DW9jWE4p.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const l="android_install_dialog_shown";function c(){const[c,p]=o.useState(!1),[m,g]=o.useState(!1),[x,u]=o.useState(!1);o.useEffect((()=>{setTimeout((()=>{const e=navigator.userAgent.toLowerCase(),o=window.matchMedia("(display-mode: standalone)").matches,a=localStorage.getItem(l),s=(()=>{const o=/android/i.test(e),a=/mobile/i.test(e),s="ontouchstart"in window,t=window.screen.width<=768,r=/iphone|ipad|ipod/i.test(e),i=/windows nt|linux x86_64|x11.*linux|macintosh/i.test(e),n=!0===navigator.webdriver;return o&&a&&s&&t&&!r&&!i&&!n})(),t=(()=>{const e=document.referrer.toLowerCase(),o=/play\.google\.com|googleplay/.test(e),a=window.location.search.includes("utm_source=play");return o||a})(),r=(()=>{const o=/wv/.test(e)||/WebView/.test(e),a="AndroidInterface"in window||"android"in window;return o||a})(),i=s&&!o&&!a&&!t&&!r;g(s),p(i)}),1e4),setTimeout((()=>{const e=new Image;e.onload=()=>u(!0),e.src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo//google-play-download-android-app-logo-png-transparent.png"}),8e3)}),[]);const h=()=>{localStorage.setItem(l,"true"),p(!1)};return e.jsx(e.Fragment,{children:e.jsx(a,{open:c,onOpenChange:h,children:e.jsxs(s,{className:"max-w-[90dvw] max-h-[90dvh] w-[400px] rounded-2xl border-none bg-white/90 backdrop-blur-sm shadow-lg",children:[e.jsxs(t,{className:"space-y-3",children:[e.jsx(r,{className:"text-2xl font-bold text-primary",children:"Instale o PedBook"}),e.jsx(i,{className:"text-base",children:"Baixe nosso aplicativo para ter acesso rápido e fácil ao PedBook diretamente do seu celular!"})]}),e.jsxs("div",{className:"flex flex-col gap-6 items-center py-4",children:[c&&e.jsx("div",{className:"w-48 h-16 flex items-center justify-center",children:x?e.jsx("img",{src:"https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo//google-play-download-android-app-logo-png-transparent.png",alt:"Google Play Store",width:"192",height:"64",className:"w-48 h-auto object-contain hover:scale-105 transition-transform",loading:"lazy",decoding:"async"}):e.jsx("div",{className:"w-48 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center",children:e.jsx(d,{className:"h-8 w-8 text-gray-400 animate-pulse"})})}),e.jsxs("div",{className:"flex gap-3 w-full",children:[e.jsx(n,{variant:"outline",className:"flex-1 rounded-xl hover:bg-gray-100",onClick:h,children:"Prefiro não"}),e.jsxs(n,{className:"flex-1 gap-2 rounded-xl bg-primary hover:bg-primary/90",onClick:()=>{window.open("https://play.google.com/store/apps/details?id=com.med.pedbook&hl=pt_BR","_blank"),h()},children:[e.jsx(d,{className:"h-4 w-4"}),"Instalar"]})]})]})]})})})}export{c as AndroidInstallDialog};
