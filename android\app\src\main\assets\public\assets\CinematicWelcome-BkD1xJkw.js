import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{m as a,X as s,aB as i,b2 as r}from"./index-DIVKaOkK.js";import{A as l}from"./index-CZYeUU5x.js";import{T as o}from"./target-JnessAn2.js";import{B as m}from"./book-open-HpWivqt7.js";import{P as c}from"./play-DwISaJ34.js";import{A as d}from"./FeedbackTrigger-Bbh1ZkR8.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./rocket-coAqOqu0.js";import"./zap-cv85ZGQL.js";import"./star-D6Dh7eqH.js";import"./circle-help-DamQHiY3.js";const n=({onComplete:n,onStartStudy:x})=>{const[p,h]=t.useState("welcome");t.useEffect((()=>{(async()=>{"welcome"===p&&(await new Promise((e=>setTimeout(e,3e3))),h("features"),await new Promise((e=>setTimeout(e,4e3))),h("cta"))})()}),[p]);const y=()=>{n()},b=[{icon:r,title:"Questões de Pediatria",description:"Milhares de questões especializadas"},{icon:o,title:"Filtros Inteligentes",description:"Estude por especialidade e tema"},{icon:m,title:"Resultados Detalhados",description:"Acompanhe seu progresso"}];return e.jsxs("div",{className:"fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 25% 25%, #3b82f6 0%, transparent 50%), \n                           radial-gradient(circle at 75% 75%, #8b5cf6 0%, transparent 50%)"}})}),e.jsx("div",{className:"absolute inset-0",children:[...Array(20)].map(((t,s)=>e.jsx(a.div,{className:"absolute w-1 h-1 bg-white rounded-full opacity-60",style:{left:100*Math.random()+"%",top:100*Math.random()+"%"},animate:{y:[-20,-100],opacity:[0,1,0]},transition:{duration:3+2*Math.random(),repeat:1/0,delay:2*Math.random()}},s)))}),e.jsx(a.button,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},onClick:y,className:"absolute top-6 right-6 z-50 bg-white/10 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/20 transition-colors",children:e.jsx(s,{className:"w-5 h-5"})}),e.jsxs(l,{mode:"wait",children:["welcome"===p&&e.jsx(a.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.1},className:"flex items-center justify-center h-full p-8",children:e.jsx("div",{className:"text-center text-white max-w-2xl",children:e.jsxs(a.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3,duration:.8},children:[e.jsx(i,{className:"w-16 h-16 mx-auto mb-6 text-yellow-400"}),e.jsx("h1",{className:"text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-transparent bg-clip-text",children:"PedBook"}),e.jsx("p",{className:"text-xl md:text-2xl text-gray-300 mb-8",children:"Prévia gratuita do MedEvo focada em pediatria"})]})})},"welcome"),"features"===p&&e.jsx(a.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:-100},className:"flex items-center justify-center h-full p-4 sm:p-6 lg:p-8",children:e.jsxs("div",{className:"text-center text-white max-w-4xl px-4",children:[e.jsx(a.h2,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},className:"text-2xl sm:text-3xl md:text-4xl font-bold mb-8 sm:mb-12",children:"Recursos da Plataforma"}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8",children:b.map(((t,s)=>e.jsxs(a.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{delay:.4+.2*s},className:"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 hover:bg-white/15 transition-colors",children:[e.jsx(t.icon,{className:"w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 text-blue-400"}),e.jsx("h3",{className:"text-lg sm:text-xl font-semibold mb-2",children:t.title}),e.jsx("p",{className:"text-gray-300 text-sm sm:text-base leading-relaxed",children:t.description})]},s)))})]})},"features"),"cta"===p&&e.jsx(a.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"flex items-center justify-center h-full p-4 sm:p-6 lg:p-8",children:e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-lg rounded-3xl p-6 sm:p-8 md:p-12 max-w-2xl mx-4 text-center shadow-2xl border border-white/20 dark:border-gray-700/30",children:e.jsxs(a.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},children:[e.jsx("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4 sm:mb-6",children:"🚀 Pronto para começar?"}),e.jsx("p",{className:"text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-6 sm:mb-8 leading-relaxed",children:"Teste como é estudar no MedEvo com questões de pediatria!"}),e.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[e.jsxs(a.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{x()},className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-2xl text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 sm:gap-3",children:[e.jsx(c,{className:"w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"Testar Prévia"}),e.jsx(d,{className:"w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0"})]}),e.jsx(a.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:y,className:"w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 px-6 sm:px-8 py-2.5 sm:py-3 rounded-2xl text-sm sm:text-base font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300",children:"Explorar Mais Tarde"})]})]})})},"cta")]})]})};export{n as CinematicWelcome};
