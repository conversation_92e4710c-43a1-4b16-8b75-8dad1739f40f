import{j as e}from"./radix-core-6kBL75b5.js";import{Q as s}from"./QuestionImport-BwGU7sh3.js";import{r as a}from"./critical-DVX9Inzy.js";import{s as t,d as r,R as i,W as c,Z as o,ac as n,$ as d,U as l,B as m,L as u,ak as p}from"./index-DV3Span9.js";import{A as h,b as x}from"./alert-CRxxoxJe.js";import{T as j}from"./trash-2-BQ8b_n_c.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const f=()=>{const{toast:s}=r(),[f,g]=a.useState(!1),[v,y]=a.useState(!1),[N,q]=a.useState(null);return e.jsxs(i,{children:[e.jsxs(c,{children:[e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(n,{className:"h-5 w-5"}),"Gerenciar Questões Duplicadas"]}),e.jsx(d,{children:"Verifique e remova questões duplicadas do banco de dados"})]}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(m,{onClick:async()=>{g(!0);try{const e=await async function(){try{const{data:e,error:s}=await t.from("questions").select("id, question_content, created_at").order("created_at",{ascending:!1});if(s)throw s;const a=new Map;e.forEach((e=>{const s=e.question_content.trim();a.has(s)||a.set(s,[]),a.get(s).push({id:e.id,created_at:e.created_at})}));const r=Array.from(a.entries()).filter((([e,s])=>s.length>1)).map((([e,s])=>({id:s[0].id,question_content:e.substring(0,100)+"...",created_at:s[0].created_at,count:s.length})));return{duplicates:r,totalDuplicates:r.reduce(((e,s)=>e+s.count-1),0)}}catch(e){throw e}}();q(e),s({title:"Verificação concluída",description:`Encontradas ${e.duplicates.length} questões com duplicatas (${e.totalDuplicates} duplicatas no total)`})}catch(e){s({title:"Erro na verificação",description:e.message,variant:"destructive"})}finally{g(!1)}},disabled:f||v,className:"flex items-center gap-2",children:[f?e.jsx(u,{className:"h-4 w-4 animate-spin"}):e.jsx(n,{className:"h-4 w-4"}),"Verificar Duplicatas"]}),N&&N.duplicates.length>0&&e.jsxs(m,{onClick:async()=>{if(N&&0!==N.duplicates.length){y(!0);try{const{removed:e,errors:a}=await async function(){try{const{data:s,error:a}=await t.from("questions").select("id, question_content, created_at").order("created_at",{ascending:!0});if(a)throw a;const r=new Map;s.forEach((e=>{const s=e.question_content.trim();r.has(s)||r.set(s,[]),r.get(s).push({id:e.id,created_at:e.created_at})}));let i=0;const c=[];for(const[o,n]of r.entries())if(n.length>1){const s=n.slice(1);for(const a of s)try{const{error:e}=await t.from("questions").delete().eq("id",a.id);e?c.push(`Erro ao remover questão ${a.id}: ${e.message}`):i++}catch(e){c.push(`Erro ao remover questão ${a.id}: ${e.message}`)}}return{removed:i,errors:c}}catch(s){throw s}}();s({title:"Remoção concluída",description:`${e} questões duplicadas removidas${a.length>0?` (${a.length} erros)`:""}`}),q(null),a.length}catch(e){s({title:"Erro na remoção",description:e.message,variant:"destructive"})}finally{y(!1)}}else s({title:"Nenhuma duplicata encontrada",description:"Execute a verificação primeiro",variant:"destructive"})},disabled:f||v,variant:"destructive",className:"flex items-center gap-2",children:[v?e.jsx(u,{className:"h-4 w-4 animate-spin"}):e.jsx(j,{className:"h-4 w-4"}),"Remover Duplicatas"]})]}),N&&e.jsx("div",{className:"space-y-4",children:N.duplicates.length>0?e.jsxs(e.Fragment,{children:[e.jsxs(h,{children:[e.jsx(p,{className:"h-4 w-4"}),e.jsxs(x,{children:["Encontradas ",e.jsx("strong",{children:N.duplicates.length})," questões com duplicatas. Total de ",e.jsx("strong",{children:N.totalDuplicates})," questões duplicadas que podem ser removidas."]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-semibold",children:"Questões com duplicatas:"}),e.jsx("div",{className:"max-h-60 overflow-y-auto space-y-2",children:N.duplicates.map(((s,a)=>e.jsx("div",{className:"p-3 border rounded-lg bg-gray-50",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-sm text-gray-600 mb-1",children:[e.jsxs("strong",{children:["Questão #",a+1]})," - ",s.count," cópias"]}),e.jsx("p",{className:"text-sm",children:s.question_content})]}),e.jsx("span",{className:"text-xs text-gray-500 ml-2",children:new Date(s.created_at).toLocaleDateString()})]})},s.id)))})]})]}):e.jsx(h,{children:e.jsx(x,{children:"✅ Nenhuma questão duplicada encontrada no banco de dados."})})}),e.jsxs("div",{className:"text-sm text-gray-600 space-y-1",children:[e.jsx("p",{children:e.jsx("strong",{children:"Como funciona:"})}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[e.jsx("li",{children:"A verificação identifica questões com conteúdo idêntico"}),e.jsx("li",{children:"A remoção mantém apenas a questão mais antiga de cada grupo"}),e.jsx("li",{children:"Questões mais recentes com mesmo conteúdo são removidas"}),e.jsx("li",{children:"Esta operação não pode ser desfeita"})]})]})]})})]})},g=()=>e.jsxs("div",{className:"container mx-auto py-8 space-y-6",children:[e.jsxs(i,{children:[e.jsx(c,{children:e.jsx(o,{children:"Importar Questões"})}),e.jsx(l,{children:e.jsx(s,{})})]}),e.jsx(f,{})]});export{g as default};
