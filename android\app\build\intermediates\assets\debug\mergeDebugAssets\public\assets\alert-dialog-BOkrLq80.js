import{j as a}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{a as e,T as t,P as o,C as d,b as l,D as m,c as r,A as i,O as c}from"./radix-feedback-dpGNY8wJ.js";import{j as f,aV as n}from"./index-CFnD44mG.js";const p=e,N=t,x=o,j=s.forwardRef((({className:s,...e},t)=>a.jsx(c,{className:f("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...e,ref:t})));j.displayName=c.displayName;const y=s.forwardRef((({className:s,...e},t)=>a.jsxs(x,{children:[a.jsx(j,{}),a.jsx(d,{ref:t,className:f("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...e})]})));y.displayName=d.displayName;const u=({className:s,...e})=>a.jsx("div",{className:f("flex flex-col space-y-2 text-center sm:text-left",s),...e});u.displayName="AlertDialogHeader";const g=({className:s,...e})=>a.jsx("div",{className:f("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...e});g.displayName="AlertDialogFooter";const b=s.forwardRef((({className:s,...e},t)=>a.jsx(l,{ref:t,className:f("text-lg font-semibold",s),...e})));b.displayName=l.displayName;const w=s.forwardRef((({className:s,...e},t)=>a.jsx(m,{ref:t,className:f("text-sm text-muted-foreground",s),...e})));w.displayName=m.displayName;const R=s.forwardRef((({className:s,...e},t)=>a.jsx(i,{ref:t,className:f(n(),s),...e})));R.displayName=i.displayName;const v=s.forwardRef((({className:s,...e},t)=>a.jsx(r,{ref:t,className:f(n({variant:"outline"}),"mt-2 sm:mt-0",s),...e})));v.displayName=r.displayName;export{p as A,y as a,u as b,b as c,w as d,g as e,v as f,R as g,N as h};
