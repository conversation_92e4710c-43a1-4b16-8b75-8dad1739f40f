import{j as e}from"./radix-core-6kBL75b5.js";import{c as a,R as s,W as r,Z as i,U as t,an as l,a5 as o,ad as n,ae as c,af as d,ag as m,ai as h,T as p,ab as x,aj as u,Y as v,B as j,aH as g,az as f,d as b,aq as N,ar as y,as as C,at as w,aw as A,$ as S,aA as F,k,L as P,s as M,a8 as E,aa as T}from"./index-CFnD44mG.js";import D from"./Footer-BQ6Dqsd-.js";import{r as R}from"./critical-DVX9Inzy.js";import{S as O}from"./switch-s5gbfSXw.js";import{U as I}from"./user-C_sBRWqs.js";import{C as z,a as q,b as V}from"./collapsible-B6HfSnGs.js";import{T as G,a as _,b as B,c as Q}from"./tooltip-BVv4YSX7.js";import{Z as W}from"./zap-DhWHOaH4.js";import{S as U}from"./stethoscope-DLVdj0oT.js";import{C as $}from"./copy-zh0SugSR.js";import{S as H}from"./save-Df_H0u2K.js";import{R as Z}from"./refresh-cw-Brjmhzft.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-ot4XwGkJ.js";import"./rocket-BOvZdSbI.js";import"./target-BX0FTOgV.js";import"./book-open-CpPiu5Sl.js";import"./star-ShS_gnKj.js";import"./circle-help-B8etwa8R.js";import"./instagram-X0NDZon3.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=a("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]),X=({data:a,onChange:x,templateType:u})=>{const v="pediatric_1_2m"===u;return e.jsxs(s,{children:[e.jsx(r,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5 text-primary"}),"Perfil do Paciente"]})}),e.jsxs(t,{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[v&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"name",children:"Nome"}),e.jsx(o,{id:"name",placeholder:"Nome do paciente",value:a.name||"",onChange:e=>x({name:e.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"gender",children:"Gênero"}),e.jsxs(n,{value:a.gender,onValueChange:e=>x({gender:e}),children:[e.jsx(c,{id:"gender",children:e.jsx(d,{placeholder:"Selecione o gênero"})}),e.jsxs(m,{children:[e.jsx(h,{value:"male",children:"Masculino"}),e.jsx(h,{value:"female",children:"Feminino"}),e.jsx(h,{value:"other",children:"Outro"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"age",children:"Idade"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(o,{id:"age",type:"number",min:0,step:1,value:a.age||"",onChange:e=>x({age:parseInt(e.target.value)||0}),className:"flex-1"}),e.jsxs(n,{value:a.ageUnit,onValueChange:e=>x({ageUnit:e}),children:[e.jsx(c,{className:"w-32",children:e.jsx(d,{placeholder:"Unidade"})}),e.jsxs(m,{children:[e.jsx(h,{value:"days",children:"Dias"}),e.jsx(h,{value:"months",children:"Meses"}),e.jsx(h,{value:"years",children:"Anos"})]})]})]})]}),v&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"birthDate",children:"Data de Nascimento"}),e.jsx(o,{id:"birthDate",type:"date",value:a.birthDate||"",onChange:e=>x({birthDate:e.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"informant",children:"Informante"}),e.jsx(o,{id:"informant",placeholder:"Nome do informante e relação com o paciente",value:a.informant||"",onChange:e=>x({informant:e.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"previousDiagnosis",children:"Diagnósticos Prévios"}),e.jsx(o,{id:"previousDiagnosis",placeholder:"Diagnósticos prévios, se houver",value:a.previousDiagnosis||"",onChange:e=>x({previousDiagnosis:e.target.value})})]}),e.jsxs("div",{className:"space-y-2 flex items-center justify-between",children:[e.jsx(l,{htmlFor:"premature",className:"cursor-pointer",children:"Prematuro?"}),e.jsx(O,{id:"premature",checked:a.premature||!1,onCheckedChange:e=>x({premature:e})})]}),a.premature&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"correctedAge",children:"Idade Corrigida"}),e.jsx(o,{id:"correctedAge",placeholder:"Idade corrigida para prematuridade",value:a.correctedAge||"",onChange:e=>x({correctedAge:e.target.value})})]})]}),!v&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"appointmentType",children:"Tipo de Atendimento"}),e.jsx(o,{id:"appointmentType",placeholder:"Ex: Consulta inicial, retorno, urgência",value:a.appointmentType,onChange:e=>x({appointmentType:e.target.value})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"mainComplaint",children:v?"Queixas":"Queixa Principal"}),e.jsx(p,{id:"mainComplaint",placeholder:v?"Descreva as queixas atuais":"Descreva a queixa principal nas palavras do paciente",value:a.mainComplaint,onChange:e=>x({mainComplaint:e.target.value}),className:"min-h-[80px]"})]})]})]})},Y=({data:a,onChange:o,templateType:n})=>{const[c,d]=R.useState(!1),[m,h]=R.useState([]),f=e=>{if("traditional"!==n)return c?"Apenas descreva alterações relevantes.":"Descreva os achados...";switch(e){case"hda":return c?"Apenas descreva alterações relevantes. Ex: 'febre há 3 dias, tosse produtiva'":"O acompanhante relata que o paciente era previamente hígido e, há cerca de X dias, iniciou um quadro de (sintomas principais), acompanhado de (sintomas secundários)...";case"perinatais":return c?"Apenas alterações relevantes. Ex: 'prematuridade (32 sem)'":"Gestação sem intercorrências, parto tipo (normal/cesárea), a termo, peso ao nascer...";case"patologicos":return c?"Apenas alterações relevantes. Ex: 'asma, epilepsia'":"Comorbidades, doenças prévias, internações...";case"cirurgicos":return c?"Apenas alterações relevantes. Ex: 'apendicectomia há 2 anos'":"Procedimentos cirúrgicos prévios...";case"alergicos":return c?"Apenas alterações relevantes. Ex: 'alergia à dipirona'":"Alergias medicamentosas, alimentares ou outras...";case"medicamentosos":return c?"Apenas alterações relevantes. Ex: 'prednisolona 20mg/dia'":"Medicamentos em uso regular...";case"vacinacao":return c?"Apenas alterações relevantes. Ex: 'atrasada para idade'":"Caderneta de vacinação em dia? Qual a próxima vacina programada?";case"desenvolvimento":return c?"Apenas alterações relevantes. Ex: 'atraso na fala'":"Neuropsicológico: linguagem, interação social. Motor: habilidades motoras grossas e finas...";case"alimentacao":return c?"Apenas alterações relevantes. Ex: 'seletividade alimentar'":"Tipo de aleitamento durante o 1º ano, introdução de alimentos sólidos, padrão alimentar atual...";case"ambiente":return c?"Apenas alterações relevantes. Ex: 'mora com avós'":"Condições da habitação, saneamento básico, presença de animais, exposição a tabagismo, rotina de sono...";default:return"Descreva os achados..."}},b=[{id:"hda",title:"História da Doença Atual",placeholder:f("hda"),value:a.hda},{id:"perinatais",title:"Antecedentes Perinatais",placeholder:f("perinatais"),value:a.perinatais},{id:"patologicos",title:"Antecedentes Patológicos",placeholder:f("patologicos"),value:a.patologicos},{id:"cirurgicos",title:"Antecedentes Cirúrgicos",placeholder:f("cirurgicos"),value:a.cirurgicos},{id:"alergicos",title:"Antecedentes Alérgicos",placeholder:f("alergicos"),value:a.alergicos},{id:"medicamentosos",title:"Medicamentos em uso",placeholder:f("medicamentosos"),value:a.medicamentosos},{id:"vacinacao",title:"Vacinação",placeholder:f("vacinacao"),value:a.vacinacao},{id:"desenvolvimento",title:"Desenvolvimento Neuropsicomotor",placeholder:f("desenvolvimento"),value:a.desenvolvimento},{id:"alimentacao",title:"Alimentação",placeholder:f("alimentacao"),value:a.alimentacao},{id:"ambiente",title:"Ambiente Familiar/Social",placeholder:f("ambiente"),value:a.ambiente}];return e.jsxs(s,{children:[e.jsx(r,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-5 w-5 text-primary"}),"Anamnese ","traditional"===n?"Tradicional":"Simplificada"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Apenas alterações"}),e.jsx(O,{checked:c,onCheckedChange:d,className:"data-[state=checked]:bg-primary"}),e.jsx(G,{children:e.jsxs(_,{children:[e.jsx(B,{asChild:!0,children:e.jsx(u,{className:"h-4 w-4 text-gray-400 cursor-help"})}),e.jsx(Q,{children:e.jsx("p",{className:"w-[220px] text-xs",children:'No modo "Apenas alterações", você só precisa descrever o que está alterado. Os campos vazios serão preenchidos com valores normais para a idade e gênero.'})})]})})]})]})}),e.jsxs(t,{className:"space-y-6",children:[c?e.jsxs("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start",children:[e.jsx(v,{className:"h-5 w-5 text-amber-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-amber-800",children:"Modo rápido ativado"}),e.jsx("p",{className:"text-sm text-amber-700",children:"Preencha apenas os achados alterados. Os campos vazios serão automaticamente preenchidos com valores normais para a idade e gênero do paciente."})]})]}):null,b.map((a=>e.jsxs(z,{className:"border rounded-md",open:m.includes(a.id),onOpenChange:()=>{return e=a.id,void h((a=>a.includes(e)?a.filter((a=>a!==e)):[...a,e]));var e},children:[e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs(l,{htmlFor:a.id,className:"font-medium",children:[a.title,a.value&&e.jsxs("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary",children:[e.jsx(W,{className:"h-3 w-3 mr-1"}),"Preenchido"]})]}),e.jsx(q,{asChild:!0,children:e.jsx(j,{variant:"ghost",size:"sm",children:e.jsx(g,{className:"h-4 w-4"})})})]}),e.jsx(V,{className:"px-4 pb-4",children:e.jsx(p,{id:a.id,value:a.value,onChange:e=>{return s=a.id,r=e.target.value,void o({[s]:r});var s,r},placeholder:a.placeholder,className:"min-h-[60px]"})})]},a.id)))]})]})},J=({data:a,onChange:n})=>{const[c,d]=R.useState(!1),[m,h]=R.useState([]),x=(e,s)=>{n({sinaisVitais:{...a.sinaisVitais,[e]:s}})},b=(e,s)=>{n({abdome:{...a.abdome,[e]:s}})},N=e=>{switch(e){case"sinaisVitais":const s=a.sinaisVitais;return!!(s.fc||s.fr||s.pa||s.temp||s.sat);case"abdome":const r=a.abdome;return!!(r.distensao||r.rhaAumentados||r.dorPalpacao||r.defesaInvoluntaria||r.massaPalpavel||r.detalhes);default:return!!a[e]}},y=[{id:"estadoGeral",title:"Estado Geral",placeholder:c?"Apenas descreva alterações. Ex: 'prostrado, desidratado'":"BEG, LOTE, corado, hidratado, acianótico, anictérico...",hasData:!!a.estadoGeral},{id:"sinaisVitais",title:"Sinais Vitais",hasData:N("sinaisVitais")},{id:"cabecaPescoco",title:"Cabeça e Pescoço",placeholder:c?"Apenas descreva alterações. Ex: 'otoscopia com hiperemia à direita'":"Pupilas isofotorreagentes, mucosas úmidas, orofaringe sem alterações...",hasData:!!a.cabecaPescoco},{id:"torax",title:"Tórax",placeholder:c?"Apenas descreva alterações. Ex: 'estertores crepitantes em base direita'":"Simétrico, expansibilidade preservada, murmúrio vesicular presente bilateralmente sem ruídos adventícios...",hasData:!!a.torax},{id:"abdome",title:"Abdome",hasData:N("abdome")},{id:"geniturinario",title:"Geniturinário",placeholder:c?"Apenas descreva alterações. Ex: 'fimose'":"Genitália externa típica para idade e sexo, sem alterações...",hasData:!!a.geniturinario},{id:"locomocao",title:"Locomoção",placeholder:c?"Apenas descreva alterações. Ex: 'claudicação à direita'":"Marcha preservada, sem alterações, força e tônus muscular preservados...",hasData:!!a.locomocao},{id:"neurologico",title:"Neurológico",placeholder:c?"Apenas descreva alterações. Ex: 'rigidez de nuca'":"Glasgow 15, sem alterações neurológicas, sem sinais meníngeos...",hasData:!!a.neurologico},{id:"peleMucosas",title:"Pele e Mucosas",placeholder:c?"Apenas descreva alterações. Ex: 'lesões eritematosas em região torácica'":"Pele íntegra, sem lesões, mucosas úmidas e coradas...",hasData:!!a.peleMucosas}];return e.jsxs(s,{children:[e.jsx(r,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-5 w-5 text-primary"}),"Exame Físico"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Apenas alterações"}),e.jsx(O,{checked:c,onCheckedChange:d,className:"data-[state=checked]:bg-primary"}),e.jsx(G,{children:e.jsxs(_,{children:[e.jsx(B,{asChild:!0,children:e.jsx(u,{className:"h-4 w-4 text-gray-400 cursor-help"})}),e.jsx(Q,{children:e.jsx("p",{className:"w-[220px] text-xs",children:'No modo "Apenas alterações", você só precisa descrever o que está alterado. Os campos vazios serão preenchidos com valores normais.'})})]})})]})]})}),e.jsxs(t,{className:"space-y-6",children:[c?e.jsxs("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start",children:[e.jsx(v,{className:"h-5 w-5 text-amber-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-amber-800",children:"Modo rápido ativado"}),e.jsx("p",{className:"text-sm text-amber-700",children:"Preencha apenas os achados alterados. Os campos vazios serão automaticamente preenchidos com valores normais para a idade e gênero do paciente."})]})]}):null,y.map((s=>e.jsxs(z,{className:"border rounded-md",open:m.includes(s.id),onOpenChange:()=>{return e=s.id,void h((a=>a.includes(e)?a.filter((a=>a!==e)):[...a,e]));var e},children:[e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs(l,{className:"font-medium",children:[s.title,s.hasData&&e.jsxs("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary",children:[e.jsx(W,{className:"h-3 w-3 mr-1"}),"Preenchido"]})]}),e.jsx(q,{asChild:!0,children:e.jsx(j,{variant:"ghost",size:"sm",children:e.jsx(g,{className:"h-4 w-4"})})})]}),e.jsx(V,{className:"px-4 pb-4",children:"sinaisVitais"===s.id?e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"fc",children:"FC"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(o,{id:"fc",value:a.sinaisVitais.fc,onChange:e=>x("fc",e.target.value),placeholder:"80"}),e.jsx("span",{className:"ml-2",children:"bpm"})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"fr",children:"FR"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(o,{id:"fr",value:a.sinaisVitais.fr,onChange:e=>x("fr",e.target.value),placeholder:"20"}),e.jsx("span",{className:"ml-2",children:"irpm"})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"pa",children:"PA"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(o,{id:"pa",value:a.sinaisVitais.pa,onChange:e=>x("pa",e.target.value),placeholder:"120x80"}),e.jsx("span",{className:"ml-2",children:"mmHg"})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"temp",children:"Temp"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(o,{id:"temp",value:a.sinaisVitais.temp,onChange:e=>x("temp",e.target.value),placeholder:"36.5"}),e.jsx("span",{className:"ml-2",children:"°C"})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"sat",children:"Sat"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(o,{id:"sat",value:a.sinaisVitais.sat,onChange:e=>x("sat",e.target.value),placeholder:"98"}),e.jsx("span",{className:"ml-2",children:"%"})]})]})]}):"abdome"===s.id?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap gap-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{id:"distensao",checked:a.abdome.distensao,onCheckedChange:e=>b("distensao",e)}),e.jsx(l,{htmlFor:"distensao",children:"Distensão"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{id:"rhaAumentados",checked:a.abdome.rhaAumentados,onCheckedChange:e=>b("rhaAumentados",e)}),e.jsx(l,{htmlFor:"rhaAumentados",children:"RHA aumentados"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{id:"dorPalpacao",checked:a.abdome.dorPalpacao,onCheckedChange:e=>b("dorPalpacao",e)}),e.jsx(l,{htmlFor:"dorPalpacao",children:"Dor à palpação"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{id:"defesaInvoluntaria",checked:a.abdome.defesaInvoluntaria,onCheckedChange:e=>b("defesaInvoluntaria",e)}),e.jsx(l,{htmlFor:"defesaInvoluntaria",children:"Defesa involuntária"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{id:"massaPalpavel",checked:a.abdome.massaPalpavel,onCheckedChange:e=>b("massaPalpavel",e)}),e.jsx(l,{htmlFor:"massaPalpavel",children:"Massa palpável"})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"abdomeDetalhes",children:"Detalhes adicionais"}),e.jsx(p,{id:"abdomeDetalhes",value:a.abdome.detalhes,onChange:e=>b("detalhes",e.target.value),placeholder:c?"Descreva apenas alterações adicionais. Ex: 'dor em FID'":"Outros detalhes sobre o abdome...",className:"min-h-[60px] mt-2"})]})]}):e.jsx(p,{id:s.id,value:a[s.id],onChange:e=>{return a=s.id,r=e.target.value,void n({[a]:r});var a,r},placeholder:s.placeholder,className:"min-h-[60px]"})})]},s.id)))]})]})},K=({anamnese:a,exame:l})=>{const{toast:o}=b(),n=(e,a)=>{navigator.clipboard.writeText(e),o({title:`${a} copiado!`,description:`O texto do ${a.toLowerCase()} foi copiado para a área de transferência.`})},c=a=>{const s=a.split(/(\*\*[^*]+\*\*|\n##[^#]+)/g).filter(Boolean);return e.jsx("div",{className:"space-y-4",children:s.map(((a,s)=>{if(a.startsWith("**")&&a.endsWith("**"))return e.jsx("h3",{className:"text-lg font-bold text-primary mt-6 first:mt-0",children:a.replace(/\*\*/g,"")},s);if(a.startsWith("\n##"))return e.jsx("h3",{className:"text-lg font-bold text-primary mt-6 first:mt-0",children:a.replace(/\n##/,"").trim()},s);{const r=a.split("\n\n").filter(Boolean);return e.jsx("div",{className:"space-y-2",children:r.map(((a,r)=>e.jsx("p",{className:"text-gray-800",children:a.trim()},`${s}-${r}`)))},s)}}))})};return e.jsxs(s,{children:[e.jsx(r,{children:e.jsx(i,{children:"Resultado"})}),e.jsx(t,{children:e.jsxs(N,{defaultValue:"completo",className:"space-y-4",children:[e.jsxs(y,{className:"grid grid-cols-3 w-full",children:[e.jsx(C,{value:"completo",children:"Texto Completo"}),e.jsxs(C,{value:"anamnese",children:[e.jsx(x,{className:"h-4 w-4 mr-2"})," Anamnese"]}),e.jsxs(C,{value:"exame",children:[e.jsx(U,{className:"h-4 w-4 mr-2"})," Exame Físico"]})]}),e.jsx(w,{value:"completo",children:e.jsxs("div",{className:"border rounded-md overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 border-b p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-primary",children:"ANAMNESE"}),e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>n(a,"Anamnese"),className:"flex gap-1 items-center",children:[e.jsx($,{className:"h-4 w-4"})," Copiar"]})]})}),e.jsx(A,{className:"h-48 p-4 bg-white",children:c(a)}),e.jsx("div",{className:"bg-gray-50 border-y p-4 mt-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-primary",children:"EXAME FÍSICO"}),e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>n(l,"Exame Físico"),className:"flex gap-1 items-center",children:[e.jsx($,{className:"h-4 w-4"})," Copiar"]})]})}),e.jsx(A,{className:"h-48 p-4 bg-white",children:c(l)})]})}),e.jsx(w,{value:"anamnese",children:e.jsxs("div",{className:"border rounded-md overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 border-b p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-primary",children:"ANAMNESE"}),e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>n(a,"Anamnese"),className:"flex gap-1 items-center",children:[e.jsx($,{className:"h-4 w-4"})," Copiar"]})]})}),e.jsx(A,{className:"h-96 p-4 bg-white",children:c(a)})]})}),e.jsx(w,{value:"exame",children:e.jsxs("div",{className:"border rounded-md overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 border-b p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-primary",children:"EXAME FÍSICO"}),e.jsxs(j,{variant:"ghost",size:"sm",onClick:()=>n(l,"Exame Físico"),className:"flex gap-1 items-center",children:[e.jsx($,{className:"h-4 w-4"})," Copiar"]})]})}),e.jsx(A,{className:"h-96 p-4 bg-white",children:c(l)})]})})]})})]})},ee=({onSelect:a})=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-primary",children:"Selecione o Tipo de Anamnese"}),e.jsx("p",{className:"text-gray-500 mt-2",children:"Escolha o modelo de anamnese que deseja utilizar"})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[e.jsxs(s,{className:"border-2 border-primary hover:shadow-lg transition-all cursor-pointer",children:[e.jsxs(r,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-5 w-5 text-primary"}),"Anamnese Tradicional"]}),e.jsx(S,{children:"Modelo completo com todas as seções organizadas para pediatria"})]}),e.jsx(t,{children:e.jsxs("ul",{className:"space-y-2 list-disc list-inside text-sm",children:[e.jsx("li",{children:"Identificação completa do paciente"}),e.jsx("li",{children:"História da doença atual detalhada"}),e.jsx("li",{children:"Antecedentes pessoais e familiares"}),e.jsx("li",{children:"Histórico gestacional e neonatal"}),e.jsx("li",{children:"Desenvolvimento neuropsicomotor"}),e.jsx("li",{children:"Exame físico completo por sistemas"})]})}),e.jsx(F,{children:e.jsx(j,{onClick:()=>a("traditional"),className:"w-full",children:"Selecionar Modelo Tradicional"})})]}),e.jsxs(s,{className:"border-2 border-primary hover:shadow-lg transition-all cursor-pointer",children:[e.jsxs(r,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(k,{className:"h-5 w-5 text-primary"}),"Puericultura 1-2 meses"]}),e.jsx(S,{children:"Modelo específico para consulta de 1-2 meses"})]}),e.jsx(t,{children:e.jsxs("ul",{className:"space-y-2 list-disc list-inside text-sm",children:[e.jsx("li",{children:"Dados cadastrais completos"}),e.jsx("li",{children:"Questionário dirigido para a faixa etária"}),e.jsx("li",{children:"Alimentação e amamentação"}),e.jsx("li",{children:"Medicamentos e vacinação"}),e.jsx("li",{children:"Desenvolvimento neuropsicomotor"}),e.jsx("li",{children:"Exame físico específico para a idade"})]})}),e.jsx(F,{children:e.jsx(j,{onClick:()=>a("pediatric_1_2m"),className:"w-full",children:"Selecionar Puericultura 1-2m"})})]}),e.jsxs(s,{className:"border border-muted hover:shadow-lg transition-all cursor-pointer opacity-50",children:[e.jsxs(r,{children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-5 w-5 text-primary"}),"Anamnese Simplificada"]}),e.jsx(S,{children:"Modelo mais conciso com campos essenciais (Em breve)"})]}),e.jsx(t,{children:e.jsxs("ul",{className:"space-y-2 list-disc list-inside text-sm",children:[e.jsx("li",{children:"Identificação básica do paciente"}),e.jsx("li",{children:"Queixa principal e história breve"}),e.jsx("li",{children:"Antecedentes relevantes"}),e.jsx("li",{children:"Exame físico com achados principais"}),e.jsx("li",{children:"Ideal para consultas de rotina"}),e.jsx("li",{children:"Formato mais direto e objetivo"})]})}),e.jsx(F,{children:e.jsx(j,{disabled:!0,variant:"outline",className:"w-full",children:"Em Breve"})})]})]})]}),ae=({data:a,onChange:o})=>{const[n,c]=R.useState(!1),[d,m]=R.useState([]),h=e=>{if(n)return"Apenas descreva alterações relevantes.";switch(e){case"headSymptoms":return"Descreva alterações em crânio/cabeça.";case"mouthSymptoms":return"Descreva alterações na boca.";case"earSymptoms":return"Descreva alterações nos ouvidos.";case"eyeSymptoms":return"Descreva estrabismo (fixo/variável), secreção ocular, etc.";case"breathingIssues":return"Descreva cianose, cansaço às mamadas, tosse, engasgos, etc.";case"feedingIssues":return"Descreva regurgitação, vômitos, sucção, etc.";case"digestionIssues":return"Descreva número de evacuações por dia, consistência, etc.";case"urineStool":return"Descreva frequência da troca de fraldas, características da urina e fezes.";case"behaviorIssues":return"Descreva alterações de comportamento, sonolência, irritabilidade, tremores, espasmos, convulsões, etc.";case"breastfeeding":return"Aleitamento materno exclusivo? Duração e intervalo das mamadas?";case"breastfeedingTechnique":return"Posição, alternância dos seios, dificuldades, ingurgitamento, dor, fissuras, etc.";case"artificialFeeding":return"Tipo de leite, preparo, número de mamadeiras por dia, etc.";case"sleep":return"Onde dorme, posição, quantas horas por dia, etc.";case"careRoutines":return"Banhos, sabonete, talco, cremes de barreira, quem cuida, uso de chupeta, etc.";case"motherMeds":return"Contraceptivos, medicações contínuas usadas pela mãe.";case"babyMeds":return"Medicamentos em uso pelo bebê - qual, dose, tempo, indicação.";case"vaccineReactions":return"Reações adversas a vacinas, indicação de vacinas especiais (CRIE).";case"dnpmObservations":return"Observações sobre o desenvolvimento neuropsicomotor.";default:return"Descreva os achados..."}},f=[{id:"headSymptoms",title:"Crânio/Cabeça",placeholder:h("headSymptoms"),value:a.headSymptoms},{id:"mouthSymptoms",title:"Boca",placeholder:h("mouthSymptoms"),value:a.mouthSymptoms},{id:"earSymptoms",title:"Ouvidos",placeholder:h("earSymptoms"),value:a.earSymptoms},{id:"eyeSymptoms",title:"Olhos",placeholder:h("eyeSymptoms"),value:a.eyeSymptoms},{id:"breathingIssues",title:"Respiração",placeholder:h("breathingIssues"),value:a.breathingIssues},{id:"feedingIssues",title:"Alimentação",placeholder:h("feedingIssues"),value:a.feedingIssues},{id:"digestionIssues",title:"Digestão",placeholder:h("digestionIssues"),value:a.digestionIssues},{id:"urineStool",title:"Urina e Fezes",placeholder:h("urineStool"),value:a.urineStool},{id:"behaviorIssues",title:"Comportamento",placeholder:h("behaviorIssues"),value:a.behaviorIssues},{id:"breastfeeding",title:"Aleitamento Materno",placeholder:h("breastfeeding"),value:a.breastfeeding},{id:"breastfeedingTechnique",title:"Técnica da Amamentação",placeholder:h("breastfeedingTechnique"),value:a.breastfeedingTechnique},{id:"artificialFeeding",title:"Alimentação Artificial",placeholder:h("artificialFeeding"),value:a.artificialFeeding},{id:"sleep",title:"Sono",placeholder:h("sleep"),value:a.sleep},{id:"careRoutines",title:"Rotinas de Cuidado",placeholder:h("careRoutines"),value:a.careRoutines},{id:"motherMeds",title:"Medicamentos da Mãe",placeholder:h("motherMeds"),value:a.motherMeds},{id:"babyMeds",title:"Medicamentos do Bebê",placeholder:h("babyMeds"),value:a.babyMeds},{id:"vaccineReactions",title:"Vacinação",placeholder:h("vaccineReactions"),value:a.vaccineReactions},{id:"dnpmObservations",title:"Desenvolvimento Neuropsicomotor",placeholder:h("dnpmObservations"),value:a.dnpmObservations}];return e.jsxs(s,{children:[e.jsx(r,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-5 w-5 text-primary"}),"Questionário Dirigido - Puericultura 1-2 meses"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Apenas alterações"}),e.jsx(O,{checked:n,onCheckedChange:c,className:"data-[state=checked]:bg-primary"}),e.jsx(G,{children:e.jsxs(_,{children:[e.jsx(B,{asChild:!0,children:e.jsx(u,{className:"h-4 w-4 text-gray-400 cursor-help"})}),e.jsx(Q,{children:e.jsx("p",{className:"w-[220px] text-xs",children:'No modo "Apenas alterações", você só precisa descrever o que está alterado. Os campos vazios serão preenchidos com valores normais para a idade e gênero.'})})]})})]})]})}),e.jsxs(t,{className:"space-y-6",children:[n?e.jsxs("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start",children:[e.jsx(v,{className:"h-5 w-5 text-amber-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-amber-800",children:"Modo rápido ativado"}),e.jsx("p",{className:"text-sm text-amber-700",children:"Preencha apenas os achados alterados. Os campos vazios serão automaticamente preenchidos com valores normais para a idade e gênero do paciente."})]})]}):null,f.map((a=>e.jsxs(z,{className:"border rounded-md",open:d.includes(a.id),onOpenChange:()=>{return e=a.id,void m((a=>a.includes(e)?a.filter((a=>a!==e)):[...a,e]));var e},children:[e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs(l,{htmlFor:a.id,className:"font-medium",children:[a.title,a.value&&e.jsxs("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary",children:[e.jsx(W,{className:"h-3 w-3 mr-1"}),"Preenchido"]})]}),e.jsx(q,{asChild:!0,children:e.jsx(j,{variant:"ghost",size:"sm",children:e.jsx(g,{className:"h-4 w-4"})})})]}),e.jsx(V,{className:"px-4 pb-4",children:e.jsx(p,{id:a.id,value:a.value,onChange:e=>{return s=a.id,r=e.target.value,void o({[s]:r});var s,r},placeholder:a.placeholder,className:"min-h-[60px]"})})]},a.id)))]})]})},se=({data:a,onChange:o})=>{const[n,c]=R.useState(!1),[d,m]=R.useState([]),h=e=>{if(n)return"Apenas descreva alterações relevantes.";switch(e){case"weight":return"Peso (g)/ganho por dia (g/dia) e escore Z";case"height":return"Estatura (cm) e escore Z";case"headCircumference":return"Perímetro cefálico (cm) e escore Z";case"anteriorFontanelle":return"Fontanela anterior (cm x cm) - avaliar tensão, forma losangular";case"posteriorFontanelle":return"Fontanela posterior (cm x cm) - avaliar tensão, fechamento";case"sutures":return"Suturas (quais estão abertas e se há particularidades)";case"heartRate":return"Frequência cardíaca (bpm) - contar em 1 minuto";case"respiratoryRate":return"Frequência respiratória (irpm) - contar em 1 minuto";case"temperature":return"Temperatura (°C)";case"headNeck":return"Cabeça e pescoço - descreva achados";case"redReflex":return"Reflexo vermelho - presente bilateralmente?";case"oropharynx":return"Orofaringe - descreva achados";case"otoscopy":return"Otoscopia - descreva achados";case"cardiovascular":return"Aparelho cardiovascular - descreva achados";case"respiratory":return"Aparelho respiratório - descreva achados";case"abdomen":return"Abdome - descreva achados";case"peripheralPulses":return"Pulsos periféricos - descreva achados";case"genitalia":return"Genitália - descreva achados";case"bcgScar":return"Cicatriz de BCG - descreva";case"ortolaniManeuver":return"Manobra de Ortolani - positiva ou negativa";case"reflexWalking":return"Marcha reflexa - presente ou ausente";case"moro":return"Reflexo de Moro - simétrico? Completo ou incompleto?";case"asimetricTonicNeck":return"Reflexo tônico cervical assimétrico - presente ou ausente";case"followsObjects":return"Segue objeto com o olhar - sim ou não";case"socialSmile":return"Sorriso social - presente ou ausente";case"screeningTests":return"Resultados dos testes do pezinho, orelhinha, linguinha e coraçãozinho";default:return"Descreva os achados..."}},x=[{id:"weight",title:"Peso",placeholder:h("weight"),value:a.weight},{id:"height",title:"Estatura",placeholder:h("height"),value:a.height},{id:"headCircumference",title:"Perímetro Cefálico",placeholder:h("headCircumference"),value:a.headCircumference},{id:"anteriorFontanelle",title:"Fontanela Anterior",placeholder:h("anteriorFontanelle"),value:a.anteriorFontanelle},{id:"posteriorFontanelle",title:"Fontanela Posterior",placeholder:h("posteriorFontanelle"),value:a.posteriorFontanelle},{id:"sutures",title:"Suturas",placeholder:h("sutures"),value:a.sutures},{id:"heartRate",title:"Frequência Cardíaca",placeholder:h("heartRate"),value:a.heartRate},{id:"respiratoryRate",title:"Frequência Respiratória",placeholder:h("respiratoryRate"),value:a.respiratoryRate},{id:"temperature",title:"Temperatura",placeholder:h("temperature"),value:a.temperature},{id:"headNeck",title:"Cabeça e Pescoço",placeholder:h("headNeck"),value:a.headNeck},{id:"redReflex",title:"Reflexo Vermelho",placeholder:h("redReflex"),value:a.redReflex},{id:"oropharynx",title:"Orofaringe",placeholder:h("oropharynx"),value:a.oropharynx},{id:"otoscopy",title:"Otoscopia",placeholder:h("otoscopy"),value:a.otoscopy},{id:"cardiovascular",title:"Aparelho Cardiovascular",placeholder:h("cardiovascular"),value:a.cardiovascular},{id:"respiratory",title:"Aparelho Respiratório",placeholder:h("respiratory"),value:a.respiratory},{id:"abdomen",title:"Abdome",placeholder:h("abdomen"),value:a.abdomen},{id:"peripheralPulses",title:"Pulsos Periféricos",placeholder:h("peripheralPulses"),value:a.peripheralPulses},{id:"genitalia",title:"Genitália",placeholder:h("genitalia"),value:a.genitalia},{id:"bcgScar",title:"Cicatriz de BCG",placeholder:h("bcgScar"),value:a.bcgScar},{id:"ortolaniManeuver",title:"Manobra de Ortolani",placeholder:h("ortolaniManeuver"),value:a.ortolaniManeuver},{id:"reflexWalking",title:"Marcha Reflexa",placeholder:h("reflexWalking"),value:a.reflexWalking},{id:"moro",title:"Reflexo de Moro",placeholder:h("moro"),value:a.moro},{id:"asimetricTonicNeck",title:"Reflexo Tônico Cervical Assimétrico",placeholder:h("asimetricTonicNeck"),value:a.asimetricTonicNeck},{id:"followsObjects",title:"Segue Objetos com o Olhar",placeholder:h("followsObjects"),value:a.followsObjects},{id:"socialSmile",title:"Sorriso Social",placeholder:h("socialSmile"),value:a.socialSmile},{id:"screeningTests",title:"Testes de Triagem",placeholder:h("screeningTests"),value:a.screeningTests}];return e.jsxs(s,{children:[e.jsx(r,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-5 w-5 text-primary"}),"Exame Físico - Puericultura 1-2 meses"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Apenas alterações"}),e.jsx(O,{checked:n,onCheckedChange:c,className:"data-[state=checked]:bg-primary"}),e.jsx(G,{children:e.jsxs(_,{children:[e.jsx(B,{asChild:!0,children:e.jsx(u,{className:"h-4 w-4 text-gray-400 cursor-help"})}),e.jsx(Q,{children:e.jsx("p",{className:"w-[220px] text-xs",children:'No modo "Apenas alterações", você só precisa descrever o que está alterado. Os campos vazios serão preenchidos com valores normais para a idade e gênero.'})})]})})]})]})}),e.jsxs(t,{className:"space-y-6",children:[n?e.jsxs("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start",children:[e.jsx(v,{className:"h-5 w-5 text-amber-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-amber-800",children:"Modo rápido ativado"}),e.jsx("p",{className:"text-sm text-amber-700",children:"Preencha apenas os achados alterados. Os campos vazios serão automaticamente preenchidos com valores normais para a idade e gênero do paciente."})]})]}):null,x.map((a=>e.jsxs(z,{className:"border rounded-md",open:d.includes(a.id),onOpenChange:()=>{return e=a.id,void m((a=>a.includes(e)?a.filter((a=>a!==e)):[...a,e]));var e},children:[e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs(l,{htmlFor:a.id,className:"font-medium",children:[a.title,a.value&&e.jsxs("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary",children:[e.jsx(W,{className:"h-3 w-3 mr-1"}),"Preenchido"]})]}),e.jsx(q,{asChild:!0,children:e.jsx(j,{variant:"ghost",size:"sm",children:e.jsx(g,{className:"h-4 w-4"})})})]}),e.jsx(V,{className:"px-4 pb-4",children:e.jsx(p,{id:a.id,value:a.value,onChange:e=>{return s=a.id,r=e.target.value,void o({[s]:r});var s,r},placeholder:a.placeholder,className:"min-h-[60px]"})})]},a.id)))]})]})},re=()=>{const{toast:a}=b(),[r,i]=R.useState("template"),[t,l]=R.useState(!1),[o,n]=R.useState("traditional"),[c,d]=R.useState({gender:"male",age:0,ageUnit:"years",appointmentType:"",mainComplaint:""}),[m,h]=R.useState({hda:"",perinatais:"",patologicos:"",cirurgicos:"",alergicos:"",medicamentosos:"",vacinacao:"",desenvolvimento:"",alimentacao:"",ambiente:""}),[p,u]=R.useState({headSymptoms:"",mouthSymptoms:"",earSymptoms:"",eyeSymptoms:"",breathingIssues:"",feedingIssues:"",digestionIssues:"",urineStool:"",behaviorIssues:"",breastfeeding:"",breastfeedingTechnique:"",artificialFeeding:"",sleep:"",careRoutines:"",motherMeds:"",babyMeds:"",vaccineReactions:"",dnpmObservations:""}),[v,g]=R.useState({estadoGeral:"",sinaisVitais:{fc:"",fr:"",pa:"",temp:"",sat:""},cabecaPescoco:"",torax:"",abdome:{distensao:!1,rhaAumentados:!1,dorPalpacao:!1,defesaInvoluntaria:!1,massaPalpavel:!1,detalhes:""},geniturinario:"",locomocao:"",neurologico:"",peleMucosas:""}),[f,A]=R.useState({weight:"",height:"",headCircumference:"",anteriorFontanelle:"",posteriorFontanelle:"",sutures:"",heartRate:"",respiratoryRate:"",temperature:"",headNeck:"",redReflex:"",oropharynx:"",otoscopy:"",cardiovascular:"",respiratory:"",abdomen:"",peripheralPulses:"",genitalia:"",bcgScar:"",ortolaniManeuver:"",reflexWalking:"",moro:"",asimetricTonicNeck:"",followsObjects:"",socialSmile:"",screeningTests:""}),[S,F]=R.useState({anamnese:"",exame:""}),k=async()=>{l(!0);try{if(0===c.age)return a({title:"Erro ao gerar texto",description:"Por favor, informe a idade do paciente.",variant:"destructive"}),void l(!1);const e={patient:c,templateType:o};"traditional"===o?(e.anamnese=m,e.exame=v):"pediatric_1_2m"===o?(e.pediatric=p,e.exame=f):(e.anamnese=m,e.exame=v);const{data:s,error:r}=await M.functions.invoke("generate-anamnese",{body:e});if(r)throw new Error(r.message);if(!s.anamnese||!s.exame)throw new Error("A resposta da IA está incompleta ou mal formatada");F({anamnese:s.anamnese,exame:s.exame}),i("resultado"),a({title:"Texto gerado com sucesso!",description:"O texto foi gerado usando inteligência artificial para complementar os campos não preenchidos."})}catch(e){a({title:"Erro ao gerar texto",description:e instanceof Error?e.message:"Ocorreu um erro ao processar sua solicitação. Tente novamente.",variant:"destructive"})}finally{l(!1)}};return e.jsx(s,{className:"p-6 shadow-md",children:e.jsxs(N,{value:r,onValueChange:i,className:"space-y-6",children:[e.jsxs(y,{className:"grid grid-cols-5 w-full",children:[e.jsx(C,{value:"template",children:"Tipo de Atendimento"}),e.jsx(C,{value:"perfil",children:"Perfil do Paciente"}),"traditional"===o?e.jsxs(e.Fragment,{children:[e.jsx(C,{value:"anamnese",children:"Anamnese"}),e.jsx(C,{value:"exame",children:"Exame Físico"})]}):"pediatric_1_2m"===o?e.jsxs(e.Fragment,{children:[e.jsx(C,{value:"pediatric",children:"Questionário"}),e.jsx(C,{value:"pediatricExame",children:"Exame Físico"})]}):e.jsxs(e.Fragment,{children:[e.jsx(C,{value:"anamnese",children:"Anamnese"}),e.jsx(C,{value:"exame",children:"Exame Físico"})]}),e.jsx(C,{value:"resultado",children:"Resultado"})]}),e.jsx(w,{value:"template",className:"space-y-6",children:e.jsx(ee,{onSelect:e=>{n(e),i("perfil"),"pediatric_1_2m"===e&&d({...c,age:1,ageUnit:"months"}),a({title:"traditional"===e?"Modelo de anamnese tradicional selecionado":"pediatric_1_2m"===e?"Modelo de puericultura 1-2 meses selecionado":"Modelo de anamnese simplificada selecionado",description:"Continue preenchendo os dados do paciente."})}})}),e.jsxs(w,{value:"perfil",className:"space-y-6",children:[e.jsx(X,{data:c,onChange:e=>{d({...c,...e})},templateType:o}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(j,{variant:"outline",onClick:()=>i("template"),children:"Voltar: Tipo de Atendimento"}),e.jsxs(j,{onClick:()=>{"traditional"===o?i("anamnese"):"pediatric_1_2m"===o&&i("pediatric")},children:["Próximo: ","traditional"===o?"Anamnese":"Questionário"]})]})]}),e.jsxs(w,{value:"anamnese",className:"space-y-6",children:[e.jsx(Y,{data:m,onChange:e=>{h({...m,...e})},templateType:o}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(j,{variant:"outline",onClick:()=>i("perfil"),children:"Voltar: Perfil"}),e.jsx(j,{onClick:()=>i("exame"),children:"Próximo: Exame Físico"})]})]}),e.jsxs(w,{value:"pediatric",className:"space-y-6",children:[e.jsx(ae,{data:p,onChange:e=>{u({...p,...e})}}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(j,{variant:"outline",onClick:()=>i("perfil"),children:"Voltar: Perfil"}),e.jsx(j,{onClick:()=>i("pediatricExame"),children:"Próximo: Exame Físico"})]})]}),e.jsxs(w,{value:"exame",className:"space-y-6",children:[e.jsx(J,{data:v,onChange:e=>{g({...v,...e})}}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(j,{variant:"outline",onClick:()=>i("anamnese"),children:"Voltar: Anamnese"}),e.jsx(j,{onClick:k,disabled:t,className:"relative",children:t?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 mr-2 animate-spin"}),"Gerando Texto..."]}):"Gerar Texto"})]})]}),e.jsxs(w,{value:"pediatricExame",className:"space-y-6",children:[e.jsx(se,{data:f,onChange:e=>{A({...f,...e})}}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(j,{variant:"outline",onClick:()=>i("pediatric"),children:"Voltar: Questionário"}),e.jsx(j,{onClick:k,disabled:t,className:"relative",children:t?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 mr-2 animate-spin"}),"Gerando Texto..."]}):"Gerar Texto"})]})]}),e.jsxs(w,{value:"resultado",className:"space-y-6",children:[e.jsx(K,{anamnese:S.anamnese,exame:S.exame}),e.jsxs("div",{className:"flex flex-wrap gap-2 justify-between",children:[e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs(j,{onClick:()=>{const e=`ANAMNESE\n${S.anamnese}\n\nEXAME FÍSICO\n${S.exame}`;navigator.clipboard.writeText(e),a({title:"Texto copiado!",description:"O texto completo foi copiado para a área de transferência."})},className:"flex items-center gap-1",children:[e.jsx($,{className:"h-4 w-4"}),"Copiar tudo"]}),e.jsxs(j,{onClick:()=>{a({title:"Modelo salvo!",description:"Este modelo foi salvo e estará disponível para uso futuro."})},variant:"outline",className:"flex items-center gap-1",children:[e.jsx(H,{className:"h-4 w-4"}),"Salvar como modelo"]}),e.jsxs(j,{onClick:()=>{d({gender:"male",age:0,ageUnit:"years",appointmentType:"",mainComplaint:""}),h({hda:"",perinatais:"",patologicos:"",cirurgicos:"",alergicos:"",medicamentosos:"",vacinacao:"",desenvolvimento:"",alimentacao:"",ambiente:""}),u({headSymptoms:"",mouthSymptoms:"",earSymptoms:"",eyeSymptoms:"",breathingIssues:"",feedingIssues:"",digestionIssues:"",urineStool:"",behaviorIssues:"",breastfeeding:"",breastfeedingTechnique:"",artificialFeeding:"",sleep:"",careRoutines:"",motherMeds:"",babyMeds:"",vaccineReactions:"",dnpmObservations:""}),g({estadoGeral:"",sinaisVitais:{fc:"",fr:"",pa:"",temp:"",sat:""},cabecaPescoco:"",torax:"",abdome:{distensao:!1,rhaAumentados:!1,dorPalpacao:!1,defesaInvoluntaria:!1,massaPalpavel:!1,detalhes:""},geniturinario:"",locomocao:"",neurologico:"",peleMucosas:""}),A({weight:"",height:"",headCircumference:"",anteriorFontanelle:"",posteriorFontanelle:"",sutures:"",heartRate:"",respiratoryRate:"",temperature:"",headNeck:"",redReflex:"",oropharynx:"",otoscopy:"",cardiovascular:"",respiratory:"",abdomen:"",peripheralPulses:"",genitalia:"",bcgScar:"",ortolaniManeuver:"",reflexWalking:"",moro:"",asimetricTonicNeck:"",followsObjects:"",socialSmile:"",screeningTests:""}),F({anamnese:"",exame:""}),i("template"),a({title:"Formulário resetado",description:"Todos os campos foram limpos."})},variant:"outline",className:"flex items-center gap-1",children:[e.jsx(Z,{className:"h-4 w-4"}),"Resetar tudo"]}),e.jsxs(j,{onClick:()=>{a({title:"Modo rápido ativado",description:"Digite apenas os achados alterados e deixe que o sistema complete o resto.",action:e.jsx(j,{onClick:()=>{"traditional"===o?i("anamnese"):"pediatric_1_2m"===o&&i("pediatric")},variant:"outline",size:"sm",children:"Começar"})})},variant:"outline",className:"flex items-center gap-1",children:[e.jsx(W,{className:"h-4 w-4"}),"Modo rápido"]})]}),e.jsxs(j,{onClick:()=>i("template"),variant:"secondary",children:[e.jsx(x,{className:"h-4 w-4 mr-1"}),"Criar nova anamnese"]})]})]})]})})},ie=()=>e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-b from-white via-primary/5 to-primary/10",children:[e.jsxs(E,{children:[e.jsx("title",{children:"Anamnese+ | PedBook - Calculadora Pediátrica"}),e.jsx("meta",{name:"description",content:"Geração automática de textos personalizados de anamnese e exame físico, a partir de inputs simples, com base no perfil do paciente e nos achados alterados."})]}),e.jsx(T,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-6",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"mb-6 space-y-2",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70",children:"Anamnese+"}),e.jsx("p",{className:"text-gray-600",children:"Gere textos personalizados de anamnese e exame físico de forma rápida e eficiente"})]}),e.jsx(re,{})]})}),e.jsx(D,{})]});export{ie as default};
