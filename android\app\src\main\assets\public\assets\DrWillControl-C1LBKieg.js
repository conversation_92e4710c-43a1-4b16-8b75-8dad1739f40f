import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{a7 as a,V as t,Y as l,R as r,W as i,Z as d,S as n,$ as c,U as m,B as o}from"./index-D9amGMlQ.js";import{S as x}from"./switch-UJd64E7P.js";import{A as h,b as p}from"./alert-DF0vYpCj.js";import{L as j}from"./router-BAzpOxbo.js";import{B as u}from"./brain-circuit-_YmeD2N0.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const N=()=>{const{isEnabled:N,lastUpdated:g,updateStatus:f}=(()=>{const[e,a]=s.useState({isEnabled:!0,lastUpdated:null});return s.useEffect((()=>{const e=localStorage.getItem("drWillEnabled"),s=localStorage.getItem("drWillLastUpdated");null!==e&&a({isEnabled:JSON.parse(e),lastUpdated:s?new Date(s):null})}),[]),s.useEffect((()=>{const e=e=>{if("drWillEnabled"===e.key&&null!==e.newValue){const s=JSON.parse(e.newValue),t=localStorage.getItem("drWillLastUpdated");a({isEnabled:s,lastUpdated:t?new Date(t):null})}},s=e=>{a(e.detail)};return window.addEventListener("storage",e),window.addEventListener("drWillStatusChanged",s),()=>{window.removeEventListener("storage",e),window.removeEventListener("drWillStatusChanged",s)}}),[]),{isEnabled:e.isEnabled,lastUpdated:e.lastUpdated,updateStatus:e=>{const s={isEnabled:e,lastUpdated:new Date};a(s),localStorage.setItem("drWillEnabled",JSON.stringify(e)),localStorage.setItem("drWillLastUpdated",s.lastUpdated.toISOString()),window.dispatchEvent(new CustomEvent("drWillStatusChanged",{detail:s}))}}})(),[b,v]=s.useState(!1),w=async()=>{v(!0);try{await new Promise((e=>setTimeout(e,1e3))),f(!N)}catch(e){}finally{v(!1)}};return e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-4xl",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx(j,{to:"/admin",className:"text-blue-600 hover:text-blue-800",children:e.jsx(a,{className:"w-6 h-6"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-purple-100 p-3 rounded-full",children:e.jsx(u,{className:"h-8 w-8 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Controle do Dr. Will"}),e.jsx("p",{className:"text-gray-600",children:"Gerenciar disponibilidade do assistente IA"})]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(h,{className:"border-2 "+(N?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:e.jsxs("div",{className:"flex items-center gap-2",children:[N?e.jsx(t,{className:"w-5 h-5 text-green-600"}):e.jsx(l,{className:"w-5 h-5 text-red-600"}),e.jsxs(p,{className:"font-medium "+(N?"text-green-800":"text-red-800"),children:["Dr. Will está atualmente ",N?"ATIVO":"INATIVO",g&&e.jsxs("span",{className:"block text-sm font-normal mt-1",children:["Última atualização: ",g.toLocaleString("pt-BR")]})]})]})})}),e.jsxs(r,{className:"mb-6",children:[e.jsxs(i,{children:[e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(n,{className:"w-5 h-5"}),"Controle de Disponibilidade"]}),e.jsx(c,{children:"Use o botão abaixo para ativar ou desativar o Dr. Will para todos os usuários"})]}),e.jsxs(m,{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"font-medium",children:"Status do Dr. Will"}),e.jsx("p",{className:"text-sm text-gray-600",children:N?"O assistente está disponível para todos os usuários":"O assistente está temporariamente indisponível"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-sm font-medium "+(N?"text-green-600":"text-red-600"),children:N?"Ativo":"Inativo"}),e.jsx(x,{checked:N,onCheckedChange:w,disabled:b,className:"data-[state=checked]:bg-green-600"})]})]}),e.jsx("div",{className:"flex gap-3",children:e.jsx(o,{onClick:w,disabled:b,variant:N?"destructive":"default",className:"flex-1",children:b?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Processando..."]}):e.jsx(e.Fragment,{children:N?"Desativar Dr. Will":"Ativar Dr. Will"})})})]})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs(r,{children:[e.jsx(i,{children:e.jsx(d,{className:"text-lg",children:"Quando Ativo"})}),e.jsx(m,{children:e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(t,{className:"w-4 h-4 text-green-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Usuários podem fazer perguntas ao Dr. Will"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(t,{className:"w-4 h-4 text-green-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Chat funciona normalmente"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(t,{className:"w-4 h-4 text-green-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Upload de imagens habilitado"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(t,{className:"w-4 h-4 text-green-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Todas as funcionalidades disponíveis"})]})]})})]}),e.jsxs(r,{children:[e.jsx(i,{children:e.jsx(d,{className:"text-lg",children:"Quando Inativo"})}),e.jsx(m,{children:e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(l,{className:"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Banner de aviso exibido"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(l,{className:"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Chat completamente desabilitado"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(l,{className:"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Upload de imagens bloqueado"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx(l,{className:"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:"Mensagem de manutenção exibida"})]})]})})]})]}),e.jsx(r,{className:"mt-6 border-amber-200 bg-amber-50",children:e.jsx(m,{className:"pt-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(l,{className:"w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-amber-800 mb-1",children:"Nota Importante"}),e.jsx("p",{className:"text-sm text-amber-700",children:"As alterações são aplicadas imediatamente. Usuários que estão atualmente usando o Dr. Will verão as mudanças na próxima interação ou ao recarregar a página."})]})]})})})]})};export{N as default};
