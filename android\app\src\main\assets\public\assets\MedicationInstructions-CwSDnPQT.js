import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{u as t}from"./query-vendor-B-7l6Nb3.js";import{R as a,W as i,Z as r,U as o,an as n,ad as l,ae as c,af as d,ag as m,ai as u,aq as h,ar as x,as as p,at as j,aA as b,B as f,L as g,I as N,s as v,d as y,ap as w}from"./index-CrSshpOb.js";import{S}from"./switch-Blnykg1v.js";import{u as _,S as k,L as C,U as L,I as T,E as z}from"./editor-vendor-0G6QaH11.js";import{S as I}from"./save-j3OIC8aV.js";import{H as A,L as $}from"./list-ordered-C1qkrQwG.js";import{H as q,L as B}from"./link-aY87WwX7.js";import{B as M,I as U}from"./italic-D8hkkwnv.js";import{L as H}from"./list-_n1PON_B.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const E=({editor:s})=>{if(!s)return null;const t=e=>s=>{s.preventDefault(),s.stopPropagation(),e()};return e.jsxs("div",{className:"flex items-center gap-1 border-b p-2 flex-wrap",children:[e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleBold().run())),className:s.isActive("bold")?"bg-muted":"",children:e.jsx(M,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleItalic().run())),className:s.isActive("italic")?"bg-muted":"",children:e.jsx(U,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>{const e=window.prompt("URL:");e&&s.chain().focus().setLink({href:e}).run()})),className:s.isActive("link")?"bg-muted":"",children:e.jsx(B,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleBulletList().run())),className:s.isActive("bulletList")?"bg-muted":"",children:e.jsx(H,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>s.chain().focus().toggleOrderedList().run())),className:s.isActive("orderedList")?"bg-muted":"",children:e.jsx($,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>{s.chain().focus().insertContent("<h2><strong>##. Título</strong></h2>").run()})),title:"Adicionar Título (##.)",children:e.jsx(A,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>{s.chain().focus().insertContent("<strong>##; Subtítulo</strong><br><br>").run()})),title:"Adicionar Subtítulo (##;)",children:e.jsx(q,{className:"h-4 w-4"})}),e.jsx(f,{type:"button",variant:"ghost",size:"sm",onClick:t((()=>{const e=window.prompt("URL da imagem:");e&&s.chain().focus().setImage({src:e}).run()})),children:e.jsx(N,{className:"h-4 w-4"})})]})};function F({medicationId:t,medicationName:N,existingInstructions:y,onSuccess:w}){const[$,B]=s.useState(""),[M,U]=s.useState(!1),[H,F]=s.useState(!1),[P,V]=s.useState("edit"),[D,O]=s.useState("simple"===y?.format_type?"simple":"standard"),R=_({extensions:[k,C.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-500 underline"}}),L,T.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg my-4"}})],content:"",onUpdate:({editor:e})=>{B(e.getHTML())}});return s.useEffect((()=>{y&&(B(y.content||""),U(y.is_published||!1),O("simple"===y.format_type?"simple":"standard"),R&&y.content&&R.commands.setContent(y.content))}),[y,R]),e.jsxs(a,{className:"w-full",children:[e.jsx(i,{children:e.jsxs(r,{children:["Bula para ",N]})}),e.jsxs(o,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-2",children:[e.jsx(n,{htmlFor:"format-type",children:"Tipo de formatação:"}),e.jsxs(l,{value:D,onValueChange:e=>O(e),children:[e.jsx(c,{className:"w-[220px]",children:e.jsx(d,{placeholder:"Selecione o tipo"})}),e.jsxs(m,{children:[e.jsx(u,{value:"standard",children:"Padrão (com tamanhos específicos)"}),e.jsx(u,{value:"simple",children:"Simples (apenas marcadores)"})]})]})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-md mb-4 text-sm",children:[e.jsx("h4",{className:"font-semibold text-blue-700 mb-2",children:"Formatação Especial"}),"standard"===D?e.jsxs("ul",{className:"space-y-1 list-disc pl-5",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Título:"})," Use o botão ",e.jsx(A,{className:"inline h-4 w-4"})," para adicionar um título principal (##. Título)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Subtítulo:"})," Use o botão ",e.jsx(q,{className:"inline h-4 w-4"})," para adicionar um subtítulo (##; Subtítulo)"]}),e.jsx("li",{children:e.jsx("em",{children:"Nota: Neste modo, os títulos e subtítulos devem ser criados usando os botões específicos"})})]}):e.jsxs("ul",{className:"space-y-1 list-disc pl-5",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Título:"}),' Digite "##." seguido do título (ex: ##. Título)']}),e.jsxs("li",{children:[e.jsx("strong",{children:"Subtítulo:"}),' Digite "##;" seguido do subtítulo (ex: ##; Subtítulo)']}),e.jsx("li",{children:e.jsx("em",{children:"Nota: Neste modo, apenas os marcadores ##. e ##; são considerados, independente do tamanho da fonte ou formatação"})})]})]}),e.jsxs(h,{defaultValue:"edit",value:P,onValueChange:V,className:"w-full",children:[e.jsxs(x,{children:[e.jsx(p,{value:"edit",children:"Editar"}),e.jsx(p,{value:"preview",children:"Visualizar"})]}),e.jsx(j,{value:"edit",className:"py-4",children:e.jsxs("div",{className:"border rounded-md overflow-hidden",children:[e.jsx(E,{editor:R}),e.jsx(z,{editor:R,className:"prose prose-sm max-w-none p-4 min-h-[400px] focus:outline-none"})]})}),e.jsx(j,{value:"preview",className:"py-4",children:e.jsx("div",{className:"border rounded-md p-4 min-h-[400px] prose max-w-none",children:$?e.jsx("div",{dangerouslySetInnerHTML:{__html:(G=$,G?"simple"===D?(e=>{if(!e)return"";let s=e.replace(/(<[^>]*>)*##\.\s*(.*?)(<\/[^>]*>)*/g,((e,s,t)=>`<h2 class="text-xl font-bold text-blue-600 mt-5 mb-2">${t.replace(/<\/?[^>]+(>|$)/g,"")}</h2>`));return s=s.replace(/##\.\s*[^<\n\r]*(?:<br>|<\/p>|$)/g,""),s=s.replace(/(<[^>]*>)*##;\s*(.*?)(<\/[^>]*>)*/g,((e,s,t)=>`<h4 class="text-base font-semibold text-blue-500 mt-2 mb-1">${t.replace(/<\/?[^>]+(>|$)/g,"")}</h4>`)),s=s.replace(/##;\s*[^<\n\r]*(?:<br>|<\/p>|$)/g,""),s})(G):(e=>e?e.replace(/<h2><strong>##\.\s*(.*?)<\/strong><\/h2>/g,'<h2 class="text-xl font-bold text-blue-600 mt-5 mb-2">$1</h2>').replace(/<h3><strong>##\.\s*(.*?)<\/strong><\/h3>/g,'<h3 class="text-lg font-semibold text-blue-500 mt-3 mb-1">$1</h3>').replace(/<strong>##;\s*(.*?)<\/strong>/g,'<h4 class="text-base font-semibold text-blue-500 mt-2 mb-1">$1</h4>'):"")(G):"")}}):e.jsx("p",{className:"text-muted-foreground italic",children:"Nenhum conteúdo para visualizar"})})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:"publish",checked:M,onCheckedChange:U}),e.jsx(n,{htmlFor:"publish",children:"Publicar bula"})]})]}),e.jsx(b,{className:"flex justify-end",children:e.jsxs(f,{onClick:async()=>{F(!0);try{if(y){const{data:e,error:s}=await v.from("pedbook_medication_instructions").update({content:$,is_published:M,format_type:D,updated_at:(new Date).toISOString()}).eq("id",y.id);if(s)throw s}else{const{data:e,error:s}=await v.from("pedbook_medication_instructions").insert({medication_id:t,content:$,is_published:M,format_type:D});if(s)throw s}w()}catch(e){}finally{F(!1)}},disabled:H||!$.trim(),className:"gap-2",children:[H?e.jsx(g,{className:"h-4 w-4 animate-spin"}):e.jsx(I,{className:"h-4 w-4"}),"Salvar"]})})]});var G}function P(){const[n,l]=s.useState(null),{toast:c}=y(),{data:d,isLoading:m}=t({queryKey:["medications"],queryFn:async()=>{const{data:e,error:s}=await v.from("pedbook_medications").select("id, name, category_id, pedbook_medication_categories(name)").order("name");if(s)throw s;return e}}),{data:u,isLoading:b,refetch:f}=t({queryKey:["medication-instructions",n],queryFn:async()=>{if(!n)return null;const{data:e,error:s}=await v.from("pedbook_medication_instructions").select("*").eq("medication_id",n).maybeSingle();if(s){if("PGRST116"===s.code)return null;throw s}return e},enabled:!!n});return e.jsxs("div",{className:"container py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Gerenciar Bulas de Medicamentos"}),e.jsxs(h,{defaultValue:"list",className:"space-y-4",children:[e.jsxs(x,{children:[e.jsx(p,{value:"list",children:"Selecionar Medicamento"}),n&&e.jsx(p,{value:"edit",children:"Editar Bula"})]}),e.jsx(j,{value:"list",className:"space-y-4",children:e.jsxs(a,{children:[e.jsx(i,{children:e.jsx(r,{children:"Medicamentos"})}),e.jsx(o,{children:m?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(g,{className:"h-8 w-8 animate-spin text-primary"})}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",children:d?.map((s=>e.jsx(a,{className:"cursor-pointer hover:shadow-md transition-shadow "+(s.id===n?"border-primary border-2":""),onClick:()=>{return e=s.id,void l(e);var e},children:e.jsxs(o,{className:"p-4 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.name}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.pedbook_medication_categories?.name||"Sem categoria"})]}),s.id===n&&e.jsx(w,{className:"h-5 w-5 text-primary"})]})},s.id)))})})]})}),e.jsx(j,{value:"edit",children:n&&e.jsx(F,{medicationId:n,medicationName:(()=>{if(!n||!d)return"";const e=d.find((e=>e.id===n));return e?.name||""})(),existingInstructions:u,onSuccess:()=>{f(),c({title:"Bula salva com sucesso",description:"O conteúdo da bula foi atualizado"})}})})]})]})}export{P as default};
