import{j as e}from"./radix-core-6kBL75b5.js";import{c as a,al as s,am as t,s as r,ak as i,R as o,W as l,Z as d,U as n,an as c,ad as m,ae as h,af as u,ag as x,ai as p,B as f,ao as j,a5 as v,ap as g,T as N}from"./index-CFnD44mG.js";import{r as y}from"./critical-DVX9Inzy.js";import{A as w,b}from"./alert-DRNmYOQS.js";import{R as q}from"./refresh-cw-Brjmhzft.js";import{C as S}from"./circle-check-CtsmY2z0.js";import{A}from"./arrow-down-w6pwFTha.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=a("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),C=()=>{const[a,C]=y.useState(!1),[k,_]=y.useState(!1),[z,F]=y.useState([]),[T,Q]=y.useState([]),[O,P]=y.useState(""),[R,$]=y.useState(""),[I,M]=y.useState([]),[V,B]=y.useState([]),[L,U]=y.useState(10),[W,Z]=y.useState(0),[D,G]=y.useState(0),[H,J]=y.useState(0),[K,X]=y.useState(0),Y=s(),ee=t();y.useEffect((()=>{ae()}),[]),y.useEffect((()=>{O?se(O):(Q([]),$(""))}),[O]),y.useEffect((()=>{R?te():(M([]),B([]),J(0),X(0))}),[R]);const ae=async()=>{C(!0);try{const{data:e,error:a}=await r.from("study_categories").select("*").eq("type","specialty").order("name");if(a)throw a;F(e||[])}catch(e){toast({title:"Erro ao carregar especialidades",description:"Não foi possível carregar as especialidades. Tente novamente mais tarde.",variant:"destructive"})}finally{C(!1)}},se=async e=>{C(!0);try{const{data:a,error:s}=await r.from("study_categories").select("*").eq("type","theme").eq("parent_id",e).order("name");if(s)throw s;Q(a||[])}catch(a){toast({title:"Erro ao carregar temas",description:"Não foi possível carregar os temas. Tente novamente mais tarde.",variant:"destructive"})}finally{C(!1)}},te=async()=>{C(!0);try{const{count:e,error:a}=await r.from("questions").select("*",{count:"exact",head:!0}).eq("theme_id",R),{count:s,error:t}=await r.from("questions").select("*",{count:"exact",head:!0}).eq("theme_id",R).not("original_statement","is",null);if(a||t)throw a||t;J(e||0),X(s||0)}catch(e){ee("Erro ao carregar estatísticas","Não foi possível carregar as estatísticas das questões.")}finally{C(!1)}},re=async()=>{if(R){C(!0),M([]),B([]);try{const{data:e,error:a}=await r.from("questions").select("id, statement, original_statement").eq("theme_id",R).is("original_statement",null).order("created_at",{ascending:!1}).limit(L);if(a)throw a;M(e||[]),Z(e?.length||0),0===e?.length&&toast({title:"Sem questões para formatar",description:"Todas as questões deste tema já foram formatadas."})}catch(e){toast({title:"Erro ao carregar questões",description:"Não foi possível carregar as questões para formatação.",variant:"destructive"})}finally{C(!1)}}else ee("Tema não selecionado","Por favor, selecione um tema para carregar questões.")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(w,{variant:"default",className:"bg-amber-50 border-amber-200",children:[e.jsx(i,{className:"h-5 w-5 text-amber-500"}),e.jsx(b,{className:"text-amber-700",children:"Esta ferramenta permite melhorar a formatação visual dos enunciados de questões, sem alterar o conteúdo ou significado."})]}),e.jsxs(o,{children:[e.jsx(l,{children:e.jsx(d,{children:"Selecionar Questões"})}),e.jsxs(n,{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"specialty",children:"Especialidade"}),e.jsxs(m,{value:O,onValueChange:P,disabled:a,children:[e.jsx(h,{id:"specialty",children:e.jsx(u,{placeholder:"Selecione uma especialidade"})}),e.jsx(x,{children:z.map((a=>e.jsx(p,{value:a.id,children:a.name},a.id)))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"theme",children:"Tema"}),e.jsxs(m,{value:R,onValueChange:$,disabled:a||!O,children:[e.jsx(h,{id:"theme",children:e.jsx(u,{placeholder:"Selecione um tema"})}),e.jsx(x,{children:T.map((a=>e.jsx(p,{value:a.id,children:a.name},a.id)))})]})]})]}),R&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(c,{children:"Status das Questões"}),e.jsxs(f,{variant:"ghost",size:"sm",onClick:te,disabled:a,children:[e.jsx(q,{className:"h-4 w-4 mr-1"}),"Atualizar"]})]}),e.jsx(j,{value:K/H*100,className:"h-2"}),e.jsxs("div",{className:"text-sm text-gray-500",children:[K," de ",H," questões formatadas (",Math.round(K/H*100)||0,"%)"]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-4",children:[e.jsxs("div",{className:"w-full md:w-1/3",children:[e.jsx(c,{htmlFor:"batchSize",children:"Quantidade para formatar"}),e.jsx(v,{id:"batchSize",type:"number",min:"1",max:"50",value:L,onChange:e=>U(parseInt(e.target.value)||10),className:"mt-1"})]}),e.jsxs("div",{className:"flex items-end gap-2 w-full md:w-2/3 mt-4 md:mt-0",children:[e.jsx(f,{onClick:re,disabled:a||k||!R,className:"flex-1",children:"Carregar Questões"}),e.jsx(f,{onClick:async()=>{if(0!==I.length){_(!0),B([]),G(0);try{for(let e=0;e<I.length;e++){const a=I[e],{data:s,error:t}=await r.functions.invoke("enhance-text",{body:{text:a.statement}});if(t)throw t;B((e=>[...e,{id:a.id,originalStatement:a.statement,formattedStatement:s.enhancedText,isApproved:!1,isEditing:!1}])),G(e+1)}Y("Formatação concluída",`${I.length} questões foram formatadas.`)}catch(e){ee("Erro ao formatar questões",e.message||"Ocorreu um erro durante a formatação das questões.")}finally{_(!1)}}else toast({title:"Nenhuma questão para formatar",description:"Carregue questões primeiro antes de formatar.",variant:"destructive"})},disabled:a||k||0===I.length,className:"flex-1",children:"Formatar com IA"})]})]}),I.length>0&&e.jsxs("div",{className:"text-sm text-gray-500",children:[W," questões carregadas para formatação"]}),k&&e.jsx("div",{className:"text-center py-4",children:e.jsxs("div",{className:"flex flex-col items-center space-y-3",children:[e.jsx(q,{className:"animate-spin h-8 w-8 text-primary"}),e.jsxs("div",{children:["Processando ",D," de ",I.length," questões..."]}),e.jsx(j,{value:D/I.length*100,className:"h-2 w-64"})]})})]})]})]}),V.length>0&&e.jsxs(o,{children:[e.jsxs(l,{className:"flex flex-row items-center justify-between",children:[e.jsx(d,{children:"Revisar e Aprovar"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(f,{variant:"outline",onClick:()=>{B((e=>e.map((e=>({...e,isApproved:!0})))))},disabled:k,children:[e.jsx(S,{className:"h-4 w-4 mr-1"}),"Aprovar Todos"]}),e.jsxs(f,{onClick:async()=>{const e=V.filter((e=>e.isApproved));if(0===e.length)return void toast({title:"Nenhuma questão aprovada",description:"Aprove pelo menos uma questão para salvar.",variant:"destructive"});_(!0);let a=0;try{for(const s of e){const{error:e}=await r.from("questions").update({statement:s.formattedStatement,original_statement:s.originalStatement}).eq("id",s.id);e||a++}toast({title:"Questões atualizadas",description:`${a} questões foram atualizadas com sucesso.`}),te(),B([]),M([]),re()}catch(s){toast({title:"Erro ao salvar questões",description:"Ocorreu um erro ao salvar as questões aprovadas.",variant:"destructive"})}finally{_(!1)}},disabled:k||0===V.filter((e=>e.isApproved)).length,children:[e.jsx(g,{className:"h-4 w-4 mr-1"}),"Salvar Aprovados"]})]})]}),e.jsx(n,{className:"space-y-6",children:V.map(((a,s)=>e.jsxs("div",{className:"border rounded-md p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("h3",{className:"font-medium",children:["Questão ",s+1]}),e.jsxs(f,{variant:a.isApproved?"default":"outline",size:"sm",onClick:()=>{return e=a.id,void B((a=>a.map((a=>a.id===e?{...a,isApproved:!a.isApproved}:a))));var e},disabled:k,children:[e.jsx(g,{className:"h-4 w-4 mr-1"}),a.isApproved?"Aprovado":"Aprovar"]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 p-3 rounded-md",children:[e.jsx("h4",{className:"text-sm font-medium mb-1 text-gray-500",children:"Original:"}),e.jsx("pre",{className:"text-sm whitespace-pre-wrap",children:a.originalStatement})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx(A,{className:"h-6 w-6 text-gray-400"})}),e.jsxs("div",{className:"bg-green-50 p-3 rounded-md",children:[e.jsxs("div",{className:"flex justify-between items-center mb-1",children:[e.jsx("h4",{className:"text-sm font-medium text-green-700",children:"Formatado:"}),e.jsxs(f,{variant:"ghost",size:"sm",onClick:()=>{return e=a.id,void B((a=>a.map((a=>a.id===e?{...a,isEditing:!a.isEditing}:a))));var e},disabled:k,children:[e.jsx(E,{className:"h-4 w-4 mr-1"}),a.isEditing?"Concluir":"Editar"]})]}),a.isEditing?e.jsx(N,{value:a.formattedStatement,onChange:e=>{return s=a.id,t=e.target.value,void B((e=>e.map((e=>e.id===s?{...e,formattedStatement:t}:e))));var s,t},className:"min-h-[200px] font-mono text-sm"}):e.jsx("pre",{className:"text-sm whitespace-pre-wrap",children:a.formattedStatement})]})]})]},a.id)))})]})]})},k=()=>e.jsx("div",{className:"container mx-auto py-8",children:e.jsxs(o,{children:[e.jsx(l,{children:e.jsx(d,{children:"Formatação de Questões"})}),e.jsx(n,{children:e.jsx(C,{})})]})});export{k as default};
