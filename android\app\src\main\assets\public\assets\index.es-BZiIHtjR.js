import{c as t,d as e}from"./critical-DVX9Inzy.js";import{_ as r}from"./pdf-vendor-C6iMwFa1.js";import"./supabase-vendor-qi_Ptfv-.js";import"./radix-core-6kBL75b5.js";var i=function(t){return t&&t.Math===Math&&t},n=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof t&&t)||i("object"==typeof t&&t)||function(){return this}()||Function("return this")(),a={},s=function(t){try{return!!t()}catch(e){return!0}},o=!s((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),h=!s((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),u=h,l=Function.prototype.call,c=u?l.bind(l):function(){return l.apply(l,arguments)},f={},g={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,d=p&&!g.call({1:2},1);f.f=d?function(t){var e=p(this,t);return!!e&&e.enumerable}:g;var y,v,m=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},x=h,b=Function.prototype,w=b.call,S=x&&b.bind.bind(w,w),T=x?S:function(t){return function(){return w.apply(t,arguments)}},O=T,A=O({}.toString),C=O("".slice),P=function(t){return C(A(t),8,-1)},E=s,N=P,M=Object,R=T("".split),_=E((function(){return!M("z").propertyIsEnumerable(0)}))?function(t){return"String"===N(t)?R(t,""):M(t)}:M,V=function(t){return null==t},I=V,k=TypeError,L=function(t){if(I(t))throw new k("Can't call method on "+t);return t},D=_,j=L,B=function(t){return D(j(t))},z="object"==typeof document&&document.all,U=void 0===z&&void 0!==z?function(t){return"function"==typeof t||t===z}:function(t){return"function"==typeof t},F=U,H=function(t){return"object"==typeof t?null!==t:F(t)},X=n,Y=U,W=function(t,e){return arguments.length<2?(r=X[t],Y(r)?r:void 0):X[t]&&X[t][e];var r},q=T({}.isPrototypeOf),$=n.navigator,G=$&&$.userAgent,Q=G?String(G):"",Z=n,K=Q,J=Z.process,tt=Z.Deno,et=J&&J.versions||tt&&tt.version,rt=et&&et.v8;rt&&(v=(y=rt.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!v&&K&&(!(y=K.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=K.match(/Chrome\/(\d+)/))&&(v=+y[1]);var it=v,nt=it,at=s,st=n.String,ot=!!Object.getOwnPropertySymbols&&!at((function(){var t=Symbol("symbol detection");return!st(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&nt&&nt<41})),ht=ot&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,ut=W,lt=U,ct=q,ft=Object,gt=ht?function(t){return"symbol"==typeof t}:function(t){var e=ut("Symbol");return lt(e)&&ct(e.prototype,ft(t))},pt=String,dt=function(t){try{return pt(t)}catch(e){return"Object"}},yt=U,vt=dt,mt=TypeError,xt=function(t){if(yt(t))return t;throw new mt(vt(t)+" is not a function")},bt=xt,wt=V,St=function(t,e){var r=t[e];return wt(r)?void 0:bt(r)},Tt=c,Ot=U,At=H,Ct=TypeError,Pt={exports:{}},Et=n,Nt=Object.defineProperty,Mt=function(t,e){try{Nt(Et,t,{value:e,configurable:!0,writable:!0})}catch(r){Et[t]=e}return e},Rt=n,_t=Mt,Vt="__core-js_shared__",It=Pt.exports=Rt[Vt]||_t(Vt,{});(It.versions||(It.versions=[])).push({version:"3.39.0",mode:"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"});var kt=Pt.exports,Lt=kt,Dt=function(t,e){return Lt[t]||(Lt[t]=e||{})},jt=L,Bt=Object,zt=function(t){return Bt(jt(t))},Ut=zt,Ft=T({}.hasOwnProperty),Ht=Object.hasOwn||function(t,e){return Ft(Ut(t),e)},Xt=T,Yt=0,Wt=Math.random(),qt=Xt(1..toString),$t=function(t){return"Symbol("+(void 0===t?"":t)+")_"+qt(++Yt+Wt,36)},Gt=Dt,Qt=Ht,Zt=$t,Kt=ot,Jt=ht,te=n.Symbol,ee=Gt("wks"),re=Jt?te.for||te:te&&te.withoutSetter||Zt,ie=function(t){return Qt(ee,t)||(ee[t]=Kt&&Qt(te,t)?te[t]:re("Symbol."+t)),ee[t]},ne=c,ae=H,se=gt,oe=St,he=TypeError,ue=ie("toPrimitive"),le=function(t,e){if(!ae(t)||se(t))return t;var r,i=oe(t,ue);if(i){if(void 0===e&&(e="default"),r=ne(i,t,e),!ae(r)||se(r))return r;throw new he("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,i;if("string"===e&&Ot(r=t.toString)&&!At(i=Tt(r,t)))return i;if(Ot(r=t.valueOf)&&!At(i=Tt(r,t)))return i;if("string"!==e&&Ot(r=t.toString)&&!At(i=Tt(r,t)))return i;throw new Ct("Can't convert object to primitive value")}(t,e)},ce=gt,fe=function(t){var e=le(t,"string");return ce(e)?e:e+""},ge=H,pe=n.document,de=ge(pe)&&ge(pe.createElement),ye=function(t){return de?pe.createElement(t):{}},ve=ye,me=!o&&!s((function(){return 7!==Object.defineProperty(ve("div"),"a",{get:function(){return 7}}).a})),xe=o,be=c,we=f,Se=m,Te=B,Oe=fe,Ae=Ht,Ce=me,Pe=Object.getOwnPropertyDescriptor;a.f=xe?Pe:function(t,e){if(t=Te(t),e=Oe(e),Ce)try{return Pe(t,e)}catch(r){}if(Ae(t,e))return Se(!be(we.f,t,e),t[e])};var Ee={},Ne=o&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Me=H,Re=String,_e=TypeError,Ve=function(t){if(Me(t))return t;throw new _e(Re(t)+" is not an object")},Ie=o,ke=me,Le=Ne,De=Ve,je=fe,Be=TypeError,ze=Object.defineProperty,Ue=Object.getOwnPropertyDescriptor,Fe="enumerable",He="configurable",Xe="writable";Ee.f=Ie?Le?function(t,e,r){if(De(t),e=je(e),De(r),"function"==typeof t&&"prototype"===e&&"value"in r&&Xe in r&&!r[Xe]){var i=Ue(t,e);i&&i[Xe]&&(t[e]=r.value,r={configurable:He in r?r[He]:i[He],enumerable:Fe in r?r[Fe]:i[Fe],writable:!1})}return ze(t,e,r)}:ze:function(t,e,r){if(De(t),e=je(e),De(r),ke)try{return ze(t,e,r)}catch(i){}if("get"in r||"set"in r)throw new Be("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var Ye=Ee,We=m,qe=o?function(t,e,r){return Ye.f(t,e,We(1,r))}:function(t,e,r){return t[e]=r,t},$e={exports:{}},Ge=o,Qe=Ht,Ze=Function.prototype,Ke=Ge&&Object.getOwnPropertyDescriptor,Je=Qe(Ze,"name"),tr={PROPER:Je&&"something"===function(){}.name,CONFIGURABLE:Je&&(!Ge||Ge&&Ke(Ze,"name").configurable)},er=U,rr=kt,ir=T(Function.toString);er(rr.inspectSource)||(rr.inspectSource=function(t){return ir(t)});var nr,ar,sr,or=rr.inspectSource,hr=U,ur=n.WeakMap,lr=hr(ur)&&/native code/.test(String(ur)),cr=$t,fr=Dt("keys"),gr=function(t){return fr[t]||(fr[t]=cr(t))},pr={},dr=lr,yr=n,vr=H,mr=qe,xr=Ht,br=kt,wr=gr,Sr=pr,Tr="Object already initialized",Or=yr.TypeError,Ar=yr.WeakMap;if(dr||br.state){var Cr=br.state||(br.state=new Ar);Cr.get=Cr.get,Cr.has=Cr.has,Cr.set=Cr.set,nr=function(t,e){if(Cr.has(t))throw new Or(Tr);return e.facade=t,Cr.set(t,e),e},ar=function(t){return Cr.get(t)||{}},sr=function(t){return Cr.has(t)}}else{var Pr=wr("state");Sr[Pr]=!0,nr=function(t,e){if(xr(t,Pr))throw new Or(Tr);return e.facade=t,mr(t,Pr,e),e},ar=function(t){return xr(t,Pr)?t[Pr]:{}},sr=function(t){return xr(t,Pr)}}var Er={set:nr,get:ar,has:sr,enforce:function(t){return sr(t)?ar(t):nr(t,{})},getterFor:function(t){return function(e){var r;if(!vr(e)||(r=ar(e)).type!==t)throw new Or("Incompatible receiver, "+t+" required");return r}}},Nr=T,Mr=s,Rr=U,_r=Ht,Vr=o,Ir=tr.CONFIGURABLE,kr=or,Lr=Er.enforce,Dr=Er.get,jr=String,Br=Object.defineProperty,zr=Nr("".slice),Ur=Nr("".replace),Fr=Nr([].join),Hr=Vr&&!Mr((function(){return 8!==Br((function(){}),"length",{value:8}).length})),Xr=String(String).split("String"),Yr=$e.exports=function(t,e,r){"Symbol("===zr(jr(e),0,7)&&(e="["+Ur(jr(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!_r(t,"name")||Ir&&t.name!==e)&&(Vr?Br(t,"name",{value:e,configurable:!0}):t.name=e),Hr&&r&&_r(r,"arity")&&t.length!==r.arity&&Br(t,"length",{value:r.arity});try{r&&_r(r,"constructor")&&r.constructor?Vr&&Br(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(n){}var i=Lr(t);return _r(i,"source")||(i.source=Fr(Xr,"string"==typeof e?e:"")),t};Function.prototype.toString=Yr((function(){return Rr(this)&&Dr(this).source||kr(this)}),"toString");var Wr,qr=$e.exports,$r=U,Gr=Ee,Qr=qr,Zr=Mt,Kr=function(t,e,r,i){i||(i={});var n=i.enumerable,a=void 0!==i.name?i.name:e;if($r(r)&&Qr(r,a,i),i.global)n?t[e]=r:Zr(e,r);else{try{i.unsafe?t[e]&&(n=!0):delete t[e]}catch(s){}n?t[e]=r:Gr.f(t,e,{value:r,enumerable:!1,configurable:!i.nonConfigurable,writable:!i.nonWritable})}return t},Jr={},ti=Math.ceil,ei=Math.floor,ri=Math.trunc||function(t){var e=+t;return(e>0?ei:ti)(e)},ii=function(t){var e=+t;return e!=e||0===e?0:ri(e)},ni=ii,ai=Math.max,si=Math.min,oi=ii,hi=Math.min,ui=function(t){var e=oi(t);return e>0?hi(e,9007199254740991):0},li=ui,ci=function(t){return li(t.length)},fi=B,gi=ci,pi={indexOf:(Wr=!1,function(t,e,r){var i=fi(t),n=gi(i);if(0===n)return-1;var a=function(t,e){var r=ni(t);return r<0?ai(r+e,0):si(r,e)}(r,n);for(Wr;n>a;a++)if(a in i&&i[a]===e)return a||0;return-1})},di=Ht,yi=B,vi=pi.indexOf,mi=pr,xi=T([].push),bi=function(t,e){var r,i=yi(t),n=0,a=[];for(r in i)!di(mi,r)&&di(i,r)&&xi(a,r);for(;e.length>n;)di(i,r=e[n++])&&(~vi(a,r)||xi(a,r));return a},wi=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Si=bi,Ti=wi.concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(t){return Si(t,Ti)};var Oi={};Oi.f=Object.getOwnPropertySymbols;var Ai=W,Ci=Jr,Pi=Oi,Ei=Ve,Ni=T([].concat),Mi=Ai("Reflect","ownKeys")||function(t){var e=Ci.f(Ei(t)),r=Pi.f;return r?Ni(e,r(t)):e},Ri=Ht,_i=Mi,Vi=a,Ii=Ee,ki=s,Li=U,Di=/#|\.prototype\./,ji=function(t,e){var r=zi[Bi(t)];return r===Fi||r!==Ui&&(Li(e)?ki(e):!!e)},Bi=ji.normalize=function(t){return String(t).replace(Di,".").toLowerCase()},zi=ji.data={},Ui=ji.NATIVE="N",Fi=ji.POLYFILL="P",Hi=ji,Xi=n,Yi=a.f,Wi=qe,qi=Kr,$i=Mt,Gi=function(t,e,r){for(var i=_i(e),n=Ii.f,a=Vi.f,s=0;s<i.length;s++){var o=i[s];Ri(t,o)||r&&Ri(r,o)||n(t,o,a(e,o))}},Qi=Hi,Zi=function(t,e){var r,i,n,a,s,o=t.target,h=t.global,u=t.stat;if(r=h?Xi:u?Xi[o]||$i(o,{}):Xi[o]&&Xi[o].prototype)for(i in e){if(a=e[i],n=t.dontCallGetSet?(s=Yi(r,i))&&s.value:r[i],!Qi(h?i:o+(u?".":"#")+i,t.forced)&&void 0!==n){if(typeof a==typeof n)continue;Gi(a,n)}(t.sham||n&&n.sham)&&Wi(a,"sham",!0),qi(r,i,a,t)}},Ki=n,Ji=Q,tn=P,en=function(t){return Ji.slice(0,t.length)===t},rn=en("Bun/")?"BUN":en("Cloudflare-Workers")?"CLOUDFLARE":en("Deno/")?"DENO":en("Node.js/")?"NODE":Ki.Bun&&"string"==typeof Bun.version?"BUN":Ki.Deno&&"object"==typeof Deno.version?"DENO":"process"===tn(Ki.process)?"NODE":Ki.window&&Ki.document?"BROWSER":"REST",nn="NODE"===rn,an=T,sn=xt,on=H,hn=String,un=TypeError,ln=H,cn=L,fn=function(t){if(function(t){return on(t)||null===t}(t))return t;throw new un("Can't set "+hn(t)+" as a prototype")},gn=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=function(t,e,r){try{return an(sn(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(i){}}(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(i){}return function(r,i){return cn(r),fn(i),ln(r)?(e?t(r,i):r.__proto__=i,r):r}}():void 0),pn=Ee.f,dn=Ht,yn=ie("toStringTag"),vn=function(t,e,r){t&&!r&&(t=t.prototype),t&&!dn(t,yn)&&pn(t,yn,{configurable:!0,value:e})},mn=qr,xn=Ee,bn=W,wn=o,Sn=ie("species"),Tn=q,On=TypeError,An={};An[ie("toStringTag")]="z";var Cn="[object z]"===String(An),Pn=U,En=P,Nn=ie("toStringTag"),Mn=Object,Rn="Arguments"===En(function(){return arguments}()),_n=Cn?En:function(t){var e,r,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(r){}}(e=Mn(t),Nn))?r:Rn?En(e):"Object"===(i=En(e))&&Pn(e.callee)?"Arguments":i},Vn=T,In=s,kn=U,Ln=_n,Dn=or,jn=function(){},Bn=W("Reflect","construct"),zn=/^\s*(?:class|function)\b/,Un=Vn(zn.exec),Fn=!zn.test(jn),Hn=function(t){if(!kn(t))return!1;try{return Bn(jn,[],t),!0}catch(e){return!1}},Xn=function(t){if(!kn(t))return!1;switch(Ln(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Fn||!!Un(zn,Dn(t))}catch(e){return!0}};Xn.sham=!0;var Yn,Wn,qn,$n,Gn=!Bn||In((function(){var t;return Hn(Hn.call)||!Hn(Object)||!Hn((function(){t=!0}))||t}))?Xn:Hn,Qn=Gn,Zn=dt,Kn=TypeError,Jn=Ve,ta=V,ea=ie("species"),ra=function(t,e){var r,i=Jn(t).constructor;return void 0===i||ta(r=Jn(i)[ea])?e:function(t){if(Qn(t))return t;throw new Kn(Zn(t)+" is not a constructor")}(r)},ia=h,na=Function.prototype,aa=na.apply,sa=na.call,oa="object"==typeof Reflect&&Reflect.apply||(ia?sa.bind(aa):function(){return sa.apply(aa,arguments)}),ha=P,ua=T,la=function(t){if("Function"===ha(t))return ua(t)},ca=xt,fa=h,ga=la(la.bind),pa=function(t,e){return ca(t),void 0===e?t:fa?ga(t,e):function(){return t.apply(e,arguments)}},da=W("document","documentElement"),ya=T([].slice),va=TypeError,ma=/(?:ipad|iphone|ipod).*applewebkit/i.test(Q),xa=n,ba=oa,wa=pa,Sa=U,Ta=Ht,Oa=s,Aa=da,Ca=ya,Pa=ye,Ea=ma,Na=nn,Ma=xa.setImmediate,Ra=xa.clearImmediate,_a=xa.process,Va=xa.Dispatch,Ia=xa.Function,ka=xa.MessageChannel,La=xa.String,Da=0,ja={},Ba="onreadystatechange";Oa((function(){Yn=xa.location}));var za=function(t){if(Ta(ja,t)){var e=ja[t];delete ja[t],e()}},Ua=function(t){return function(){za(t)}},Fa=function(t){za(t.data)},Ha=function(t){xa.postMessage(La(t),Yn.protocol+"//"+Yn.host)};Ma&&Ra||(Ma=function(t){!function(t,e){if(t<e)throw new va("Not enough arguments")}(arguments.length,1);var e=Sa(t)?t:Ia(t),r=Ca(arguments,1);return ja[++Da]=function(){ba(e,void 0,r)},Wn(Da),Da},Ra=function(t){delete ja[t]},Na?Wn=function(t){_a.nextTick(Ua(t))}:Va&&Va.now?Wn=function(t){Va.now(Ua(t))}:ka&&!Ea?($n=(qn=new ka).port2,qn.port1.onmessage=Fa,Wn=wa($n.postMessage,$n)):xa.addEventListener&&Sa(xa.postMessage)&&!xa.importScripts&&Yn&&"file:"!==Yn.protocol&&!Oa(Ha)?(Wn=Ha,xa.addEventListener("message",Fa,!1)):Wn=Ba in Pa("script")?function(t){Aa.appendChild(Pa("script"))[Ba]=function(){Aa.removeChild(this),za(t)}}:function(t){setTimeout(Ua(t),0)});var Xa={set:Ma},Ya=n,Wa=o,qa=Object.getOwnPropertyDescriptor,$a=function(){this.head=null,this.tail=null};$a.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}};var Ga,Qa,Za,Ka,Ja,ts=$a,es=/ipad|iphone|ipod/i.test(Q)&&"undefined"!=typeof Pebble,rs=/web0s(?!.*chrome)/i.test(Q),is=n,ns=pa,as=Xa.set,ss=ts,os=ma,hs=es,us=rs,ls=nn,cs=is.MutationObserver||is.WebKitMutationObserver,fs=is.document,gs=is.process,ps=is.Promise,ds=function(t){if(!Wa)return Ya[t];var e=qa(Ya,t);return e&&e.value}("queueMicrotask");if(!ds){var ys=new ss,vs=function(){var t,e;for(ls&&(t=gs.domain)&&t.exit();e=ys.get();)try{e()}catch(r){throw ys.head&&Ga(),r}t&&t.enter()};os||ls||us||!cs||!fs?!hs&&ps&&ps.resolve?((Ka=ps.resolve(void 0)).constructor=ps,Ja=ns(Ka.then,Ka),Ga=function(){Ja(vs)}):ls?Ga=function(){gs.nextTick(vs)}:(as=ns(as,is),Ga=function(){as(vs)}):(Qa=!0,Za=fs.createTextNode(""),new cs(vs).observe(Za,{characterData:!0}),Ga=function(){Za.data=Qa=!Qa}),ds=function(t){ys.head||Ga(),ys.add(t)}}var ms=ds,xs=function(t){try{return{error:!1,value:t()}}catch(e){return{error:!0,value:e}}},bs=n.Promise,ws=n,Ss=bs,Ts=U,Os=Hi,As=or,Cs=ie,Ps=rn,Es=it;Ss&&Ss.prototype;var Ns=Cs("species"),Ms=!1,Rs=Ts(ws.PromiseRejectionEvent),_s={CONSTRUCTOR:Os("Promise",(function(){var t=As(Ss),e=t!==String(Ss);if(!e&&66===Es)return!0;if(!Es||Es<51||!/native code/.test(t)){var r=new Ss((function(t){t(1)})),i=function(t){t((function(){}),(function(){}))};if((r.constructor={})[Ns]=i,!(Ms=r.then((function(){}))instanceof i))return!0}return!(e||"BROWSER"!==Ps&&"DENO"!==Ps||Rs)})),REJECTION_EVENT:Rs,SUBCLASSING:Ms},Vs={},Is=xt,ks=TypeError,Ls=function(t){var e,r;this.promise=new t((function(t,i){if(void 0!==e||void 0!==r)throw new ks("Bad Promise constructor");e=t,r=i})),this.resolve=Is(e),this.reject=Is(r)};Vs.f=function(t){return new Ls(t)};var Ds,js,Bs,zs,Us=Zi,Fs=nn,Hs=n,Xs=c,Ys=Kr,Ws=gn,qs=vn,$s=xt,Gs=U,Qs=H,Zs=ra,Ks=Xa.set,Js=ms,to=xs,eo=ts,ro=Er,io=bs,no=Vs,ao="Promise",so=_s.CONSTRUCTOR,oo=_s.REJECTION_EVENT,ho=_s.SUBCLASSING,uo=ro.getterFor(ao),lo=ro.set,co=io&&io.prototype,fo=io,go=co,po=Hs.TypeError,yo=Hs.document,vo=Hs.process,mo=no.f,xo=mo,bo=!!(yo&&yo.createEvent&&Hs.dispatchEvent),wo="unhandledrejection",So=function(t){var e;return!(!Qs(t)||!Gs(e=t.then))&&e},To=function(t,e){var r,i,n,a=e.value,s=1===e.state,o=s?t.ok:t.fail,h=t.resolve,u=t.reject,l=t.domain;try{o?(s||(2===e.rejection&&Eo(e),e.rejection=1),!0===o?r=a:(l&&l.enter(),r=o(a),l&&(l.exit(),n=!0)),r===t.promise?u(new po("Promise-chain cycle")):(i=So(r))?Xs(i,r,h,u):h(r)):u(a)}catch(c){l&&!n&&l.exit(),u(c)}},Oo=function(t,e){t.notified||(t.notified=!0,Js((function(){for(var r,i=t.reactions;r=i.get();)To(r,t);t.notified=!1,e&&!t.rejection&&Co(t)})))},Ao=function(t,e,r){var i,n;bo?((i=yo.createEvent("Event")).promise=e,i.reason=r,i.initEvent(t,!1,!0),Hs.dispatchEvent(i)):i={promise:e,reason:r},!oo&&(n=Hs["on"+t])&&n(i)},Co=function(t){Xs(Ks,Hs,(function(){var e,r=t.facade,i=t.value;if(Po(t)&&(e=to((function(){Fs?vo.emit("unhandledRejection",i,r):Ao(wo,r,i)})),t.rejection=Fs||Po(t)?2:1,e.error))throw e.value}))},Po=function(t){return 1!==t.rejection&&!t.parent},Eo=function(t){Xs(Ks,Hs,(function(){var e=t.facade;Fs?vo.emit("rejectionHandled",e):Ao("rejectionhandled",e,t.value)}))},No=function(t,e,r){return function(i){t(e,i,r)}},Mo=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,Oo(t,!0))},Ro=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new po("Promise can't be resolved itself");var i=So(e);i?Js((function(){var r={done:!1};try{Xs(i,e,No(Ro,r,t),No(Mo,r,t))}catch(n){Mo(r,n,t)}})):(t.value=e,t.state=1,Oo(t,!1))}catch(n){Mo({done:!1},n,t)}}};if(so&&(go=(fo=function(t){(function(t,e){if(Tn(e,t))return t;throw new On("Incorrect invocation")})(this,go),$s(t),Xs(Ds,this);var e=uo(this);try{t(No(Ro,e),No(Mo,e))}catch(r){Mo(e,r)}}).prototype,(Ds=function(t){lo(this,{type:ao,done:!1,notified:!1,parent:!1,reactions:new eo,rejection:!1,state:0,value:null})}).prototype=Ys(go,"then",(function(t,e){var r=uo(this),i=mo(Zs(this,fo));return r.parent=!0,i.ok=!Gs(t)||t,i.fail=Gs(e)&&e,i.domain=Fs?vo.domain:void 0,0===r.state?r.reactions.add(i):Js((function(){To(i,r)})),i.promise})),js=function(){var t=new Ds,e=uo(t);this.promise=t,this.resolve=No(Ro,e),this.reject=No(Mo,e)},no.f=mo=function(t){return t===fo||void 0===t?new js(t):xo(t)},Gs(io)&&co!==Object.prototype)){Bs=co.then,ho||Ys(co,"then",(function(t,e){var r=this;return new fo((function(t,e){Xs(Bs,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete co.constructor}catch(vv){}Ws&&Ws(co,go)}Us({global:!0,constructor:!0,wrap:!0,forced:so},{Promise:fo}),qs(fo,ao,!1),zs=bn(ao),wn&&zs&&!zs[Sn]&&function(t,e,r){r.get&&mn(r.get,e,{getter:!0}),r.set&&mn(r.set,e,{setter:!0}),xn.f(t,e,r)}(zs,Sn,{configurable:!0,get:function(){return this}});var _o={},Vo=_o,Io=ie("iterator"),ko=Array.prototype,Lo=_n,Do=St,jo=V,Bo=_o,zo=ie("iterator"),Uo=function(t){if(!jo(t))return Do(t,zo)||Do(t,"@@iterator")||Bo[Lo(t)]},Fo=c,Ho=xt,Xo=Ve,Yo=dt,Wo=Uo,qo=TypeError,$o=c,Go=Ve,Qo=St,Zo=pa,Ko=c,Jo=Ve,th=dt,eh=ci,rh=q,ih=Uo,nh=function(t,e,r){var i,n;Go(t);try{if(!(i=Qo(t,"return"))){if("throw"===e)throw r;return r}i=$o(i,t)}catch(vv){n=!0,i=vv}if("throw"===e)throw r;if(n)throw i;return Go(i),r},ah=TypeError,sh=function(t,e){this.stopped=t,this.result=e},oh=sh.prototype,hh=function(t,e,r){var i,n,a,s,o,h,u,l,c=r&&r.that,f=!(!r||!r.AS_ENTRIES),g=!(!r||!r.IS_RECORD),p=!(!r||!r.IS_ITERATOR),d=!(!r||!r.INTERRUPTED),y=Zo(e,c),v=function(t){return i&&nh(i,"normal",t),new sh(!0,t)},m=function(t){return f?(Jo(t),d?y(t[0],t[1],v):y(t[0],t[1])):d?y(t,v):y(t)};if(g)i=t.iterator;else if(p)i=t;else{if(!(n=ih(t)))throw new ah(th(t)+" is not iterable");if(void 0!==(l=n)&&(Vo.Array===l||ko[Io]===l)){for(a=0,s=eh(t);s>a;a++)if((o=m(t[a]))&&rh(oh,o))return o;return new sh(!1)}i=function(t,e){var r=arguments.length<2?Wo(t):e;if(Ho(r))return Xo(Fo(r,t));throw new qo(Yo(t)+" is not iterable")}(t,n)}for(h=g?t.next:i.next;!(u=Ko(h,i)).done;){try{o=m(u.value)}catch(vv){nh(i,"throw",vv)}if("object"==typeof o&&o&&rh(oh,o))return o}return new sh(!1)},uh=ie("iterator"),lh=!1;try{var ch=0,fh={next:function(){return{done:!!ch++}},return:function(){lh=!0}};fh[uh]=function(){return this},Array.from(fh,(function(){throw 2}))}catch(vv){}var gh=bs,ph=_s.CONSTRUCTOR||!function(){try{if(!lh)return!1}catch(vv){return!1}var t,e=!1;try{var r={};r[uh]=function(){return{next:function(){return{done:e=!0}}}},t=r,gh.all(t).then(void 0,(function(){}))}catch(vv){}return e}(),dh=c,yh=xt,vh=Vs,mh=xs,xh=hh;Zi({target:"Promise",stat:!0,forced:ph},{all:function(t){var e=this,r=vh.f(e),i=r.resolve,n=r.reject,a=mh((function(){var r=yh(e.resolve),a=[],s=0,o=1;xh(t,(function(t){var h=s++,u=!1;o++,dh(r,e,t).then((function(t){u||(u=!0,a[h]=t,--o||i(a))}),n)})),--o||i(a)}));return a.error&&n(a.value),r.promise}});var bh=Zi,wh=_s.CONSTRUCTOR,Sh=bs,Th=W,Oh=U,Ah=Kr,Ch=Sh&&Sh.prototype;if(bh({target:"Promise",proto:!0,forced:wh,real:!0},{catch:function(t){return this.then(void 0,t)}}),Oh(Sh)){var Ph=Th("Promise").prototype.catch;Ch.catch!==Ph&&Ah(Ch,"catch",Ph,{unsafe:!0})}var Eh=c,Nh=xt,Mh=Vs,Rh=xs,_h=hh;Zi({target:"Promise",stat:!0,forced:ph},{race:function(t){var e=this,r=Mh.f(e),i=r.reject,n=Rh((function(){var n=Nh(e.resolve);_h(t,(function(t){Eh(n,e,t).then(r.resolve,i)}))}));return n.error&&i(n.value),r.promise}});var Vh=Vs;Zi({target:"Promise",stat:!0,forced:_s.CONSTRUCTOR},{reject:function(t){var e=Vh.f(this);return(0,e.reject)(t),e.promise}});var Ih=Ve,kh=H,Lh=Vs,Dh=Zi,jh=_s.CONSTRUCTOR;function Bh(t,e,r,i,n,a,s){try{var o=t[a](s),h=o.value}catch(u){return void r(u)}o.done?e(h):Promise.resolve(h).then(i,n)}function zh(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function s(t){Bh(a,i,n,s,o,"next",t)}function o(t){Bh(a,i,n,s,o,"throw",t)}s(void 0)}))}}W("Promise"),Dh({target:"Promise",stat:!0,forced:jh},{resolve:function(t){return function(t,e){if(Ih(t),kh(e)&&e.constructor===t)return e;var r=Lh.f(t);return(0,r.resolve)(e),r.promise}(this,t)}});var Uh=_n,Fh=String,Hh=function(t){if("Symbol"===Uh(t))throw new TypeError("Cannot convert a Symbol value to a string");return Fh(t)},Xh=Ve,Yh=function(){var t=Xh(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e},Wh=s,qh=n.RegExp,$h=Wh((function(){var t=qh("a","y");return t.lastIndex=2,null!==t.exec("abcd")}));$h||Wh((function(){return!qh("a","y").sticky}));var Gh={BROKEN_CARET:$h||Wh((function(){var t=qh("^r","gy");return t.lastIndex=2,null!==t.exec("str")})),UNSUPPORTED_Y:$h},Qh={},Zh=bi,Kh=wi,Jh=Object.keys||function(t){return Zh(t,Kh)},tu=o,eu=Ne,ru=Ee,iu=Ve,nu=B,au=Jh;Qh.f=tu&&!eu?Object.defineProperties:function(t,e){iu(t);for(var r,i=nu(e),n=au(e),a=n.length,s=0;a>s;)ru.f(t,r=n[s++],i[r]);return t};var su,ou=Ve,hu=Qh,uu=wi,lu=pr,cu=da,fu=ye,gu="prototype",pu="script",du=gr("IE_PROTO"),yu=function(){},vu=function(t){return"<"+pu+">"+t+"</"+pu+">"},mu=function(t){t.write(vu("")),t.close();var e=t.parentWindow.Object;return t=null,e},xu=function(){try{su=new ActiveXObject("htmlfile")}catch(vv){}var t,e,r;xu="undefined"!=typeof document?document.domain&&su?mu(su):(e=fu("iframe"),r="java"+pu+":",e.style.display="none",cu.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(vu("document.F=Object")),t.close(),t.F):mu(su);for(var i=uu.length;i--;)delete xu[gu][uu[i]];return xu()};lu[du]=!0;var bu,wu,Su=Object.create||function(t,e){var r;return null!==t?(yu[gu]=ou(t),r=new yu,yu[gu]=null,r[du]=t):r=xu(),void 0===e?r:hu.f(r,e)},Tu=s,Ou=n.RegExp,Au=Tu((function(){var t=Ou(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})),Cu=s,Pu=n.RegExp,Eu=Cu((function(){var t=Pu("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Nu=c,Mu=T,Ru=Hh,_u=Yh,Vu=Gh,Iu=Su,ku=Er.get,Lu=Au,Du=Eu,ju=Dt("native-string-replace",String.prototype.replace),Bu=RegExp.prototype.exec,zu=Bu,Uu=Mu("".charAt),Fu=Mu("".indexOf),Hu=Mu("".replace),Xu=Mu("".slice),Yu=(wu=/b*/g,Nu(Bu,bu=/a/,"a"),Nu(Bu,wu,"a"),0!==bu.lastIndex||0!==wu.lastIndex),Wu=Vu.BROKEN_CARET,qu=void 0!==/()??/.exec("")[1];(Yu||qu||Wu||Lu||Du)&&(zu=function(t){var e,r,i,n,a,s,o,h=this,u=ku(h),l=Ru(t),c=u.raw;if(c)return c.lastIndex=h.lastIndex,e=Nu(zu,c,l),h.lastIndex=c.lastIndex,e;var f=u.groups,g=Wu&&h.sticky,p=Nu(_u,h),d=h.source,y=0,v=l;if(g&&(p=Hu(p,"y",""),-1===Fu(p,"g")&&(p+="g"),v=Xu(l,h.lastIndex),h.lastIndex>0&&(!h.multiline||h.multiline&&"\n"!==Uu(l,h.lastIndex-1))&&(d="(?: "+d+")",v=" "+v,y++),r=new RegExp("^(?:"+d+")",p)),qu&&(r=new RegExp("^"+d+"$(?!\\s)",p)),Yu&&(i=h.lastIndex),n=Nu(Bu,g?r:h,v),g?n?(n.input=Xu(n.input,y),n[0]=Xu(n[0],y),n.index=h.lastIndex,h.lastIndex+=n[0].length):h.lastIndex=0:Yu&&n&&(h.lastIndex=h.global?n.index+n[0].length:i),qu&&n&&n.length>1&&Nu(ju,n[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(n[a]=void 0)})),n&&f)for(n.groups=s=Iu(null),a=0;a<f.length;a++)s[(o=f[a])[0]]=n[o[1]];return n});var $u=zu;Zi({target:"RegExp",proto:!0,forced:/./.exec!==$u},{exec:$u});var Gu=c,Qu=Kr,Zu=$u,Ku=s,Ju=ie,tl=qe,el=Ju("species"),rl=RegExp.prototype,il=function(t,e,r,i){var n=Ju(t),a=!Ku((function(){var e={};return e[n]=function(){return 7},7!==""[t](e)})),s=a&&!Ku((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[el]=function(){return r},r.flags="",r[n]=/./[n]),r.exec=function(){return e=!0,null},r[n](""),!e}));if(!a||!s||r){var o=/./[n],h=e(n,""[t],(function(t,e,r,i,n){var s=e.exec;return s===Zu||s===rl.exec?a&&!n?{done:!0,value:Gu(o,e,r,i)}:{done:!0,value:Gu(t,r,e,i)}:{done:!1}}));Qu(String.prototype,t,h[0]),Qu(rl,n,h[1])}i&&tl(rl[n],"sham",!0)},nl=T,al=ii,sl=Hh,ol=L,hl=nl("".charAt),ul=nl("".charCodeAt),ll=nl("".slice),cl=function(t,e){var r,i,n=sl(ol(t)),a=al(e),s=n.length;return a<0||a>=s?"":(r=ul(n,a))<55296||r>56319||a+1===s||(i=ul(n,a+1))<56320||i>57343?hl(n,a):ll(n,a,a+2)},fl=function(t,e,r){return e+(r?cl(t,e).length:1)},gl=c,pl=Ve,dl=U,yl=P,vl=$u,ml=TypeError,xl=function(t,e){var r=t.exec;if(dl(r)){var i=gl(r,t,e);return null!==i&&pl(i),i}if("RegExp"===yl(t))return gl(vl,t,e);throw new ml("RegExp#exec called on incompatible receiver")},bl=c,wl=Ve,Sl=V,Tl=ui,Ol=Hh,Al=L,Cl=St,Pl=fl,El=xl;il("match",(function(t,e,r){return[function(e){var r=Al(this),i=Sl(e)?void 0:Cl(e,t);return i?bl(i,e,r):new RegExp(e)[t](Ol(r))},function(t){var i=wl(this),n=Ol(t),a=r(e,i,n);if(a.done)return a.value;if(!i.global)return El(i,n);var s=i.unicode;i.lastIndex=0;for(var o,h=[],u=0;null!==(o=El(i,n));){var l=Ol(o[0]);h[u]=l,""===l&&(i.lastIndex=Pl(n,Tl(i.lastIndex),s)),u++}return 0===u?null:h}]}));var Nl=T,Ml=zt,Rl=Math.floor,_l=Nl("".charAt),Vl=Nl("".replace),Il=Nl("".slice),kl=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ll=/\$([$&'`]|\d{1,2})/g,Dl=oa,jl=c,Bl=T,zl=il,Ul=s,Fl=Ve,Hl=U,Xl=V,Yl=ii,Wl=ui,ql=Hh,$l=L,Gl=fl,Ql=St,Zl=function(t,e,r,i,n,a){var s=r+t.length,o=i.length,h=Ll;return void 0!==n&&(n=Ml(n),h=kl),Vl(a,h,(function(a,h){var u;switch(_l(h,0)){case"$":return"$";case"&":return t;case"`":return Il(e,0,r);case"'":return Il(e,s);case"<":u=n[Il(h,1,-1)];break;default:var l=+h;if(0===l)return a;if(l>o){var c=Rl(l/10);return 0===c?a:c<=o?void 0===i[c-1]?_l(h,1):i[c-1]+_l(h,1):a}u=i[l-1]}return void 0===u?"":u}))},Kl=xl,Jl=ie("replace"),tc=Math.max,ec=Math.min,rc=Bl([].concat),ic=Bl([].push),nc=Bl("".indexOf),ac=Bl("".slice),sc="$0"==="a".replace(/./,"$0"),oc=!!/./[Jl]&&""===/./[Jl]("a","$0");zl("replace",(function(t,e,r){var i=oc?"$":"$0";return[function(t,r){var i=$l(this),n=Xl(t)?void 0:Ql(t,Jl);return n?jl(n,t,i,r):jl(e,ql(i),t,r)},function(t,n){var a=Fl(this),s=ql(t);if("string"==typeof n&&-1===nc(n,i)&&-1===nc(n,"$<")){var o=r(e,a,s,n);if(o.done)return o.value}var h=Hl(n);h||(n=ql(n));var u,l=a.global;l&&(u=a.unicode,a.lastIndex=0);for(var c,f=[];null!==(c=Kl(a,s))&&(ic(f,c),l);)""===ql(c[0])&&(a.lastIndex=Gl(s,Wl(a.lastIndex),u));for(var g,p="",d=0,y=0;y<f.length;y++){for(var v,m=ql((c=f[y])[0]),x=tc(ec(Yl(c.index),s.length),0),b=[],w=1;w<c.length;w++)ic(b,void 0===(g=c[w])?g:String(g));var S=c.groups;if(h){var T=rc([m],b,x,s);void 0!==S&&ic(T,S),v=ql(Dl(n,void 0,T))}else v=Zl(m,s,x,b,S,n);x>=d&&(p+=ac(s,d,x)+v,d=x+m.length)}return p+ac(s,d)}]}),!!Ul((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!sc||oc);var hc,uc=H,lc=P,cc=ie("match"),fc=TypeError,gc=function(t){if(function(t){var e;return uc(t)&&(void 0!==(e=t[cc])?!!e:"RegExp"===lc(t))}(t))throw new fc("The method doesn't accept regular expressions");return t},pc=ie("match"),dc=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[pc]=!1,"/./"[t](e)}catch(i){}}return!1},yc=Zi,vc=la,mc=a.f,xc=ui,bc=Hh,wc=gc,Sc=L,Tc=dc,Oc=vc("".slice),Ac=Math.min,Cc=Tc("startsWith");yc({target:"String",proto:!0,forced:!(!Cc&&(hc=mc(String.prototype,"startsWith"),hc&&!hc.writable)||Cc)},{startsWith:function(t){var e=bc(Sc(this));wc(t);var r=xc(Ac(arguments.length>1?arguments[1]:void 0,e.length)),i=bc(t);return Oc(e,r,r+i.length)===i}});var Pc=ie,Ec=Su,Nc=Ee.f,Mc=Pc("unscopables"),Rc=Array.prototype;void 0===Rc[Mc]&&Nc(Rc,Mc,{configurable:!0,value:Ec(null)});var _c,Vc,Ic,kc=!s((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Lc=Ht,Dc=U,jc=zt,Bc=kc,zc=gr("IE_PROTO"),Uc=Object,Fc=Uc.prototype,Hc=Bc?Uc.getPrototypeOf:function(t){var e=jc(t);if(Lc(e,zc))return e[zc];var r=e.constructor;return Dc(r)&&e instanceof r?r.prototype:e instanceof Uc?Fc:null},Xc=s,Yc=U,Wc=H,qc=Hc,$c=Kr,Gc=ie("iterator"),Qc=!1;[].keys&&("next"in(Ic=[].keys())?(Vc=qc(qc(Ic)))!==Object.prototype&&(_c=Vc):Qc=!0),(!Wc(_c)||Xc((function(){var t={};return _c[Gc].call(t)!==t})))&&(_c={}),Yc(_c[Gc])||$c(_c,Gc,(function(){return this}));var Zc={IteratorPrototype:_c,BUGGY_SAFARI_ITERATORS:Qc},Kc=Zc.IteratorPrototype,Jc=Su,tf=m,ef=vn,rf=_o,nf=function(){return this},af=Zi,sf=c,of=U,hf=Hc,uf=gn,lf=vn,cf=qe,ff=Kr,gf=_o,pf=tr.PROPER,df=tr.CONFIGURABLE,yf=Zc.IteratorPrototype,vf=Zc.BUGGY_SAFARI_ITERATORS,mf=ie("iterator"),xf="keys",bf="values",wf="entries",Sf=function(){return this},Tf=B,Of=function(t){Rc[Mc][t]=!0},Af=_o,Cf=Er,Pf=Ee.f,Ef=function(t,e){return{value:t,done:e}},Nf=o,Mf="Array Iterator",Rf=Cf.set,_f=Cf.getterFor(Mf),Vf=function(t,e,r,i,n,a,s){!function(t,e,r,i){var n=e+" Iterator";t.prototype=Jc(Kc,{next:tf(+!i,r)}),ef(t,n,!1),rf[n]=nf}(r,e,i);var o,h,u,l=function(t){if(t===n&&d)return d;if(!vf&&t&&t in g)return g[t];switch(t){case xf:case bf:case wf:return function(){return new r(this,t)}}return function(){return new r(this)}},c=e+" Iterator",f=!1,g=t.prototype,p=g[mf]||g["@@iterator"]||n&&g[n],d=!vf&&p||l(n),y="Array"===e&&g.entries||p;if(y&&(o=hf(y.call(new t)))!==Object.prototype&&o.next&&(hf(o)!==yf&&(uf?uf(o,yf):of(o[mf])||ff(o,mf,Sf)),lf(o,c,!0)),pf&&n===bf&&p&&p.name!==bf&&(df?cf(g,"name",bf):(f=!0,d=function(){return sf(p,this)})),n)if(h={values:l(bf),keys:a?d:l(xf),entries:l(wf)},s)for(u in h)(vf||f||!(u in g))&&ff(g,u,h[u]);else af({target:e,proto:!0,forced:vf||f},h);return g[mf]!==d&&ff(g,mf,d,{name:n}),gf[e]=d,h}(Array,"Array",(function(t,e){Rf(this,{type:Mf,target:Tf(t),index:0,kind:e})}),(function(){var t=_f(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,Ef(void 0,!0);switch(t.kind){case"keys":return Ef(r,!1);case"values":return Ef(e[r],!1)}return Ef([r,e[r]],!1)}),"values"),If=Af.Arguments=Af.Array;if(Of("keys"),Of("values"),Of("entries"),Nf&&"values"!==If.name)try{Pf(If,"name",{value:"values"})}catch(vv){}var kf=ye("span").classList,Lf=kf&&kf.constructor&&kf.constructor.prototype,Df=Lf===Object.prototype?void 0:Lf,jf=n,Bf={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},zf=Df,Uf=Vf,Ff=qe,Hf=vn,Xf=ie("iterator"),Yf=Uf.values,Wf=function(t,e){if(t){if(t[Xf]!==Yf)try{Ff(t,Xf,Yf)}catch(vv){t[Xf]=Yf}if(Hf(t,e,!0),Bf[e])for(var r in Uf)if(t[r]!==Uf[r])try{Ff(t,r,Uf[r])}catch(vv){t[r]=Uf[r]}}};for(var qf in Bf)Wf(jf[qf]&&jf[qf].prototype,qf);function $f(t,e,i){return(e=function(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e);if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==r(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}Wf(zf,"DOMTokenList");var Gf=xt,Qf=zt,Zf=_,Kf=ci,Jf=TypeError,tg="Reduce of empty array with no initial value",eg={left:function(t,e,r,i){var n=Qf(t),a=Zf(n),s=Kf(n);if(Gf(e),0===s&&r<2)throw new Jf(tg);var o=0;if(r<2)for(;;){if(o in a){i=a[o],o+=1;break}if(s<=(o+=1))throw new Jf(tg)}for(;s>o;o+=1)o in a&&(i=e(i,a[o],o,n));return i}},rg=s,ig=function(t,e){var r=[][t];return!!r&&rg((function(){r.call(null,e||function(){return 1},1)}))},ng=eg.left;Zi({target:"Array",proto:!0,forced:!nn&&it>79&&it<83||!ig("reduce")},{reduce:function(t){var e=arguments.length;return ng(this,t,e,e>1?arguments[1]:void 0)}});var ag=Zi,sg=la,og=a.f,hg=ui,ug=Hh,lg=gc,cg=L,fg=dc,gg=sg("".slice),pg=Math.min,dg=fg("endsWith"),yg=!dg&&!!function(){var t=og(String.prototype,"endsWith");return t&&!t.writable}();ag({target:"String",proto:!0,forced:!yg&&!dg},{endsWith:function(t){var e=ug(cg(this));lg(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===r?i:pg(hg(r),i),a=ug(t);return gg(e,n-a.length,n)===a}});var vg=c,mg=T,xg=il,bg=Ve,wg=V,Sg=L,Tg=ra,Og=fl,Ag=ui,Cg=Hh,Pg=St,Eg=xl,Ng=s,Mg=Gh.UNSUPPORTED_Y,Rg=Math.min,_g=mg([].push),Vg=mg("".slice),Ig=!Ng((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),kg="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;xg("split",(function(t,e,r){var i="0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:vg(e,this,t,r)}:e;return[function(e,r){var n=Sg(this),a=wg(e)?void 0:Pg(e,t);return a?vg(a,e,n,r):vg(i,Cg(n),e,r)},function(t,n){var a=bg(this),s=Cg(t);if(!kg){var o=r(i,a,s,n,i!==e);if(o.done)return o.value}var h=Tg(a,RegExp),u=a.unicode,l=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(Mg?"g":"y"),c=new h(Mg?"^(?:"+a.source+")":a,l),f=void 0===n?4294967295:n>>>0;if(0===f)return[];if(0===s.length)return null===Eg(c,s)?[s]:[];for(var g=0,p=0,d=[];p<s.length;){c.lastIndex=Mg?0:p;var y,v=Eg(c,Mg?Vg(s,p):s);if(null===v||(y=Rg(Ag(c.lastIndex+(Mg?p:0)),s.length))===g)p=Og(s,p,u);else{if(_g(d,Vg(s,g,p)),d.length===f)return d;for(var m=1;m<=v.length-1;m++)if(_g(d,v[m]),d.length===f)return d;p=g=y}}return _g(d,Vg(s,g)),d}]}),kg||!Ig,Mg);var Lg={exports:{}},Dg={exports:{}};(function(){var t,e,r,i,n,a;"undefined"!=typeof performance&&null!==performance&&performance.now?Dg.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(Dg.exports=function(){return(t()-n)/1e6},e=process.hrtime,i=(t=function(){var t;return 1e9*(t=e())[0]+t[1]})(),a=1e9*process.uptime(),n=i-a):Date.now?(Dg.exports=function(){return Date.now()-r},r=Date.now()):(Dg.exports=function(){return(new Date).getTime()-r},r=(new Date).getTime())}).call(t);for(var jg=Dg.exports,Bg="undefined"==typeof window?t:window,zg=["moz","webkit"],Ug="AnimationFrame",Fg=Bg["request"+Ug],Hg=Bg["cancel"+Ug]||Bg["cancelRequest"+Ug],Xg=0;!Fg&&Xg<zg.length;Xg++)Fg=Bg[zg[Xg]+"Request"+Ug],Hg=Bg[zg[Xg]+"Cancel"+Ug]||Bg[zg[Xg]+"CancelRequest"+Ug];if(!Fg||!Hg){var Yg=0,Wg=0,qg=[],$g=1e3/60;Fg=function(t){if(0===qg.length){var e=jg(),r=Math.max(0,$g-(e-Yg));Yg=r+e,setTimeout((function(){var t=qg.slice(0);qg.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(Yg)}catch(r){setTimeout((function(){throw r}),0)}}),Math.round(r))}return qg.push({handle:++Wg,callback:t,cancelled:!1}),Wg},Hg=function(t){for(var e=0;e<qg.length;e++)qg[e].handle===t&&(qg[e].cancelled=!0)}}Lg.exports=function(t){return Fg.call(Bg,t)},Lg.exports.cancel=function(){Hg.apply(Bg,arguments)},Lg.exports.polyfill=function(t){t||(t=Bg),t.requestAnimationFrame=Fg,t.cancelAnimationFrame=Hg};const Gg=e(Lg.exports);var Qg,Zg="\t\n\v\f\r                　\u2028\u2029\ufeff",Kg=L,Jg=Hh,tp=Zg,ep=T("".replace),rp=RegExp("^["+tp+"]+"),ip=RegExp("(^|[^"+tp+"])["+tp+"]+$"),np={trim:function(t){var e=Jg(Kg(t));return e=ep(e,rp,""),e=ep(e,ip,"$1")}},ap=tr.PROPER,sp=Zg,op=np.trim;Zi({target:"String",proto:!0,forced:(Qg="trim",s((function(){return!!sp[Qg]()||"​᠎"!=="​᠎"[Qg]()||ap&&sp[Qg].name!==Qg})))},{trim:function(){return op(this)}});const hp=e((function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,a=r[i].process,s=n.exec(t);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var n=r[i].example,a=0;a<n.length;a++)t[t.length]=n[a];for(var s in e)t[t.length]=s;var o=document.createElement("ul");for(o.setAttribute("id","rgbcolor-examples"),i=0;i<t.length;i++)try{var h=document.createElement("li"),u=new RGBColor(t[i]),l=document.createElement("div");l.style.cssText="margin: 3px; border: 1px solid black; background:"+u.toHex()+"; color:"+u.toHex(),l.appendChild(document.createTextNode("test"));var c=document.createTextNode(" "+t[i]+" -> "+u.toRGB()+" -> "+u.toHex());h.appendChild(l),h.appendChild(c),o.appendChild(h)}catch(f){}return o}}));var up=Zi,lp=pi.indexOf,cp=ig,fp=la([].indexOf),gp=!!fp&&1/fp([1],1,-0)<0;up({target:"Array",proto:!0,forced:gp||!cp("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return gp?fp(this,t,e)||0:lp(this,t,e)}});var pp=Zi,dp=gc,yp=L,vp=Hh,mp=dc,xp=T("".indexOf);pp({target:"String",proto:!0,forced:!mp("includes")},{includes:function(t){return!!~xp(vp(yp(this)),vp(dp(t)),arguments.length>1?arguments[1]:void 0)}});var bp=P,wp=Zi,Sp=Array.isArray||function(t){return"Array"===bp(t)},Tp=T([].reverse),Op=[1,2];wp({target:"Array",proto:!0,forced:String(Op)===String(Op.reverse())},{reverse:function(){return Sp(this)&&(this.length=this.length),Tp(this)}});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var Ap=function(t,e){return(Ap=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function Cp(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}Ap(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function Pp(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function Ep(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var Np=Math.PI;function Mp(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,n=t.rY,a=t.x,s=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var o=Pp([(e-a)/2,(r-s)/2],-t.xRot/180*Np),h=o[0],u=o[1],l=Math.pow(h,2)/Math.pow(i,2)+Math.pow(u,2)/Math.pow(n,2);1<l&&(i*=Math.sqrt(l),n*=Math.sqrt(l)),t.rX=i,t.rY=n;var c=Math.pow(i,2)*Math.pow(u,2)+Math.pow(n,2)*Math.pow(h,2),f=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-c)/c)),g=i*u/n*f,p=-n*h/i*f,d=Pp([g,p],t.xRot/180*Np);t.cX=d[0]+(e+a)/2,t.cY=d[1]+(r+s)/2,t.phi1=Math.atan2((u-p)/n,(h-g)/i),t.phi2=Math.atan2((-u-p)/n,(-h-g)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*Np),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*Np),t.phi1*=180/Np,t.phi2*=180/Np}function Rp(t,e,r){Ep(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var _p,Vp=Math.PI/180;function Ip(t,e,r){return(1-r)*t+r*e}function kp(t,e,r,i){return t+Math.cos(i/180*Np)*e+Math.sin(i/180*Np)*r}function Lp(t,e,r,i){var n=1e-6,a=e-t,s=r-e,o=3*a+3*(i-r)-6*s,h=6*(s-a),u=3*a;return Math.abs(o)<n?[-u/h]:function(t,e,r){var i=t*t/4-e;if(i<-1e-6)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(h/o,u/o,n)}function Dp(t,e,r,i,n){var a=1-n;return t*(a*a*a)+e*(3*a*a*n)+r*(3*a*n*n)+i*(n*n*n)}!function(t){function e(){return n((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n((function(n,a,s){return n.type&Hp.SMOOTH_CURVE_TO&&(n.type=Hp.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?s:e,n.x1=n.relative?a-t:2*a-t,n.y1=n.relative?s-e:2*s-e),n.type&Hp.CURVE_TO?(t=n.relative?a+n.x2:n.x2,e=n.relative?s+n.y2:n.y2):(t=NaN,e=NaN),n.type&Hp.SMOOTH_QUAD_TO&&(n.type=Hp.QUAD_TO,r=isNaN(r)?a:r,i=isNaN(i)?s:i,n.x1=n.relative?a-r:2*a-r,n.y1=n.relative?s-i:2*s-i),n.type&Hp.QUAD_TO?(r=n.relative?a+n.x1:n.x1,i=n.relative?s+n.y1:n.y1):(r=NaN,i=NaN),n}))}function i(){var t=NaN,e=NaN;return n((function(r,i,n){if(r.type&Hp.SMOOTH_QUAD_TO&&(r.type=Hp.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&Hp.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var a=r.x1,s=r.y1;r.type=Hp.CURVE_TO,r.x1=((r.relative?0:i)+2*a)/3,r.y1=((r.relative?0:n)+2*s)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*s)/3}else t=NaN,e=NaN;return r}))}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(a){if(isNaN(i)&&!(a.type&Hp.MOVE_TO))throw new Error("path must start with moveto");var s=t(a,e,r,i,n);return a.type&Hp.CLOSE_PATH&&(e=i,r=n),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&Hp.MOVE_TO&&(i=e,n=r),s}}function a(t,e,r,i,a,s){return Ep(t,e,r,i,a,s),n((function(n,o,h,u){var l=n.x1,c=n.x2,f=n.relative&&!isNaN(u),g=void 0!==n.x?n.x:f?0:o,p=void 0!==n.y?n.y:f?0:h;function d(t){return t*t}n.type&Hp.HORIZ_LINE_TO&&0!==e&&(n.type=Hp.LINE_TO,n.y=n.relative?0:h),n.type&Hp.VERT_LINE_TO&&0!==r&&(n.type=Hp.LINE_TO,n.x=n.relative?0:o),void 0!==n.x&&(n.x=n.x*t+p*r+(f?0:a)),void 0!==n.y&&(n.y=g*e+n.y*i+(f?0:s)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(f?0:a)),void 0!==n.y1&&(n.y1=l*e+n.y1*i+(f?0:s)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(f?0:a)),void 0!==n.y2&&(n.y2=c*e+n.y2*i+(f?0:s));var y=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===y)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=Hp.LINE_TO;else{var v=n.xRot*Math.PI/180,m=Math.sin(v),x=Math.cos(v),b=1/d(n.rX),w=1/d(n.rY),S=d(x)*b+d(m)*w,T=2*m*x*(b-w),O=d(m)*b+d(x)*w,A=S*i*i-T*e*i+O*e*e,C=T*(t*i+e*r)-2*(S*r*i+O*t*e),P=S*r*r-T*t*r+O*t*t,E=(Math.atan2(C,A-P)+Math.PI)%Math.PI/2,N=Math.sin(E),M=Math.cos(E);n.rX=Math.abs(y)/Math.sqrt(A*d(M)+C*N*M+P*d(N)),n.rY=Math.abs(y)/Math.sqrt(A*d(N)-C*N*M+P*d(M)),n.xRot=180*E/Math.PI}return void 0!==n.sweepFlag&&0>y&&(n.sweepFlag=+!n.sweepFlag),n}))}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),Ep(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n((function(i,n,a,s,o){if(isNaN(s)&&!(i.type&Hp.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&Hp.HORIZ_LINE_TO&&(i.type=Hp.LINE_TO,i.y=i.relative?0:a),r&&i.type&Hp.VERT_LINE_TO&&(i.type=Hp.LINE_TO,i.x=i.relative?0:n),t&&i.type&Hp.CLOSE_PATH&&(i.type=Hp.LINE_TO,i.x=i.relative?s-n:s,i.y=i.relative?o-a:o),i.type&Hp.ARC&&(0===i.rX||0===i.rY)&&(i.type=Hp.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i}))},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),Ep(t);var e=NaN,r=NaN,i=NaN,a=NaN;return n((function(n,s,o,h,u){var l=Math.abs,c=!1,f=0,g=0;if(n.type&Hp.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:s-e,g=isNaN(r)?0:o-r),n.type&(Hp.CURVE_TO|Hp.SMOOTH_CURVE_TO)?(e=n.relative?s+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&Hp.SMOOTH_QUAD_TO?(i=isNaN(i)?s:2*s-i,a=isNaN(a)?o:2*o-a):n.type&Hp.QUAD_TO?(i=n.relative?s+n.x1:n.x1,a=n.relative?o+n.y1:n.y2):(i=NaN,a=NaN),n.type&Hp.LINE_COMMANDS||n.type&Hp.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&Hp.CURVE_TO||n.type&Hp.SMOOTH_CURVE_TO||n.type&Hp.QUAD_TO||n.type&Hp.SMOOTH_QUAD_TO){var p=void 0===n.x?0:n.relative?n.x:n.x-s,d=void 0===n.y?0:n.relative?n.y:n.y-o;f=isNaN(i)?void 0===n.x1?f:n.relative?n.x:n.x1-s:i-s,g=isNaN(a)?void 0===n.y1?g:n.relative?n.y:n.y1-o:a-o;var y=void 0===n.x2?0:n.relative?n.x:n.x2-s,v=void 0===n.y2?0:n.relative?n.y:n.y2-o;l(p)<=t&&l(d)<=t&&l(f)<=t&&l(g)<=t&&l(y)<=t&&l(v)<=t&&(c=!0)}return n.type&Hp.CLOSE_PATH&&l(s-h)<=t&&l(o-u)<=t&&(c=!0),c?[]:n}))},t.MATRIX=a,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),Ep(t,e,r);var i=Math.sin(t),n=Math.cos(t);return a(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),Ep(t,e),a(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),Ep(t,e),a(t,0,0,e,0,0)},t.SKEW_X=function(t){return Ep(t),a(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return Ep(t),a(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),Ep(t),a(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),Ep(t),a(1,0,0,-1,0,t)},t.A_TO_C=function(){return n((function(t,e,r){return Hp.ARC===t.type?function(t,e,r){var i,n,a,s;t.cX||Mp(t,e,r);for(var o=Math.min(t.phi1,t.phi2),h=Math.max(t.phi1,t.phi2)-o,u=Math.ceil(h/90),l=new Array(u),c=e,f=r,g=0;g<u;g++){var p=Ip(t.phi1,t.phi2,g/u),d=Ip(t.phi1,t.phi2,(g+1)/u),y=d-p,v=4/3*Math.tan(y*Vp/4),m=[Math.cos(p*Vp)-v*Math.sin(p*Vp),Math.sin(p*Vp)+v*Math.cos(p*Vp)],x=m[0],b=m[1],w=[Math.cos(d*Vp),Math.sin(d*Vp)],S=w[0],T=w[1],O=[S+v*Math.sin(d*Vp),T-v*Math.cos(d*Vp)],A=O[0],C=O[1];l[g]={relative:t.relative,type:Hp.CURVE_TO};var P=function(e,r){var i=Pp([e*t.rX,r*t.rY],t.xRot),n=i[0],a=i[1];return[t.cX+n,t.cY+a]};i=P(x,b),l[g].x1=i[0],l[g].y1=i[1],n=P(A,C),l[g].x2=n[0],l[g].y2=n[1],a=P(S,T),l[g].x=a[0],l[g].y=a[1],t.relative&&(l[g].x1-=c,l[g].y1-=f,l[g].x2-=c,l[g].y2-=f,l[g].x-=c,l[g].y-=f),c=(s=[l[g].x,l[g].y])[0],f=s[1]}return l}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return n((function(t,e,r){return t.relative&&(e=0,r=0),Hp.ARC===t.type&&Mp(t,e,r),t}))},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),a=i(),s=r(),o=n((function(e,r,i){var n=s(a(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function h(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function u(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(n.type&Hp.DRAWING_COMMANDS&&(h(r),u(i)),n.type&Hp.HORIZ_LINE_TO&&h(n.x),n.type&Hp.VERT_LINE_TO&&u(n.y),n.type&Hp.LINE_TO&&(h(n.x),u(n.y)),n.type&Hp.CURVE_TO){h(n.x),u(n.y);for(var l=0,c=Lp(r,n.x1,n.x2,n.x);l<c.length;l++)0<(P=c[l])&&1>P&&h(Dp(r,n.x1,n.x2,n.x,P));for(var f=0,g=Lp(i,n.y1,n.y2,n.y);f<g.length;f++)0<(P=g[f])&&1>P&&u(Dp(i,n.y1,n.y2,n.y,P))}if(n.type&Hp.ARC){h(n.x),u(n.y),Mp(n,r,i);for(var p=n.xRot/180*Math.PI,d=Math.cos(p)*n.rX,y=Math.sin(p)*n.rX,v=-Math.sin(p)*n.rY,m=Math.cos(p)*n.rY,x=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],b=x[0],w=x[1],S=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<b?i+360:i},T=0,O=Rp(v,-d,0).map(S);T<O.length;T++)(P=O[T])>b&&P<w&&h(kp(n.cX,d,v,P));for(var A=0,C=Rp(m,-y,0).map(S);A<C.length;A++){var P;(P=C[A])>b&&P<w&&u(kp(n.cY,y,m,P))}}return e}));return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(_p||(_p={}));var jp,Bp=function(){function t(){}return t.prototype.round=function(t){return this.transform(_p.ROUND(t))},t.prototype.toAbs=function(){return this.transform(_p.TO_ABS())},t.prototype.toRel=function(){return this.transform(_p.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(_p.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(_p.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(_p.QT_TO_C())},t.prototype.aToC=function(){return this.transform(_p.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(_p.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(_p.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(_p.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(_p.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,a){return this.transform(_p.MATRIX(t,e,r,i,n,a))},t.prototype.skewX=function(t){return this.transform(_p.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(_p.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(_p.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(_p.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(_p.ANNOTATE_ARCS())},t}(),zp=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},Up=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},Fp=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return Cp(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var a=t[n],s=!(this.curCommandType!==Hp.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=Up(a)&&("0"===this.curNumber&&"0"===a||s);if(!Up(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||s){if(this.curNumber&&-1!==this.curCommandType){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+n);if(this.curCommandType===Hp.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"');this.curArgs.push(h),this.curArgs.length===Xp[this.curCommandType]&&(Hp.HORIZ_LINE_TO===this.curCommandType?i({type:Hp.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):Hp.VERT_LINE_TO===this.curCommandType?i({type:Hp.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===Hp.MOVE_TO||this.curCommandType===Hp.LINE_TO||this.curCommandType===Hp.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),Hp.MOVE_TO===this.curCommandType&&(this.curCommandType=Hp.LINE_TO)):this.curCommandType===Hp.CURVE_TO?i({type:Hp.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===Hp.SMOOTH_CURVE_TO?i({type:Hp.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===Hp.QUAD_TO?i({type:Hp.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===Hp.ARC&&i({type:Hp.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!zp(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=Hp.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=Hp.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=Hp.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=Hp.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=Hp.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=Hp.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=Hp.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=Hp.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+".");this.curCommandType=Hp.ARC,this.curCommandRelative="a"===a}else e.push({type:Hp.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var a=n[i],s=t(a);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(Bp),Hp=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return Cp(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=_p.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===Hp.CLOSE_PATH)e+="z";else if(i.type===Hp.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===Hp.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===Hp.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===Hp.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===Hp.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===Hp.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===Hp.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===Hp.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==Hp.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new Fp,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(Bp),Xp=((jp={})[Hp.MOVE_TO]=2,jp[Hp.LINE_TO]=2,jp[Hp.HORIZ_LINE_TO]=1,jp[Hp.VERT_LINE_TO]=1,jp[Hp.CLOSE_PATH]=0,jp[Hp.QUAD_TO]=4,jp[Hp.SMOOTH_QUAD_TO]=2,jp[Hp.CURVE_TO]=6,jp[Hp.SMOOTH_CURVE_TO]=4,jp[Hp.ARC]=7,jp),Yp=c,Wp=Ht,qp=q,$p=Yh,Gp=RegExp.prototype,Qp=tr.PROPER,Zp=Kr,Kp=Ve,Jp=Hh,td=s,ed="toString",rd=RegExp.prototype,id=rd[ed],nd=td((function(){return"/a/b"!==id.call({source:"a",flags:"b"})})),ad=Qp&&id.name!==ed;function sd(t){return(sd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}(nd||ad)&&Zp(rd,ed,(function(){var t=Kp(this);return"/"+Jp(t.source)+"/"+Jp(function(t){var e=t.flags;return void 0!==e||"flags"in Gp||Wp(t,"flags")||!qp(Gp,t)?e:Yp($p,t)}(t))}),{unsafe:!0});var od=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],hd=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];var ud=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null},ld=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>zh((function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)}))()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function cd(t){return t.replace(/(?!\u3000)\s+/gm," ")}function fd(t){return t.replace(/^[\n \t]+/,"")}function gd(t){return t.replace(/[\n \t]+$/,"")}function pd(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var dd=/^[A-Z-]+$/;function yd(t){return dd.test(t)?t.toLowerCase():t}function vd(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function md(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,((t,r)=>e--&&r?String(Math.round(parseFloat(t))):t))}var xd=/(\[[^\]]+\])/g,bd=/(#[^\s+>~.[:]+)/g,wd=/(\.[^\s+>~.[:]+)/g,Sd=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,Td=/(:[\w-]+\([^)]*\))/gi,Od=/(:[^\s+>~.[:]+)/g,Ad=/([^\s+>~.[:]+)/g;function Cd(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function Pd(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=Cd(r,xd),e[1]+=i,[r,i]=Cd(r,bd),e[0]+=i,[r,i]=Cd(r,wd),e[1]+=i,[r,i]=Cd(r,Sd),e[2]+=i,[r,i]=Cd(r,Td),e[1]+=i,[r,i]=Cd(r,Od),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=Cd(r,Ad),e[2]+=i,e.join("")}var Ed=1e-8;function Nd(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function Md(t,e){return(t[0]*e[0]+t[1]*e[1])/(Nd(t)*Nd(e))}function Rd(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Md(t,e))}function _d(t){return t*t*t}function Vd(t){return 3*t*t*(1-t)}function Id(t){return 3*t*(1-t)*(1-t)}function kd(t){return(1-t)*(1-t)*(1-t)}function Ld(t){return t*t}function Dd(t){return 2*t*(1-t)}function jd(t){return(1-t)*(1-t)}class Bd{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new Bd(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return cd(this.getString()).trim().split(t).map((t=>new Bd(e,r,t)))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=md(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*n.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?Bd.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var a=new hp(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new Bd(this.document,this.name,e)}}Bd.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class zd{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class Ud{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=pd(t);return new Ud(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=pd(t);return new Ud(r,i)}static parsePath(t){for(var e=pd(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new Ud(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class Fd{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach(((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent})),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(a,s)&&(i[n]=t)}))}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInBox(a,s)&&(i[n]=t)}))}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new Ud(t,e),a=i.canvas;a;)n.x-=a.offsetLeft,n.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var Hd="undefined"!=typeof window?window:null,Xd="undefined"!=typeof fetch?fetch.bind(void 0):null;class Yd{constructor(t){var{fetch:e=Xd,window:r=Hd}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new zd,this.mouse=new Fd(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every((t=>t()));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:a,height:s,desiredHeight:o,minX:h=0,minY:u=0,refX:l,refY:c,clip:f=!1,clipX:g=0,clipY:p=0}=t,d=cd(i).replace(/^defer\s/,""),[y,v]=d.split(" "),m=y||"xMidYMid",x=v||"meet",b=n/a,w=s/o,S=Math.min(b,w),T=Math.max(b,w),O=a,A=o;"meet"===x&&(O*=S,A*=S),"slice"===x&&(O*=T,A*=T);var C=new Bd(e,"refX",l),P=new Bd(e,"refY",c),E=C.hasValue()&&P.hasValue();if(E&&r.translate(-S*C.getPixels("x"),-S*P.getPixels("y")),f){var N=S*g,M=S*p;r.beginPath(),r.moveTo(N,M),r.lineTo(n,M),r.lineTo(n,s),r.lineTo(N,s),r.closePath(),r.clip()}if(!E){var R="meet"===x&&S===w,_="slice"===x&&T===w,V="meet"===x&&S===b,I="slice"===x&&T===b;m.startsWith("xMid")&&(R||_)&&r.translate(n/2-O/2,0),m.endsWith("YMid")&&(V||I)&&r.translate(0,s/2-A/2),m.startsWith("xMax")&&(R||_)&&r.translate(n-O,0),m.endsWith("YMax")&&(V||I)&&r.translate(0,s-A)}switch(!0){case"none"===m:r.scale(b,w);break;case"meet"===x:r.scale(S,S);break;case"slice"===x:r.scale(T,T)}r.translate(-h,-u)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:a=!1,forceRedraw:s,scaleWidth:o,scaleHeight:h,offsetX:u,offsetY:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:c,mouse:f}=this,g=1e3/c;if(this.frameDuration=g,this.readyPromise=new Promise((t=>{this.resolveReady=t})),this.isReady()&&this.render(t,n,a,o,h,u,l),e){var p=Date.now(),d=p,y=0,v=()=>{p=Date.now(),(y=p-d)>=g&&(d=p-y%g,this.shouldUpdate(i,s)&&(this.render(t,n,a,o,h,u,l),f.runEvents())),this.intervalId=Gg(v)};r||f.start(),this.intervalId=Gg(v)}}stop(){this.intervalId&&(Gg.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce(((t,e)=>e.update(r)||t),!1))return!0}return!("function"!=typeof e||!e())||!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents()}render(t,e,r,i,n,a,s){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:h,viewPort:u,ctx:l,isFirstRender:c}=this,f=l.canvas;u.clear(),f.width&&f.height?u.setCurrent(f.width,f.height):u.setCurrent(o,h);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(c||"number"!=typeof i&&"number"!=typeof n)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,y=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),y=p.getPixels("y")),u.setCurrent(d,y),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof s&&t.getAttribute("y",!0).setValue(s),"number"==typeof i||"number"==typeof n){var v=pd(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof i){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/i:isNaN(v[2])||(m=v[2]/i)}if("number"==typeof n){var w=t.getStyle("height");w.hasValue()?x=w.getPixels("y")/n:isNaN(v[3])||(x=v[3]/n)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var S=t.getStyle("transform",!0,!0);S.setValue("".concat(S.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||l.clearRect(0,0,d,y),t.render(l),c&&(this.isFirstRender=!1)}}Yd.defaultWindow=Hd,Yd.defaultFetch=Xd;var{defaultFetch:Wd}=Yd,qd="undefined"!=typeof DOMParser?DOMParser:null;class $d{constructor(){var{fetch:t=Wd,DOMParser:e=qd}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return zh((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return zh((function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)}))()}}class Gd{constructor(t,e){this.type="translate",this.point=null,this.point=Ud.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class Qd{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=pd(e);this.angle=new Bd(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(a.getRadians()),t.translate(-s,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(-1*a.getRadians()),t.translate(-s,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class Zd{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=Ud.parseScale(e);0!==i.x&&0!==i.y||(i.x=Ed,i.y=Ed),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(e,r||e),t.translate(-a,-s)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(1/e,1/r||e),t.translate(-a,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class Kd{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=pd(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),a=r.getPixels("y");t.translate(n,a),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-a)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],a=i[2],s=i[4],o=i[1],h=i[3],u=i[5],l=1/(n*(1*h-0*u)-a*(1*o-0*u)+s*(0*o-0*h)),c=e.getPixels("x"),f=r.getPixels("y");t.translate(c,f),t.transform(l*(1*h-0*u),l*(0*u-1*o),l*(0*s-1*a),l*(1*n-0*s),l*(a*u-s*h),l*(s*o-n*u)),t.translate(-c,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class Jd extends Kd{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new Bd(t,"angle",e)}}class ty extends Jd{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class ey extends Jd{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class ry{constructor(t,e,r){this.document=t,this.transforms=[];var i=function(t){return cd(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e);i.forEach((t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=ry.transformTypes[e];void 0!==n&&this.transforms.push(new n(this.document,i,r))}}))}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),a=[i,n];return r.hasValue()?new ry(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}ry.transformTypes={translate:Gd,rotate:Qd,scale:Zd,matrix:Kd,skewX:ty,skewY:ey};class iy{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){Array.from(e.attributes).forEach((e=>{var r=yd(e.nodeName);this.attributes[r]=new Bd(t,r,e.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map((t=>t.trim())).forEach((e=>{if(e){var[r,i]=e.split(":").map((t=>t.trim()));this.styles[r]=new Bd(t,r,i)}}));var{definitions:i}=t,n=this.getAttribute("id");n.hasValue()&&(i[n.getString()]||(i[n.getString()]=this)),Array.from(e.childNodes).forEach((e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}}))}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new Bd(this.document,t,"");return this.attributes[t]=i,i}return r||Bd.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return Bd.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!=n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:a}=this;if(a){var s=a.getStyle(t);if(null!=s&&s.hasValue())return s}}if(e){var o=new Bd(this.document,t,"");return this.styles[t]=o,o}return i||Bd.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=ry.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach((e=>{e.render(t)}))}addChild(t){var e=t instanceof iy?t:this.document.createElement(t);e.parent=this,iy.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some((e=>".".concat(e)===t))}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var a in i){var s=this.stylesSpecificity[a];void 0===s&&(s="000"),n>=s&&(this.styles[a]=i[a],this.stylesSpecificity[a]=n)}}}removeStyles(t,e){return e.reduce(((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]}),[])}restoreStyles(t,e){e.forEach((e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)}))}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}iy.ignoreChildTypes=["title"];class ny extends iy{constructor(t,e,r){super(t,e,r)}}function ay(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function sy(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function oy(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class hy{constructor(t,e,r,i,n,a){var s=a?"string"==typeof a?hy.parse(a):a:{};this.fontFamily=n||s.fontFamily,this.fontSize=i||s.fontSize,this.fontStyle=t||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=e||s.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",i="",n="",a="",s=cd(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return s.forEach((t=>{switch(!0){case!o.fontStyle&&hy.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&hy.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&hy.weights.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([n]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}})),new hy(e,r,i,n,a,t)}toString(){return[sy(this.fontStyle),this.fontVariant,oy(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"==typeof process?t:t.trim().split(",").map(ay).join(","))].join(" ").trim();var t}}hy.styles="normal|italic|oblique|inherit",hy.variants="normal|small-caps|inherit",hy.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class uy{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var a=6*e-12*r+6*i,s=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==s){var h=Math.pow(a,2)-4*o*s;if(!(h<0)){var u=(-a+Math.sqrt(h))/(2*s);0<u&&u<1&&(t?this.addX(this.sumCubic(u,e,r,i,n)):this.addY(this.sumCubic(u,e,r,i,n)));var l=(-a-Math.sqrt(h))/(2*s);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}else{if(0===a)return;var c=-o/a;0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,a,s,o){this.addPoint(t,e),this.addPoint(s,o),this.bezierCurveAdd(!0,t,r,n,s),this.bezierCurveAdd(!1,e,i,a,o)}addQuadraticCurve(t,e,r,i,n,a){var s=t+2/3*(r-t),o=e+2/3*(i-e),h=s+1/3*(n-t),u=o+1/3*(a-e);this.addBezierCurve(t,e,s,h,o,u,n,a)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:a}=this;return r<=t&&t<=n&&i<=e&&e<=a}}class ly extends Hp{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new Ud(0,0),this.control=new Ud(0,0),this.current=new Ud(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new Ud(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==Hp.CURVE_TO&&t!==Hp.SMOOTH_CURVE_TO&&t!==Hp.QUAD_TO&&t!==Hp.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new Ud(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class cy extends iy{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,i);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var h=new Bd(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=h}if(n.isUrlDefinition()){var u=n.getFillStyleDefinition(this,a);u&&(t.strokeStyle=u)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var l=n.getString();"inherit"!==l&&(t.strokeStyle="none"===l?"rgba(0,0,0,0)":l)}if(a.hasValue()){var c=new Bd(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=c}var f=this.getStyle("stroke-width");if(f.hasValue()){var g=f.getPixels();t.lineWidth=g||Ed}var p=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),y=this.getStyle("stroke-miterlimit"),v=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),d.hasValue()&&(t.lineJoin=d.getString()),y.hasValue()&&(t.miterLimit=y.getNumber()),v.hasValue()&&"none"!==v.getString()){var x=pd(v.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),S=this.getStyle("font-style"),T=this.getStyle("font-variant"),O=this.getStyle("font-weight"),A=this.getStyle("font-size"),C=this.getStyle("font-family"),P=new hy(S.getString(),T.getString(),O.getString(),A.hasValue()?"".concat(A.getPixels(!0),"px"):"",C.getString(),hy.parse(w.getString(),t.font));S.setValue(P.fontStyle),T.setValue(P.fontVariant),O.setValue(P.fontWeight),A.setValue(P.fontSize),C.setValue(P.fontFamily),t.font=P.toString(),A.isPixels()&&(this.document.emSize=A.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class fy extends cy{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new ly(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new uy;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case ly.MOVE_TO:this.pathM(t,r);break;case ly.LINE_TO:this.pathL(t,r);break;case ly.HORIZ_LINE_TO:this.pathH(t,r);break;case ly.VERT_LINE_TO:this.pathV(t,r);break;case ly.CURVE_TO:this.pathC(t,r);break;case ly.SMOOTH_CURVE_TO:this.pathS(t,r);break;case ly.QUAD_TO:this.pathQ(t,r);break;case ly.SMOOTH_QUAD_TO:this.pathT(t,r);break;case ly.ARC:this.pathA(t,r);break;case ly.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map(((t,e)=>[t,r[e]]))}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[h,u]=r[0];o.render(t,h,u)}if(a.isUrlDefinition())for(var l=a.getDefinition(),c=1;c<i;c++){var[f,g]=r[c];l.render(t,f,g)}if(s.isUrlDefinition()){var p=s.getDefinition(),[d,y]=r[i];p.render(t,d,y)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=fy.pathM(r),{x:n,y:a}=i;r.addMarker(i),e.addPoint(n,a),t&&t.moveTo(n,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=fy.pathL(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathH(t){var{current:e,command:r}=t,i=new Ud((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=fy.pathH(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathV(t){var{current:e,command:r}=t,i=new Ud(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=fy.pathV(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=fy.pathC(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=fy.pathS(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=fy.pathQ(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=fy.pathT(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:a,lArcFlag:s,sweepFlag:o}=r,h=a*(Math.PI/180),u=t.getAsCurrentPoint(),l=new Ud(Math.cos(h)*(e.x-u.x)/2+Math.sin(h)*(e.y-u.y)/2,-Math.sin(h)*(e.x-u.x)/2+Math.cos(h)*(e.y-u.y)/2),c=Math.pow(l.x,2)/Math.pow(i,2)+Math.pow(l.y,2)/Math.pow(n,2);c>1&&(i*=Math.sqrt(c),n*=Math.sqrt(c));var f=(s===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(l.y,2)-Math.pow(n,2)*Math.pow(l.x,2))/(Math.pow(i,2)*Math.pow(l.y,2)+Math.pow(n,2)*Math.pow(l.x,2)));isNaN(f)&&(f=0);var g=new Ud(f*i*l.y/n,f*-n*l.x/i),p=new Ud((e.x+u.x)/2+Math.cos(h)*g.x-Math.sin(h)*g.y,(e.y+u.y)/2+Math.sin(h)*g.x+Math.cos(h)*g.y),d=Rd([1,0],[(l.x-g.x)/i,(l.y-g.y)/n]),y=[(l.x-g.x)/i,(l.y-g.y)/n],v=[(-l.x-g.x)/i,(-l.y-g.y)/n],m=Rd(y,v);return Md(y,v)<=-1&&(m=Math.PI),Md(y,v)>=1&&(m=0),{currentPoint:u,rX:i,rY:n,sweepFlag:o,xAxisRotation:h,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:a,sweepFlag:s,xAxisRotation:o,centp:h,a1:u,ad:l}=fy.pathA(r),c=1-s?1:-1,f=u+c*(l/2),g=new Ud(h.x+n*Math.cos(f),h.y+a*Math.sin(f));if(r.addMarkerAngle(g,f-c*Math.PI/2),r.addMarkerAngle(i,f-c*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(u)&&!isNaN(l)){var p=n>a?n:a,d=n>a?1:n/a,y=n>a?a/n:1;t.translate(h.x,h.y),t.rotate(o),t.scale(d,y),t.arc(0,0,p,u,u+l,Boolean(1-s)),t.scale(1/d,1/y),t.rotate(-o),t.translate(-h.x,-h.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){fy.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class gy extends fy{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class py extends cy{constructor(t,e,r){super(t,e,new.target===py||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach(((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n})),e}getFontSize(){var{document:t,parent:e}=this,r=hy.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new uy(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var a=e.length,s=e[r-1],o=e[r+1],h="isolated";if((0===r||" "===s)&&r<a-1&&" "!==o&&(h="terminal"),r>0&&" "!==s&&r<a-1&&" "!==o&&(h="medial"),r>0&&" "!==s&&(r===a-1||" "===o)&&(h="initial"),void 0!==t.glyphs[i]){var u=t.glyphs[i];n=u instanceof gy?u:u[h]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,a=cd(e.textContent||"");return 0===i&&(a=fd(a)),i===n&&(a=gd(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach(((e,r)=>{this.renderChild(t,this,this,r)}));var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:a}=n.fontFace,s=hy.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(s.fontSize),h=r.getStyle("font-style").getString(s.fontStyle),u=o/a,l=n.isRTL?i.split("").reverse().join(""):i,c=pd(r.getAttribute("dx").getString()),f=l.length,g=0;g<f;g++){var p=this.getGlyph(n,l,g);t.translate(this.x,this.y),t.scale(u,-u);var d=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===h&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===h&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/u,-1/u),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||n.horizAdvX)/a,void 0===c[g]||isNaN(c[g])||(this.x+=c[g])}else{var{x:y,y:v}=this;t.fillStyle&&t.fillText(i,y,v),t.strokeStyle&&t.strokeText(i,y,v)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t,e=this.leafTexts[this.textChunkStart],r=e.getStyle("text-anchor").getString("start");t="start"===r?e.x-this.minX:"end"===r?e.x-this.maxX:e.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=t;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach(((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)})),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach(((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)})):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!=typeof n.measureText)return n;t.save(),n.setContext(t,!0);var a=n.getAttribute("x"),s=n.getAttribute("y"),o=n.getAttribute("dx"),h=n.getAttribute("dy"),u=n.getStyle("font-family").getDefinition(),l=Boolean(u)&&u.isRTL;0===i&&(a.hasValue()||a.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),h.hasValue()||h.setValue(n.getInheritedAttribute("dy")));var c=n.measureText(t);return l&&(e.x-=c),a.hasValue()?(e.applyAnchoring(),n.x=a.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,l||(e.x+=c),s.hasValue()?(n.y=s.getPixels("y"),h.hasValue()&&(n.y+=h.getPixels("y"))):(h.hasValue()&&(e.y+=h.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+c),e.maxX=Math.max(e.maxX,n.x,n.x+c),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!=typeof n.getBoundingBox)return null;var a=n.getBoundingBox(t);return a?(n.children.forEach(((r,i)=>{var s=e.getChildBoundingBox(t,e,n,i);a.addBoundingBox(s)})),a):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach(((r,i)=>{e.renderChild(t,e,n,i)}))}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),a=i.isRTL?e.split("").reverse().join(""):e,s=pd(r.getAttribute("dx").getString()),o=a.length,h=0,u=0;u<o;u++)h+=(this.getGlyph(i,a,u).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===s[u]||isNaN(s[u])||(h+=s[u]);return h}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:l}=t.measureText(e);return this.clearContext(t),t.restore(),l}getInheritedAttribute(t){for(var e=this;e instanceof py&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class dy extends py{constructor(t,e,r){super(t,e,new.target===dy||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class yy extends dy{constructor(){super(...arguments),this.type="textNode"}}class vy extends cy{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,a=t.canvas;if(i.setDefaults(t),a.style&&void 0!==t.font&&n&&void 0!==n.getComputedStyle){t.font=n.getComputedStyle(a).getPropertyValue("font");var s=new Bd(r,"fontSize",hy.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:h}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var u=this.getAttribute("refX"),l=this.getAttribute("refY"),c=this.getAttribute("viewBox"),f=c.hasValue()?pd(c.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,y=0,v=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),h=this.getStyle("height").getPixels("y"),"marker"===this.type&&(y=p,v=d,p=0,d=0)),i.viewPort.setCurrent(o,h),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],h=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:h,minX:p,minY:d,refX:u.getValue(),refY:l.getValue(),clip:g,clipX:y,clipY:v}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,h))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),s=this.getAttribute("style"),o=i.getNumber(0),h=n.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var u=this.getAttribute("preserveAspectRatio");u.hasValue()&&u.setValue(u.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(h||e)),s.hasValue()){var l=this.getStyle("width"),c=this.getStyle("height");l.hasValue()&&l.setValue("".concat(t,"px")),c.hasValue()&&c.setValue("".concat(e,"px"))}}}class my extends fy{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),s=this.getAttribute("ry"),o=a.getPixels("x"),h=s.getPixels("y");if(a.hasValue()&&!s.hasValue()&&(h=o),s.hasValue()&&!a.hasValue()&&(o=h),o=Math.min(o,i/2),h=Math.min(h,n/2),t){var u=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+u*o,r,e+i,r+h-u*h,e+i,r+h),t.lineTo(e+i,r+n-h),t.bezierCurveTo(e+i,r+n-h+u*h,e+i-o+u*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-u*o,r+n,e,r+n-h+u*h,e,r+n-h),t.lineTo(e,r+h),t.bezierCurveTo(e,r+h-u*h,e+o-u*o,r,e+o,r),t.closePath())}return new uy(e,r,e+i,r+n)}getMarkers(){return null}}class xy extends fy{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new uy(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class by extends fy{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,a),t.bezierCurveTo(n+r,a+e*i,n+e*r,a+i,n,a+i),t.bezierCurveTo(n-e*r,a+i,n-r,a+e*i,n-r,a),t.bezierCurveTo(n-r,a-e*i,n-e*r,a-i,n,a-i),t.bezierCurveTo(n+e*r,a-i,n+r,a-e*i,n+r,a),t.closePath()),new uy(n-r,a-i,n+r,a+i)}getMarkers(){return null}}class wy extends fy{constructor(){super(...arguments),this.type="line"}getPoints(){return[new Ud(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new Ud(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new uy(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Sy extends fy{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=Ud.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new uy(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach((e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)})),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach(((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])})),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Ty extends Sy{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class Oy extends iy{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),a=new vy(this.document,null);a.attributes.viewBox=new Bd(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new Bd(this.document,"width","".concat(i,"px")),a.attributes.height=new Bd(this.document,"height","".concat(n,"px")),a.attributes.transform=new Bd(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var s=this.document.createCanvas(i,n),o=s.getContext("2d"),h=this.getAttribute("x"),u=this.getAttribute("y");h.hasValue()&&u.hasValue()&&o.translate(h.getPixels("x",!0),u.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var l=-1;l<=1;l++)for(var c=-1;c<=1;c++)o.save(),a.attributes.x=new Bd(this.document,"x",l*s.width),a.attributes.y=new Bd(this.document,"y",c*s.height),a.render(o),o.restore();return t.createPattern(s,"repeat")}}class Ay extends iy{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,a=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===a&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new vy(this.document,null);o.type=this.type,o.attributes.viewBox=new Bd(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new Bd(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new Bd(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new Bd(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new Bd(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new Bd(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new Bd(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new Bd(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-i,-n)}}}class Cy extends iy{constructor(){super(...arguments),this.type="defs"}render(){}}class Py extends cy{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new uy;return this.children.forEach((r=>{e.addBoundingBox(r.getBoundingBox(t))})),e}}class Ey extends iy{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach((t=>{"stop"===t.type&&i.push(t)}))}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach((t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))})),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:h}=s.screen,[u]=h.viewPorts,l=new my(s,null);l.attributes.x=new Bd(s,"x",-o/3),l.attributes.y=new Bd(s,"y",-o/3),l.attributes.width=new Bd(s,"width",o),l.attributes.height=new Bd(s,"height",o);var c=new Py(s,null);c.attributes.transform=new Bd(s,"transform",this.getAttribute("gradientTransform").getValue()),c.children=[l];var f=new vy(s,null);f.attributes.x=new Bd(s,"x",0),f.attributes.y=new Bd(s,"y",0),f.attributes.width=new Bd(s,"width",u.width),f.attributes.height=new Bd(s,"height",u.height),f.children=[c];var g=s.createCanvas(u.width,u.height),p=g.getContext("2d");return p.fillStyle=a,f.render(p),p.createPattern(g,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach((e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())}))}addParentOpacity(t,e){return t.hasValue()?new Bd(this.document,"color",e).addOpacity(t).getColor():e}}class Ny extends Ey{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&a===o?null:t.createLinearGradient(n,a,s,o)}}class My extends Ey{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,o=a;this.getAttribute("fx").hasValue()&&(s=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var h=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),u=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,o,u,n,a,h)}}class Ry extends iy{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),n.hasValue()&&(a=a.addOpacity(n)),this.offset=i,this.color=a.getColor()}}class _y extends iy{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new Bd(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var a=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var o=s.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),a=Math.ceil(i);r.from=new Bd(t,"from",parseFloat(e.getValue()[n])),r.to=new Bd(t,"to",parseFloat(e.getValue()[a])),r.progress=(i-n)/(a-n)}else r.from=this.from,r.to=this.to;return r}}class Vy extends _y{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new hp(e.getColor()),n=new hp(r.getColor());if(i.ok&&n.ok){var a=i.r+(n.r-i.r)*t,s=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(s),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class Iy extends _y{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=pd(e.getString()),n=pd(r.getString());return i.map(((e,r)=>e+(n[r]-e)*t)).join(" ")}}class ky extends iy{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var a of n)switch(a.type){case"font-face":this.fontFace=a;var s=a.getStyle("font-family");s.hasValue()&&(i[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]=Object.create(null)),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class Ly extends iy{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class Dy extends fy{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class jy extends py{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class By extends py{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],a=i.length>0&&Array.from(i).every((t=>3===t.nodeType));this.hasText=a,this.text=a?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,a=new Bd(e,"fontSize",hy.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new uy(r,i-a.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var s=new Py(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function zy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function Uy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zy(Object(r),!0).forEach((function(e){$f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class Fy extends py{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach((e=>{var{type:r,points:i}=e;switch(r){case ly.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case ly.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case ly.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case ly.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case ly.ARC:var[n,a,s,o,h,u,l,c]=i,f=s>o?s:o,g=s>o?1:s/o,p=s>o?o/s:1;t&&(t.translate(n,a),t.rotate(l),t.scale(g,p),t.arc(0,0,f,h,h+u,Boolean(1-c)),t.scale(1/g,1/p),t.rotate(-l),t.translate(-n,-a));break;case ly.CLOSE_PATH:t&&t.closePath()}}))}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach(((i,n)=>{var{p0:a,p1:s,rotation:o,text:h}=i;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(h,0,0),t.strokeStyle&&t.strokeText(h,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(a.x,a.y+r/8),t.lineTo(s.x,s.y+r/5))})),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,a,s,o,h){var u=a,l=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(l+=(i-r)/n),h>-1&&(u+=this.getLetterSpacingAt(h));var c=this.textHeight/20,f=this.getEquidistantPointOnPath(u,c,0),g=this.getEquidistantPointOnPath(u+l,c,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(s){var y=Math.cos(Math.PI/2+d)*s,v=Math.cos(-d)*s;p.p0=Uy(Uy({},f),{},{x:f.x+y,y:f.y+v}),p.p1=Uy(Uy({},g),{},{x:g.x+y,y:g.y+v})}return{offset:u+=l,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map((t=>t.getPixels("x"))),a=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),h=this.parent.getStyle("letter-spacing"),u=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(u=o.getPixels()):u=h.getPixels();var l=[],c=e.length;this.letterSpacingCache=l;for(var f=0;f<c;f++)l.push(void 0!==n[f]?n[f]:u);var g=l.reduce(((t,e,r)=>0===r?0:t+e||0),0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var y=this.getPathLength(),v=this.getStyle("startOffset").getNumber(0)*y,m=0;"middle"!==s&&"center"!==s||(m=-d/2),"end"!==s&&"right"!==s||(m=-d),m+=v,r.forEach(((e,n)=>{var{offset:o,segment:h,rotation:u}=this.findSegmentToFitChar(t,s,d,y,i,m,a,e,n);m=o,h.p0&&h.p1&&this.glyphInfo.push({text:r[n],p0:h.p0,p1:h.p1,rotation:u})}))}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,a=i?i.y:0,s=r.next(),o=s.type,h=[];switch(s.type){case ly.MOVE_TO:this.pathM(r,h);break;case ly.LINE_TO:o=this.pathL(r,h);break;case ly.HORIZ_LINE_TO:o=this.pathH(r,h);break;case ly.VERT_LINE_TO:o=this.pathV(r,h);break;case ly.CURVE_TO:this.pathC(r,h);break;case ly.SMOOTH_CURVE_TO:o=this.pathS(r,h);break;case ly.QUAD_TO:this.pathQ(r,h);break;case ly.SMOOTH_QUAD_TO:o=this.pathT(r,h);break;case ly.ARC:h=this.pathA(r);break;case ly.CLOSE_PATH:fy.pathZ(r)}s.type!==ly.CLOSE_PATH?e.push({type:o,points:h,start:{x:n,y:a},pathLength:this.calcLength(n,a,o,h)}):e.push({type:ly.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=fy.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=fy.pathL(t).point;return e.push(r,i),ly.LINE_TO}pathH(t,e){var{x:r,y:i}=fy.pathH(t).point;return e.push(r,i),ly.LINE_TO}pathV(t,e){var{x:r,y:i}=fy.pathV(t).point;return e.push(r,i),ly.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=fy.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=fy.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),ly.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=fy.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=fy.pathT(t);return e.push(r.x,r.y,i.x,i.y),ly.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:a,a1:s,ad:o}=fy.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,s,o,n,i]}calcLength(t,e,r,i){var n=0,a=null,s=null,o=0;switch(r){case ly.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case ly.CURVE_TO:for(n=0,a=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)s=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case ly.QUAD_TO:for(n=0,a=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)s=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case ly.ARC:n=0;var h=i[4],u=i[5],l=i[4]+u,c=Math.PI/180;if(Math.abs(h-l)<c&&(c=Math.abs(h-l)),a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],h,0),u<0)for(o=h-c;o>l;o-=c)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;else for(o=h+c;o<l;o+=c)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],l,0),n+this.getLineLength(a.x,a.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,i,n){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+Ed),h=Math.sqrt(t*t/(1+o*o));i<e&&(h*=-1);var u=o*h,l=null;if(i===e)l={x:a,y:s+u};else if((s-r)/(a-e+Ed)===o)l={x:a+h,y:s+u};else{var c,f,g=this.getLineLength(e,r,i,n);if(g<Ed)return null;var p=(a-e)*(i-e)+(s-r)*(n-r);c=e+(p/=g*g)*(i-e),f=r+p*(n-r);var d=this.getLineLength(a,s,c,f),y=Math.sqrt(t*t-d*d);h=Math.sqrt(y*y/(1+o*o)),i<e&&(h*=-1),l={x:c+h,y:f+(u=o*h)}}return l}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var a of n){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var s=t-r,o=0;switch(a.type){case ly.LINE_TO:i=this.getPointOnLine(s,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case ly.ARC:var h=a.points[4],u=a.points[5],l=a.points[4]+u;if(o=h+s/a.pathLength*u,u<0&&o<l||u>=0&&o>l)break;i=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case ly.CURVE_TO:(o=s/a.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case ly.QUAD_TO:(o=s/a.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(i)return i;break}r+=a.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce(((t,e)=>e.pathLength>0?t+e.pathLength:t),0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,a,s,o,h){return{x:o*_d(t)+a*Vd(t)+i*Id(t)+e*kd(t),y:h*_d(t)+s*Vd(t)+n*Id(t)+r*kd(t)}}getPointOnQuadraticBezier(t,e,r,i,n,a,s){return{x:a*Ld(t)+i*Dd(t)+e*jd(t),y:s*Ld(t)+n*Dd(t)+r*jd(t)}}getPointOnEllipticalArc(t,e,r,i,n,a){var s=Math.cos(a),o=Math.sin(a),h=r*Math.cos(n),u=i*Math.sin(n);return{x:t+(h*s-u*o),y:e+(h*o+u*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var a=0,s=0;s<=r;s+=i){var o=this.getPointOnPath(s),h=this.getPointOnPath(s+i);o&&h&&(a+=this.getLineLength(o.x,o.y,h.x,h.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:s}),a-=n)}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var Hy=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class Xy extends cy{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return zh((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(i){}e.loaded=!0}))()}loadSvg(t){var e=this;return zh((function*(){var r=Hy.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield e.document.fetch(t),a=yield n.text();e.image=a}catch(s){}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&s&&o){if(t.save(),t.translate(n,a),this.isSvg){var h=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:o});h.document.documentElement.parent=this,h.render()}else{var u=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:u.width,height:o,desiredHeight:u.height}),this.loaded&&(void 0===u.complete||u.complete)&&t.drawImage(u,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new uy(t,e,t+r,e+i)}}class Yy extends cy{constructor(){super(...arguments),this.type="symbol"}render(t){}}class Wy{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return zh((function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach((e=>{var r=i.createElement(e);i.definitions[t]=r}))}catch(a){}r.loaded=!0}))()}}class qy extends iy{constructor(t,e,r){super(t,e,r),this.type="style",cd(Array.from(e.childNodes).map((t=>t.textContent)).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach((e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),a=i[1].split(";");n.forEach((e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(a.forEach((e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();n&&a&&(i[n]=new Bd(t,n,a))})),t.styles[r]=i,t.stylesSpecificity[r]=Pd(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach((e=>{if(e.indexOf('format("svg")')>0){var r=vd(e);r&&new Wy(t).load(n,r)}}))}}}))}}))}}qy.parseExternalUrl=vd;class $y extends cy{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new vy(e,null)).attributes.viewBox=new Bd(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new Bd(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new Bd(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new Bd(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new Bd(e,"width",n.getString())),a.hasValue()&&(i.attributes.height=new Bd(e,"height",a.getString()))}var s=i.parent;i.parent=this,i.render(t),i.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return ry.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Gy(t,e,r,i,n,a){return t[r*i*4+4*e+a]}function Qy(t,e,r,i,n,a,s){t[r*i*4+4*e+a]=s}function Zy(t,e,r){return t[e]*r}function Ky(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class Jy extends iy{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=pd(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=i[0]*Math.PI/180;i=[Ky(a,.213,.787,-.213),Ky(a,.715,-.715,-.715),Ky(a,.072,-.072,.928),0,0,Ky(a,.213,-.213,.143),Ky(a,.715,.285,.14),Ky(a,.072,-.072,-.283),0,0,Ky(a,.213,-.213,-.787),Ky(a,.715,-.715,.715),Ky(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:a,matrix:s}=this,o=t.getImageData(0,0,i,n),h=0;h<n;h++)for(var u=0;u<i;u++){var l=Gy(o.data,u,h,i,0,0),c=Gy(o.data,u,h,i,0,1),f=Gy(o.data,u,h,i,0,2),g=Gy(o.data,u,h,i,0,3),p=Zy(s,0,l)+Zy(s,1,c)+Zy(s,2,f)+Zy(s,3,g)+Zy(s,4,1),d=Zy(s,5,l)+Zy(s,6,c)+Zy(s,7,f)+Zy(s,8,g)+Zy(s,9,1),y=Zy(s,10,l)+Zy(s,11,c)+Zy(s,12,f)+Zy(s,13,g)+Zy(s,14,1),v=Zy(s,15,l)+Zy(s,16,c)+Zy(s,17,f)+Zy(s,18,g)+Zy(s,19,1);a&&(p=0,d=0,y=0,v*=g/255),Qy(o.data,u,h,i,0,0,p),Qy(o.data,u,h,i,0,1,d),Qy(o.data,u,h,i,0,2,y),Qy(o.data,u,h,i,0,3,v)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class tv extends iy{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!a&&!s){var o=new uy;this.children.forEach((e=>{o.addBoundingBox(e.getBoundingBox(t))})),i=Math.floor(o.x1),n=Math.floor(o.y1),a=Math.floor(o.width),s=Math.floor(o.height)}var h=this.removeStyles(e,tv.ignoreStyles),u=r.createCanvas(i+a,n+s),l=u.getContext("2d");r.screen.setDefaults(l),this.renderChildren(l),new Jy(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(l,0,0,i+a,n+s);var c=r.createCanvas(i+a,n+s),f=c.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=l.createPattern(u,"no-repeat"),f.fillRect(0,0,i+a,n+s),t.fillStyle=f.createPattern(c,"no-repeat"),t.fillRect(0,0,i+a,n+s),this.restoreStyles(e,h)}render(t){}}tv.ignoreStyles=["mask","transform","clip-path"];var ev=()=>{};class rv extends iy{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=ev,r.closePath=ev),Reflect.apply(i,t,[]),this.children.forEach((i=>{if(void 0!==i.path){var a=void 0!==i.elementTransform?i.elementTransform():null;a||(a=ry.fromElement(e,i)),a&&a.apply(t),i.path(t),r&&(r.closePath=n),a&&a.unapply(t)}})),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class iv extends iy{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var a=0,s=0;i.forEach((t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),s=Math.max(s,e)}));var o=Math.floor(n.width),h=Math.floor(n.height),u=o+2*a,l=h+2*s;if(!(u<1||l<1)){var c=Math.floor(n.x),f=Math.floor(n.y),g=this.removeStyles(e,iv.ignoreStyles),p=r.createCanvas(u,l),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-c+a,-f+s),e.render(d),i.forEach((t=>{"function"==typeof t.apply&&t.apply(d,0,0,u,l)})),t.drawImage(p,0,0,u,l,c-a,f-s,u,l),this.restoreStyles(e,g)}}}render(t){}}iv.ignoreStyles=["filter","transform","clip-path"];class nv extends iy{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class av extends iy{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class sv extends iy{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class ov extends iy{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:a,blurRadius:s}=this,o=a.window?a.window.document.body:null,h=t.canvas;h.id=a.getUniqueId(),o&&(h.style.display="none",o.appendChild(h)),function(t,e,r,i,n,a){if(!(isNaN(a)||a<1)){a|=0;var s=function(t,e,r,i,n){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==sd(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var a=t.getContext("2d");try{return a.getImageData(e,r,i,n)}catch(s){throw new Error("unable to access image data: "+s)}}(t,e,r,i,n);s=function(t,e,r,i,n,a){for(var s,o=t.data,h=2*a+1,u=i-1,l=n-1,c=a+1,f=c*(c+1)/2,g=new ud,p=g,d=1;d<h;d++)p=p.next=new ud,d===c&&(s=p);p.next=g;for(var y=null,v=null,m=0,x=0,b=od[a],w=hd[a],S=0;S<n;S++){p=g;for(var T=o[x],O=o[x+1],A=o[x+2],C=o[x+3],P=0;P<c;P++)p.r=T,p.g=O,p.b=A,p.a=C,p=p.next;for(var E=0,N=0,M=0,R=0,_=c*T,V=c*O,I=c*A,k=c*C,L=f*T,D=f*O,j=f*A,B=f*C,z=1;z<c;z++){var U=x+((u<z?u:z)<<2),F=o[U],H=o[U+1],X=o[U+2],Y=o[U+3],W=c-z;L+=(p.r=F)*W,D+=(p.g=H)*W,j+=(p.b=X)*W,B+=(p.a=Y)*W,E+=F,N+=H,M+=X,R+=Y,p=p.next}y=g,v=s;for(var q=0;q<i;q++){var $=B*b>>>w;if(o[x+3]=$,0!==$){var G=255/$;o[x]=(L*b>>>w)*G,o[x+1]=(D*b>>>w)*G,o[x+2]=(j*b>>>w)*G}else o[x]=o[x+1]=o[x+2]=0;L-=_,D-=V,j-=I,B-=k,_-=y.r,V-=y.g,I-=y.b,k-=y.a;var Q=q+a+1;Q=m+(Q<u?Q:u)<<2,L+=E+=y.r=o[Q],D+=N+=y.g=o[Q+1],j+=M+=y.b=o[Q+2],B+=R+=y.a=o[Q+3],y=y.next;var Z=v,K=Z.r,J=Z.g,tt=Z.b,et=Z.a;_+=K,V+=J,I+=tt,k+=et,E-=K,N-=J,M-=tt,R-=et,v=v.next,x+=4}m+=i}for(var rt=0;rt<i;rt++){var it=o[x=rt<<2],nt=o[x+1],at=o[x+2],st=o[x+3],ot=c*it,ht=c*nt,ut=c*at,lt=c*st,ct=f*it,ft=f*nt,gt=f*at,pt=f*st;p=g;for(var dt=0;dt<c;dt++)p.r=it,p.g=nt,p.b=at,p.a=st,p=p.next;for(var yt=i,vt=0,mt=0,xt=0,bt=0,wt=1;wt<=a;wt++){x=yt+rt<<2;var St=c-wt;ct+=(p.r=it=o[x])*St,ft+=(p.g=nt=o[x+1])*St,gt+=(p.b=at=o[x+2])*St,pt+=(p.a=st=o[x+3])*St,bt+=it,vt+=nt,mt+=at,xt+=st,p=p.next,wt<l&&(yt+=i)}x=rt,y=g,v=s;for(var Tt=0;Tt<n;Tt++){var Ot=x<<2;o[Ot+3]=st=pt*b>>>w,st>0?(st=255/st,o[Ot]=(ct*b>>>w)*st,o[Ot+1]=(ft*b>>>w)*st,o[Ot+2]=(gt*b>>>w)*st):o[Ot]=o[Ot+1]=o[Ot+2]=0,ct-=ot,ft-=ht,gt-=ut,pt-=lt,ot-=y.r,ht-=y.g,ut-=y.b,lt-=y.a,Ot=rt+((Ot=Tt+c)<l?Ot:l)*i<<2,ct+=bt+=y.r=o[Ot],ft+=vt+=y.g=o[Ot+1],gt+=mt+=y.b=o[Ot+2],pt+=xt+=y.a=o[Ot+3],y=y.next,ot+=it=v.r,ht+=nt=v.g,ut+=at=v.b,lt+=st=v.a,bt-=it,vt-=nt,mt-=at,xt-=st,v=v.next,x+=i}}return t}(s,0,0,i,n,a),t.getContext("2d").putImageData(s,e,r)}}(h,e,r,i,n,s),o&&o.removeChild(h)}}class hv extends iy{constructor(){super(...arguments),this.type="title"}}class uv extends iy{constructor(){super(...arguments),this.type="desc"}}var lv={svg:vy,rect:my,circle:xy,ellipse:by,line:wy,polyline:Sy,polygon:Ty,path:fy,pattern:Oy,marker:Ay,defs:Cy,linearGradient:Ny,radialGradient:My,stop:Ry,animate:_y,animateColor:Vy,animateTransform:Iy,font:ky,"font-face":Ly,"missing-glyph":Dy,glyph:gy,text:py,tspan:dy,tref:jy,a:By,textPath:Fy,image:Xy,g:Py,symbol:Yy,style:qy,use:$y,mask:tv,clipPath:rv,filter:iv,feDropShadow:nv,feMorphology:av,feComposite:sv,feColorMatrix:Jy,feGaussianBlur:ov,title:hv,desc:uv};function cv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function fv(){return fv=zh((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise(((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,n,a)=>{i(a)},r.src=t}))})),fv.apply(this,arguments)}class gv{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=gv.createCanvas,createImage:n=gv.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every((t=>t.loaded))}isFontsLoaded(){return this.fonts.every((t=>t.loaded))}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=gv.elementTypes[e];return void 0!==r?new r(this,t):new ny(this,t)}createTextNode(t){return new yy(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cv(Object(r),!0).forEach((function(e){$f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}}function pv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function dv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pv(Object(r),!0).forEach((function(e){$f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}gv.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},gv.createImage=function(t){return fv.apply(this,arguments)},gv.elementTypes=lv;class yv{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new $d(r),this.screen=new Yd(t,r),this.options=r;var i=new gv(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return zh((function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=new $d(i),a=yield n.parse(e);return new yv(t,a,i)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new $d(r).parseFromString(e);return new yv(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return yv.from(t,e,dv(dv({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return yv.fromString(t,e,dv(dv({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return zh((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(dv({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,dv(dv({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}export{By as AElement,Vy as AnimateColorElement,_y as AnimateElement,Iy as AnimateTransformElement,uy as BoundingBox,_d as CB1,Vd as CB2,Id as CB3,kd as CB4,yv as Canvg,xy as CircleElement,rv as ClipPathElement,Cy as DefsElement,uv as DescElement,gv as Document,iy as Element,by as EllipseElement,Jy as FeColorMatrixElement,sv as FeCompositeElement,nv as FeDropShadowElement,ov as FeGaussianBlurElement,av as FeMorphologyElement,iv as FilterElement,hy as Font,ky as FontElement,Ly as FontFaceElement,Py as GElement,gy as GlyphElement,Ey as GradientElement,Xy as ImageElement,wy as LineElement,Ny as LinearGradientElement,Ay as MarkerElement,tv as MaskElement,Kd as Matrix,Dy as MissingGlyphElement,Fd as Mouse,Ed as PSEUDO_ZERO,$d as Parser,fy as PathElement,ly as PathParser,Oy as PatternElement,Ud as Point,Ty as PolygonElement,Sy as PolylineElement,Bd as Property,Ld as QB1,Dd as QB2,jd as QB3,My as RadialGradientElement,my as RectElement,cy as RenderedElement,Qd as Rotate,vy as SVGElement,Wy as SVGFontLoader,Zd as Scale,Yd as Screen,Jd as Skew,ty as SkewX,ey as SkewY,Ry as StopElement,qy as StyleElement,Yy as SymbolElement,jy as TRefElement,dy as TSpanElement,py as TextElement,Fy as TextPathElement,hv as TitleElement,ry as Transform,Gd as Translate,ny as UnknownElement,$y as UseElement,zd as ViewPort,cd as compressSpaces,yv as default,Pd as getSelectorSpecificity,yd as normalizeAttributeName,md as normalizeColor,vd as parseExternalUrl,ld as presets,pd as toNumbers,fd as trimLeft,gd as trimRight,Nd as vectorMagnitude,Rd as vectorsAngle,Md as vectorsRatio};
