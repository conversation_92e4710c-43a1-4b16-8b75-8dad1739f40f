import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as s,u as r}from"./query-vendor-B-7l6Nb3.js";import{d as i,D as t,e as o,f as c,g as n,an as l,a5 as d,B as m,s as u,aM as x,ac as p}from"./index-DIVKaOkK.js";import{A as j,a as g,b as h,c as f,d as v,e as y,f as b,g as C}from"./alert-dialog-CtINwVfb.js";import{T as N}from"./trash-2-DYvB71ud.js";import{a as k}from"./router-BAzpOxbo.js";import{P as w}from"./plus-C0NJ4ejE.js";import{P as q}from"./pencil-BlQ8tUo0.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";function _({category:r,isOpen:x,onClose:p,session:k}){const[w,q]=a.useState(r?.name||""),[_,S]=a.useState(!1),{toast:E}=i(),O=s();return a.useEffect((()=>{x&&q(r?.name||"")}),[x,r]),e.jsxs(e.Fragment,{children:[e.jsx(t,{open:x,onOpenChange:p,children:e.jsxs(o,{children:[e.jsx(c,{children:e.jsx(n,{children:r?"Editar Categoria":"Nova Categoria"})}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(r){const{error:e}=await u.from("pedbook_prescription_categories").update({name:w}).eq("id",r.id);if(e)throw e;E({title:"Categoria atualizada com sucesso!",description:`A categoria ${w} foi atualizada.`})}else{const{error:e}=await u.from("pedbook_prescription_categories").insert([{name:w,user_id:k.user.id}]);if(e)throw e;E({title:"Categoria criada com sucesso!",description:`A categoria ${w} foi adicionada.`})}O.invalidateQueries({queryKey:["prescription-categories"]}),p()}catch(a){E({variant:"destructive",title:"Erro ao salvar categoria",description:a.message||"Ocorreu um erro ao salvar a categoria."})}},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"name",children:"Nome"}),e.jsx(d,{id:"name",value:w,onChange:e=>q(e.target.value),required:!0})]}),e.jsxs("div",{className:"flex justify-between",children:[r&&e.jsxs(m,{type:"button",variant:"destructive",onClick:()=>S(!0),children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Excluir"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{type:"button",variant:"outline",onClick:p,children:"Cancelar"}),e.jsx(m,{type:"submit",children:"Salvar"})]})]})]})]})}),e.jsx(j,{open:_,onOpenChange:S,children:e.jsxs(g,{children:[e.jsxs(h,{children:[e.jsx(f,{children:"Confirmar exclusão"}),e.jsxs(v,{children:['Tem certeza que deseja excluir a categoria "',r?.name,'"? Esta ação não pode ser desfeita.']})]}),e.jsxs(y,{children:[e.jsx(b,{children:"Cancelar"}),e.jsx(C,{onClick:async()=>{try{const{error:e}=await u.from("pedbook_prescription_categories").delete().eq("id",r?.id);if(e)throw e;E({title:"Categoria excluída com sucesso!",description:`A categoria ${r?.name} foi excluída.`}),O.invalidateQueries({queryKey:["prescription-categories"]}),S(!1),p()}catch(e){E({variant:"destructive",title:"Erro ao excluir categoria",description:e.message||"Ocorreu um erro ao excluir a categoria."})}},className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Excluir"})]})]})})]})}function S(){const[s,i]=a.useState(""),[t,o]=a.useState(null),[c,n]=a.useState(!1),[l,j]=a.useState(null),g=k();a.useEffect((()=>{u.auth.getSession().then((({data:{session:e}})=>{e?j(e):g("/")}))}),[g]);const{data:h}=r({queryKey:["prescription-categories"],queryFn:async()=>{const{data:e,error:a}=await u.from("pedbook_prescription_categories").select("*").eq("user_id",l?.user?.id).order("name");if(a)throw a;return e},enabled:!!l?.user?.id}),f=h?.filter((e=>e.name.toLowerCase().includes(s.toLowerCase())));return e.jsxs("div",{className:x.pageBackground("container mx-auto py-8"),children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold dark:text-gray-100",children:"Categorias de Prescrição"}),e.jsxs(m,{onClick:()=>{o(null),n(!0)},children:[e.jsx(w,{className:"mr-2 h-4 w-4"}),"Nova Categoria"]})]}),e.jsxs("div",{className:"relative mb-6",children:[e.jsx(p,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(d,{type:"search",placeholder:"Pesquisar categorias...",value:s,onChange:e=>i(e.target.value),className:"pl-10 dark:bg-slate-800 dark:border-slate-700 dark:text-gray-100"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f?.map((a=>e.jsx("div",{className:x.card("p-6 space-y-2"),children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("div",{children:e.jsx("h3",{className:"text-lg font-semibold dark:text-gray-100",children:a.name})}),e.jsx(m,{variant:"ghost",size:"icon",onClick:()=>{o(a),n(!0)},className:"dark:text-gray-300 dark:hover:text-white",children:e.jsx(q,{className:"h-4 w-4"})})]})},a.id)))}),l&&e.jsx(_,{category:t,isOpen:c,onClose:()=>{n(!1),o(null)},session:l})]})}export{S as default};
