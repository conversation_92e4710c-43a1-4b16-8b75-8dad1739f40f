/*! @license DOMPurify 3.2.6 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */
const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:a,create:l}=Object,{apply:c,construct:s}="undefined"!=typeof Reflect&&Reflect;i||(i=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),s||(s=function(e,t){return new e(...t)});const u=R(Array.prototype.forEach),m=R(Array.prototype.lastIndexOf),p=R(Array.prototype.pop),f=R(Array.prototype.push),d=R(Array.prototype.splice),h=R(String.prototype.toLowerCase),g=R(String.prototype.toString),T=R(String.prototype.match),y=R(String.prototype.replace),E=R(String.prototype.indexOf),A=R(String.prototype.trim),_=R(Object.prototype.hasOwnProperty),S=R(RegExp.prototype.test),N=(b=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s(b,t)});var b;function R(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return c(e,t,o)}}function w(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:h;t&&t(e,null);let i=o.length;for(;i--;){let t=o[i];if("string"==typeof t){const e=r(t);e!==t&&(n(o)||(o[i]=e),t=e)}e[t]=!0}return e}function O(e){for(let t=0;t<e.length;t++)_(e,t)||(e[t]=null);return e}function v(t){const n=l(null);for(const[o,r]of e(t))_(t,o)&&(Array.isArray(r)?n[o]=O(r):r&&"object"==typeof r&&r.constructor===Object?n[o]=v(r):n[o]=r);return n}function D(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return R(n.get);if("function"==typeof n.value)return R(n.value)}e=o(e)}return function(){return null}}const L=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),C=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),x=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),I=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),k=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),M=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),U=i(["#text"]),z=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),P=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),H=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),F=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=a(/<%[\w\W]*|[\w\W]*%>/gm),G=a(/\$\{[\w\W]*/gm),Y=a(/^data-[\-\w.\u00B7-\uFFFF]+$/),j=a(/^aria-[\-\w]+$/),X=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),q=a(/^(?:\w+script|data):/i),$=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),K=a(/^html$/i),V=a(/^[a-z][.\w]*(-[.\w]+)+$/i);var Z=Object.freeze({__proto__:null,ARIA_ATTR:j,ATTR_WHITESPACE:$,CUSTOM_ELEMENT:V,DATA_ATTR:Y,DOCTYPE_NAME:K,ERB_EXPR:W,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:q,MUSTACHE_EXPR:B,TMPLIT_EXPR:G});var J=function t(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window;const o=e=>t(e);if(o.version="3.2.6",o.removed=[],!n||!n.document||9!==n.document.nodeType||!n.Element)return o.isSupported=!1,o;let{document:r}=n;const a=r,c=a.currentScript,{DocumentFragment:s,HTMLTemplateElement:b,Node:R,Element:O,NodeFilter:B,NamedNodeMap:W=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Y,trustedTypes:j}=n,q=O.prototype,$=D(q,"cloneNode"),V=D(q,"remove"),J=D(q,"nextSibling"),Q=D(q,"childNodes"),ee=D(q,"parentNode");if("function"==typeof b){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let te,ne="";const{implementation:oe,createNodeIterator:re,createDocumentFragment:ie,getElementsByTagName:ae}=r,{importNode:le}=a;let ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof e&&"function"==typeof ee&&oe&&void 0!==oe.createHTMLDocument;const{MUSTACHE_EXPR:se,ERB_EXPR:ue,TMPLIT_EXPR:me,DATA_ATTR:pe,ARIA_ATTR:fe,IS_SCRIPT_OR_DATA:de,ATTR_WHITESPACE:he,CUSTOM_ELEMENT:ge}=Z;let{IS_ALLOWED_URI:Te}=Z,ye=null;const Ee=w({},[...L,...C,...x,...k,...U]);let Ae=null;const _e=w({},[...z,...P,...H,...F]);let Se=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ne=null,be=null,Re=!0,we=!0,Oe=!1,ve=!0,De=!1,Le=!0,Ce=!1,xe=!1,Ie=!1,ke=!1,Me=!1,Ue=!1,ze=!0,Pe=!1,He=!0,Fe=!1,Be={},We=null;const Ge=w({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ye=null;const je=w({},["audio","video","img","source","image","track"]);let Xe=null;const qe=w({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),$e="http://www.w3.org/1998/Math/MathML",Ke="http://www.w3.org/2000/svg",Ve="http://www.w3.org/1999/xhtml";let Ze=Ve,Je=!1,Qe=null;const et=w({},[$e,Ke,Ve],g);let tt=w({},["mi","mo","mn","ms","mtext"]),nt=w({},["annotation-xml"]);const ot=w({},["title","style","font","a","script"]);let rt=null;const it=["application/xhtml+xml","text/html"];let at=null,lt=null;const ct=r.createElement("form"),st=function(e){return e instanceof RegExp||e instanceof Function},ut=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!lt||lt!==e){if(e&&"object"==typeof e||(e={}),e=v(e),rt=-1===it.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,at="application/xhtml+xml"===rt?g:h,ye=_(e,"ALLOWED_TAGS")?w({},e.ALLOWED_TAGS,at):Ee,Ae=_(e,"ALLOWED_ATTR")?w({},e.ALLOWED_ATTR,at):_e,Qe=_(e,"ALLOWED_NAMESPACES")?w({},e.ALLOWED_NAMESPACES,g):et,Xe=_(e,"ADD_URI_SAFE_ATTR")?w(v(qe),e.ADD_URI_SAFE_ATTR,at):qe,Ye=_(e,"ADD_DATA_URI_TAGS")?w(v(je),e.ADD_DATA_URI_TAGS,at):je,We=_(e,"FORBID_CONTENTS")?w({},e.FORBID_CONTENTS,at):Ge,Ne=_(e,"FORBID_TAGS")?w({},e.FORBID_TAGS,at):v({}),be=_(e,"FORBID_ATTR")?w({},e.FORBID_ATTR,at):v({}),Be=!!_(e,"USE_PROFILES")&&e.USE_PROFILES,Re=!1!==e.ALLOW_ARIA_ATTR,we=!1!==e.ALLOW_DATA_ATTR,Oe=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ve=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,De=e.SAFE_FOR_TEMPLATES||!1,Le=!1!==e.SAFE_FOR_XML,Ce=e.WHOLE_DOCUMENT||!1,ke=e.RETURN_DOM||!1,Me=e.RETURN_DOM_FRAGMENT||!1,Ue=e.RETURN_TRUSTED_TYPE||!1,Ie=e.FORCE_BODY||!1,ze=!1!==e.SANITIZE_DOM,Pe=e.SANITIZE_NAMED_PROPS||!1,He=!1!==e.KEEP_CONTENT,Fe=e.IN_PLACE||!1,Te=e.ALLOWED_URI_REGEXP||X,Ze=e.NAMESPACE||Ve,tt=e.MATHML_TEXT_INTEGRATION_POINTS||tt,nt=e.HTML_INTEGRATION_POINTS||nt,Se=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&st(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Se.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&st(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Se.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Se.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),De&&(we=!1),Me&&(ke=!0),Be&&(ye=w({},U),Ae=[],!0===Be.html&&(w(ye,L),w(Ae,z)),!0===Be.svg&&(w(ye,C),w(Ae,P),w(Ae,F)),!0===Be.svgFilters&&(w(ye,x),w(Ae,P),w(Ae,F)),!0===Be.mathMl&&(w(ye,k),w(Ae,H),w(Ae,F))),e.ADD_TAGS&&(ye===Ee&&(ye=v(ye)),w(ye,e.ADD_TAGS,at)),e.ADD_ATTR&&(Ae===_e&&(Ae=v(Ae)),w(Ae,e.ADD_ATTR,at)),e.ADD_URI_SAFE_ATTR&&w(Xe,e.ADD_URI_SAFE_ATTR,at),e.FORBID_CONTENTS&&(We===Ge&&(We=v(We)),w(We,e.FORBID_CONTENTS,at)),He&&(ye["#text"]=!0),Ce&&w(ye,["html","head","body"]),ye.table&&(w(ye,["tbody"]),delete Ne.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');te=e.TRUSTED_TYPES_POLICY,ne=te.createHTML("")}else void 0===te&&(te=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(i){return null}}(j,c)),null!==te&&"string"==typeof ne&&(ne=te.createHTML(""));i&&i(e),lt=e}},mt=w({},[...C,...x,...I]),pt=w({},[...k,...M]),ft=function(e){f(o.removed,{element:e});try{ee(e).removeChild(e)}catch(t){V(e)}},dt=function(e,t){try{f(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(n){f(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ke||Me)try{ft(t)}catch(n){}else try{t.setAttribute(e,"")}catch(n){}},ht=function(e){let t=null,n=null;if(Ie)e="<remove></remove>"+e;else{const t=T(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===rt&&Ze===Ve&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=te?te.createHTML(e):e;if(Ze===Ve)try{t=(new Y).parseFromString(o,rt)}catch(a){}if(!t||!t.documentElement){t=oe.createDocument(Ze,"template",null);try{t.documentElement.innerHTML=Je?ne:o}catch(a){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),Ze===Ve?ae.call(t,Ce?"html":"body")[0]:Ce?t.documentElement:i},gt=function(e){return re.call(e.ownerDocument||e,e,B.SHOW_ELEMENT|B.SHOW_COMMENT|B.SHOW_TEXT|B.SHOW_PROCESSING_INSTRUCTION|B.SHOW_CDATA_SECTION,null)},Tt=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof W)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},yt=function(e){return"function"==typeof R&&e instanceof R};function Et(e,t,n){u(e,(e=>{e.call(o,t,n,lt)}))}const At=function(e){let t=null;if(Et(ce.beforeSanitizeElements,e,null),Tt(e))return ft(e),!0;const n=at(e.nodeName);if(Et(ce.uponSanitizeElement,e,{tagName:n,allowedTags:ye}),Le&&e.hasChildNodes()&&!yt(e.firstElementChild)&&S(/<[/\w!]/g,e.innerHTML)&&S(/<[/\w!]/g,e.textContent))return ft(e),!0;if(7===e.nodeType)return ft(e),!0;if(Le&&8===e.nodeType&&S(/<[/\w]/g,e.data))return ft(e),!0;if(!ye[n]||Ne[n]){if(!Ne[n]&&St(n)){if(Se.tagNameCheck instanceof RegExp&&S(Se.tagNameCheck,n))return!1;if(Se.tagNameCheck instanceof Function&&Se.tagNameCheck(n))return!1}if(He&&!We[n]){const t=ee(e)||e.parentNode,n=Q(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=$(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,J(e))}}return ft(e),!0}return e instanceof O&&!function(e){let t=ee(e);t&&t.tagName||(t={namespaceURI:Ze,tagName:"template"});const n=h(e.tagName),o=h(t.tagName);return!!Qe[e.namespaceURI]&&(e.namespaceURI===Ke?t.namespaceURI===Ve?"svg"===n:t.namespaceURI===$e?"svg"===n&&("annotation-xml"===o||tt[o]):Boolean(mt[n]):e.namespaceURI===$e?t.namespaceURI===Ve?"math"===n:t.namespaceURI===Ke?"math"===n&&nt[o]:Boolean(pt[n]):e.namespaceURI===Ve?!(t.namespaceURI===Ke&&!nt[o])&&!(t.namespaceURI===$e&&!tt[o])&&!pt[n]&&(ot[n]||!mt[n]):!("application/xhtml+xml"!==rt||!Qe[e.namespaceURI]))}(e)?(ft(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!S(/<\/no(script|embed|frames)/i,e.innerHTML)?(De&&3===e.nodeType&&(t=e.textContent,u([se,ue,me],(e=>{t=y(t,e," ")})),e.textContent!==t&&(f(o.removed,{element:e.cloneNode()}),e.textContent=t)),Et(ce.afterSanitizeElements,e,null),!1):(ft(e),!0)},_t=function(e,t,n){if(ze&&("id"===t||"name"===t)&&(n in r||n in ct))return!1;if(we&&!be[t]&&S(pe,t));else if(Re&&S(fe,t));else if(!Ae[t]||be[t]){if(!(St(e)&&(Se.tagNameCheck instanceof RegExp&&S(Se.tagNameCheck,e)||Se.tagNameCheck instanceof Function&&Se.tagNameCheck(e))&&(Se.attributeNameCheck instanceof RegExp&&S(Se.attributeNameCheck,t)||Se.attributeNameCheck instanceof Function&&Se.attributeNameCheck(t))||"is"===t&&Se.allowCustomizedBuiltInElements&&(Se.tagNameCheck instanceof RegExp&&S(Se.tagNameCheck,n)||Se.tagNameCheck instanceof Function&&Se.tagNameCheck(n))))return!1}else if(Xe[t]);else if(S(Te,y(n,he,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==E(n,"data:")||!Ye[e])if(Oe&&!S(de,y(n,he,"")));else if(n)return!1;return!0},St=function(e){return"annotation-xml"!==e&&T(e,ge)},Nt=function(e){Et(ce.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Tt(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ae,forceKeepAttr:void 0};let r=t.length;for(;r--;){const a=t[r],{name:l,namespaceURI:c,value:s}=a,m=at(l),f=s;let d="value"===l?f:A(f);if(n.attrName=m,n.attrValue=d,n.keepAttr=!0,n.forceKeepAttr=void 0,Et(ce.uponSanitizeAttribute,e,n),d=n.attrValue,!Pe||"id"!==m&&"name"!==m||(dt(l,e),d="user-content-"+d),Le&&S(/((--!?|])>)|<\/(style|title)/i,d)){dt(l,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){dt(l,e);continue}if(!ve&&S(/\/>/i,d)){dt(l,e);continue}De&&u([se,ue,me],(e=>{d=y(d,e," ")}));const h=at(e.nodeName);if(_t(h,m,d)){if(te&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(c);else switch(j.getAttributeType(h,m)){case"TrustedHTML":d=te.createHTML(d);break;case"TrustedScriptURL":d=te.createScriptURL(d)}if(d!==f)try{c?e.setAttributeNS(c,l,d):e.setAttribute(l,d),Tt(e)?ft(e):p(o.removed)}catch(i){dt(l,e)}}else dt(l,e)}Et(ce.afterSanitizeAttributes,e,null)},bt=function e(t){let n=null;const o=gt(t);for(Et(ce.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)Et(ce.uponSanitizeShadowNode,n,null),At(n),Nt(n),n.content instanceof s&&e(n.content);Et(ce.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,i=null,l=null;if(Je=!e,Je&&(e="\x3c!--\x3e"),"string"!=typeof e&&!yt(e)){if("function"!=typeof e.toString)throw N("toString is not a function");if("string"!=typeof(e=e.toString()))throw N("dirty is not a string, aborting")}if(!o.isSupported)return e;if(xe||ut(t),o.removed=[],"string"==typeof e&&(Fe=!1),Fe){if(e.nodeName){const t=at(e.nodeName);if(!ye[t]||Ne[t])throw N("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof R)n=ht("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!ke&&!De&&!Ce&&-1===e.indexOf("<"))return te&&Ue?te.createHTML(e):e;if(n=ht(e),!n)return ke?null:Ue?ne:""}n&&Ie&&ft(n.firstChild);const c=gt(Fe?e:n);for(;i=c.nextNode();)At(i),Nt(i),i.content instanceof s&&bt(i.content);if(Fe)return e;if(ke){if(Me)for(l=ie.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(Ae.shadowroot||Ae.shadowrootmode)&&(l=le.call(a,l,!0)),l}let m=Ce?n.outerHTML:n.innerHTML;return Ce&&ye["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&S(K,n.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+m),De&&u([se,ue,me],(e=>{m=y(m,e," ")})),te&&Ue?te.createHTML(m):m},o.setConfig=function(){ut(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),xe=!0},o.clearConfig=function(){lt=null,xe=!1},o.isValidAttribute=function(e,t,n){lt||ut({});const o=at(e),r=at(t);return _t(o,r,n)},o.addHook=function(e,t){"function"==typeof t&&f(ce[e],t)},o.removeHook=function(e,t){if(void 0!==t){const n=m(ce[e],t);return-1===n?void 0:d(ce[e],n,1)[0]}return p(ce[e])},o.removeHooks=function(e){ce[e]=[]},o.removeAllHooks=function(){ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}();export{J as default};
