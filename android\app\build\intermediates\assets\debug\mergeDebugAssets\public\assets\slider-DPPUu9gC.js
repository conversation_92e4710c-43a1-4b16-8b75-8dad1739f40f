import{b as e,j as n,h as t,c as r,P as o,u as i}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,u as d}from"./radix-forms-DX-owj97.js";import{u as l,e as u}from"./radix-interactive-DJo-0Sg_.js";import{c}from"./radix-toast-1_gbKn9f.js";import{j as f}from"./index-CrSshpOb.js";var m=["PageUp","PageDown"],p=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},h="Slider",[w,g,S]=c(h),[x,b]=t(h,[S]),[y,j]=x(h),D=a.forwardRef(((t,o)=>{const{name:i,min:d=0,max:l=100,step:u=1,orientation:c="horizontal",disabled:f=!1,minStepsBetweenThumbs:v=0,defaultValue:h=[d],value:g,onValueChange:S=()=>{},onValueCommit:x=()=>{},inverted:b=!1,form:j,...D}=t,R=a.useRef(new Set),E=a.useRef(0),M="horizontal"===c?P:_,[K=[],A]=e({prop:g,defaultProp:h,onChange:e=>{const n=[...R.current];n[E.current]?.focus(),S(e)}}),k=a.useRef(K);function C(e,n,{commit:t}={commit:!1}){const r=function(e){return(String(e).split(".")[1]||"").length}(u),o=function(e,n){const t=Math.pow(10,n);return Math.round(e*t)/t}(Math.round((e-d)/u)*u+d,r),i=s(o,[d,l]);A(((e=[])=>{const r=function(e=[],n,t){const r=[...e];return r[t]=n,r.sort(((e,n)=>e-n))}(e,i,n);if(function(e,n){if(n>0){const t=function(e){return e.slice(0,-1).map(((n,t)=>e[t+1]-n))}(e);return Math.min(...t)>=n}return!0}(r,v*u)){E.current=r.indexOf(i);const n=String(r)!==String(e);return n&&t&&x(r),n?r:e}return e}))}return n.jsx(y,{scope:t.__scopeSlider,name:i,disabled:f,min:d,max:l,valueIndexToChangeRef:E,thumbs:R.current,values:K,orientation:c,form:j,children:n.jsx(w.Provider,{scope:t.__scopeSlider,children:n.jsx(w.Slot,{scope:t.__scopeSlider,children:n.jsx(M,{"aria-disabled":f,"data-disabled":f?"":void 0,...D,ref:o,onPointerDown:r(D.onPointerDown,(()=>{f||(k.current=K)})),min:d,max:l,inverted:b,onSlideStart:f?void 0:function(e){const n=function(e,n){if(1===e.length)return 0;const t=e.map((e=>Math.abs(e-n))),r=Math.min(...t);return t.indexOf(r)}(K,e);C(e,n)},onSlideMove:f?void 0:function(e){C(e,E.current)},onSlideEnd:f?void 0:function(){const e=k.current[E.current];K[E.current]!==e&&x(K)},onHomeKeyDown:()=>!f&&C(d,0,{commit:!0}),onEndKeyDown:()=>!f&&C(l,K.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:n})=>{if(!f){const t=m.includes(e.key)||e.shiftKey&&p.includes(e.key)?10:1,r=E.current;C(K[r]+u*t*n,r,{commit:!0})}}})})})})}));D.displayName=h;var[R,E]=x(h,{startEdge:"left",endEdge:"right",size:"width",direction:1}),P=a.forwardRef(((e,t)=>{const{min:r,max:o,dir:s,inverted:d,onSlideStart:u,onSlideMove:c,onSlideEnd:f,onStepKeyDown:m,...p}=e,[h,w]=a.useState(null),g=i(t,(e=>w(e))),S=a.useRef(),x=l(s),b="ltr"===x,y=b&&!d||!b&&d;function j(e){const n=S.current||h.getBoundingClientRect(),t=L([0,n.width],y?[r,o]:[o,r]);return S.current=n,t(e-n.left)}return n.jsx(R,{scope:e.__scopeSlider,startEdge:y?"left":"right",endEdge:y?"right":"left",direction:y?1:-1,size:"width",children:n.jsx(M,{dir:x,"data-orientation":"horizontal",...p,ref:g,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{const n=j(e.clientX);u?.(n)},onSlideMove:e=>{const n=j(e.clientX);c?.(n)},onSlideEnd:()=>{S.current=void 0,f?.()},onStepKeyDown:e=>{const n=v[y?"from-left":"from-right"].includes(e.key);m?.({event:e,direction:n?-1:1})}})})})),_=a.forwardRef(((e,t)=>{const{min:r,max:o,inverted:s,onSlideStart:d,onSlideMove:l,onSlideEnd:u,onStepKeyDown:c,...f}=e,m=a.useRef(null),p=i(t,m),h=a.useRef(),w=!s;function g(e){const n=h.current||m.current.getBoundingClientRect(),t=L([0,n.height],w?[o,r]:[r,o]);return h.current=n,t(e-n.top)}return n.jsx(R,{scope:e.__scopeSlider,startEdge:w?"bottom":"top",endEdge:w?"top":"bottom",size:"height",direction:w?1:-1,children:n.jsx(M,{"data-orientation":"vertical",...f,ref:p,style:{...f.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{const n=g(e.clientY);d?.(n)},onSlideMove:e=>{const n=g(e.clientY);l?.(n)},onSlideEnd:()=>{h.current=void 0,u?.()},onStepKeyDown:e=>{const n=v[w?"from-bottom":"from-top"].includes(e.key);c?.({event:e,direction:n?-1:1})}})})})),M=a.forwardRef(((e,t)=>{const{__scopeSlider:i,onSlideStart:a,onSlideMove:s,onSlideEnd:d,onHomeKeyDown:l,onEndKeyDown:u,onStepKeyDown:c,...f}=e,v=j(h,i);return n.jsx(o.span,{...f,ref:t,onKeyDown:r(e.onKeyDown,(e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(u(e),e.preventDefault()):m.concat(p).includes(e.key)&&(c(e),e.preventDefault())})),onPointerDown:r(e.onPointerDown,(e=>{const n=e.target;n.setPointerCapture(e.pointerId),e.preventDefault(),v.thumbs.has(n)?n.focus():a(e)})),onPointerMove:r(e.onPointerMove,(e=>{e.target.hasPointerCapture(e.pointerId)&&s(e)})),onPointerUp:r(e.onPointerUp,(e=>{const n=e.target;n.hasPointerCapture(e.pointerId)&&(n.releasePointerCapture(e.pointerId),d(e))}))})})),K="SliderTrack",A=a.forwardRef(((e,t)=>{const{__scopeSlider:r,...i}=e,a=j(K,r);return n.jsx(o.span,{"data-disabled":a.disabled?"":void 0,"data-orientation":a.orientation,...i,ref:t})}));A.displayName=K;var k="SliderRange",C=a.forwardRef(((e,t)=>{const{__scopeSlider:r,...s}=e,d=j(k,r),l=E(k,r),u=a.useRef(null),c=i(t,u),f=d.values.length,m=d.values.map((e=>T(e,d.min,d.max))),p=f>1?Math.min(...m):0,v=100-Math.max(...m);return n.jsx(o.span,{"data-orientation":d.orientation,"data-disabled":d.disabled?"":void 0,...s,ref:c,style:{...e.style,[l.startEdge]:p+"%",[l.endEdge]:v+"%"}})}));C.displayName=k;var N="SliderThumb",I=a.forwardRef(((e,t)=>{const r=g(e.__scopeSlider),[o,s]=a.useState(null),d=i(t,(e=>s(e))),l=a.useMemo((()=>o?r().findIndex((e=>e.ref.current===o)):-1),[r,o]);return n.jsx(H,{...e,ref:d,index:l})})),H=a.forwardRef(((e,t)=>{const{__scopeSlider:s,index:d,name:l,...c}=e,f=j(N,s),m=E(N,s),[p,v]=a.useState(null),h=i(t,(e=>v(e))),g=!p||f.form||!!p.closest("form"),S=u(p),x=f.values[d],b=void 0===x?0:T(x,f.min,f.max),y=function(e,n){return n>2?`Value ${e+1} of ${n}`:2===n?["Minimum","Maximum"][e]:void 0}(d,f.values.length),D=S?.[m.size],R=D?function(e,n,t){const r=e/2;return(r-L([0,50],[0,r])(n)*t)*t}(D,b,m.direction):0;return a.useEffect((()=>{if(p)return f.thumbs.add(p),()=>{f.thumbs.delete(p)}}),[p,f.thumbs]),n.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[m.startEdge]:`calc(${b}% + ${R}px)`},children:[n.jsx(w.ItemSlot,{scope:e.__scopeSlider,children:n.jsx(o.span,{role:"slider","aria-label":e["aria-label"]||y,"aria-valuemin":f.min,"aria-valuenow":x,"aria-valuemax":f.max,"aria-orientation":f.orientation,"data-orientation":f.orientation,"data-disabled":f.disabled?"":void 0,tabIndex:f.disabled?void 0:0,...c,ref:h,style:void 0===x?{display:"none"}:e.style,onFocus:r(e.onFocus,(()=>{f.valueIndexToChangeRef.current=d}))})}),g&&n.jsx(z,{name:l??(f.name?f.name+(f.values.length>1?"[]":""):void 0),form:f.form,value:x},d)]})}));I.displayName=N;var z=e=>{const{value:t,...r}=e,o=a.useRef(null),i=d(t);return a.useEffect((()=>{const e=o.current,n=window.HTMLInputElement.prototype,r=Object.getOwnPropertyDescriptor(n,"value").set;if(i!==t&&r){const n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}}),[i,t]),n.jsx("input",{style:{display:"none"},...r,ref:o,defaultValue:t})};function T(e,n,t){return s(100/(t-n)*(e-n),[0,100])}function L(e,n){return t=>{if(e[0]===e[1]||n[0]===n[1])return n[0];const r=(n[1]-n[0])/(e[1]-e[0]);return n[0]+r*(t-e[0])}}var U=D,V=A,O=C,$=I;const B=a.forwardRef((({className:e,...t},r)=>n.jsxs(U,{ref:r,className:f("relative flex w-full touch-none select-none items-center",e),...t,children:[n.jsx(V,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:n.jsx(O,{className:"absolute h-full bg-primary"})}),n.jsx($,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})));B.displayName=U.displayName;export{B as S};
