import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-Z9xiY6-d.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-DV3Span9.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-DaWfQ4Sj.js";import"./FeedbackTrigger-OUR0fGD_.js";import"./rocket-DWbYpez3.js";import"./target-qP-p0xEy.js";import"./zap-xHziMQfW.js";import"./book-open-Ca6sFj94.js";import"./star-CdRVV6QC.js";import"./circle-help-psGgvKcv.js";import"./instagram-BdLPZF9i.js";import"./collapsible-B6HfSnGs.js";import"./accordion-CjGuVlYu.js";import"./PatientInfoSection-DVjcHVML.js";import"./scale-M-7UdoXa.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-BTLyX4dA.js";import"./user-BnzFohvk.js";import"./DosageDisplay-ByUrR2wr.js";import"./alert-CRxxoxJe.js";import"./plus-CG1D5Wcu.js";import"./lightbulb-fFdJcCpd.js";import"./external-link-C9J9ACvb.js";import"./stethoscope-DDurCu3z.js";import"./syringe-DxrZ6GVK.js";import"./wind-CcYjxQfR.js";import"./bug-IMSBLNdl.js";import"./chevron-left-2OWC_ZeG.js";import"./house-B4q9FJzN.js";function t(){return r.jsx(o,{})}export{t as default};
