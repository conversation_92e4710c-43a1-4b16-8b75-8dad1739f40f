import{r as e}from"./critical-DVX9Inzy.js";import{u as t,f as n,j as o,P as r,c as s,d as a,g as i,h as u,k as c,l as d,n as l,R as p,S as f,m as v,F as h,b as m,a as P}from"./radix-core-6kBL75b5.js";import{c as g,C,R as E,A as b,a as y}from"./radix-interactive-DJo-0Sg_.js";var w,x="dismissableLayer.update",O=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),D=e.forwardRef(((i,u)=>{const{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:l,onFocusOutside:p,onInteractOutside:f,onDismiss:v,...h}=i,m=e.useContext(O),[P,g]=e.useState(null),C=P?.ownerDocument??globalThis?.document,[,E]=e.useState({}),b=t(u,(e=>g(e))),y=Array.from(m.layers),[D]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),F=y.indexOf(D),_=P?y.indexOf(P):-1,A=m.layersWithOutsidePointerEventsDisabled.size>0,L=_>=F,k=function(t,n=globalThis?.document){const o=a(t),r=e.useRef(!1),s=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!r.current){let t=function(){j("dismissableLayer.pointerDownOutside",o,r,{discrete:!0})};const r={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);r.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}}),[n,o]),{onPointerDownCapture:()=>r.current=!0}}((e=>{const t=e.target,n=[...m.branches].some((e=>e.contains(t)));L&&!n&&(l?.(e),f?.(e),e.defaultPrevented||v?.())}),C),T=function(t,n=globalThis?.document){const o=a(t),r=e.useRef(!1);return e.useEffect((()=>{const e=e=>{e.target&&!r.current&&j("dismissableLayer.focusOutside",o,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,o]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}((e=>{const t=e.target;[...m.branches].some((e=>e.contains(t)))||(p?.(e),f?.(e),e.defaultPrevented||v?.())}),C);return n((e=>{_===m.layers.size-1&&(d?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))}),C),e.useEffect((()=>{if(P)return c&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(w=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(P)),m.layers.add(P),R(),()=>{c&&1===m.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=w)}}),[P,C,c,m]),e.useEffect((()=>()=>{P&&(m.layers.delete(P),m.layersWithOutsidePointerEventsDisabled.delete(P),R())}),[P,m]),e.useEffect((()=>{const e=()=>E({});return document.addEventListener(x,e),()=>document.removeEventListener(x,e)}),[]),o.jsx(r.div,{...h,ref:b,style:{pointerEvents:A?L?"auto":"none":void 0,...i.style},onFocusCapture:s(i.onFocusCapture,T.onFocusCapture),onBlurCapture:s(i.onBlurCapture,T.onBlurCapture),onPointerDownCapture:s(i.onPointerDownCapture,k.onPointerDownCapture)})}));function R(){const e=new CustomEvent(x);document.dispatchEvent(e)}function j(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?i(r,s):r.dispatchEvent(s)}D.displayName="DismissableLayer",e.forwardRef(((n,s)=>{const a=e.useContext(O),i=e.useRef(null),u=t(s,i);return e.useEffect((()=>{const e=i.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}}),[a.branches]),o.jsx(r.div,{...n,ref:u})})).displayName="DismissableLayerBranch";var F="Popover",[_,A]=u(F,[g]),L=g(),[k,T]=_(F),N=t=>{const{__scopePopover:n,children:r,open:s,defaultOpen:a,onOpenChange:i,modal:u=!1}=t,c=L(n),d=e.useRef(null),[l,p]=e.useState(!1),[f=!1,v]=m({prop:s,defaultProp:a,onChange:i});return o.jsx(E,{...c,children:o.jsx(k,{scope:n,contentId:P(),triggerRef:d,open:f,onOpenChange:v,onOpenToggle:e.useCallback((()=>v((e=>!e))),[v]),hasCustomAnchor:l,onCustomAnchorAdd:e.useCallback((()=>p(!0)),[]),onCustomAnchorRemove:e.useCallback((()=>p(!1)),[]),modal:u,children:r})})};N.displayName=F;var I="PopoverAnchor";e.forwardRef(((t,n)=>{const{__scopePopover:r,...s}=t,a=T(I,r),i=L(r),{onCustomAnchorAdd:u,onCustomAnchorRemove:c}=a;return e.useEffect((()=>(u(),()=>c())),[u,c]),o.jsx(b,{...i,...s,ref:n})})).displayName=I;var S="PopoverTrigger",W=e.forwardRef(((e,n)=>{const{__scopePopover:a,...i}=e,u=T(S,a),c=L(a),d=t(n,u.triggerRef),l=o.jsx(r.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":Q(u.open),...i,ref:d,onClick:s(e.onClick,u.onOpenToggle)});return u.hasCustomAnchor?l:o.jsx(b,{asChild:!0,...c,children:l})}));W.displayName=S;var M="PopoverPortal",[B,z]=_(M,{forceMount:void 0}),K=e=>{const{__scopePopover:t,forceMount:n,children:r,container:s}=e,a=T(M,t);return o.jsx(B,{scope:t,forceMount:n,children:o.jsx(c,{present:n||a.open,children:o.jsx(d,{asChild:!0,container:s,children:r})})})};K.displayName=M;var U="PopoverContent",Z=e.forwardRef(((e,t)=>{const n=z(U,e.__scopePopover),{forceMount:r=n.forceMount,...s}=e,a=T(U,e.__scopePopover);return o.jsx(c,{present:r||a.open,children:a.modal?o.jsx(q,{...s,ref:t}):o.jsx(G,{...s,ref:t})})}));Z.displayName=U;var q=e.forwardRef(((n,r)=>{const a=T(U,n.__scopePopover),i=e.useRef(null),u=t(r,i),c=e.useRef(!1);return e.useEffect((()=>{const e=i.current;if(e)return l(e)}),[]),o.jsx(p,{as:f,allowPinchZoom:!0,children:o.jsx(H,{...n,ref:u,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:s(n.onCloseAutoFocus,(e=>{e.preventDefault(),c.current||a.triggerRef.current?.focus()})),onPointerDownOutside:s(n.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,o=2===t.button||n;c.current=o}),{checkForDefaultPrevented:!1}),onFocusOutside:s(n.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1})})})})),G=e.forwardRef(((t,n)=>{const r=T(U,t.__scopePopover),s=e.useRef(!1),a=e.useRef(!1);return o.jsx(H,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(s.current||r.triggerRef.current?.focus(),e.preventDefault()),s.current=!1,a.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(s.current=!0,"pointerdown"===e.detail.originalEvent.type&&(a.current=!0));const n=e.target,o=r.triggerRef.current?.contains(n);o&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&a.current&&e.preventDefault()}})})),H=e.forwardRef(((e,t)=>{const{__scopePopover:n,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:l,...p}=e,f=T(U,n),m=L(n);return v(),o.jsx(h,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:a,children:o.jsx(D,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:l,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>f.onOpenChange(!1),children:o.jsx(C,{"data-state":Q(f.open),role:"dialog",id:f.contentId,...m,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})})),J="PopoverClose";function Q(e){return e?"open":"closed"}e.forwardRef(((e,t)=>{const{__scopePopover:n,...a}=e,i=T(J,n);return o.jsx(r.button,{type:"button",...a,ref:t,onClick:s(e.onClick,(()=>i.onOpenChange(!1)))})})).displayName=J,e.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,s=L(n);return o.jsx(y,{...s,...r,ref:t})})).displayName="PopoverArrow";var V=N,X=W,Y=K,$=Z;export{$ as C,Y as P,V as R,X as T};
