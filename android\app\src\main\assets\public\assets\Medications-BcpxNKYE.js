import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-fEz-90BT.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-CR7o3nEo.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-sDEBI9y7.js";import"./FeedbackTrigger-CHct9Uio.js";import"./rocket-BWCmj4V_.js";import"./target-h-6dvqt2.js";import"./zap-BgS9hP5n.js";import"./book-open-mjVJfd0u.js";import"./star-DyEs3k-S.js";import"./circle-help-BhrzdJqz.js";import"./instagram-CpsvBnt_.js";import"./collapsible-B6HfSnGs.js";import"./accordion-Bu1COLnt.js";import"./PatientInfoSection-RWqrjCYC.js";import"./scale-Df85Q8TH.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-5ssh2F-n.js";import"./user-CvHHZeMX.js";import"./DosageDisplay-BaKUtpsD.js";import"./alert-BM83XFmR.js";import"./plus-C7-Pt48y.js";import"./lightbulb-CvV9Za2f.js";import"./external-link-VKWX2k_s.js";import"./stethoscope-DDjxc4Gv.js";import"./syringe-Yp0fWAMP.js";import"./wind-Cdu2gLUF.js";import"./bug-VyH6d1R-.js";import"./chevron-left-CJ5TTXm4.js";import"./house-CAzLMWsM.js";function t(){return r.jsx(o,{})}export{t as default};
