import{j as e}from"./radix-core-6kBL75b5.js";import{r as t,b as s}from"./critical-DVX9Inzy.js";import{c as a,ad as r,a6 as i,j as o,B as n,C as l,az as d,aI as c,s as u,ax as m,X as h,m as p,aL as g,aH as x,d as f,D as y,e as b,f as v,g as w,z as j,ao as _,L as N,U as q,ap as F,u as k,aJ as T,aC as S,W as C,ab as E}from"./index-CNG-Xj2g.js";import{u as A,a as I}from"./query-vendor-B-7l6Nb3.js";import{u as L}from"./useUserData-7KCa27WD.js";import{S as M}from"./skeleton-DcJ7fi2L.js";import{S as O}from"./separator-Cc_jdJJQ.js";import{T as R,a as D,b as Q,c as P}from"./tooltip-DuZWK81I.js";import{e as B}from"./ensureUserId-DKLv_GH_.js";import{S as V}from"./slider-i-IU0_ge.js";import{a as z}from"./router-BAzpOxbo.js";import{J as $}from"./index-k36xMONP.js";import{T as W}from"./target-Dul0NbVV.js";import{B as U}from"./building-2-BEiGuev6.js";import{C as H}from"./calendar-CqqFJfDW.js";import{F as K}from"./file-question-B9RGTZD1.js";import{A as Y}from"./FeedbackTrigger-ik6vfZ65.js";import J from"./Footer-BgCSiPkf.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=a("Shuffle",[["path",{d:"M2 18h1.4c1.3 0 2.5-.6 3.3-1.7l6.1-8.6c.7-1.1 2-1.7 3.3-1.7H22",key:"1wmou1"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 6h1.9c1.5 0 2.9.9 3.6 2.2",key:"10bdb2"}],["path",{d:"M22 18h-5.9c-1.3 0-2.6-.7-3.3-1.8l-.5-.8",key:"vgxac0"}],["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}]]),X=({placeholder:t,value:s,onChange:a})=>e.jsxs("div",{className:"relative",children:[e.jsx(r,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),e.jsx(i,{placeholder:t,value:s,onChange:e=>a(e.target.value),className:"pl-10 bg-gray-100 dark:bg-gray-700 border-none"})]}),Z=({item:t,level:s,isExpanded:a,isSelected:r,questionCount:i,hasChildren:c,onToggleExpand:u,onToggleSelect:m})=>e.jsxs("div",{className:o("flex items-center justify-between p-2 rounded-lg border transition-all duration-200",(()=>{switch(s){case 0:return"border-blue-200 dark:border-blue-800";case 1:return"border-green-200 dark:border-green-800";case 2:return"border-amber-200 dark:border-amber-800";default:return"border-gray-200 dark:border-gray-700"}})(),(()=>{switch(s){case 0:return"hover:bg-blue-50/60 dark:hover:bg-blue-900/30";case 1:return"hover:bg-green-50/60 dark:hover:bg-green-900/30";case 2:return"hover:bg-amber-50/60 dark:hover:bg-amber-900/30";default:return"hover:bg-gray-50/60 dark:hover:bg-gray-800/60"}})(),r&&(()=>{switch(s){case 0:return"bg-blue-50 dark:bg-blue-900/50";case 1:return"bg-green-50 dark:bg-green-900/50";case 2:return"bg-amber-50 dark:bg-amber-900/50";default:return"bg-[#FEF7CD] dark:bg-yellow-900/50"}})(),s>0&&"sm:ml-6 ml-3"),children:[e.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[c&&e.jsx(n,{variant:"ghost",size:"sm",className:"p-0 h-6 w-6 hover:bg-transparent",onClick:()=>u(t.id),children:e.jsx(l,{className:o("h-4 w-4 transition-transform duration-200 text-gray-400 dark:text-gray-500",a&&"rotate-90")})}),e.jsxs("div",{className:"flex items-start gap-3 flex-1 cursor-pointer",onClick:()=>{m(t.id,t.type)},children:[e.jsx("div",{className:o("w-5 h-5 min-w-[1.25rem] min-h-[1.25rem] rounded border transition-all duration-200 flex items-center justify-center flex-shrink-0 mt-0.5",r?"bg-[#FF6B00] border-[#FF6B00] text-white":"border-gray-300 dark:border-gray-600 hover:border-[#FF6B00]"),children:r&&e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",className:"w-3.5 h-3.5 flex-shrink-0",strokeWidth:"2.5",children:e.jsx("polyline",{points:"20 6 9 17 4 12"})})}),e.jsx("span",{className:o("text-sm transition-colors duration-200 leading-relaxed flex-1",r?"text-[#FF6B00] font-medium":"text-gray-700 dark:text-gray-300"),children:t.name})]})]}),e.jsx(d,{variant:"outline",className:o("min-w-[3rem] justify-center transition-all duration-200 border",(()=>{if(r)return"bg-[#FF6B00] text-white border-none";switch(s){case 0:return"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700";case 1:return"bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700";case 2:return"bg-amber-100 dark:bg-amber-900/50 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700";default:return"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-700"}})()),children:i.total})]}),ee=()=>{const[e]=t.useState("residencia"),[s,a]=t.useState(!0);return t.useEffect((()=>{const e=setTimeout((()=>{a(!1)}),100);return()=>clearTimeout(e)}),[e]),{domain:e,isLoading:s,userProfile:null,isResidencia:!0,isReady:!s&&!!e}},te=({specialties:t,themes:s,focuses:a,selectedFilters:r,expandedItems:i,questionCounts:o,onToggleExpand:n,onToggleFilter:l,searchTerm:d,isResidencia:c})=>{ee();const u=d.toLowerCase(),m=a.filter((e=>(o.totalCounts[e.id]||0)>0)),h=m.filter((e=>e.name.toLowerCase().includes(u))),p=s.filter((e=>(o.totalCounts[e.id]||0)>0)).filter((e=>{const t=e.name.toLowerCase().includes(u),s=h.some((t=>t.theme_id===e.id));return t||s||!d}));r.specialties?.[r.specialties.length-1];const g=r.themes?.[r.themes.length-1],x=r.focuses?.[r.focuses.length-1];return p&&0!==p.length?e.jsx(e.Fragment,{children:p.map((t=>{const s=m.filter((e=>{const s=e.name.toLowerCase().includes(u);return e.theme_id===t.id&&(s||!d)}));return e.jsxs("div",{className:"mb-1",children:[e.jsx(Z,{item:{...t,type:"theme"},level:0,isExpanded:i.includes(t.id),isSelected:r.themes?.includes(t.id),questionCount:{total:o.totalCounts[t.id]||0,filtered:o.filteredCounts[t.id]||0},hasChildren:s.length>0,onToggleExpand:n,onToggleSelect:l,shouldScrollIntoView:t.id===g}),i.includes(t.id)&&s.map((t=>e.jsx("div",{className:"mb-1",children:e.jsx(Z,{item:{...t,type:"focus"},level:1,isExpanded:!1,isSelected:r.focuses?.includes(t.id),questionCount:{total:o.totalCounts[t.id]||0,filtered:o.filteredCounts[t.id]||0},hasChildren:!1,onToggleExpand:n,onToggleSelect:l,shouldScrollIntoView:t.id===x})},t.id)))]},t.id)}))}):e.jsx("div",{className:"p-2 text-center text-gray-500",children:"Nenhum tema encontrado para os filtros selecionados"})},se=(e,t)=>A({queryKey:["hierarchical-filter-counts",e,t],queryFn:async()=>{try{const s=e.specialties&&e.specialties.length>0||e.themes&&e.themes.length>0||e.focuses&&e.focuses.length>0,a=e.locations&&e.locations.length>0,r=e.years&&e.years.length>0;e.questionFormats&&e.questionFormats.length;let i=!1;if("locations"===t&&s&&(i=!0),"years"===t&&(s||a)&&(i=!0),"formats"===t&&(s||a||r)&&(i=!0),!i)return{};const o={locations:"exam_location",years:"exam_year",formats:"question_format"},{data:n,error:l}=await u.rpc("get_pediatric_hierarchical_counts",{p_specialty_ids:e.specialties||[],p_theme_ids:e.themes||[],p_focus_ids:e.focuses||[],p_location_ids:e.locations||[],p_years:(e.years||[]).map(Number),p_question_types:e.questionTypes||[],p_question_formats:e.questionFormats||[],p_target_field:o[t]});return l?{}:n||{}}catch(s){return{}}},enabled:!0,staleTime:3e4,cacheTime:3e5,retry:1}),ae=({locations:s=[],selectedLocations:a=[],onToggleLocation:r,selectedFilters:i,searchTerm:o=""})=>{const n=i.specialties&&i.specialties.length>0||i.themes&&i.themes.length>0||i.focuses&&i.focuses.length>0,{data:l,isLoading:d}=se(i,"locations"),c=e=>n&&l?l[e.id]||0:e.question_count||e.count||0,u=t.useMemo((()=>s.filter((e=>c(e)>0))),[s,n,l]),m=t.useMemo((()=>u.filter((e=>e.name.toLowerCase().includes(o.toLowerCase())))),[u,o]),h=t.useMemo((()=>[...m].sort(((e,t)=>c(t)-c(e)))),[m,n,l]);return n&&d?e.jsx("div",{className:"space-y-2",children:[1,2,3].map((t=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-100 dark:bg-gray-800 animate-pulse",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-5 h-5 rounded bg-gray-300 dark:bg-gray-600"}),e.jsx("div",{className:"h-4 w-40 bg-gray-300 dark:bg-gray-600 rounded"})]}),e.jsx("div",{className:"h-6 w-12 bg-gray-300 dark:bg-gray-600 rounded-full"})]},t)))}):e.jsxs("div",{className:"space-y-2",children:[h.map((t=>{const s=c(t);return e.jsx(Z,{item:{...t,type:"location"},level:0,isExpanded:!1,isSelected:a.includes(t.id),questionCount:{total:s,filtered:s},hasChildren:!1,onToggleExpand:()=>{},onToggleSelect:r,className:n?"ring-2 ring-blue-200":void 0},t.id)})),0===h.length&&!d&&e.jsx("div",{className:"text-center text-gray-500 py-4",children:n?"Nenhuma instituição encontrada para as especialidades/temas/focos selecionados":"Nenhuma instituição encontrada"})]})},re=({years:t,selectedYears:s,onToggleYear:a,questionCounts:r,hasActiveFilters:i,selectedFilters:o,searchTerm:n})=>{const l=o.specialties&&o.specialties.length>0||o.themes&&o.themes.length>0||o.focuses&&o.focuses.length>0||o.locations&&o.locations.length>0,{data:d,isLoading:c}=se(o,"years"),u=e=>{const t=e.year.toString();return l&&d?d[t]||0:r.totalCounts[t]||e.question_count||e.count||0},m=[...t.filter((e=>u(e)>0)).filter((e=>e.year.toString().includes(n)))].sort(((e,t)=>t.year-e.year));return l&&c?e.jsx("div",{className:"space-y-2",children:[1,2,3].map((t=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-100 dark:bg-gray-800 animate-pulse",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-5 h-5 rounded bg-gray-300 dark:bg-gray-600"}),e.jsx("div",{className:"h-4 w-16 bg-gray-300 dark:bg-gray-600 rounded"})]}),e.jsx("div",{className:"h-6 w-12 bg-gray-300 dark:bg-gray-600 rounded-full"})]},t)))}):e.jsxs(e.Fragment,{children:[m.map((t=>{const r=t.year.toString(),i=u(t),o=s.includes(r),n={id:r,name:r,type:"year"};return e.jsx(Z,{item:n,level:0,isExpanded:!1,isSelected:o,questionCount:{total:i,filtered:i},hasChildren:!1,onToggleExpand:()=>{},onToggleSelect:e=>a(e),className:l?"ring-2 ring-green-200":void 0},r)})),0===m.length&&!c&&e.jsx("div",{className:"text-center text-gray-500 py-4",children:l?"Nenhum ano encontrado para os filtros selecionados":"Nenhum ano encontrado"})]})},ie=({id:t,name:s,type:a,isSelected:r,onToggle:i,count:n,hasActiveFilters:l})=>e.jsxs("div",{className:o("flex items-center justify-between p-2 rounded-lg transition-all duration-200 cursor-pointer","hover:bg-[#FEF7CD]/50 dark:hover:bg-yellow-900/30",r&&"bg-[#FEF7CD] dark:bg-yellow-900/50"),onClick:i,children:[e.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[e.jsx("div",{className:o("w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center",r?"bg-[#FF6B00] border-black text-white":"border-black hover:border-[#FF6B00]"),children:r&&e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",className:"w-3.5 h-3.5",children:e.jsx("polyline",{points:"20 6 9 17 4 12"})})}),e.jsx("span",{className:o("text-sm transition-colors duration-200",r?"text-[#FF6B00] font-medium":"text-gray-700"),children:s})]}),e.jsx("div",{className:o("min-w-[3rem] text-center px-2 py-0.5 rounded-full text-xs font-medium transition-all duration-200",r?"bg-[#FF6B00] text-white":"bg-gray-100 text-gray-600"),children:n})]}),oe=({selectedTypes:t,onToggleType:s,searchTerm:a="",selectedFilters:r,questionCounts:i})=>{const o=[{id:"teorica-1",name:"Teórica I",count:0},{id:"teorica-2",name:"Teórica II",count:0},{id:"teorico-pratica",name:"Teórico-Prática",count:0}].filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))),n=Object.values(r).some((e=>e.length>0));return e.jsx("div",{className:"space-y-2",children:0===o.length?e.jsx("div",{className:"text-center py-4 text-gray-500",children:"Nenhum tipo de prova encontrado"}):e.jsx("div",{className:"space-y-1",children:o.map((a=>e.jsx(ie,{id:a.id,name:a.name,type:"question_type",isSelected:t.includes(a.id),onToggle:()=>s(a.id),count:a.count,hasActiveFilters:n},a.id)))})})},ne=({selectedFormats:t,onToggleFormat:s,searchTerm:a="",selectedFilters:r,questionCounts:i})=>{const o=r.specialties&&r.specialties.length>0||r.themes&&r.themes.length>0||r.focuses&&r.focuses.length>0||r.locations&&r.locations.length>0||r.years&&r.years.length>0,{data:n,isLoading:l}=se(r,"formats"),d=e=>o&&n?n[e]||0:i?.totalCounts?.[e]||0,c=[{id:"ALTERNATIVAS",name:"📝 Alternativas",description:"Questões de múltipla escolha",count:d("ALTERNATIVAS")},{id:"VERDADEIRO_FALSO",name:"✅ Verdadeiro ou Falso",description:"Questões de V ou F",count:d("VERDADEIRO_FALSO")},{id:"DISSERTATIVA",name:"📄 Dissertativa",description:"Questões abertas/texto livre",count:d("DISSERTATIVA")}].filter((e=>e.count>0)).filter((e=>e.name.toLowerCase().includes(a.toLowerCase())||e.description.toLowerCase().includes(a.toLowerCase())));return Object.values(r).some((e=>Array.isArray(e)&&e.length>0)),o&&l?e.jsx("div",{className:"space-y-2",children:[1,2,3].map((t=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-100 dark:bg-gray-800 animate-pulse",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-5 h-5 rounded bg-gray-300 dark:bg-gray-600"}),e.jsx("div",{className:"h-4 w-32 bg-gray-300 dark:bg-gray-600 rounded"})]}),e.jsx("div",{className:"h-6 w-12 bg-gray-300 dark:bg-gray-600 rounded-full"})]},t)))}):e.jsxs("div",{className:"space-y-2",children:[0===c.length?e.jsx("div",{className:"text-center py-4 text-gray-500",children:o?"Nenhum formato de questão encontrado para os filtros selecionados":"Nenhum formato de questão encontrado"}):e.jsx("div",{className:"space-y-1",children:c.map((a=>{const r={id:a.id,name:a.name,type:"question_format"};return e.jsxs("div",{className:"group",children:[e.jsx(Z,{item:r,level:0,isExpanded:!1,isSelected:t.includes(a.id),questionCount:{total:a.count,filtered:a.count},hasChildren:!1,onToggleExpand:()=>{},onToggleSelect:()=>s(a.id),className:o?"ring-2 ring-pink-200":void 0}),e.jsx("div",{className:"text-xs text-gray-500 ml-8 mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:a.description})]},a.id)}))}),e.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:e.jsxs("div",{className:"text-xs text-blue-700",children:[e.jsx("div",{className:"font-medium mb-1",children:"💡 Tipos de Questão:"}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["• ",e.jsx("strong",{children:"Alternativas:"})," Questões com múltiplas opções"]}),e.jsxs("div",{children:["• ",e.jsx("strong",{children:"V ou F:"})," Questões de verdadeiro ou falso"]}),e.jsxs("div",{children:["• ",e.jsx("strong",{children:"Dissertativa:"})," Questões abertas para texto livre"]})]})]})})]})},le=({activeTab:s,filters:a,selectedFilters:r,expandedItems:i,questionCounts:o,onToggleExpand:n,onToggleFilter:l,searchTerm:d,isLoading:c=!1})=>{const[u,h]=t.useState(""),{domain:p,isResidencia:g,isReady:x}=ee();t.useEffect((()=>{}),[s,p,x]);const f=void 0!==d?d:u;return x?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm pt-1 pb-3",children:e.jsx(X,{placeholder:`Pesquisar ${(()=>{switch(s){case"specialty":return"Especialidades";case"location":return"Instituições";case"year":return"Anos";case"question_type":return"Tipo de Prova";default:return""}})().toLowerCase()}...`,value:void 0!==d?d:u,onChange:void 0!==d?()=>{}:h})}),e.jsx("div",{className:"h-[400px] sm:w-full overflow-hidden",children:e.jsx(m,{className:"h-full","data-prevent-scroll-reset":"true",children:e.jsx("div",{className:"space-y-2 pb-6",children:(()=>{if(c)return e.jsx("div",{className:"space-y-4 p-2",children:Array.from({length:6}).map(((t,s)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{className:"h-4 w-4 rounded-sm"}),e.jsx(M,{className:"h-4 w-full"})]},s)))});switch(s){case"specialty":return e.jsx(te,{specialties:a.specialties,themes:a.themes,focuses:a.focuses,selectedFilters:r,expandedItems:i,questionCounts:o,onToggleExpand:n,onToggleFilter:l,searchTerm:f,isResidencia:g});case"location":return e.jsx(ae,{locations:a.locations,selectedLocations:r.locations,onToggleLocation:e=>l(e,"location"),selectedFilters:r,searchTerm:f});case"year":return e.jsx(re,{years:a.years,selectedYears:r.years,onToggleYear:e=>l(e,"year"),questionCounts:o,hasActiveFilters:!1,selectedFilters:r,searchTerm:f});case"question_type":return e.jsx(oe,{selectedTypes:r.question_types,onToggleType:e=>l(e,"question_type"),selectedFilters:r,searchTerm:f,questionCounts:o});case"question_format":return e.jsx(ne,{selectedFormats:r.question_formats,onToggleFormat:e=>l(e,"question_format"),selectedFilters:r,searchTerm:f,questionCounts:o});default:return null}})()})})})]}):e.jsx("div",{className:"p-4",children:"Carregando filtros..."})},de=e=>({specialty:"specialties",theme:"themes",focus:"focuses",location:"locations",year:"years"}[e]),ce=({selectedFilters:t,availableFilters:s,onRemoveFilter:a,onClearAllFilters:r,totalQuestions:i,isLoading:o})=>{const n=(e,t)=>{if(!s||!e)return e;if("year"===t)return e;if("question_type"===t)return{"teorica-1":"Teórica I","teorica-2":"Teórica II","teorico-pratica":"Teórico-Prática"}[e]||e;const a=de(t),r=(s[a]||[]).find((t=>t.id===e));return r?.name||e},l=s=>{const r=de(s);return(t[r]||[]).map((t=>e.jsxs(d,{variant:"outline",className:"px-3 py-1.5 text-sm font-medium border-2 border-black dark:border-gray-600 bg-[#FEF7CD] dark:bg-yellow-900/30 text-gray-800 dark:text-gray-200 rounded-md hover:bg-[#FEF7CD]/80 dark:hover:bg-yellow-900/50 transition-colors cursor-pointer",onClick:()=>a(t,s),children:[n(t,s),e.jsx(h,{className:"h-3.5 w-3.5 ml-2 text-gray-500 dark:text-gray-400"})]},`${s}-${t}`)))},c=Object.values(t).some((e=>e&&e.length>0));return c?e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl border-2 border-gray-300/70 dark:border-gray-600/70 shadow-md p-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:"🏷️ Filtros Aplicados"}),c&&e.jsx("button",{onClick:r,className:"text-xs text-[#FF6B00] hover:text-[#FF4D00] transition-colors font-medium",children:"Limpar todos"})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[l("specialty"),l("theme"),l("focus"),l("location"),l("year"),l("question_type")]})]})}):null},ue=({questionCount:t,onShowRandomDialog:s,onStartStudy:a,selectedFilters:r})=>{const i=t>300,o=0===t,l=r&&(r.specialties&&r.specialties.length>0||r.themes&&r.themes.length>0||r.focuses&&r.focuses.length>0||r.locations&&r.locations.length>0||r.years&&r.years.length>0||r.question_types&&r.question_types.length>0||r.question_formats&&r.question_formats.length>0);return e.jsxs(p.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:[(o||i)&&e.jsx("div",{className:"mb-4 text-center px-2",children:e.jsx("div",{className:"inline-block text-xs sm:text-sm px-3 sm:px-4 py-2 rounded-lg border-2 max-w-full "+(i?"bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-300 dark:border-red-700/50":"bg-amber-50 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 border-amber-300 dark:border-amber-700/50"),children:i?e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"font-semibold flex items-center justify-center gap-1 flex-wrap",children:["⚠️ Muitas questões (",t.toLocaleString(),")"]}),e.jsxs("div",{className:"text-xs opacity-90 text-center",children:["📚 ",e.jsx("span",{className:"font-medium",children:"Estudar:"})," Filtre até ",300]}),e.jsxs("div",{className:"text-xs opacity-90 text-center",children:["🎲 ",e.jsx("span",{className:"font-medium",children:"Mix:"})," Sem limite"]})]}):e.jsx("div",{className:"flex items-center justify-center gap-1",children:"ℹ️ Selecione filtros para começar"})})}),e.jsx(O,{className:"my-6"}),e.jsx("h4",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-4 text-center flex items-center justify-center gap-2",children:"🚀 Iniciar Estudos"}),e.jsx("div",{className:"flex flex-col sm:flex-row justify-center items-center gap-4",children:e.jsx(R,{children:e.jsxs(D,{children:[e.jsx(Q,{asChild:!0,children:e.jsxs(n,{variant:"outline",onClick:s,className:"w-full sm:w-[200px] border-2 border-primary/20 hover:border-primary/30 hover:bg-primary/5 transition-all duration-300",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Mix de Questões"]})}),e.jsx(P,{children:e.jsx("p",{children:l?"Criar sessão com questões dos filtros selecionados":"Criar sessão com questões aleatórias de pediatria"})})]})})})]})},me=({title:t,count:s,isOpen:a,onToggle:r,children:i})=>e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:e=>{e.stopPropagation(),r(e)},className:"w-full flex items-center justify-between p-4 transition-all duration-300 hover:bg-[#FEF7CD]/50 dark:hover:bg-yellow-900/30",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-lg font-bold text-gray-800 dark:text-gray-200",children:t}),void 0!==s&&s>0&&e.jsx(d,{variant:"outline",className:"bg-[#FF6B00] text-white border-black font-bold px-2 py-1 text-xs",children:s})]}),a?e.jsx(g,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform duration-300"}):e.jsx(x,{className:"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform duration-300"})]}),e.jsx("div",{className:o("transition-all duration-300 ease-in-out overflow-hidden",a?"max-h-[500px] opacity-100":"max-h-0 opacity-0"),children:e.jsx("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:i})})]}),he={specialties:{},themes:{},focuses:{},years:{}},pe=()=>{const[e,s]=t.useState(null),{toast:a}=f(),{domain:r,isResidencia:i,isAdmin:o}=L(),n=I(),l=t.useCallback((async(e,t,i)=>{try{const{data:{user:n}}=await u.auth.getUser();if(!n)return a({title:"Erro de autenticação",description:"Você precisa estar logado para criar uma sessão de estudo.",variant:"destructive"}),null;if(n.id!==e&&!o)return a({title:"Erro de permissão",description:"Você não tem permissão para criar sessões para outro usuário.",variant:"destructive"}),null;if(!t.length)throw new Error("No questions selected for the session");let l=t,d=null;if(t.length>100){const{data:e,error:s}=await u.from("questions").select("specialty_id, theme_id, focus_id").eq("id",t[0]).single();s||(d=e)}else{const{data:e,error:s}=await u.from("questions").select("id, specialty_id, theme_id, focus_id").in("id",t);if(s)throw s;if(l=e.map((e=>e.id)),d=e[0],!l.length)throw new Error("No valid questions found for the session")}const{data:c,error:m}=await u.from("study_sessions").insert({user_id:e,questions:l,total_questions:l.length,current_question_index:0,stats:{correct_answers:0,incorrect_answers:0,time_spent:0,by_specialty:{},by_theme:{},by_focus:{}},status:"in_progress",title:i,specialty_id:d?.specialty_id,theme_id:d?.theme_id,focus_id:d?.focus_id,knowledge_domain:r}).select().single();if(m)throw m;return s(c),c}catch(n){return a({title:"Erro ao criar sessão",description:n.message,variant:"destructive"}),null}}),[a,r,i]);return{activeSession:e,setActiveSession:s,createSession:l,updateSessionProgress:async(e,t,s,a=0)=>!0,completeSession:async e=>{try{const{data:{user:t}}=await u.auth.getUser();if(!t)return void a({title:"Erro de autenticação",description:"Você precisa estar logado para completar a sessão.",variant:"destructive"});const{data:s,error:r}=await u.from("study_sessions").select("*").eq("id",e).single();if(r)if("42P01"===r.code);else if("42501"!==r.code)throw r;if(s&&s.user_id!==t.id&&!o)return void a({title:"Erro de permissão",description:"Você não tem permissão para completar sessões de outro usuário.",variant:"destructive"});const{error:i}=await u.from("study_sessions").update({status:"completed",end_time:(new Date).toISOString()}).eq("id",e);if(i)throw i;n.invalidateQueries({predicate:e=>{const t=e.queryKey[0];return"study-sessions"===t||"user-statistics"===t||"user-study-stats"===t}})}catch(t){a({title:"Erro ao completar sessão",description:"Não foi possível completar a sessão de estudos",variant:"destructive"})}}}},ge=({open:s,onOpenChange:a,filteredQuestions:r,totalQuestionCount:o,filters:l})=>{ee();const{toast:d}=f(),c=z(),{createSession:m}=pe(),[h,p]=t.useState(!1),[g,x]=t.useState(10),[q,F]=t.useState("Mix de Questões - Pediatria"),k=o>0?Math.min(10,o):10;return t.useEffect((()=>{g>k&&x(Math.min(g,k))}),[k,g]),e.jsx(y,{open:s,onOpenChange:a,children:e.jsxs(b,{className:"w-[95vw] max-w-[425px] max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto",children:[e.jsxs(v,{children:[e.jsx(w,{children:"🎲 Mix de Questões"}),e.jsx(j,{children:o>0?`Criar uma sessão com questões dos filtros selecionados (${o} disponíveis).`:"Criar uma sessão com questões aleatórias de pediatria."})]}),e.jsxs("div",{className:"grid gap-6 py-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"title",className:"text-sm font-medium",children:"Título da Sessão"}),e.jsx(i,{id:"title",value:q,onChange:e=>F(e.target.value),className:"w-full",placeholder:"Digite o título da sessão"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(_,{htmlFor:"count",className:"text-sm font-medium",children:"Quantidade de Questões"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(V,{id:"count",value:[g],min:1,max:k,step:1,onValueChange:e=>x(e[0]),className:"w-full"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500",children:[e.jsx("span",{children:"1"}),e.jsxs("span",{className:"font-medium text-base text-gray-900",children:[g," questões"]}),e.jsx("span",{children:k})]})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end gap-3 pt-2",children:[e.jsx(n,{variant:"outline",onClick:()=>a(!1),className:"w-full sm:w-auto order-2 sm:order-1",children:"Cancelar"}),e.jsx(n,{onClick:async()=>{try{p(!0);let e=[];if(l&&(l.specialties&&l.specialties.length>0||l.themes&&l.themes.length>0||l.focuses&&l.focuses.length>0||l.locations&&l.locations.length>0||l.years&&l.years.length>0||l.questionTypes&&l.questionTypes.length>0||l.questionFormats&&l.questionFormats.length>0)){const t={p_specialty_ids:l.specialties||[],p_theme_ids:l.themes||[],p_focus_ids:l.focuses||[],p_location_ids:l.locations||[],p_years:(l.years||[]).map(Number),p_question_types:l.questionTypes||[],p_question_formats:l.questionFormats||[],p_quantity:g},{data:s,error:a}=await u.rpc("get_random_filtered_pediatric_questions",t);if(a)throw a;if(!s||0===s.length)throw new Error("Nenhuma questão encontrada com os filtros selecionados");e=s.map((e=>e.id))}else{const{data:t,error:s}=await u.rpc("get_random_pediatric_questions",{p_quantity:g});if(s)throw s;if(!t||0===t.length)throw new Error("Nenhuma questão encontrada");e=t.map((e=>e.id))}const t=await B(),s=await m(t,e,q);if(!s?.id)throw new Error("Falha ao criar sessão");c(`/questions/${s.id}`)}catch(e){d({title:"Erro ao criar sessão aleatória",description:e.message||"Ocorreu um erro ao criar a sessão de estudos",variant:"destructive"})}finally{p(!1),a(!1)}},disabled:h,className:"w-full sm:w-auto order-1 sm:order-2 bg-[#FF6B00] hover:bg-[#FF6B00]/90",children:h?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"mr-2 h-4 w-4 animate-spin"}),"Criando..."]}):"Criar Sessão"})]})]})})},xe=["🚀 Preparando filtros de pediatria para você...","🔎 Consultando especialidades e temas pediátricos...","💡 Buscando as melhores questões de pediatria…","🤓 Montando o seu estudo de pediatria!","🕑 Só mais alguns segundos!","⚡ Otimizando seu aprendizado pediátrico...","🗂️ Trazendo questões atualizadas de pediatria..."];function fe(){const[s,a]=t.useState(10),[i,o]=t.useState(0);return t.useEffect((()=>{const e=setInterval((()=>{o((e=>(e+1)%xe.length))}),1250),t=setInterval((()=>{a((e=>e>=95?e:e+Math.floor(8*Math.random()+3)))}),350);return()=>{clearInterval(e),clearInterval(t)}}),[]),e.jsxs("div",{className:"min-h-screen w-full bg-gradient-to-br from-[#FEF7CD] via-[#f8fafc] to-[#FEF7CD] dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4",children:[e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f59e0b' fill-opacity='0.1'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/svg%3E\")"}})}),e.jsxs(q,{className:"relative max-w-lg w-full bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border-2 border-yellow-400/70 dark:border-yellow-500/70 shadow-xl rounded-2xl overflow-hidden",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-400 to-purple-500 p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx("div",{className:"bg-white/20 backdrop-blur-sm rounded-full p-3",children:e.jsx(r,{className:"h-6 w-6 text-white"})})}),e.jsx("h2",{className:"text-xl font-bold text-white mb-1",children:"🩺 Preparando Estudos de Pediatria"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"Carregando filtros e questões pediátricas – aguarde um instante!"})]}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/30 rounded-full p-4",children:e.jsx(N,{className:"h-12 w-12 animate-spin text-blue-600 dark:text-blue-400"})})}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-300 font-medium",children:"Progresso"}),e.jsxs("span",{className:"text-blue-600 dark:text-blue-400 font-bold",children:[s,"%"]})]}),e.jsx(F,{value:s,className:"h-3 bg-blue-100 dark:bg-blue-800 rounded-full"})]}),e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-blue-900 dark:text-blue-300 font-semibold text-base transition-all duration-300",children:xe[i]},i)})]})]})]})}const ye=()=>{const{user:a}=k(),{toast:r}=f(),[i,o]=t.useState(0),[l,c]=t.useState(!1),[m,h]=t.useState(!1),[p,g]=t.useState(!0),[x,y]=t.useState(null),[b,v]=t.useState(!1),[w,j]=t.useState(""),[_,N]=t.useState(!0),F=t.useRef(null),E=[{title:"🎯 Filtros de Pediatria",description:"Bem-vindo aos filtros do PedBook! Aqui você pode personalizar sua experiência de estudos em pediatria. Vamos explorar cada seção para você dominar a plataforma.",icon:W,color:"from-blue-500 to-indigo-600",badge:"INÍCIO"},{title:"🧠 Especialidades → Temas → Focos",description:"Esta é a seção mais importante! Aqui você navega pela hierarquia: ESPECIALIDADE → TEMA → FOCO. No PedBook, você terá acesso apenas à pediatria, mas com toda a profundidade de temas e focos específicos.",targetSelector:"[data-tutorial='specialty-section']",icon:T,color:"from-yellow-500 to-orange-600",badge:"HIERÁRQUICO"},{title:"🏥 Instituições",description:"Filtre questões por instituições específicas como UNIFESP, USP, UFMG e outras. Ideal para focar em bancas que você mais estuda ou que caem na sua prova de residência em pediatria.",targetSelector:"[data-tutorial='institution-section']",icon:U,color:"from-blue-500 to-cyan-600"},{title:"📅 Anos das Provas",description:"Selecione anos específicos das provas. Questões mais recentes podem refletir tendências atuais em pediatria, enquanto questões antigas testam conceitos consolidados.",targetSelector:"[data-tutorial='year-section']",icon:H,color:"from-green-500 to-emerald-600"},{title:"🎯 Formato de Questão",description:"Filtre por formato específico: questões discursivas, objetivas, com imagens, casos clínicos pediátricos. Personalize conforme seu estilo de estudo.",targetSelector:"[data-tutorial='question-format-section']",icon:K,color:"from-pink-500 to-rose-600"},{title:"🎲 Opções de Estudo",description:"Após filtrar, você pode: estudar questões FILTRADAS (seguindo seus critérios) ou um MIX ALEATÓRIO (variado para testar conhecimentos gerais de pediatria). Ambas são estratégias válidas!",targetSelector:"[data-tutorial='study-options']",icon:S,color:"from-indigo-500 to-purple-600",badge:"FINAL"}];if(t.useEffect((()=>{(async()=>{if(a)try{const{data:e,error:t}=await u.from("user_preferences").select("pedbook_question_filter_tutorial_completed").eq("user_id",a.id).limit(1);!t&&e&&0!==e.length&&e[0]?.pedbook_question_filter_tutorial_completed?h(!0):c(!0)}catch(e){c(!0)}finally{g(!1)}else g(!1)})()}),[a]),t.useEffect((()=>{if(!l)return;const e=()=>{const e=E[i];if(e.targetSelector){const t=document.querySelector(e.targetSelector);if(t){const e=t.getBoundingClientRect();y(e),setTimeout((()=>{const s=t.offsetTop,a=t.offsetHeight,r=window.innerHeight,i=s-r/2+a/2,o=e.bottom,n=e.top;n>=0&&o<=r&&n<r||window.scrollTo({top:Math.max(0,i),behavior:"smooth"})}),100)}else y(null)}else y(null)};e();const t=()=>e(),s=()=>e();return window.addEventListener("resize",t),window.addEventListener("scroll",s,{passive:!0}),()=>{window.removeEventListener("resize",t),window.removeEventListener("scroll",s)}}),[i,l]),t.useEffect((()=>{const e=document.body.style.overflow;return l&&(document.body.style.overflow="hidden"),()=>{document.body.style.overflow=e}}),[l]),p||m||!l||!a)return null;const A=E[i],I=(()=>{const e=window.innerWidth<768,t=window.innerWidth<1024;if(!x)return{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:e?"90vw":t?"80vw":"450px",maxWidth:e?"350px":"500px",maxHeight:e?"70vh":"80vh",zIndex:1003};const s=e?Math.min(320,.9*window.innerWidth):t?Math.min(400,.8*window.innerWidth):450,a=e?280:350,r=16;let o,n;return e?x.top-a-r>0?(o=x.top-a-r,n=Math.max(r,Math.min(window.innerWidth-s-r,x.left+x.width/2-s/2))):x.bottom+a+r<window.innerHeight?(o=x.bottom+r,n=Math.max(r,Math.min(window.innerWidth-s-r,x.left+x.width/2-s/2))):(o=r,n=Math.max(r,Math.min(window.innerWidth-s-r,x.left+x.width/2-s/2))):1===i?(o=Math.max(r,x.top-a-32),n=Math.max(r,Math.min(window.innerWidth-s-r,x.left+x.width/2-s/2))):x.top-a-r>0?(o=x.top-a-r,n=Math.max(r,Math.min(window.innerWidth-s-r,x.left+x.width/2-s/2))):x.right+s+r<window.innerWidth?(n=x.right+r,o=Math.max(r,Math.min(window.innerHeight-a-r,x.top+x.height/2-a/2))):x.left-s-r>0?(n=x.left-s-r,o=Math.max(r,Math.min(window.innerHeight-a-r,x.top+x.height/2-a/2))):(o=x.bottom+r,n=Math.max(r,Math.min(window.innerWidth-s-r,x.left+x.width/2-s/2))),{position:"fixed",top:`${o}px`,left:`${n}px`,width:`${s}px`,maxHeight:e?"70vh":"80vh",zIndex:1003,transform:""}})(),L=window.innerWidth<768;return e.jsxs("div",{ref:F,className:"fixed inset-0 z-[1000] touch-none",style:{overflow:"hidden",height:"100vh"},children:[e.jsxs("svg",{className:"fixed inset-0 w-full h-full pointer-events-none",style:{zIndex:1001},children:[e.jsx("defs",{children:e.jsxs("mask",{id:"filter-tutorial-mask",children:[e.jsx("rect",{width:"100%",height:"100%",fill:"white"}),x&&e.jsx("rect",{x:x.left,y:x.top,width:x.width,height:x.height,fill:"black"})]})}),e.jsx("rect",{width:"100%",height:"100%",fill:"rgba(0, 0, 0, 0.7)",mask:"url(#filter-tutorial-mask)"})]}),x&&e.jsx("div",{className:"fixed pointer-events-none z-[1002] border-4 border-yellow-400 rounded-lg animate-pulse",style:{top:x.top-4,left:x.left-4,width:x.width+8,height:x.height+8}}),e.jsxs(q,{className:"p-0 bg-white rounded-2xl shadow-2xl z-[1004] animate-in fade-in-50 slide-in-from-bottom-4 fixed border-2 border-gray-100 overflow-hidden "+(L?"text-sm":""),style:{...I,overflowY:"auto"},children:[e.jsx("div",{className:`bg-gradient-to-r ${A.color||"from-blue-500 to-purple-600"} ${L?"p-3":"p-4"} text-white relative`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[A.icon&&e.jsx("div",{className:`bg-white/20 ${L?"p-1.5":"p-2"} rounded-lg`,children:s.createElement(A.icon,{className:L?"h-4 w-4":"h-5 w-5"})}),e.jsxs("div",{children:[e.jsx("h3",{className:(L?"text-base":"text-lg")+" font-bold",children:A.title}),e.jsxs("div",{className:(L?"text-xs":"text-sm")+" text-white/80",children:["Passo ",i+1," de ",E.length]})]})]}),A.badge&&e.jsx(d,{variant:"secondary",className:"bg-white/20 text-white border-white/30 "+(L?"text-xs px-2 py-0.5":""),children:A.badge})]})}),e.jsxs("div",{className:(L?"p-4":"p-6")+" space-y-3",children:[b&&w&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"}),e.jsx("p",{className:"text-blue-700 text-sm font-medium",children:w})]})}),e.jsx("p",{className:"text-gray-700 leading-relaxed "+(L?"text-sm":"text-base"),children:A.description}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:`bg-gradient-to-r ${A.color||"from-blue-500 to-purple-600"} h-2 rounded-full transition-all duration-500`,style:{width:(i+1)/E.length*100+"%"}})}),e.jsxs("div",{className:"flex justify-between items-center "+(L?"pt-1":"pt-2"),children:[e.jsx("div",{className:"text-xs text-gray-500",children:0===i?"Vamos começar!":i===E.length-1?"Quase lá!":"Continue explorando"}),e.jsx(n,{onClick:()=>{if(_)if(i<E.length-1){const e=i+1;o(e),1===e&&(v(!0),N(!1),j("🎯 Abrindo uma especialidade..."),setTimeout((()=>{const e=document.querySelector('[data-tutorial="specialty-section"]');if(!e)return v(!1),void N(!0);const t=e.querySelectorAll(".lucide-chevron-right");if(t.length>0){const s=t[0].closest("button");s&&(s.click(),setTimeout((()=>{j("📚 Agora abrindo um tema..."),setTimeout((()=>{const t=e.querySelectorAll(".lucide-chevron-right");if(t.length>1){const s=t[1].closest("button");s&&(s.click(),setTimeout((()=>{j("🎯 E agora um foco específico..."),setTimeout((()=>{const t=e.querySelectorAll(".lucide-chevron-right");if(t.length>2){const e=t[2].closest("button");e&&e.click()}setTimeout((()=>{j("✨ Viu como é fácil? Agora você pode prosseguir!"),setTimeout((()=>{v(!1),N(!0),j("")}),1500)}),1e3)}),800)}),1e3))}else setTimeout((()=>{j("✨ Viu como é fácil? Agora você pode prosseguir!"),setTimeout((()=>{v(!1),N(!0),j("")}),1500)}),1e3)}),800)}),1e3))}else setTimeout((()=>{v(!1),N(!0)}),1e3)}),800))}else c(!1),h(!0),(async()=>{if(a)try{const{data:e,error:t}=await u.from("user_preferences").select("id").eq("user_id",a.id).limit(1);if(t)return;let s;if(e&&e.length>0){const{error:t}=await u.from("user_preferences").update({pedbook_question_filter_tutorial_completed:!0,updated_at:(new Date).toISOString()}).eq("id",e[0].id);s=t}else{const{error:e}=await u.from("user_preferences").insert({user_id:a.id,pedbook_question_filter_tutorial_completed:!0});s=e}s?r({title:"Erro",description:"Não foi possível salvar o status do tutorial",variant:"destructive"}):$.success("Tutorial de filtros concluído!",{description:"Agora você sabe como usar todos os filtros de pediatria"})}catch(e){}})()},disabled:!_,className:`bg-gradient-to-r ${A.color||"from-blue-500 to-purple-600"} hover:opacity-90 text-white font-medium ${L?"px-4 py-1.5 text-sm":"px-6 py-2"} rounded-lg transition-all duration-200 flex items-center gap-2 ${_?"":"opacity-50 cursor-not-allowed"}`,children:_?i<E.length-1?e.jsxs(e.Fragment,{children:["Próximo",e.jsx(Y,{className:L?"h-3 w-3":"h-4 w-4"})]}):e.jsxs(e.Fragment,{children:["Entendi!",e.jsx(C,{className:L?"h-3 w-3":"h-4 w-4"})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent"}),"Aguarde..."]})})]})]})]})]})},be=({selectedFilters:s,setSelectedFilters:a,searchTerm:r,setSearchTerm:o,setQuestions:l,onSessionCreated:d,onShowRandomDialog:m})=>{const{toast:h}=f(),{domain:p,isResidencia:g,isReady:x}=ee(),[N,q]=t.useState("specialty"),[F,k]=t.useState(!1),[T,S]=t.useState(!1),[C,E]=t.useState(""),[M,O]=t.useState(1),[R,D]=t.useState(!1),[Q,P]=t.useState(!1),[V,z]=t.useState(0),{handleCreateSession:$}=(()=>{const{toast:e}=f(),{createSession:s}=pe(),{fetchPediatricQuestions:a}={buildQuery:(e,t)=>{let s=u.from("questions").select("*").eq("specialty_name","pediatria").limit(1e3);const a=[];if(e.specialties?.length>0){const t=`specialty_id.in.(${e.specialties.join(",")})`;a.push(t)}if(e.themes?.length>0){const t=`theme_id.in.(${e.themes.join(",")})`;a.push(t)}if(e.focuses?.length>0){const t=`focus_id.in.(${e.focuses.join(",")})`;a.push(t)}if(a.length>0){const e=a.join(",");s=s.or(e)}return e.locations?.length>0&&(s=s.in("exam_location",e.locations)),e.years?.length>0&&(s=s.in("exam_year",e.years.map(Number))),e.question_types?.length>0&&(s=s.in("assessment_type",e.question_types)),e.question_formats?.length>0&&(s=s.in("question_format",e.question_formats)),s},fetchPediatricQuestions:async(e,t=1,s=50,a)=>{const r=a?"get_filtered_pediatric_questions_excluding_answered":"get_filtered_pediatric_questions",i={specialty_ids:e.specialties||[],theme_ids:e.themes||[],focus_ids:e.focuses||[],location_ids:e.locations||[],years:e.years?.map(Number)||[],question_types:e.questionTypes||[],question_formats:e.questionFormats||[],page_number:t,items_per_page:s,...a&&{user_id:a}};return await u.rpc(r,i)},countPediatricQuestions:async(e,t)=>{const s=t?"get_filtered_pediatric_question_count_excluding_answered":"get_filtered_pediatric_question_count",a={specialty_ids:e.specialties||[],theme_ids:e.themes||[],focus_ids:e.focuses||[],location_ids:e.locations||[],years:e.years?.map(Number)||[],question_types:e.questionTypes||[],question_formats:e.questionFormats||[],...t&&{user_id:t}};return await u.rpc(s,a)}},{user:r}=L(),{domain:i}=ee();return{handleCreateSession:t.useCallback((async(t,i,o)=>{try{if(o&&o.length>0){const t=o.map((e=>e.id)),a=await B(),r=await s(a,t,i);return r?.id?r:(e({title:"Erro ao criar sessão",description:"Não foi possível criar a sessão de estudos",variant:"destructive"}),null)}let n,l;const{data:d,error:c}=await a(t,1,1e3,t.excludeAnswered&&r?.id?r.id:void 0);if(c?l=c:n=d?.questions||[],l)throw l;if(!n?.length)return e({title:"Nenhuma questão encontrada",description:"Tente outros filtros para encontrar questões de pediatria",variant:"destructive"}),null;const u=await B(),m=await s(u,n.map((e=>e.id)),i);return m?.id?m:(e({title:"Erro ao criar sessão",description:"Não foi possível criar a sessão de estudos",variant:"destructive"}),null)}catch(n){return e({title:"Erro ao criar sessão",description:n.message,variant:"destructive"}),null}}),[e,s,r?.id,i])}})(),{data:W}=(()=>{const e=I();return t.useEffect((()=>()=>{}),[e]),A({queryKey:["correct-questions"],queryFn:async()=>{const{data:e}=await u.auth.getUser(),t=e.user?.id;if(!t)throw new Error("Usuário não autenticado");const{data:s,error:a}=await u.from("user_answers").select("question_id").eq("user_id",t).eq("is_correct",!0);if(a)throw a;const r=[...new Set(s?.map((e=>e.question_id))||[])];return 0===r.length?null:{ids:r,count:r.length}},staleTime:3e4})})();t.useEffect((()=>{}),[p,g,x]),t.useEffect((()=>{z(W?.count?W.count:0)}),[W]);const{selectedFilters:U,setSelectedFilters:H,expandedItems:K,toggleExpand:Y}=(e=>{const[s,a]=t.useState(e),[r,i]=t.useState([]),o=t.useCallback(((e,t)=>{const r=de(t),i=s[r]||[],o=i.includes(e)?i.filter((t=>t!==e)):[...i,e];a((e=>({...e,[r]:o})))}),[s]),n=t.useCallback((e=>{i((t=>t.includes(e)?t.filter((t=>t!==e)):[...t,e]))}),[]);return{selectedFilters:s,setSelectedFilters:a,expandedItems:r,handleToggleFilter:o,toggleExpand:n}})(s||{specialties:[],themes:[],focuses:[],locations:[],years:[],question_types:[],question_formats:[],excludeAnswered:!1}),{questionCount:J,isUpdating:G,isFetching:X,handleFilterToggle:Z,setSelectedFilters:te}=(({initialFilters:e,onFiltersChange:s,debounceMs:a=300})=>{const[r,i]=t.useState(e),[o,n]=t.useState(!1),{domain:l,isReady:d}=ee(),m=I(),h=t.useRef(),{count:p,isFetching:g}=(({filters:e,debounceMs:s=300,enabled:a=!0})=>{const{domain:r,isReady:i}=ee(),{user:o}=L(),n=c(e,s),l=t.useMemo((()=>!(!n||"object"!=typeof n)&&Object.values(n).some((e=>!!Array.isArray(e)&&e.length>0))),[n]),d=A({queryKey:["question-count",n,r,o?.id],queryFn:async()=>{if(!l||!i||!r)return{total_count:0};try{const e=n?.excludeAnswered&&o?.id?"get_filtered_pediatric_question_count_excluding_answered":"get_filtered_pediatric_question_count",t={specialty_ids:n?.specialties||[],theme_ids:n?.themes||[],focus_ids:n?.focuses||[],location_ids:n?.locations||[],years:(n?.years||[]).map(Number),question_types:n?.question_types||[],question_formats:n?.question_formats||[],...n?.excludeAnswered&&o?.id&&{user_id:o.id}},{data:s,error:a}=await u.rpc(e,t);return a?{total_count:0}:s||{total_count:0}}catch(e){return{total_count:0}}},enabled:a&&l&&i&&!!r,staleTime:12e4,cacheTime:3e5,refetchOnWindowFocus:!1,retry:1,retryDelay:500});return{...d,count:d.data?.total_count||0,isLoading:d.isLoading,isFetching:d.isFetching}})({filters:r,debounceMs:a,enabled:d&&!!l}),x=t.useCallback(((e,t)=>{const r={specialty:"specialties",theme:"themes",focus:"focuses",location:"locations",year:"years",question_type:"question_types",question_format:"question_formats"}[t];i((t=>{const a=[...t[r]||[]],i=a.includes(e),o={...t,[r]:i?a.filter((t=>t!==e)):[...a,e]};return s&&s(o),o})),n(!0),h.current&&clearTimeout(h.current),h.current=setTimeout((()=>{n(!1)}),a+100)}),[s,a]);return t.useEffect((()=>{r&&"object"==typeof r&&Object.values(r).some((e=>Array.isArray(e)&&e.length>0))}),[r,d,l,m]),t.useEffect((()=>{i(e)}),[e]),t.useEffect((()=>()=>{h.current&&clearTimeout(h.current)}),[]),{selectedFilters:r,setSelectedFilters:i,handleFilterToggle:x,questionCount:p,isUpdating:o||g,isFetching:g}})({initialFilters:U,onFiltersChange:e=>{H(e),a(e)},debounceMs:300}),{data:se,isLoading:ae,error:re}=A({queryKey:["pediatric-question-metadata"],queryFn:async()=>{try{const{data:e,error:t}=await u.rpc("get_pediatric_questions_metadata");if(t)throw t;return e||{specialties:[],themes:[],focuses:[],locations:[],years:[]}}catch(e){throw e}},enabled:!0,staleTime:36e5,cacheTime:72e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,refetchInterval:!1,refetchIntervalInBackground:!1,retry:1,retryOnMount:!1}),{data:ie,isLoading:oe,isFetching:ne}=((e,s=1,a=50,r=!0)=>{const{domain:i,isReady:o}=ee(),{user:n}=L(),l=t.useMemo((()=>!(!e||"object"!=typeof e)&&Object.values(e).some((e=>!!Array.isArray(e)&&e.length>0))),[e]),d=A({queryKey:["filtered-questions",e,s,a,i,e?.excludeAnswered,n?.id],queryFn:async()=>{try{if(!l||!o||!i)return{questions:[],total_count:0,filtered_counts:he,excluded_questions_count:0};let t,r;(e?.years||[]).map(Number);const d=e?.excludeAnswered&&n?.id?"get_filtered_pediatric_questions_excluding_answered":"get_filtered_pediatric_questions",c={specialty_ids:e.specialties||[],theme_ids:e.themes||[],focus_ids:e.focuses||[],location_ids:e.locations||[],years:(e.years||[]).map(Number),question_types:e.question_types||[],question_formats:e.question_formats||[],page_number:s,items_per_page:a,...e?.excludeAnswered&&n?.id&&{user_id:n.id}},m=await u.rpc(d,c);if(t=m.data,r=m.error,r)throw r;if(!t)throw new Error("No data returned");const h=t;let p=h.questions||[],g=h.total_count||0,x=0;p.length;const f=p.reduce(((e,t)=>(t.specialty?.id&&(e.specialties[t.specialty.id]=(e.specialties[t.specialty.id]||0)+1),t.theme?.id&&(e.themes[t.theme.id]=(e.themes[t.theme.id]||0)+1),t.focus?.id&&(e.focuses[t.focus.id]=(e.focuses[t.focus.id]||0)+1),t.year&&(e.years[t.year]=(e.years[t.year]||0)+1),e)),{...he});return{questions:p,total_count:g,filtered_counts:f,excluded_questions_count:x}}catch(t){throw t}},enabled:r&&l&&o&&!!i,staleTime:6e5,cacheTime:9e5,refetchOnWindowFocus:!1,refetchOnMount:!1});return{...d,isLoading:d.isLoading,isFetching:d.isFetching,data:d.data||{questions:[],total_count:0,filtered_counts:he,excluded_questions_count:0}}})(U,M,50,!1),xe=R?J:0,be=[],ve=ae,we=oe||ne;t.useEffect((()=>{}),[]),t.useEffect((()=>{if(!U||"object"!=typeof U)return;const e=Object.values(U).some((e=>Array.isArray(e)&&e.length>0));R!==e&&D(e)}),[U,R]);const je=t.useMemo((()=>(()=>{const e={},t={};if(se&&(se.specialties.forEach((t=>{const s=t.question_count||t.count||0;e[t.id]=s})),se.themes.forEach((t=>{const s=t.question_count||t.count||0;e[t.id]=s})),se.focuses.forEach((t=>{const s=t.question_count||t.count||0;e[t.id]=s})),se.locations.forEach((t=>{const s=t.question_count||t.count||0;e[t.id]=s})),se.years.forEach((t=>{const s=t.question_count||t.count||0;e[t.year.toString()]=s}))),e.ALTERNATIVAS=14921,e.VERDADEIRO_FALSO=285,e.DISSERTATIVA=159,be){const e={};be.forEach((t=>{t.specialty?.id&&(e[t.specialty.id]=(e[t.specialty.id]||0)+1),t.theme?.id&&(e[t.theme.id]=(e[t.theme.id]||0)+1),t.focus?.id&&(e[t.focus.id]=(e[t.focus.id]||0)+1),t.question_format&&(e[t.question_format]=(e[t.question_format]||0)+1)})),Object.assign(t,e)}return{totalCounts:e,filteredCounts:t}})()),[se,be]);if(!x||ve)return e.jsx(fe,{});const _e=g,Ne="revalida"!==p,qe=se||{specialties:[],themes:[],focuses:[],locations:[],years:[]};return e.jsxs("div",{className:"space-y-6 p-6",children:[e.jsx("div",{className:"relative",children:e.jsx("div",{className:"bg-gradient-to-r from-[#FF6B00] to-[#FF8800] rounded-2xl p-4 sm:p-6 border-2 border-black shadow-lg",children:e.jsxs("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-4",children:[e.jsxs("div",{className:"text-center lg:text-left",children:[e.jsx("h1",{className:"text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2",children:"🎯 Banco de Questões"}),e.jsx("p",{className:"text-white/90 text-sm lg:text-base",children:"Filtre e encontre as questões perfeitas para seu estudo"})]}),e.jsxs("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl p-3 sm:p-4 border-2 border-black dark:border-gray-600 min-w-[140px] sm:min-w-[160px] text-center",children:[e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 mb-1",children:"Questões Encontradas"}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-[#FF6B00]",children:G||X?"...":0===xe?e.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400 font-medium",children:"Selecione filtros"}):xe.toLocaleString()})]})]})})}),e.jsx(ce,{selectedFilters:U,availableFilters:qe,onRemoveFilter:Z,onClearAllFilters:()=>{const e={specialties:[],themes:[],focuses:[],locations:[],years:[],question_types:[],question_formats:[],excludeAnswered:!1};H(e),te(e),P(!1),a(e)},totalQuestions:xe,isLoading:we}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl border-2 border-yellow-400/70 dark:border-yellow-500/70 shadow-md overflow-hidden","data-tutorial":"specialty-section",children:e.jsx(me,{title:"🧠 Temas",count:(U.themes?.length||0)+(U.focuses?.length||0),isOpen:"specialty"===N,onToggle:()=>q("specialty"===N?null:"specialty"),children:e.jsx(le,{activeTab:"specialty",filters:qe,selectedFilters:U,expandedItems:K,onToggleExpand:Y,onToggleFilter:Z,questionCounts:je,isLoading:ae})})}),!1,Ne&&_e&&e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl border-2 border-blue-400/70 dark:border-blue-500/70 shadow-md overflow-hidden","data-tutorial":"institution-section",children:e.jsx(me,{title:"🏥 Instituições",count:U.locations?.length||0,isOpen:"institution"===N,onToggle:()=>q("institution"===N?null:"institution"),children:e.jsx(le,{activeTab:"location",filters:qe,selectedFilters:U,expandedItems:K,onToggleExpand:Y,onToggleFilter:Z,questionCounts:je,isLoading:ae})})}),e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl border-2 border-green-400/70 dark:border-green-500/70 shadow-md overflow-hidden","data-tutorial":"year-section",children:e.jsx(me,{title:"📅 Anos",count:U.years?.length||0,isOpen:"year"===N,onToggle:()=>q("year"===N?null:"year"),children:e.jsx(le,{activeTab:"year",filters:qe,selectedFilters:U,expandedItems:K,onToggleExpand:Y,onToggleFilter:Z,questionCounts:je,isLoading:ae})})}),!1,e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl border-2 border-pink-400/70 dark:border-pink-500/70 shadow-md overflow-hidden","data-tutorial":"question-format-section",children:e.jsx(me,{title:"🎯 Formato de Questão",count:U.question_formats?.length||0,isOpen:"question_format"===N,onToggle:()=>q("question_format"===N?null:"question_format"),children:e.jsx(le,{activeTab:"question_format",filters:qe,selectedFilters:U,expandedItems:K,onToggleExpand:Y,onToggleFilter:Z,questionCounts:je,isLoading:ae})})})]}),e.jsx("div",{className:"bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl border-2 border-[#FF6B00]/70 dark:border-orange-500/70 shadow-lg p-6","data-tutorial":"study-options",children:e.jsx(ue,{questionCount:xe,onShowRandomDialog:()=>{S(!0)},onStartStudy:()=>k(!0),selectedFilters:U})}),e.jsx(y,{open:F,onOpenChange:k,children:e.jsxs(b,{className:"w-[95vw] max-w-md max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto",children:[e.jsxs(v,{children:[e.jsx(w,{children:"Iniciar Sessão de Estudos"}),e.jsx(j,{children:"Digite um título para identificar esta sessão de estudos"})]}),e.jsxs("div",{className:"space-y-6 py-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"session-title",className:"text-sm font-medium",children:"Título da Sessão"}),e.jsx(i,{id:"session-title",placeholder:"Ex: Revisão Clínica Médica",value:C,onChange:e=>E(e.target.value),className:"w-full"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end gap-3 pt-2",children:[e.jsx(n,{variant:"outline",onClick:()=>k(!1),className:"w-full sm:w-auto order-2 sm:order-1",children:"Cancelar"}),e.jsx(n,{onClick:async()=>{if(C.trim())if(0!==xe)if(xe>300)h({title:"Erro",description:"O limite máximo é de 300 questões por sessão de estudos",variant:"destructive"});else try{const e=await $(U,C);if(!e?.id)throw new Error("Failed to create session");k(!1),E(""),d(e.id)}catch(e){h({title:"Erro ao criar sessão",description:e.message,variant:"destructive"})}else h({title:"Erro",description:"Selecione pelo menos uma questão para iniciar os estudos",variant:"destructive"});else h({title:"Erro",description:"Por favor, insira um título para a sessão de estudos",variant:"destructive"})},className:"w-full sm:w-auto order-1 sm:order-2",children:"Começar"})]})]})]})}),e.jsx(ge,{open:T,onOpenChange:S,domain:p,filteredQuestions:be,totalQuestionCount:xe,filters:U}),e.jsx(ye,{})]})},ve=()=>{const s=z(),{toast:a}=f(),[r,i]=t.useState(""),[o,n]=t.useState([]),[l,d]=t.useState({specialties:[],themes:[],focuses:[],locations:[],years:[],question_types:[],question_formats:[],excludeAnswered:!1});return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx(E,{}),e.jsx("main",{className:"container mx-auto px-4 py-6 max-w-6xl",children:e.jsx(be,{selectedFilters:l,setSelectedFilters:d,searchTerm:r,setSearchTerm:i,setQuestions:n,onSessionCreated:async e=>{try{s(`/questions/${e}`)}catch(t){a({title:"Erro ao iniciar estudos",description:t.message,variant:"destructive"})}},onShowRandomDialog:()=>{},domain:"pediatria"})}),e.jsx(J,{})]})},we=Object.freeze(Object.defineProperty({__proto__:null,default:ve},Symbol.toStringTag,{value:"Module"}));export{ve as P,ee as a,we as b,pe as u};
