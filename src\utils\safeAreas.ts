import { StatusBar } from '@capacitor/status-bar';
import { isCapacitor } from './capacitorUtils';

/**
 * Interface para safe areas
 */
export interface SafeAreas {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

/**
 * Cache das safe areas para evitar recálculos desnecessários
 */
let cachedSafeAreas: SafeAreas | null = null;
let lastViewportHeight = 0;

/**
 * Detecta se o dispositivo tem notch ou safe areas
 */
export const hasNotch = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Verificar CSS env() support para safe areas
  const testElement = document.createElement('div');
  testElement.style.paddingTop = 'env(safe-area-inset-top)';
  document.body.appendChild(testElement);
  const hasEnvSupport = getComputedStyle(testElement).paddingTop !== '0px';
  document.body.removeChild(testElement);
  
  return hasEnvSupport;
};

/**
 * Obtém as safe areas do dispositivo
 */
export const getSafeAreas = async (): Promise<SafeAreas> => {
  // Se não for Capacitor, retornar valores padrão
  if (!isCapacitor()) {
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }

  // Verificar se o viewport mudou (rotação, teclado, etc.)
  const currentViewportHeight = window.innerHeight;
  if (cachedSafeAreas && lastViewportHeight === currentViewportHeight) {
    return cachedSafeAreas;
  }

  try {
    // Método 1: Usar CSS env() se disponível
    if (hasNotch()) {
      const safeAreas = getSafeAreasFromCSS();
      if (safeAreas.top > 0 || safeAreas.bottom > 0) {
        cachedSafeAreas = safeAreas;
        lastViewportHeight = currentViewportHeight;
        return safeAreas;
      }
    }

    // Método 2: Usar StatusBar API do Capacitor
    const statusBarInfo = await StatusBar.getInfo();
    const statusBarHeight = statusBarInfo.height || 0;

    // Método 3: Calcular gesture bar baseado no dispositivo
    const gestureBarHeight = getGestureBarHeight();

    const safeAreas: SafeAreas = {
      top: statusBarHeight,
      bottom: gestureBarHeight,
      left: 0,
      right: 0
    };

    cachedSafeAreas = safeAreas;
    lastViewportHeight = currentViewportHeight;
    return safeAreas;

  } catch (error) {
    console.warn('Erro ao obter safe areas:', error);
    
    // Fallback: valores estimados para Android
    const fallbackSafeAreas: SafeAreas = {
      top: 24, // Status bar típica do Android
      bottom: 48, // Gesture bar típica
      left: 0,
      right: 0
    };

    cachedSafeAreas = fallbackSafeAreas;
    lastViewportHeight = currentViewportHeight;
    return fallbackSafeAreas;
  }
};

/**
 * Obtém safe areas usando CSS env()
 */
const getSafeAreasFromCSS = (): SafeAreas => {
  const testElement = document.createElement('div');
  testElement.style.position = 'fixed';
  testElement.style.top = '0';
  testElement.style.left = '0';
  testElement.style.width = '1px';
  testElement.style.height = '1px';
  testElement.style.paddingTop = 'env(safe-area-inset-top)';
  testElement.style.paddingBottom = 'env(safe-area-inset-bottom)';
  testElement.style.paddingLeft = 'env(safe-area-inset-left)';
  testElement.style.paddingRight = 'env(safe-area-inset-right)';
  
  document.body.appendChild(testElement);
  
  const computedStyle = getComputedStyle(testElement);
  const safeAreas: SafeAreas = {
    top: parseInt(computedStyle.paddingTop) || 0,
    bottom: parseInt(computedStyle.paddingBottom) || 0,
    left: parseInt(computedStyle.paddingLeft) || 0,
    right: parseInt(computedStyle.paddingRight) || 0
  };
  
  document.body.removeChild(testElement);
  return safeAreas;
};

/**
 * Estima a altura da gesture bar baseado no dispositivo
 */
const getGestureBarHeight = (): number => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Android moderno geralmente tem gesture bar
  if (/android/i.test(userAgent)) {
    // Verificar se é Android 10+ (gesture navigation)
    const androidVersion = userAgent.match(/android (\d+)/);
    if (androidVersion && parseInt(androidVersion[1]) >= 10) {
      return 48; // Altura típica da gesture bar
    }
    return 48; // Navigation bar tradicional
  }
  
  return 0;
};

/**
 * Aplica safe areas a um elemento
 */
export const applySafeAreas = async (element: HTMLElement, options: {
  top?: boolean;
  bottom?: boolean;
  left?: boolean;
  right?: boolean;
} = {}) => {
  if (!isCapacitor()) return;

  const safeAreas = await getSafeAreas();
  const { top = true, bottom = true, left = true, right = true } = options;

  if (top && safeAreas.top > 0) {
    element.style.paddingTop = `${safeAreas.top}px`;
  }
  if (bottom && safeAreas.bottom > 0) {
    element.style.paddingBottom = `${safeAreas.bottom}px`;
  }
  if (left && safeAreas.left > 0) {
    element.style.paddingLeft = `${safeAreas.left}px`;
  }
  if (right && safeAreas.right > 0) {
    element.style.paddingRight = `${safeAreas.right}px`;
  }
};

/**
 * Gera CSS custom properties para safe areas
 */
export const generateSafeAreaCSS = async (): Promise<string> => {
  const safeAreas = await getSafeAreas();
  
  return `
    :root {
      --safe-area-inset-top: ${safeAreas.top}px;
      --safe-area-inset-bottom: ${safeAreas.bottom}px;
      --safe-area-inset-left: ${safeAreas.left}px;
      --safe-area-inset-right: ${safeAreas.right}px;
      --safe-area-height: calc(100vh - ${safeAreas.top}px - ${safeAreas.bottom}px);
      --safe-area-width: calc(100vw - ${safeAreas.left}px - ${safeAreas.right}px);
    }
  `;
};

/**
 * Hook para aplicar safe areas automaticamente
 */
export const useSafeAreas = () => {
  const applyToBody = async () => {
    if (!isCapacitor()) return;

    const css = await generateSafeAreaCSS();
    
    // Remover style anterior se existir
    const existingStyle = document.getElementById('safe-areas-css');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Adicionar novo style
    const styleElement = document.createElement('style');
    styleElement.id = 'safe-areas-css';
    styleElement.textContent = css;
    document.head.appendChild(styleElement);

    console.log('🛡️ Safe areas aplicadas:', await getSafeAreas());
  };

  return { applyToBody, getSafeAreas };
};

/**
 * Limpa o cache das safe areas (útil em mudanças de orientação)
 */
export const clearSafeAreasCache = () => {
  cachedSafeAreas = null;
  lastViewportHeight = 0;
};
