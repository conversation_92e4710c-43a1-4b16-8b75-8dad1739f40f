import{j as e}from"./radix-core-6kBL75b5.js";import{R as a,an as r,a5 as o,B as s,l as t,b3 as n,aM as d,aa as i,a7 as l,U as m}from"./index-CFnD44mG.js";import c from"./Footer-BQ6Dqsd-.js";import{r as x}from"./critical-DVX9Inzy.js";import{A as g,a as p,b,c as u}from"./accordion-DNR0EX5j.js";import{a as h,P as j}from"./poisoningSEOData-B3nJazuU.js";import{c as k,a as f}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-ot4XwGkJ.js";import"./rocket-BOvZdSbI.js";import"./target-BX0FTOgV.js";import"./zap-DhWHOaH4.js";import"./book-open-CpPiu5Sl.js";import"./star-ShS_gnKj.js";import"./circle-help-B8etwa8R.js";import"./instagram-X0NDZon3.js";const N=({onWeightChange:s,onAgeChange:t,showWeight:n=!0,showAge:d=!0})=>{const[i,l]=x.useState(""),[m,c]=x.useState("");return e.jsx(a,{className:"p-6 space-y-4 bg-white dark:bg-slate-800 shadow-lg border-2 border-red-200/80 dark:border-red-900/50",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[n&&e.jsxs("div",{className:"space-y-2 animate-fade-in",children:[e.jsx(r,{htmlFor:"weight",className:"block text-lg font-medium text-gray-800 dark:text-gray-200",children:"Peso (kg)"}),e.jsx(o,{id:"weight",type:"number",placeholder:"Ex: 20",className:"text-lg h-12 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100",value:i,onChange:e=>(e=>{l(e);const a=parseFloat(e);!isNaN(a)&&a>0&&s(a)})(e.target.value)})]}),d&&e.jsxs("div",{className:"space-y-2 animate-fade-in",children:[e.jsx(r,{htmlFor:"age",className:"block text-lg font-medium text-gray-800 dark:text-gray-200",children:"Idade (anos)"}),e.jsx(o,{id:"age",type:"number",placeholder:"Ex: 5",className:"text-lg h-12 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100",value:m,onChange:e=>(e=>{c(e);const a=parseFloat(e);!isNaN(a)&&a>=0&&t(12*a)})(e.target.value)})]})]})})},y=({toxidrome:a,onCalculate:r})=>{const[o,n]=x.useState(null),[d,i]=x.useState(null),[l,m]=x.useState("");return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-blue-900 dark:text-blue-300",children:"Calculadora de Dose"}),e.jsx(N,{onWeightChange:n,onAgeChange:i,showWeight:!1!==a.requiresWeight,showAge:!0===a.requiresAge}),e.jsxs(s,{onClick:()=>{let e="";switch(a.id){case"benzodiazepinicos":e=(s=o)?`Flumazenil (Lanexat®):\n\nDose inicial: ${(.01*s).toFixed(2)} mg IV lento (0,01 mg/kg)\nDose máxima inicial: 0,2 mg\n\nSe necessário, repetir a cada minuto até dose máxima acumulada de ${(.05*s).toFixed(2)} mg (0,05 mg/kg) ou 1 mg (o que for menor).\n\nManutenção: Infusão de 0,1-0,4 mg/hora se necessário.`:"Informe o peso do paciente para calcular a dose.";break;case"opioides":e=((e,a)=>{if(!e)return"Informe o peso do paciente para calcular a dose.";let r="Naloxona (Narcan®):\n\n";return null!==a&&(r+=a<1?"Neonato (< 1 mês): 0,01 mg/kg IV/IM/SC/ET\n":a<60?"Criança (1 mês - 5 anos): 0,1 mg/kg IV/IM/SC/ET\n":"Criança (> 5 anos): 2 mg IV/IM/SC/ET\n"),r+=`Dose calculada por peso: ${(.1*e).toFixed(2)} mg IV/IM/SC/ET (0,1 mg/kg)\nDose máxima inicial: 2 mg\n\nSe necessário, repetir a cada 2-3 minutos.\nEm caso de resposta inadequada, considerar aumentar dose para ${(.2*e).toFixed(2)} mg (0,2 mg/kg).`,r})(o,d);break;case"anticolinergicos":e=(e=>e?`Fisostigmina (Antilirium®):\n\nDose: ${(.02*e).toFixed(2)} mg/kg IV lento (0,02 mg/kg)\nDose máxima: 0,5 mg em crianças menores de 12 anos e 2 mg em maiores de 12 anos\n\nAdministrar lentamente, não excedendo 0,5 mg/min.\nSe necessário, repetir a cada 5-10 minutos até resolução dos sintomas ou desenvolvimento de efeitos colinérgicos.`:"Informe o peso do paciente para calcular a dose.")(o);break;case"paracetamol":e=(e=>{if(!e)return"Informe o peso do paciente para calcular a dose.";const a=(150*e).toFixed(0);return`N-Acetilcisteína (NAC, Fluimucil®):\n\nProtocolo IV (21 horas):\n1ª dose: ${a} mg (150 mg/kg) em 200 mL SG5% por 1 hora\n2ª dose: ${(50*e).toFixed(0)} mg (50 mg/kg) em 500 mL SG5% por 4 horas\n3ª dose: ${(100*e).toFixed(0)} mg (100 mg/kg) em 1000 mL SG5% por 16 horas\n\nProtocolo VO (72 horas):\nDose de ataque: ${a} mg (150 mg/kg)\nManutenção: ${(70*e).toFixed(0)} mg (70 mg/kg) a cada 4 horas por 17 doses`})(o);break;case"betabloqueadores":e=(e=>e?`Glucagon:\n\nDose em bolus: ${(.03*e).toFixed(2)} - ${(.1*e).toFixed(2)} mg/kg IV (0,03-0,1 mg/kg)\nDose máxima: 1 mg para crianças ≤ 25 kg, 5 mg para > 25 kg\n\nInfusão contínua: ${(.07*e).toFixed(2)} mg/kg/hora\nTitular conforme resposta clínica até máximo de 0,1 mg/kg/hora`:"Informe o peso do paciente para calcular a dose.")(o);break;case"metemoglobinemia":e=(e=>e?`Azul de Metileno:\n\nDose: ${(1*e).toFixed(0)} - ${(2*e).toFixed(0)} mg IV lento (1-2 mg/kg)\nDiluir em 100 mL de SG5% e administrar em 15-30 minutos\n\nPode ser repetido em 1 hora se os sintomas persistirem e a metemoglobinemia permanecer > 30%\nDose máxima acumulada: 7 mg/kg`:"Informe o peso do paciente para calcular a dose.")(o);break;case"colinergicos":e=(e=>e?`Atropina e Pralidoxima:\n\nAtropina:\nDose inicial: ${(.02*e).toFixed(2)} - ${(.05*e).toFixed(2)} mg IV (0,02-0,05 mg/kg)\nDose mínima: 0,1 mg, Dose máxima: 5 mg\nRepetir a cada 5-10 minutos até atropinização (secreções secas, midríase)\n\nPralidoxima (para organofosforados):\nDose de ataque: ${(25*e).toFixed(0)} - ${(50*e).toFixed(0)} mg IV lento (25-50 mg/kg)\nInfusão contínua: ${(10*e).toFixed(0)} - ${(20*e).toFixed(0)} mg/kg/hora`:"Informe o peso do paciente para calcular a dose.")(o);break;case"antidepressivos_triciclicos":e=(e=>e?`Bicarbonato de Sódio 8,4%:\n\nDose inicial: ${(1*e).toFixed(0)} - ${(2*e).toFixed(0)} mEq IV (1-2 mEq/kg)\nAdministrar lentamente (em 30-60 minutos)\n\nManutenção: Ajustar dose para manter pH sanguíneo entre 7,45-7,55\nMonitorar pH sanguíneo, eletrólitos e ECG`:"Informe o peso do paciente para calcular a dose.")(o);break;default:e="Não há cálculo de dose disponível para este antídoto."}var s;m(e),r(e)},disabled:!o&&!1!==a.requiresWeight,className:"w-full py-6 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white flex items-center justify-center gap-2",children:[e.jsx(t,{className:"h-5 w-5"}),e.jsxs("span",{children:["Calcular Dose de ",a.antidote]})]})]})};function $({toxidrome:a}){const r=e=>{const a={blue:{bg:"bg-blue-50 dark:bg-blue-900/30",text:"text-blue-900 dark:text-blue-300",content:"text-blue-800 dark:text-blue-200",border:"border-blue-200 dark:border-blue-800"},purple:{bg:"bg-purple-50 dark:bg-purple-900/30",text:"text-purple-900 dark:text-purple-300",content:"text-purple-800 dark:text-purple-200",border:"border-purple-200 dark:border-purple-800"},emerald:{bg:"bg-emerald-50 dark:bg-emerald-900/30",text:"text-emerald-900 dark:text-emerald-300",content:"text-emerald-800 dark:text-emerald-200",border:"border-emerald-200 dark:border-emerald-800"},amber:{bg:"bg-amber-50 dark:bg-amber-900/30",text:"text-amber-900 dark:text-amber-300",content:"text-amber-800 dark:text-amber-200",border:"border-amber-200 dark:border-amber-800"},red:{bg:"bg-red-50 dark:bg-red-900/30",text:"text-red-900 dark:text-red-300",content:"text-red-800 dark:text-red-200",border:"border-red-200 dark:border-red-800"},gray:{bg:"bg-gray-50 dark:bg-gray-800",text:"text-gray-900 dark:text-gray-100",content:"text-gray-700 dark:text-gray-300",border:"border-gray-200 dark:border-gray-700"}};return a[e]||a.gray},o=r("blue"),s=r("purple"),t=r("emerald"),n=r("amber"),d=r("red"),i=r("gray");return e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:`${o.bg} border ${o.border} rounded-lg p-6 animate-fade-in`,children:[e.jsx("h3",{className:`font-semibold text-xl mb-3 ${o.text}`,children:"Antídoto"}),e.jsx("p",{className:o.content,children:a.antidote})]}),e.jsxs("div",{className:`${s.bg} border ${s.border} rounded-lg p-6 animate-fade-in delay-100`,children:[e.jsx("h3",{className:`font-semibold text-xl mb-3 ${s.text}`,children:"Uso"}),e.jsx("p",{className:s.content,children:a.usage})]}),a.examples&&a.examples.length>0&&e.jsxs("div",{className:`${t.bg} border ${t.border} rounded-lg p-6 animate-fade-in delay-150`,children:[e.jsx("h3",{className:`font-semibold text-xl mb-3 ${t.text}`,children:"Exemplos"}),e.jsx("ul",{className:`list-disc list-inside ${t.content}`,children:a.examples.map(((a,r)=>e.jsx("li",{children:a},r)))})]}),a.management&&a.management.length>0&&e.jsxs("div",{className:`${n.bg} border ${n.border} rounded-lg p-6 animate-fade-in delay-200`,children:[e.jsx("h3",{className:`font-semibold text-xl mb-3 ${n.text}`,children:"Manejo"}),e.jsx("ul",{className:`list-disc list-inside ${n.content}`,children:a.management.map(((a,r)=>e.jsx("li",{children:a},r)))})]}),e.jsxs("div",{className:`${d.bg} border ${d.border} rounded-lg p-6 animate-fade-in delay-300`,children:[e.jsx("h3",{className:`font-semibold text-xl mb-3 ${d.text}`,children:"Cuidados"}),e.jsx("ul",{className:"list-disc pl-5 space-y-2",children:a.precautions.map(((a,r)=>e.jsx("li",{className:d.content,children:a},r)))})]}),a.reference&&e.jsx(g,{type:"single",collapsible:!0,className:"w-full",children:e.jsxs(p,{value:"reference",className:"border-none",children:[e.jsx(b,{className:`${i.bg} border ${i.border} rounded-t-lg px-6 py-4 hover:no-underline hover:bg-gray-100 dark:hover:bg-gray-700`,children:e.jsx("span",{className:`font-semibold ${i.text}`,children:"Referência Bibliográfica:"})}),e.jsx(u,{className:`${i.bg} border-x border-b ${i.border} rounded-b-lg px-6 pb-4`,children:e.jsxs("div",{className:"pt-2",children:[e.jsx("h4",{className:`font-medium ${i.text} mb-2`,children:" "}),e.jsx("p",{className:`${i.content} text-sm italic`,children:a.reference})]})})]})})]})}const v=()=>{const{id:r}=k(),o=f(),t=n.find((e=>e.id===r)),[g,p]=x.useState(null),b=h[r||"main"];return t?(t.name,t.name.toLowerCase(),t.antidote,e.jsxs("div",{className:d.pageBackground(),children:[b&&e.jsx(j,{...b}),e.jsx(i,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-8",children:[e.jsxs(s,{variant:"outline",onClick:()=>o("/poisonings"),className:"mb-6 hover:scale-105 transition-transform dark:text-gray-200 dark:border-gray-700 dark:hover:bg-slate-700",children:[e.jsx(l,{className:"mr-2 h-4 w-4"}),"Voltar"]}),e.jsx("div",{className:"max-w-4xl mx-auto space-y-6",children:e.jsxs(a,{className:"overflow-hidden border-0 shadow-lg animate-fade-in dark:bg-slate-800 dark:border-slate-700",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 p-6",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-white",children:t.name}),e.jsxs("p",{className:"text-blue-100 mt-2 text-lg",children:["Toxíndrome: ",t.type]})]}),e.jsxs(m,{className:"p-6 space-y-6 dark:bg-slate-800",children:[e.jsx($,{toxidrome:t}),e.jsxs("div",{className:d.glassContainer("p-6 animate-fade-in delay-200"),children:[e.jsx(y,{toxidrome:t,onCalculate:p}),g&&e.jsxs("div",{className:"mt-4 p-6 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-blue-100 dark:border-blue-900/50 animate-fade-in",children:[e.jsx("h4",{className:"font-medium text-blue-900 dark:text-blue-300 mb-2",children:"Dose Calculada:"}),e.jsx("pre",{className:"whitespace-pre-wrap text-gray-700 dark:text-gray-300 font-medium",children:g})]})]})]})]})})]}),e.jsx(c,{})]})):e.jsxs("div",{className:d.pageBackground(),children:[e.jsxs(HelmetWrapper,{children:[e.jsx("title",{children:"Intoxicação não encontrada | PedBook"}),e.jsx("meta",{name:"description",content:"Intoxicação não encontrada no PedBook."}),e.jsx("meta",{property:"og:title",content:"Intoxicação não encontrada | PedBook"}),e.jsx("meta",{property:"og:description",content:"Intoxicação não encontrada no PedBook."}),e.jsx("meta",{property:"og:type",content:"website"}),e.jsx("meta",{property:"og:url",content:`https://pedb.com.br/poisonings/${r}`})]}),e.jsx(i,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Intoxicação não encontrada"}),e.jsxs(s,{onClick:()=>o("/poisonings"),className:"mt-4",children:[e.jsx(l,{className:"mr-2 h-4 w-4"}),"Voltar"]})]})}),e.jsx(c,{})]})};export{v as default};
