import{j as e}from"./radix-core-6kBL75b5.js";import{u as r,j as a,ay as s,aM as t,a8 as i,aa as o,B as l,a7 as d,k as c,n,aJ as m}from"./index-CFnD44mG.js";import p from"./Footer-BQ6Dqsd-.js";import{L as g,a as u}from"./router-BAzpOxbo.js";import{Z as x}from"./zap-DhWHOaH4.js";import{C as b}from"./chart-line-JWIF4j9a.js";import{C as h}from"./calendar--wSGgBN5.js";import{S as j}from"./stethoscope-DLVdj0oT.js";import{W as y}from"./wind-DaL1n_Qn.js";import{E as k}from"./eye-Cftk1iKW.js";import{S as f}from"./scale-DoQjTjLO.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-ot4XwGkJ.js";import"./rocket-BOvZdSbI.js";import"./target-BX0FTOgV.js";import"./book-open-CpPiu5Sl.js";import"./star-ShS_gnKj.js";import"./circle-help-B8etwa8R.js";import"./instagram-X0NDZon3.js";const v=({title:t,description:i,icon:o,color:l,path:d,requiresAuth:c,badge:n,isHighlighted:m})=>{const{user:p}=r();return e.jsx(g,{to:d,className:a("block group h-full",m&&"w-full max-w-4xl mx-auto"),children:e.jsxs("div",{className:a("relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 hover:shadow-lg hover:-translate-y-1","bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md","border border-gray-100 dark:border-gray-700/50",m&&"text-center flex flex-col items-center justify-center space-y-4 py-8"),children:[e.jsx("div",{className:a("absolute top-0 left-0 right-0 h-1.5 rounded-t-xl","blue"===l?"bg-blue-500":"orange"===l?"bg-orange-500":"purple"===l?"bg-purple-500":"red"===l?"bg-red-500":"yellow"===l?"bg-yellow-500":"indigo"===l?"bg-indigo-500":"green"===l?"bg-green-500":"bg-primary")}),m?e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-3 max-w-2xl",children:[e.jsx("div",{className:"flex items-center justify-center gap-2",children:e.jsx("h3",{className:"text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-100",children:t})}),i&&e.jsx("p",{className:"text-sm md:text-base text-gray-600 dark:text-gray-300 leading-relaxed",children:i})]})}):e.jsxs("div",{className:"flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:a("w-10 h-10 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center mb-2 sm:mb-3 shadow-sm transition-transform group-hover:scale-110","border-2",(e=>{switch(e){case"blue":return"border-blue-500 dark:border-blue-600";case"orange":return"border-orange-500 dark:border-orange-600";case"purple":return"border-purple-500 dark:border-purple-600";case"red":return"border-red-500 dark:border-red-600";case"yellow":return"border-yellow-500 dark:border-yellow-600";case"indigo":return"border-indigo-500 dark:border-indigo-600";case"green":return"border-green-500 dark:border-green-600";default:return"border-gray-300 dark:border-gray-600"}})(l),(e=>{switch(e){case"blue":return"bg-blue-50 dark:bg-blue-900/30";case"orange":return"bg-orange-50 dark:bg-orange-900/30";case"purple":return"bg-purple-50 dark:bg-purple-900/30";case"red":return"bg-red-50 dark:bg-red-900/30";case"yellow":return"bg-yellow-50 dark:bg-yellow-900/30";case"indigo":return"bg-indigo-50 dark:bg-indigo-900/30";case"green":return"bg-green-50 dark:bg-green-900/30";default:return"bg-gray-50 dark:bg-gray-800/30"}})(l)),children:e.jsx(o,{className:a("w-5 h-5 sm:w-7 sm:h-7",(e=>{switch(e){case"blue":return"text-blue-700 dark:text-blue-400";case"orange":return"text-orange-700 dark:text-orange-400";case"purple":return"text-purple-700 dark:text-purple-400";case"red":return"text-red-700 dark:text-red-400";case"yellow":return"text-yellow-700 dark:text-yellow-400";case"indigo":return"text-indigo-700 dark:text-indigo-400";case"green":return"text-green-700 dark:text-green-400";default:return"text-gray-700 dark:text-gray-400"}})(l))})}),e.jsxs("div",{className:"space-y-2 flex-1",children:[e.jsx("h3",{className:"text-sm sm:text-base font-semibold text-gray-800 dark:text-gray-100 line-clamp-2",children:t}),i&&e.jsx("p",{className:"text-[10px] sm:text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2",children:i}),c&&!p&&e.jsx("div",{className:"mt-2",children:e.jsx(s,{variant:"outline",className:"bg-white/80 dark:bg-gray-800/80 border-primary/20 dark:border-primary/30 text-primary dark:text-primary/80 shadow-sm",children:"Requer login"})}),n&&e.jsx("div",{className:"mt-2",children:e.jsx(s,{variant:"outline",className:"bg-white/80 dark:bg-gray-800/80 border-primary/20 dark:border-primary/30 text-primary dark:text-primary/80 shadow-sm",children:n})})]})]})]})})},w=()=>{const r=u(),a=[{title:"Calculadora de SRI",description:"Sequência Rápida de Intubação - Calculadora interativa para adultos e pediatria com doses, materiais e procedimento guiado",icon:x,color:"purple",path:"/calculadoras/sri"},{title:"Nomograma de Bhutani",description:"Avaliação do risco de hiperbilirrubinemia significativa em recém-nascidos",icon:b,color:"yellow",path:"/calculadoras/bhutani"},{title:"Escore de APGAR",description:"Avaliação da vitalidade de recém-nascidos baseada em cinco parâmetros clínicos",icon:c,color:"blue",path:"/calculadoras/apgar"},{title:"Escore de Rodwell",description:"Avaliação da probabilidade de sepse neonatal com base em critérios hematológicos",icon:n,color:"red",path:"/calculadoras/rodwell"},{title:"Capurro Somático",description:"Avaliação da idade gestacional do recém-nascido com base em características clínicas",icon:h,color:"green",path:"/calculadoras/capurro"},{title:"Capurro Neurológico",description:"Avaliação da idade gestacional com base em critérios neurológicos e somáticos",icon:m,color:"purple",path:"/calculadoras/capurro-neuro"},{title:"Escala de Finnegan",description:"Avaliação da síndrome de abstinência neonatal (SAN) em recém-nascidos",icon:j,color:"orange",path:"/calculadoras/finnegan"},{title:"Controle da Asma (GINA)",description:"Avaliação do controle da asma baseada nos critérios GINA 2022",icon:y,color:"blue",path:"/calculadoras/gina"},{title:"Escala de Glasgow Pediátrica",description:"Avaliação do nível de consciência em crianças",icon:k,color:"indigo",path:"/calculadoras/glasgow"},{title:"IMC e Obesidade Pediátrica",description:"Avaliação do estado nutricional de crianças e adolescentes com base nas tabelas de Z-Score da OMS",icon:f,color:"green",path:"/calculadoras/imc"}];return e.jsxs("div",{className:t.gradientBackground("min-h-screen flex flex-col"),children:[e.jsxs(i,{children:[e.jsx("title",{children:"PedBook | Calculadoras"}),e.jsx("meta",{name:"description",content:"Calculadoras pediátricas para auxílio na prática diária."})]}),e.jsx(o,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-5xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(l,{variant:"ghost",size:"icon",onClick:()=>r("/"),className:"hover:bg-primary/10 hidden sm:flex dark:hover:bg-primary/20",children:e.jsx(d,{className:"h-5 w-5"})}),e.jsxs("div",{className:"text-center flex-1 space-y-4",children:[e.jsx("h1",{className:t.gradientHeading("text-3xl"),children:"Calculadoras e Escalas Pediátricas"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Ferramentas de cálculo para auxílio na prática pediátrica diária"})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:gap-4",children:a.map(((r,a)=>e.jsx("div",{className:"transform transition-all duration-500 hover:scale-[1.02]",style:{animationDelay:100*a+"ms",animation:"fade-in-up 0.5s ease-out forwards",opacity:0},children:e.jsx(v,{...r})},a)))})]})}),e.jsx(p,{})]})};export{w as default};
