import{j as s}from"./radix-core-6kBL75b5.js";import{a8 as e,aa as a,l as r,ab as t,av as i,B as o}from"./index-BGVWLj2Q.js";import c from"./Footer-D6qSLzC8.js";import{C as l}from"./clock-Ctd21uX9.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-S5r6pr1T.js";import"./rocket-BJgtWoQ_.js";import"./target-B7qg_LDj.js";import"./zap-CJm1mYQd.js";import"./book-open-ClUpo2Lw.js";import"./star-Bpzr1rUs.js";import"./circle-help-CBQwJU4Z.js";import"./instagram-D_9FoHz5.js";const m=()=>s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsxs(e,{children:[s.jsx("title",{children:"PedBook | Bot WhatsApp"}),s.jsx("meta",{name:"description",content:"Acesse as dosagens pediátricas 24 horas por dia através do nosso bot no WhatsApp."})]}),s.jsx(a,{}),s.jsx("main",{className:"flex-1 container mx-auto px-4 py-8 md:py-16",children:s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsxs("div",{className:"text-center space-y-6 mb-12",children:[s.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 gradient-text",children:"Bot WhatsApp PedBook"}),s.jsx("p",{className:"text-xl text-gray-600",children:"Seu assistente pediátrico disponível 24 horas por dia, 7 dias por semana"})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[s.jsxs("div",{className:"glass-card p-6 space-y-4 rounded-xl",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"bg-primary/10 p-3 rounded-lg",children:s.jsx(r,{className:"w-6 h-6 text-primary"})}),s.jsx("h2",{className:"text-xl font-semibold",children:"Cálculo Automático"})]}),s.jsx("p",{className:"text-gray-600",children:"Calcule doses de medicamentos de forma rápida e precisa, com base no peso e idade do paciente."})]}),s.jsxs("div",{className:"glass-card p-6 space-y-4 rounded-xl",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"bg-primary/10 p-3 rounded-lg",children:s.jsx(t,{className:"w-6 h-6 text-primary"})}),s.jsx("h2",{className:"text-xl font-semibold",children:"Prescrições Automáticas"})]}),s.jsx("p",{className:"text-gray-600",children:"NOVIDADE! Gere prescrições completas automaticamente para as condições mais comuns."})]})]}),s.jsxs("div",{className:"glass-card p-6 md:p-8 rounded-xl text-center space-y-4 mb-12",children:[s.jsxs("div",{className:"flex justify-center gap-4 flex-wrap",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(l,{className:"w-5 h-5 text-primary"}),s.jsx("span",{className:"text-gray-700",children:"Disponível 24/7"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(i,{className:"w-5 h-5 text-green-500"}),s.jsx("span",{className:"text-gray-700",children:"Totalmente gratuito"})]})]}),s.jsxs(o,{onClick:()=>{window.open("https://api.whatsapp.com/send?phone=5511971424463&text=Me%20envie%20suas%20fun%C3%A7%C3%B5es","_blank")},className:"bg-green-500 hover:bg-green-600 text-white px-4 md:px-6 py-2 md:py-3 text-base md:text-lg rounded-xl transition-all duration-300 hover:scale-105 w-full md:w-auto",children:[s.jsx(i,{className:"w-5 h-5 mr-2"}),"Iniciar conversa no WhatsApp"]}),s.jsx("p",{className:"text-xs md:text-sm text-gray-500",children:"Clique no botão acima para começar a usar o bot gratuitamente"})]})]})}),s.jsx(c,{})]});export{m as default};
