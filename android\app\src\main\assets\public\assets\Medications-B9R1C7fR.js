import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-Dn8ESRcC.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-BGVWLj2Q.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-D6qSLzC8.js";import"./FeedbackTrigger-S5r6pr1T.js";import"./rocket-BJgtWoQ_.js";import"./target-B7qg_LDj.js";import"./zap-CJm1mYQd.js";import"./book-open-ClUpo2Lw.js";import"./star-Bpzr1rUs.js";import"./circle-help-CBQwJU4Z.js";import"./instagram-D_9FoHz5.js";import"./collapsible-B6HfSnGs.js";import"./accordion-Du3PyyHa.js";import"./PatientInfoSection-B__L9wIk.js";import"./scale-CM4ytIKK.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-CkqAMcCO.js";import"./user-CFgLwn9u.js";import"./DosageDisplay-Ds9BTDw8.js";import"./alert-Bpv71Luy.js";import"./plus-P3v-LB0m.js";import"./lightbulb-DTF-rAfa.js";import"./external-link-C_1WqVJQ.js";import"./stethoscope-CoAh0LEU.js";import"./syringe-ByumoBlt.js";import"./wind-BHohqBRk.js";import"./bug-CSMV3pBy.js";import"./chevron-left-BRR5gjCm.js";import"./house-Har_FMMU.js";function t(){return r.jsx(o,{})}export{t as default};
