import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{s as t,d as a,R as r,W as c,Z as i,$ as o,U as n,L as l,ao as d}from"./index-CFFY2EZF.js";async function m(e,s,a){try{const{data:r,error:c}=await t.from("study_categories").select("*").eq("name",e).eq("type",s).maybeSingle();if(c)throw c;if(r)return{id:r.id,name:r.name,type:r.type};const{data:i,error:o}=await t.from("study_categories").insert({name:e,type:s,parent_id:a}).select().single();if(o)throw o;return{id:i.id,name:i.name,type:i.type}}catch(r){throw r}}async function u(e){try{const{data:s,error:a}=await t.from("exam_locations").select("*").ilike("name",e).maybeSingle();if(a)throw a;if(s)return{id:s.id,name:s.name};const{data:r,error:c}=await t.from("exam_locations").insert({name:e}).select().single();if(c)throw c;return{id:r.id,name:r.name}}catch(s){throw s}}async function h(e){try{const{data:s,error:a}=await t.from("exam_years").select("*").eq("year",e).single();if(a&&"PGRST116"!==a.code)throw a;if(s)return s;const{data:r,error:c}=await t.from("exam_years").insert({year:e}).select().single();if(c)throw c;return r}catch(s){throw s}}function p(e){if(!e)return"ALTERNATIVAS";switch(e.toUpperCase()){case"MULTIPLE_CHOICE":case"MULTIPLE_CHOICE_FOUR":case"MULTIPLE_CHOICE_4":case"ALTERNATIVAS":case"MÚLTIPLA ESCOLHA":case"MULTIPLA ESCOLHA":case"MÚLTIPLA ESCOLHA 4":case"MULTIPLA ESCOLHA 4":default:return"ALTERNATIVAS";case"TRUE_OR_FALSE":case"VERDADEIRO_FALSO":case"VERDADEIRO OU FALSO":case"VERDADEIRO_OU_FALSO":case"V_OU_F":case"VF":return"VERDADEIRO_FALSO";case"DISCURSIVE":case"DISSERTATIVA":case"DISCURSIVA":return"DISSERTATIVA"}}async function f(e){try{const s=await e.text(),a=JSON.parse(s);if(!Array.isArray(a.questions))throw new Error("O arquivo deve conter um array de questões");return await async function(e){try{const r={success:0,errors:[],created:{specialties:new Map,themes:new Map,focuses:new Map,locations:new Map,years:new Set}};for(let c=0;c<e.questions.length;c++){const i=e.questions[c];try{const e=i.statement_text||i.statement;if(!e)throw new Error("Questão sem enunciado");const{data:a,error:c}=await t.from("questions").select("id").eq("question_content",e).maybeSingle();if(c)throw c;if(a){r.errors.push(`Questão já existe no banco de dados (ID: ${a.id})`);continue}let o=null;i.specialty&&(o=r.created.specialties.get(`specialty:${i.specialty}`),o||(o=await m(i.specialty,"specialty"),o&&r.created.specialties.set(`specialty:${i.specialty}`,o)));let n=null;i.theme&&o&&(n=r.created.themes.get(`theme:${i.theme}`),n||(n=await m(i.theme,"theme",o.id),n&&r.created.themes.set(`theme:${i.theme}`,n)));let l=null;i.focus&&n&&(l=r.created.focuses.get(`focus:${i.focus}`),l||(l=await m(i.focus,"focus",n.id),l&&r.created.focuses.set(`focus:${i.focus}`,l)));let d=null;if(i.institution_id||i.location){const e=i.institution_id||i.location;e&&(d=await u(e))}i.year&&(await h(i.year),r.created.years.add(i.year));const f=p(i.answer_type),y=(s=i.images)&&""!==s?"string"==typeof s?s.trim()?[s]:[]:Array.isArray(s)?s:[]:[],w={};i.topics&&i.topics.length>0&&(w.topics=i.topics),i.is_annulled&&(w.is_annulled=!0);const x={question_content:e,response_choices:i.alternatives,correct_choice:String(i.correct_answer),specialty_id:o?.id,theme_id:n?.id,focus_id:l?.id,exam_location:d?.id,exam_year:i.year,question_format:f,knowledge_domain:i.specialty,specialty_name:i.specialty,media_attachments:y,assessment_type:i.tipo,question_number:i.numero,content_tags:Object.keys(w).length>0?w:null},{data:g,error:_}=await t.from("questions").insert(x).select().single();if(_)throw _;r.success++}catch(a){const e=`Questão ${c+1}: ${a.message}`;r.errors.push(e)}}return r}catch(a){throw new Error(`Erro na importação: ${a.message}`)}var s}(a)}catch(s){throw new Error(`Erro ao processar arquivo: ${s.message}`)}}const y=()=>{const{toast:t}=a(),[m,u]=s.useState(!1),[h,p]=s.useState(null),[y,w]=s.useState(0),[x,g]=s.useState(""),[_,E]=s.useState({current:0,total:0});return e.jsxs(r,{children:[e.jsxs(c,{children:[e.jsx(i,{children:"Importar Questões"}),e.jsx(o,{children:"Faça upload de arquivos JSON contendo as questões a serem importadas"})]}),e.jsx(n,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("input",{type:"file",accept:".json",onChange:async e=>{const s=e.target.files;s&&0!==s.length&&(await(async e=>{u(!0),p(null),E({current:0,total:e.length});const s={success:0,errors:[],created:{specialties:new Map,themes:new Map,focuses:new Map,locations:new Map,years:new Set}};try{for(let t=0;t<e.length;t++){const r=e[t];g(r.name),E((e=>({...e,current:t+1}))),w(0);try{w(10);const e=new Promise(((e,s)=>{setTimeout((()=>s(new Error("Timeout: Processamento demorou mais de 5 minutos"))),3e5)})),t=await Promise.race([f(r),e]);w(100),s.success+=t.success,s.errors.push(...t.errors.map((e=>`[${r.name}] ${e}`))),t.created.specialties.forEach(((e,t)=>{s.created.specialties.set(t,e)})),t.created.themes.forEach(((e,t)=>{s.created.themes.set(t,e)})),t.created.focuses.forEach(((e,t)=>{s.created.focuses.set(t,e)})),t.created.locations.forEach(((e,t)=>{s.created.locations.set(t,e)})),t.created.years.forEach((e=>{s.created.years.add(e)}))}catch(a){s.errors.push(`[${r.name}] ${a.message}`)}}p(s),t({title:"Importação concluída!",description:`${s.success} questões importadas com sucesso.${s.errors.length>0?` ${s.errors.length} erros encontrados.`:""}`})}catch(a){t({title:"Erro na importação",description:a.message||"Erro desconhecido ao importar questões",variant:"destructive"})}finally{u(!1),g(""),w(0)}})(s),e.target.value="")},disabled:m,multiple:!0,className:"file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 disabled:opacity-50"}),m&&e.jsx(l,{className:"h-4 w-4 animate-spin"})]}),m&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-500",children:[e.jsxs("span",{children:["Processando arquivo ",_.current," de ",_.total]}),e.jsx("span",{children:x})]}),e.jsx(d,{value:y,className:"h-2"})]}),h&&e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:"rounded-lg bg-secondary/50 p-4",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Resultados da Importação:"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{children:["✅ ",h.success," questões importadas com sucesso"]}),h.errors.length>0&&e.jsxs("li",{children:["❌ ",h.errors.length," erros encontrados"]}),e.jsxs("li",{children:["📚 ",h.created.specialties.size," especialidades"]}),e.jsxs("li",{children:["📝 ",h.created.themes.size," temas"]}),e.jsxs("li",{children:["🎯 ",h.created.focuses.size," focos"]}),e.jsxs("li",{children:["📍 ",h.created.locations.size," localizações"]}),e.jsxs("li",{children:["📅 ",h.created.years.size," anos"]})]})]}),h.errors.length>0&&e.jsxs("div",{className:"rounded-lg bg-destructive/10 p-4 max-h-60 overflow-auto",children:[e.jsx("h3",{className:"font-semibold mb-2 text-destructive",children:"Erros:"}),e.jsx("ul",{className:"space-y-1 text-sm",children:h.errors.map(((s,t)=>e.jsx("li",{className:"text-destructive",children:s},t)))})]})]})]})})]})};export{y as Q};
