import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,d as r,aq as o,ar as t,as as i,at as n,R as c,W as d,Z as m,$ as l,U as u,a5 as p,B as x,L as f,ao as h,Y as b,s as j}from"./index-BGVWLj2Q.js";import{A as v,a as y,b as g}from"./alert-Bpv71Luy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=s("FileJson",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 12a1 1 0 0 0-1 1v1a1 1 0 0 1-1 1 1 1 0 0 1 1 1v1a1 1 0 0 0 1 1",key:"1oajmo"}],["path",{d:"M14 18a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1 1 1 0 0 1-1-1v-1a1 1 0 0 0-1-1",key:"mpwhp6"}]]),_=()=>{const{toast:s}=r(),[_,E]=a.useState(!1),[S,$]=a.useState({total:0,processed:0,succeeded:0,failed:0}),[w,A]=a.useState(0),[O,k]=a.useState([]),[C,M]=a.useState(""),[q,J]=a.useState(""),U=e=>"object"==typeof e&&!Array.isArray(e)&&null!==e&&(void 0!==e.substancias||Object.keys(e).some((a=>"descricao"!==a&&"object"==typeof e[a]&&!Array.isArray(e[a])&&null!==e[a]))),V=async(e,a,s,r,o,t)=>{void 0!==e.descricao&&delete e.descricao,void 0!==e.substancias&&delete e.substancias;for(const[n,c]of Object.entries(e))if("descricao"!==n&&"substancias"!==n&&"object"==typeof c&&null!==c&&U(c))try{const e=c,d=n,m=`${o} > ${d}`,{data:l,error:u}=await j.from("pedbook_breastfeeding_subsections").insert({section_id:a,parent_subsection_id:s,name:d,description:e.descricao||null,display_order:0,nesting_level:r}).select().single();if(u){t.incrementErrorCount(1),t.addError(`Erro ao criar subseção ${m}: ${u.message}`);continue}t.incrementSubsectionCount(1);const p=l.id;if(e.substancias&&Array.isArray(e.substancias))for(const s of e.substancias)try{await D(a,p,s),t.incrementMedicationCount(1),t.incrementSuccessCount(1)}catch(i){t.incrementErrorCount(1),t.addError(`Erro ao criar medicamento ${s.nome} em ${m}: ${i.message}`)}await V(e,a,p,r+1,m,t)}catch(i){t.incrementErrorCount(1),t.addError(`Erro ao processar subseção ${n}: ${i.message}`)}if(e.substancias&&Array.isArray(e.substancias))for(const n of e.substancias)try{await D(a,s,n),t.incrementMedicationCount(1),t.incrementSuccessCount(1)}catch(i){t.incrementErrorCount(1),t.addError(`Erro ao criar medicamento ${n.nome}: ${i.message}`)}},D=async(e,a,s)=>{const{error:r}=await j.from("pedbook_breastfeeding_medications").insert({section_id:e,subsection_id:a,name:s.nome,compatibility_level:F(s.compatibilidade_amamentacao),usage_description:s.uso_amamentacao,additional_info:s.informacao_adicional||null});if(r)throw r},F=e=>{const a=e.trim();return"Verde"===a?"Verde":"Amarelo"===a?"Amarelo":"Vermelho"===a?"Vermelho":"Amarelo"};return e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[e.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Gerenciar Medicamentos e Amamentação"}),e.jsxs(o,{defaultValue:"import",children:[e.jsxs(t,{className:"mb-4",children:[e.jsx(i,{value:"import",children:"Importar JSON"}),e.jsx(i,{value:"clear",children:"Limpar Dados"})]}),e.jsx(n,{value:"import",children:e.jsxs(c,{children:[e.jsxs(d,{children:[e.jsx(m,{children:"Importar Medicamentos para Amamentação"}),e.jsx(l,{children:"Importe medicamentos e suas informações para o banco de dados a partir de um arquivo JSON."})]}),e.jsx(u,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Selecione um arquivo JSON"}),e.jsx(p,{type:"file",accept:".json",onChange:e=>{const a=e.target.files?.[0];if(!a)return;const s=new FileReader;s.onload=async e=>{e.target?.result&&J(e.target.result.toString())},s.readAsText(a)},disabled:_,className:"file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Ou cole o JSON diretamente"}),e.jsx("textarea",{className:"min-h-[200px] w-full border rounded-md p-2 font-mono text-sm",value:q,onChange:e=>{J(e.target.value)},disabled:_,placeholder:'{ "SEÇÃO": { "descricao": null, "substancias": [{ "nome": "Medicamento", "compatibilidade_amamentacao": "Verde", "uso_amamentacao": "Uso compatível com a amamentação." }], "SUBSEÇÃO": { "descricao": null, "substancias": [] } } }'})]}),e.jsx(x,{onClick:async()=>{if(q)try{E(!0),k([]),A(0);const a=JSON.parse(q);let r=Object.keys(a).length,o=0,t=0,i=0,n=0,c=0;for(const[s,d]of Object.entries(a)){o++;try{M(`Processando seção ${o}/${r}: ${s}`);const{data:a,error:m}=await j.from("pedbook_breastfeeding_sections").insert({name:s,description:d.descricao||null,display_order:o}).select().single();if(m){c++,k((e=>[...e,`Erro ao criar seção ${s}: ${m.message}`]));continue}const l=a.id,u=d;if(u.substancias&&Array.isArray(u.substancias))for(const s of u.substancias)try{await D(l,null,s),t++,n++}catch(e){c++,k((a=>[...a,`Erro ao criar medicamento ${s.nome}: ${e.message}`]))}await V(u,l,null,1,s,{incrementSubsectionCount:e=>{i+=e},incrementMedicationCount:e=>{t+=e},incrementSuccessCount:e=>{n+=e},incrementErrorCount:e=>{c+=e},addError:e=>{k((a=>[...a,e]))}}),A(o/r*100),$({total:r,processed:o,succeeded:n,failed:c})}catch(e){c++,k((a=>[...a,`Erro ao processar seção ${s}: ${e.message}`]))}}s({title:"Importação concluída",description:`Processados: ${o} seções, ${i} subseções, ${t} medicamentos. Sucesso: ${n}, Erros: ${c}`,variant:c>0?"destructive":"default"}),A(100)}catch(e){s({title:"Erro ao processar JSON",description:e.message,variant:"destructive"}),k((a=>[...a,`Erro ao processar JSON: ${e.message}`]))}finally{E(!1)}else s({title:"Nenhum arquivo selecionado",description:"Por favor, selecione um arquivo JSON para importar.",variant:"destructive"})},disabled:_||!q.trim(),className:"w-full",children:_?e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"mr-2 h-4 w-4 animate-spin"}),"Importando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"mr-2 h-4 w-4"}),"Importar Dados"]})}),_&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-xs text-gray-500",children:C}),e.jsx(h,{value:w}),e.jsxs("div",{className:"text-xs text-gray-600",children:[S.processed," de ",S.total," seções processadas (",S.succeeded," sucessos, ",S.failed," falhas)"]})]}),O.length>0&&e.jsx("div",{className:"mt-4",children:e.jsxs(v,{variant:"destructive",children:[e.jsx(b,{className:"h-4 w-4"}),e.jsxs(y,{children:["Erros encontrados (",O.length,")"]}),e.jsx(g,{children:e.jsx("div",{className:"max-h-40 overflow-y-auto mt-2",children:e.jsx("ul",{className:"list-disc pl-4 space-y-1",children:O.map(((a,s)=>e.jsx("li",{className:"text-xs",children:a},s)))})})})]})})]})})]})}),e.jsx(n,{value:"clear",children:e.jsxs(c,{children:[e.jsxs(d,{children:[e.jsx(m,{children:"Limpar Dados Existentes"}),e.jsxs(l,{children:["Remova todos os dados de medicamentos e amamentação do banco de dados.",e.jsx("br",{}),e.jsx("span",{className:"text-red-500 font-medium",children:"Atenção: Esta ação não pode ser desfeita!"})]})]}),e.jsx(u,{children:e.jsx(x,{variant:"destructive",onClick:async()=>{try{E(!0),M("Limpando dados existentes...");const{error:e}=await j.from("pedbook_breastfeeding_medications").delete().neq("id","00000000-0000-0000-0000-000000000000");if(e)throw e;const{error:a}=await j.from("pedbook_breastfeeding_subsections").delete().neq("id","00000000-0000-0000-0000-000000000000");if(a)throw a;const{error:r}=await j.from("pedbook_breastfeeding_sections").delete().neq("id","00000000-0000-0000-0000-000000000000");if(r)throw r;s({title:"Dados limpos com sucesso",description:"Todos os dados de medicamentos para amamentação foram removidos."})}catch(e){s({title:"Erro ao limpar dados",description:e.message,variant:"destructive"})}finally{E(!1)}},disabled:_,children:_?e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"mr-2 h-4 w-4 animate-spin"}),"Processando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(b,{className:"mr-2 h-4 w-4"}),"Limpar Todos os Dados"]})})})]})})]}),e.jsxs(c,{className:"mt-6",children:[e.jsx(d,{children:e.jsx(m,{children:"Formato esperado para o JSON"})}),e.jsx(u,{children:e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-xs overflow-x-auto",children:'{\n  "NOME DA SEÇÃO": {\n    "descricao": null,\n    "substancias": [\n      {\n        "nome": "Nome do Medicamento",\n        "compatibilidade_amamentacao": "Verde",\n        "uso_amamentacao": "Uso compatível com a amamentação."\n      }\n    ],\n    "NOME DA SUBSEÇÃO": {\n      "descricao": null,\n      "substancias": [\n        {\n          "nome": "Nome do Medicamento",\n          "compatibilidade_amamentacao": "Amarelo",\n          "uso_amamentacao": "Uso criterioso durante a amamentação."\n        }\n      ],\n      "NOME DA SUB-SUBSEÇÃO": {\n        "descricao": null,\n        "substancias": [\n          {\n            "nome": "Nome do Medicamento",\n            "compatibilidade_amamentacao": "Verde",\n            "uso_amamentacao": "Uso compatível com a amamentação."\n          }\n        ]\n      }\n    }\n  }\n}'})})]})]})};export{_ as default};
