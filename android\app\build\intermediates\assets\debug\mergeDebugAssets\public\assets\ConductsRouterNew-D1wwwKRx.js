var e=Object.defineProperty,t=(t,r,a)=>((t,r,a)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a)(t,"symbol"!=typeof r?r+"":r,a);import{j as r}from"./radix-core-6kBL75b5.js";import{b as a,r as s}from"./critical-DVX9Inzy.js";import{u as o}from"./query-vendor-B-7l6Nb3.js";import{j as n,d as l,b0 as i,aa as c,B as d,C as m,ac as g,a5 as u,L as h,s as x,a8 as p,u as b,R as y,T as f,D as j,e as v,X as w,m as k,aK as N,g as C,z as S,aq as _,at as E,f as $,ab as T}from"./index-CFFY2EZF.js";import q from"./Footer-Cy0ETRb8.js";import{L,c as I,a as M}from"./router-BAzpOxbo.js";import{F}from"./folder-lglkc8Qk.js";import{F as R}from"./folder-open-CG3MhL0n.js";import{C as A}from"./chevron-left-DTv-nKMo.js";import{B as z}from"./book-open-DfMTUXW0.js";import{A as P,a as O,b as H,c as W}from"./accordion-Cl5OLBW2.js";import{A as B,a as D,b as V,c as K,d as Y,e as U,f as X,g as G}from"./alert-dialog-C1vu9HxH.js";import{H as J}from"./FeedbackTrigger-D3WjakYT.js";import{T as Z}from"./thumbs-up-BjJPXZSW.js";import{M as Q}from"./meh-DKo_xs6I.js";import{T as ee}from"./thumbs-down-CHJuq6nr.js";import{Z as te,a as re}from"./zoom-out-CZnhGeh_.js";import{L as ae}from"./LazyImage-HHjE2m0O.js";import{V as se}from"./radix-toast-1_gbKn9f.js";import{M as oe}from"./markdown-vendor-C57yw7YK.js";import{r as ne}from"./index-Bf1cTgQT.js";import{L as le}from"./list-CpwFXIO5.js";import{u as ie}from"./use-mobile-DZ3cxmhN.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-CMzOelh-.js";import"./rocket-CXfOzAv0.js";import"./target-BR2vVj-t.js";import"./zap-DiDsx3f0.js";import"./star-D6ndiBmz.js";import"./circle-help-8_s38d5r.js";const ce=({topic:e,categorySlug:t,subcategorySlug:a,searchTerm:s})=>{const o=e.is_subcategory?`/condutas-e-manejos/${t}/${e.slug}`:a?`/condutas-e-manejos/${t}/${a}/${e.slug}`:`/condutas-e-manejos/${t}/${e.slug}`;return r.jsx(L,{to:o,className:"block w-full h-full",children:r.jsx("div",{className:n("h-full p-3 md:p-4 rounded-xl border transition-all duration-300 backdrop-blur-sm group",e.is_subcategory?"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 border-blue-200 dark:border-blue-700 hover:from-blue-100 hover:via-indigo-100 hover:to-purple-100 dark:hover:from-blue-800/40 dark:hover:via-indigo-800/40 dark:hover:to-purple-800/40 hover:shadow-xl hover:border-blue-300 dark:hover:border-blue-600 hover:scale-[1.03] hover:-translate-y-1":"bg-white dark:bg-slate-800/90 border-gray-200 dark:border-slate-700 hover:shadow-lg hover:border-green-200 dark:hover:border-green-700/50 hover:scale-[1.02] hover:-translate-y-0.5"),children:r.jsxs("div",{className:"flex items-center gap-3",children:[e.is_subcategory?r.jsxs("div",{className:"relative flex-shrink-0",children:[r.jsx(F,{className:"h-6 w-6 text-blue-500 dark:text-blue-400 group-hover:hidden transition-all"}),r.jsx(R,{className:"h-6 w-6 text-blue-600 dark:text-blue-300 hidden group-hover:block transition-all"})]}):r.jsx("div",{className:"w-6 h-6 rounded-lg bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/40 dark:to-emerald-900/40 flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform",children:r.jsx("div",{className:"w-2.5 h-2.5 rounded-full bg-green-500 dark:bg-green-400"})}),r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx("h3",{className:n("font-semibold leading-tight transition-colors line-clamp-2",e.is_subcategory?"text-base text-blue-700 dark:text-blue-300 group-hover:text-blue-800 dark:group-hover:text-blue-200":"text-base text-gray-800 dark:text-gray-200 group-hover:text-green-700 dark:group-hover:text-green-300"),children:(e=>{if(!s||""===s.trim())return e;const t=e.split(new RegExp(`(${s})`,"gi"));return r.jsx(r.Fragment,{children:t.map(((e,t)=>e.toLowerCase()===s.toLowerCase()?r.jsx("span",{className:"bg-yellow-200 dark:bg-yellow-900 font-semibold",children:e},t):e))})})(e.name)})})]})})})},de=new class{constructor(){t(this,"logs",[]),t(this,"timers",new Map)}logRender(e,t){}logRequest(e,t,r){}logEffect(e,t,r="EFFECT"){}startTimer(e){}endTimer(e,t,r="TIMER"){}getStats(){return{}}findDuplicateRequests(e){return[]}findExcessiveRenders(e){return[]}cleanup(){}};setInterval((()=>de.cleanup()),3e5),window.clearAllCaches=()=>{"undefined"!=typeof window&&["requestCache","summaryCache","topicListCache"].forEach((e=>{try{const t=window[e];t&&"function"==typeof t.clear&&t.clear()}catch(t){}}))};const me=a.memo((()=>{const{categorySlug:e,subcategorySlug:t}=I(),[n,p]=s.useState(""),b=M();l();const{data:y,isLoading:f}=(e=>o({queryKey:["conduct-category",e],queryFn:async()=>{if(!e)return null;de.logRequest("ConductsTopicList",`category-${e}`);const{data:t,error:r}=await x.from("pedbook_conducts_categories").select("id, name").eq("slug",e).single();if(r)throw r;return t},enabled:!!e,staleTime:3e5,gcTime:6e5}))(e),{data:j,isLoading:v}=((e,t)=>o({queryKey:["conduct-subcategory",e,t],queryFn:async()=>{if(!e||!t)return null;de.logRequest("ConductsTopicList",`subcategory-${e}-${t}`);const{data:r,error:a}=await x.from("pedbook_conducts_topics").select("id, name, is_subcategory").eq("category_id",e).eq("slug",t).eq("is_subcategory",!0).maybeSingle();if(a)throw a;return r},enabled:!!e&&!!t,staleTime:3e5,gcTime:6e5}))(y?.id,t),{data:w=[],isLoading:k}=((e,t)=>o({queryKey:["conduct-topics",e,t],queryFn:async()=>{if(!e)return[];let r;de.logRequest("ConductsTopicList",`topics-${e}-${t||"direct"}`),r=t?x.from("v_conducts_topics").select("id, name, slug, category_name, is_subcategory, parent_id").eq("parent_id",t):x.from("v_conducts_topics").select("id, name, slug, category_name, is_subcategory, parent_id").eq("category_slug",e).is("parent_id",null);const{data:a,error:s}=await r;if(s)throw s;return a},enabled:!!e,staleTime:3e5,gcTime:6e5}))(e,j?.id),N=w.map((e=>e.id)),{data:C={},isLoading:S}=(e=>o({queryKey:["conduct-summary-counts",e.sort().join(",")],queryFn:async()=>{if(0===e.length)return{};de.logRequest("ConductsTopicList",`summary-counts-batch-${e.length}`);const[t,r]=await Promise.all([x.from("pedbook_conducts_summaries").select("topic_id").in("topic_id",e).eq("published",!0),x.from("pedbook_conducts_optimized").select("topic_id").in("topic_id",e).eq("published",!0)]),a={};return t.data?.forEach((e=>{a[e.topic_id]=(a[e.topic_id]||0)+1})),r.data?.forEach((e=>{a[e.topic_id]=(a[e.topic_id]||0)+1})),a},enabled:e.length>0,staleTime:3e5,gcTime:6e5}))(N),_=a.useMemo((()=>w.map((e=>({...e,summary_count:C[e.id]||0}))).sort(((e,t)=>e.name.localeCompare(t.name,"pt-BR")))),[w,Object.keys(C).join(","),Object.values(C).join(",")]),E=f||v||k||S;de.logRender("ConductsTopicList",{categorySlug:e,subcategorySlug:t,topicsCount:_.length,loading:E});const $=a.useMemo((()=>n.trim()?_.filter((e=>{const t=n.toLowerCase(),r=e.name.toLowerCase();return r.includes(t)||i(r,t)<=2})):_),[n,_]);return r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col",children:[r.jsx(c,{}),r.jsx("main",{className:"flex-1 container mx-auto px-4 lg:px-6 xl:px-8 py-6 md:py-8",children:r.jsxs("div",{className:"max-w-7xl mx-auto",children:[r.jsxs("div",{className:"bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 p-4 mb-6 shadow-lg",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsxs(d,{variant:"ghost",size:"sm",onClick:()=>{b(t?`/condutas-e-manejos/${e}`:"/condutas-e-manejos")},className:"flex items-center gap-1 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 px-2 py-1 rounded-lg transition-all text-sm",children:[r.jsx(A,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden sm:inline",children:"Voltar"})]}),r.jsx("div",{className:"flex items-center gap-1 text-gray-600 dark:text-gray-300 min-w-0 flex-1",children:r.jsxs("div",{className:"flex items-center gap-1 px-2 py-1 bg-blue-50 dark:bg-blue-900/30 rounded-md min-w-0",children:[r.jsx("span",{className:"text-sm font-medium text-blue-700 dark:text-blue-300 truncate",children:y?.name}),j&&r.jsxs(r.Fragment,{children:[r.jsx(m,{className:"w-3 h-3 text-blue-400 flex-shrink-0"}),r.jsx("span",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 truncate",children:j.name})]})]})})]}),r.jsxs("div",{className:"relative mt-6",children:[r.jsx(g,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),r.jsx(u,{type:"text",placeholder:"🔍 Buscar tópicos e subcategorias...",value:n,onChange:e=>p(e.target.value),className:"pl-12 pr-4 py-3 text-base bg-white dark:bg-slate-700 border-2 border-blue-100 dark:border-blue-800 focus:border-blue-300 dark:focus:border-blue-600 rounded-xl shadow-sm"})]})]}),E&&r.jsxs("div",{className:"flex flex-col items-center justify-center py-16",children:[r.jsx(h,{className:"w-12 h-12 animate-spin text-blue-500 mb-4"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Carregando tópicos..."})]}),!E&&r.jsx(r.Fragment,{children:$.length>0?r.jsx("div",{className:"space-y-6",children:(()=>{const a=$.filter((e=>e.is_subcategory)),s=$.filter((e=>!e.is_subcategory));return r.jsxs(r.Fragment,{children:[a.length>0&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2",children:[r.jsx(F,{className:"w-5 h-5 text-blue-500"}),"Subcategorias"]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4",children:a.map((a=>r.jsx(ce,{topic:a,categorySlug:e||"",subcategorySlug:t,searchTerm:n},a.id)))})]}),s.length>0&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2",children:[r.jsx(z,{className:"w-5 h-5 text-green-500"}),"Tópicos ",a.length>0?`(${s.length})`:""]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4",children:s.map((a=>r.jsx(ce,{topic:a,categorySlug:e||"",subcategorySlug:t,searchTerm:n},a.id)))})]})]})})()}):r.jsx("div",{className:"text-center py-16",children:r.jsxs("div",{className:"max-w-md mx-auto",children:[r.jsx("div",{className:"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full flex items-center justify-center",children:r.jsx(g,{className:"w-12 h-12 text-blue-500 dark:text-blue-400"})}),r.jsx("h3",{className:"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-3",children:"Nenhum tópico encontrado"}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:n?`Não encontramos resultados para "${n}". Tente ajustar sua pesquisa.`:"Não há tópicos disponíveis nesta categoria no momento."}),n&&r.jsx(d,{variant:"outline",onClick:()=>p(""),className:"mx-auto",children:"Limpar busca"})]})})})]})}),r.jsx(q,{})]})}));me.displayName="ConductsTopicListNew";const ge=({name:e,description:t,slug:a,categoryName:s,categorySlug:o})=>{const n=t||`Condutas e manejos pediátricos sobre ${e.toLowerCase()}, incluindo protocolos, diagnóstico e tratamento baseados em evidências.`,l=(()=>{const t=new Set([`${e} pediatria`,`conduta ${e.toLowerCase()}`,`manejo ${e.toLowerCase()}`,`protocolo ${e.toLowerCase()}`,`pediatria ${e.toLowerCase()}`,`tratamento ${e.toLowerCase()}`,`guideline ${e.toLowerCase()}`]);return s&&(t.add(`${s} pediatria`),t.add(`condutas ${s.toLowerCase()}`),t.add(`protocolos ${s.toLowerCase()}`)),Array.from(t).join(", ")})(),i=`https://pedb.com.br/condutas-e-manejos/${o}/${a}`,c=`PedBook | ${e} - Condutas e Manejos Pediátricos`,d="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/1496949.webp";return r.jsxs(p,{children:[r.jsx("title",{children:c}),r.jsx("meta",{name:"description",content:n}),r.jsx("meta",{name:"keywords",content:l}),r.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large"}),r.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),r.jsx("meta",{charSet:"UTF-8"}),r.jsx("link",{rel:"canonical",href:i}),r.jsx("meta",{property:"og:title",content:c}),r.jsx("meta",{property:"og:description",content:n}),r.jsx("meta",{property:"og:type",content:"article"}),r.jsx("meta",{property:"og:url",content:i}),r.jsx("meta",{property:"og:site_name",content:"PedBook"}),r.jsx("meta",{property:"og:locale",content:"pt_BR"}),r.jsx("meta",{property:"og:image",content:d}),r.jsx("meta",{property:"og:image:alt",content:`Protocolo pediátrico sobre ${e}`}),r.jsx("meta",{property:"og:image:width",content:"1200"}),r.jsx("meta",{property:"og:image:height",content:"630"}),r.jsx("meta",{property:"article:publisher",content:"https://pedb.com.br"}),r.jsx("meta",{property:"article:section",content:s||"Condutas e Manejos"}),r.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),r.jsx("meta",{name:"twitter:title",content:c}),r.jsx("meta",{name:"twitter:description",content:n}),r.jsx("meta",{name:"twitter:image",content:d}),r.jsx("meta",{name:"twitter:site",content:"@PedBook"}),r.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"MedicalGuideline",name:c,description:n,url:i,keywords:l.split(", "),inLanguage:"pt-BR",specialty:"Pediatria",guideline:{"@type":"MedicalGuidelineRecommendation",recommendationStrength:"Strong",evidenceLevel:"Evidence-based"},guidelineSubject:{"@type":"MedicalCondition",name:e},guidelineCategory:s,audience:{"@type":"MedicalAudience",audienceType:"Profissionais de Saúde"},publisher:{"@type":"Organization",name:"PedBook",url:"https://pedb.com.br",logo:{"@type":"ImageObject",url:"https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/faviconx.png"}},image:{"@type":"ImageObject",url:d,width:1200,height:630},dateModified:(new Date).toISOString()})})]})},ue=[{value:"excellent",label:"Excelente",icon:J,color:"text-blue-600",bgColor:"bg-blue-50 hover:bg-blue-100"},{value:"good",label:"Bom",icon:Z,color:"text-green-500",bgColor:"bg-green-50 hover:bg-green-100"},{value:"regular",label:"Regular",icon:Q,color:"text-yellow-500",bgColor:"bg-yellow-50 hover:bg-yellow-100"},{value:"poor",label:"Ruim",icon:ee,color:"text-red-500",bgColor:"bg-red-50 hover:bg-red-100"}];function he({summaryId:e,summaryTitle:t}){const{user:a}=b(),{toast:o}=l(),[n,i]=s.useState(null),[c,m]=s.useState(!1),[g,u]=s.useState(""),[h,p]=s.useState(null);s.useEffect((()=>{(async()=>{if(a)try{const{data:t,error:r}=await x.from("pedbook_conducts_feedback").select("rating").eq("summary_id",e).eq("user_id",a.id).maybeSingle();if(r)return;t&&i(t.rating)}catch(t){}})()}),[a,e]);const j=async(r,s)=>{if(a)if(n)o({title:"Feedback já enviado",description:"Você já avaliou este resumo anteriormente.",variant:"destructive"});else try{const{error:n}=await x.from("pedbook_conducts_feedback").insert({summary_id:e,summary_title:t,user_id:a.id,rating:r,comment:s});if(n){if("23505"===n.code)return void o({title:"Feedback já enviado",description:"Você já avaliou este resumo anteriormente.",variant:"destructive"});throw n}i(r),o({title:"Feedback enviado",description:"Obrigado por contribuir com sua avaliação!"+(s?" Seu comentário nos ajudará a melhorar.":"")})}catch(l){o({title:"Erro ao enviar feedback",description:l.message,variant:"destructive"})}else o({title:"Login necessário",description:"Você precisa estar logado para enviar feedback.",variant:"destructive"})};return r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"mt-6 border-t pt-6",children:[r.jsx("div",{className:"text-center space-y-2",children:r.jsx("h3",{className:"text-base font-medium text-gray-900",children:n?"Sua avaliação":"Este resumo foi útil para você?"})}),r.jsx("div",{className:"mt-4 flex justify-center gap-3",children:ue.map((e=>{const t=n===e.value;return r.jsxs(y,{className:`flex flex-col items-center justify-center p-2 cursor-${n?"default":"pointer"} transition-all\n                  ${t?`${e.bgColor} ring-2 ring-${e.color}`:"bg-gray-50"}\n                  ${n?"":`${e.bgColor} hover:ring-2 hover:ring-${e.color}`}\n                  w-16 h-16`,onClick:()=>!n&&(e=>{if("excellent"!==e){p(e);const{dismiss:t}=o({title:"Poderia nos ajudar a melhorar?",description:r.jsxs("div",{className:"mt-2 space-y-4",children:[r.jsx("p",{children:"Gostaríamos saber sua opinião para melhorarmos nosso conteúdo. Aceitamos qualquer tipo de crítica construtiva ou sugestão."}),r.jsxs("div",{className:"flex justify-end space-x-2",children:[r.jsx(d,{variant:"outline",onClick:()=>{t(),j(e)},children:"Não, obrigado"}),r.jsx(d,{onClick:()=>{t(),m(!0)},children:"Sim, quero ajudar"})]})]}),duration:1e4,className:"fixed !top-[50%] !left-[50%] !-translate-x-1/2 !-translate-y-1/2 !bottom-auto !right-auto max-w-[90dvw] w-full md:max-w-[400px] !p-6"})}else j(e)})(e.value),children:[r.jsx(e.icon,{className:`w-6 h-6 ${t?e.color:"text-gray-400"}`}),r.jsx("span",{className:"mt-1 text-xs font-medium "+(t?"text-gray-900":"text-gray-500"),children:e.label})]},e.value)}))})]}),r.jsx(B,{open:c,onOpenChange:m,children:r.jsxs(D,{children:[r.jsxs(V,{children:[r.jsx(K,{children:"Deixe seu comentário"}),r.jsx(Y,{children:"Sua opinião é muito importante para melhorarmos nosso conteúdo. Por favor, compartilhe suas sugestões ou críticas construtivas."})]}),r.jsx("div",{className:"my-4",children:r.jsx(f,{value:g,onChange:e=>u(e.target.value),placeholder:"Digite seu comentário aqui...",className:"min-h-[100px]"})}),r.jsxs(U,{children:[r.jsx(X,{onClick:()=>{m(!1),h&&j(h)},children:"Cancelar"}),r.jsx(G,{onClick:()=>{h&&g.trim()&&(j(h,g.trim()),m(!1),u(""),p(null))},children:"Enviar comentário"})]})]})})]})}function xe({isOpen:e,onClose:t,imageUrl:a,alt:o}){const[n,l]=s.useState(1),[i,c]=s.useState(!1),[m,g]=s.useState({x:0,y:0}),[u,h]=s.useState({x:0,y:0}),x=()=>{c(!1)},p=()=>{t(),l(1),g({x:0,y:0})};return r.jsx(j,{open:e,onOpenChange:p,children:r.jsx(v,{className:"max-w-[90vw] max-h-[90vh] p-0 overflow-hidden bg-black/90",onPointerDownOutside:p,children:r.jsxs("div",{className:"relative w-full h-full min-h-[50vh]",children:[r.jsx(d,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 z-50 text-white hover:bg-white/20",onClick:p,children:r.jsx(w,{className:"h-4 w-4"})}),r.jsxs("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 z-50 flex items-center gap-2",children:[r.jsx(d,{variant:"ghost",size:"icon",className:"text-white hover:bg-white/20",onClick:()=>{l((e=>Math.max(e-.5,1))),n<=1.5&&g({x:0,y:0})},disabled:n<=1,children:r.jsx(te,{className:"h-4 w-4"})}),r.jsx(d,{variant:"ghost",size:"icon",className:"text-white hover:bg-white/20",onClick:()=>{l((e=>Math.min(e+.5,3)))},disabled:n>=3,children:r.jsx(re,{className:"h-4 w-4"})})]}),r.jsx(k.div,{className:"w-full h-full flex items-center justify-center cursor-grab active:cursor-grabbing no-swipe",onMouseDown:e=>{n>1&&(c(!0),h({x:e.clientX-m.x,y:e.clientY-m.y}))},onMouseMove:e=>{i&&n>1&&g({x:e.clientX-u.x,y:e.clientY-u.y})},onMouseUp:x,onMouseLeave:x,onTouchStart:e=>{if(n>1&&1===e.touches.length){const t=e.touches[0];c(!0),h({x:t.clientX-m.x,y:t.clientY-m.y})}},onTouchMove:e=>{if(i&&n>1&&1===e.touches.length){const t=e.touches[0];g({x:t.clientX-u.x,y:t.clientY-u.y})}},onTouchEnd:()=>{c(!1)},"data-zoom":"true","data-draggable":"true",animate:{scale:n,x:m.x,y:m.y},transition:{type:"spring",stiffness:300,damping:30},children:r.jsx("img",{src:a,alt:o||"Imagem expandida",className:"max-w-full max-h-[80vh] object-contain select-none",draggable:!1})})]})})})}const pe=({src:e,alt:t,title:a,source:o,children:n,allImages:l=[],currentIndex:i=0})=>{const[c,d]=s.useState(!1),[g,u]=s.useState(i),h=l.length>0?l:[{src:e,title:a,source:o,alt:t}],x=h[g]||h[0];return s.useEffect((()=>{const e=e=>{if(c)switch(e.key){case"Escape":d(!1);break;case"ArrowLeft":h.length>1&&u((e=>e>0?e-1:h.length-1));break;case"ArrowRight":h.length>1&&u((e=>e<h.length-1?e+1:0))}};return c&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}}),[c,h.length]),s.useEffect((()=>{c&&u(i)}),[c,i]),r.jsxs(j,{open:c,onOpenChange:d,children:[r.jsx(N,{asChild:!0,children:n}),r.jsxs(v,{className:"max-w-[95dvw] w-[95dvw] h-[95dvh] sm:max-w-[90vw] sm:max-h-[90vh] sm:h-auto sm:w-auto p-0 border-0 bg-white dark:bg-gray-900 shadow-2xl overflow-hidden rounded-xl",onInteractOutside:()=>d(!1),hideCloseButton:!0,children:[r.jsxs(se,{children:[r.jsx(C,{children:x.title||"Visualizar imagem"}),r.jsxs(S,{children:[x.title?`Imagem: ${x.title}`:"Imagem expandida",x.source?` - Fonte: ${x.source}`:"",h.length>1?` - ${g+1} de ${h.length}`:""]})]}),r.jsxs("div",{className:"relative w-full h-full bg-white dark:bg-gray-900 flex flex-col",children:[r.jsx("div",{className:"flex-shrink-0 flex justify-end p-3 sm:p-4",children:r.jsx("button",{onClick:()=>d(!1),className:"w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 shadow-md","aria-label":"Fechar imagem",children:r.jsx(w,{className:"w-4 h-4 sm:w-5 sm:h-5"})})}),r.jsxs("div",{className:"flex-1 flex items-center justify-center relative",children:[h.length>1&&r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 z-10",children:r.jsx("button",{onClick:()=>{u((e=>e>0?e-1:h.length-1))},className:"w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 shadow-md","aria-label":"Imagem anterior",children:r.jsx(A,{className:"w-5 h-5 sm:w-6 sm:h-6"})})}),r.jsx("div",{className:"absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 z-10",children:r.jsx("button",{onClick:()=>{u((e=>e<h.length-1?e+1:0))},className:"w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 shadow-md","aria-label":"Próxima imagem",children:r.jsx(m,{className:"w-5 h-5 sm:w-6 sm:h-6"})})})]}),r.jsx("div",{className:"flex items-center justify-center px-16 sm:px-20 w-full h-full",children:r.jsx("div",{className:"relative flex items-center justify-center max-w-full max-h-[calc(95dvh-16rem)] sm:max-h-[calc(90vh-12rem)]",children:r.jsx("img",{src:x.src,alt:x.alt||x.title||"Imagem expandida",className:"max-w-full max-h-full object-contain rounded-lg shadow-lg",style:{maxWidth:"100%",maxHeight:"100%"}})})})]}),r.jsxs("div",{className:"flex-shrink-0 p-4 sm:p-6",children:[(x.title||x.source)&&r.jsxs("div",{className:"max-w-2xl mx-auto text-center mb-4",children:[x.title&&r.jsx("h3",{className:"text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:x.title}),x.source&&r.jsx("p",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-400 italic",children:x.source}),h.length>1&&r.jsxs("p",{className:"text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-2",children:[g+1," de ",h.length]})]}),h.length>1&&r.jsx("div",{className:"flex justify-center gap-2",children:h.map(((e,t)=>r.jsx("button",{onClick:()=>u(t),className:"w-2 h-2 rounded-full transition-all duration-200 "+(t===g?"bg-blue-500 dark:bg-blue-400 w-6":"bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"),"aria-label":`Ir para imagem ${t+1}`},t)))})]})]})]})]})},be=a.memo((({src:e,title:t,source:a,alt:s,className:o="",allImages:n=[],currentIndex:l=0})=>r.jsx("figure",{className:`my-6 text-center px-4 sm:px-0 ${o}`,children:r.jsxs("div",{className:"inline-block max-w-[calc(100vw-2rem)] sm:max-w-sm mx-auto",children:[r.jsx(pe,{src:e,title:t,source:a,alt:s||t||"Imagem médica",allImages:n,currentIndex:l,children:r.jsxs("div",{className:"cursor-pointer group relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-shadow duration-200 max-w-full",children:[r.jsx(ae,{src:e,alt:s||t||"Imagem médica",className:"w-full h-auto max-h-80 object-contain transition-transform duration-200 group-hover:scale-[1.02]",priority:!1}),r.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 dark:group-hover:bg-white/10 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100",children:r.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700 dark:text-gray-200 shadow-lg",children:"Clique para expandir"})})]})}),(t||a)&&r.jsxs("figcaption",{className:"mt-3 text-sm text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:[t&&r.jsx("div",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-1",children:t}),a&&r.jsx("div",{className:"italic text-gray-500 dark:text-gray-400",children:a})]})]})}))),ye=({images:e,className:t=""})=>0===e.length?r.jsxs("div",{className:`text-center py-12 ${t}`,children:[r.jsx("div",{className:"w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center",children:r.jsx("span",{className:"text-4xl",children:"🖼️"})}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"Nenhuma imagem global encontrada"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-md mx-auto",children:"As imagens marcadas como globais aparecerão aqui em formato de galeria."})]}):r.jsx("div",{className:`w-full ${t}`,children:r.jsx("div",{className:"flex flex-wrap justify-center gap-8 max-w-4xl mx-auto",children:e.map(((t,a)=>r.jsx("div",{className:"flex-shrink-0",children:r.jsx(be,{src:t.src,title:t.title,source:t.source,alt:t.alt,allImages:e,currentIndex:a})},a)))})});let fe=null;const je=({title:e,conductsContent:t,treatmentContent:o,hasTreatment:n})=>{const[l,i]=s.useState("conducts"),[c,d]=s.useState(!1),[g,u]=s.useState(""),[h,x]=s.useState(""),[p,b]=s.useState(""),[y,f]=s.useState(""),w="conducts"===l?g:p,k="conducts"===l?h:y,[N,T]=s.useState(!1),q=s.useCallback((e=>{i(e);const t=new CustomEvent("optimizedTabChange",{detail:e});window.dispatchEvent(t);const r=document.querySelector(".content-scroll-area");r&&(r.scrollTop=0)}),[]),L=s.useCallback((e=>{const t=document.getElementById(e);t&&(T(!1),setTimeout((()=>{const e=document.querySelector(".content-scroll-area");if(e){const r=t.offsetTop,a=c?100:80;e.scrollTo({top:r-a,behavior:"smooth"})}else t.scrollIntoView({behavior:"smooth",block:"start"})}),150))}),[c]);s.useEffect((()=>{const e=()=>{d(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const I=s.useCallback((e=>{if(!e)return[];const t=e.split("\n"),r=[];return t.forEach((e=>{if(e.startsWith("## ")){const t=e.replace("## ","").trim(),a=t.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();r.push({id:a,title:t,level:2})}})),r}),[]),M=s.useMemo((()=>I("conducts"===l?t:o)),[l,t,o,I]);s.useEffect((()=>{if(fe=l,"images"===l)return;if(0===M.length)return;let e,t=w;const r=()=>{clearTimeout(e),e=setTimeout((()=>{const e=document.querySelector(".content-scroll-area");if(!e)return;const r=e.scrollTop+20;let a="";const s=M.filter((e=>2===e.level)),o=[];for(const t of s){const e=document.getElementById(t.id);if(e){const a=e.offsetTop,s=a-r;s<=300&&o.push({id:t.id,title:t.title,elementTop:a,distanceToSection:s,isPassed:r>=a})}}if(t){const e=o.find((e=>e.id===t));if(e){const r=o.filter((r=>r.id!==t&&Math.abs(r.distanceToSection)<Math.abs(e.distanceToSection)-30));e.distanceToSection>=-30&&0===r.length&&(a=t)}}if(!a&&o.length>0){const e=o.filter((e=>e.isPassed)),t=e.length>0?e[e.length-1]:o[o.length-1];a=t.id}if(!a&&o.length>0){const e=o.reduce(((e,t)=>Math.abs(t.distanceToSection)<Math.abs(e.distanceToSection)?t:e));Math.abs(e.distanceToSection)<=100&&(a=e.id)}if(!a){const t=M.filter((e=>3===e.level));for(let s=t.length-1;s>=0;s--){const o=t[s],n=e.querySelectorAll("h3");for(const e of n)if((e.textContent?.trim()||"").replace(/\*\*/g,"").replace(/\*/g,"").trim()===o.title&&r>=e.offsetTop-30){a=o.id;break}if(a===o.id)break}}if(a!==t){t=a,"conducts"===l?u(a):b(a);const e=M.find((e=>e.id===a));e?"conducts"===l?x(e.title):f(e.title):"conducts"===l?x(""):f("")}}),16)},a=document.querySelector(".content-scroll-area"),s=e=>{const t=e.target;t&&t.classList.contains("content-scroll-area")&&fe===l&&r()};return a&&(document.addEventListener("scroll",s,!0),r(),setTimeout((()=>{const e=document.querySelector(".content-scroll-area");e&&(e.scrollTop=100)}),2e3)),()=>{clearTimeout(e),document.removeEventListener("scroll",s,!0)}}),[M,l]);const F=s.useCallback(((e,t=!1)=>{const r=M.find((t=>t.id===e));if(r){if(t){const e=document.querySelector('[data-state="open"]');e&&e.click()}if(2===r.level){const t=document.getElementById(e);if(t){const r=document.querySelector(".content-scroll-area");if(r){const a=document.querySelector(`[data-section-id="${e}"]`);a&&(a.classList.add("animate-pulse"),setTimeout((()=>a.classList.remove("animate-pulse")),800));const s=t.offsetTop-80,o=r.scrollTop,n=s-o,l=Math.min(800,Math.max(400,.5*Math.abs(n))),i=performance.now(),c=e=>e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1,d=e=>{const t=e-i,a=Math.min(t/l,1),s=c(a),m=o+n*s;r.scrollTop=m,a<1&&requestAnimationFrame(d)};requestAnimationFrame(d)}}}}}),[M]),R=s.useMemo((()=>({table:({children:e})=>r.jsx("div",{className:"overflow-x-auto my-4",children:r.jsx("table",{className:"min-w-full border-collapse border border-gray-300 dark:border-gray-600",children:e})}),th:({children:e})=>r.jsx("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-50 dark:bg-gray-800 font-semibold text-left text-gray-900 dark:text-gray-100",children:e}),td:({children:e})=>r.jsx("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-gray-700 dark:text-gray-300",children:e}),blockquote:({children:e})=>r.jsx("blockquote",{className:"border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-4 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-r-lg text-gray-700 dark:text-gray-300",children:e}),h2:({children:e})=>{const t=("string"==typeof e?e:e?.toString()||"").toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();return r.jsx("div",{className:"first:mt-0 mt-8 mb-8",children:r.jsxs("h2",{id:t,className:"relative text-base sm:text-xl font-bold text-gray-900 dark:text-gray-100 scroll-mt-20 pb-4",children:[r.jsx("span",{className:"relative z-10 bg-white dark:bg-gray-900 pr-4",children:e}),r.jsx("div",{className:"absolute left-0 bottom-0 w-full h-px bg-gradient-to-r from-blue-500/60 via-blue-400/40 to-transparent dark:from-blue-400/60 dark:via-blue-300/40"}),r.jsx("div",{className:"absolute left-0 top-0 w-16 h-full bg-gradient-to-r from-blue-500/8 via-blue-400/5 to-transparent dark:from-blue-400/8 dark:via-blue-300/5 rounded-r-2xl"}),r.jsx("div",{className:"absolute left-2 top-1/2 transform -translate-y-1/2 w-1.5 h-1.5 bg-blue-500 dark:bg-blue-400 rounded-full opacity-60"})]})})},ImageWithCaption:e=>r.jsx(be,{...e}),li:({children:e})=>{if("string"==typeof e){const t=e.split(/\n/),s=t.map(((e,s)=>r.jsxs(a.Fragment,{children:[e,s<t.length-1&&r.jsx("br",{})]},s)));return r.jsx("li",{className:"mb-3 text-gray-700 dark:text-gray-300 leading-relaxed pl-1",children:s})}return r.jsx("li",{className:"mb-3 text-gray-700 dark:text-gray-300 leading-relaxed",children:e})},p:({children:e})=>{const t=e=>"string"==typeof e?(e=>{if(!e||"string"!=typeof e)return e;const t=e.split(/\n/);return t.map(((e,s)=>r.jsxs(a.Fragment,{children:[e,s<t.length-1&&r.jsx("br",{})]},s)))})(e):a.isValidElement(e)&&e.props&&e.props.children?a.cloneElement(e,{...e.props,children:a.Children.map(e.props.children,t)}):e,s=a.Children.map(e,t);return r.jsx("div",{children:s})},ul:({children:e})=>r.jsx("ul",{className:"list-disc list-outside space-y-1 my-4 ml-2 pl-1",children:e}),ol:({children:e})=>r.jsx("ol",{className:"list-decimal list-inside space-y-1 my-4",children:e}),strong:({children:e})=>r.jsx("strong",{className:"font-semibold text-gray-900 dark:text-gray-100",children:e}),code:({children:e})=>{const t=document.documentElement.classList.contains("dark");return r.jsx("code",{className:"inline-block",style:{color:t?"rgb(229 231 235)":"rgb(31 41 55)",backgroundColor:t?"rgb(31 41 55)":"rgb(243 244 246)",padding:"0.125rem 0.375rem",borderRadius:"0.25rem",fontSize:"0.875rem",fontFamily:'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',border:t?"1px solid rgb(55 65 81)":"1px solid rgb(229 231 235)",fontWeight:"500"},children:e})}})),[]),A=s.useCallback((e=>(e.match(/<ImageWithCaption[^>]*\/>/g)||[]).map((e=>{const t=e.match(/<ImageWithCaption\s+([^>]+)\s*\/>/)?.[1]||"",r=t.match(/src="([^"]+)"/),a=t.match(/title="([^"]+)"/),s=t.match(/source="([^"]+)"/),o=t.match(/global="([^"]+)"/);return{src:r?r[1]:"",title:a?a[1]:"",source:s?s[1]:"",alt:a?a[1]:"",global:!!o&&"true"===o[1]}}))),[]),z=s.useCallback((e=>A(e).filter((e=>e.global))),[A]),P=s.useMemo((()=>{const e=z(t),r=n?z(o):[];return e.length>0||r.length>0}),[t,o,n,z]);s.useEffect((()=>{const e=e=>{const t=e.detail;t&&("conducts"===t||"treatment"===t&&n||"images"===t&&P)&&q(t)};return window.addEventListener("tabChange",e),()=>window.removeEventListener("tabChange",e)}),[q,n,P]);const O=s.useCallback((e=>{const t=e.replace(/(\* .*)> (\d+)/g,"$1&gt; $2").replace(/(\s+)> (\d+)/g,"$1&gt; $2").replace(/(\()> (\d+)/g,"($1&gt; $2"),a=A(t),s=t.split(/(<ImageWithCaption[^>]*\/>)/g);let o=0;const n=[];let l=0;for(;l<s.length;){const e=s[l];if(/<ImageWithCaption\s+([^>]+)\s*\/>/.test(e)){const t=[e];for(l++;l<s.length;){const e=s[l];if(/<ImageWithCaption\s+([^>]+)\s*\/>/.test(e))t.push(e),l++;else{if(""!==e.trim())break;l++}}n.push({type:"images",content:t})}else e.trim()?(n.push({type:"markdown",content:e}),l++):l++}return r.jsx("div",{children:n.map(((e,t)=>{if("images"!==e.type)return r.jsx(oe,{remarkPlugins:[ne],className:"prose prose-sm max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-code:text-gray-800 dark:prose-code:text-gray-200 prose-code:bg-gray-100 dark:prose-code:bg-gray-800",components:R,skipHtml:!0,children:e.content},t);{const s=e.content;if(1!==s.length)return r.jsx("div",{className:"my-8 grid grid-cols-1 sm:grid-cols-2 gap-6",children:s.map(((e,t)=>{const s=e.match(/<ImageWithCaption\s+([^>]+)\s*\/>/);if(s){const e=s[1],n=e.match(/src="([^"]+)"/),l=e.match(/title="([^"]+)"/),i=e.match(/source="([^"]+)"/),c=n?n[1]:"",d=l?l[1]:"",m=i?i[1]:"",g=o++;return r.jsx(be,{src:c,title:d,source:m,allImages:a,currentIndex:g,className:"my-0"},t)}return null}))},t);{const e=s[0].match(/<ImageWithCaption\s+([^>]+)\s*\/>/);if(e){const s=e[1],n=s.match(/src="([^"]+)"/),l=s.match(/title="([^"]+)"/),i=s.match(/source="([^"]+)"/),c=n?n[1]:"",d=l?l[1]:"",m=i?i[1]:"",g=o++;return r.jsx(be,{src:c,title:d,source:m,allImages:a,currentIndex:g},t)}}}return null}))})}),[R,A]);return r.jsxs("div",{className:"h-full flex flex-col overflow-hidden relative",children:[k&&"images"!==l&&r.jsx("div",{className:"flex-shrink-0 w-full",children:r.jsx("div",{className:"w-full px-4 lg:container lg:mx-auto lg:max-w-7xl lg:px-6 py-0.5",children:r.jsx("button",{onClick:()=>T(!0),className:"w-full bg-white/90 dark:bg-gray-900/90 rounded-2xl px-4 py-1.5 border border-blue-200/60 dark:border-blue-800/60 shadow-md backdrop-blur-sm hover:bg-white dark:hover:bg-gray-800 transition-colors duration-150 hover:shadow-lg",children:r.jsxs("div",{className:"flex items-center gap-3 min-w-0",children:[r.jsx("div",{className:"w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-400 dark:from-blue-400 dark:to-blue-300 rounded-full animate-pulse shadow-sm flex-shrink-0"}),r.jsxs("span",{className:"text-sm font-medium text-blue-700 dark:text-blue-300 truncate",children:["Lendo: ",k]}),r.jsx(le,{className:"w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0 ml-auto"})]})})})}),r.jsx("div",{className:"flex-1 overflow-hidden",children:r.jsx("div",{className:"w-full px-4 lg:container lg:mx-auto lg:max-w-7xl lg:px-6 pt-1 pb-2 h-full",children:r.jsx("div",{className:"bg-white/80 dark:bg-gray-900/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm h-full overflow-hidden flex flex-col",children:r.jsxs(_,{value:l,onValueChange:q,className:"w-full h-full flex flex-col",children:[r.jsx(E,{value:"conducts",className:"mt-0 flex-1 overflow-y-auto content-scroll-area p-6 lg:p-8 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800",children:r.jsx("div",{className:"prose prose-lg max-w-none w-full dark:prose-invert\r\n                  prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-headings:font-bold\r\n                  prose-h2:text-3xl prose-h2:border-0 prose-h2:pb-4 prose-h2:mb-8 prose-h2:mt-0 prose-h2:text-blue-900 dark:prose-h2:text-blue-300\r\n                  prose-h3:text-xl prose-h3:text-gray-800 dark:prose-h3:text-gray-200 prose-h3:mt-6 prose-h3:mb-4 prose-h3:font-semibold\r\n                  prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-5 prose-p:text-base\r\n                  prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:mb-1 prose-li:text-base prose-li:leading-relaxed\r\n                  prose-ul:space-y-1 prose-ol:space-y-1 prose-ul:my-4 prose-ol:my-4\r\n                  prose-strong:text-gray-900 dark:prose-strong:text-gray-100 prose-strong:font-semibold\r\n                  prose-code:bg-blue-100/80 dark:prose-code:bg-blue-900/30 prose-code:px-2.5 prose-code:py-1 prose-code:rounded-lg prose-code:text-sm prose-code:text-blue-800 dark:prose-code:text-blue-300 prose-code:border prose-code:border-blue-200/50 dark:prose-code:border-blue-800/50\r\n                  [&>*:first-child]:mt-0\r\n                  [&>h2:first-child]:mt-0\r\n                  [&>h3:first-child]:mt-0\r\n                ",children:O(t)})}),n&&r.jsx(E,{value:"treatment",className:"mt-0 flex-1 overflow-y-auto content-scroll-area p-6 lg:p-8 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800",children:r.jsx("div",{className:"prose prose-lg max-w-none w-full dark:prose-invert\r\n                    prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-headings:font-bold\r\n                    prose-h2:text-3xl prose-h2:border-0 prose-h2:pb-4 prose-h2:mb-8 prose-h2:mt-0 prose-h2:text-green-900 dark:prose-h2:text-green-300\r\n                    prose-h3:text-xl prose-h3:text-gray-800 dark:prose-h3:text-gray-200 prose-h3:mt-6 prose-h3:mb-4 prose-h3:font-semibold\r\n                    prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-5 prose-p:text-base\r\n                    prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:mb-1 prose-li:text-base prose-li:leading-relaxed\r\n                    prose-ul:space-y-1 prose-ol:space-y-1 prose-ul:my-4 prose-ol:my-4\r\n                    prose-strong:text-gray-900 dark:prose-strong:text-gray-100 prose-strong:font-semibold\r\n                    prose-code:bg-green-100/80 dark:prose-code:bg-green-900/30 prose-code:px-2.5 prose-code:py-1 prose-code:rounded-lg prose-code:text-sm prose-code:text-green-800 dark:prose-code:text-green-300 prose-code:border prose-code:border-green-200/50 dark:prose-code:border-green-800/50\r\n                    [&>*:first-child]:mt-0\r\n                    [&>h2:first-child]:mt-0\r\n                    [&>h3:first-child]:mt-0\r\n                  ",children:O(o)})}),P&&r.jsx(E,{value:"images",className:"mt-0 flex-1 overflow-y-auto content-scroll-area p-6 lg:p-8 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800",children:r.jsx("div",{className:"w-full",children:(()=>{const e=[...z(t),...n?z(o):[]];return r.jsx(ye,{images:e,className:"py-4"})})()})})]})})})}),r.jsx("div",{className:"hidden lg:flex flex-shrink-0",children:r.jsx("div",{className:"w-full lg:max-w-6xl lg:mx-auto px-4 lg:px-6 pb-4",children:r.jsx("div",{className:"bg-gradient-to-t from-white via-white to-white/90 dark:from-gray-900 dark:via-gray-900 dark:to-gray-900/90 border border-gray-200/30 dark:border-gray-700/30 rounded-2xl py-3 px-6 backdrop-blur-sm shadow-sm",children:r.jsx("div",{className:"flex items-center justify-center gap-4",children:"images"===l?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"flex items-center gap-2",children:[...z(t),...n?z(o):[]].map(((e,t)=>r.jsx("div",{className:"w-3 h-3 rounded-full bg-purple-500 dark:bg-purple-400 shadow-sm",title:`Imagem ${t+1}`},t)))}),r.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 ml-4",children:(()=>{const e=z(t),r=n?z(o):[],a=e.length+r.length;return`${a} ${1===a?"imagem":"imagens"}`})()})]}):r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"flex items-center gap-2",children:M.map(((e,t)=>{const a=w===e.id,s=M.findIndex((e=>e.id===w))>t;return r.jsx("button",{onClick:()=>F(e.id),className:"w-3 h-3 rounded-full transition-all hover:scale-110 "+(a?"bg-blue-500 dark:bg-blue-400 ring-2 ring-blue-200 dark:ring-blue-800":s?"bg-green-500 dark:bg-green-400":"bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"),title:e.title},e.id)}))}),r.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 ml-4",children:[M.findIndex((e=>e.id===w))+1," / ",M.length," seções"]})]})})})})}),r.jsx(j,{open:N,onOpenChange:T,children:r.jsxs(v,{className:"max-w-[95dvw] max-h-[85dvh] w-full h-full rounded-3xl border-2 border-gray-200 dark:border-gray-700 shadow-2xl overflow-hidden",children:[r.jsxs($,{className:"px-6 py-5 border-b border-gray-100 dark:border-gray-800 text-center",children:[r.jsxs(C,{className:"flex items-center justify-center gap-3 text-xl font-semibold text-gray-900 dark:text-gray-100",children:[r.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center",children:r.jsx(le,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),"Sumário"]}),r.jsx(S,{className:"text-gray-500 dark:text-gray-400 mt-1 text-sm",children:"Navegue rapidamente pelas seções do conteúdo"})]}),r.jsx("div",{className:"flex-1 overflow-y-auto p-5 lg:p-6",children:r.jsx("div",{className:"space-y-2 lg:space-y-3",children:M.map(((e,t)=>r.jsx("button",{onClick:()=>L(e.id),className:"w-full text-left p-3 lg:p-4 rounded-xl border transition-colors duration-150 hover:shadow-sm "+(w===e.id?"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 shadow-sm":"bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"),children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-7 h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center text-xs lg:text-sm font-bold "+(w===e.id?"bg-blue-600 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300"),children:t+1}),r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx("h3",{className:"font-semibold text-sm lg:text-base leading-tight "+(w===e.id?"text-blue-900 dark:text-blue-100":"text-gray-900 dark:text-gray-100"),children:e.title})}),r.jsx(m,{className:"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0 "+(w===e.id?"text-blue-600 dark:text-blue-400":"text-gray-400 dark:text-gray-500")})]})},e.id)))})})]})})]})},ve=new Map,we=3e5;let ke=null;const Ne=a.memo((()=>{const{categorySlug:e,subcategorySlug:t,topicSlug:a}=I(),[o,n]=s.useState(null),[i,m]=s.useState(null),[g,u]=s.useState(""),[h,p]=s.useState(null),[b,f]=s.useState(!0),[j,v]=s.useState(null),[w,N]=s.useState(!1),[C,S]=s.useState("conducts"),{toast:_}=l(),E=ie();s.useEffect((()=>{const e=e=>{const t=e.detail;t&&S(t)};return window.addEventListener("optimizedTabChange",e),()=>window.removeEventListener("optimizedTabChange",e)}),[]),s.useEffect((()=>{ke&&clearTimeout(ke);let r=!1;return(a||t)&&e&&(ke=setTimeout((()=>{(async()=>{if(!r)try{if(!e)throw new Error("categorySlug é obrigatório");const r=`summary_category_${e}`;let s,o;const l=window.__SUMMARY_CACHE__||new Map,i=l.get(r)||ve.get(r);if(i&&Date.now()-i.timestamp<we)s=i.data,o=i.error;else{const t=await x.from("pedbook_conducts_categories").select("id, name").eq("slug",e).maybeSingle();s=t.data,o=t.error,ve.set(r,{data:s,error:o,timestamp:Date.now()})}if(o)throw o;if(!s)throw new Error("Categoria não encontrada");p(s.name);const c=a||t,d=`topic-${s.id}-${c}`,g=l.get(d);let h,b;if(g&&Date.now()-g.timestamp<we)h=g.data,b=g.error;else{const e=await x.from("pedbook_conducts_topics").select("id, name").eq("category_id",s.id).eq("slug",c).single();h=e.data,b=e.error}if(b||!h)throw new Error("Tópico não encontrado");u(h.name);let y=null,j=null;const{data:v,error:w}=await x.from("pedbook_conducts_optimized").select("*").eq("topic_id",h.id).eq("published",!0).maybeSingle();if(v&&!w)y=v;else{const{data:e,error:t}=await x.from("pedbook_conducts_optimized").select("*").eq("topic_id",h.id).maybeSingle();y=e,j=t}if(y)return m(y),N(!0),void f(!1);const{data:k,error:C}=await x.from("pedbook_conducts_summaries").select("*").eq("topic_id",h.id).eq("published",!0).maybeSingle();if(C)throw C;if(!k)return void _({title:"Conteúdo não encontrado",description:"O resumo deste tópico ainda não está disponível."});let S="standard";if("simple"===k.format_type)S="simple";else if("standard"===k.format_type)S="standard";else{const e=k.content;(/<[^>]*>.*?##[\.;].*?<\/[^>]*>/g.test(e)||e.includes("##.")||e.includes("##;"))&&(S="simple")}let E,T=k.content;T.includes("<p>##. teste<br><br>##; teste</p>")?E=[{title:"teste",content:"",subsections:[{title:"teste",content:""}]}]:("simple"===S&&(T=M(T,"simple")),E=$("simple"===S?T:k.content));const q={...k,format_type:S,sections:E};n(q)}catch(s){_({title:"Erro ao carregar conteúdo",description:s.message||"Não foi possível carregar o conteúdo"})}finally{f(!1)}})()}),50)),()=>{r=!0,ke&&clearTimeout(ke)}}),[a,t,e,_]);const $=e=>{const t=[];return Array.from(e.matchAll(/(?:<h[23]><strong>##\.\s*(.*?)<\/strong><\/h[23]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>|<h2[^>]*>(.*?)<\/h2>)([\s\S]*?)(?=<h[23]><strong>##\.|<h2>##\.|<h2|$)/g)).forEach(((e,r)=>{const a=(e[1]||e[2]||e[3]||e[4]).trim();let s=e[5];s=s.replace(/(?:\n|^)##;\s*(.*?)(?::|;)?(?=\n|$)/g,"\n<h3><strong>##; $1</strong></h3>"),s=s.replace(/<h[34]>([^<]*?)(?::|;)?\s*<\/h[34]>/g,"<h3><strong>##; $1</strong></h3>");const o=Array.from(s.matchAll(/(?:<h[34]>(?:(?:<strong>)?##;\s*([^<]*?)(?::|;)?(?:<\/strong>)?|##;\s*([^<]*?)(?::|;)?)<\/h[34]>|<h3[^>]*>(.*?)<\/h3>)([\s\S]*?)(?=<h[34]>(?:(?:<strong>)?##;|<strong>)|<h[23]><strong>##\.|<h[23]|$)/g)),n=[];o.forEach(((e,t)=>{const r=(e=>{const t=document.createElement("textarea");return t.innerHTML=e,t.value})((e[1]||e[2]||e[3]).trim());let a=e[4];if(a&&(a=a.replace(/<\/?(?:h[34])>/g,"").replace(/^\s*(?:<br\s*\/?>\s*)*/,"").replace(/^[:\s;]+/,"").trim(),["h3","h4"].forEach((e=>{const t=a.indexOf(`</${e}>`);-1!==t&&(a=a.substring(t+5))})),a=a.trim()),n.push({title:r,content:a||""}),0===t){const t=s.indexOf(e[0]);-1!==t&&(s=s.substring(0,t).trim())}})),t.push({title:a,content:s.trim(),subsections:n})})),t},q=e=>{if(!e)return"";if(e.trim().startsWith("#"))return e;if(!e.trim().startsWith("{"))return e;try{const t=JSON.parse(e);if(!t.sections||!Array.isArray(t.sections))return e;let r="";return t.sections.forEach((e=>{e.title&&e.content&&(r+=`## ${e.title}\n\n`,r+=`${e.content}\n\n`)})),r.trim()}catch(t){return e}},M=(e,t="standard")=>{if(!e)return"";if("simple"===t){if(e.includes("<p>##. teste<br><br>##; teste</p>"))return'<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">teste</h2><h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">teste</h3>';let t=e.replace(/<p>##\.\s*(.*?)(?:<\/p>|<br>)/g,((e,t)=>`<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${t}</h2>`));return t=t.replace(/##\.\s*(.*?)(?=<br>|<\/p>|$)/g,((e,t)=>`<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${t}</h2>`)),t=t.replace(/<p>##;\s*(.*?)(?:<\/p>|<br>)/g,((e,t)=>`<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">${t}</h3>`)),t=t.replace(/##;\s*(.*?)(?=<br>|<\/p>|$)/g,((e,t)=>`<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">${t}</h3>`)),t}return e};return s.useEffect((()=>(window.handleImageClick=e=>{v(e)},()=>{delete window.handleImageClick})),[]),s.useEffect((()=>{if(E){const e=document.body.style.height,t=document.body.style.maxHeight,r=document.documentElement.style.height,a=document.documentElement.style.maxHeight,s=document.getElementById("root"),o=s?.style.height,n=s?.style.maxHeight,l=s?.style.overflow;return document.body.style.height="100vh",document.body.style.maxHeight="100vh",document.documentElement.style.height="100vh",document.documentElement.style.maxHeight="100vh",s&&(s.style.height="100vh",s.style.maxHeight="100vh",s.style.overflow="hidden"),()=>{document.body.style.height=e,document.body.style.maxHeight=t,document.documentElement.style.height=r,document.documentElement.style.maxHeight=a,s&&(s.style.height=o||"",s.style.maxHeight=n||"",s.style.overflow=l||"")}}}),[E]),b?r.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[r.jsx(k.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"w-12 h-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400 rounded-full"}),r.jsx("div",{className:"mt-4 text-center",children:r.jsx("p",{className:"text-gray-600",children:"Carregando resumo..."})})]}):r.jsxs("div",{className:"h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800 overflow-hidden",children:[r.jsx(ge,{name:g,description:o?.content,slug:a||"",categoryName:h,categorySlug:e}),r.jsx(c,{}),r.jsx("div",{className:"flex-shrink-0 w-full lg:hidden border-b border-gray-200/30 dark:border-gray-700/30 bg-gradient-to-r from-background/90 via-background/95 to-background/90 backdrop-blur-md shadow-sm",children:r.jsx("div",{className:"w-full px-4 py-3",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(L,{to:`/condutas-e-manejos/${e}`,children:r.jsx(d,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-xl hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 hover:scale-105",children:r.jsx(A,{className:"h-4 w-4"})})}),r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx("h1",{className:"text-lg font-semibold text-foreground truncate",children:g})}),w&&i&&r.jsx("div",{className:"lg:hidden",children:r.jsxs("div",{className:"flex bg-gradient-to-r from-gray-50 to-gray-50/80 dark:from-gray-800 dark:to-gray-800/80 rounded-xl p-0.5 gap-0.5 border border-gray-200/60 dark:border-gray-700/60 shadow-sm backdrop-blur-sm",children:[r.jsxs("button",{onClick:()=>{if("conducts"===C)return;const e=new CustomEvent("tabChange",{detail:"conducts"});window.dispatchEvent(e)},className:"flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 transform hover:scale-105 "+("conducts"===C?"bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md cursor-default":"hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-100/80 dark:hover:from-gray-700 dark:hover:to-gray-700/80 text-gray-700 dark:text-gray-200 cursor-pointer hover:shadow-sm"),disabled:"conducts"===C,children:[r.jsx("span",{className:"text-sm",children:"📋"}),r.jsx("span",{className:"hidden sm:inline",children:"Condutas"})]}),i.has_treatment&&r.jsxs("button",{onClick:()=>{if("treatment"===C)return;const e=new CustomEvent("tabChange",{detail:"treatment"});window.dispatchEvent(e)},className:"flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 transform hover:scale-105 "+("treatment"===C?"bg-gradient-to-r from-green-600 to-green-500 text-white shadow-md cursor-default":"hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-100/80 dark:hover:from-gray-700 dark:hover:to-gray-700/80 text-gray-700 dark:text-gray-200 cursor-pointer hover:shadow-sm"),disabled:"treatment"===C,children:[r.jsx("span",{className:"text-sm",children:"💊"}),r.jsx("span",{className:"hidden sm:inline",children:"Prescrição"})]}),(()=>{const e=i.conducts_content||"",t=i.treatment_content||"",r=/<ImageWithCaption[^>]*\/>/g,a=e.match(r)||[],s=t.match(r)||[];return a.length>0||s.length>0})()&&r.jsxs("button",{onClick:()=>{if("images"===C)return;const e=new CustomEvent("tabChange",{detail:"images"});window.dispatchEvent(e)},className:"flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 transform hover:scale-105 "+("images"===C?"bg-gradient-to-r from-purple-600 to-purple-500 text-white shadow-md cursor-default":"hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-100/80 dark:hover:from-gray-700 dark:hover:to-gray-700/80 text-gray-700 dark:text-gray-200 cursor-pointer hover:shadow-sm"),disabled:"images"===C,children:[r.jsx("span",{className:"text-sm",children:"🖼️"}),r.jsx("span",{className:"hidden sm:inline",children:"Imagens"})]})]})}),w&&i&&r.jsx("div",{className:"hidden lg:flex items-center justify-start flex-1 ml-16",children:r.jsxs("div",{className:"flex bg-gray-50 dark:bg-gray-800 rounded-lg p-1 gap-1 border border-gray-200 dark:border-gray-700",children:[r.jsxs("button",{onClick:()=>{if("conducts"===C)return;const e=new CustomEvent("tabChange",{detail:"conducts"});window.dispatchEvent(e)},className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all "+("conducts"===C?"bg-blue-600 text-white shadow-sm cursor-default":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer"),"data-tab-trigger":"conducts",disabled:"conducts"===C,children:[r.jsx("span",{children:"📋"}),r.jsx("span",{children:"Condutas"})]}),i.has_treatment&&r.jsxs("button",{onClick:()=>{if("treatment"===C)return;const e=new CustomEvent("tabChange",{detail:"treatment"});window.dispatchEvent(e)},className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all "+("treatment"===C?"bg-green-600 text-white shadow-sm cursor-default":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer"),"data-tab-trigger":"treatment",disabled:"treatment"===C,children:[r.jsx("span",{children:"💊"}),r.jsx("span",{children:"Prescrições"})]}),(()=>{const e=i.conducts_content||"",t=i.treatment_content||"",r=/<ImageWithCaption[^>]*\/>/g,a=e.match(r)||[],s=t.match(r)||[];return a.length>0||s.length>0})()&&r.jsxs("button",{onClick:()=>{if("images"===C)return;const e=new CustomEvent("tabChange",{detail:"images"});window.dispatchEvent(e)},className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all "+("images"===C?"bg-purple-600 text-white shadow-sm cursor-default":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer"),"data-tab-trigger":"images",disabled:"images"===C,children:[r.jsx("span",{children:"🖼️"}),r.jsx("span",{children:"Imagens"})]})]})})]})})}),r.jsx("main",{className:"flex-1 w-full px-0 lg:container lg:mx-auto lg:px-4 pb-0",children:r.jsxs("div",{className:"w-full lg:max-w-7xl lg:mx-auto h-full flex flex-col",children:[r.jsx("div",{className:"hidden lg:flex flex-shrink-0 px-4 pt-3 pb-2",children:r.jsx("div",{className:"w-full bg-gradient-to-r from-background/90 via-background/95 to-background/90 backdrop-blur-md shadow-sm border border-gray-200/30 dark:border-gray-700/30 rounded-2xl px-4 py-3",children:r.jsxs("div",{className:"flex items-center gap-3 w-full",children:[r.jsx(L,{to:`/condutas-e-manejos/${e}`,className:"flex-shrink-0",children:r.jsx(d,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-xl hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 hover:scale-105",children:r.jsx(A,{className:"h-4 w-4"})})}),r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx("h1",{className:"text-lg font-semibold text-foreground truncate",children:g})}),w&&i&&r.jsx("div",{className:"flex items-center justify-start flex-1 ml-16",children:r.jsxs("div",{className:"flex bg-gray-50 dark:bg-gray-800 rounded-lg p-1 gap-1 border border-gray-200 dark:border-gray-700",children:[r.jsxs("button",{onClick:()=>{if("conducts"===C)return;const e=new CustomEvent("tabChange",{detail:"conducts"});window.dispatchEvent(e)},className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all "+("conducts"===C?"bg-blue-600 text-white shadow-sm cursor-default":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer"),"data-tab-trigger":"conducts",disabled:"conducts"===C,children:[r.jsx("span",{children:"📋"}),r.jsx("span",{children:"Condutas"})]}),i.has_treatment&&r.jsxs("button",{onClick:()=>{if("treatment"===C)return;const e=new CustomEvent("tabChange",{detail:"treatment"});window.dispatchEvent(e)},className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all "+("treatment"===C?"bg-green-600 text-white shadow-sm cursor-default":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer"),"data-tab-trigger":"treatment",disabled:"treatment"===C,children:[r.jsx("span",{children:"💊"}),r.jsx("span",{children:"Prescrições"})]}),(()=>{const e=i.conducts_content||"",t=i.treatment_content||"",r=/<ImageWithCaption[^>]*\/>/g,a=e.match(r)||[],s=t.match(r)||[];return a.length>0||s.length>0})()&&r.jsxs("button",{onClick:()=>{if("images"===C)return;const e=new CustomEvent("tabChange",{detail:"images"});window.dispatchEvent(e)},className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all "+("images"===C?"bg-purple-600 text-white shadow-sm cursor-default":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer"),"data-tab-trigger":"images",disabled:"images"===C,children:[r.jsx("span",{children:"🖼️"}),r.jsx("span",{children:"Imagens"})]})]})})]})})}),r.jsx("div",{className:"flex-1",children:w&&i?r.jsx(je,{title:g,conductsContent:q(i.conducts_content),treatmentContent:q(i.treatment_content),hasTreatment:i.has_treatment}):o?r.jsx("div",{className:"h-full bg-white flex flex-col overflow-hidden",children:r.jsx(k.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"flex-1 overflow-y-auto px-4 lg:p-4 py-4",children:r.jsx("div",{className:"space-y-4",children:r.jsxs(y,{className:"bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-lg border-blue-100 dark:border-slate-700",children:[r.jsx(P,{type:"multiple",className:"space-y-4",children:o.sections.map(((e,t)=>r.jsxs(O,{value:`section-${t}`,className:"rounded-xl overflow-hidden border border-blue-100 dark:border-gray-700 bg-white/80 backdrop-blur-sm shadow-md transition-all hover:shadow-lg",children:[r.jsx(H,{className:"px-6 py-4 hover:bg-blue-50/50 data-[state=open]:bg-blue-50/80 dark:hover:bg-blue-900/20 transition-colors duration-300",children:r.jsxs("div",{className:"flex items-center gap-3 text-left",children:[r.jsx("span",{className:"flex items-center justify-center rounded-full bg-primary text-white p-2",children:r.jsx(T,{className:"h-5 w-5"})}),r.jsxs("div",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:e.title}),e.subsections.length>0&&r.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[e.subsections.length," ",1===e.subsections.length?"subtópico":"subtópicos"]})]})]})}),r.jsx(W,{className:"px-6 pt-3 pb-6 bg-white dark:bg-slate-800",children:r.jsxs("div",{children:[e.content&&r.jsx(r.Fragment,{children:r.jsx("div",{className:"prose max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300",dangerouslySetInnerHTML:{__html:M(e.content,o?.format_type)}})}),e.subsections.length>0&&r.jsx(P,{type:"multiple",className:"space-y-2 mt-4",children:e.subsections.map(((e,a)=>r.jsxs(O,{value:`subsection-${t}-${a}`,className:"border border-blue-100 dark:border-slate-700 rounded-lg overflow-hidden",children:[r.jsx(H,{className:"px-6 py-3 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors",children:r.jsxs("div",{className:"flex items-center gap-2 text-left",children:[r.jsxs("span",{className:"flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs",children:[t+1,".",a+1]}),r.jsx("h3",{className:"text-lg font-medium text-blue-700 dark:text-blue-300",children:e.title})]})}),r.jsx(W,{className:"px-6 pt-2 pb-4 bg-blue-50/30 dark:bg-blue-900/10",children:r.jsx("div",{children:r.jsx(r.Fragment,{children:r.jsx("div",{className:"prose max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300",dangerouslySetInnerHTML:{__html:M(e.content,o?.format_type)}})})})})]},a)))})]})})]},t)))}),r.jsx("div",{className:"px-6 py-8",children:r.jsx(he,{summaryId:o.id,summaryTitle:o.title})})]})})})}):r.jsxs(k.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},className:"text-center py-12",children:[r.jsx("h3",{className:"text-xl font-medium text-gray-600 dark:text-gray-300",children:"Nenhum resumo encontrado para este tópico"}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400 mt-2",children:"O conteúdo pode estar em desenvolvimento ou não publicado."})]})})]})}),r.jsx(xe,{isOpen:!!j,onClose:()=>v(null),imageUrl:j||"",alt:"Imagem do resumo"})]})}));Ne.displayName="ConductsSummary";const Ce=a.memo((()=>{const{categorySlug:e,subcategorySlug:t,topicSlug:s}=I(),{showSummary:n,isLoading:l,reason:i}=((e,t,r)=>{a.useEffect((()=>{de.logEffect("ConductsRouter",[e,t,r],"PARAMS_CHANGED")}),[e,t,r]);const s=a.useMemo((()=>r?{showSummary:!0,reason:"3_PARAMS"}:t?{showSummary:null,reason:"2_PARAMS_CHECK_NEEDED"}:{showSummary:!1,reason:"1_PARAM"}),[e,t,r]),{data:n,isLoading:l}=o({queryKey:["conduct-topic-check",e,t],queryFn:async()=>{if(!e||!t)return null;de.logRequest("ConductsRouter",`topic-check-${e}-${t}`);const{data:r,error:a}=await x.from("pedbook_conducts_categories").select("id, name").eq("slug",e).single();if(a||!r)throw new Error("Category not found");const{data:s,error:o}=await x.from("pedbook_conducts_topics").select("id, name, is_subcategory").eq("category_id",r.id).eq("slug",t).maybeSingle();return s},enabled:null===s.showSummary,staleTime:3e5,gcTime:6e5});return{showSummary:a.useMemo((()=>null!==s.showSummary?s.showSummary:l?null:!n?.is_subcategory),[s.showSummary,l,n]),isLoading:l,reason:s.reason}})(e,t,s);return de.logRender("ConductsRouter",{categorySlug:e,subcategorySlug:t,topicSlug:s,showSummary:n,isLoading:l,reason:i}),l||null===n?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(h,{className:"w-8 h-8 animate-spin text-blue-500"})}):n?r.jsx(Ne,{}):r.jsx(me,{})}));Ce.displayName="ConductsRouterNew";export{Ce as default};
