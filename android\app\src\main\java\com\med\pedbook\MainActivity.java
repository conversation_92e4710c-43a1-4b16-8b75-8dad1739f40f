package com.med.pedbook;

import android.os.Bundle;
import android.view.View;

import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Garantir que as barras do sistema fiquem visíveis
        mostrarBarras();
    }

    private void mostrarBarras() {
        View decorView = getWindow().getDecorView();

        // Usar comportamento padrão - sem flags especiais
        // Isso garante que o conteúdo NÃO vá por trás das barras
        decorView.setSystemUiVisibility(0);

        // ESSENCIAL: Garantir que o conteúdo respeite as barras do sistema
        decorView.setFitsSystemWindows(true);
    }


}
