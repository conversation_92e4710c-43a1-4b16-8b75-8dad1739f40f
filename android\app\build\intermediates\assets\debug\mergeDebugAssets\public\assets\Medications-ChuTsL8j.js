import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-DoG78xzO.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-DIVKaOkK.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-BDHDCXV0.js";import"./FeedbackTrigger-Bbh1ZkR8.js";import"./rocket-coAqOqu0.js";import"./target-JnessAn2.js";import"./zap-cv85ZGQL.js";import"./book-open-HpWivqt7.js";import"./star-D6Dh7eqH.js";import"./circle-help-DamQHiY3.js";import"./instagram-C9X9J-Hm.js";import"./collapsible-B6HfSnGs.js";import"./accordion-Cgsgrotl.js";import"./PatientInfoSection-DOt-4I2a.js";import"./scale-DL7R6jao.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-Ct4wKNFN.js";import"./user-BD69V0eQ.js";import"./DosageDisplay-D3W0Iil9.js";import"./alert-B7PEwKmg.js";import"./plus-C0NJ4ejE.js";import"./lightbulb-DfqZ7Con.js";import"./external-link-DDwnfNtx.js";import"./stethoscope-CJh6EjrS.js";import"./syringe-TUxzgApY.js";import"./wind-CxMiWv3B.js";import"./bug-BseOIgdd.js";import"./chevron-left-DVcojMFg.js";import"./house-BZF2AwBB.js";function t(){return r.jsx(o,{})}export{t as default};
