import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as s,u as i}from"./query-vendor-B-7l6Nb3.js";import{d as r,an as d,a5 as o,T as n,B as t,s as c,D as l,e as m,f as u,g as x,aM as h,ad as j,ae as p,af as f,ag as v,ai as g,aq as y,ar as N,as as _,at as b,z as k}from"./index-CR7o3nEo.js";import{D as C,a as w}from"./DosageList-CqGQrc7e.js";import{A as I,a as q,b as S,c as E,d as D,e as O,f as A,g as z}from"./alert-dialog-DjBGocRG.js";import{P as F}from"./pencil-BmDCKKnl.js";import{T as K}from"./trash-2-BHlncC12.js";import{P}from"./plus-C7-Pt48y.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./switch-BlCL6T7n.js";function Q({medicationId:i,useCase:l,onSuccess:m,onCancel:u}){const[x,h]=a.useState(l?.name||""),[j,p]=a.useState(l?.description||""),[f,v]=a.useState(l?.display_order||0),{toast:g}=r(),y=s();return a.useEffect((()=>{l&&(h(l.name),p(l.description||""),v(l.display_order||0))}),[l]),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(l){const{error:e}=await c.from("pedbook_medication_use_cases").update({name:x,description:j,display_order:f}).eq("id",l.id);if(e)throw e;g({title:"Indicação atualizada com sucesso!",description:"As alterações foram salvas."})}else{const{data:e,error:a}=await c.from("pedbook_medication_use_cases").select("display_order").eq("medication_id",i).order("display_order",{ascending:!1}).limit(1);if(a)throw a;const s=e&&e[0]?e[0].display_order+1:1,{error:r}=await c.from("pedbook_medication_use_cases").insert({medication_id:i,name:x,description:j,display_order:s});if(r)throw r;g({title:"Indicação criada com sucesso!",description:"A nova indicação de uso foi adicionada."})}y.invalidateQueries({queryKey:["medication-use-cases"]}),m?.()}catch(a){g({variant:"destructive",title:l?"Erro ao atualizar indicação":"Erro ao criar indicação",description:a.message})}},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"name",children:"Nome da Indicação"}),e.jsx(o,{id:"name",value:x,onChange:e=>h(e.target.value),placeholder:"Ex: Escabiose",required:!0})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"description",children:"Descrição"}),e.jsx(n,{id:"description",value:j,onChange:e=>p(e.target.value),placeholder:"Descreva a indicação de uso..."})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"displayOrder",children:"Ordem de Exibição"}),e.jsx(o,{id:"displayOrder",type:"number",min:"0",value:f,onChange:e=>v(parseInt(e.target.value)),placeholder:"Ordem de exibição"})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[u&&e.jsx(t,{type:"button",variant:"outline",onClick:u,children:"Cancelar"}),e.jsx(t,{type:"submit",children:l?"Salvar Alterações":"Criar Indicação"})]})]})}function T({medicationId:d}){const[o,n]=a.useState(null),[h,j]=a.useState(null),{toast:p}=r(),f=s(),{data:v}=i({queryKey:["medication-use-cases",d],queryFn:async()=>{const{data:e,error:a}=await c.from("pedbook_medication_use_cases").select("*").eq("medication_id",d).order("name");if(a)throw a;return e}});return v?.length?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:v.map((a=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 space-y-2 flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:a.name}),a.description&&e.jsx("p",{className:"text-sm text-gray-600",children:a.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(t,{variant:"ghost",size:"icon",onClick:()=>j(a),className:"hover:bg-primary/10",children:e.jsx(F,{className:"h-4 w-4"})}),e.jsx(t,{variant:"ghost",size:"icon",onClick:()=>n(a.id),className:"hover:bg-destructive/10",children:e.jsx(K,{className:"h-4 w-4"})})]})]},a.id)))}),e.jsx(I,{open:!!o,onOpenChange:()=>n(null),children:e.jsxs(q,{children:[e.jsxs(S,{children:[e.jsx(E,{children:"Confirmar exclusão"}),e.jsx(D,{children:"Tem certeza que deseja excluir esta indicação de uso? Esta ação não pode ser desfeita. As dosagens associadas a esta indicação serão mantidas, mas não estarão mais vinculadas a esta indicação."})]}),e.jsxs(O,{children:[e.jsx(A,{children:"Cancelar"}),e.jsx(z,{onClick:async()=>{if(o)try{const{error:e}=await c.from("pedbook_medication_dosages").update({use_case_id:null}).eq("use_case_id",o);if(e)throw e;const{error:a}=await c.from("pedbook_medication_use_cases").delete().eq("id",o);if(a)throw a;p({title:"Indicação excluída com sucesso",description:"A indicação de uso foi removida."}),f.invalidateQueries({queryKey:["medication-use-cases"]}),f.invalidateQueries({queryKey:["medication-dosages"]}),n(null)}catch(e){p({variant:"destructive",title:"Erro ao excluir indicação",description:e.message})}},children:"Confirmar"})]})]})}),e.jsx(l,{open:!!h,onOpenChange:()=>j(null),children:e.jsxs(m,{children:[e.jsx(u,{children:e.jsx(x,{children:"Editar Indicação de Uso"})}),h&&e.jsx(Q,{medicationId:d,useCase:h,onSuccess:()=>j(null),onCancel:()=>j(null)})]})})]}):e.jsx("div",{className:"text-center text-gray-500 py-8",children:"Nenhuma indicação de uso cadastrada para este medicamento"})}function U(){const[s,r]=a.useState(!1),[d,o]=a.useState(!1),[n,I]=a.useState(null),[q,S]=a.useState(""),{data:E}=i({queryKey:["medications"],queryFn:async()=>{const{data:e,error:a}=await c.from("pedbook_medications").select("id, name").order("name");if(a)throw a;return e}});return e.jsxs("div",{className:h.pageBackground("container mx-auto py-8"),children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold dark:text-gray-100",children:"Gerenciar Dosagens"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-64",children:e.jsxs(j,{value:q,onValueChange:S,children:[e.jsx(p,{className:h.select(),children:e.jsx(f,{placeholder:"Selecione um medicamento"})}),e.jsx(v,{children:E?.map((a=>e.jsx(g,{value:a.id,children:a.name},a.id)))})]})}),e.jsxs(t,{onClick:()=>{q&&r(!0)},disabled:!q,children:[e.jsx(P,{className:"h-4 w-4 mr-2"}),"Nova Dosagem"]}),e.jsxs(t,{onClick:()=>{q&&o(!0)},disabled:!q,variant:"secondary",children:[e.jsx(P,{className:"h-4 w-4 mr-2"}),"Nova Indicação"]})]})]}),q&&e.jsxs(y,{defaultValue:"dosages",className:"space-y-4",children:[e.jsxs(N,{children:[e.jsx(_,{value:"dosages",children:"Dosagens"}),e.jsx(_,{value:"usecases",children:"Indicações de Uso"})]}),e.jsx(b,{value:"dosages",children:e.jsx(C,{selectedMedicationId:q,onEdit:e=>{I(e),r(!0)}})}),e.jsx(b,{value:"usecases",children:e.jsx(T,{medicationId:q})})]}),e.jsx(l,{open:s,onOpenChange:e=>{e||(r(!1),I(null))},children:e.jsxs(m,{className:"dialog-content-70 dark:bg-slate-800 dark:border-slate-700",children:[e.jsxs(u,{children:[e.jsx(x,{className:"dark:text-gray-100",children:n?"Editar Dosagem":"Nova Dosagem"}),e.jsx(k,{className:"dark:text-gray-400",children:n?"Edite as informações da dosagem":"Preencha as informações da nova dosagem"})]}),q&&e.jsx(w,{medicationId:q,dosage:n,onSuccess:()=>{r(!1),I(null)},onCancel:()=>{r(!1),I(null)}})]})}),e.jsx(l,{open:d,onOpenChange:o,children:e.jsxs(m,{className:"dark:bg-slate-800 dark:border-slate-700",children:[e.jsxs(u,{children:[e.jsx(x,{className:"dark:text-gray-100",children:"Nova Indicação de Uso"}),e.jsx(k,{className:"dark:text-gray-400",children:"Adicione uma nova indicação de uso para este medicamento"})]}),q&&e.jsx(Q,{medicationId:q,onSuccess:()=>o(!1),onCancel:()=>o(!1)})]})})]})}export{U as default};
