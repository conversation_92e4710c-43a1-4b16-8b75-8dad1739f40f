import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-DIv01s8x.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-CFFY2EZF.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-Cy0ETRb8.js";import"./FeedbackTrigger-D3WjakYT.js";import"./rocket-CXfOzAv0.js";import"./target-BR2vVj-t.js";import"./zap-DiDsx3f0.js";import"./book-open-DfMTUXW0.js";import"./star-D6ndiBmz.js";import"./circle-help-8_s38d5r.js";import"./instagram-CMzOelh-.js";import"./collapsible-B6HfSnGs.js";import"./accordion-Cl5OLBW2.js";import"./PatientInfoSection-5JOorJ0q.js";import"./scale-BQZyeNkz.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-BzRFPDjE.js";import"./user-DTxhJiBv.js";import"./DosageDisplay-C-bhu6S2.js";import"./alert-VzAM23dx.js";import"./plus-D_N5TOws.js";import"./lightbulb-DIs8y-cY.js";import"./external-link-CtIRWxnM.js";import"./stethoscope-ChLTja_o.js";import"./syringe-DAzry6Ak.js";import"./wind-BHVYaGHp.js";import"./bug-ClSSXhR5.js";import"./chevron-left-DTv-nKMo.js";import"./house-Ds-j6AyJ.js";function t(){return r.jsx(o,{})}export{t as default};
