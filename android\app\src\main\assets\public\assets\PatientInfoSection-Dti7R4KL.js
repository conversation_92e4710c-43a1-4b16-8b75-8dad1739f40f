import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{ao as s,a6 as r,al as t,ae as o,af as n,ag as i,ah as m,aj as d}from"./index-D89HBjcn.js";import{S as l}from"./scale-C0VY02RJ.js";import{u as c}from"./useAgeInput-CLfpowYq.js";import{C as x}from"./calendar-Bey2EULq.js";import{U as g}from"./user-B_6Qs7t0.js";const h=({value:o,onChange:n,onCommit:i})=>{const[m,d]=a.useState(o.toString()),[c,x]=a.useState(!1),[g,h]=a.useState(""),u=200;a.useEffect((()=>{d(o.toString())}),[o]);const p=e=>{h(e),x(!0),setTimeout((()=>x(!1)),5e3)};return e.jsxs("div",{className:"space-y-2 w-full",children:[e.jsxs(s,{htmlFor:"weight",className:"text-sm font-medium text-gray-700 dark:text-gray-200 flex items-center gap-2",children:[e.jsx(l,{className:"h-4 w-4"}),"Peso do Paciente"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{id:"weight",type:"number",min:1,max:u,step:"0.1",value:m,onChange:e=>{const a=e.target.value;d(a);const s=parseFloat(a);if(!isNaN(s))if(s>u){p("Peso máximo aceito é 200 kg. Para pacientes acima disso, calcule as doses usando o teto absoluto seguro para cada fármaco.");const e=u;d(e.toString()),n(e),i(e)}else s<1&&s>0?(p("Peso mínimo recomendado é 1 kg (prematuros extremos). Abaixo disso, personalize manualmente as doses."),n(s),i(s)):s>=1&&s<=u&&(n(s),i(s))},onBlur:()=>{const e=parseFloat(m);isNaN(e)||e<0?(d(1..toString()),n(1),i(1)):e>u?(p("Peso máximo aceito é 200 kg. Para pacientes acima disso, calcule as doses usando o teto absoluto seguro para cada fármaco."),d(u.toString()),n(u),i(u)):e<1?(p("Peso mínimo recomendado é 1 kg (prematuros extremos). Abaixo disso, personalize manualmente as doses."),d(1..toString()),n(1),i(1)):(d(e.toString()),n(e),i(e))},className:"flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center",placeholder:"1-200 kg"}),e.jsx("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-300 min-w-[2rem]",children:"kg"})]}),c&&e.jsxs("div",{className:"flex items-start gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md",children:[e.jsx(t,{className:"h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-amber-800 dark:text-amber-200 leading-relaxed",children:g})]})]})},u=({ageInMonths:l,onChange:g,onCommit:h,showMonths:u=!0,labelColor:p="text-gray-700 dark:text-gray-200"})=>{const[b,j]=a.useState(!1),[f,N]=a.useState(""),{unit:w,inputValue:y,handleUnitChange:k,handleChange:v,handleBlur:C}=c({ageInMonths:l,onChange:g,onCommit:h,onValidationError:e=>{N(e),j(!0),setTimeout((()=>j(!1)),5e3)}});return e.jsxs("div",{className:"space-y-2 w-full",children:[e.jsxs(s,{htmlFor:"age",className:`text-sm font-medium ${p} flex items-center gap-2`,children:[e.jsx(x,{className:"h-4 w-4"}),"Idade do paciente"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{id:"age",type:"number",min:0,max:"years"===w?120:12,step:"1",value:y,onChange:e=>v(e.target.value),onBlur:C,className:"flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center",placeholder:"years"===w?"0-120 anos":"0-12 meses"}),u?e.jsxs(o,{value:w,onValueChange:e=>k(e),children:[e.jsx(n,{className:"w-[110px] bg-white/50 border-primary/20","aria-label":"Selecionar unidade de idade (meses ou anos)",children:e.jsx(i,{})}),e.jsxs(m,{children:[e.jsx(d,{value:"months",children:"meses"}),e.jsx(d,{value:"years",children:"anos"})]})]}):e.jsx("div",{className:"w-[110px] px-4 py-2 bg-white/50 border border-primary/20 rounded-md text-sm text-center text-primary/80",children:"anos"})]}),b&&e.jsxs("div",{className:"flex items-start gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:[e.jsx(t,{className:"h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-sm text-red-800 dark:text-red-200 leading-relaxed",children:f})]})]})},p=({weight:a,onWeightChange:s,onWeightCommit:r,age:t,onAgeChange:o,onAgeCommit:n,requiredMeasures:i=["weight","age"]})=>{if(0===i.length)return null;const m=i.length;return e.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm",children:e.jsxs("div",{className:"p-3 sm:p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(g,{className:"w-4 h-4 text-primary"}),e.jsx("h3",{className:"text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100",children:"Dados do Paciente"})]}),e.jsxs("div",{className:`grid ${1===m?"place-items-center":"grid-cols-2"} gap-2 sm:gap-4`,children:[i.includes("weight")&&e.jsx("div",{className:1===m?"w-full max-w-md":"w-full",children:e.jsx(h,{value:a,onChange:s,onCommit:r})}),i.includes("age")&&e.jsx("div",{className:1===m?"w-full max-w-md":"w-full",children:e.jsx(u,{ageInMonths:t,onChange:o,onCommit:n})})]})]})})};export{p as P};
