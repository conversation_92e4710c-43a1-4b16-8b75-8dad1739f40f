import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{ao as s,a6 as r,B as t,d as i,s as o,U as c,ar as n,as as d,at as l,au as m,X as p,Y as u,_ as h,a0 as x,V as j}from"./index-CNG-Xj2g.js";import{P as v}from"./plus-5xdDX9-5.js";import{Q as g}from"./QuestionImport-DIaWV8ce.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const f=({selectedType:i,selectedParentId:o,onAddCategory:c,onParentChange:n,getParentOptions:d})=>{const[l,m]=a.useState("");return e.jsxs("div",{children:[e.jsx(s,{children:"Nome da nova categoria"}),e.jsxs("div",{className:"flex gap-2 mt-1.5",children:[e.jsx(r,{placeholder:"Nome da nova categoria",value:l,onChange:e=>m(e.target.value)}),"specialty"!==i&&e.jsxs("select",{className:"border rounded p-2 min-w-[200px] hover:border-primary/50 transition-colors",onChange:e=>n(e.target.value),value:o,required:!0,children:[e.jsxs("option",{value:"",children:["Selecione ","theme"===i?"a especialidade":"o tema"]}),d().map((a=>e.jsx("option",{value:a.id,children:a.name},a.id)))]}),e.jsxs(t,{onClick:()=>{c(l),m("")},className:"whitespace-nowrap hover:animate-scale transition-all",children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Adicionar"]})]})]})},y=({categories:a,selectedType:s})=>e.jsx("div",{className:"border rounded-lg p-4 space-y-2",children:0===a.length?e.jsx("p",{className:"text-gray-500 text-center py-4",children:"Nenhuma categoria cadastrada"}):a.filter((e=>e.type===s)).map((s=>e.jsxs("div",{className:"flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg border hover:border-primary/50 transition-all",children:[e.jsx("span",{className:"font-medium",children:s.name}),s.parentId&&e.jsxs("span",{className:"text-sm text-gray-500",children:["↳ ",a.find((e=>e.id===s.parentId))?.name]})]},s.id)))}),N=()=>{const{toast:s}=i(),[r,t]=a.useState([]),[p,u]=a.useState("specialty"),[h,x]=a.useState();a.useEffect((()=>{j()}),[]);const j=async()=>{try{const{data:e,error:a}=await o.from("study_categories").select("*");if(a)throw a;const s=e.map((e=>({id:e.id,name:e.name,type:e.type,parentId:e.parent_id})));t(s)}catch(e){s({title:"Erro ao carregar categorias",description:e.message,variant:"destructive"})}};return e.jsx(c,{className:"p-6 space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:"Gerenciar Categorias"}),e.jsxs(n,{value:p,onValueChange:e=>{u(e),x(void 0)},children:[e.jsxs(d,{className:"w-full",children:[e.jsx(l,{value:"specialty",className:"flex-1",children:"Especialidades"}),e.jsx(l,{value:"theme",className:"flex-1",children:"Temas"}),e.jsx(l,{value:"focus",className:"flex-1",children:"Focos"})]}),e.jsx(m,{value:p,className:"space-y-4 mt-4",children:e.jsxs("div",{className:"grid gap-4",children:[e.jsx(f,{selectedType:p,selectedParentId:h,onAddCategory:async e=>{if(e.trim())try{const a=h;if("theme"===p&&!a)return void s({title:"Erro",description:"Selecione uma especialidade para o tema",variant:"destructive"});if("focus"===p&&!a)return void s({title:"Erro",description:"Selecione um tema para o foco",variant:"destructive"});const{data:r,error:i}=await o.from("study_categories").insert({name:e,type:p,parent_id:a}).select().single();if(i)throw i;const c={id:r.id,name:r.name,type:r.type,parentId:r.parent_id};t((e=>[...e,c])),s({title:"Sucesso",description:"Categoria adicionada com sucesso!"})}catch(a){s({title:"Erro ao adicionar categoria",description:a.message,variant:"destructive"})}else s({title:"Erro",description:"O nome da categoria não pode estar vazio",variant:"destructive"})},onParentChange:x,getParentOptions:()=>{switch(p){case"theme":return r.filter((e=>"specialty"===e.type));case"focus":return r.filter((e=>"theme"===e.type));default:return[]}}}),e.jsx(y,{categories:r,selectedType:p})]})})]})]})})},w=()=>{const{toast:s}=i(),[c,n]=a.useState([]),[d,l]=a.useState("");a.useEffect((()=>{m()}),[]);const m=async()=>{try{const{data:e,error:a}=await o.from("exam_locations").select("*");if(a)throw a;n(e)}catch(e){s({title:"Erro ao carregar locais",description:e.message,variant:"destructive"})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Gerenciar Locais de Prova"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(r,{placeholder:"Nome do local de prova",value:d,onChange:e=>l(e.target.value)}),e.jsxs(t,{onClick:async()=>{if(d.trim())try{const{data:e,error:a}=await o.from("exam_locations").insert({name:d}).select().single();if(a)throw a;n((a=>[...a,e])),l(""),s({title:"Sucesso",description:"Local adicionado com sucesso!"})}catch(e){s({title:"Erro ao adicionar local",description:e.message,variant:"destructive"})}else s({title:"Erro",description:"O nome do local não pode estar vazio",variant:"destructive"})},children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Adicionar"]})]}),e.jsx("div",{className:"border rounded-lg p-4",children:c.map((a=>e.jsxs("div",{className:"flex items-center justify-between p-2 hover:bg-gray-100 rounded",children:[e.jsx("span",{children:a.name}),e.jsx(t,{variant:"ghost",size:"sm",onClick:()=>(async e=>{try{const{error:a}=await o.from("exam_locations").delete().eq("id",e);if(a)throw a;n((a=>a.filter((a=>a.id!==e)))),s({title:"Sucesso",description:"Local removido com sucesso!"})}catch(a){s({title:"Erro ao remover local",description:a.message,variant:"destructive"})}})(a.id),children:e.jsx(p,{className:"h-4 w-4"})})]},a.id)))})]})},b=()=>e.jsx("div",{className:"container py-8",children:e.jsxs(c,{children:[e.jsxs(u,{children:[e.jsx(h,{children:"Configurações do Sistema"}),e.jsx(x,{children:"Gerencie categorias, locais de prova e outras configurações do sistema"})]}),e.jsx(j,{children:e.jsxs(n,{defaultValue:"categories",children:[e.jsxs(d,{children:[e.jsx(l,{value:"categories",children:"Categorias"}),e.jsx(l,{value:"locations",children:"Locais de Prova"}),e.jsx(l,{value:"import",children:"Importar Questões"})]}),e.jsx(m,{value:"categories",children:e.jsx(N,{})}),e.jsx(m,{value:"locations",children:e.jsx(w,{})}),e.jsx(m,{value:"import",children:e.jsx(g,{})})]})})]})});export{b as default};
