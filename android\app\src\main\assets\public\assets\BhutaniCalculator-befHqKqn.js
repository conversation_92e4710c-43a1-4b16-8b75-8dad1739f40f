import{j as e}from"./radix-core-6kBL75b5.js";import{B as a,a8 as r,U as s,ao as t,a6 as i,aA as o,ab as n}from"./index-DQuOk0R3.js";import d from"./Footer-BmzagX2Z.js";import{a as l}from"./router-BAzpOxbo.js";import{r as c}from"./critical-DVX9Inzy.js";import{C as m,r as u}from"./chart-js-B1VMWqNt.js";import{C as x,a as p}from"./calculatorSEOData-DKBtSf92.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-Bwp4oswe.js";import"./rocket-Czd-at64.js";import"./target-CuA2iBUH.js";import"./zap-B06nQ-rd.js";import"./book-open-gHhE7Hhk.js";import"./star-CaqDe8as.js";import"./circle-help-CTIYt4iy.js";import"./instagram-DOFGyRt3.js";function h(){const s=l();return e.jsxs("div",{className:"max-w-3xl mx-auto",children:[e.jsxs(a,{variant:"ghost",className:"text-primary hover:text-primary/80 transition-colors w-fit mb-4 flex items-center gap-2",onClick:()=>s("/calculadoras"),children:[e.jsx(r,{className:"h-4 w-4"}),"Voltar para calculadoras"]}),e.jsxs("div",{className:"text-center space-y-4 mt-4",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-primary",children:"Nomograma de Bhutani"}),e.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"O Nomograma de Bhutani é uma ferramenta para avaliação do risco de hiperbilirrubinemia significativa em recém-nascidos."})]})]})}m.register(...u);const j=()=>{const r=c.useRef(null),n=c.useRef(null),[d,l]=c.useState(""),[u,x]=c.useState(!1),p=[4,5,7,8.5,9.5,11.1,11.6,12.5,13,13.3,13.2,13.1],h=[5,6,9,10.9,12.8,13.6,14.8,15.2,15.6,15.9,15.5,15.2],j=[7,8,11,13.1,15.1,16,16.8,17.2,17.2,17.3,17.2,17.1],b=e=>{const a=[];for(let r=12;r<=144;r++){const s=(r-12)/12;if(Number.isInteger(s)&&s<e.length)a.push(e[s]);else{const r=Math.floor(s),t=Math.ceil(s);if(t<e.length){const i=s-r;a.push(e[r]*(1-i)+e[t]*i)}else a.push(e[e.length-1])}}return a};return c.useEffect((()=>{if(r.current){const e=r.current.getContext("2d");e&&(n.current&&n.current.destroy(),n.current=new m(e,{type:"line",data:{labels:Array.from({length:133},((e,a)=>a+12)),datasets:[{label:"P40 (mg/dL)",data:b(p),borderColor:"blue",borderWidth:1,fill:!1,tension:.1,pointRadius:0},{label:"P75 (mg/dL)",data:b(h),borderColor:"green",borderWidth:1,fill:!1,tension:.1,pointRadius:0},{label:"P95 (mg/dL)",data:b(j),borderColor:"red",borderWidth:1,fill:!1,tension:.1,pointRadius:0},{label:"Bilirrubina Total (mg/dL)",data:[],borderColor:"orange",pointBackgroundColor:"orange",borderWidth:2,fill:!1,pointRadius:5,showLine:u}]},options:{responsive:!0,maintainAspectRatio:!1,scales:{x:{type:"linear",position:"bottom",min:12,max:144,ticks:{stepSize:12}},y:{beginAtZero:!0,max:20}}}}))}return()=>{n.current&&n.current.destroy()}}),[u]),e.jsxs(s,{className:"p-6 space-y-6",children:[e.jsxs("form",{onSubmit:e=>{e.preventDefault();const a=new FormData(e.target),r=parseFloat(a.get("age")),s=parseFloat(a.get("bilirubin"));if(r<12||r>144||isNaN(s))alert("Idade deve estar entre 12 e 144 horas e bilirrubina deve ser válida.");else if(n.current){const a=n.current,t=[...a.data.datasets[3].data];u||(t.length=0),t.push({x:r,y:s}),t.sort(((e,a)=>e.x-a.x)),a.data.datasets[3].data=t,a.update();const i=Math.floor(r-12),o=b(p)[i],d=b(h)[i];let c="";c=s>b(j)[i]?"Zona de Alto Risco":s>d?"Zona de Risco Intermediário Alto":s>o?"Zona de Risco Intermediário Baixo":"Zona de Baixo Risco",l(c),u||e.target.reset()}},className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"age",children:"Idade (horas)"}),e.jsx(i,{id:"age",name:"age",type:"number",min:"12",max:"144",step:"1",required:!0,placeholder:"Entre 12 e 144 horas"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"bilirubin",children:"Bilirrubina Total (mg/dL)"}),e.jsx(i,{id:"bilirubin",name:"bilirubin",type:"number",step:"0.1",required:!0,placeholder:"Valor da bilirrubina"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{id:"continuousEntry",checked:u,onCheckedChange:e=>x(e)}),e.jsx(t,{htmlFor:"continuousEntry",children:"Entrada contínua de dados"})]}),e.jsxs("div",{className:"flex gap-2 justify-center",children:[e.jsx(a,{type:"submit",className:"bg-primary",children:"Adicionar ponto"}),e.jsx(a,{type:"button",variant:"outline",onClick:()=>{n.current&&(n.current.data.datasets[3].data=[],n.current.update(),l(""))},children:"Limpar gráfico"})]})]}),e.jsxs("div",{className:"h-[400px] md:h-[300px]",children:[" ",e.jsx("canvas",{ref:r})]}),d&&e.jsxs("div",{className:"text-center font-semibold text-lg",children:[e.jsx("span",{className:`\n            ${d.includes("Alto Risco")?"text-red-600":""}\n            ${d.includes("Intermediário Alto")?"text-orange-600":""}\n            ${d.includes("Intermediário Baixo")?"text-yellow-600":""}\n            ${d.includes("Baixo Risco")?"text-green-600":""}\n          `,children:d}),d.includes("Alto Risco")&&e.jsx("div",{className:"text-red-600 mt-2",children:"Conduta: Iniciar fototerapia imediatamente. Reavaliar BT em 4 a 6 horas. OBS: Se BT permanecer em nível crítico considere exsanguineotransfusão em casos de progressão ou sinais de kernicterus."}),d.includes("Intermediário Alto")&&e.jsx("div",{className:"text-orange-600 mt-2",children:"Conduta: Monitorar BT sérica em 12 a 24 horas. Considerar fototerapia dependendo da idade gestacional e fatores de risco associados."}),d.includes("Intermediário Baixo")&&e.jsx("div",{className:"text-yellow-600 mt-2",children:"Conduta: Acompanhar clinicamente. Monitorar BT dependendo da evolução clínica e fatores de risco. Garantir aleitamento materno eficaz."}),d.includes("Baixo Risco")&&e.jsx("div",{className:"text-green-600 mt-2",children:"Conduta: Seguimento ambulatorial de rotina. Educação dos pais sobre sinais de alerta."})]})]})},b=()=>e.jsxs("div",{className:"space-y-4 text-sm text-muted-foreground",children:[e.jsx("h3",{className:"font-semibold",children:"Referências:"}),e.jsx("p",{children:"Sociedade Brasileira de Pediatria. Manual de orientação: hiperbilirrubinemia indireta no período neonatal. Departamento Científico de Neonatologia (2019-2021)."}),e.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:e.jsxs("p",{className:"text-yellow-800",children:[e.jsx("strong",{children:"Aviso importante:"})," Esta ferramenta serve apenas como guia e não substitui o julgamento clínico. Sempre considere outros fatores de risco e consulte as diretrizes locais para o manejo da hiperbilirrubinemia."]})})]}),g=()=>{const a=p.bhutani;return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(x,{...a}),e.jsx(n,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsx(h,{}),e.jsx(j,{}),e.jsx(b,{})]})}),e.jsx(d,{})]})};export{g as default};
