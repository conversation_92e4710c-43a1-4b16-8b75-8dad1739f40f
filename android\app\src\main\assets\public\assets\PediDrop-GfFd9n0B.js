import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,m as r,R as t,U as i,B as l,aL as d,aH as o,d as n,D as c,e as m,f as x,g as h,z as p,av as g,T as b,s as u,b6 as j,aa as v,aB as y}from"./index-CrSshpOb.js";import f from"./Footer-ClHMSbsi.js";import{S as N}from"./skeleton-Cg77x26y.js";import{M as k,u as w}from"./MarkdownEditor-XgSr594v.js";import{C}from"./calendar-zjm19wJF.js";import{C as D}from"./clock-CsU5Tz0c.js";import{B as P}from"./bookmark-BgvDsLPW.js";import{d as _}from"./supabase-vendor-qi_Ptfv-.js";import{D as S}from"./droplets-DIBDsWL0.js";import{H as F}from"./FeedbackTrigger-CJimmo1j.js";import{T}from"./target-Cn5InUof.js";import{T as z}from"./trending-up-DXHXsr4i.js";import{M as E}from"./meh-K2vJFtlM.js";import{T as I}from"./thumbs-up-C72x3tRg.js";import{S as O}from"./star-DsgxKBIV.js";import{S as q}from"./send-ekC0Pf3C.js";import{U as L}from"./users-HWMvGbPF.js";import{a as R}from"./router-BAzpOxbo.js";import{C as A}from"./chevron-left-CuYzwBha.js";import{R as H}from"./refresh-cw-Yj3GAwb9.js";import"./query-vendor-B-7l6Nb3.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-BDU9Wbeo.js";import"./rocket-Bte4lXB7.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./circle-help-C80RLJKB.js";import"./index-LRPfEZCp.js";import"./markdown-vendor-C57yw7YK.js";import"./index-Bf1cTgQT.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=s("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),M=s("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]),$=({drop:s,index:n,variant:c="standard",isToday:m=!1})=>{const[x,h]=a.useState(!1),p=e=>new Date(e).toLocaleDateString("pt-BR",{day:"2-digit",month:"short",year:"numeric"}),g={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5,delay:.1*n}}};return"compact"===c?e.jsx(r.div,{variants:g,initial:"hidden",animate:"visible",whileHover:{y:-2},transition:{duration:.2},children:e.jsx(t,{className:"overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500",children:e.jsx(i,{className:"p-4",children:e.jsxs("div",{className:"flex items-start justify-between gap-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 line-clamp-2 mb-2",children:s.title}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3",children:s.summary}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(C,{className:"h-3 w-3"}),p(s.pub_date)]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(D,{className:"h-3 w-3"}),s.reading_time||5," min"]})]})]}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(l,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation()},children:e.jsx(P,{className:"h-4 w-4"})}),e.jsx(l,{variant:"ghost",size:"sm",onClick:e=>{e.stopPropagation(),navigator.share&&navigator.share({title:s.title,text:s.summary,url:window.location.href})},children:e.jsx(B,{className:"h-4 w-4"})})]})]})})})}):"featured"===c?e.jsxs(r.div,{variants:g,initial:"hidden",animate:"visible",whileHover:{y:-2},transition:{duration:.3},className:"mb-8 max-w-4xl mx-auto",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white text-center py-4 rounded-t-lg relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"}),e.jsx("div",{className:"relative z-10 flex items-center justify-center gap-2",children:m?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),e.jsx("h2",{className:"text-sm font-bold tracking-wider uppercase",children:"PEDIDROP DE HOJE"}),e.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"})]}):e.jsx("h2",{className:"text-sm font-bold tracking-wider uppercase",children:"PEDIDROP"})})]}),e.jsx("div",{className:"bg-white dark:bg-slate-800 border-x border-b border-gray-200 dark:border-gray-700 rounded-b-lg shadow-xl shadow-blue-500/10 dark:shadow-purple-500/10",children:e.jsxs("div",{className:"p-6 relative",children:[e.jsx("div",{className:"absolute inset-0 rounded-b-lg bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10 pointer-events-none"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl md:text-2xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-3",children:s.title}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1 bg-gray-50 dark:bg-gray-700 px-2 py-1 rounded",children:[e.jsx(D,{className:"h-3 w-3"}),s.reading_time||5," min"]}),e.jsxs("div",{className:"flex items-center gap-1 bg-gray-50 dark:bg-gray-700 px-2 py-1 rounded",children:[e.jsx(C,{className:"h-3 w-3"}),p(s.pub_date)]})]})]}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-8",children:e.jsx(k,{content:s.summary})}),s.sections&&s.sections.length>0&&e.jsx("div",{className:"space-y-6",children:s.sections.map(((a,s)=>e.jsxs("div",{className:"mb-6",children:[e.jsxs("h3",{className:"text-lg font-bold text-gray-900 dark:text-gray-100 mb-3",children:["• ",a.title]}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed pl-4",children:e.jsx(k,{content:a.content})})]},a.id)))}),s.references&&s.references.length>0&&e.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("h3",{className:"font-bold text-gray-800 dark:text-gray-200 mb-4",children:"📚 Referências"}),e.jsx("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-3",children:s.references.map(((a,s)=>e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsxs("span",{className:"text-gray-400 mt-1 font-medium",children:[s+1,"."]}),e.jsx("div",{className:"flex-1",children:a.url?e.jsx("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors",children:a.title||a.reference_text}):e.jsx("span",{children:a.title||a.reference_text})})]},a.id)))})]})]})]})})]}):e.jsxs(r.div,{variants:g,initial:"hidden",animate:"visible",whileHover:{y:-1},transition:{duration:.3},className:"border border-gray-200 dark:border-gray-600 rounded-lg mb-4 overflow-hidden",children:[e.jsxs("div",{className:"flex items-start justify-between gap-4 p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",onClick:()=>h(!x),children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("h3",{className:"font-bold text-lg text-gray-900 dark:text-gray-100 leading-tight mb-2",children:["• ",s.title]}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(D,{className:"h-3 w-3"}),s.reading_time||5," min"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(C,{className:"h-3 w-3"}),p(s.pub_date)]})]})]}),e.jsx("div",{className:"shrink-0",children:e.jsx(l,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:x?e.jsx(d,{className:"h-4 w-4"}):e.jsx(o,{className:"h-4 w-4"})})})]}),x&&e.jsx(r.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800",children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-6 pb-4 border-b border-gray-200 dark:border-gray-600",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed text-sm",children:s.summary})}),s.sections&&s.sections.length>0&&e.jsx("div",{className:"space-y-4 mb-6",children:s.sections.map(((a,s)=>e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-base font-bold text-gray-900 dark:text-gray-100 mb-2",children:["• ",a.title]}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed pl-4 text-sm",children:e.jsx(k,{content:a.content})})]},a.id)))}),s.references&&s.references.length>0&&e.jsxs("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("h4",{className:"font-bold text-gray-800 dark:text-gray-200 mb-3 text-sm",children:"📚 Referências"}),e.jsx("ul",{className:"text-xs text-gray-600 dark:text-gray-400 space-y-2",children:s.references.map(((a,s)=>e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsxs("span",{className:"text-gray-400 mt-1 font-medium",children:[s+1,"."]}),e.jsx("div",{className:"flex-1",children:a.url?e.jsx("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors",children:a.title||a.reference_text}):e.jsx("span",{children:a.title||a.reference_text})})]},a.id)))})]})]})})]})},V=[{type:"continue",icon:z,title:"Continue assim!",description:"O PediDrop está ótimo, continue com este formato",color:"bg-green-500",bgColor:"bg-green-50 dark:bg-green-900/20",borderColor:"border-green-200 dark:border-green-800",textColor:"text-green-700 dark:text-green-300"},{type:"improve",icon:E,title:"Pode melhorar",description:"Gosto do formato, mas tem pontos a melhorar",color:"bg-yellow-500",bgColor:"bg-yellow-50 dark:bg-yellow-900/20",borderColor:"border-yellow-200 dark:border-yellow-800",textColor:"text-yellow-700 dark:text-yellow-300"},{type:"discontinue",icon:M,title:"Repensar formato",description:"Não está funcionando, precisa de mudanças grandes",color:"bg-red-500",bgColor:"bg-red-50 dark:bg-red-900/20",borderColor:"border-red-200 dark:border-red-800",textColor:"text-red-700 dark:text-red-300"}],U=({open:s,onOpenChange:t,postId:i,postTitle:d})=>{const[o,j]=a.useState(null),[v,y]=a.useState(5),[f,N]=a.useState(""),[k,w]=a.useState(!1),[C,D]=a.useState(!1),{toast:P}=n(),z=_.useUser();a.useEffect((()=>{s&&(async()=>{if(z&&i)try{const{data:e}=await u.from("pedbook_feedbacks").select("id").eq("post_id",i).eq("user_id",z.id).eq("feedback_category","pedidrop").maybeSingle();e&&D(!0)}catch(e){}})()}),[z,i,s]);const E=()=>{k||(t(!1),setTimeout((()=>{j(null),y(5),N(""),D(!1)}),300))};return e.jsx(c,{open:s,onOpenChange:E,children:e.jsxs(m,{className:"max-w-2xl max-h-[90dvh] overflow-y-auto",children:[e.jsxs(x,{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg",children:e.jsx(S,{className:"h-6 w-6 text-white"})}),e.jsx(h,{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text",children:"Feedback do PediDrop"})]}),e.jsxs(p,{className:"text-center text-base leading-relaxed",children:[e.jsx("strong",{children:"Precisamos da sua opinião!"})," O PediDrop é um formato novo e queremos saber se está sendo relevante para você. Sua avaliação nos ajuda a decidir se continuamos, melhoramos ou repensamos este modelo."]})]}),e.jsx("div",{className:"space-y-6 py-4",children:C?e.jsxs(r.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-8 space-y-4",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center",children:e.jsx(F,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),e.jsx("h3",{className:"text-lg font-semibold text-green-700 dark:text-green-300",children:"Obrigado pelo seu feedback!"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Você já avaliou este PediDrop. Sua opinião é muito importante para nós! 🙏"})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(T,{className:"h-5 w-5 text-blue-600"}),"Como você avalia o formato PediDrop?"]}),e.jsx("div",{className:"grid gap-3",children:V.map((a=>{const s=a.icon,t=o===a.type;return e.jsx(r.button,{onClick:()=>j(a.type),className:"p-4 rounded-lg border-2 transition-all duration-200 text-left "+(t?`${a.bgColor} ${a.borderColor} ${a.textColor}`:"bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:`p-2 rounded-lg ${a.color}`,children:e.jsx(s,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-semibold",children:a.title}),e.jsx("p",{className:"text-sm opacity-80",children:a.description})]}),t&&e.jsx("div",{className:"text-green-600 dark:text-green-400",children:e.jsx(I,{className:"h-5 w-5"})})]})},a.type)}))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(O,{className:"h-5 w-5 text-yellow-500"}),"Nota geral (1-5 estrelas)"]}),e.jsx("div",{className:"flex justify-center gap-2",children:[1,2,3,4,5].map((a=>e.jsx("button",{onClick:()=>y(a),className:"transition-all duration-200 hover:scale-110 p-2 "+(v>=a?"text-yellow-400 dark:text-yellow-300":"text-gray-300 dark:text-gray-600"),type:"button",children:e.jsx(O,{className:"w-8 h-8",fill:v>=a?"currentColor":"none",strokeWidth:1.5})},a)))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(g,{className:"h-5 w-5 text-purple-600"}),"Comentários e sugestões (opcional)"]}),e.jsx(b,{value:f,onChange:e=>N(e.target.value),placeholder:"Conte-nos o que pensa sobre o PediDrop: o que está funcionando bem? O que poderia melhorar? Que tipo de conteúdo gostaria de ver? Sua opinião é fundamental para decidirmos o futuro deste formato!",className:"min-h-[100px] resize-none",maxLength:1e3}),e.jsxs("div",{className:"text-right text-sm text-gray-500",children:[f.length,"/1000 caracteres"]})]}),e.jsxs("div",{className:"flex gap-3 pt-4",children:[e.jsx(l,{variant:"outline",onClick:E,disabled:k,className:"flex-1",children:"Talvez depois"}),e.jsx(l,{onClick:async()=>{if(z)if(o){w(!0);try{const{data:e}=await u.from("pedbook_feedback_types").select("id").eq("name","PediDrop").single();if(!e)throw new Error("Tipo de feedback PediDrop não encontrado");const{error:a}=await u.from("pedbook_feedbacks").insert({type_id:e.id,user_id:z.id,title:`Feedback PediDrop - ${o}`,message:f.trim()||`Avaliação do formato PediDrop: ${o} (${v} estrelas)`,feedback_category:"pedidrop",post_id:i||"general",feedback_type:o,rating:v});if(a){if("23505"===a.code)return P({title:"Feedback já enviado",description:"Você já avaliou este PediDrop anteriormente.",variant:"destructive"}),void D(!0);throw a}P({title:"Feedback enviado!",description:"Obrigado por nos ajudar a melhorar o PediDrop! 🙏"}),D(!0),setTimeout((()=>{t(!1)}),2e3)}catch(e){P({title:"Erro ao enviar feedback",description:e.message||"Tente novamente mais tarde.",variant:"destructive"})}finally{w(!1)}}else P({title:"Selecione uma opção",description:"Por favor, escolha uma das opções de feedback.",variant:"destructive"});else P({title:"Login necessário",description:"Você precisa estar logado para enviar feedback.",variant:"destructive"})},disabled:k||!o,className:"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:k?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Enviando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Enviar Feedback"]})})]})]})})]})})},Y=({postId:s,postTitle:d})=>{const{showFeedback:o,triggerFeedback:n,closeFeedback:c,isLoggedIn:m}=(()=>{const[e,s]=a.useState(!1),[r,t]=a.useState(!1),[i,l]=a.useState(0),d=_.useUser();return a.useEffect((()=>{if(sessionStorage.getItem("pedidrop_feedback_shown"))return;const e=Date.now(),a=setInterval((()=>{l(Date.now()-e)}),1e3),s=()=>{(window.pageYOffset||document.documentElement.scrollTop)+window.innerHeight>=document.documentElement.scrollHeight-200&&!r&&t(!0)};return window.addEventListener("scroll",s),()=>{clearInterval(a),window.removeEventListener("scroll",s)}}),[r]),a.useEffect((()=>{if(r&&i>3e4&&d&&!e&&!sessionStorage.getItem("pedidrop_feedback_shown")){const e=setTimeout((()=>{s(!0),sessionStorage.setItem("pedidrop_feedback_shown","true")}),2e3);return()=>clearTimeout(e)}}),[r,i,d,e]),{showFeedback:e,triggerFeedback:()=>{d&&(s(!0),sessionStorage.setItem("pedidrop_feedback_shown","true"))},closeFeedback:()=>{s(!1)},hasScrolledToBottom:r,timeOnPage:Math.floor(i/1e3),isLoggedIn:!!d}})();return e.jsxs(e.Fragment,{children:[e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-12 mb-8",children:e.jsx(t,{className:"bg-gradient-to-br from-blue-50 via-purple-50/30 to-indigo-50/20 dark:from-slate-800 dark:via-slate-700/50 dark:to-slate-800 border-blue-200/50 dark:border-blue-800/30 shadow-lg",children:e.jsx(i,{className:"p-6 md:p-8",children:e.jsxs("div",{className:"text-center space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg",children:e.jsx(S,{className:"h-6 w-6 text-white"})}),e.jsx("h3",{className:"text-xl md:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text",children:"Sua opinião importa!"})]}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300 text-base md:text-lg leading-relaxed max-w-2xl mx-auto",children:["O ",e.jsx("strong",{children:"PediDrop"})," é um formato experimental. Queremos saber se está sendo útil para você e se devemos continuar, melhorar ou repensar este modelo de atualização clínica."]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-lg mx-auto",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[e.jsx(D,{className:"h-4 w-4 text-blue-500"}),e.jsx("span",{children:"5 min de leitura"})]}),e.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[e.jsx(z,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{children:"Baseado em evidências"})]}),e.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[e.jsx(L,{className:"h-4 w-4 text-purple-500"}),e.jsx("span",{children:"Para pediatras"})]})]}),e.jsxs("div",{className:"space-y-4",children:[m?e.jsxs(l,{onClick:n,size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",children:[e.jsx(g,{className:"h-5 w-5 mr-2"}),"Avaliar o PediDrop"]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Faça login para avaliar o PediDrop"}),e.jsxs(l,{variant:"outline",size:"lg",className:"border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900/30",onClick:()=>window.location.href="/login",children:[e.jsx(F,{className:"h-5 w-5 mr-2"}),"Fazer Login para Avaliar"]})]}),e.jsx("div",{className:"bg-white/60 dark:bg-slate-700/30 rounded-lg p-4 border border-blue-200/30 dark:border-blue-800/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[e.jsx(O,{className:"h-4 w-4 text-yellow-500"}),e.jsx("span",{children:"Seu feedback nos ajuda a decidir o futuro do PediDrop"})]})})]}),!1]})})})}),e.jsx(U,{open:o,onOpenChange:c,postId:s,postTitle:d})]})},Q=()=>{const s=R(),[t,i]=a.useState(5),[d,n]=a.useState(!1),{data:c,isLoading:m,isError:x,error:h,refetch:p}=w({limit:t,offset:0,includeUnpublished:!1});return e.jsxs("div",{className:"flex flex-col min-h-screen bg-gradient-to-br from-blue-50 via-purple-50/30 to-indigo-50/20 dark:from-slate-900 dark:via-slate-800/50 dark:to-slate-900",children:[e.jsxs(j,{children:[e.jsx("title",{children:"PediDrop - Atualização Clínica em 5 Minutos | PedBook"}),e.jsx("meta",{name:"description",content:"Atualizações clínicas diárias em pediatria. Conteúdo baseado em evidências, direto ao ponto, em apenas 5 minutos de leitura."}),e.jsx("meta",{name:"keywords",content:"pediatria, atualizações clínicas, medicina baseada em evidências, educação médica continuada"})]}),e.jsx(v,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(r.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8 mt-2",children:e.jsxs("div",{className:"mb-4 sm:mb-8",children:[e.jsxs("div",{className:"relative mb-3 sm:mb-4",children:[e.jsx("div",{className:"absolute left-0 top-1/2 -translate-y-1/2 z-10",children:e.jsxs(l,{variant:"ghost",onClick:()=>s("/"),className:"flex items-center gap-1 h-8 px-2 sm:h-9 sm:px-3 hover:bg-blue-50/60 dark:hover:bg-slate-800/60 transition-all duration-300 hover:scale-105 rounded-lg","aria-label":"Voltar ao menu inicial",children:[e.jsx(A,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline text-sm font-medium",children:"Voltar"})]})}),e.jsxs("div",{className:"flex items-center justify-center gap-2 sm:gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(S,{className:"h-8 w-8 sm:h-12 sm:w-12 text-blue-600 dark:text-blue-400"}),e.jsx(y,{className:"h-4 w-4 sm:h-6 sm:w-6 text-purple-500 absolute -top-1 -right-1 animate-pulse"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-600 text-transparent bg-clip-text",children:"PediDrop"}),e.jsx("p",{className:"text-sm sm:text-lg text-gray-600 dark:text-gray-300 font-medium",children:"Atualização clínica em 5 minutos"})]})]})]}),e.jsx("div",{className:"max-w-3xl mx-auto mb-4 sm:mb-6",children:e.jsxs("p",{className:"text-gray-700 dark:text-gray-300 text-sm sm:text-lg leading-relaxed",children:["🩺 Todos os dias, às ",e.jsx("strong",{children:"7h da manhã"}),", publicamos conteúdo clínico novo, direto ao ponto, com base nas melhores evidências médicas — tudo em menos de",e.jsxs("span",{className:"inline-flex items-center gap-1 mx-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 font-semibold text-xs sm:text-sm",children:[e.jsx(D,{className:"h-3 w-3 sm:h-4 sm:w-4"}),"5 minutos"]}),"de leitura."]})})]})}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:m?e.jsx("div",{className:"space-y-6",children:Array.from({length:3}).map(((a,s)=>e.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(N,{className:"h-6 w-6 rounded"}),e.jsx(N,{className:"h-4 w-20"})]}),e.jsx(N,{className:"h-6 w-3/4"}),e.jsx(N,{className:"h-4 w-full"}),e.jsx(N,{className:"h-4 w-2/3"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{className:"h-6 w-16"}),e.jsx(N,{className:"h-6 w-20"}),e.jsx(N,{className:"h-6 w-18"})]})]})},s)))}):x?e.jsx("div",{className:"text-center py-12",children:e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-md mx-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-red-800 dark:text-red-200 mb-2",children:"Erro ao carregar PediDrops"}),e.jsx("p",{className:"text-red-600 dark:text-red-300 mb-4",children:h?.message||"Não foi possível carregar as atualizações clínicas."}),e.jsxs(l,{variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30",onClick:()=>{p()},children:[e.jsx(H,{className:"h-4 w-4 mr-2"}),"Tentar Novamente"]})]})}):c&&c.length>0?e.jsxs(e.Fragment,{children:[c.length>0&&e.jsx("div",{className:"mb-12",children:e.jsx($,{drop:c[0],index:0,variant:"featured",isToday:(e=>{const a=new Date,s=new Date(e);return s.getDate()===a.getDate()&&s.getMonth()===a.getMonth()&&s.getFullYear()===a.getFullYear()})(c[0].pub_date)},c[0].id)}),c.length>1&&e.jsxs("div",{className:"mb-8 max-w-4xl mx-auto",children:[e.jsxs("div",{className:"bg-gradient-to-r from-slate-600 via-slate-700 to-slate-800 text-white text-center py-4 rounded-t-lg relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"}),e.jsxs("div",{className:"relative z-10 flex items-center justify-center gap-2",children:[e.jsx("div",{className:"w-1 h-1 bg-slate-300 rounded-full"}),e.jsx("h2",{className:"text-sm font-bold tracking-wider uppercase",children:"PEDIDROPS ANTERIORES"}),e.jsx("div",{className:"w-1 h-1 bg-slate-300 rounded-full"})]})]}),e.jsx("div",{className:"bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-b-lg shadow-lg p-6",children:e.jsx("div",{className:"space-y-4",children:c.slice(1).map(((a,s)=>e.jsx($,{drop:a,index:s+1,variant:"standard"},a.id)))})})]}),c.length>=t&&e.jsx("div",{className:"text-center",children:e.jsx(l,{onClick:async()=>{n(!0),i((e=>e+5)),setTimeout((()=>{n(!1)}),500)},disabled:d,size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3",children:d?e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"h-5 w-5 mr-2 animate-spin"}),"Carregando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"h-5 w-5 mr-2"}),"Carregar Mais PediDrops"]})})})]}):e.jsx("div",{className:"text-center py-12",children:e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-8 max-w-md mx-auto",children:[e.jsx(S,{className:"h-16 w-16 text-blue-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2",children:"Nenhum PediDrop encontrado"}),e.jsx("p",{className:"text-blue-600 dark:text-blue-300",children:"Não há atualizações clínicas disponíveis no momento. Volte amanhã às 7h para novos conteúdos!"})]})})}),e.jsx(Y,{postId:c&&c.length>0?c[0].id:void 0,postTitle:c&&c.length>0?c[0].title:void 0})]})}),e.jsx(f,{})]})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */export{Q as default};
