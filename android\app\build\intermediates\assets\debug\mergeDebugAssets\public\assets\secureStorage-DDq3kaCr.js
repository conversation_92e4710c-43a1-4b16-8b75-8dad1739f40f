const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/useConsolidatedSession-BN42Q1qE.js","assets/query-vendor-B-7l6Nb3.js","assets/critical-DVX9Inzy.js","assets/radix-core-6kBL75b5.js","assets/index-CFnD44mG.js","assets/supabase-vendor-qi_Ptfv-.js","assets/router-BAzpOxbo.js","assets/form-vendor-rYZw_ur7.js","assets/radix-forms-DX-owj97.js","assets/radix-interactive-DJo-0Sg_.js","assets/radix-toast-1_gbKn9f.js","assets/radix-feedback-dpGNY8wJ.js","assets/radix-popover-DQqTw7_-.js","assets/radix-layout-CC8mXA4O.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,s,a)=>((t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a)(t,"symbol"!=typeof s?s+"":s,a);import{c as s,s as a,u as r,h as n,B as i,X as o,aD as l,m as c,b as d,D as m,e as u,f as g,g as h,z as p,l as f,ab as b,C as x}from"./index-CFnD44mG.js";import{r as y,b as w}from"./critical-DVX9Inzy.js";import{_ as v}from"./supabase-vendor-qi_Ptfv-.js";import{u as k}from"./query-vendor-B-7l6Nb3.js";import{j}from"./radix-core-6kBL75b5.js";import{Z as N,a as _}from"./zoom-out-B1ZaW-Lk.js";import{D as C}from"./download-BiDIwqLu.js";import{a as S}from"./router-BAzpOxbo.js";import{B as $}from"./book-open-CpPiu5Sl.js";import{C as M}from"./chevron-left-CRTmHBdc.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=s("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),T=new class{constructor(){t(this,"logs",[]),t(this,"maxLogs",50),t(this,"isDevelopment",!1),t(this,"lastLogTime",new Map),t(this,"throttleMs",1e3)}shouldLog(e){return this.isDevelopment?!(e.includes("refresh")||e.includes("loading")||e.includes("update")||e.includes("render")):e.includes("ERROR")||e.includes("CRITICAL")||e.includes("FAILED")||e.includes("❌")||e.includes("🚨")}addLog(e,t){this.logs.push({message:e,timestamp:Date.now(),level:t}),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs))}info(e){this.shouldLog(e)&&this.addLog(e,"info")}warn(e){this.shouldLog(e)&&this.addLog(e,"warn")}error(e,t){this.addLog(e,"error")}dev(e,...t){}debug(e,...t){this.isDevelopment&&this.shouldLog(e)&&this.addLog(e,"info")}success(e){}loading(e){}clearOldLogs(){const e=Date.now()-36e5;this.logs=this.logs.filter((t=>t.timestamp>e)),this.lastLogTime.clear()}getLogs(){return[...this.logs]}getStats(){const e=Date.now(),t=this.logs.filter((t=>e-t.timestamp<6e4)),s=this.logs.filter((e=>"error"===e.level));return{totalLogs:this.logs.length,logsLastMinute:t.length,totalErrors:s.length,lastError:s[s.length-1]?.message||"None"}}};setInterval((()=>{T.clearOldLogs()}),18e5);const L={threadLoaded:(e,t)=>{},threadCreated:(e,t)=>{},messageSaved:(e,t)=>{},contextLoaded:(e,t)=>T.info(`[CONTEXT] Loaded ${t} messages for AI context from thread ${e.slice(0,8)}...`),cacheHit:e=>T.info(`[CACHE] Cache hit for ${e}`),cacheMiss:e=>T.info(`[CACHE] Cache miss for ${e}, loading from database`),error:(e,t)=>T.error(`[DR_WILL] Error in ${e}`,t),performance:(e,t)=>{t>1e3?T.warn(`[PERFORMANCE] ${e} took ${t}ms (slow)`):T.info(`[PERFORMANCE] ${e} completed in ${t}ms`)},success:e=>T.success(e),info:e=>T.info(e),warn:e=>T.warn(e)},D=new class{constructor(){t(this,"metrics",[]),t(this,"threadStates",new Map),t(this,"maxMetrics",100),t(this,"SLOW_OPERATION_MS",2e3),t(this,"MAX_LOAD_ATTEMPTS",3),t(this,"ERROR_THRESHOLD",5)}recordMetric(e,t,s,a){const r={operation:e,duration:t,timestamp:Date.now(),success:s,error:a};this.metrics.push(r),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics)),!s&&a&&this.checkErrorRate()}updateThreadState(e,t,s){const a=this.threadStates.get(e),r=Date.now();a?(a.messageCount=t,a.lastActivity=r,a.loadAttempts+=1,s&&a.errors.push(s)):this.threadStates.set(e,{id:e,messageCount:t,lastActivity:r,loadAttempts:1,errors:s?[s]:[]})}checkErrorRate(){const e=Date.now()-6e4;this.metrics.filter((t=>!t.success&&t.timestamp>e)).length,this.ERROR_THRESHOLD}getDiagnostics(){const e=Date.now(),t=this.metrics.filter((t=>e-t.timestamp<6e4)),s=this.metrics.filter((t=>e-t.timestamp<36e5)),a=t.length>0?t.filter((e=>e.success)).length/t.length*100:100,r=t.length>0?t.reduce(((e,t)=>e+t.duration),0)/t.length:0,n=t.filter((e=>e.duration>this.SLOW_OPERATION_MS)),i=t.filter((e=>!e.success));return{timestamp:(new Date).toISOString(),performance:{successRate:Math.round(a),avgDuration:Math.round(r),slowOperations:n.length,totalOperations:t.length},errors:{lastMinute:i.length,lastHour:s.filter((e=>!e.success)).length,recent:i.slice(-3).map((e=>({operation:e.operation,error:e.error,timestamp:new Date(e.timestamp).toLocaleTimeString()})))},threads:{active:this.threadStates.size,problematic:Array.from(this.threadStates.values()).filter((e=>e.loadAttempts>2||e.errors.length>0)).map((e=>({id:e.id.slice(0,8)+"...",loadAttempts:e.loadAttempts,errors:e.errors.length,lastActivity:new Date(e.lastActivity).toLocaleTimeString()})))}}}cleanup(){const e=Date.now()-36e5;this.metrics=this.metrics.filter((t=>t.timestamp>e));for(const[t,s]of this.threadStates.entries())s.lastActivity<e&&this.threadStates.delete(t)}getHealthStatus(){const e=this.getDiagnostics();return e.performance.successRate<50||e.errors.lastMinute>10?"critical":e.performance.successRate<80||e.performance.slowOperations>3?"warning":"healthy"}logStatus(){this.getHealthStatus(),this.getDiagnostics()}};setInterval((()=>{D.cleanup()}),18e5);const R=async(e,t)=>{const s=Date.now();let a,r=!1;try{const e=await t();return r=!0,e}catch(n){throw a=n instanceof Error?n.message:String(n),n}finally{const t=Date.now()-s;D.recordMetric(e,t,r,a)}},I=new Map,O=e=>{const t=I.get(e);return t&&Date.now()-t.timestamp<t.ttl?t.data:(I.delete(e),null)},A=(e,t,s=6e4)=>{I.set(e,{data:t,timestamp:Date.now(),ttl:s})},z=()=>{const[e,t]=y.useState([]),[s,i]=y.useState(null),[o,l]=y.useState([]),[c,d]=y.useState(!1),{user:m}=r();y.useEffect((()=>{n.historyHookInit({user:m?{id:m.id,email:m.email}:null,hasUser:!!m,userId:m?.id,authState:{localStorage_userId:localStorage.getItem("auth_user_id"),localStorage_profile:localStorage.getItem("auth_profile")?"exists":"missing"}})}),[]);const u=y.useRef(m?.id),g=y.useRef(m);y.useEffect((()=>{u.current!==m?.id&&(n.historyUserChanged({from:u.current,to:m?.id,userObject:m?{id:m.id,email:m.email}:null,hasUser:!!m,userType:typeof m}),u.current=m?.id,g.current=m)}),[m?.id]);const{data:h}=(()=>{const{data:e,isLoading:t,error:s}=k({queryKey:["study-categories-all"],queryFn:async()=>{const{data:e,error:t}=await a.from("study_categories").select("id, name, type, parent_id").order("type, name").limit(2e3);if(t)throw t;return e||[]},staleTime:36e5,gcTime:72e5});return{data:e||[],isLoading:t,error:s,specialties:e?.filter((e=>"specialty"===e.type))||[],themes:e?.filter((e=>"theme"===e.type))||[],focuses:e?.filter((e=>"focus"===e.type))||[]}})(),p=y.useRef(!1),f=y.useRef(null),b=y.useCallback((async(e=!1)=>{if(m?.id)return await R("loadThreads",(async()=>{const s=`threads_${m.id}`;if(!e){const e=O(s);if(e)return L.cacheHit(s),t(e),void(p.current=!0);if(p.current&&f.current===m.id)return}try{d(!0),L.cacheMiss(s);const{data:e,error:r}=await a.from("pedbook_chat_threads").select("id, title, last_message_at, message_count, created_at, metadata").eq("user_id",m.id).order("last_message_at",{ascending:!1});if(r)throw L.error("loading threads",r),r;const n=e.map((e=>({id:e.id,title:e.title,lastMessageAt:new Date(e.last_message_at||e.created_at),messageCount:e.message_count||0,createdAt:new Date(e.created_at),metadata:e.metadata})));t(n),p.current=!0,f.current=m.id,A(s,n)}catch(r){throw L.error("loading threads",r),r}finally{d(!1)}}))}),[m?.id]),x=y.useCallback((async e=>m?.id?await R(`loadMessages_${e.slice(0,8)}`,(async()=>{const t=`messages_${e}_${m.id}`;if(s!==e){const s=O(t);if(s)return L.cacheHit(t),l(s),i(e),L.threadLoaded(e,s.length),D.updateThreadState(e,s.length),s}try{d(!0),L.cacheMiss(t);const{data:s,error:r}=await a.from("pedbook_chat_history").select("*").eq("thread_id",e).eq("user_id",m.id).order("created_at",{ascending:!0});if(r)throw L.error("loading messages",r),D.updateThreadState(e,0,r.message),r;const n=s.map((e=>({id:e.id,content:e.content,isUser:"user"===e.role,timestamp:new Date(e.created_at),images:e.metadata?.images||void 0,image_url:e.metadata?.images||void 0})));return l(n),i(e),A(t,n,3e4),L.threadLoaded(e,n.length),D.updateThreadState(e,n.length),n}catch(r){throw L.error("loading messages",r),D.updateThreadState(e,0,r instanceof Error?r.message:String(r)),r}finally{d(!1)}})):null),[m?.id,s]),w=y.useCallback((async(e,t)=>{let r=m?.id;return r||(r=localStorage.getItem("auth_user_id")),r?await R("saveMessage_"+(e.isUser?"user":"ai"),(async()=>{const n=t||s;if(!n){const e="No thread ID provided for saving message";throw L.error("saving message",e),new Error(e)}const i={};e.images&&e.images.length>0&&(i.images=e.images);const{data:o,error:l}=await a.from("pedbook_chat_history").insert({user_id:r,thread_id:n,role:e.isUser?"user":"assistant",content:e.content,metadata:i}).select().single();if(l)throw L.error("saving message",l),l;await a.rpc("increment_thread_message_count",{thread_id:n,user_id:r}),L.messageSaved(o.id,e.isUser);const c=`threads_${r}`,d=`messages_${n}_${r}`;return I.delete(c),I.delete(d),b(!0).catch((()=>{})),o.id})):null}),[m?.id,s,b]),j=y.useCallback((async(e,t)=>{n.historyCreateThreadCalled({user:m?{id:m.id,email:m.email}:null,hasUser:!!m,userId:m?.id,userType:typeof m,titleOrFirstMessage:e,sessionId:t,timeSincePageLoad:performance.now()});const s={localStorage_userId:localStorage.getItem("auth_user_id"),localStorage_profile:localStorage.getItem("auth_profile")?"exists":"missing"};n.historyCreateThreadAuthCheck(s);let r=m?.id;return r||(r=localStorage.getItem("auth_user_id")),r?await R("createNewThread",(async()=>{const s=crypto.randomUUID();let o;o=t&&e?e:e?e.length>50?e.substring(0,50)+"...":e:"Nova conversa";const c=t?{sessionId:t}:null,{data:d,error:u}=await a.from("pedbook_chat_threads").insert({id:s,user_id:r,title:o,message_count:0,last_message_at:(new Date).toISOString(),metadata:c}).select().single();if(u)throw L.error("creating thread",u),u;return n.historyCreateThreadSuccess({threadId:s,userId:r,usedFallback:r!==m?.id,originalUserId:m?.id}),L.threadCreated(s,o),i(s),l([]),await b(!0),s})):(n.historyCreateThreadNoUser({user:m,authState:s,fallbackUserId:r}),null)}),[m?.id,b]),N=y.useCallback((async e=>{if(m?.id)try{await a.from("pedbook_chat_history").delete().eq("thread_id",e).eq("user_id",m.id);const{error:r}=await a.from("pedbook_chat_threads").delete().eq("id",e).eq("user_id",m.id);if(r)throw r;t((t=>t.filter((t=>t.id!==e)))),s===e&&(i(null),l([]))}catch(r){}}),[m?.id,s,e.length]),_=y.useCallback((async()=>{if(m?.id)try{await a.from("pedbook_chat_history").delete().eq("user_id",m.id);const{error:e}=await a.from("pedbook_chat_threads").delete().eq("user_id",m.id);if(e)throw e;t([]),i(null),l([])}catch(e){throw e}}),[m?.id]),C=y.useCallback((()=>{i(null),l([])}),[]),S=y.useCallback((async e=>{const t=e||s;let r=m?.id;return r||(r=localStorage.getItem("auth_user_id")),t&&r?await R(`getConversationHistory_${t.slice(0,8)}`,(async()=>{const{data:e,error:s}=await a.from("pedbook_chat_history").select("role, content, metadata, created_at").eq("thread_id",t).eq("user_id",r).order("created_at",{ascending:!0});if(s)throw L.error("getting conversation history",s),s;const n=e.map((e=>({role:"user"===e.role?"user":"assistant",content:e.content,images:e.metadata?.images||void 0})));return L.contextLoaded(t,n.length),n})):[]}),[s,m?.id]),$=y.useCallback((async(e,t)=>m?.id?await R("findOrCreateContextualThread",(async()=>{const{data:s,error:r}=await a.from("pedbook_chat_threads").select("id, title, metadata").eq("user_id",m.id).eq("metadata->>sessionId",t).order("created_at",{ascending:!1}).limit(1);if(!r&&s&&s.length>0){const r=s[0];if(r.title&&r.title.includes("- Sessão Contextual")){const s=await M(e,t),{error:n}=await a.from("pedbook_chat_threads").update({title:s}).eq("id",r.id).eq("user_id",m.id)}return r.id}const n=await M(e,t);return await j(n,t)})):null),[m?.id,j]),M=async(e,t)=>{try{const t=h.length>0?await v((()=>import("./useConsolidatedSession-BN42Q1qE.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13])).then((t=>({session:{title:e,total_questions:0,specialty_id:null,theme_id:null,focus_id:null,knowledge_domain:"general"}}))):null,s=t?.session,a=null;let r=null,n=null,i=null;if(!a&&s){if(s.specialty_id&&h.length>0){const e=h.find((e=>e.id===s.specialty_id&&"specialty"===e.type));r=e?.name}if(s.theme_id&&h.length>0){const e=h.find((e=>e.id===s.theme_id&&"theme"===e.type));n=e?.name}if(s.focus_id&&h.length>0){const e=h.find((e=>e.id===s.focus_id&&"focus"===e.type));i=e?.name}}if(a||!s)return`📚 ${e} - Sessão Contextual`;let o=s.title||e;if(!o||"Sessão de Estudos"===o||o.toLowerCase().includes("nova sessão")||o.toLowerCase().includes("sessão")&&o.length<20){const e=[];if(r&&e.push(r),n&&n!==r&&e.push(n),i&&"Geral"!==i&&"N/A"!==i&&i!==n&&e.push(i),s.knowledge_domain&&"general"!==s.knowledge_domain){const t={medico:"Médico",enfermagem:"Enfermagem",fisioterapia:"Fisioterapia",nutricao:"Nutrição",farmacia:"Farmácia",odontologia:"Odontologia"}[s.knowledge_domain]||s.knowledge_domain;e.push(t)}e.length>0?(o=e.join(" • "),o+=` (${s.total_questions}q)`):o=`${o} (${s.total_questions}q)`}else o=`${o} (${s.total_questions}q)`;return o.length>60&&(o=o.substring(0,57)+"..."),`📚 ${o}`}catch(s){return`📚 ${e} - Sessão Contextual`}};return{threads:e,currentThreadId:s,historyMessages:o,historyIsLoading:c,loadThreads:b,loadMessages:x,saveMessage:w,createNewThread:j,findOrCreateContextualThread:$,deleteThread:N,deleteAllThreads:_,clearCurrentConversation:C,getConversationHistory:S}};function q(e,t){const s=e.length,a=t.length;if(0===s)return a;if(0===a)return s;const r=Array(a+1).fill(null).map((()=>Array(s+1).fill(null)));for(let i=0;i<=s;i++)r[0][i]=i;for(let i=0;i<=a;i++)r[i][0]=i;for(let i=1;i<=a;i++)for(let a=1;a<=s;a++){const s=e[a-1]===t[i-1]?0:1;r[i][a]=Math.min(r[i-1][a]+1,r[i][a-1]+1,r[i-1][a-1]+s)}const n=Math.max(s,a);return(n-r[a][s])/n}const U={amoxicillin:["amoxicilina"],acetaminophen:["paracetamol"],ibuprofen:["ibuprofeno"],azithromycin:["azitromicina"],ceftriaxone:["ceftriaxona"],cephalexin:["cefalexina"],clarithromycin:["claritromicina"],sulfamethoxazole:["sulfametoxazol"],trimethoprim:["trimetoprima"],prednisolone:["prednisolona"],dexamethasone:["dexametasona"],albuterol:["salbutamol"],loratadine:["loratadina"],cetirizine:["cetirizina"],omeprazole:["omeprazol"],metronidazole:["metronidazol"],fluconazole:["fluconazol"]};class P{static generateTranslationMap(){if(this.translationMap.size>0)return this.translationMap;const e=new Map;for(const[t,s]of Object.entries(U))for(const a of s)e.set(t.toLowerCase(),a.toLowerCase());for(const[t,s]of Object.entries(U))for(const a of s)e.set(a.toLowerCase(),t.toLowerCase());return this.translationMap=e,e}static async loadMedicationsData(){const e=Date.now();if(this.medicationsCache.length>0&&e-this.lastMedicationsCacheUpdate<this.CACHE_DURATION)return this.medicationsCache;try{const{data:t,error:s}=await a.from("pedbook_medications").select("\n          id,\n          name,\n          slug,\n          brands,\n          description,\n          dosages:pedbook_medication_dosages(id),\n          instructions:pedbook_medication_instructions!inner(id)\n        ").eq("instructions.is_published",!0).order("name");if(s)throw s;const r=(t||[]).map((e=>({id:e.id,name:e.name,slug:e.slug,brands:e.brands||"",description:e.description||"",has_calculation:e.dosages&&e.dosages.length>0,has_bula:e.instructions&&e.instructions.length>0})));return this.medicationsCache=r,this.lastMedicationsCacheUpdate=e,this.medicationsCache}catch(t){return[]}}static async loadConductsData(){const e=Date.now();if(this.conductsCache.length>0&&e-this.lastConductsCacheUpdate<this.CACHE_DURATION)return this.conductsCache;try{const{data:t,error:s}=await a.from("pedbook_conducts_summaries").select("\n          id,\n          title,\n          slug,\n          topic_id,\n          pedbook_conducts_topics (\n            name,\n            slug,\n            pedbook_conducts_categories (\n              name,\n              slug\n            )\n          )\n        ").eq("published",!0).order("title");if(s)throw s;const r=(t||[]).map((e=>({id:e.id,title:e.title,slug:e.slug,topic_id:e.topic_id||"",topic_name:e.pedbook_conducts_topics?.name||"",topic_slug:e.pedbook_conducts_topics?.slug||"",category_name:e.pedbook_conducts_topics?.pedbook_conducts_categories?.name||"",category_slug:e.pedbook_conducts_topics?.pedbook_conducts_categories?.slug||""})));return this.conductsCache=r,this.lastConductsCacheUpdate=e,this.conductsCache}catch(t){return[]}}static getMedicationVariations(e){const t=[],s=e.toLowerCase(),a={"furoato de mometasona":["mometasona"],budesonida:["budesonide"],montelucaste:["montelukast"],loratadina:["loratadine"],cetirizina:["cetirizine"],desloratadina:["desloratadine"],dipirona:["metamizol","metamizole"],paracetamol:["acetaminophen"],ibuprofeno:["ibuprofen"]};if(a[s]&&t.push(...a[s]),s.includes(" de ")){const e=s.split(" de ");e.length>1&&t.push(e[1])}const r=["acetato de","cloridrato de","sulfato de","furoato de"];for(const n of r)s.startsWith(n)&&t.push(s.replace(n,"").trim());return t}static isGenericWord(e){return["oral","sódio","potássio","cálcio","magnésio","ferro","zinco","vitamina","ácido","base","sal","água","solução","comprimido","cápsula","xarope","gotas","spray","gel","creme","pomada"].includes(e.toLowerCase())||e.length<4}static findMentionedMedications(e,t){const s=[],a=[],r=e.toLowerCase(),n=r.match(/\b[a-záàâãéèêíìîóòôõúùûç]+\b/g)||[],i=this.findCompoundMedications(e,t);s.push(...i);for(const o of t){if(s.some((e=>e.id===o.id))||a.some((e=>e.id===o.id)))continue;let e=!1;if(!new RegExp(`\\b${o.name.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r)||this.isPartOfCompound(o.name,r)){if(!e){const t=this.getMedicationVariations(o.name);for(const a of t)if(n.includes(a.toLowerCase())){s.push(o),e=!0;break}}if(!e){const t=o.name.toLowerCase().split(/\s+/),s=t[t.length-1];if(s.length>=4&&n.includes(s)&&!this.isGenericWord(s)){a.push(o),e=!0;continue}}if(!e){const t=this.generateTranslationMap();for(const a of n)if(a.length>=4){const r=t.get(a);if(r&&r===o.name.toLowerCase()){s.push(o),e=!0;break}}}if(!e&&o.brands){const t=o.brands.split(",").map((e=>e.trim()));for(const a of t)if(a.length>2&&new RegExp(`\\b${a.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r)&&!this.isPartOfCompound(a,r)){s.push(o),e=!0;break}}if(!e){const t=this.generateTranslationMap();for(const s of n)if(s.length>=5){if(q(s,o.name.toLowerCase())>=.8){a.push(o),e=!0;break}for(const[r,n]of t.entries())if(n===o.name.toLowerCase()&&q(s,r)>=.8){a.push(o),e=!0;break}if(e)break}}}else s.push(o),e=!0}return{directMentions:s.filter(((e,t,s)=>t===s.findIndex((t=>t.id===e.id)))),suggestions:a.filter(((e,t,s)=>t===s.findIndex((t=>t.id===e.id)))).slice(0,3)}}static generateConductVariations(e){const t=[],s=e.toLowerCase();t.push(s);const a=e.replace(/\s*\([^)]*\)/g,"").trim().toLowerCase();a!==s&&t.push(a);const r=s.normalize("NFD").replace(/[\u0300-\u036f]/g,"");r!==s&&t.push(r),s.includes("-")&&t.push(s.replace(/-/g," ")),s.includes(" ")&&t.push(s.replace(/\s+/g,"-"));const n=e.match(/\b[\w]+-[\w]+-[\w]+\b/g);n&&n.forEach((e=>{const s=e.toLowerCase();t.push(s),t.push(s.replace(/-/g," ")),t.push(s.replace(/-/g,""))}));const i=s.match(/\b[a-záàâãéèêíìîóòôõúùûç]{4,}\b/g)||[];return t.push(...i),[...new Set(t)]}static async searchConductsInDatabase(e){try{const t=this.detectMainTopic(e),s=this.extractRelevantTerms(e),r=[...t,...s];if(0===r.length)return[];const n=r.sort(((e,s)=>t.includes(e)&&!t.includes(s)?-1:!t.includes(e)&&t.includes(s)?1:e.includes("-")&&!s.includes("-")?-1:!e.includes("-")&&s.includes("-")?1:s.length-e.length)).slice(0,8).map((e=>`title.ilike.%${e}%`)).join(","),{data:i,error:o}=await a.from("pedbook_conducts_summaries").select("\n          id,\n          title,\n          slug,\n          topic_id,\n          pedbook_conducts_topics (\n            name,\n            slug,\n            pedbook_conducts_categories (\n              name,\n              slug\n            )\n          )\n        ").eq("published",!0).or(n).limit(10);if(o)throw o;return(i||[]).map((e=>({id:e.id,title:e.title,slug:e.slug,topic_id:e.topic_id||"",topic_name:e.pedbook_conducts_topics?.name||"",topic_slug:e.pedbook_conducts_topics?.slug||"",category_name:e.pedbook_conducts_topics?.pedbook_conducts_categories?.name||"",category_slug:e.pedbook_conducts_topics?.pedbook_conducts_categories?.slug||""})))}catch(t){return[]}}static detectMainTopic(e){const t=e.toLowerCase(),s=[],a=t.substring(0,200),r=[/\b(pneumonia|broncopneumonia)\b/g,/\b(bronquiolite)\b/g,/\b(asma|crise\s+asmática)\b/g,/\b(covid|coronavirus|sars-cov-2)\b/g,/\b(tuberculose|tb)\b/g,/\b(meningite)\b/g,/\b(sepse|sepsis|choque\s+séptico)\b/g,/\b(diabetes|diabética|cetoacidose)\b/g,/\b(hipertensão|hipertensiva)\b/g,/\b(insuficiência\s+cardíaca|ic)\b/g,/\b(avc|acidente\s+vascular)\b/g,/\b(infarto|iam)\b/g,/\b(itu|infecção\s+urinária)\b/g,/\b(gastroenterite|diarreia)\b/g,/\b(alergia\s+alimentar|aplv)\b/g,/\b(dermatite\s+atópica)\b/g,/\b(varicela|catapora)\b/g,/\b(mão-pé-boca|hand-foot-mouth)\b/g,/\b(febre\s+reumática)\b/g,/\b(kawasaki)\b/g];for(const i of r){const e=t.match(i);e&&s.push(...e.map((e=>e.trim())))}const n=a.match(/\b[a-záàâãéèêíìîóòôõúùûç]{4,}\b/g)||[];return s.push(...n.slice(0,5)),[...new Set(s)]}static scoreAndPrioritizeConducts(e,t){const s=e.toLowerCase();s.match(/\b[a-záàâãéèêíìîóòôõúùûç]{3,}\b/g);const a=this.detectMainTopic(e);return t.map((t=>{let r=0;const n=t.title.toLowerCase(),i=n.match(/\b[a-záàâãéèêíìîóòôõúùûç]{3,}\b/g)||[];for(const e of a)if(n.includes(e.toLowerCase())){r+=2;break}s.includes(n)&&(r+=1);const o=t.title.replace(/\s*\([^)]*\)/g,"").trim().toLowerCase();o!==n&&s.includes(o)&&(r+=.9);const l=n.match(/\b[\w]+-[\w]+-[\w]+\b/g)||[];for(const e of l){s.includes(e)&&(r+=1.2);const t=e.replace(/-/g," ");s.includes(t)&&(r+=1);const a=e.replace(/-/g,"");s.includes(a)&&(r+=.9)}for(const e of l){const t=s.substring(0,100);(t.includes(e)||t.includes(e.replace(/-/g," ")))&&(r+=.5)}const c=s.indexOf(n);c>=0&&(r+=.3*Math.max(0,1-c/s.length));let d=0;for(const e of i)if(e.length>=4){const t=(s.match(new RegExp(`\\b${e}\\b`,"g"))||[]).length;t>0&&(d++,r+=Math.min(.1*t,.3))}i.length>0&&(r+=d/i.length*.4);for(const e of i)if(e.length>=4){const t=new RegExp(`\\b${e}\\b`,"gi"),a=(s.match(t)||[]).length;a>1&&(r+=Math.min(.3*a,1))}const m=["manejo","tratamento","diagnóstico","síndrome","doença","conduta","abordagem","clínica"];for(const e of m)s.includes(e)&&n.includes(e)&&(r+=.2);const u=e.split("\n")[0].toLowerCase();return(u.includes(n)||n.includes(u.substring(0,20)))&&(r+=1.5),{conduct:t,score:r}})).sort(((e,t)=>t.score-e.score))}static extractRelevantTerms(e){const t=[],s=e.toLowerCase(),a=e.match(/\b[\w]+-[\w]+-[\w]+\b/g);a&&a.forEach((e=>{const s=e.toLowerCase();t.push(s),t.push(s.replace(/-/g," ")),t.push(s.replace(/-/g,""))}));const r=s.match(/\b[a-záàâãéèêíìîóòôõúùûç]{4,}\b/g)||[],n=["para","com","uma","dos","das","que","não","mais","como","pode","deve","será","está","são","tem","ter","fazer","usar","oral","dose","dias","horas","anos","idade","peso"],i=r.filter((e=>e.length>=4&&!n.includes(e)&&!t.includes(e)));t.push(...i);const o=t.map((e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")));return[...new Set([...t,...o])]}static async findMentionedConducts(e,t){const s=[],a=[],r=e.toLowerCase(),n=r.match(/\b[a-záàâãéèêíìîóòôõúùûç]+\b/g)||[],i=await this.searchConductsInDatabase(e),o=this.scoreAndPrioritizeConducts(e,i);for(const d of o)d.score>=1?s.some((e=>e.id===d.conduct.id))||s.push(d.conduct):d.score>=.4&&(a.some((e=>e.id===d.conduct.id))||a.push(d.conduct));for(const d of t){if(s.some((e=>e.id===d.id))||a.some((e=>e.id===d.id)))continue;let e=!1;if(new RegExp(`\\b${d.title.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r)){s.push(d),e=!0;continue}const t=d.title.replace(/\s*\([^)]*\)/g,"").trim();if(t!==d.title&&new RegExp(`\\b${t.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r))s.push(d),e=!0;else if(!e&&d.topic_name&&new RegExp(`\\b${d.topic_name.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r))s.push(d),e=!0;else if(!e&&d.category_name&&new RegExp(`\\b${d.category_name.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r))a.push(d),e=!0;else{if(!e){const t=this.generateConductVariations(d.title);for(const a of t)if(a.split(/[\s-]+/).length>=3){if(r.includes(a)){s.push(d),e=!0;break}}else if(a.length>=4&&new RegExp(`\\b${a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"i").test(r)){s.push(d),e=!0;break}}if(!e){const t=d.title.toLowerCase().split(/\s+/).filter((e=>e.length>=4)),s=t.filter((e=>n.includes(e)));if(t.length>0&&s.length>=Math.ceil(.5*t.length)){a.push(d),e=!0;continue}}if(!e)for(const t of n)if(t.length>=5&&q(t,d.title.toLowerCase())>=.7){a.push(d),e=!0;break}}}const l=s.filter(((e,t,s)=>t===s.findIndex((t=>t.id===e.id)))).slice(0,3),c=a.filter(((e,t,s)=>t===s.findIndex((t=>t.id===e.id)))).filter((e=>!l.some((t=>t.id===e.id)))).slice(0,2);return{directMentions:l,suggestions:c}}static findCompoundMedications(e,t){const s=[];e.toLowerCase();const a=[/amoxicilina?\s*(?:com|with|\+)\s*clavulanato?/gi,/amoxicillin?\s*(?:com|with|\+)\s*clavulanic?\s*acid?/gi,/amoxicillin?\s*clavulanate?/gi,/sulfametoxazol?\s*(?:com|with|\+)\s*trimetoprima?/gi,/sulfamethoxazole?\s*(?:com|with|\+)\s*trimethoprim?/gi,/bactrim/gi,/septra/gi];for(const r of a){const a=e.match(r);if(a)for(const e of a){const a=this.findSpecificCompound(e,t);a&&!s.some((e=>e.id===a.id))&&s.push(a)}}return s}static findSpecificCompound(e,t){const s=e.toLowerCase();return s.includes("amoxicil")&&(s.includes("clavul")||s.includes("clavul"))?t.find((e=>"amoxicilina-clavulanato"===e.slug))||null:s.includes("sulfamet")&&s.includes("trimet")?t.find((e=>"sulfametoxazol-trimetoprima"===e.slug))||null:(s.includes("bactrim")||s.includes("septra"))&&t.find((e=>"sulfametoxazol-trimetoprima"===e.slug))||null}static isPartOfCompound(e,t){const s=e.toLowerCase();return[`${s}\\s+com\\s+`,`${s}\\s*\\+\\s*`,`\\s+com\\s+${s}`,`\\s*\\+\\s*${s}`].some((e=>new RegExp(e,"i").test(t)))}static generateMedicationLinks(e){const t=[];return e.has_calculation&&t.push(`🧮 [Calcular dosagem](/medicamentos/${e.slug})`),e.has_bula&&t.push(`📋 [Bula profissional](/bulas-profissionais/${e.slug})`),t.join(" • ")}static async enhanceResponse(e){try{const[t,s]=await Promise.all([this.loadMedicationsData(),this.loadConductsData()]),a=this.findMentionedMedications(e,t),{directMentions:r,suggestions:n}=a,i=await this.findMentionedConducts(e,s),{directMentions:o,suggestions:l}=i,c=r.filter((e=>e.has_calculation||e.has_bula)),d=n.filter((e=>e.has_calculation||e.has_bula)),m=o,u=l,g=c.length+d.length+m.length+u.length;if(0===g)return{medications:[],conducts:[],enhancedResponse:e,showDialog:!1};const h=`\n\n---\n\n**RAG_RESOURCES_BUTTON:${JSON.stringify({count:g,directMentions:c.map((e=>({id:e.id,name:e.name,slug:e.slug,has_calculation:e.has_calculation,has_bula:e.has_bula,brands:e.brands,description:e.description}))),suggestions:d.map((e=>({id:e.id,name:e.name,slug:e.slug,has_calculation:e.has_calculation,has_bula:e.has_bula,brands:e.brands,description:e.description}))),conductMentions:m.map((e=>({id:e.id,title:e.title,slug:e.slug,topic_slug:e.topic_slug,category_slug:e.category_slug}))),conductSuggestions:u.map((e=>({id:e.id,title:e.title,slug:e.slug,topic_slug:e.topic_slug,category_slug:e.category_slug})))})}**`;return{medications:[...c,...d],conducts:[...m,...u],enhancedResponse:e+h,showDialog:!0,dialogData:{directMentions:c,suggestions:d,conductMentions:m,conductSuggestions:u,title:`Recursos encontrados (${g})`}}}catch(t){return{medications:[],conducts:[],enhancedResponse:e,showDialog:!1}}}static async searchMedications(e,t=5){try{const s=await this.loadMedicationsData(),a=e.toLowerCase();return s.filter((e=>e.name.toLowerCase().includes(a)||e.brands&&e.brands.toLowerCase().includes(a))).slice(0,t)}catch(s){return[]}}static clearCache(){this.medicationsCache=[],this.lastCacheUpdate=0}}t(P,"medicationsCache",[]),t(P,"conductsCache",[]),t(P,"lastMedicationsCacheUpdate",0),t(P,"lastConductsCacheUpdate",0),t(P,"CACHE_DURATION",18e5),t(P,"SIMILARITY_THRESHOLD",.8),t(P,"translationMap",new Map);const H=e=>{const[t,s]=y.useState([]),[r,i]=y.useState(!1),[o,l]=y.useState(!1),[c,d]=y.useState(null),m=y.useRef(null),u=z(),g=e?.currentThreadId??u.currentThreadId,h=e?.saveToHistory??u.saveMessage,p=e?.createNewThread??u.createNewThread,f=e?.getConversationHistory??u.getConversationHistory,{deleteThread:b,threads:x,clearCurrentConversation:w,loadMessages:v,loadThreads:k}=u,[j,N]=y.useState(!1),[_,C]=y.useState(null),S=y.useCallback((async e=>await R(`loadMessagesFromThread_${e.slice(0,8)}`,(async()=>{const t=await v(e);if(t&&t.length>0){const a=t.map((e=>({id:e.id,content:e.content,isUser:e.isUser,timestamp:e.timestamp,isStreaming:!1})));s(a),L.threadLoaded(e,a.length),D.updateThreadState(e,a.length)}else s([]),D.updateThreadState(e,0);C(e),await new Promise((e=>setTimeout(e,50)))}))),[v,g]),$=y.useCallback((e=>{s((t=>[...t,e]))}),[]),M=y.useCallback(((e,t)=>{s((s=>s.map((s=>s.id===e?{...s,...t}:s))))}),[]),E=y.useCallback((async(e,t,s,a,o)=>{if("string"!=typeof e)return;if(!e.trim()||r)return;n.chatSendMessageCalled({content:e.substring(0,50)+"...",userId:t,hasUserId:!!t,forceThreadId:s,forceNewThreadDirect:a});const c={localStorage_userId:localStorage.getItem("auth_user_id"),localStorage_profile:localStorage.getItem("auth_profile")?"exists":"missing"};return n.chatAuthStateCheck(c),await R("sendMessage",(async()=>{m.current&&m.current.abort(),m.current=new AbortController;let r=null;if(r=!0===a?null:s||g,!r&&(n.chatCreateThreadCalled({content:e.substring(0,50)+"..."}),r=await p(e.trim()),n.chatCreateThreadResult({threadId:r,success:!!r}),!r))return void L.error("sendMessage","Failed to create new thread");const c=new Date,u={id:Date.now().toString(),content:e.trim(),isUser:!0,timestamp:c,images:o?Array.isArray(o)?o:[o]:void 0};if($(u),!(await h({id:u.id,content:u.content,isUser:!0,timestamp:u.timestamp,images:u.images},r)))return L.error("saving user message to history","Failed to save"),d("Erro ao salvar mensagem"),void i(!1);await new Promise((e=>setTimeout(e,100)));const b=await f(r);i(!0),l(!0),d(null),await I(e,r,b,t,o)}))}),[r,$,M]),T=e=>{let t=e.trim();const s=t.split(/\n\s*\n/).filter((e=>e.trim())),a=[];for(let l=0;l<s.length;l++){const e=s[l].trim();if(e.match(/^\*\*[^*]+\*\*\s*$/)&&l+1<s.length){const t=s[l+1].trim();a.push(e+"\n\n"+t),l++}else e.length>50&&a.push(e)}if(a.length>=2)return a.slice(0,2);const r=t.split(/\n\s*\n/).filter((e=>{const t=e.trim();return t.length>50&&!t.match(/^\*\*[^*]+\*\*\s*$/)}));if(r.length>=2)return r.slice(0,2);const n=t.split(/([🔍💡🧠📋🎯⚡✅🎓📚❓👩‍⚕️🏗️])/),i=[];for(let l=1;l<n.length;l+=2)n[l]&&n[l+1]&&i.push(n[l]+n[l+1].trim());if(i.length>=2)return i.slice(0,2);const o=t.split(/[.!?]+/).filter((e=>e.trim()));if(o.length>=4)return[o.slice(0,2).join(". ")+".",o.slice(2,4).join(". ")+"."];if(t.length>100){const e=Math.floor(t.length/2);let s=e;for(let r=e;r<t.length&&r<e+100;r++){if("."===t[r]&&" "===t[r+1]){s=r+1;break}if("\n"===t[r]){s=r;break}if(" "===t[r]&&r>e+20){s=r;break}}const a=[t.substring(0,s).trim(),t.substring(s).trim()].filter((e=>e.length>20));return a.length>=2?a:[t]}return[t]},I=async(e,t,r,n,o)=>{const c=(Date.now()+1).toString();$({id:c,content:"",isUser:!1,timestamp:new Date,isStreaming:!0,isThinking:!0});try{const s=a.supabaseUrl,i=a.supabaseKey,l={message:e,userId:n,history:r,image_url:o},d=await fetch(`${s}/functions/v1/dr-will-pedbook`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(l),signal:m.current.signal});if(!d.ok){const e=await d.text();throw new Error(`Dr. Will API error: ${d.status} - ${e}`)}const p=d.body?.getReader();if(!p)throw new Error("Dr. Will stream não disponível");const f=new TextDecoder;let b="",x="",y=0,w=!1;for(;;){const{done:e,value:t}=await p.read();if(y++,e)break;const s=f.decode(t,{stream:!0}).split("\n");for(const a of s)if(a.startsWith("data: "))try{const e=a.slice(6).trim();if(!e)continue;const t=JSON.parse(e);if(t.done)break;if(t.content&&(!0===t.isThinking?(b+=t.content,M(c,{content:b,isStreaming:!0,isThinking:!0})):(!w&&(w=!0,b)&&T(b),x+=t.content,M(c,{content:x,isStreaming:!0,isThinking:!1}))),t.error)throw new Error(t.error)}catch(u){}}let v=x;if(!x.includes("RAG_RESOURCES_BUTTON:")&&!x.includes("**RAG_RESOURCES_BUTTON:"))try{const e=await P.enhanceResponse(x);e&&e.enhancedResponse!==x&&(v=e.enhancedResponse)}catch(g){}M(c,{content:v,isStreaming:!1,isThinking:!1}),v&&t&&(await h({id:c,content:v,isUser:!1,timestamp:new Date},t)||L.error("saving AI response to history","Failed to save"))}catch(g){if("AbortError"===g.name)s((e=>e.filter((e=>e.id!==c))));else{let e="Desculpe, ocorreu um erro inesperado. Tente novamente.";g.message?.includes("API error")?e="Erro de conexão com Dr. Will. Verifique sua internet e tente novamente.":g.message?.includes("timeout")?e="Dr. Will está demorando para responder. Tente uma pergunta mais simples.":g.message?.includes("rate limit")?e="Muitas perguntas simultâneas. Aguarde um momento antes de tentar novamente.":g.message?.includes("GEMINI_API_KEY")&&(e="Dr. Will está temporariamente indisponível. Tente novamente mais tarde."),M(c,{content:`❌ **Erro**: ${e}\n\n💡 **Dicas para tentar novamente:**\n- Verifique sua conexão com a internet\n- Tente uma pergunta mais específica\n- Aguarde alguns segundos antes de tentar novamente\n\nSe o problema persistir, entre em contato com o suporte.`,isStreaming:!1}),d(e)}}finally{i(!1),l(!1)}},O=y.useCallback((()=>{s([]),d(null)}),[]),A=y.useCallback((e=>{s(e)}),[]),q=y.useCallback((()=>{m.current&&m.current.abort()}),[]),U=y.useCallback((async e=>{if(0===t.length)return null;try{const{data:s,error:r}=await a.from("chat_sessions").insert({user_id:e,messages:t,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).select().single();if(r)throw r;return s}catch(s){return null}}),[t]),H=y.useCallback((async e=>{try{const{data:t,error:r}=await a.from("chat_sessions").select("*").eq("id",e).single();if(r)throw r;return t&&t.messages&&s(t.messages),t}catch(t){return null}}),[]);return{messages:t,isLoading:r,isStreaming:o,error:c,sendMessage:E,clearMessages:O,setMessages:A,cancelRequest:q,saveChatSession:U,loadChatSession:H,addMessage:$,updateMessage:M,currentThreadId:g,clearCurrentConversation:w,loadMessages:S,createNewThread:p,deleteThread:b,threads:x,loadThreads:k}},B=e=>e?e.replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold text-indigo-800 dark:text-indigo-200 bg-indigo-50 dark:bg-indigo-900/30 px-1 rounded">$1</strong>').replace(/\*(.*?)\*/g,'<em class="italic text-purple-700 dark:text-purple-300">$1</em>').replace(/\n\n/g,'</p><p class="mb-2 text-gray-700 dark:text-gray-300 leading-relaxed">').replace(/\n/g,"<br>").replace(/^(.*)$/,'<p class="mb-2 text-gray-700 dark:text-gray-300 leading-relaxed">$1</p>'):"",W=(e,t=!1)=>e?e.includes('<h3 class="font-bold')||e.includes('<div class="ml-')||e.includes('<p class="mb-')||!t&&e.length<100?e:F(e,t):"",F=(e,t)=>{if((e.match(/\[(?:TABLE|MERMAID|TEXT)_RESPONSE\]/g)||[]).length>1){const s=(e=>{const t=[],s=e.split(/(\[(?:TABLE|MERMAID|TEXT)_RESPONSE\])/);let a="text",r="";for(let n=0;n<s.length;n++){const e=s[n];"[TABLE_RESPONSE]"===e?(r.trim()&&t.push({type:a,content:r.trim()}),a="table",r=""):"[MERMAID_RESPONSE]"===e?(r.trim()&&t.push({type:a,content:r.trim()}),a="mermaid",r=""):"[TEXT_RESPONSE]"===e?(r.trim()&&t.push({type:a,content:r.trim()}),a="text",r=""):r+=e}return r.trim()&&t.push({type:a,content:r.trim()}),t})(e);return s.map(((e,s)=>{switch(e.type){case"table":return K(e.content,t);case"mermaid":return se(e.content,t);default:return J(e.content)}})).join("")}const s=e.includes("[TABLE_RESPONSE]"),a=e.includes("[MERMAID_RESPONSE]");e.includes("[TEXT_RESPONSE]");let r=e.replace(/\[MERMAID_RESPONSE\]/g,"").replace(/\[TABLE_RESPONSE\]/g,"").replace(/\[TEXT_RESPONSE\]/g,"").trim();const{type:n,body:i}=V(r);if(t&&i.length<50)return`<p class="mb-3">${Y(i)}</p>`;const o=X(i),l=te(i);return a||"mermaid"===n||l?se(i,t):s||"table"===n||o||G(i)?K(i,t):J(i)},V=e=>{let t=e.replace(/\[MERMAID_RESPONSE\]/g,"").replace(/\[TABLE_RESPONSE\]/g,"").replace(/\[TEXT_RESPONSE\]/g,"").trim();if(e.includes("[MERMAID_RESPONSE]")||te(e)){const s=e.match(/```\s*(mindmap|graph|flowchart|sequenceDiagram)[\s\S]*?```/);return{type:"mermaid",body:t,mermaidCode:s?s[0]:void 0}}return e.includes("[TABLE_RESPONSE]")||X(e)?{type:"table",body:t}:{type:"text",body:t}},X=e=>{const t=e.split("\n").filter((e=>{const t=e.trim();return!(!t.includes("|")||t.split("|").length<3)&&(!(t.match(/^\s*\|[\s\-:]+\|\s*$/)||t.match(/^[\s\-:|]+$/)||t.match(/^\|[\s\-:]+\|$/))&&!((t.match(/-/g)||[]).length>.8*t.length))}));if(t.length<2)return!1;const s=t.map((e=>e.split("|").length)),a=s[0];return s.filter((e=>Math.abs(e-a)<=1)).length>=.8*t.length},G=e=>{const t=e.split("\n");let s=0,a=0;for(const r of t){const e=r.trim();e.includes("|")&&e.split("|").length>=3?e.match(/^\s*\|[\s\-:]+\|\s*$/)||e.match(/^[\s\-:|]+$/)||e.match(/^\|[\s\-:]+\|$/)?s=0:(s++,a=Math.max(a,s)):s=0}return a>=3},Y=e=>{if(!e)return"";let t=e;return t=t.replace(/\*\*([^*\n]+?)\*\*/g,'<strong class="font-semibold text-gray-900">$1</strong>'),t=t.replace(/^\*\*([^*\n]+?)\*\*/gm,'<strong class="font-semibold text-gray-900">$1</strong>'),t=t.replace(/\*\*([^*\n]+?)\*\*$/gm,'<strong class="font-semibold text-gray-900">$1</strong>'),t=t.replace(/(\*\*Suspeita de [^:]+:)/g,'<strong class="font-semibold text-gray-900">$1</strong>'),t=t.replace(/(^|[^*])\*([^*\n]+?)\*(?![*])/g,((e,t,s)=>"*"===t?e:t+`<em class="italic text-gray-700">${s}</em>`)),t=t.replace(/`([^`\n]+?)`/g,'<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>'),t=t.replace(/\s{3,}/g," "),t.trim()},J=e=>{if(!e||0===e.trim().length)return"";const t=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n").trim();if(t.length<20)return`<p class="mb-3">${Y(t)}</p>`;const s=t.split(/\n{2,}/),a=[];for(const n of s){if(!n.trim())continue;const e=n.split("\n"),t=[];for(let s=0;s<e.length;s++){const a=e[s],r=a.trim();if(""===r)continue;if(r.length<3&&!r.match(/^\d+\.$/))continue;const n=r.match(/^(\d+)\.\s+\*\*(.+)\*\*$/);if(n){const e=n[1],s=Y(n[2]);t.push(`<h3 class="font-bold text-lg text-gray-900 mt-6 mb-3">${e}. ${s}</h3>`);continue}if(r.startsWith("#### ")){const e=Y(r.substring(5));t.push(`<h4 class="text-base font-semibold text-gray-800 mt-4 mb-3">${e}</h4>`);continue}if(r.startsWith("### ")){const e=Y(r.substring(4));t.push(`<h3 class="font-bold text-lg text-gray-900 mt-6 mb-3">${e}</h3>`);continue}if(r.startsWith("## ")){const e=Y(r.substring(3));t.push(`<h2 class="font-bold text-xl text-gray-900 mt-6 mb-4">${e}</h2>`);continue}const i=a.match(/^(\s*)\*\s+(.+)$/);if(i){const e=i[1].length,s=i[2];let a="ml-4",r="text-gray-600",n="▪";4===e?a="ml-6":8===e?a="ml-10":e>=12&&(a="ml-14");const o=Y(s);t.push(`<div class="${a} mb-1 ${r}">${n} ${o}</div>`);continue}const o=r.match(/^(\d+)\.\s+(.+)$/);if(o){const e=o[1],s=Y(o[2]);o[2].includes("**")?t.push(`<div class="ml-4 mb-3"><span class="font-bold text-blue-700">${e}.</span> ${s}</div>`):t.push(`<div class="ml-4 mb-2"><span class="font-semibold text-blue-600">${e}.</span> ${s}</div>`)}else if(r.includes("**")&&r.endsWith(":")){const e=Y(r);t.push(`<div class="ml-8 mb-1 text-gray-700">${e}</div>`)}else{const e=Y(r);t.length>0&&t[t.length-1].includes("ml-")&&!r.match(/^[*\d]/)&&r.length<200?t.push(`<div class="ml-8 mb-1 text-gray-600">${e}</div>`):t.push(`<p class="mb-3">${e}</p>`)}}t.length>0&&a.push(t.join(""))}const r=a.join('<div class="mb-2"></div>');return r.length<50&&t.length>20?`<p class="mb-3">${Y(t)}</p>`:r.replace(/<div class="mb-1"><\/div>/g,"").replace(/(<\/strong>)(\s*)([A-Z])/g,"$1 $3").replace(/(\.)(\s*)(\*\*)/g,"$1 $3").replace(/\s{2,}/g," ")},K=(e,t)=>{const{beforeTable:s,tableContent:a,afterTable:r}=Z(e),n=[];if(s.trim()){const e=J(s);n.push(e)}if(a.length>0&&(t?n.push(Q()):n.push(ee(a))),r.trim()){const e=J(r);n.push(e)}return n.join("")},Z=e=>{const t=e.split("\n"),s=[];let a="",r="",n=-1,i=-1;for(let o=0;o<t.length;o++){const e=t[o].trim();if(e.includes("|")&&e.split("|").length>=3)e.match(/^\s*\|[\s\-:]+\|\s*$/)||e.match(/^[\s\-:|]+$/)||e.match(/^\|[\s\-:]+\|$/)||(-1===n&&(n=o),i=o,s.push(e));else{if(-1!==n&&""===e)continue;if(-1!==n&&s.length>0)break}}return-1!==n?(a=t.slice(0,n).join("\n"),r=t.slice(i+1).join("\n")):a=e,{beforeTable:a,tableContent:s,afterTable:r}},Q=()=>'\n    <div class="mb-4 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">\n      <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-3">\n        <div class="flex items-center gap-2">\n          <div class="w-4 h-4 bg-white bg-opacity-20 rounded animate-pulse"></div>\n          <span class="text-white font-semibold text-sm">Montando Tabela...</span>\n        </div>\n      </div>\n      <div class="p-3">\n        <div class="flex items-center justify-center py-4">\n          <div class="flex gap-1">\n            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>\n            <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>\n            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  ',ee=e=>{const t=e.filter((e=>{const t=e.trim();return!(!t.includes("|")||t.split("|").length<3)&&(!(t.match(/^\s*\|[\s\-:]+\|\s*$/)||t.match(/^[\s\-:|]+$/)||t.match(/^\|[\s\-:]+\|$/))&&!((t.match(/-/g)||[]).length>.8*t.length))}));if(0===t.length)return"";const s=(e=>{if(0===e.length)return[];const t=[];let s="",a=0;for(let r=0;r<e.length;r++){const n=e[r].trim(),i=n.split("|").map((e=>e.trim())).filter((e=>""!==e));if(0!==r){if(i.length>=a)s&&t.push(s),s=n;else if(i.length>0&&s){const e=s.split("|").map((e=>e.trim()));for(let t=e.length-1;t>=0;t--)if(e[t]&&""!==e[t]){e[t]+=" "+i.join(" ");break}s="| "+e.join(" | ")+" |"}}else a=i.length,t.push(n)}return s&&t.push(s),t})(t),a=s.map((e=>e.split("|").map((e=>{const t=e.trim();return Y(t)})).filter((e=>""!==e&&!e.match(/^[\s\-:]+$/))))).filter((e=>e.length>=2));if(0===a.length)return"";const r=a.filter((e=>{const t=e[0];return t&&t.length<200}));if(0===r.length)return"";const n=`table-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,i=`table-data-${n}`;return`\n    <div class="mb-4 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">\n      <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-3">\n        <div class="flex items-center justify-between">\n          <span class="text-white font-semibold">📊 Tabela Completa</span>\n          <button onclick="openTableDialog('${n}')" class="bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-600">\n            Ver Completa\n          </button>\n        </div>\n      </div>\n      <div class="p-3">\n        <div class="text-center text-gray-600 text-sm">\n          ${Math.max(0,r.length-1)} linhas de dados • Clique para expandir\n        </div>\n      </div>\n      <script type="application/json" id="${i}">\n        ${JSON.stringify(r)}\n      <\/script>\n    </div>\n  `},te=e=>{const t=e.toLowerCase().trim(),s=["mindmap","graph","flowchart","sequencediagram","classDiagram","stateDiagram","journey","gantt"].some((e=>t.includes(e.toLowerCase()))),a=/root\s*\(\([^)]+\)\)/.test(t),r=t.includes("mapa mental")||t.includes("mindmap")||t.includes("fluxograma")||t.includes("diagrama")||t.includes("esquema visual");return s||a||r},se=(e,t=!1)=>{if(!e||0===e.trim().length)return"";const s=(e=>{const t=e.toLowerCase();return t.includes("fluxograma")||t.includes("flowchart")?{type:"fluxograma",icon:"📊",title:"Fluxograma Interativo"}:t.includes("organograma")?{type:"organograma",icon:"🏢",title:"Organograma Interativo"}:t.includes("diagrama")?{type:"diagrama",icon:"📈",title:"Diagrama Interativo"}:t.includes("esquema")?{type:"esquema",icon:"🗂️",title:"Esquema Interativo"}:{type:"mapa mental",icon:"🧠",title:"Mapa Mental Interativo"}})(e);if(t&&e.length<100)return`\n      <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">\n        <div class="flex items-center gap-2">\n          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>\n          <span class="text-blue-700 font-medium">Gerando ${s.type}...</span>\n        </div>\n      </div>\n    `;const a=e.match(/```\s*(mindmap|graph|flowchart|sequenceDiagram)[\s\S]*?```/);let r="",n=e;if(a)r=a[0].replace(/```\s*(mindmap|graph|flowchart|sequenceDiagram)/,"").replace(/```$/,"").trim(),n=e.replace(a[0],"").trim();else{const t=e.split("\n"),s=[],a=[];let i=!1;for(const e of t){const t=e.trim().toLowerCase();t.startsWith("mindmap")||t.startsWith("graph")||t.startsWith("flowchart")?(i=!0,s.push(e)):i&&(""===t||e.startsWith("  ")||e.startsWith("\t"))?s.push(e):(i=!1,a.push(e))}s.length>0&&(r=s.join("\n").trim(),n=a.join("\n").trim())}n=n.replace(/```mermaid\s*```/g,"").replace(/```mermaid\s*\n\s*```/g,"").replace(/\n\s*\n\s*\n/g,"\n\n").trim(),r&&(r=(e=>{let t=e;t=t.replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'"),t=t.replace(/ç/g,"c").replace(/ã/g,"a").replace(/õ/g,"o").replace(/á/g,"a").replace(/é/g,"e").replace(/í/g,"i").replace(/ó/g,"o").replace(/ú/g,"u").replace(/â/g,"a").replace(/ê/g,"e").replace(/ô/g,"o").replace(/ü/g,"u").replace(/Ç/g,"C").replace(/Ã/g,"A").replace(/Õ/g,"O").replace(/Á/g,"A").replace(/É/g,"E").replace(/Í/g,"I").replace(/Ó/g,"O").replace(/Ú/g,"U").replace(/Â/g,"A").replace(/Ê/g,"E").replace(/Ô/g,"O");const s=t.split("\n"),a=[];let r=!1;for(let i=0;i<s.length;i++){let e=s[i];const t=e.trim();if("mindmap"===t&&r)break;if("mindmap"===t){r=!0,a.push("mindmap");continue}if(!t)continue;const n=e.match(/^(\s*)/),o=n?n[1].length:0,l=Math.floor(o/2),c="  ".repeat(l);let d=t;if(d=d.replace(/[()]/g,"").replace(/:/g,"").replace(/;/g,"").replace(/,/g,"").replace(/\//g," ou ").replace(/\+/g," mais ").replace(/-{2,}/g," ").replace(/\s+/g," ").trim(),d.toLowerCase().includes("root")||0===l&&1===i){let e=d;e=e.replace(/^root\s*/i,"");const t=e.match(/\(\((.+?)\)\)/)||e.match(/(.+)/),s=(t?t[1]||t[0]:"Topico Principal").replace(/[()]/g,"").trim();a.push(`  root((${s}))`)}else if(d&&d.length>0){d.length>30&&(d=d.substring(0,27)+"...");const e=d.replace(/\s+/g," ").trim();e&&a.push(c+e)}}if(a.length<2)return"mindmap\n  root((Mapa Mental))\n    Topico 1\n    Topico 2\n    Topico 3";if(a.length>50){const e=[];for(const t of a)(Math.floor((t.match(/^(\s*)/)?.[1]?.length||0)/2)<=2||e.length<45)&&e.push(t);a.splice(0,a.length,...e)}const n=[];for(let i=0;i<a.length;i++){const e=a[i];e.trim()&&(0===i&&"mindmap"!==e.trim()?(n.push("mindmap"),"mindmap"!==e.trim()&&n.push(e)):n.push(e))}return n.length<3?"mindmap\n  root((Mapa Mental))\n    Topico 1\n    Topico 2\n    Topico 3":n.join("\n")})(r));const i=`mermaid-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;let o="";return n&&n.length>0&&(o+=J(n)),r&&r.length>0&&(o+=t||r.length<30?`\n        <div class="mb-3 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 border border-purple-200 dark:border-purple-700 rounded-lg p-3">\n          <div class="flex items-center justify-between gap-3">\n            <span class="font-medium text-gray-800 dark:text-gray-200 flex-1 min-w-0">${s.icon} ${s.title}</span>\n            <button\n              disabled\n              class="bg-gray-400 text-white px-3 py-1.5 rounded-md text-sm font-medium cursor-not-allowed flex items-center gap-1.5 whitespace-nowrap flex-shrink-0"\n            >\n              <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>\n              Criando...\n            </button>\n          </div>\n        </div>\n      `:`\n        <div class="mb-3 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 border border-purple-200 dark:border-purple-700 rounded-lg p-3">\n          <div class="flex items-center justify-between gap-3">\n            <span class="font-medium text-gray-800 dark:text-gray-200 flex-1 min-w-0">${s.icon} ${s.title}</span>\n            <button\n              onclick="expandMermaidDiagram('${i}')"\n              class="bg-purple-600 hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-800 text-white px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 whitespace-nowrap flex-shrink-0"\n            >\n              Expandir\n            </button>\n          </div>\n          \x3c!-- Hidden element for modal data --\x3e\n          <div id="${i}" style="display: none;"></div>\n          <script type="application/json" id="${i}-code">\n            ${JSON.stringify(r)}\n          <\/script>\n        </div>\n      `),o},ae=({isOpen:e,onClose:t,mermaidCode:s,title:a})=>{const r=y.useRef(null),[n,c]=w.useState(1),[d,m]=w.useState(!0),[u,g]=w.useState(!1),[h,p]=w.useState({x:0,y:0}),[f,b]=w.useState({x:0,y:0}),x=(e=>{const t=e.toLowerCase();return t.includes("fluxograma")||t.includes("flowchart")?{icon:"📊",title:"Fluxograma Interativo"}:t.includes("organograma")?{icon:"🏢",title:"Organograma Interativo"}:t.includes("diagrama")?{icon:"📈",title:"Diagrama Interativo"}:t.includes("esquema")?{icon:"🗂️",title:"Esquema Interativo"}:{icon:"🧠",title:"Mapa Mental Interativo"}})(s),v=a||x.title;y.useEffect((()=>{e&&s&&r.current&&k()}),[e,s]);const k=async()=>{if(r.current){m(!0);try{let e=0;for(;void 0===window.mermaid&&e<10;)await new Promise((e=>setTimeout(e,200))),e++;if(void 0===window.mermaid)throw new Error("Mermaid.js não disponível após 10 tentativas");const t=window.mermaid;let a=s;const i=document.documentElement.classList.contains("dark");t.initialize({startOnLoad:!1,theme:i?"dark":"default",themeVariables:i?{primaryColor:"#8b5cf6",primaryTextColor:"#f3f4f6",primaryBorderColor:"#6366f1",lineColor:"#9ca3af",background:"#1f2937",mainBkg:"#374151",secondBkg:"#4b5563",tertiaryColor:"#6b7280"}:{primaryColor:"#8b5cf6",primaryTextColor:"#1f2937",primaryBorderColor:"#6366f1",lineColor:"#6b7280",background:"#ffffff"},mindmap:{padding:60,maxNodeSizeX:350,maxNodeSizeY:180,useMaxWidth:!1,htmlLabels:!0,nodeSpacing:150,levelSpacing:200,curve:"basis"},logLevel:"error"});const o=`mermaid-modal-${Date.now()}`,l=await t.render(o,a);if(r.current){r.current.innerHTML=l.svg;const e=r.current.querySelector("svg");if(e){e.style.maxWidth="none",e.style.maxHeight="none",e.style.width="auto",e.style.height="auto",e.style.display="block",e.style.margin="0 auto",e.style.transform=`scale(${n}) translate(${f.x}px, ${f.y}px)`,e.style.transformOrigin="center";const t=e.getAttribute("viewBox");if(t){const[s,a,r,n]=t.split(" ").map(Number),i=30;e.setAttribute("viewBox",`${s-i} ${a-i} ${r+2*i} ${n+2*i}`)}const s=document.documentElement.classList.contains("dark")?"#1f2937":"white",a=e.querySelector('rect[fill="white"], rect[fill="#1f2937"]');if(a)a.setAttribute("fill",s);else{const t=document.createElementNS("http://www.w3.org/2000/svg","rect");t.setAttribute("width","100%"),t.setAttribute("height","100%"),t.setAttribute("fill",s),t.setAttribute("x","0"),t.setAttribute("y","0"),e.insertBefore(t,e.firstChild)}}}}catch(e){try{const e=`mindmap\n  root((${a||"Mapa Mental"}))\n    Informação 1\n    Informação 2\n    Informação 3`,t=window.mermaid,s=`mermaid-fallback-${Date.now()}`,i=await t.render(s,e);if(r.current){r.current.innerHTML=i.svg;const e=r.current.querySelector("svg");e&&(e.style.maxWidth="100%",e.style.height="auto",e.style.display="block",e.style.margin="0 auto",e.style.transform=`scale(${n})`,e.style.transformOrigin="center")}}catch(t){r.current&&(r.current.innerHTML=`\n            <div class="text-center text-red-500 p-8">\n              <p class="mb-4">Não foi possível renderizar o mapa mental</p>\n              <p class="text-sm text-gray-600 mb-4">O conteúdo é muito complexo para visualização</p>\n              <details class="text-left">\n                <summary class="cursor-pointer text-blue-600 hover:text-blue-800">Ver código original</summary>\n                <pre class="text-xs text-gray-600 bg-gray-100 p-4 rounded overflow-auto max-h-40 mt-2">${s}</pre>\n              </details>\n            </div>\n          `)}}finally{m(!1)}}},S=e=>{if(r.current){const t=r.current.querySelector("svg");t&&(t.style.transform=`scale(${e}) translate(${f.x}px, ${f.y}px)`)}},$=e=>{g(!0),p({x:e.clientX-f.x,y:e.clientY-f.y})},M=e=>{if(!u)return;const t={x:e.clientX-h.x,y:e.clientY-h.y};if(b(t),r.current){const e=r.current.querySelector("svg");e&&(e.style.transform=`scale(${n}) translate(${t.x}px, ${t.y}px)`)}},E=()=>{g(!1)};return w.useEffect((()=>{e&&(b({x:0,y:0}),c(1))}),[e]),e?j.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4",children:j.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg max-w-6xl max-h-[90vh] w-full flex flex-col",children:[j.jsxs("div",{className:"border-b border-gray-200 dark:border-gray-600",children:[j.jsxs("div",{className:"flex items-center justify-between p-3 sm:p-4",children:[j.jsxs("div",{className:"flex-1 min-w-0",children:[j.jsxs("h3",{className:"text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 truncate",children:[x.icon," ",v]}),j.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1 hidden sm:block",children:"🖱️ Arraste para navegar • 🔍 Use os botões para zoom"})]}),j.jsx(i,{size:"sm",variant:"outline",onClick:t,className:"ml-2 flex-shrink-0",children:j.jsx(o,{className:"h-4 w-4"})})]}),j.jsxs("div",{className:"flex items-center justify-between px-3 pb-3 sm:px-4 sm:pb-4",children:[j.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 sm:hidden",children:"🖱️ Arraste • 🔍 Zoom"}),j.jsxs("div",{className:"flex items-center gap-1 sm:gap-2",children:[j.jsx(i,{size:"sm",variant:"outline",onClick:()=>{const e=Math.max(n-.2,.5);c(e),S(e)},disabled:n<=.5,children:j.jsx(N,{className:"h-4 w-4"})}),j.jsxs("span",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-300 min-w-[2.5rem] sm:min-w-[3rem] text-center",children:[Math.round(100*n),"%"]}),j.jsx(i,{size:"sm",variant:"outline",onClick:()=>{const e=Math.min(n+.2,5);c(e),S(e)},disabled:n>=5,children:j.jsx(_,{className:"h-4 w-4"})}),j.jsxs(i,{size:"sm",variant:"outline",onClick:()=>{if(!r.current)return;const e=r.current.querySelector("svg");if(e)try{const t=e.cloneNode(!0);t.style.transform="none",t.style.maxWidth="none",t.style.maxHeight="none";const s=document.documentElement.classList.contains("dark")?"#1f2937":"white",a=document.createElementNS("http://www.w3.org/2000/svg","rect");a.setAttribute("width","100%"),a.setAttribute("height","100%"),a.setAttribute("fill",s),t.insertBefore(a,t.firstChild);const r=(new XMLSerializer).serializeToString(t),n=new Blob([r],{type:"image/svg+xml;charset=utf-8"}),i=URL.createObjectURL(n),o=document.createElement("a");o.href=i,o.download=`mapa-mental-${Date.now()}.svg`,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(i),l({title:"Download concluído!",description:"Mapa mental salvo como SVG."})}catch(t){l({title:"Erro no download",description:"Não foi possível baixar o mapa mental.",variant:"destructive"})}},className:"flex items-center gap-1 sm:gap-2 ml-1 sm:ml-2",title:"Baixar mapa mental como SVG",children:[j.jsx(C,{className:"h-4 w-4"}),j.jsx("span",{className:"hidden sm:inline",children:"SVG"})]})]})]})]}),j.jsxs("div",{className:"flex-1 overflow-auto p-6",children:[d&&j.jsxs("div",{className:"flex items-center justify-center h-64",children:[j.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"}),j.jsx("span",{className:"ml-2 text-gray-600 dark:text-gray-300",children:"Renderizando mapa mental..."})]}),j.jsx("div",{ref:r,className:"text-center min-h-[400px] flex items-center justify-center overflow-hidden relative",style:{cursor:u?"grabbing":"grab",userSelect:"none",touchAction:"none"},onMouseDown:$,onMouseMove:M,onMouseUp:E,onMouseLeave:()=>{g(!1)},onTouchStart:e=>{const t=e.touches[0];$({clientX:t.clientX,clientY:t.clientY})},onTouchMove:e=>{const t=e.touches[0];M({clientX:t.clientX,clientY:t.clientY})},onTouchEnd:e=>{E()}})]})]})}):null},re=({content:e,isStreaming:t,formatThinkingContent:s,compact:a=!1})=>{const[r,n]=y.useState([]),[i,o]=y.useState(0),[l,d]=y.useState(!1),[m,u]=y.useState("");y.useEffect((()=>{!e&&m&&(n([]),o(0),u(""))}),[e,m]);const g=y.useCallback((e=>{if(!e)return[];const t=[...e.matchAll(/\*\*([^*\n]+)\*\*/g)];if(0===t.length)return[e];const s=[];return t.forEach(((a,r)=>{const n=a.index,i=t[r+1],o=i?i.index:e.length,l=e.substring(n,o).trim();l.length>10&&s.push(l)})),s.length>0?s:[e]}),[m]);return y.useEffect((()=>{if(!e)return;const t=g(e);if(0!==t.length){if(t.length>r.length){const e=r.length,s=setTimeout((()=>{n((s=>s.length<t.length?[...s,t[e]]:s)),o(e+1)}),0===e?200:800);return()=>clearTimeout(s)}e!==m&&u(e)}}),[e,r.length,g]),a?j.jsxs("div",{className:"relative bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-900/30 dark:via-purple-900/30 dark:to-pink-900/30 border-2 border-indigo-200 dark:border-indigo-700 rounded-xl p-3 -m-3 overflow-hidden",children:[j.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-indigo-100/30 to-purple-100/30 dark:from-indigo-800/20 dark:to-purple-800/20 animate-pulse"}),j.jsxs("div",{className:"relative flex items-center gap-3 mb-3",children:[j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white text-sm shadow-lg",children:"🧠"}),j.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"})]}),j.jsxs("div",{className:"flex-1",children:[j.jsxs("h4",{className:"font-bold text-gray-800 dark:text-gray-200 text-sm flex items-center gap-2",children:["Dr. Will está pensando",j.jsxs("div",{className:"flex gap-1",children:[j.jsx("div",{className:"w-1 h-1 bg-indigo-500 rounded-full animate-bounce"}),j.jsx("div",{className:"w-1 h-1 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),j.jsx("div",{className:"w-1 h-1 bg-pink-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),j.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:["Etapa ",i," de ",r.length+(t?1:0)]})]}),j.jsx("button",{onClick:()=>d(!l),className:"px-2 py-1 bg-white/70 dark:bg-gray-700/70 hover:bg-white/90 dark:hover:bg-gray-600/90 border border-indigo-200 dark:border-indigo-700 rounded-lg text-xs font-medium text-indigo-600 dark:text-indigo-400 transition-all duration-200 hover:shadow-sm",children:l?"Ocultar":"Ver"})]}),l&&j.jsxs("div",{className:"relative space-y-2",children:[r.map(((e,t)=>j.jsx(c.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},transition:{duration:.4,ease:"easeOut"},className:"relative bg-white/70 dark:bg-gray-700/70 backdrop-blur-sm rounded-lg p-2 border border-white/50 dark:border-gray-600/50 shadow-sm",children:j.jsxs("div",{className:"flex items-start gap-2",children:[j.jsx("div",{className:"w-4 h-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5",children:t+1}),j.jsx("div",{className:"flex-1 text-xs text-gray-700 dark:text-gray-300 leading-relaxed",children:j.jsx("div",{dangerouslySetInnerHTML:{__html:s(e)}})})]})},t))),t&&j.jsx(c.div,{initial:{opacity:0},animate:{opacity:1},className:"relative bg-white/50 dark:bg-gray-700/50 backdrop-blur-sm rounded-lg p-2 border border-white/30 dark:border-gray-600/30 shadow-sm",children:j.jsxs("div",{className:"flex items-center gap-2",children:[j.jsx("div",{className:"w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center animate-pulse",children:j.jsx("div",{className:"w-1 h-1 bg-gray-500 dark:bg-gray-400 rounded-full"})}),j.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400",children:[j.jsxs("div",{className:"flex gap-1",children:[j.jsx("div",{className:"w-1 h-1 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"}),j.jsx("div",{className:"w-1 h-1 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),j.jsx("div",{className:"w-1 h-1 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),j.jsx("span",{children:"Processando..."})]})]})})]}),j.jsx("div",{className:"relative mt-3 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden",children:j.jsx(c.div,{className:"absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full",initial:{width:"0%"},animate:{width:i/Math.max(r.length+(t?1:0),1)*100+"%"},transition:{duration:.5}})})]}):j.jsxs("div",{className:"relative bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 dark:from-indigo-900/30 dark:via-purple-900/30 dark:to-pink-900/30 border-2 border-indigo-200 dark:border-indigo-700 rounded-2xl p-4 -m-4 overflow-hidden",children:[j.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-indigo-100/30 to-purple-100/30 dark:from-indigo-800/20 dark:to-purple-800/20 animate-pulse"}),j.jsxs("div",{className:"relative flex items-center gap-3 mb-4",children:[j.jsxs("div",{className:"relative",children:[j.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white text-xl shadow-lg",children:"🧠"}),j.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-ping"})]}),j.jsxs("div",{className:"flex-1",children:[j.jsxs("h4",{className:"font-bold text-gray-800 dark:text-gray-200 text-lg flex items-center gap-2",children:["Dr. Will está pensando",j.jsxs("div",{className:"flex gap-1",children:[j.jsx("div",{className:"w-2 h-2 bg-indigo-500 rounded-full animate-bounce"}),j.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),j.jsx("div",{className:"w-2 h-2 bg-pink-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),j.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:["Etapa ",i," de ",r.length+(t?1:0)]})]}),j.jsx("button",{onClick:()=>d(!l),className:"px-3 py-2 bg-white/70 dark:bg-gray-700/70 hover:bg-white/90 dark:hover:bg-gray-600/90 border border-indigo-200 dark:border-indigo-700 rounded-xl text-sm font-medium text-indigo-600 dark:text-indigo-400 transition-all duration-200 hover:shadow-md",children:l?"Ocultar Pensamento":"Ver Pensamento"})]}),l&&j.jsxs("div",{className:"relative space-y-3",children:[r.map(((e,t)=>j.jsx(c.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},transition:{duration:.5,ease:"easeOut"},className:"relative bg-white/70 dark:bg-gray-700/70 backdrop-blur-sm rounded-xl p-3 border border-white/50 dark:border-gray-600/50 shadow-sm",children:j.jsxs("div",{className:"flex items-start gap-3",children:[j.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5",children:t+1}),j.jsx("div",{className:"flex-1 text-sm text-gray-700 dark:text-gray-300 leading-relaxed",children:j.jsx("div",{dangerouslySetInnerHTML:{__html:s(e)}})})]})},t))),t&&j.jsx(c.div,{initial:{opacity:0},animate:{opacity:1},className:"relative bg-white/50 dark:bg-gray-700/50 backdrop-blur-sm rounded-xl p-3 border border-white/30 dark:border-gray-600/30 shadow-sm",children:j.jsxs("div",{className:"flex items-center gap-3",children:[j.jsx("div",{className:"w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center animate-pulse",children:j.jsx("div",{className:"w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full"})}),j.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400",children:[j.jsxs("div",{className:"flex gap-1",children:[j.jsx("div",{className:"w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"}),j.jsx("div",{className:"w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),j.jsx("div",{className:"w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),j.jsx("span",{children:"Processando próxima etapa..."})]})]})})]}),j.jsx("div",{className:"relative mt-4 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden",children:j.jsx(c.div,{className:"absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full",initial:{width:"0%"},animate:{width:i/Math.max(r.length+(t?1:0),1)*100+"%"},transition:{duration:.5}})})]})},ne=({open:e,onOpenChange:t,sessionTitle:s,sessionId:a})=>{const r=S(),{clearQuestionContext:n}=d();return j.jsx(m,{open:e,onOpenChange:t,children:j.jsxs(u,{className:"max-w-md mx-4 max-h-[90vh] overflow-y-auto",children:[j.jsx(g,{children:j.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[j.jsx("div",{className:"w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center flex-shrink-0",children:j.jsx("span",{className:"text-2xl",children:"⚠️"})}),j.jsxs("div",{className:"min-w-0 flex-1",children:[j.jsx(h,{className:"text-xl font-bold text-gray-900 truncate",children:"Sessão Inativa"}),j.jsx(p,{className:"text-orange-600 text-sm",children:"Contexto não disponível"})]})]})}),j.jsxs("div",{className:"space-y-4",children:[j.jsxs("div",{className:"text-center",children:[j.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Conversa Contextual Detectada"}),j.jsxs("p",{className:"text-gray-600 text-sm leading-relaxed break-words",children:["Esta conversa é sobre a sessão"," ",j.jsxs("span",{className:"font-semibold text-orange-600 break-all",children:['"',s,'"']}),"."," ","Para acessá-la com contexto completo, você precisa retornar a essa sessão específica."]})]}),j.jsx("div",{className:"bg-orange-50 rounded-lg p-4 border border-orange-200",children:j.jsxs("div",{className:"flex items-start gap-3",children:[j.jsx("div",{className:"w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:j.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),j.jsxs("div",{className:"text-sm text-orange-800 min-w-0 flex-1",children:[j.jsx("div",{className:"font-medium mb-1",children:"O que isso significa?"}),j.jsxs("ul",{className:"space-y-1 text-orange-700",children:[j.jsx("li",{className:"break-words",children:"• Esta conversa contém contexto específico de questões"}),j.jsx("li",{className:"break-words",children:"• Sem a sessão ativa, as respostas podem ser imprecisas"}),j.jsxs("li",{className:"break-words",children:["• ",a?"Clique para retornar à sessão original":"Vá para questões para iniciar uma sessão"]})]})]})]})})]}),j.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3 pt-4",children:[j.jsxs(i,{onClick:async()=>{t(!1),n(),a?(await new Promise((e=>requestAnimationFrame(e))),r(`/questions/${a}`,{replace:!0,state:{forceRefresh:!0}})):r("/questions")},className:"flex-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-sm",children:[j.jsx("svg",{className:"w-4 h-4 mr-2 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:j.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})}),j.jsx("span",{className:"truncate",children:a?`Ir para "${s}"`:"Ir para Questões"})]}),j.jsx(i,{variant:"outline",onClick:()=>t(!1),className:"sm:w-auto",children:"Fechar"})]})]})})},ie=({open:e,onOpenChange:t,directMentions:s,suggestions:a,conductMentions:r,conductSuggestions:n,title:o})=>{const l=e=>{e&&e.includes("/")&&(window.location.href=e)},c=(e,t,s)=>j.jsx("div",{className:(s?"bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-600":"bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-700 hover:border-amber-300 dark:hover:border-amber-600")+" border rounded-lg p-3 hover:shadow-md transition-all duration-200",children:j.jsxs("div",{className:"flex items-center justify-between gap-3",children:[j.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[j.jsx("div",{className:`w-6 h-6 rounded-full ${s?"bg-gradient-to-br from-green-500 to-green-600":"bg-gradient-to-br from-amber-500 to-orange-500"} flex items-center justify-center text-white text-xs font-bold flex-shrink-0`,children:t+1}),j.jsxs("div",{className:"flex-1 min-w-0",children:[j.jsx("h3",{className:"font-semibold text-sm text-gray-900 dark:text-white truncate",children:e.name}),e.brands&&j.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 truncate",children:e.brands})]})]}),j.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.has_calculation&&e.slug&&j.jsxs(i,{variant:"outline",size:"sm",onClick:()=>l(`/medicamentos/${e.slug}`),className:"h-7 px-2 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300",children:[j.jsx(f,{className:"w-3 h-3"}),j.jsx("span",{className:"text-xs ml-1",children:"Calcular"})]}),e.has_bula&&e.slug&&j.jsxs(i,{variant:"outline",size:"sm",onClick:()=>l(`/bulas-profissionais/${e.slug}`),className:"h-7 px-2 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-900/30 text-green-700 dark:text-green-300",children:[j.jsx(b,{className:"w-3 h-3"}),j.jsx("span",{className:"text-xs ml-1",children:"Bula"})]})]})]})},e.id),d=(e,t,s)=>j.jsx("div",{className:(s?"bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600":"bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 hover:border-purple-300 dark:hover:border-purple-600")+" border rounded-lg p-3 hover:shadow-md transition-all duration-200",children:j.jsxs("div",{className:"flex items-center justify-between gap-3",children:[j.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[j.jsx("div",{className:`w-6 h-6 rounded-full ${s?"bg-gradient-to-br from-blue-500 to-blue-600":"bg-gradient-to-br from-purple-500 to-indigo-500"} flex items-center justify-center text-white text-xs font-bold flex-shrink-0`,children:t+1}),j.jsx("div",{className:"flex-1 min-w-0",children:j.jsx("h3",{className:"font-semibold text-sm text-gray-900 dark:text-white truncate",children:e.title})})]}),j.jsx("div",{className:"flex items-center gap-2 flex-shrink-0",children:j.jsxs(i,{variant:"outline",size:"sm",onClick:()=>l(`/condutas-e-manejos/${e.category_slug}/${e.topic_slug}`),className:"h-7 px-2 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300",children:[j.jsx($,{className:"w-3 h-3"}),j.jsx("span",{className:"text-xs ml-1",children:"Ler"})]})})]})},e.id);return j.jsx(m,{open:e,onOpenChange:t,children:j.jsxs(u,{className:"w-[95vw] max-w-4xl h-[90dvh] max-h-[90dvh] flex flex-col p-0 gap-0",style:{zIndex:9999},children:[j.jsxs(g,{className:"px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20",children:[j.jsxs(h,{className:"flex items-center gap-4 text-xl",children:[j.jsx("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 via-green-500 to-blue-600 flex items-center justify-center text-white text-xl shadow-lg",children:"🔗"}),j.jsxs("div",{children:[j.jsx("div",{className:"font-bold text-gray-900 dark:text-white text-lg",children:o}),j.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400 font-normal flex items-center gap-2",children:[j.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),"Recursos da plataforma PedBook"]})]})]}),j.jsx(p,{className:"sr-only",children:"Lista de medicamentos encontrados com links para cálculo de dosagem e bulas profissionais"})]}),j.jsxs("div",{className:"flex-1 overflow-y-auto px-6 py-4",children:[s.length>0&&j.jsxs("div",{className:"mb-4",children:[j.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[j.jsx("div",{className:"w-5 h-5 rounded-full bg-green-500 flex items-center justify-center text-white text-xs font-bold",children:"💊"}),j.jsxs("h3",{className:"text-sm font-bold text-gray-900 dark:text-white",children:["Medicamentos (",s.length,")"]})]}),j.jsx("div",{className:"space-y-2",children:s.map(((e,t)=>c(e,t,!0)))})]}),a.length>0&&j.jsxs("div",{className:"mb-4",children:[j.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[j.jsx("div",{className:"w-5 h-5 rounded-full bg-amber-500 flex items-center justify-center text-white text-xs font-bold",children:"?"}),j.jsxs("h3",{className:"text-sm font-bold text-gray-900 dark:text-white",children:["Sugestões (",a.length,")"]})]}),j.jsx("div",{className:"space-y-2",children:a.map(((e,t)=>c(e,t,!1)))})]}),r.length>0&&j.jsxs("div",{className:"mb-4",children:[j.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[j.jsx("div",{className:"w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-bold",children:"📋"}),j.jsxs("h3",{className:"text-sm font-bold text-gray-900 dark:text-white",children:["Condutas (",r.length,")"]})]}),j.jsx("div",{className:"space-y-2",children:r.map(((e,t)=>d(e,t,!0)))})]}),n.length>0&&j.jsxs("div",{className:"mb-4",children:[j.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[j.jsx("div",{className:"w-5 h-5 rounded-full bg-purple-500 flex items-center justify-center text-white text-xs font-bold",children:"?"}),j.jsxs("h3",{className:"text-sm font-bold text-gray-900 dark:text-white",children:["Leituras Sugeridas (",n.length,")"]})]}),j.jsx("div",{className:"space-y-2",children:n.map(((e,t)=>d(e,t,!1)))})]})]}),j.jsx("div",{className:"px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20",children:j.jsxs("div",{className:"flex items-center justify-between",children:[j.jsxs("div",{className:"flex items-center gap-3",children:[j.jsxs("div",{className:"flex items-center gap-1",children:[j.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),j.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse",style:{animationDelay:"0.5s"}})]}),j.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:"Recursos internos da plataforma PedBook"})]}),j.jsxs("div",{className:"flex items-center gap-2",children:[j.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[s.length+a.length+r.length+n.length," recursos encontrados"]}),j.jsx(i,{variant:"ghost",size:"sm",onClick:()=>t(!1),className:"hover:bg-white/50 dark:hover:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",children:"Fechar"})]})]})})]})})},oe=({images:e,className:t=""})=>{const[s,a]=y.useState(0),[r,n]=y.useState(!1),[l,c]=y.useState(0);if(!e||0===e.length)return null;const d=e=>{c(e),n(!0)};return j.jsxs(j.Fragment,{children:[j.jsxs("div",{className:`relative mb-2 ${t}`,children:[j.jsxs("div",{className:"relative group",children:[j.jsx("img",{src:e[s],alt:`Imagem ${s+1}`,className:"max-w-full max-h-48 rounded-lg object-cover cursor-pointer hover:opacity-90 transition-opacity",onClick:()=>d(s)}),j.jsx(i,{variant:"secondary",size:"sm",className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-none",onClick:()=>d(s),children:j.jsx(_,{className:"w-4 h-4"})}),e.length>1&&j.jsxs(j.Fragment,{children:[j.jsx(i,{variant:"secondary",size:"sm",className:"absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-none",onClick:()=>{a((t=>(t-1+e.length)%e.length))},children:j.jsx(M,{className:"w-4 h-4"})}),j.jsx(i,{variant:"secondary",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-none",onClick:()=>{a((t=>(t+1)%e.length))},children:j.jsx(x,{className:"w-4 h-4"})}),j.jsx("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1",children:e.map(((e,t)=>j.jsx("button",{className:"w-2 h-2 rounded-full transition-colors "+(t===s?"bg-white":"bg-white/50"),onClick:()=>a(t)},t)))})]})]}),e.length>1&&j.jsxs("div",{className:"text-xs text-gray-500 mt-1 text-center",children:[s+1," de ",e.length]})]}),j.jsxs(m,{open:r,onOpenChange:n,children:[r&&j.jsx("div",{className:"fixed inset-0 z-[79] bg-black/80",onClick:()=>n(!1)}),j.jsxs(u,{className:"max-w-4xl max-h-[90vh] p-0 z-[80]",children:[j.jsx(g,{className:"p-4 pb-0",children:j.jsxs(h,{className:"flex items-center justify-between",children:[j.jsxs("span",{children:["Imagem ",l+1," de ",e.length]}),j.jsx(i,{variant:"ghost",size:"sm",onClick:()=>n(!1),children:j.jsx(o,{className:"w-4 h-4"})})]})}),j.jsxs("div",{className:"relative p-4",children:[j.jsx("img",{src:e[l],alt:`Imagem ${l+1}`,className:"w-full h-auto max-h-[70vh] object-contain rounded-lg"}),e.length>1&&j.jsxs(j.Fragment,{children:[j.jsx(i,{variant:"secondary",size:"sm",className:"absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white border-none",onClick:()=>{c((t=>(t-1+e.length)%e.length))},children:j.jsx(M,{className:"w-4 h-4"})}),j.jsx(i,{variant:"secondary",size:"sm",className:"absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white border-none",onClick:()=>{c((t=>(t+1)%e.length))},children:j.jsx(x,{className:"w-4 h-4"})}),j.jsx("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2",children:e.map(((e,t)=>j.jsx("button",{className:"w-3 h-3 rounded-full transition-colors "+(t===l?"bg-white":"bg-white/50"),onClick:()=>c(t)},t)))})]})]})]})]})]})},le=new Map,ce=["chat-images","images","files"],de=86400,me=async(e,t,s,r={})=>{try{const{error:n}=await a.storage.from(e).upload(t,s);if(n)throw new Error(`Falha no upload: ${n.message}`);return await(async(e,t,s={})=>{const{expirySeconds:r=de,useCache:n=!0}=s;if(!ce.includes(e)){const{data:{publicUrl:s}}=a.storage.from(e).getPublicUrl(t);return s}const i=`${e}/${t}`;if(n){const e=le.get(i);if(e&&e.expires>Date.now())return e.url}try{const{data:s,error:o}=await a.storage.from(e).createSignedUrl(t,r);if(o)throw new Error(`Falha ao gerar URL segura: ${o.message}`);if(!s?.signedUrl)throw new Error("URL assinada não foi retornada pelo Supabase");if(n){const e=Date.now()+1e3*(r-3600);le.set(i,{url:s.signedUrl,expires:e})}return s.signedUrl}catch(o){throw o}})(e,t,r)}catch(n){throw n}},ue=(e,t)=>{const s=t.split(".").pop();return`dr-will-uploads/${e}/${crypto.randomUUID()}.${s}`};export{E as H,oe as M,ne as S,re as T,H as a,ae as b,ie as c,L as d,W as e,B as f,ue as g,me as h,D as i,T as l,R as m,z as u};
