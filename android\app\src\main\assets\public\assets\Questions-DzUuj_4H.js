import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{F as t,Q as a}from"./QuestionCard-CvSLb3NC.js";import{d as i,s as r,D as n,aK as o,B as c,e as d,f as l,g as u,z as m,T as p,aC as f,ba as h,ad as _,ae as g,af as y,ag as x,ai as w,j as v,ap as j,C as q,b,ao as S,aa as T,L as N}from"./index-DwykrzWu.js";import{u as k,a as E,P as I}from"./PediatricStudy-BccY_9uK.js";import{a as A}from"./query-vendor-B-7l6Nb3.js";import{u as Q}from"./useUserData-dBM0os7h.js";import{C}from"./chevron-left-UiG5RJWe.js";import{a as z,c as D}from"./router-BAzpOxbo.js";import U from"./Footer-CEErUVD6.js";import"./alert-47AuHV0Y.js";import"./ensureUserId-BgQBbRp3.js";import"./bug-rUAcu1O2.js";import"./send-Cxl242LC.js";import"./eye-C0LkTxPj.js";import"./circle-help-DkV0sebI.js";import"./thumbs-up-BOkUb6_f.js";import"./thumbs-down-DdqCWI6x.js";import"./LazyImage-CBhcU0Wf.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./skeleton-Dv9EM7L7.js";import"./separator-yzavG1Yz.js";import"./tooltip-t2dArorp.js";import"./slider-DJZ9XRYr.js";import"./index-k36xMONP.js";import"./target-Bq7BybE4.js";import"./building-2-Dm0SGprC.js";import"./calendar-hvRXSQ6q.js";import"./file-question-DbHf8Lm5.js";import"./FeedbackTrigger-Cce_pfRl.js";import"./rocket-BVKdk9NC.js";import"./zap-sktuObTW.js";import"./book-open-vsXyzIQN.js";import"./star-CVUfjIpQ.js";import"./instagram-BgC8q_0n.js";const M="session_timer_",O=e=>{if(e<0)return"00:00";const s=Math.floor(e/3600),t=Math.floor(e%3600/60),a=Math.floor(e%60);return s>0?`${s.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`:`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},F=({elapsedTime:s,onTimeUpdate:t,isActive:a=!0})=>e.jsx("div",{className:"text-lg font-mono bg-gray-50 dark:bg-gray-700 px-4 py-2 rounded-lg border border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"w-2 h-2 rounded-full "+(a?"bg-green-500 animate-pulse":"bg-gray-400")}),O(s)]})}),P=({questionId:a,userId:h,children:_})=>{const[g,y]=s.useState(!1),[x,w]=s.useState(""),[v,j]=s.useState(!1),{toast:q}=i();return e.jsxs(n,{open:g,onOpenChange:y,children:[e.jsx(o,{asChild:!0,children:_||e.jsxs(c,{variant:"outline",size:"sm",className:"flex gap-2",children:[e.jsx(t,{className:"h-4 w-4"}),"Reportar"]})}),e.jsxs(d,{className:"sm:max-w-[425px]",children:[e.jsxs(l,{children:[e.jsx(u,{children:"Reportar Questão"}),e.jsx(m,{children:"Descreva o problema encontrado nesta questão. Sua contribuição nos ajuda a melhorar a plataforma."})]}),e.jsx("div",{className:"grid gap-4 py-4",children:e.jsx(p,{placeholder:"Descreva o problema encontrado...",value:x,onChange:e=>w(e.target.value),className:"min-h-[100px]"})}),e.jsx(f,{children:e.jsx(c,{type:"submit",onClick:async()=>{if(x.trim()){j(!0);try{const{error:e}=await r.from("question_reports").insert([{question_id:a,user_id:h,message:x.trim()}]);if(e)throw e;q({title:"Report enviado",description:"Obrigado por nos ajudar a melhorar a plataforma!"}),w(""),y(!1)}catch(e){q({title:"Erro ao enviar report",description:"Não foi possível enviar seu report. Tente novamente.",variant:"destructive"})}finally{j(!1)}}else q({title:"Erro",description:"Por favor, descreva o problema encontrado.",variant:"destructive"})},disabled:v,children:v?"Enviando...":"Enviar Report"})})]})]})},R=({currentIndex:a,totalQuestions:i,onSelectQuestion:r,onFinishSession:n,elapsedTime:o,onTimeUpdate:d,questionId:l,userId:u,answeredStatuses:m={}})=>{const p=10,{isNavigationLocked:f}=h(),b=Math.floor(a/p),[S,T]=s.useState({start:b*p,end:Math.min((b+1)*p-1,i-1)});s.useEffect((()=>{const e=Math.floor(a/p),s=e*p,t=Math.min((e+1)*p-1,i-1);T({start:s,end:t})}),[a,i]);const N=Math.ceil(i/p),k=Math.floor(a/p)+1;return e.jsx("div",{className:"sticky top-0 z-50 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-md rounded-xl mb-6",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 max-w-full px-4 py-3",children:[e.jsxs("div",{className:"md:hidden flex items-center justify-center w-full max-w-md mx-auto gap-4",children:[e.jsxs(_,{value:a.toString(),onValueChange:e=>!f&&r(parseInt(e)),disabled:f,children:[e.jsx(g,{className:"w-12 h-10 rounded-lg",children:e.jsx(y,{children:a+1})}),e.jsx(x,{className:"max-h-60",children:e.jsx("div",{className:"grid grid-cols-5 gap-1 p-1",children:Array.from({length:i},((s,t)=>{const i=m[t];let r="flex items-center justify-center h-8 w-8 rounded-md text-sm ";return r+=t===a?"bg-primary text-primary-foreground":!0===i?"bg-green-500 text-white":!1===i?"bg-destructive text-destructive-foreground":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300",e.jsx(w,{value:t.toString(),className:v(r),children:e.jsx("span",{className:"flex items-center justify-center",children:!0===i?e.jsx(j,{className:"h-3 w-3"}):t+1})},t)}))})})]}),e.jsx(F,{elapsedTime:o,onTimeUpdate:d,isActive:!0}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{questionId:l,userId:u,children:e.jsx(c,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full",children:e.jsx(t,{className:"h-4 w-4"})})}),e.jsx(c,{variant:"default",size:"sm",onClick:n,disabled:f,className:"rounded-full px-4",children:"Finalizar"})]})]}),e.jsxs("div",{className:"hidden md:flex items-center gap-2",children:[e.jsx(c,{variant:"ghost",size:"icon",onClick:()=>{if(f)return;const e=Math.floor(S.start/p)-1,s=e*p,t=Math.min((e+1)*p-1,i-1);T({start:s,end:t}),r(s)},disabled:0===S.start||f,className:"h-8 w-8 rounded-full",children:e.jsx(C,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex gap-2 overflow-x-auto",children:Array.from({length:S.end-S.start+1},((s,t)=>{const i=S.start+t;if(a===i)return e.jsx(c,{variant:"default",size:"sm",onClick:()=>!f&&r(i),disabled:f,className:"h-8 w-8 p-0 rounded-full",children:i+1},i);const n=m[i];return!0===n?e.jsx(c,{variant:"outline",size:"sm",onClick:()=>!f&&r(i),disabled:f,className:"h-8 w-8 p-0 rounded-full bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300",children:e.jsx(j,{className:"h-4 w-4"})},i):!1===n?e.jsx(c,{variant:"outline",size:"sm",onClick:()=>!f&&r(i),disabled:f,className:"h-8 w-8 p-0 rounded-full bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-700 text-red-700 dark:text-red-300",children:i+1},i):e.jsx(c,{variant:"outline",size:"sm",onClick:()=>!f&&r(i),disabled:f,className:"h-8 w-8 p-0 rounded-full",children:i+1},i)}))}),e.jsx(c,{variant:"ghost",size:"icon",onClick:()=>{if(f)return;const e=Math.floor(S.start/p)+1,s=e*p,t=Math.min((e+1)*p-1,i-1);T({start:s,end:t}),r(s)},disabled:S.end>=i-1||f,className:"h-8 w-8 rounded-full",children:e.jsx(q,{className:"h-4 w-4"})}),N>1&&e.jsxs("span",{className:"text-xs text-muted-foreground ml-2",children:["Página ",k," de ",N]})]}),e.jsxs("div",{className:"hidden md:flex items-center gap-4",children:[e.jsx(F,{elapsedTime:o,onTimeUpdate:d,isActive:!0}),e.jsx(P,{questionId:l,userId:u,children:e.jsxs(c,{variant:"outline",size:"sm",className:"hidden md:flex gap-2 rounded-full",children:[e.jsx(t,{className:"h-4 w-4"}),"Reportar"]})}),e.jsx(c,{variant:"default",size:"sm",onClick:n,disabled:f,className:"flex items-center gap-2 rounded-full",children:"Finalizar"})]})]})})},K=({questions:t,sessionId:n,userId:o})=>{const[c,d]=s.useState(0),[l,u]=s.useState({}),[m,p]=s.useState(new Set),[f,h]=s.useState({}),[_,g]=s.useState(!1),[y,x]=s.useState({}),[w,v]=s.useState(null),j=z(),{toast:q}=i();k();const{markQuestionAsAnswered:T}=(e=>{const[t,a]=s.useState(null),[n,o]=s.useState(new Set),{toast:c}=i();return s.useEffect((()=>{e&&(async()=>{try{const{data:s,error:t}=await r.from("study_sessions").select("*").eq("user_id",e).eq("status","in_progress").order("started_at",{ascending:!1}).limit(1);if(t)throw t;if(!s||0===s.length)return;const i=s[0],{data:n,error:c}=await r.from("session_events").select("question_id").eq("session_id",i.id);if(c)throw c;const d=new Set(n?.map((e=>e.question_id))||[]);o(d),a(i)}catch(s){c({title:"Erro ao recuperar sessão",description:s.message,variant:"destructive"})}})()}),[e,c]),{activeSession:t,setActiveSession:a,answeredQuestions:n,markQuestionAsAnswered:e=>{o((s=>new Set([...s,e])))}}})(o),N=A(),{setCurrentQuestion:E,setQuestionIndex:I,setTotalQuestions:C,setSessionId:D,setSessionTitle:U,clearQuestionContext:O}=b(),{submitAnswer:F}=(e=>{const[t,a]=s.useState(!1),{toast:n}=i(),o=A(),{isAdmin:c}=Q();return{submitAnswer:async(s,i,d,l)=>{if(t)return!1;a(!0);try{const{data:{user:t}}=await r.auth.getUser();if(!t)return n({title:"Erro de autenticação",description:"Você precisa estar logado para salvar respostas.",variant:"destructive"}),!1;if(t.id!==s&&!c)return n({title:"Erro de permissão",description:"Você não tem permissão para salvar respostas para outro usuário.",variant:"destructive"}),!1;if("DISCURSIVE"===i.answer_type){if(!i.specialty?.id)throw new Error("Especialidade é obrigatória para salvar a resposta");const{data:t,error:a}=await r.from("user_answers").insert({user_id:s,question_id:i.id,text_answer:d,is_correct:!0,specialty_id:i.specialty.id,theme_id:i.theme?.id,focus_id:i.focus?.id,session_id:e,time_spent:l,year:i.year||(new Date).getFullYear()}).select().single();if(a)throw a;try{const{error:s}=await r.from("session_events").insert({session_id:e,question_id:i.id,response_status:!0,response_time:l,theme_id:i.theme?.id,specialty_id:i.specialty?.id,focus_id:i.focus?.id});if(s&&"42P01"!==s.code&&"42501"!==s.code)throw s}catch(u){}n({title:"Resposta salva",description:"Sua resposta discursiva foi registrada com sucesso"})}else{const t=parseInt(d),a=t===("number"==typeof i.correct_answer?i.correct_answer:parseInt(String(i.correct_answer)))+1,n=i.specialty?.id||i.specialty_id;if(!n)throw new Error("Especialidade é obrigatória para salvar a resposta");const{error:c}=await r.from("user_answers").insert({user_id:s,question_id:i.id,selected_answer:t,is_correct:a,specialty_id:n,theme_id:i.theme?.id||i.theme_id,focus_id:i.focus?.id||i.focus_id,session_id:e,time_spent:l,year:i.year||i.exam_year||(new Date).getFullYear()});if(c)throw c;const m={session_id:e,question_id:i.id,response_status:a,response_time:l,theme_id:i.theme?.id||i.theme_id,specialty_id:n,focus_id:i.focus?.id||i.focus_id,selected_answer:t,response:d};try{const{data:e,error:s}=await r.from("session_events").insert(m).select();if(s)if("42P01"===s.code);else if("42501"!==s.code)throw s}catch(u){}o.invalidateQueries({queryKey:["correct-questions"]}),o.invalidateQueries({queryKey:["user-statistics"]})}try{const{data:s,error:t}=await r.from("study_sessions").select("current_question_index, total_questions").eq("id",e).single();if(t){if("42P01"===t.code);else if("42501"!==t.code)throw t}else if(s){const t=s.current_question_index+1,a=t>=s.total_questions,{error:i}=await r.from("study_sessions").update({current_question_index:t,status:a?"completed":"in_progress",completed_at:a?(new Date).toISOString():null}).eq("id",e);if(i){if("42P01"===i.code);else if("42501"!==i.code)throw i}else a&&(await o.refetchQueries({queryKey:["user-statistics"]}),await o.refetchQueries({queryKey:["user-study-stats"]}),await o.refetchQueries({predicate:e=>{const s=e.queryKey[0];return"study-sessions"===s||"flashcard-sessions"===s}}),await o.invalidateQueries({queryKey:["progress"]}),await o.invalidateQueries({queryKey:["session-history"]}),await o.invalidateQueries({predicate:e=>{const s=e.queryKey[0];return s?.includes("session")||s?.includes("statistics")||s?.includes("progress")}}),await new Promise((e=>setTimeout(e,100))))}}catch(m){}return Y&&(T(Y),p((e=>new Set([...e,Y]))),h((e=>({...e,[Y]:!0})))),!0}catch(f){return n({title:"Erro ao salvar resposta",description:"Ocorreu um erro ao tentar salvar sua resposta. Por favor, tente novamente.",variant:"destructive"}),!1}finally{a(!1)}},isSubmitting:t}})(n),{totalElapsedTime:P,currentQuestionTime:K,startQuestionTimer:$,getQuestionTime:V,finishSession:L}=(e=>{const[t,a]=s.useState((()=>{const s=`${M}${e}`,t=localStorage.getItem(s);if(t)try{return JSON.parse(t)}catch(a){}return{sessionId:e,totalElapsedTime:0,questionTimes:{},currentQuestionId:null,sessionStartTime:Date.now(),lastSaveTime:Date.now()}})),i=s.useRef();s.useRef();const n=s.useRef(Date.now()),o=s.useRef(!0),c=s.useCallback((s=>{const t=`${M}${e}`;localStorage.setItem(t,JSON.stringify(s))}),[e]),d=s.useCallback((async s=>{try{const t={};Object.values(s.questionTimes).forEach((e=>{t[e.questionId]=Math.floor(e.elapsedTime)}));const{error:a}=await r.from("study_sessions").update({total_time_spent:Math.floor(s.totalElapsedTime),question_times:t,last_activity:(new Date).toISOString()}).eq("id",e)}catch(t){}}),[e]),l=s.useCallback((async()=>{try{const{data:s,error:t}=await r.from("study_sessions").select("question_times, total_time_spent, session_start_time").eq("id",e).single();if(t)return;if(s&&s.question_times){const e={};let t=0;Object.entries(s.question_times).forEach((([s,a])=>{e[s]={questionId:s,startTime:Date.now(),elapsedTime:a||0,isActive:!1,lastUpdate:Date.now()},t+=a||0})),a((a=>({...a,questionTimes:{...a.questionTimes,...e},totalElapsedTime:Math.max(a.totalElapsedTime,s.total_time_spent||t),sessionStartTime:s.session_start_time?new Date(s.session_start_time).getTime():a.sessionStartTime})))}}catch(s){}}),[e]);s.useEffect((()=>{const e=()=>{const e=!document.hidden;o.current=e,n.current=Date.now(),a(e?e=>{const s=Date.now(),t={...e.questionTimes};Object.keys(t).forEach((e=>{const a=t[e];if(a.isActive){const e=Math.floor((s-a.lastUpdate)/1e3);a.elapsedTime+=e,a.lastUpdate=s}}));const a={...e,questionTimes:t};return c(a),a}:e=>(c(e),e))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)}),[c]),s.useEffect((()=>{l()}),[l]),s.useEffect((()=>(i.current=window.setInterval((()=>{if(!o.current)return;const e=Date.now();e-n.current>3e4||(a((s=>{const t={...s.questionTimes};let a=0;Object.keys(t).forEach((s=>{const i=t[s];i.isActive&&(i.elapsedTime+=1,i.lastUpdate=e),a+=i.elapsedTime}));const i={...s,questionTimes:t,totalElapsedTime:a};return c(i),e-s.lastSaveTime>1e4&&(d(i),i.lastSaveTime=e),i})),n.current=e)}),1e3),()=>{i.current&&clearInterval(i.current)})),[c,d]);const u=s.useCallback((e=>{a((s=>{const t={...s.questionTimes};Object.keys(t).forEach((e=>{t[e].isActive=!1})),t[e]?(t[e].isActive=!0,t[e].lastUpdate=Date.now()):t[e]={questionId:e,startTime:Date.now(),elapsedTime:0,isActive:!0,lastUpdate:Date.now()};const a={...s,questionTimes:t,currentQuestionId:e};return c(a),a})),n.current=Date.now()}),[c]),m=s.useCallback((()=>{a((e=>{const s={...e.questionTimes};Object.keys(s).forEach((e=>{s[e].isActive=!1}));const t={...e,questionTimes:s,currentQuestionId:null};return c(t),t}))}),[c]),p=s.useCallback((e=>t.questionTimes[e]?.elapsedTime||0),[t.questionTimes]),f=s.useCallback((async()=>{m(),await d(t);const s=`${M}${e}`;localStorage.removeItem(s)}),[e,m,d,t]);return{totalElapsedTime:t.totalElapsedTime,currentQuestionTime:t.currentQuestionId?p(t.currentQuestionId):0,startQuestionTimer:u,pauseCurrentTimer:m,getQuestionTime:p,finishSession:f,isActive:o.current}})(n),B=t[c],Y=B?.id,J=c===t.length-1,W=0===c,Z=m.size===t.length,G=m.size/t.length*100;s.useEffect((()=>{(async()=>{if(n&&t.length)try{const{data:e,error:s}=await r.from("study_sessions").select("title").eq("id",n).single(),a=e?.title||"Sessão de Estudos";v(a),D(n),U(a),C(t.length)}catch(e){}})()}),[n,t.length,D,U,C]),s.useEffect((()=>{B&&(E(B),I(c))}),[B,c,E,I]),s.useEffect((()=>()=>{O()}),[O]),s.useEffect((()=>{Y&&$(Y)}),[Y,$,c]),s.useEffect((()=>{(async()=>{if(n&&!_)try{const{data:{user:e}}=await r.auth.getUser();if(!e)return void q({title:"Erro de autenticação",description:"Você precisa estar logado para acessar o progresso da sessão.",variant:"destructive"});const{data:s,error:t}=await r.from("study_sessions").select("user_id").eq("id",n).single();if(t){if("42P01"!==t.code&&"42501"!==t.code)throw t}else if(s&&s.user_id!==e.id){const{data:s}=await r.from("profiles").select("is_admin").eq("id",e.id).single();if(!s?.is_admin)return void q({title:"Erro de permissão",description:"Você não tem permissão para acessar esta sessão.",variant:"destructive"})}const{data:a,error:i}=await r.from("session_events").select("question_id, response_status, response").eq("session_id",n);if(i){if("42P01"===i.code)return;if("42501"===i.code)return;throw i}if(a){const e=new Set(a.map((e=>e.question_id)));p(e);const s={},t={},i={};a.forEach((e=>{s[e.question_id]=!0,t[e.question_id]=e.response_status,null!==e.response&&(i[e.question_id]=e.response)})),h(s),x(t),u(i),g(!0)}}catch(e){}})()}),[n,_,t.length]);const H=async()=>{try{await L();const{data:{user:e}}=await r.auth.getUser();if(!e)return void q({title:"Erro de autenticação",description:"Você precisa estar logado para finalizar a sessão.",variant:"destructive"});const{data:s,error:t}=await r.from("study_sessions").select("user_id").eq("id",n).single();if(t){if("42P01"!==t.code&&"42501"!==t.code)throw t}else if(s&&s.user_id!==e.id){const{data:s}=await r.from("profiles").select("is_admin").eq("id",e.id).single();if(!s?.is_admin)return void q({title:"Erro de permissão",description:"Você não tem permissão para finalizar esta sessão.",variant:"destructive"})}const{error:a}=await r.from("study_sessions").update({completed_at:(new Date).toISOString(),status:"completed"}).eq("id",n);if(a)if("42P01"===a.code);else if("42501"!==a.code)throw a;await N.refetchQueries({queryKey:["user-statistics"]}),await N.refetchQueries({queryKey:["user-study-stats"]}),await N.refetchQueries({predicate:e=>{const s=e.queryKey[0];return"study-sessions"===s||"flashcard-sessions"===s}}),N.invalidateQueries({queryKey:["correct-questions"]}),j(`/results/${n}`)}catch{q({title:"Erro ao finalizar sessão",description:"Tente novamente.",variant:"destructive"})}};if(!t.length)return null;const X={};t.forEach(((e,s)=>{e.id in y&&(X[s]=y[e.id])}));const ee=!!Y&&m.has(Y),se=!!Y&&f[Y];return e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"mb-4 bg-white rounded-xl shadow-lg p-4 -mt-6 md:-mt-12",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-500",children:["Progresso: ",m.size,"/",t.length," questões"]}),e.jsxs("span",{className:"text-sm font-medium text-gray-500",children:[G.toFixed(0),"%"]})]}),e.jsx(S,{value:G,className:"h-3 bg-gray-100"})]})}),e.jsx(R,{currentIndex:c,totalQuestions:t.length,onSelectQuestion:e=>{d(e)},onFinishSession:H,elapsedTime:P,onTimeUpdate:()=>{},questionId:B?.id||"",userId:o,answeredStatuses:X}),e.jsx("div",{className:"space-y-6 pb-8",children:e.jsx(a,{question:B,selectedAnswer:l[Y]||null,hasAnswered:ee,onSelectAnswer:e=>((e,s)=>{u((t=>({...t,[e]:s})))})(Y,e),onSubmitAnswer:async e=>{if(Y&&l[Y])try{const e=l[Y],s=V(Y);if(await F(o,B,e,s)){const s=e===("number"==typeof B.correct_answer?(B.correct_answer+1).toString():(parseInt(String(B.correct_answer))+1).toString());x((e=>({...e,[Y]:s})))}}catch(s){}},onNext:J&&Z?H:()=>{c<t.length-1&&d((e=>e+1))},onPrevious:W?void 0:()=>{c>0&&d((e=>e-1))},userId:o,sessionId:n,isAnswered:ee,timeSpent:K,showFeedback:se,isLastQuestion:J,isFirstQuestion:W,allQuestionsAnswered:Z})})]})},$=()=>{const{sessionId:t}=D(),a=z(),{toast:n}=i();E();const[o,d]=s.useState([]),[l,u]=s.useState(!0),[m,p]=s.useState(null);s.useEffect((()=>{t&&(o.length>0&&t||(async()=>{try{u(!0);const{data:{user:e},error:s}=await r.auth.getUser();if(s)throw s;if(!e)throw new Error("Usuário não autenticado");if(p(e.id),!t)return void u(!1);const{data:i,error:o}=await r.from("study_sessions").select("*, knowledge_domain").eq("id",t).maybeSingle();if(o)throw o;if(!i)return n({title:"Sessão não encontrada",description:"A sessão que você está tentando acessar não existe",variant:"destructive"}),void a("/estudos");if(!i.questions||0===i.questions.length)throw new Error("Nenhuma questão encontrada nesta sessão");const c=i.knowledge_domain||"residencia";let l,m;if(i.questions.length>100){const{data:e,error:s}=await r.rpc("get_questions_by_ids",{question_ids:i.questions,domain_filter:c});s?m=s:l=e?.map((e=>({...e,specialty:e.specialty_name?{id:e.specialty_id,name:e.specialty_name}:null,theme:e.theme_name?{id:e.theme_id,name:e.theme_name}:null,focus:e.focus_name?{id:e.focus_id,name:e.focus_name}:null,location:e.location_name?{id:e.exam_location,name:e.location_name}:null})))}else{const{data:e,error:s}=await r.from("questions").select("\n              *,\n              specialty:study_categories!questions_specialty_id_fkey(id, name),\n              theme:study_categories!questions_theme_id_fkey(id, name),\n              focus:study_categories!questions_focus_id_fkey(id, name),\n              location:exam_locations!questions_location_id_fkey(id, name)\n            ").in("id",i.questions).eq("knowledge_domain",c);l=e,m=s}if(m)throw m;if(!l?.length)throw new Error("Nenhuma questão encontrada para esta sessão");const h=l.map((e=>{const s=f(e.media_attachments||e.images);return{id:e.id,statement:e.question_content||e.statement,question_content:e.question_content||e.statement,alternatives:Array.isArray(e.alternatives)&&e.alternatives.length>0?e.alternatives.map((e=>String(e))):"object"==typeof e.alternatives&&null!==e.alternatives?Object.values(e.alternatives).map(String):void 0,response_choices:Array.isArray(e.response_choices)&&e.response_choices.length>0?e.response_choices.map((e=>String(e))):Array.isArray(e.alternatives)&&e.alternatives.length>0?e.alternatives.map((e=>String(e))):[],correct_answer:parseInt(String(e.correct_choice||e.correct_answer)),correct_choice:parseInt(String(e.correct_choice||e.correct_answer)),answer_type:e.question_format||e.answer_type||"ALTERNATIVAS",question_format:e.question_format||e.answer_type||"ALTERNATIVAS",specialty:e.specialty,theme:e.theme,focus:e.focus,year:e.exam_year||e.year,exam_year:e.exam_year||e.year,location:e.location,institution:e.institutions?.[0]?.institution,statistics:Array.isArray(e.statistics)?e.statistics.map((e=>({count:"number"==typeof e?e:0,percentage:0}))):[],alternativeComments:"object"==typeof e.alternative_comments&&null!==e.alternative_comments?Object.entries(e.alternative_comments).reduce(((e,[s,t])=>({...e,[parseInt(s)]:String(t)})),{}):{},comments:e.comments||[],owner:e.owner,likes:e.likes||0,dislikes:e.dislikes||0,liked_by:e.liked_by||[],disliked_by:e.disliked_by||[],created_at:e.created_at,domain:e.knowledge_domain||e.domain,images:s,media_attachments:s,question_type:e.assessment_type||e.question_type,assessment_type:e.assessment_type||e.question_type,question_number:e.question_number,ai_commentary:e.ai_commentary}}));d(h)}catch(e){n({title:"Erro ao carregar questões",description:e.message||"Ocorreu um erro ao carregar as questões",variant:"destructive"}),a("/estudos")}finally{u(!1)}})())}),[t]);const f=e=>e?"string"==typeof e?[e]:Array.isArray(e)?e.map((e=>"string"==typeof e?e:"")).filter(Boolean):"object"==typeof e&&null!==e?Object.values(e).filter((e=>"string"==typeof e&&e.length>0)):[]:[];return l?e.jsxs(e.Fragment,{children:[e.jsx(T,{}),e.jsx("div",{className:"container mx-auto px-4 pt-16 md:py-8",children:e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 rounded-lg shadow-sm bg-white/30 backdrop-blur-sm",children:[e.jsx(N,{className:"h-8 w-8 animate-spin text-primary mb-4"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Carregando questões..."}),e.jsx("p",{className:"text-muted-foreground text-center",children:"Aguarde enquanto preparamos suas questões de pediatria"})]})}),e.jsx(U,{})]}):t?o.length?e.jsxs(e.Fragment,{children:[e.jsx(T,{}),e.jsx("div",{className:"pt-8 md:pt-12",style:{paddingRight:0,paddingLeft:0},children:t&&m&&e.jsx(K,{questions:o,sessionId:t,userId:m})}),e.jsx(U,{})]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{}),e.jsxs("div",{className:"container mx-auto px-4 pt-16 md:py-8",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Nenhuma questão encontrada"}),e.jsx(c,{onClick:()=>a("/estudos"),children:"Voltar para Filtros"})]}),e.jsx(U,{})]}):e.jsx(I,{})};export{$ as default};
