import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{a,u as i}from"./query-vendor-B-7l6Nb3.js";import{d as o,an as r,a5 as t,s as n,ad as l,ae as m,af as c,ag as d,ai as g,T as h,B as x,R as u,W as j,U as p,Z as v,D as f,aK as y,e as N,f as M,g as b}from"./index-DwBJcqzE.js";import{i as w,c as C,a as _}from"./imageOptimization-Di_XtiVZ.js";import{L as P}from"./LazyImage-DwPjlzfH.js";import{S}from"./skeleton-D1oaQJqQ.js";import{A as D,h as T,a as F,b as U,c as $,d as z,e as A,f as E,g as I}from"./alert-dialog-BxXVl-s1.js";import{T as L}from"./trash-2-DVslkler.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const Y=({imageUrl:s,setImageUrl:a})=>{const{toast:i}=o();return e.jsxs("div",{children:[e.jsx(r,{htmlFor:"image",children:"Imagem"}),e.jsx(t,{id:"image",type:"file",accept:"image/*",onChange:async e=>{const s=e.target.files?.[0];if(s)try{if(!w(s))return void i({variant:"destructive",title:"Formato não suportado",description:"Por favor, selecione uma imagem JPG, PNG ou WebP."});const e=s.size,o=await C(s,{maxWidth:1200,maxHeight:800,quality:.85,format:"webp"}),r=_(e,o.size),t=`${crypto.randomUUID()}.webp`,{error:l}=await n.storage.from("dnpm-images").upload(t,o);if(l)throw l;const{data:{publicUrl:m}}=n.storage.from("dnpm-images").getPublicUrl(t);a(m),i({title:"Imagem enviada com sucesso!",description:`Tamanho reduzido em ${r}% (${(e/1024/1024).toFixed(2)}MB → ${(o.size/1024/1024).toFixed(2)}MB)`})}catch(o){i({variant:"destructive",title:"Erro ao fazer upload da imagem",description:o.message})}}}),s&&e.jsx("div",{className:"mt-2",children:e.jsx(P,{src:s,alt:"Preview da imagem",className:"max-w-xs rounded-lg shadow-md",priority:!0})})]})},q=({ageType:s,setAgeType:a,ageYears:i,setAgeYears:o,ageMonths:n,setAgeMonths:h})=>e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"ageType",children:"Tipo de Idade"}),e.jsxs(l,{value:s,onValueChange:e=>a(e),children:[e.jsx(m,{children:e.jsx(c,{placeholder:"Selecione o tipo de idade"})}),e.jsxs(d,{children:[e.jsx(g,{value:"months",children:"Meses"}),e.jsx(g,{value:"years",children:"Anos"})]})]})]}),"years"===s&&e.jsxs("div",{children:[e.jsx(r,{htmlFor:"ageYears",children:"Anos"}),e.jsx(t,{id:"ageYears",type:"number",min:0,max:18,value:i||"",onChange:e=>o(parseInt(e.target.value)||null)})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"ageMonths",children:"Meses"}),e.jsx(t,{id:"ageMonths",type:"number",min:0,max:11,value:n,onChange:e=>h(parseInt(e.target.value)||0)})]})]}),k=({onSubmit:a,isLoading:i})=>{const[o,t]=s.useState("months"),[n,l]=s.useState(null),[m,c]=s.useState(0),[d,g]=s.useState(""),[u,j]=s.useState(""),[p,v]=s.useState(""),[f,y]=s.useState(""),[N,M]=s.useState("");return e.jsxs("form",{onSubmit:e=>{e.preventDefault(),a({ageType:o,ageYears:n,ageMonths:m,socialEmotional:d,languageCommunication:u,cognition:p,motorPhysical:f,imageUrl:N})},className:"space-y-6",children:[e.jsx(q,{ageType:o,setAgeType:t,ageYears:n,setAgeYears:l,ageMonths:m,setAgeMonths:c}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"socialEmotional",children:"Social e Emocional"}),e.jsx(h,{id:"socialEmotional",value:d,onChange:e=>g(e.target.value),className:"min-h-[100px]"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"languageCommunication",children:"Linguagem e Comunicação"}),e.jsx(h,{id:"languageCommunication",value:u,onChange:e=>j(e.target.value),className:"min-h-[100px]"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"cognition",children:"Cognição"}),e.jsx(h,{id:"cognition",value:p,onChange:e=>v(e.target.value),className:"min-h-[100px]"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"motorPhysical",children:"Motora/Física"}),e.jsx(h,{id:"motorPhysical",value:f,onChange:e=>y(e.target.value),className:"min-h-[100px]"})]}),e.jsx(Y,{imageUrl:N,setImageUrl:M}),e.jsx(x,{type:"submit",disabled:i,children:i?"Salvando...":"Salvar Marco DNPM"})]})},K=({milestones:s,isLoading:a,onDelete:i})=>{if(a)return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[1,2,3].map((s=>e.jsxs(u,{className:"w-full",children:[e.jsx(j,{children:e.jsx(S,{className:"h-4 w-3/4"})}),e.jsx(p,{children:e.jsx(S,{className:"h-20 w-full"})})]},s)))});if(0===s.length)return e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-muted-foreground",children:"Nenhum marco DNPM cadastrado."})});const o=e=>{if("years"===e.age_type){const s=Math.floor(e.age_months/12),a=e.age_months%12;return`${s} ano${1!==s?"s":""}${a>0?` e ${a} ${1===a?"mês":"meses"}`:""}`}return`${e.age_months} ${1===e.age_months?"mês":"meses"}`};return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:s.map((s=>e.jsxs(u,{children:[e.jsxs(j,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(v,{className:"text-sm font-medium",children:o(s)}),e.jsxs(D,{children:[e.jsx(T,{asChild:!0,children:e.jsx(x,{variant:"ghost",size:"icon",className:"h-8 w-8",children:e.jsx(L,{className:"h-4 w-4"})})}),e.jsxs(F,{children:[e.jsxs(U,{children:[e.jsx($,{children:"Confirmar exclusão"}),e.jsx(z,{children:"Tem certeza que deseja excluir este marco DNPM? Esta ação não pode ser desfeita."})]}),e.jsxs(A,{children:[e.jsx(E,{children:"Cancelar"}),e.jsx(I,{onClick:()=>i(s.id),children:"Confirmar"})]})]})]})]}),e.jsx(p,{children:s.social_emotional&&e.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:s.social_emotional})})]},s.id)))})},O=()=>{const[r,t]=s.useState(!1),[l,m]=s.useState(!1),{toast:c}=o(),d=a(),{data:g,isLoading:h}=i({queryKey:["dnpm-milestones"],queryFn:async()=>{const{data:e,error:s}=await n.from("pedbook_dnpm_milestones").select("*").order("age_months",{ascending:!0});if(s)throw s;return e}});return e.jsxs("div",{className:"container mx-auto py-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Marcos DNPM"}),e.jsxs(f,{open:l,onOpenChange:m,children:[e.jsx(y,{asChild:!0,children:e.jsx(x,{children:"Novo Marco DNPM"})}),e.jsxs(N,{className:"max-w-2xl",children:[e.jsx(M,{children:e.jsx(b,{children:"Cadastro de Marco DNPM"})}),e.jsx(k,{onSubmit:async e=>{t(!0);try{const{error:s}=await n.from("pedbook_dnpm_milestones").insert({age_type:e.ageType,age_years:"years"===e.ageType?e.ageYears:null,age_months:"months"===e.ageType?e.ageMonths:12*(e.ageYears||0)+e.ageMonths,social_emotional:e.socialEmotional,language_communication:e.languageCommunication,cognition:e.cognition,motor_physical:e.motorPhysical,image_url:e.imageUrl});if(s)throw s;c({title:"Marco DNPM criado com sucesso!",description:"O novo marco foi adicionado ao sistema."}),d.invalidateQueries({queryKey:["dnpm-milestones"]}),m(!1)}catch(s){c({variant:"destructive",title:"Erro ao criar marco DNPM",description:s.message})}finally{t(!1)}},isLoading:r})]})]})]}),e.jsx(K,{milestones:g||[],isLoading:h,onDelete:async e=>{try{const{error:s}=await n.from("pedbook_dnpm_milestones").delete().eq("id",e);if(s)throw s;c({title:"Marco DNPM removido com sucesso!",description:"O marco foi removido do sistema."}),d.invalidateQueries({queryKey:["dnpm-milestones"]})}catch(s){c({variant:"destructive",title:"Erro ao remover marco DNPM",description:s.message})}}})]})};export{O as default};
