
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ArrowLef<PERSON>, Sparkles, Calculator } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { HydrationCalculator } from "@/components/calculators/hydration/HydrationCalculator";
import { AdvancedHydrationCalculator } from "@/components/calculators/hydration/AdvancedHydrationCalculator";
import { HydrationInfoCard } from "@/components/calculators/hydration/HydrationInfoCard";
import { ReferencesCard } from "@/components/calculators/hydration/ReferencesCard";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { StudyMaterialDialog } from "@/components/study-material/StudyMaterialDialog";

const HydrationCalculatorPage = () => {
  const navigate = useNavigate();
  const seoData = FLOWCHART_SEO_DATA['hidratacao'];
  const [selectedCalculator, setSelectedCalculator] = useState<'selection' | 'simple' | 'advanced'>('selection');
  const [studyMaterialOpen, setStudyMaterialOpen] = useState(false);

  const handleCalculatorSelect = (type: 'simple' | 'advanced') => {
    setSelectedCalculator(type);
  };

  const handleBackToSelection = () => {
    setSelectedCalculator('selection');
  };

  // Determinar para onde voltar baseado na URL atual
  const getBackPath = () => {
    const currentPath = window.location.pathname;
    if (currentPath.includes('/flowcharts/')) {
      return '/flowcharts';
    }
    return '/calculadoras';
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <FlowchartSEO {...seoData} />
      <Header />

      <main className="flex-1 container mx-auto px-4 py-2 md:py-8">
        <div className="max-w-6xl mx-auto space-y-3 md:space-y-8">
          {/* Layout Desktop */}
          <div className="hidden md:flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => selectedCalculator === 'selection' ? navigate(getBackPath()) : handleBackToSelection()}
              className="h-10 w-10 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </Button>

            <div className="flex-1">
              <h1 className={getThemeClasses.gradientHeading("text-3xl text-center")}>
                {selectedCalculator === 'selection'
                  ? 'Calculadora de Hidratação Pediátrica'
                  : selectedCalculator === 'advanced'
                    ? 'Calculadora Avançada de Hidratação'
                    : 'Calculadora Simples de Hidratação'
                }
              </h1>
              <p className="text-center text-gray-600 dark:text-gray-400 mt-2">
                {selectedCalculator === 'selection'
                  ? 'Escolha a versão que melhor atende suas necessidades'
                  : 'Cálculo preciso de hidratação venosa baseado em evidências científicas'
                }
              </p>
            </div>

            <StudyMaterialDialog
              open={studyMaterialOpen}
              onOpenChange={setStudyMaterialOpen}
            />
          </div>

          {/* Layout Mobile */}
          <div className="md:hidden space-y-3">
            {/* Linha superior: Botão voltar + Material de estudo */}
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => selectedCalculator === 'selection' ? navigate(getBackPath()) : handleBackToSelection()}
                className="h-9 w-9 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 shadow-sm"
              >
                <ArrowLeft className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </Button>

              <StudyMaterialDialog
                open={studyMaterialOpen}
                onOpenChange={setStudyMaterialOpen}
              />
            </div>

            {/* Título e descrição centralizados */}
            <div className="text-center space-y-1">
              <h1 className={getThemeClasses.gradientHeading("text-xl leading-tight")}>
                {selectedCalculator === 'selection'
                  ? 'Calculadora de Hidratação Pediátrica'
                  : selectedCalculator === 'advanced'
                    ? 'Calculadora Avançada'
                    : 'Calculadora Simples'
                }
              </h1>
              <p className="text-xs text-gray-600 dark:text-gray-400 px-4 leading-tight">
                {selectedCalculator === 'selection'
                  ? 'Escolha a versão que melhor atende suas necessidades'
                  : 'Cálculo preciso de hidratação venosa baseado em evidências científicas'
                }
              </p>
            </div>
          </div>

          {selectedCalculator === 'selection' && (
            <>
              <HydrationInfoCard />

              {/* Cards de Seleção */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                {/* Calculadora Simples */}
                <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-800 hover:border-green-300 dark:hover:border-green-600">
                  <div
                    className="p-8 h-full flex flex-col"
                    onClick={() => handleCalculatorSelect('simple')}
                  >
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-3 rounded-full bg-gradient-to-r from-green-500 to-blue-500 text-white group-hover:scale-110 transition-transform duration-300">
                        <Calculator className="h-8 w-8" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                          Calculadora Simples
                        </h3>
                        <p className="text-green-600 dark:text-green-400 font-medium">
                          Rápida e Prática
                        </p>
                      </div>
                    </div>

                    <div className="flex-1 space-y-4">
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        Ideal para cálculos rápidos de hidratação de manutenção com configurações padrão.
                      </p>

                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-800 dark:text-gray-200">Características:</h4>
                        <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            Regra de Holliday-Segar
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            Eletrólitos padrão (Na+, K+)
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            Taxas de infusão automáticas
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            Interface simplificada
                          </li>
                        </ul>
                      </div>
                    </div>

                    <Button className="w-full mt-6 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 shadow-lg hover:shadow-xl transition-all duration-200 relative z-10 flex-shrink-0">
                      Usar Calculadora Simples
                    </Button>
                  </div>
                </Card>

                {/* Calculadora Avançada */}
                <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-600">
                  <div
                    className="p-8 h-full flex flex-col"
                    onClick={() => handleCalculatorSelect('advanced')}
                  >
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white group-hover:scale-110 transition-transform duration-300">
                        <Sparkles className="h-8 w-8" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                          Calculadora Avançada
                        </h3>
                        <p className="text-blue-600 dark:text-blue-400 font-medium">
                          Completa e Personalizável
                        </p>
                      </div>
                    </div>

                    <div className="flex-1 space-y-4">
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        Ferramenta completa com múltiplas concentrações, presets clínicos e exportação de prescrições.
                      </p>

                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-800 dark:text-gray-200">Características:</h4>
                        <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            Múltiplas concentrações (NaCl, KCl, etc.)
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            Eletrólitos completos (Ca²⁺, Mg²⁺)
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            Presets clínicos especializados
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            Exportação de prescrições
                          </li>
                          <li className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            Validações de segurança
                          </li>
                        </ul>
                      </div>
                    </div>

                    <Button className="w-full mt-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 shadow-lg hover:shadow-xl transition-all duration-200 relative z-10 flex-shrink-0">
                      Usar Calculadora Avançada
                    </Button>
                  </div>
                </Card>
              </div>
            </>
          )}

          {selectedCalculator === 'simple' && <HydrationCalculator />}
          {selectedCalculator === 'advanced' && <AdvancedHydrationCalculator />}

          <ReferencesCard />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default HydrationCalculatorPage;
