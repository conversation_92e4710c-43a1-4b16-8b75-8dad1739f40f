import{j as e}from"./radix-core-6kBL75b5.js";import{c as t,d as n}from"./critical-DVX9Inzy.js";const r=function(e){if(null==e)return i;if("function"==typeof e)return o(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=r(e[n]);return o((function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}))}(e):function(e){const t=e;return o((function(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}))}(e);if("string"==typeof e)return t=e,o((function(e){return e&&e.type===t}));var t;throw new Error("Expected function, string, or object as test")};function o(e){return function(t,n,r){return Boolean(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function i(){return!0}const l=[],a=!1,s="skip";function u(e,t,n,o){let i;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):i=t;const u=r(i),c=o?-1:1;!function e(r,i,f){const p=r&&"object"==typeof r?r:{};if("string"==typeof p.type){const e="string"==typeof p.tagName?p.tagName:"string"==typeof p.name?p.name:void 0;Object.defineProperty(h,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return h;function h(){let p,h,d,m=l;if((!t||u(r,i,f[f.length-1]||void 0))&&(m=function(e){return Array.isArray(e)?e:"number"==typeof e?[true,e]:null==e?l:[e]}(n(r,f)),m[0]===a))return m;if("children"in r&&r.children){const t=r;if(t.children&&m[0]!==s)for(h=(o?t.children.length:-1)+c,d=f.concat(t);h>-1&&h<t.children.length;){const n=t.children[h];if(p=e(n,h,d)(),p[0]===a)return p;h="number"==typeof p[1]?p[1]:h+c}}return m}}(e,void 0,[])()}function c(e,t,n,r){let o,i,l;"function"==typeof t&&"function"!=typeof n?(i=void 0,l=t,o=n):(i=t,l=n,o=r),u(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)}),o)}class f{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}}function p(e,t){const n={},r={};let o=-1;for(;++o<e.length;)Object.assign(n,e[o].property),Object.assign(r,e[o].normal);return new f(n,r,t)}function h(e){return e.toLowerCase()}f.prototype.property={},f.prototype.normal={},f.prototype.space=null;class d{constructor(e,t){this.property=e,this.attribute=t}}d.prototype.space=null,d.prototype.boolean=!1,d.prototype.booleanish=!1,d.prototype.overloadedBoolean=!1,d.prototype.number=!1,d.prototype.commaSeparated=!1,d.prototype.spaceSeparated=!1,d.prototype.commaOrSpaceSeparated=!1,d.prototype.mustUseProperty=!1,d.prototype.defined=!1;let m=0;const g=S(),y=S(),x=S(),v=S(),k=S(),b=S(),w=S();function S(){return 2**++m}const C=Object.freeze(Object.defineProperty({__proto__:null,boolean:g,booleanish:y,commaOrSpaceSeparated:w,commaSeparated:b,number:v,overloadedBoolean:x,spaceSeparated:k},Symbol.toStringTag,{value:"Module"})),I=Object.keys(C);class E extends d{constructor(e,t,n,r){let o=-1;if(super(e,t),P(this,"space",r),"number"==typeof n)for(;++o<I.length;){const e=I[o];P(this,I[o],(n&C[e])===C[e])}}}function P(e,t,n){n&&(e[t]=n)}E.prototype.defined=!0;const T={}.hasOwnProperty;function A(e){const t={},n={};let r;for(r in e.properties)if(T.call(e.properties,r)){const o=e.properties[r],i=new E(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[h(r)]=r,n[h(i.attribute)]=r}return new f(t,n,e.space)}const L=A({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),O=A({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function N(e,t){return t in e?e[t]:t}function D(e,t){return N(e,t.toLowerCase())}const M=A({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:D,properties:{xmlns:null,xmlnsXLink:null}}),z=A({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:y,ariaAutoComplete:null,ariaBusy:y,ariaChecked:y,ariaColCount:v,ariaColIndex:v,ariaColSpan:v,ariaControls:k,ariaCurrent:null,ariaDescribedBy:k,ariaDetails:null,ariaDisabled:y,ariaDropEffect:k,ariaErrorMessage:null,ariaExpanded:y,ariaFlowTo:k,ariaGrabbed:y,ariaHasPopup:null,ariaHidden:y,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:k,ariaLevel:v,ariaLive:null,ariaModal:y,ariaMultiLine:y,ariaMultiSelectable:y,ariaOrientation:null,ariaOwns:k,ariaPlaceholder:null,ariaPosInSet:v,ariaPressed:y,ariaReadOnly:y,ariaRelevant:null,ariaRequired:y,ariaRoleDescription:k,ariaRowCount:v,ariaRowIndex:v,ariaRowSpan:v,ariaSelected:y,ariaSetSize:v,ariaSort:null,ariaValueMax:v,ariaValueMin:v,ariaValueNow:v,ariaValueText:null,role:null}}),F=A({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:D,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:b,acceptCharset:k,accessKey:k,action:null,allow:null,allowFullScreen:g,allowPaymentRequest:g,allowUserMedia:g,alt:null,as:null,async:g,autoCapitalize:null,autoComplete:k,autoFocus:g,autoPlay:g,blocking:k,capture:null,charSet:null,checked:g,cite:null,className:k,cols:v,colSpan:null,content:null,contentEditable:y,controls:g,controlsList:k,coords:v|b,crossOrigin:null,data:null,dateTime:null,decoding:null,default:g,defer:g,dir:null,dirName:null,disabled:g,download:x,draggable:y,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:g,formTarget:null,headers:k,height:v,hidden:g,high:v,href:null,hrefLang:null,htmlFor:k,httpEquiv:k,id:null,imageSizes:null,imageSrcSet:null,inert:g,inputMode:null,integrity:null,is:null,isMap:g,itemId:null,itemProp:k,itemRef:k,itemScope:g,itemType:k,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:g,low:v,manifest:null,max:null,maxLength:v,media:null,method:null,min:null,minLength:v,multiple:g,muted:g,name:null,nonce:null,noModule:g,noValidate:g,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:g,optimum:v,pattern:null,ping:k,placeholder:null,playsInline:g,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:g,referrerPolicy:null,rel:k,required:g,reversed:g,rows:v,rowSpan:v,sandbox:k,scope:null,scoped:g,seamless:g,selected:g,shadowRootClonable:g,shadowRootDelegatesFocus:g,shadowRootMode:null,shape:null,size:v,sizes:null,slot:null,span:v,spellCheck:y,src:null,srcDoc:null,srcLang:null,srcSet:null,start:v,step:null,style:null,tabIndex:v,target:null,title:null,translate:null,type:null,typeMustMatch:g,useMap:null,value:y,width:v,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:k,axis:null,background:null,bgColor:null,border:v,borderColor:null,bottomMargin:v,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:g,declare:g,event:null,face:null,frame:null,frameBorder:null,hSpace:v,leftMargin:v,link:null,longDesc:null,lowSrc:null,marginHeight:v,marginWidth:v,noResize:g,noHref:g,noShade:g,noWrap:g,object:null,profile:null,prompt:null,rev:null,rightMargin:v,rules:null,scheme:null,scrolling:y,standby:null,summary:null,text:null,topMargin:v,valueType:null,version:null,vAlign:null,vLink:null,vSpace:v,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:g,disableRemotePlayback:g,prefix:null,property:null,results:v,security:null,unselectable:null}}),_=A({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:N,properties:{about:w,accentHeight:v,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:v,amplitude:v,arabicForm:null,ascent:v,attributeName:null,attributeType:null,azimuth:v,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:v,by:null,calcMode:null,capHeight:v,className:k,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:v,diffuseConstant:v,direction:null,display:null,dur:null,divisor:v,dominantBaseline:null,download:g,dx:null,dy:null,edgeMode:null,editable:null,elevation:v,enableBackground:null,end:null,event:null,exponent:v,externalResourcesRequired:null,fill:null,fillOpacity:v,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:b,g2:b,glyphName:b,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:v,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:v,horizOriginX:v,horizOriginY:v,id:null,ideographic:v,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:v,k:v,k1:v,k2:v,k3:v,k4:v,kernelMatrix:w,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:v,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:v,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:v,overlineThickness:v,paintOrder:null,panose1:null,path:null,pathLength:v,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:k,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:v,pointsAtY:v,pointsAtZ:v,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:w,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:w,rev:w,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:w,requiredFeatures:w,requiredFonts:w,requiredFormats:w,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:v,specularExponent:v,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:v,strikethroughThickness:v,string:null,stroke:null,strokeDashArray:w,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:v,strokeOpacity:v,strokeWidth:null,style:null,surfaceScale:v,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:w,tabIndex:v,tableValues:null,target:null,targetX:v,targetY:v,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:w,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:v,underlineThickness:v,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:v,values:null,vAlphabetic:v,vMathematical:v,vectorEffect:null,vHanging:v,vIdeographic:v,version:null,vertAdvY:v,vertOriginX:v,vertOriginY:v,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:v,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),R=/^data[-\w.:]+$/i,j=/-[a-z]/g,B=/[A-Z]/g;function H(e,t){const n=h(t);let r=t,o=d;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&R.test(t)){if("-"===t.charAt(4)){const e=t.slice(5).replace(j,V);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=t.slice(4);if(!j.test(e)){let n=e.replace(B,U);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}o=E}return new o(r,t)}function U(e){return"-"+e.toLowerCase()}function V(e){return e.charAt(1).toUpperCase()}const q={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},W=p([O,L,M,z,F],"html"),Y=p([O,L,M,z,_],"svg");function K(e){const t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function Q(e){return e.join(" ").trim()}function X(e){const t=[],n=String(e||"");let r=n.indexOf(","),o=0,i=!1;for(;!i;){-1===r&&(r=n.length,i=!0);const e=n.slice(o,r).trim();!e&&i||t.push(e),o=r+1,r=n.indexOf(",",o)}return t}function $(e,t){const n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}const J=document.createElement("i");function G(e){const t="&"+e+";";J.innerHTML=t;const n=J.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}function Z(){}const ee=/[ \t\n\f\r]/g;function te(e){return"object"==typeof e?"text"===e.type&&ne(e.value):ne(e)}function ne(e){return""===e.replace(ee,"")}const re="object"==typeof self?self:globalThis,oe=e=>((e,t)=>{const n=(t,n)=>(e.set(n,t),t),r=o=>{if(e.has(o))return e.get(o);const[i,l]=t[o];switch(i){case 0:case-1:return n(l,o);case 1:{const e=n([],o);for(const t of l)e.push(r(t));return e}case 2:{const e=n({},o);for(const[t,n]of l)e[r(t)]=r(n);return e}case 3:return n(new Date(l),o);case 4:{const{source:e,flags:t}=l;return n(new RegExp(e,t),o)}case 5:{const e=n(new Map,o);for(const[t,n]of l)e.set(r(t),r(n));return e}case 6:{const e=n(new Set,o);for(const t of l)e.add(r(t));return e}case 7:{const{name:e,message:t}=l;return n(new re[e](t),o)}case 8:return n(BigInt(l),o);case"BigInt":return n(Object(BigInt(l)),o)}return n(new re[i](l),o)};return r})(new Map,e)(0),ie="",{toString:le}={},{keys:ae}=Object,se=e=>{const t=typeof e;if("object"!==t||!e)return[0,t];const n=le.call(e).slice(8,-1);switch(n){case"Array":return[1,ie];case"Object":return[2,ie];case"Date":return[3,ie];case"RegExp":return[4,ie];case"Map":return[5,ie];case"Set":return[6,ie]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},ue=([e,t])=>0===e&&("function"===t||"symbol"===t),ce=(e,{json:t,lossy:n}={})=>{const r=[];return((e,t,n,r)=>{const o=(e,t)=>{const o=r.push(e)-1;return n.set(t,o),o},i=r=>{if(n.has(r))return n.get(r);let[l,a]=se(r);switch(l){case 0:{let t=r;switch(a){case"bigint":l=8,t=r.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+a);t=null;break;case"undefined":return o([-1],r)}return o([l,t],r)}case 1:{if(a)return o([a,[...r]],r);const e=[],t=o([l,e],r);for(const n of r)e.push(i(n));return t}case 2:{if(a)switch(a){case"BigInt":return o([a,r.toString()],r);case"Boolean":case"Number":case"String":return o([a,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());const n=[],s=o([l,n],r);for(const t of ae(r))!e&&ue(se(r[t]))||n.push([i(t),i(r[t])]);return s}case 3:return o([l,r.toISOString()],r);case 4:{const{source:e,flags:t}=r;return o([l,{source:e,flags:t}],r)}case 5:{const t=[],n=o([l,t],r);for(const[o,l]of r)(e||!ue(se(o))&&!ue(se(l)))&&t.push([i(o),i(l)]);return n}case 6:{const t=[],n=o([l,t],r);for(const o of r)!e&&ue(se(o))||t.push(i(o));return n}}const{message:s}=r;return o([l,{name:a,message:s}],r)};return i})(!(t||n),!!t,new Map,r)(e),r},fe="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?oe(ce(e,t)):structuredClone(e):(e,t)=>oe(ce(e,t)),pe=/[#.]/g,he={}.hasOwnProperty;function de(e,t,n){const r=n&&function(e){const t={};let n=-1;for(;++n<e.length;)t[e[n].toLowerCase()]=e[n];return t}(n);return function(n,o,...i){let l,a=-1;if(null==n){l={type:"root",children:[]};const e=o;i.unshift(e)}else if(l=function(e,t){const n=e||"",r={};let o,i,l=0;for(;l<n.length;){pe.lastIndex=l;const e=pe.exec(n),t=n.slice(l,e?e.index:n.length);t&&(o?"#"===o?r.id=t:Array.isArray(r.className)?r.className.push(t):r.className=[t]:i=t,l+=t.length),e&&(o=e[0],l++)}return{type:"element",tagName:i||t||"div",properties:r,children:[]}}(n,t),l.tagName=l.tagName.toLowerCase(),r&&he.call(r,l.tagName)&&(l.tagName=r[l.tagName]),function(e){if(null===e||"object"!=typeof e||Array.isArray(e))return!0;if("string"!=typeof e.type)return!1;const t=e,n=Object.keys(e);for(const r of n){const e=t[r];if(e&&"object"==typeof e){if(!Array.isArray(e))return!0;const t=e;for(const e of t)if("number"!=typeof e&&"string"!=typeof e)return!0}}return!(!("children"in e)||!Array.isArray(e.children))}(o))i.unshift(o);else{let t;for(t in o)he.call(o,t)&&me(e,l.properties,t,o[t])}for(;++a<i.length;)ge(l.children,i[a]);return"element"===l.type&&"template"===l.tagName&&(l.content={type:"root",children:l.children},l.children=[]),l}}function me(e,t,n,r){const o=H(e,n);let i,l=-1;if(null!=r){if("number"==typeof r){if(Number.isNaN(r))return;i=r}else i="boolean"==typeof r?r:"string"==typeof r?o.spaceSeparated?K(r):o.commaSeparated?X(r):o.commaOrSpaceSeparated?K(X(r).join(" ")):ye(o,o.property,r):Array.isArray(r)?r.concat():"style"===o.property?function(e){const t=[];let n;for(n in e)he.call(e,n)&&t.push([n,e[n]].join(": "));return t.join("; ")}(r):String(r);if(Array.isArray(i)){const e=[];for(;++l<i.length;){const t=ye(o,o.property,i[l]);e[l]=t}i=e}if("className"===o.property&&Array.isArray(t.className)){const e=i;i=t.className.concat(e)}t[o.property]=i}}function ge(e,t){let n=-1;if(null==t);else if("string"==typeof t||"number"==typeof t)e.push({type:"text",value:String(t)});else if(Array.isArray(t))for(;++n<t.length;)ge(e,t[n]);else{if("object"!=typeof t||!("type"in t))throw new Error("Expected node, nodes, or string, got `"+t+"`");"root"===t.type?ge(e,t.children):e.push(t)}}function ye(e,t,n){if("string"==typeof n){if(e.number&&n&&!Number.isNaN(Number(n)))return Number(n);if((e.boolean||e.overloadedBoolean)&&(""===n||h(n)===h(t)))return!0}return n}const xe=de(W,"div"),ve=de(Y,"g",["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"]),ke={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},be=Se("end"),we=Se("start");function Se(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}const Ce=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Ie=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Ee={};function Pe(e,t){return(Ee.jsx?Ie:Ce).test(e)}var Te={},Ae=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,Le=/\n/g,Oe=/^\s*/,Ne=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,De=/^:\s*/,Me=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,ze=/^[;\s]*/,Fe=/^\s+|\s+$/g,_e="";function Re(e){return e?e.replace(Fe,_e):_e}var je=t&&t.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Te,"__esModule",{value:!0});var Be=Te.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,He.default)(e),o="function"==typeof t;return r.forEach((function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}})),n},He=je((function(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function o(e){var t=e.match(Le);t&&(n+=t.length);var o=e.lastIndexOf("\n");r=~o?e.length-o:r+e.length}function i(){var e={line:n,column:r};return function(t){return t.position=new l(e),u(),t}}function l(e){this.start=e,this.end={line:n,column:r},this.source=t.source}function a(o){var i=new Error(t.source+":"+n+":"+r+": "+o);if(i.reason=o,i.filename=t.source,i.line=n,i.column=r,i.source=e,!t.silent)throw i}function s(t){var n=t.exec(e);if(n){var r=n[0];return o(r),e=e.slice(r.length),n}}function u(){s(Oe)}function c(e){var t;for(e=e||[];t=f();)!1!==t&&e.push(t);return e}function f(){var t=i();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;_e!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,_e===e.charAt(n-1))return a("End of comment missing");var l=e.slice(2,n-2);return r+=2,o(l),e=e.slice(n),r+=2,t({type:"comment",comment:l})}}function p(){var e=i(),t=s(Ne);if(t){if(f(),!s(De))return a("property missing ':'");var n=s(Me),r=e({type:"declaration",property:Re(t[0].replace(Ae,_e)),value:n?Re(n[0].replace(Ae,_e)):_e});return s(ze),r}}return l.prototype.content=e,u(),function(){var e,t=[];for(c(t);e=p();)!1!==e&&(t.push(e),c(t));return t}()}));const Ue=Be.default||Be;function Ve(e){return e&&"object"==typeof e?"position"in e||"type"in e?We(e.position):"start"in e||"end"in e?We(e):"line"in e||"column"in e?qe(e):"":""}function qe(e){return Ye(e&&e.line)+":"+Ye(e&&e.column)}function We(e){return qe(e&&e.start)+"-"+qe(e&&e.end)}function Ye(e){return e&&"number"==typeof e?e:1}class Ke extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",o={},i=!1;if(t&&(o="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=Ve(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}Ke.prototype.file="",Ke.prototype.name="",Ke.prototype.reason="",Ke.prototype.message="",Ke.prototype.stack="",Ke.prototype.column=void 0,Ke.prototype.line=void 0,Ke.prototype.ancestors=void 0,Ke.prototype.cause=void 0,Ke.prototype.fatal=void 0,Ke.prototype.place=void 0,Ke.prototype.ruleId=void 0,Ke.prototype.source=void 0;const Qe={}.hasOwnProperty,Xe=new Map,$e=/[A-Z]/g,Je=/-([a-z])/g,Ge=new Set(["table","tbody","thead","tfoot","tr"]),Ze=new Set(["td","th"]),et="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function tt(e,t,n){return"element"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(o=Y,e.schema=o),e.ancestors.push(t);const i=lt(e,t.tagName,!1),l=function(e,t){const n={};let r,o;for(o in t.properties)if("children"!==o&&Qe.call(t.properties,o)){const i=it(e,o,t.properties[o]);if(i){const[o,l]=i;e.tableCellAlignToStyle&&"align"===o&&"string"==typeof l&&Ze.has(t.tagName)?r=l:n[o]=l}}return r&&((n.style||(n.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=r),n}(e,t);let a=ot(e,t);return Ge.has(t.tagName)&&(a=a.filter((function(e){return"string"!=typeof e||!te(e)}))),nt(e,l,i,t),rt(l,a),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){const n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}at(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.name&&"html"===r.space&&(o=Y,e.schema=o),e.ancestors.push(t);const i=null===t.name?e.Fragment:lt(e,t.name,!0),l=function(e,t){const n={};for(const r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){const t=r.data.estree.body[0];t.type;const o=t.expression;o.type;const i=o.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else at(e,t.position);else{const o=r.name;let i;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){const t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else at(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),a=ot(e,t);return nt(e,l,i,t),rt(l,a),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);at(e,t.position)}(e,t):"root"===t.type?function(e,t,n){const r={};return rt(r,ot(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?function(e,t){return t.value}(0,t):void 0}function nt(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function rt(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function ot(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:Xe;for(;++r<t.children.length;){const i=t.children[r];let l;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const t=o.get(e)||0;l=e+"-"+t,o.set(e,t+1)}}const a=tt(e,i,l);void 0!==a&&n.push(a)}return n}function it(e,t,n){const r=H(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?$(n):Q(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){const n={};try{Ue(t,(function(e,t){let r=e;"--"!==r.slice(0,2)&&("-ms-"===r.slice(0,4)&&(r="ms-"+r.slice(4)),r=r.replace(Je,ut)),n[r]=t}))}catch(r){if(!e.ignoreInvalidStyle){const t=r,n=new Ke("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=et+"#cannot-parse-style-attribute",n}}return n}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){const t={};let n;for(n in e)Qe.call(e,n)&&(t[st(n)]=e[n]);return t}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?q[r.property]||r.property:r.attribute,n]}}function lt(e,t,n){let r;if(n)if(t.includes(".")){const e=t.split(".");let n,o=-1;for(;++o<e.length;){const t=Pe(e[o])?{type:"Identifier",name:e[o]}:{type:"Literal",value:e[o]};n=n?{type:"MemberExpression",object:n,property:t,computed:Boolean(o&&"Literal"===t.type),optional:!1}:t}r=n}else r=Pe(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){const t=r.value;return Qe.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);at(e)}function at(e,t){const n=new Ke("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=et+"#cannot-handle-mdx-estrees-without-createevaluater",n}function st(e){let t=e.replace($e,ct);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function ut(e,t){return t.toUpperCase()}function ct(e){return"-"+e.toLowerCase()}const ft={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},pt={};function ht(e,t){return dt(e,"boolean"!=typeof pt.includeImageAlt||pt.includeImageAlt,"boolean"!=typeof pt.includeHtml||pt.includeHtml)}function dt(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return mt(e.children,t,n)}return Array.isArray(e)?mt(e,t,n):""}function mt(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=dt(e[o],t,n);return r.join("")}function gt(e,t,n,r){const o=e.length;let i,l=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)i=Array.from(r),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<r.length;)i=r.slice(l,l+1e4),i.unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function yt(e,t){return e.length>0?(gt(e,e.length,0,t),e):t}const xt={}.hasOwnProperty;function vt(e){const t={};let n=-1;for(;++n<e.length;)kt(t,e[n]);return t}function kt(e,t){let n;for(n in t){const r=(xt.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let i;if(o)for(i in o){xt.call(r,i)||(r[i]=[]);const e=o[i];bt(r[i],Array.isArray(e)?e:e?[e]:[])}}}function bt(e,t){let n=-1;const r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);gt(e,0,0,r)}function wt(e,t){const n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||!(65535&~n)||65534==(65535&n)||n>1114111?"�":String.fromCodePoint(n)}function St(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Ct=Ft(/[A-Za-z]/),It=Ft(/[\dA-Za-z]/),Et=Ft(/[#-'*+\--9=?A-Z^-~]/);function Pt(e){return null!==e&&(e<32||127===e)}const Tt=Ft(/\d/),At=Ft(/[\dA-Fa-f]/),Lt=Ft(/[!-/:-@[-`{-~]/);function Ot(e){return null!==e&&e<-2}function Nt(e){return null!==e&&(e<0||32===e)}function Dt(e){return-2===e||-1===e||32===e}const Mt=Ft(/\p{P}|\p{S}/u),zt=Ft(/\s/);function Ft(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function _t(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let l="";if(37===i&&It(e.charCodeAt(n+1))&&It(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){const t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(l=String.fromCharCode(i,t),o=1):l="�"}else l=String.fromCharCode(i);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+o+1,l=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}function Rt(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){return Dt(r)?(e.enter(n),l(r)):t(r)};function l(r){return Dt(r)&&i++<o?(e.consume(r),l):(e.exit(n),t(r))}}const jt={tokenize:function(e){const t=e.attempt(this.parser.constructs.contentInitial,(function(n){if(null!==n)return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),Rt(e,t,"linePrefix");e.consume(n)}),(function(t){return e.enter("paragraph"),r(t)}));let n;return t;function r(t){const r=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=r),n=r,o(t)}function o(t){return null===t?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(t)):Ot(t)?(e.consume(t),e.exit("chunkText"),r):(e.consume(t),o)}}},Bt={tokenize:function(e){const t=this,n=[];let r,o,i,l=0;return a;function a(r){if(l<n.length){const o=n[l];return t.containerState=o[1],e.attempt(o[0].continuation,s,u)(r)}return u(r)}function s(e){if(l++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,r&&x();const n=t.events.length;let o,i=n;for(;i--;)if("exit"===t.events[i][0]&&"chunkFlow"===t.events[i][1].type){o=t.events[i][1].end;break}y(l);let a=n;for(;a<t.events.length;)t.events[a][1].end={...o},a++;return gt(t.events,i+1,0,t.events.slice(n)),t.events.length=a,u(e)}return a(e)}function u(o){if(l===n.length){if(!r)return p(o);if(r.currentConstruct&&r.currentConstruct.concrete)return d(o);t.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Ht,c,f)(o)}function c(e){return r&&x(),y(l),p(e)}function f(e){return t.parser.lazy[t.now().line]=l!==n.length,i=t.now().offset,d(e)}function p(n){return t.containerState={},e.attempt(Ht,h,d)(n)}function h(e){return l++,n.push([t.currentConstruct,t.containerState]),p(e)}function d(n){return null===n?(r&&x(),y(0),void e.consume(n)):(r=r||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),m(n))}function m(n){return null===n?(g(e.exit("chunkFlow"),!0),y(0),void e.consume(n)):Ot(n)?(e.consume(n),g(e.exit("chunkFlow")),l=0,t.interrupt=void 0,a):(e.consume(n),m)}function g(e,n){const a=t.sliceStream(e);if(n&&a.push(null),e.previous=o,o&&(o.next=e),o=e,r.defineSkip(e.start),r.write(a),t.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<i&&(!r.events[e][1].end||r.events[e][1].end.offset>i))return;const n=t.events.length;let o,a,s=n;for(;s--;)if("exit"===t.events[s][0]&&"chunkFlow"===t.events[s][1].type){if(o){a=t.events[s][1].end;break}o=!0}for(y(l),e=n;e<t.events.length;)t.events[e][1].end={...a},e++;gt(t.events,s+1,0,t.events.slice(n)),t.events.length=e}}function y(r){let o=n.length;for(;o-- >r;){const r=n[o];t.containerState=r[1],r[0].exit.call(t,e)}n.length=r}function x(){r.write([null]),o=void 0,r=void 0,t.containerState._closeFlow=void 0}}},Ht={tokenize:function(e,t,n){return Rt(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};function Ut(e){return null===e||Nt(e)||zt(e)?1:Mt(e)?2:void 0}function Vt(e,t,n){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}const qt={name:"attention",resolveAll:function(e,t){let n,r,o,i,l,a,s,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close)for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;const f={...e[n][1].end},p={...e[c][1].start};Wt(f,-a),Wt(p,a),i={type:a>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},l={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},o={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...i.start},end:{...l.end}},e[n][1].end={...i.start},e[c][1].start={...l.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=yt(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=yt(s,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",o,t]]),s=yt(s,Vt(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),s=yt(s,[["exit",o,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,s=yt(s,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,gt(e,n-1,c-n+3,s),c=n+s.length-u-2;break}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,o=Ut(r);let i;return function(t){return i=t,e.enter("attentionSequence"),l(t)};function l(a){if(a===i)return e.consume(a),l;const s=e.exit("attentionSequence"),u=Ut(a),c=!u||2===u&&o||n.includes(a),f=!o||2===o&&u||n.includes(r);return s._open=Boolean(42===i?c:c&&(o||!f)),s._close=Boolean(42===i?f:f&&(u||!c)),t(a)}}};function Wt(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Yt={name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o};function o(t){return Ct(t)?(e.consume(t),i):64===t?n(t):s(t)}function i(e){return 43===e||45===e||46===e||It(e)?(r=1,l(e)):s(e)}function l(t){return 58===t?(e.consume(t),r=0,a):(43===t||45===t||46===t||It(t))&&r++<32?(e.consume(t),l):(r=0,s(t))}function a(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||Pt(r)?n(r):(e.consume(r),a)}function s(t){return 64===t?(e.consume(t),u):Et(t)?(e.consume(t),s):n(t)}function u(e){return It(e)?c(e):n(e)}function c(n){return 46===n?(e.consume(n),r=0,u):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):f(n)}function f(t){if((45===t||It(t))&&r++<63){const n=45===t?f:c;return e.consume(t),n}return n(t)}}},Kt={partial:!0,tokenize:function(e,t,n){return function(t){return Dt(t)?Rt(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||Ot(e)?t(e):n(e)}}},Qt={continuation:{tokenize:function(e,t,n){const r=this;return function(t){return Dt(t)?Rt(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):o(t)};function o(r){return e.attempt(Qt,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){const r=this;return function(t){if(62===t){const n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),o}return n(t)};function o(n){return Dt(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}},Xt={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return Lt(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},$t={name:"characterReference",tokenize:function(e,t,n){const r=this;let o,i,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),o=31,i=It,u(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,i=At,u):(e.enter("characterReferenceValue"),o=7,i=Tt,u(t))}function u(a){if(59===a&&l){const o=e.exit("characterReferenceValue");return i!==It||G(r.sliceSerialize(o))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&l++<o?(e.consume(a),u):n(a)}}},Jt={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o)};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},Gt={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){const r=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l};function l(t){return e.enter("codeFencedFence"),Dt(t)?Rt(e,s,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===i?(e.enter("codeFencedFenceSequence"),u(t)):n(t)}function u(t){return t===i?(o++,e.consume(t),u):o>=a?(e.exit("codeFencedFenceSequence"),Dt(t)?Rt(e,c,"whitespace")(t):c(t)):n(t)}function c(r){return null===r||Ot(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}};let i,l=0,a=0;return function(t){return function(t){const n=r.events[r.events.length-1];return l=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,i=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),s(t)}(t)};function s(t){return t===i?(a++,e.consume(t),s):a<3?n(t):(e.exit("codeFencedFenceSequence"),Dt(t)?Rt(e,u,"whitespace")(t):u(t))}function u(n){return null===n||Ot(n)?(e.exit("codeFencedFence"),r.interrupt?t(n):e.check(Jt,h,x)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),c(n))}function c(t){return null===t||Ot(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(t)):Dt(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),Rt(e,f,"whitespace")(t)):96===t&&t===i?n(t):(e.consume(t),c)}function f(t){return null===t||Ot(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),p(t))}function p(t){return null===t||Ot(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(t)):96===t&&t===i?n(t):(e.consume(t),p)}function h(t){return e.attempt(o,x,d)(t)}function d(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),m}function m(t){return l>0&&Dt(t)?Rt(e,g,"linePrefix",l+1)(t):g(t)}function g(t){return null===t||Ot(t)?e.check(Jt,h,x)(t):(e.enter("codeFlowValue"),y(t))}function y(t){return null===t||Ot(t)?(e.exit("codeFlowValue"),g(t)):(e.consume(t),y)}function x(n){return e.exit("codeFenced"),t(n)}}},Zt={name:"codeIndented",tokenize:function(e,t,n){const r=this;return function(t){return e.enter("codeIndented"),Rt(e,o,"linePrefix",5)(t)};function o(e){const t=r.events[r.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?i(e):n(e)}function i(t){return null===t?a(t):Ot(t)?e.attempt(en,i,a)(t):(e.enter("codeFlowValue"),l(t))}function l(t){return null===t||Ot(t)?(e.exit("codeFlowValue"),i(t)):(e.consume(t),l)}function a(n){return e.exit("codeIndented"),t(n)}}},en={partial:!0,tokenize:function(e,t,n){const r=this;return o;function o(t){return r.parser.lazy[r.now().line]?n(t):Ot(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o):Rt(e,i,"linePrefix",5)(t)}function i(e){const i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):Ot(e)?o(e):n(e)}}},tn={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,o=3;if(!("lineEnding"!==e[o][1].type&&"space"!==e[o][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(t=o;++t<r;)if("codeTextData"===e[t][1].type){e[o][1].type="codeTextPadding",e[r][1].type="codeTextPadding",o+=2,r-=2;break}for(t=o-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):t!==r&&"lineEnding"!==e[t][1].type||(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,o,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),l(t)};function l(t){return 96===t?(e.consume(t),i++,l):(e.exit("codeTextSequence"),a(t))}function a(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),a):96===t?(o=e.enter("codeTextSequence"),r=0,u(t)):Ot(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a):(e.enter("codeTextData"),s(t))}function s(t){return null===t||32===t||96===t||Ot(t)?(e.exit("codeTextData"),a(t)):(e.consume(t),s)}function u(n){return 96===n?(e.consume(n),r++,u):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(o.type="codeTextData",s(n))}}};class nn{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){const n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){const r=t||0;this.setCursor(Math.trunc(e));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&rn(this.left,n),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),rn(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),rn(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const t=this.left.splice(e,Number.POSITIVE_INFINITY);rn(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);rn(this.left,t.reverse())}}}function rn(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function on(e){const t={};let n,r,o,i,l,a,s,u=-1;const c=new nn(e);for(;++u<c.length;){for(;u in t;)u=t[u];if(n=c.get(u),u&&"chunkFlow"===n[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&(a=n[1]._tokenizer.events,o=0,o<a.length&&"lineEndingBlank"===a[o][1].type&&(o+=2),o<a.length&&"content"===a[o][1].type))for(;++o<a.length&&"content"!==a[o][1].type;)"chunkText"===a[o][1].type&&(a[o][1]._isInFirstContentOfListItem=!0,o++);if("enter"===n[0])n[1].contentType&&(Object.assign(t,ln(c,u)),u=t[u],s=!0);else if(n[1]._container){for(o=u,r=void 0;o--&&(i=c.get(o),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type);)"enter"===i[0]&&(r&&(c.get(r)[1].type="lineEndingBlank"),i[1].type="lineEnding",r=o);r&&(n[1].end={...c.get(r)[1].start},l=c.slice(r,u),l.unshift(n),c.splice(r,u-r+1,l))}}return gt(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!s}function ln(e,t){const n=e.get(t)[1],r=e.get(t)[2];let o=t-1;const i=[],l=n._tokenizer||r.parser[n.contentType](n.start),a=l.events,s=[],u={};let c,f,p=-1,h=n,d=0,m=0;const g=[m];for(;h;){for(;e.get(++o)[1]!==h;);i.push(o),h._tokenizer||(c=r.sliceStream(h),h.next||c.push(null),f&&l.defineSkip(h.start),h._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(c),h._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),f=h,h=h.next}for(h=n;++p<a.length;)"exit"===a[p][0]&&"enter"===a[p-1][0]&&a[p][1].type===a[p-1][1].type&&a[p][1].start.line!==a[p][1].end.line&&(m=p+1,g.push(m),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(l.events=[],h?(h._tokenizer=void 0,h.previous=void 0):g.pop(),p=g.length;p--;){const t=a.slice(g[p],g[p+1]),n=i.pop();s.push([n,n+t.length-1]),e.splice(n,2,t)}for(s.reverse(),p=-1;++p<s.length;)u[d+s[p][0]]=d+s[p][1],d+=s[p][1]-s[p][0]-1;return u}const an={resolve:function(e){return on(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?o(t):Ot(t)?e.check(sn,i,o)(t):(e.consume(t),r)}function o(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},sn={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),Rt(e,o,"linePrefix")};function o(o){if(null===o||Ot(o))return n(o);const i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}};function un(e,t,n,r,o,i,l,a,s){const u=s||Number.POSITIVE_INFINITY;let c=0;return function(t){return 60===t?(e.enter(r),e.enter(o),e.enter(i),e.consume(t),e.exit(i),f):null===t||32===t||41===t||Pt(t)?n(t):(e.enter(r),e.enter(l),e.enter(a),e.enter("chunkString",{contentType:"string"}),d(t))};function f(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(o),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(a),f(t)):null===t||60===t||Ot(t)?n(t):(e.consume(t),92===t?h:p)}function h(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function d(o){return c||null!==o&&41!==o&&!Nt(o)?c<u&&40===o?(e.consume(o),c++,d):41===o?(e.consume(o),c--,d):null===o||32===o||40===o||Pt(o)?n(o):(e.consume(o),92===o?m:d):(e.exit("chunkString"),e.exit(a),e.exit(l),e.exit(r),t(o))}function m(t){return 40===t||41===t||92===t?(e.consume(t),d):d(t)}}function cn(e,t,n,r,o,i){const l=this;let a,s=0;return function(t){return e.enter(r),e.enter(o),e.consume(t),e.exit(o),e.enter(i),u};function u(f){return s>999||null===f||91===f||93===f&&!a||94===f&&!s&&"_hiddenFootnoteSupport"in l.parser.constructs?n(f):93===f?(e.exit(i),e.enter(o),e.consume(f),e.exit(o),e.exit(r),t):Ot(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||Ot(t)||s++>999?(e.exit("chunkString"),u(t)):(e.consume(t),a||(a=!Dt(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function fn(e,t,n,r,o,i){let l;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(o),e.consume(t),e.exit(o),l=40===t?41:t,a):n(t)};function a(n){return n===l?(e.enter(o),e.consume(n),e.exit(o),e.exit(r),t):(e.enter(i),s(n))}function s(t){return t===l?(e.exit(i),a(l)):null===t?n(t):Ot(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),Rt(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===l||null===t||Ot(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return t===l||92===t?(e.consume(t),u):u(t)}}function pn(e,t){let n;return function r(o){return Ot(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n=!0,r):Dt(o)?Rt(e,r,n?"linePrefix":"lineSuffix")(o):t(o)}}const hn={name:"definition",tokenize:function(e,t,n){const r=this;let o;return function(t){return e.enter("definition"),function(t){return cn.call(r,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}(t)};function i(t){return o=St(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return Nt(t)?pn(e,a)(t):a(t)}function a(t){return un(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(dn,u,u)(t)}function u(t){return Dt(t)?Rt(e,c,"whitespace")(t):c(t)}function c(i){return null===i||Ot(i)?(e.exit("definition"),r.parser.defined.push(o),t(i)):n(i)}}},dn={partial:!0,tokenize:function(e,t,n){return function(t){return Nt(t)?pn(e,r)(t):n(t)};function r(t){return fn(e,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function o(t){return Dt(t)?Rt(e,i,"whitespace")(t):i(t)}function i(e){return null===e||Ot(e)?t(e):n(e)}}},mn={name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return Ot(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},gn={name:"headingAtx",resolve:function(e,t){let n,r,o=e.length-2,i=3;return"whitespace"===e[i][1].type&&(i+=2),o-2>i&&"whitespace"===e[o][1].type&&(o-=2),"atxHeadingSequence"===e[o][1].type&&(i===o-1||o-4>i&&"whitespace"===e[o-2][1].type)&&(o-=i+1===o?2:4),o>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[o][1].end},r={type:"chunkText",start:e[i][1].start,end:e[o][1].end,contentType:"text"},gt(e,i,o-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(t){return e.enter("atxHeading"),function(t){return e.enter("atxHeadingSequence"),o(t)}(t)};function o(t){return 35===t&&r++<6?(e.consume(t),o):null===t||Nt(t)?(e.exit("atxHeadingSequence"),i(t)):n(t)}function i(n){return 35===n?(e.enter("atxHeadingSequence"),l(n)):null===n||Ot(n)?(e.exit("atxHeading"),t(n)):Dt(n)?Rt(e,i,"whitespace")(n):(e.enter("atxHeadingText"),a(n))}function l(t){return 35===t?(e.consume(t),l):(e.exit("atxHeadingSequence"),i(t))}function a(t){return null===t||35===t||Nt(t)?(e.exit("atxHeadingText"),i(t)):(e.consume(t),a)}}},yn=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],xn=["pre","script","style","textarea"],vn={concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){const r=this;let o,i,l,a,s;return function(t){return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),u}(t)};function u(a){return 33===a?(e.consume(a),c):47===a?(e.consume(a),i=!0,h):63===a?(e.consume(a),o=3,r.interrupt?t:M):Ct(a)?(e.consume(a),l=String.fromCharCode(a),d):n(a)}function c(i){return 45===i?(e.consume(i),o=2,f):91===i?(e.consume(i),o=5,a=0,p):Ct(i)?(e.consume(i),o=4,r.interrupt?t:M):n(i)}function f(o){return 45===o?(e.consume(o),r.interrupt?t:M):n(o)}function p(o){return o==="CDATA[".charCodeAt(a++)?(e.consume(o),6===a?r.interrupt?t:E:p):n(o)}function h(t){return Ct(t)?(e.consume(t),l=String.fromCharCode(t),d):n(t)}function d(a){if(null===a||47===a||62===a||Nt(a)){const s=47===a,u=l.toLowerCase();return s||i||!xn.includes(u)?yn.includes(l.toLowerCase())?(o=6,s?(e.consume(a),m):r.interrupt?t(a):E(a)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(a):i?g(a):y(a)):(o=1,r.interrupt?t(a):E(a))}return 45===a||It(a)?(e.consume(a),l+=String.fromCharCode(a),d):n(a)}function m(o){return 62===o?(e.consume(o),r.interrupt?t:E):n(o)}function g(t){return Dt(t)?(e.consume(t),g):C(t)}function y(t){return 47===t?(e.consume(t),C):58===t||95===t||Ct(t)?(e.consume(t),x):Dt(t)?(e.consume(t),y):C(t)}function x(t){return 45===t||46===t||58===t||95===t||It(t)?(e.consume(t),x):v(t)}function v(t){return 61===t?(e.consume(t),k):Dt(t)?(e.consume(t),v):y(t)}function k(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),s=t,b):Dt(t)?(e.consume(t),k):w(t)}function b(t){return t===s?(e.consume(t),s=null,S):null===t||Ot(t)?n(t):(e.consume(t),b)}function w(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||Nt(t)?v(t):(e.consume(t),w)}function S(e){return 47===e||62===e||Dt(e)?y(e):n(e)}function C(t){return 62===t?(e.consume(t),I):n(t)}function I(t){return null===t||Ot(t)?E(t):Dt(t)?(e.consume(t),I):n(t)}function E(t){return 45===t&&2===o?(e.consume(t),L):60===t&&1===o?(e.consume(t),O):62===t&&4===o?(e.consume(t),z):63===t&&3===o?(e.consume(t),M):93===t&&5===o?(e.consume(t),D):!Ot(t)||6!==o&&7!==o?null===t||Ot(t)?(e.exit("htmlFlowData"),P(t)):(e.consume(t),E):(e.exit("htmlFlowData"),e.check(kn,F,P)(t))}function P(t){return e.check(bn,T,F)(t)}function T(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),A}function A(t){return null===t||Ot(t)?P(t):(e.enter("htmlFlowData"),E(t))}function L(t){return 45===t?(e.consume(t),M):E(t)}function O(t){return 47===t?(e.consume(t),l="",N):E(t)}function N(t){if(62===t){const n=l.toLowerCase();return xn.includes(n)?(e.consume(t),z):E(t)}return Ct(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),N):E(t)}function D(t){return 93===t?(e.consume(t),M):E(t)}function M(t){return 62===t?(e.consume(t),z):45===t&&2===o?(e.consume(t),M):E(t)}function z(t){return null===t||Ot(t)?(e.exit("htmlFlowData"),F(t)):(e.consume(t),z)}function F(n){return e.exit("htmlFlow"),t(n)}}},kn={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(Kt,t,n)}}},bn={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return Ot(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o):n(t)};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},wn={name:"htmlText",tokenize:function(e,t,n){const r=this;let o,i,l;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),k):63===t?(e.consume(t),x):Ct(t)?(e.consume(t),S):n(t)}function s(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,h):Ct(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),p):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):Ot(t)?(l=c,N(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),p):c(t)}function p(e){return 62===e?O(e):45===e?f(e):c(e)}function h(t){return t==="CDATA[".charCodeAt(i++)?(e.consume(t),6===i?d:h):n(t)}function d(t){return null===t?n(t):93===t?(e.consume(t),m):Ot(t)?(l=d,N(t)):(e.consume(t),d)}function m(t){return 93===t?(e.consume(t),g):d(t)}function g(t){return 62===t?O(t):93===t?(e.consume(t),g):d(t)}function y(t){return null===t||62===t?O(t):Ot(t)?(l=y,N(t)):(e.consume(t),y)}function x(t){return null===t?n(t):63===t?(e.consume(t),v):Ot(t)?(l=x,N(t)):(e.consume(t),x)}function v(e){return 62===e?O(e):x(e)}function k(t){return Ct(t)?(e.consume(t),b):n(t)}function b(t){return 45===t||It(t)?(e.consume(t),b):w(t)}function w(t){return Ot(t)?(l=w,N(t)):Dt(t)?(e.consume(t),w):O(t)}function S(t){return 45===t||It(t)?(e.consume(t),S):47===t||62===t||Nt(t)?C(t):n(t)}function C(t){return 47===t?(e.consume(t),O):58===t||95===t||Ct(t)?(e.consume(t),I):Ot(t)?(l=C,N(t)):Dt(t)?(e.consume(t),C):O(t)}function I(t){return 45===t||46===t||58===t||95===t||It(t)?(e.consume(t),I):E(t)}function E(t){return 61===t?(e.consume(t),P):Ot(t)?(l=E,N(t)):Dt(t)?(e.consume(t),E):C(t)}function P(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,T):Ot(t)?(l=P,N(t)):Dt(t)?(e.consume(t),P):(e.consume(t),A)}function T(t){return t===o?(e.consume(t),o=void 0,L):null===t?n(t):Ot(t)?(l=T,N(t)):(e.consume(t),T)}function A(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||Nt(t)?C(t):(e.consume(t),A)}function L(e){return 47===e||62===e||Nt(e)?C(e):n(e)}function O(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function N(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),D}function D(t){return Dt(t)?Rt(e,M,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):M(t)}function M(t){return e.enter("htmlTextData"),l(t)}}},Sn={name:"labelEnd",resolveAll:function(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){const e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&gt(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,o,i,l=e.length,a=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(o){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(o=l);const s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[o][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[o-2][1].start}};return i=[["enter",s,t],["enter",u,t]],i=yt(i,e.slice(r+1,r+a+3)),i=yt(i,[["enter",c,t]]),i=yt(i,Vt(t.parser.constructs.insideSpan.null,e.slice(r+a+4,o-3),t)),i=yt(i,[["exit",c,t],e[o-2],e[o-1],["exit",u,t]]),i=yt(i,e.slice(o+1)),i=yt(i,[["exit",s,t]]),gt(e,r,e.length,i),e},tokenize:function(e,t,n){const r=this;let o,i,l=r.events.length;for(;l--;)if(("labelImage"===r.events[l][1].type||"labelLink"===r.events[l][1].type)&&!r.events[l][1]._balanced){o=r.events[l][1];break}return function(t){return o?o._inactive?c(t):(i=r.parser.defined.includes(St(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(Cn,u,i?u:c)(t):91===t?e.attempt(In,u,i?s:c)(t):i?u(t):c(t)}function s(t){return e.attempt(En,u,c)(t)}function u(e){return t(e)}function c(e){return o._balanced=!0,n(e)}}},Cn={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return Nt(t)?pn(e,o)(t):o(t)}function o(t){return 41===t?u(t):un(e,i,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return Nt(t)?pn(e,a)(t):u(t)}function l(e){return n(e)}function a(t){return 34===t||39===t||40===t?fn(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function s(t){return Nt(t)?pn(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},In={tokenize:function(e,t,n){const r=this;return function(t){return cn.call(r,e,o,i,"reference","referenceMarker","referenceString")(t)};function o(e){return r.parser.defined.includes(St(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},En={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},Pn={name:"labelStartImage",resolveAll:Sn.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),o};function o(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},Tn={name:"labelStartLink",resolveAll:Sn.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),o};function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},An={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),Rt(e,t,"linePrefix")}}},Ln={name:"thematicBreak",tokenize:function(e,t,n){let r,o=0;return function(t){return e.enter("thematicBreak"),function(e){return r=e,i(e)}(t)};function i(i){return i===r?(e.enter("thematicBreakSequence"),l(i)):o>=3&&(null===i||Ot(i))?(e.exit("thematicBreak"),t(i)):n(i)}function l(t){return t===r?(e.consume(t),o++,l):(e.exit("thematicBreakSequence"),Dt(t)?Rt(e,i,"whitespace")(t):i(t))}}},On={continuation:{tokenize:function(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(Kt,(function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,Rt(e,t,"listItemIndent",r.containerState.size+1)(n)}),(function(n){return r.containerState.furtherBlankLines||!Dt(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(Dn,t,o)(n))}));function o(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,Rt(e,e.attempt(On,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){const r=this,o=r.events[r.events.length-1];let i=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,l=0;return function(t){const o=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===o?!r.containerState.marker||t===r.containerState.marker:Tt(t)){if(r.containerState.type||(r.containerState.type=o,e.enter(o,{_container:!0})),"listUnordered"===o)return e.enter("listItemPrefix"),42===t||45===t?e.check(Ln,n,s)(t):s(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),a(t)}return n(t)};function a(t){return Tt(t)&&++l<10?(e.consume(t),a):(!r.interrupt||l<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),s(t)):n(t)}function s(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(Kt,r.interrupt?n:u,e.attempt(Nn,f,c))}function u(e){return r.containerState.initialBlankLine=!0,i++,f(e)}function c(t){return Dt(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),f):n(t)}function f(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},Nn={partial:!0,tokenize:function(e,t,n){const r=this;return Rt(e,(function(e){const o=r.events[r.events.length-1];return!Dt(e)&&o&&"listItemPrefixWhitespace"===o[1].type?t(e):n(e)}),"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},Dn={partial:!0,tokenize:function(e,t,n){const r=this;return Rt(e,(function(e){const o=r.events[r.events.length-1];return o&&"listItemIndent"===o[1].type&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(e):n(e)}),"listItemIndent",r.containerState.size+1)}},Mn={name:"setextUnderline",resolveTo:function(e,t){let n,r,o,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),o||"definition"!==e[i][1].type||(o=i);const l={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",o?(e.splice(r,0,["enter",l,t]),e.splice(o+1,0,["exit",e[n][1],t]),e[n][1].end={...e[o][1].end}):e[n][1]=l,e.push(["exit",l,t]),e},tokenize:function(e,t,n){const r=this;let o;return function(t){let l,a=r.events.length;for(;a--;)if("lineEnding"!==r.events[a][1].type&&"linePrefix"!==r.events[a][1].type&&"content"!==r.events[a][1].type){l="paragraph"===r.events[a][1].type;break}return r.parser.lazy[r.now().line]||!r.interrupt&&!l?n(t):(e.enter("setextHeadingLine"),o=t,function(t){return e.enter("setextHeadingLineSequence"),i(t)}(t))};function i(t){return t===o?(e.consume(t),i):(e.exit("setextHeadingLineSequence"),Dt(t)?Rt(e,l,"lineSuffix")(t):l(t))}function l(r){return null===r||Ot(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},zn={tokenize:function(e){const t=this,n=e.attempt(Kt,(function(r){if(null!==r)return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n;e.consume(r)}),e.attempt(this.parser.constructs.flowInitial,r,Rt(e,e.attempt(this.parser.constructs.flow,r,e.attempt(an,r)),"linePrefix")));return n;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n;e.consume(r)}}},Fn={resolveAll:Bn()},_n=jn("string"),Rn=jn("text");function jn(e){return{resolveAll:Bn("text"===e?Hn:void 0),tokenize:function(t){const n=this,r=this.parser.constructs[e],o=t.attempt(r,i,l);return i;function i(e){return s(e)?o(e):l(e)}function l(e){if(null!==e)return t.enter("data"),t.consume(e),a;t.consume(e)}function a(e){return s(e)?(t.exit("data"),o(e)):(t.consume(e),a)}function s(e){if(null===e)return!0;const t=r[e];let o=-1;if(t)for(;++o<t.length;){const e=t[o];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function Bn(e){return function(t,n){let r,o=-1;for(;++o<=t.length;)void 0===r?t[o]&&"data"===t[o][1].type&&(r=o,o++):t[o]&&"data"===t[o][1].type||(o!==r+2&&(t[r][1].end=t[o-1][1].end,t.splice(r+2,o-r-2),o=r+2),r=void 0);return e?e(t,n):t}}function Hn(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){const r=e[n-1][1],o=t.sliceStream(r);let i,l=o.length,a=-1,s=0;for(;l--;){const e=o[l];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)s++,a--;if(a)break;a=-1}else if(-2===e)i=!0,s++;else if(-1!==e){l++;break}}if(s){const o={type:n===e.length||i||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?a:r.start._bufferIndex+a,_index:r.start._index+l,line:r.end.line,column:r.end.column-s,offset:r.end.offset-s},end:{...r.end}};r.end={...o.start},r.start.offset===r.end.offset?Object.assign(r,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}const Un={42:On,43:On,45:On,48:On,49:On,50:On,51:On,52:On,53:On,54:On,55:On,56:On,57:On,62:Qt},Vn={91:hn},qn={[-2]:Zt,[-1]:Zt,32:Zt},Wn={35:gn,42:Ln,45:[Mn,Ln],60:vn,61:Mn,95:Ln,96:Gt,126:Gt},Yn={38:$t,92:Xt},Kn={[-5]:An,[-4]:An,[-3]:An,33:Pn,38:$t,42:qt,60:[Yt,wn],91:Tn,92:[mn,Xt],93:Sn,95:qt,96:tn},Qn={null:[qt,Fn]},Xn=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:{null:[42,95]},contentInitial:Vn,disable:{null:[]},document:Un,flow:Wn,flowInitial:qn,insideSpan:Qn,string:Yn,text:Kn},Symbol.toStringTag,{value:"Module"}));function $n(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const o={},i=[];let l=[],a=[];const s={attempt:m((function(e,t){g(e,t.from)})),check:m(d),consume:function(e){Ot(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,y()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){const n=t||{};return n.type=e,n.start=p(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){const t=a.pop();return t.end=p(),u.events.push(["exit",t,u]),t},interrupt:m(d,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){o[e.line]=e.column,y()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n=-1;const r=[];let o;for(;++n<e.length;){const i=e[n];let l;if("string"==typeof i)l=i;else switch(i){case-5:l="\r";break;case-4:l="\n";break;case-3:l="\r\n";break;case-2:l=t?" ":"\t";break;case-1:if(!t&&o)continue;l=" ";break;default:l=String.fromCharCode(i)}o=-2===i,r.push(l)}return r.join("")}(f(e),t)},sliceStream:f,write:function(e){return l=yt(l,e),function(){let e;for(;r._index<l.length;){const t=l[r._index];if("string"==typeof t)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<t.length;)h(t.charCodeAt(r._bufferIndex));else h(t)}}(),null!==l[l.length-1]?[]:(g(t,0),u.events=Vt(i,u.events,u),u.events)}};let c=t.tokenize.call(u,s);return t.resolveAll&&i.push(t),u;function f(e){return function(e,t){const n=t.start._index,r=t.start._bufferIndex,o=t.end._index,i=t.end._bufferIndex;let l;if(n===o)l=[e[n].slice(r,i)];else{if(l=e.slice(n,o),r>-1){const e=l[0];"string"==typeof e?l[0]=e.slice(r):l.shift()}i>0&&l.push(e[o].slice(0,i))}return l}(l,e)}function p(){const{_bufferIndex:e,_index:t,line:n,column:o,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:o,offset:i}}function h(e){c=c(e)}function d(e,t){t.restore()}function m(e,t){return function(n,o,i){let l,c,f,h;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(d=n,function(e){const t=null!==e&&d[e],n=null!==e&&d.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});var d;function m(e){return l=e,c=0,0===e.length?i:g(e[c])}function g(e){return function(n){return h=function(){const e=p(),t=u.previous,n=u.currentConstruct,o=u.events.length,i=Array.from(a);return{from:o,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=o,a=i,y()}}}(),f=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name)?v():e.tokenize.call(t?Object.assign(Object.create(u),t):u,s,x,v)(n)}}function x(t){return e(f,h),o}function v(e){return h.restore(),++c<l.length?g(l[c]):i}}}function g(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&gt(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function y(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}const Jn=/[\0\t\n\r]/g,Gn=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Zn(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){const e=n.charCodeAt(1),t=120===e||88===e;return wt(n.slice(t?2:1),t?16:10)}return G(n)||e}const er={}.hasOwnProperty;function tr(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),function(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:o(x),autolinkProtocol:c,autolinkEmail:c,atxHeading:o(m),blockQuote:o((function(){return{type:"blockquote",children:[]}})),characterEscape:c,characterReference:c,codeFenced:o(d),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:o(d,i),codeText:o((function(){return{type:"inlineCode",value:""}}),i),codeTextData:c,data:c,codeFlowValue:c,definition:o((function(){return{type:"definition",identifier:"",label:null,title:null,url:""}})),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:o((function(){return{type:"emphasis",children:[]}})),hardBreakEscape:o(g),hardBreakTrailing:o(g),htmlFlow:o(y,i),htmlFlowData:c,htmlText:o(y,i),htmlTextData:c,image:o((function(){return{type:"image",title:null,url:"",alt:null}})),label:i,link:o(x),listItem:o((function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}})),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:o(v,(function(){this.data.expectingFirstListItemValue=!0})),listUnordered:o(v),paragraph:o((function(){return{type:"paragraph",children:[]}})),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:o(m),strong:o((function(){return{type:"strong",children:[]}})),thematicBreak:o((function(){return{type:"thematicBreak"}}))},exit:{atxHeading:a(),atxHeadingSequence:function(e){const t=this.stack[this.stack.length-1];if(!t.depth){const n=this.sliceSerialize(e).length;t.depth=n}},autolink:a(),autolinkEmail:function(e){f.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){f.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:a(),characterEscapeValue:f,characterReferenceMarkerHexadecimal:h,characterReferenceMarkerNumeric:h,characterReferenceValue:function(e){const t=this.sliceSerialize(e),n=this.data.characterReferenceType;let r;n?(r=wt(t,"characterReferenceMarkerNumeric"===n?10:16),this.data.characterReferenceType=void 0):r=G(t);this.stack[this.stack.length-1].value+=r},characterReference:function(e){this.stack.pop().position.end=nr(e.end)},codeFenced:a((function(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0})),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){const e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){const e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:f,codeIndented:a((function(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")})),codeText:a((function(){const e=this.resume();this.stack[this.stack.length-1].value=e})),codeTextData:f,data:f,definition:a(),definitionDestinationString:function(){const e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=St(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){const e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:a(),hardBreakEscape:a(p),hardBreakTrailing:a(p),htmlFlow:a((function(){const e=this.resume();this.stack[this.stack.length-1].value=e})),htmlFlowData:f,htmlText:a((function(){const e=this.resume();this.stack[this.stack.length-1].value=e})),htmlTextData:f,image:a((function(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0})),label:function(){const e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){const t=e.children;n.children=t}else n.alt=t},labelText:function(e){const t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=function(e){return e.replace(Gn,Zn)}(t),n.identifier=St(t).toLowerCase()},lineEnding:function(e){const n=this.stack[this.stack.length-1];if(this.data.atHardBreak)return n.children[n.children.length-1].position.end=nr(e.end),void(this.data.atHardBreak=void 0);!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),f.call(this,e))},link:a((function(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0})),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:function(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=St(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){const e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){const e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:a((function(){this.data.setextHeadingSlurpLineEnding=void 0})),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:a(),thematicBreak:a()}};rr(t,(e||{}).mdastExtensions||[]);const n={};return function(e){let o={type:"root",children:[]};const a={stack:[o],tokenStack:[],config:t,enter:l,exit:s,buffer:i,resume:u,data:n},c=[];let f=-1;for(;++f<e.length;)"listOrdered"!==e[f][1].type&&"listUnordered"!==e[f][1].type||("enter"===e[f][0]?c.push(f):f=r(e,c.pop(),f));for(f=-1;++f<e.length;){const n=t[e[f][0]];er.call(n,e[f][1].type)&&n[e[f][1].type].call(Object.assign({sliceSerialize:e[f][2].sliceSerialize},a),e[f][1])}if(a.tokenStack.length>0){const e=a.tokenStack[a.tokenStack.length-1];(e[1]||ir).call(a,void 0,e[0])}for(o.position={start:nr(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:nr(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},f=-1;++f<t.transforms.length;)o=t.transforms[f](o)||o;return o};function r(e,t,n){let r,o,i,l,a=t-1,s=-1,u=!1;for(;++a<=n;){const t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||l||s||i||(i=a),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=a;for(o=void 0;l--;){const t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;o&&(e[o][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",o=l}else if("linePrefix"!==t[1].type&&"blockQuotePrefix"!==t[1].type&&"blockQuotePrefixWhitespace"!==t[1].type&&"blockQuoteMarker"!==t[1].type&&"listItemIndent"!==t[1].type)break}i&&(!o||i<o)&&(r._spread=!0),r.end=Object.assign({},o?e[o][1].start:t[1].end),e.splice(o||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){const o={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=o,e.splice(a,0,["enter",o,t[2]]),a++,n++,i=void 0,l=!0}}}return e[t][1]._spread=u,n}function o(e,t){return function(n){l.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function l(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:nr(t.start),end:void 0}}function a(e){return function(t){e&&e.call(this,t),s.call(this,t)}}function s(e,t){const n=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+Ve({start:e.start,end:e.end})+"): it’s not open");r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||ir).call(this,e,r[0])),n.position.end=nr(e.end)}function u(){return ht(this.stack.pop())}function c(e){const t=this.stack[this.stack.length-1].children;let n=t[t.length-1];n&&"text"===n.type||(n={type:"text",value:""},n.position={start:nr(e.start),end:void 0},t.push(n)),this.stack.push(n)}function f(e){const t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=nr(e.end)}function p(){this.data.atHardBreak=!0}function h(e){this.data.characterReferenceType=e.type}function d(){return{type:"code",lang:null,meta:null,value:""}}function m(){return{type:"heading",depth:0,children:[]}}function g(){return{type:"break"}}function y(){return{type:"html",value:""}}function x(){return{type:"link",title:null,url:"",children:[]}}function v(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}}(n)(function(e){for(;!on(e););return e}(function(e){const t={constructs:vt([Xn,...(e||{}).extensions||[]]),content:n(jt),defined:[],document:n(Bt),flow:n(zn),lazy:{},string:n(_n),text:n(Rn)};return t;function n(e){return function(n){return $n(t,e,n)}}}(n).document().write(function(){let e,t=1,n="",r=!0;return function(o,i,l){const a=[];let s,u,c,f,p;for(o=n+("string"==typeof o?o.toString():new TextDecoder(i||void 0).decode(o)),c=0,n="",r&&(65279===o.charCodeAt(0)&&c++,r=void 0);c<o.length;){if(Jn.lastIndex=c,s=Jn.exec(o),f=s&&void 0!==s.index?s.index:o.length,p=o.charCodeAt(f),!s){n=o.slice(c);break}if(10===p&&c===f&&e)a.push(-3),e=void 0;else switch(e&&(a.push(-5),e=void 0),c<f&&(a.push(o.slice(c,f)),t+=f-c),p){case 0:a.push(65533),t++;break;case 9:for(u=4*Math.ceil(t/4),a.push(-2);t++<u;)a.push(-1);break;case 10:a.push(-4),t=1;break;default:e=!0,t=1}c=f+1}return l&&(e&&a.push(-5),n&&a.push(n),a.push(null)),a}}()(e,t,!0))))}function nr(e){return{line:e.line,column:e.column,offset:e.offset}}function rr(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?rr(e,r):or(e,r)}}function or(e,t){let n;for(n in t)if(er.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function ir(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Ve({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Ve({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Ve({start:t.start,end:t.end})+") is still open")}function lr(e){const t=this;t.parser=function(n){return tr(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function ar(e,t){const n=t.referenceType;let r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&"text"===i.type?i.value="["+i.value:o.unshift({type:"text",value:"["});const l=o[o.length-1];return l&&"text"===l.type?l.value+=r:o.push({type:"text",value:r}),o}function sr(e){const t=e.spread;return null==t?e.children.length>1:t}function ur(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push(cr(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push(cr(t.slice(o),o>0,!1)),i.join("")}function cr(e,t,n){let r=0,o=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(o-1);for(;9===t||32===t;)o--,t=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}const fr={blockquote:function(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){const n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o},delete:function(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=_t(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let l,a=e.footnoteCounts.get(r);void 0===a?(a=0,e.footnoteOrder.push(r),l=e.footnoteOrder.length):l=i+1,a+=1,e.footnoteCounts.set(r,a);const s={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(t,s);const u={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return ar(e,t);const o={src:_t(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){const n={src:_t(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return ar(e,t);const o={href:_t(r.url||"")};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){const n={href:_t(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){const r=e.all(t),o=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=sr(n[r])}return t}(n):sr(t),i={},l=[];if("boolean"==typeof t.checked){const e=r[0];let n;e&&"element"===e.type&&"p"===e.tagName?n=e:(n={type:"element",tagName:"p",properties:{},children:[]},r.unshift(n)),n.children.length>0&&n.children.unshift({type:"text",value:" "}),n.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let a=-1;for(;++a<r.length;){const e=r[a];(o||0!==a||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||o?l.push(e):l.push(...e.children)}const s=r[r.length-1];s&&(o||"element"!==s.type||"p"!==s.tagName)&&l.push({type:"text",value:"\n"});const u={type:"element",tagName:"li",properties:i,children:l};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){const n={},r=e.all(t);let o=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++o<r.length;){const e=r[o];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),o.push(n)}if(n.length>0){const r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=we(t.children[1]),l=be(t.children[t.children.length-1]);i&&l&&(r.position={start:i,end:l}),o.push(r)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){const r=n?n.children:void 0,o=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,l=i?i.length:t.children.length;let a=-1;const s=[];for(;++a<l;){const n=t.children[a],r={},l=i?i[a]:void 0;l&&(r.align=l);let u={type:"element",tagName:o,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),s.push(u)}const u={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){const n={type:"text",value:ur(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:pr,yaml:pr,definition:pr,footnoteDefinition:pr};function pr(){}function hr(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function dr(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}const mr={}.hasOwnProperty,gr={};function yr(e,t){e.position&&(t.position=function(e){const t=we(e),n=be(e);if(t&&n)return{start:t,end:n}}(e))}function xr(e,t){let n=t;if(e&&e.data){const t=e.data.hName,r=e.data.hChildren,o=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&o&&Object.assign(n.properties,fe(o)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function vr(e,t){const n=t.data||{},r=!("value"in t)||mr.call(n,"hProperties")||mr.call(n,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(t)}:{type:"text",value:t.value};return e.patch(t,r),e.applyData(t,r)}function kr(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function br(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function wr(e,t){const n=function(e,t){const n=t||gr,r=new Map,o=new Map,i=new Map,l={...fr,...n.handlers},a={all:function(e){const t=[];if("children"in e){const n=e.children;let r=-1;for(;++r<n.length;){const o=a.one(n[r],e);if(o){if(r&&"break"===n[r-1].type&&(Array.isArray(o)||"text"!==o.type||(o.value=br(o.value)),!Array.isArray(o)&&"element"===o.type)){const e=o.children[0];e&&"text"===e.type&&(e.value=br(e.value))}Array.isArray(o)?t.push(...o):t.push(o)}}}return t},applyData:xr,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:l,one:function(e,t){const n=e.type,r=a.handlers[n];if(mr.call(a.handlers,n)&&r)return r(a,e,t);if(a.options.passThrough&&a.options.passThrough.includes(n)){if("children"in e){const{children:t,...n}=e,r=fe(n);return r.children=a.all(e),r}return fe(e)}return(a.options.unknownHandler||vr)(a,e,t)},options:n,patch:yr,wrap:kr};return c(e,(function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const t="definition"===e.type?r:o,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}})),a}(e,t),r=n.one(e,void 0),o=function(e){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||hr,r=e.options.footnoteBackLabel||dr,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[];let s=-1;for(;++s<e.footnoteOrder.length;){const o=e.footnoteById.get(e.footnoteOrder[s]);if(!o)continue;const i=e.all(o),l=String(o.identifier).toUpperCase(),u=_t(l.toLowerCase());let c=0;const f=[],p=e.footnoteCounts.get(l);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const h=i[i.length-1];if(h&&"element"===h.type&&"p"===h.tagName){const e=h.children[h.children.length-1];e&&"text"===e.type?e.value+=" ":h.children.push({type:"text",value:" "}),h.children.push(...f)}else i.push(...f);const d={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(i,!0)};e.patch(o,d),a.push(d)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...fe(l),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:"\n"},o),i}function Sr(e,t){return e&&"run"in e?async function(n,r){const o=wr(n,{file:r,...t});await e.run(o,r)}:function(n,r){return wr(n,{file:r,...e||t})}}function Cr(e){if(e)throw e}var Ir=Object.prototype.hasOwnProperty,Er=Object.prototype.toString,Pr=Object.defineProperty,Tr=Object.getOwnPropertyDescriptor,Ar=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Er.call(e)},Lr=function(e){if(!e||"[object Object]"!==Er.call(e))return!1;var t,n=Ir.call(e,"constructor"),r=e.constructor&&e.constructor.prototype&&Ir.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!r)return!1;for(t in e);return void 0===t||Ir.call(e,t)},Or=function(e,t){Pr&&"__proto__"===t.name?Pr(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},Nr=function(e,t){if("__proto__"===t){if(!Ir.call(e,t))return;if(Tr)return Tr(e,t).value}return e[t]};const Dr=n((function e(){var t,n,r,o,i,l,a=arguments[0],s=1,u=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[1]||{},s=2),(null==a||"object"!=typeof a&&"function"!=typeof a)&&(a={});s<u;++s)if(null!=(t=arguments[s]))for(n in t)r=Nr(a,n),a!==(o=Nr(t,n))&&(c&&o&&(Lr(o)||(i=Ar(o)))?(i?(i=!1,l=r&&Ar(r)?r:[]):l=r&&Lr(r)?r:{},Or(a,{name:n,newValue:e(c,l,o)})):void 0!==o&&Or(a,{name:n,newValue:o}));return a}));function Mr(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)}const zr=function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');Br(e);let n,r=0,o=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else o<0&&(n=!0,o=i+1);return o<0?"":e.slice(r,o)}if(t===e)return"";let l=-1,a=t.length-1;for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else l<0&&(n=!0,l=i+1),a>-1&&(e.codePointAt(i)===t.codePointAt(a--)?a<0&&(o=i):(a=-1,o=l));return r===o?o=l:o<0&&(o=e.length),e.slice(r,o)},Fr=function(e){if(Br(e),0===e.length)return".";let t,n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},_r=function(e){Br(e);let t,n=e.length,r=-1,o=0,i=-1,l=0;for(;n--;){const a=e.codePointAt(n);if(47!==a)r<0&&(t=!0,r=n+1),46===a?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1);else if(t){o=n+1;break}}return i<0||r<0||0===l||1===l&&i===r-1&&i===o+1?"":e.slice(i,r)},Rr=function(...e){let t,n=-1;for(;++n<e.length;)Br(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){Br(e);const t=47===e.codePointAt(0);let n=function(e,t){let n,r,o="",i=0,l=-1,a=0,s=-1;for(;++s<=e.length;){if(s<e.length)n=e.codePointAt(s);else{if(47===n)break;n=47}if(47===n){if(l===s-1||1===a);else if(l!==s-1&&2===a){if(o.length<2||2!==i||46!==o.codePointAt(o.length-1)||46!==o.codePointAt(o.length-2))if(o.length>2){if(r=o.lastIndexOf("/"),r!==o.length-1){r<0?(o="",i=0):(o=o.slice(0,r),i=o.length-1-o.lastIndexOf("/")),l=s,a=0;continue}}else if(o.length>0){o="",i=0,l=s,a=0;continue}t&&(o=o.length>0?o+"/..":"..",i=2)}else o.length>0?o+="/"+e.slice(l+1,s):o=e.slice(l+1,s),i=s-l-1;l=s,a=0}else 46===n&&a>-1?a++:a=-1}return o}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},jr="/";function Br(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Hr=function(){return"/"};function Ur(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}const Vr=["history","path","basename","stem","extname","dirname"];class qr{constructor(e){let t;t=e?Ur(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":Hr(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n,r=-1;for(;++r<Vr.length;){const e=Vr[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)Vr.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?zr(this.path):void 0}set basename(e){Yr(e,"basename"),Wr(e,"basename"),this.path=Rr(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?Fr(this.path):void 0}set dirname(e){Kr(this.basename,"dirname"),this.path=Rr(e||"",this.basename)}get extname(){return"string"==typeof this.path?_r(this.path):void 0}set extname(e){if(Wr(e,"extname"),Kr(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Rr(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Ur(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!Ur(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const t=e.pathname;let n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){const e=t.codePointAt(n+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),Yr(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?zr(this.path,this.extname):void 0}set stem(e){Yr(e,"stem"),Wr(e,"stem"),this.path=Rr(this.dirname||"",e+(this.extname||""))}fail(e,t,n){const r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){const r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){const r=new Ke(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function Wr(e,t){if(e&&e.includes(jr))throw new Error("`"+t+"` cannot be a path: did not expect `"+jr+"`")}function Yr(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Kr(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}const Qr=function(e){const t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},Xr={}.hasOwnProperty;class $r extends Qr{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){const e=[],t={run:function(...t){let n=-1;const r=t.pop();if("function"!=typeof r)throw new TypeError("Expected function as last argument, not "+r);!function o(i,...l){const a=e[++n];let s=-1;if(i)r(i);else{for(;++s<t.length;)null!==l[s]&&void 0!==l[s]||(l[s]=t[s]);t=l,a?function(e,t){let n;return function(...t){const l=e.length>t.length;let a;l&&t.push(r);try{a=e.apply(this,t)}catch(i){if(l&&n)throw i;return r(i)}l||(a&&a.then&&"function"==typeof a.then?a.then(o,r):a instanceof Error?r(a):o(a))};function r(e,...r){n||(n=!0,t(e,...r))}function o(e){r(null,e)}}(a,o)(...l):r(null,...l)}}(null,...t)},use:function(n){if("function"!=typeof n)throw new TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){const e=new $r;let t=-1;for(;++t<this.attachers.length;){const n=this.attachers[t];e.use(...n)}return e.data(Dr(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2===arguments.length?(eo("data",this.frozen),this.namespace[e]=t,this):Xr.call(this.namespace,e)&&this.namespace[e]||void 0:e?(eo("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...n]=this.attachers[this.freezeIndex];if(!1===n[0])continue;!0===n[0]&&(n[0]=void 0);const r=t.call(e,...n);"function"==typeof r&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const t=ro(e),n=this.parser||this.Parser;return Gr("parse",n),n(String(t),t)}process(e,t){const n=this;return this.freeze(),Gr("process",this.parser||this.Parser),Zr("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,o){const i=ro(e),l=n.parse(i);function a(e,n){e||!n?o(e):r?r(n):t(void 0,n)}n.run(l,i,(function(e,t,r){if(e||!t||!r)return a(e);const o=t,i=n.stringify(o,r);var l;"string"==typeof(l=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(l)?r.value=i:r.result=i,a(e,r)}))}}processSync(e){let t,n=!1;return this.freeze(),Gr("processSync",this.parser||this.Parser),Zr("processSync",this.compiler||this.Compiler),this.process(e,(function(e,r){n=!0,Cr(e),t=r})),no("processSync","process",n),t}run(e,t,n){to(e),this.freeze();const r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?o(void 0,n):new Promise(o);function o(o,i){const l=ro(t);r.run(e,l,(function(t,r,l){const a=r||e;t?i(t):o?o(a):n(void 0,a,l)}))}}runSync(e,t){let n,r=!1;return this.run(e,t,(function(e,t){Cr(e),n=t,r=!0})),no("runSync","run",r),n}stringify(e,t){this.freeze();const n=ro(t),r=this.compiler||this.Compiler;return Zr("stringify",r),to(e),r(e,n)}use(e,...t){const n=this.attachers,r=this.namespace;if(eo("use",this.frozen),null==e);else if("function"==typeof e)a(e,t);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?l(e):i(e)}return this;function o(e){if("function"==typeof e)a(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[t,...n]=e;a(t,n)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=Dr(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;)o(e[t])}}function a(e,t){let r=-1,o=-1;for(;++r<n.length;)if(n[r][0]===e){o=r;break}if(-1===o)n.push([e,...t]);else if(t.length>0){let[r,...i]=t;const l=n[o][1];Mr(l)&&Mr(r)&&(r=Dr(!0,l,r)),n[o]=[e,r,...i]}}}}const Jr=(new $r).freeze();function Gr(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `parser`")}function Zr(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `compiler`")}function eo(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function to(e){if(!Mr(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function no(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function ro(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new qr(e)}const oo=[],io={allowDangerousHtml:!0},lo=/^(https?|ircs?|mailto|xmpp)$/i,ao=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function so(t){const n=t.allowedElements,r=t.allowElement,o=t.children||"",i=t.className,l=t.components,a=t.disallowedElements,s=t.rehypePlugins||oo,u=t.remarkPlugins||oo,f=t.remarkRehypeOptions?{...t.remarkRehypeOptions,...io}:io,p=t.skipHtml,h=t.unwrapDisallowed,d=t.urlTransform||uo,m=Jr().use(lr).use(u).use(Sr,f).use(s),g=new qr;"string"==typeof o&&(g.value=o);for(const e of ao)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);const y=m.parse(g);let x=m.runSync(y,g);return i&&(x={type:"element",tagName:"div",properties:{className:i},children:"root"===x.type?x.children:[x]}),c(x,(function(e,t,o){if("raw"===e.type&&o&&"number"==typeof t)return p?o.children.splice(t,1):o.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ft)if(Object.hasOwn(ft,t)&&Object.hasOwn(e.properties,t)){const n=e.properties[t],r=ft[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=d(String(n||""),t,e))}}if("element"===e.type){let i=n?!n.includes(e.tagName):!!a&&a.includes(e.tagName);if(!i&&r&&"number"==typeof t&&(i=!r(e,t,o)),i&&o&&"number"==typeof t)return h&&e.children?o.children.splice(t,1,...e.children):o.children.splice(t,1),t}})),function(e,t){if(!t||void 0===t.Fragment)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if("function"!=typeof t.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=function(e,t){return function(n,r,o,i){const l=Array.isArray(o.children),a=we(n);return t(r,o,i,l,{columnNumber:a?a.column-1:void 0,fileName:e,lineNumber:a?a.line:void 0},void 0)}}(n,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw new TypeError("Expected `jsxs` in production options");o=t.jsx,i=t.jsxs,r=function(e,t,n,r){const l=Array.isArray(n.children)?i:o;return r?l(t,n,r):l(t,n)}}var o,i;const l={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?Y:W,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},a=tt(l,e,void 0);return a&&"string"!=typeof a?a:l.create(e,l.Fragment,{children:a||void 0},void 0)}(x,{Fragment:e.Fragment,components:l,ignoreInvalidStyle:!0,jsx:e.jsx,jsxs:e.jsxs,passKeys:!0,passNode:!0})}function uo(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return t<0||o>-1&&t>o||n>-1&&t>n||r>-1&&t>r||lo.test(e.slice(0,t))?e:""}function co(e,t){const n=String(e);let r=n.indexOf(t),o=r,i=0,l=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===o?++i>l&&(l=i):i=1,o=r+t.length,r=n.indexOf(t,o);return l}const fo=function(e){if(null==e)return ho;if("string"==typeof e)return t=e,po((function(e){return e.tagName===t}));var t;if("object"==typeof e)return function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=fo(e[n]);return po((function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}))}(e);if("function"==typeof e)return po(e);throw new Error("Expected function, string, or array as `test`")};function po(e){return function(t,n,r){return Boolean(function(e){return null!==e&&"object"==typeof e&&"type"in e&&"tagName"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function ho(e){return Boolean(e&&"object"==typeof e&&"type"in e&&"element"===e.type&&"tagName"in e&&"string"==typeof e.tagName)}export{co as A,Ut as B,ht as C,It as D,a as E,Ct as F,Nt as G,Pt as H,Kt as I,Rt as J,gt as K,Vt as L,so as M,Ot as N,Dt as O,vt as P,s as S,qr as V,X as a,Q as b,r as c,G as d,Y as e,H as f,ke as g,W as h,ve as i,xe as j,we as k,be as l,fe as m,h as n,Z as o,K as p,fo as q,Ke as r,$ as s,u as t,Jr as u,c as v,te as w,zt as x,Mt as y,St as z};
