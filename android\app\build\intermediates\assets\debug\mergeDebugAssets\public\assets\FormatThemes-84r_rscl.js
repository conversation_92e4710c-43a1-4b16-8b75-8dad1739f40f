import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{s,d as t,aq as i,ar as n,as as r,at as l,R as o,W as d,Z as c,B as m,U as h,an as u,a5 as x,L as j,ak as p,V as g}from"./index-CR7o3nEo.js";import{A as f,b as v}from"./alert-BM83XFmR.js";import{S as b}from"./separator-DaAFXe2m.js";import{L as N}from"./router-BAzpOxbo.js";import{R as y}from"./refresh-cw-CMhthn0W.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const w=()=>e.jsx("nav",{className:"bg-white shadow",children:e.jsx("ul",{className:"flex space-x-4",children:[{label:"Blog",path:"/admin/blog"},{label:"Categorias",path:"/admin/categories"},{label:"Medicamentos",path:"/admin/medications"},{label:"Dosagens",path:"/admin/dosages"},{label:"Categorias de Prescrição",path:"/admin/prescription-categories"},{label:"CID-10",path:"/admin/icd10"},{label:"Curvas de Crescimento",path:"/admin/growth-curves"},{label:"Metadados das Curvas",path:"/admin/growth-curve-metadata"},{label:"Vacinas",path:"/admin/vaccines"},{label:"Medicamentos na Amamentação",path:"/admin/breastfeeding-medications-enhancement"},{label:"Fórmulas",path:"/admin/formulas"},{label:"DNPM",path:"/admin/dnpm"},{label:"Configurações do Site",path:"/admin/site-settings"}].map((a=>e.jsx("li",{children:e.jsx(N,{to:a.path,className:"text-gray-700 hover:text-gray-900",children:a.label})},a.label)))})}),z=()=>{const{toast:o}=t(),[d,c]=a.useState(!1),[m,h]=a.useState(!1),[u,x]=a.useState("pediatria"),[j,p]=a.useState([]),[g,f]=a.useState(5),[v,b]=a.useState({}),[N,y]=a.useState({total:0,analyzed:0,remaining:0}),[z,C]=a.useState(0);a.useEffect((()=>{_()}),[u]);const _=async()=>{try{c(!0);const e=await(async e=>{try{const{count:a,error:t}=await s.from("questions").select("*",{count:"exact",head:!0}).eq("domain",e);if(t)throw t;const{count:i,error:n}=await s.from("questions").select("*",{count:"exact",head:!0}).eq("domain",e).not("tags->theme_analyzed","is",null);if(n)throw n;const r=a||0,l=i||0;return{total:r,analyzed:l,remaining:r-l}}catch(a){throw a}})(u);y(e)}catch(e){o({title:"Erro ao carregar estatísticas",description:"Não foi possível obter as estatísticas das questões.",variant:"destructive"})}finally{c(!1)}},k=async()=>{try{c(!0);const e=await(async(e,a=10)=>{try{const{data:t,error:i}=await s.from("questions").select("*").eq("domain",e).or("tags->theme_analyzed.is.null, not.tags->theme_analyzed").limit(a);if(i)throw i;return(t||[]).map((e=>({...e,alternatives:Array.isArray(e.alternatives)?e.alternatives:"object"==typeof e.alternatives&&null!==e.alternatives?Object.values(e.alternatives).map(String):[],tags:e.tags||{}})))}catch(t){throw t}})(u,g);0===e.length&&o({title:"Nenhuma questão disponível",description:"Não há mais questões para análise neste domínio."}),p(e),b({}),C(0)}catch(e){o({title:"Erro ao carregar questões",description:"Não foi possível obter as questões para análise.",variant:"destructive"})}finally{c(!1)}},Q=async()=>{if(0===j.length||z>=j.length)return void o({title:"Sem questões",description:"Não há questões disponíveis para análise.",variant:"destructive"});const e=j[z];try{h(!0);const a=await(async(e,a)=>{try{const{data:t,error:i}=await s.from("questions").select("*").eq("id",e).single();if(i)throw i;if(!t)throw new Error("Question not found");const n=`Analyze this medical question from the field of ${"pediatria"===a?"pediatrics":"ophthalmology"} and recommend the most appropriate theme and focus categories:\n\nStatement: "${t.statement}"\n\n${t.alternatives?`\nAlternatives:\n${Array.isArray(t.alternatives)?t.alternatives.map(((e,a)=>`${a+1}. ${e}`)).join("\n"):"object"==typeof t.alternatives&&null!==t.alternatives?Object.values(t.alternatives).map(((e,a)=>`${a+1}. ${e}`)).join("\n"):"No alternatives provided"}`:""}\n\nCurrent categories:\n- Specialty: ${t.specialty?.name||"Not assigned"}\n- Theme: ${t.theme?.name||"Not assigned"}\n- Focus: ${t.focus?.name||"Not assigned"}\n\nPlease recommend the most appropriate theme (broader category) and focus (more specific subcategory) for this question. They must be different from each other.`,{data:r,error:l}=await s.functions.invoke("analyze-question-theme",{body:{prompt:n,domain:a}});if(l)throw l;const o={...t.tags||{},theme_analyzed:!0,analyzed_at:(new Date).toISOString(),analyzed_domain:a,recommended_theme:r.recommendedTheme,recommended_focus:r.recommendedFocus,recommendation_justification:r.justification,is_new_theme:r.isNewTheme,is_new_focus:r.isNewFocus},{error:d}=await s.from("questions").update({tags:o}).eq("id",e);if(d)throw d;return{...r,questionId:e,tags:o}}catch(t){throw t}})(e.id,u);b((s=>({...s,[e.id]:a}))),o({title:"Análise concluída",description:"A questão foi analisada com sucesso."})}catch(a){o({title:"Erro na análise",description:"Não foi possível analisar a questão.",variant:"destructive"})}finally{h(!1)}},E=()=>{z<j.length-1?C(z+1):o({title:"Último item",description:"Este é o último item do lote."})},$=()=>{z>0?C(z-1):o({title:"Primeiro item",description:"Este é o primeiro item do lote."})},F=async()=>{await _(),o({title:"Estatísticas atualizadas",description:"As estatísticas foram atualizadas com sucesso."})},P=j[z],R=P?v[P.id]:null,T=async()=>{if(P)try{c(!0);const e={...P.tags||{},theme_analyzed:!0,analyzed_at:(new Date).toISOString(),analyzed_domain:u,manually_reviewed:!0},{error:a}=await s.from("questions").update({tags:e}).eq("id",P.id);if(a)throw a;o({title:"Questão marcada como revisada",description:"Esta questão foi marcada como revisada manualmente."});const t=[...j];if(t.splice(z,1),0===t.length)return p([]),C(0),void(await _());const i=z>=t.length?t.length-1:z;p(t),C(i),await _()}catch(e){o({title:"Erro",description:"Não foi possível marcar a questão como revisada.",variant:"destructive"})}finally{c(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(w,{}),e.jsxs("div",{className:"container mx-auto p-6 max-w-5xl",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Análise de Temas de Questões"}),e.jsx("p",{className:"text-gray-600",children:"Use esta ferramenta para analisar e recomendar temas e focos para questões"})]}),e.jsxs(i,{defaultValue:"pediatria",value:u,onValueChange:e=>x(e),children:[e.jsxs(n,{className:"mb-4",children:[e.jsx(r,{value:"pediatria",children:"Pediatria"}),e.jsx(r,{value:"oftalmologia",children:"Oftalmologia"})]}),e.jsxs(l,{value:"pediatria",className:"space-y-4",children:[e.jsx(q,{stats:N,isLoading:d,onRefresh:F}),e.jsx(A,{batchSize:g,setBatchSize:f,loadQuestions:k,isLoading:d}),j.length>0&&e.jsx(S,{question:P,result:R,isAnalyzing:m,onAnalyze:Q,onNext:E,onPrevious:$,currentIndex:z,totalQuestions:j.length,onMarkReviewed:T})]}),e.jsxs(l,{value:"oftalmologia",className:"space-y-4",children:[e.jsx(q,{stats:N,isLoading:d,onRefresh:F}),e.jsx(A,{batchSize:g,setBatchSize:f,loadQuestions:k,isLoading:d}),j.length>0&&e.jsx(S,{question:P,result:R,isAnalyzing:m,onAnalyze:Q,onNext:E,onPrevious:$,currentIndex:z,totalQuestions:j.length,onMarkReviewed:T})]})]})]})]})},q=({stats:a,isLoading:s,onRefresh:t})=>{const i=a.total?Math.round(a.analyzed/a.total*100):0;return e.jsxs(o,{children:[e.jsxs(d,{className:"flex flex-row items-center justify-between pb-2",children:[e.jsx(c,{children:"Estatísticas"}),e.jsxs(m,{variant:"outline",size:"sm",onClick:t,disabled:s,children:[e.jsx(y,{className:"h-4 w-4 mr-1"}),"Atualizar"]})]}),e.jsxs(h,{children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Total"}),e.jsx("div",{className:"text-2xl font-bold",children:s?"...":a.total})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium text-green-600",children:"Analisadas"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:s?"...":a.analyzed})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium text-blue-600",children:"Restantes"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:s?"...":a.remaining})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-500 mb-1",children:["Progresso: ",i,"%"]}),e.jsx("div",{className:"h-2 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-green-500 transition-all duration-500",style:{width:`${i}%`}})})]})]})]})},A=({batchSize:a,setBatchSize:s,loadQuestions:t,isLoading:i})=>e.jsxs(o,{children:[e.jsx(d,{children:e.jsx(c,{children:"Carregar Questões"})}),e.jsx(h,{children:e.jsxs("div",{className:"flex items-end gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(u,{htmlFor:"batchSize",className:"mb-2 block",children:"Quantidade de questões"}),e.jsx(x,{id:"batchSize",type:"number",min:"1",max:"20",value:a,onChange:e=>s(parseInt(e.target.value)||5)})]}),e.jsx(m,{onClick:t,disabled:i,className:"flex-1",children:i?e.jsxs(e.Fragment,{children:[e.jsx(j,{className:"mr-2 h-4 w-4 animate-spin"}),"Carregando..."]}):"Carregar Questões"})]})})]}),S=({question:a,result:s,isAnalyzing:t,onAnalyze:i,onNext:n,onPrevious:r,currentIndex:l,totalQuestions:u,onMarkReviewed:x})=>{if(!a)return null;const N=!0===a.tags?.theme_analyzed;return e.jsxs(o,{children:[e.jsxs(d,{className:"flex flex-row items-center justify-between pb-2",children:[e.jsxs(c,{children:["Questão ",l+1," de ",u]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{variant:"outline",size:"sm",onClick:r,disabled:t||0===l,children:"Anterior"}),e.jsx(m,{variant:"outline",size:"sm",onClick:n,disabled:t||l===u-1,children:"Próxima"})]})]}),e.jsxs(h,{className:"space-y-4",children:[N&&e.jsxs(f,{variant:"warning",className:"bg-amber-50 border-amber-200",children:[e.jsx(p,{className:"h-4 w-4 text-amber-600"}),e.jsx(v,{className:"text-amber-800",children:"Esta questão já foi analisada anteriormente."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Enunciado da Questão:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("p",{children:a.statement})})]}),Array.isArray(a.alternatives)&&a.alternatives.length>0&&e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Alternativas:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("ol",{className:"list-decimal list-inside",children:a.alternatives.map(((a,s)=>e.jsx("li",{className:"mb-1",children:a},s)))})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Especialidade atual:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("p",{children:a.specialty?.name||"Não definido"})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Tema atual:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("p",{children:a.theme?.name||"Não definido"})})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Foco atual:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("p",{children:a.focus?.name||"Não definido"})})]})]}),e.jsx(b,{}),s?e.jsxs("div",{className:"space-y-4",children:[e.jsxs(f,{className:"bg-green-50 border-green-200",children:[e.jsx(g,{className:"h-4 w-4 text-green-600"}),e.jsx(v,{className:"text-green-800",children:"Análise concluída com sucesso!"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Tema recomendado:"}),e.jsxs("div",{className:"bg-green-50 p-3 rounded-md",children:[e.jsx("p",{className:"font-medium",children:s.recommendedTheme}),s.isNewTheme&&e.jsx("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full mt-1 inline-block",children:"Novo tema"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Foco recomendado:"}),e.jsxs("div",{className:"bg-green-50 p-3 rounded-md",children:[e.jsx("p",{className:"font-medium",children:s.recommendedFocus}),s.isNewFocus&&e.jsx("span",{className:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full mt-1 inline-block",children:"Novo foco"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium mb-2",children:"Justificativa:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("p",{children:s.justification})})]})]}):e.jsxs("div",{className:"flex flex-col md:flex-row justify-between gap-4",children:[e.jsx(m,{onClick:i,disabled:t,className:"flex-1",children:t?e.jsxs(e.Fragment,{children:[e.jsx(j,{className:"mr-2 h-4 w-4 animate-spin"}),"Analisando..."]}):"Analisar Questão"}),e.jsx(m,{variant:"outline",onClick:x,disabled:t,className:"flex-1",children:"Marcar Como Revisada"})]})]})]})};export{z as FormatThemes,z as default};
