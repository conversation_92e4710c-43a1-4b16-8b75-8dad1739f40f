import{j as e}from"./radix-core-6kBL75b5.js";import{u as s,U as i,V as a,Y as t,_ as r,a0 as o,av as n,P as l,l as d,ac as c,n as m,k as p,al as x,aw as j,S as u}from"./index-D89HBjcn.js";import{L as h}from"./router-BAzpOxbo.js";import{L as g}from"./layout-dashboard-CFW423ZE.js";import{W as N}from"./wrench-BSxltnlI.js";import{N as f}from"./newspaper-BNnmghGn.js";import{B as b}from"./bookmark-CgtQ1BKb.js";import{B as v}from"./beaker-BvOT41Qo.js";import{T as w}from"./tag-NUSzLqhK.js";import{D as k}from"./database-O6QBRcB0.js";import{C as A}from"./chart-column-Bx6HktL4.js";import{F as y}from"./file-question-BFIq6IV5.js";import{B as C}from"./book-open-JvVCwLVv.js";import{B as M}from"./brain-circuit-DmXC3met.js";import{S as G}from"./stethoscope-C94gb3Zp.js";import{S as D}from"./syringe-CfMhlReq.js";import{M as I}from"./milk-VQnJxZ5N.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const S=()=>{const{profile:S}=s(),q=["<EMAIL>","<EMAIL>"],B=S?.is_admin&&q.includes(S?.email||""),F=[{title:"Administradores",description:"Gerenciar usuários administrativos e permissões",icon:e.jsx(n,{className:"h-8 w-8 text-red-500"}),link:"/admin/admin-users"},{title:"Modo de Manutenção",description:"Controlar acesso ao site durante manutenções",icon:e.jsx(N,{className:"h-8 w-8 text-orange-600"}),link:"/admin/maintenance",superAdminOnly:!0},{title:"Blog",description:"Gerenciar posts, categorias e tags do blog",icon:e.jsx(f,{className:"h-8 w-8 text-blue-500"}),link:"/admin/blog"},{title:"Categorias de Medicamentos",description:"Gerenciar categorias de medicamentos",icon:e.jsx(b,{className:"h-8 w-8 text-purple-500"}),link:"/admin/categories"},{title:"Medicamentos",description:"Gerenciar medicamentos e posologias",icon:e.jsx(l,{className:"h-8 w-8 text-green-500"}),link:"/admin/medications"},{title:"Fórmulas",description:"Gerenciar fórmulas médicas",icon:e.jsx(v,{className:"h-8 w-8 text-amber-500"}),link:"/admin/formulas"},{title:"Dosagens",description:"Gerenciar dosagens de medicamentos",icon:e.jsx(d,{className:"h-8 w-8 text-emerald-500"}),link:"/admin/dosages"},{title:"Bulas",description:"Gerenciar instruções de medicamentos",icon:e.jsx(c,{className:"h-8 w-8 text-yellow-500"}),link:"/admin/instructions"},{title:"Categorias de Prescrição",description:"Gerenciar categorias de prescrição",icon:e.jsx(w,{className:"h-8 w-8 text-orange-500"}),link:"/admin/prescription-categories"},{title:"CID-10",description:"Gerenciar códigos CID-10",icon:e.jsx(k,{className:"h-8 w-8 text-indigo-500"}),link:"/admin/icd10"},{title:"Curvas de Crescimento",description:"Gerenciar curvas de crescimento",icon:e.jsx(m,{className:"h-8 w-8 text-red-500"}),link:"/admin/growth-curves"},{title:"Dados das Curvas",description:"Gerenciar metadados das curvas de crescimento",icon:e.jsx(A,{className:"h-8 w-8 text-cyan-500"}),link:"/admin/growth-curve-metadata"},{title:"Importar Questões",description:"Importar questões para o sistema",icon:e.jsx(y,{className:"h-8 w-8 text-violet-500"}),link:"/admin/question-import"},{title:"Importar Medicamentos",description:"Importar nova estrutura de medicamentos",icon:e.jsx(l,{className:"h-8 w-8 text-green-500"}),link:"/admin/medication-import"},{title:"Formatação de Questões",description:"Formatar questões no sistema",icon:e.jsx(C,{className:"h-8 w-8 text-fuchsia-500"}),link:"/admin/question-formatting"},{title:"Formatar Temas",description:"Gerenciar formatação de temas",icon:e.jsx(M,{className:"h-8 w-8 text-pink-500"}),link:"/admin/format-themes"},{title:"Condutas e Manejos",description:"Gerenciar condutas e manejos clínicos",icon:e.jsx(G,{className:"h-8 w-8 text-teal-500"}),link:"/admin/condutas-e-manejos"},{title:"Vacinas",description:"Gerenciar informações sobre vacinas",icon:e.jsx(D,{className:"h-8 w-8 text-lime-500"}),link:"/admin/vaccines"},{title:"Medicamentos na Amamentação",description:"Aprimorar informações sobre medicamentos durante a amamentação",icon:e.jsx(I,{className:"h-8 w-8 text-pink-400"}),link:"/admin/breastfeeding-medications-enhancement"},{title:"Melhoria de Medicamentos com IA",description:"Usar IA para melhorar nomes e classificar medicamentos",icon:e.jsx(M,{className:"h-8 w-8 text-purple-600"}),link:"/admin/medication-improvement"},{title:"🧪 Validação CAS/DCB com IA",description:"Validar e corrigir códigos CAS e DCB dos princípios ativos",icon:e.jsx(v,{className:"h-8 w-8 text-emerald-600"}),link:"/admin/cas-dcb-validation"},{title:"Analytics de Medicamentos",description:"Análise detalhada do uso da plataforma de medicamentos",icon:e.jsx(A,{className:"h-8 w-8 text-blue-600"}),link:"/admin/medication-analytics"},{title:"Marcos DNPM",description:"Gerenciar marcos de desenvolvimento neuropsicomotor",icon:e.jsx(p,{className:"h-8 w-8 text-rose-500"}),link:"/admin/dnpm"},{title:"🚨 Debug de Problemas",description:"Monitorar e debugar problemas do sistema em tempo real",icon:e.jsx(x,{className:"h-8 w-8 text-red-600"}),link:"/admin/problems-debug"},{title:"💬 Gestão de Feedbacks",description:"Gerenciar todos os feedbacks dos usuários do site",icon:e.jsx(j,{className:"h-8 w-8 text-blue-600"}),link:"/admin/feedback-management"},{title:"🧬 Formatação de Princípios Ativos",description:"Usar IA para analisar e melhorar dados de princípios ativos",icon:e.jsx(M,{className:"h-8 w-8 text-emerald-600"}),link:"/admin/active-ingredients-formatter"},{title:"🤖 Controle Dr. Will",description:"Ativar/desativar o assistente IA Dr. Will",icon:e.jsx(M,{className:"h-8 w-8 text-purple-600"}),link:"/admin/dr-will-control"},{title:"Configurações",description:"Configurações do sistema",icon:e.jsx(u,{className:"h-8 w-8 text-gray-500"}),link:"/admin/settings"},{title:"Configurações do Site",description:"Configurações gerais do site",icon:e.jsx(g,{className:"h-8 w-8 text-slate-500"}),link:"/admin/site-settings"}];return e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Painel Administrativo"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Bem-vindo ao painel administrativo do Pediatria Descomplicada. Aqui você pode gerenciar todo o conteúdo e configurações do site."}),e.jsx(i,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100",children:e.jsx(a,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"bg-blue-100 p-3 rounded-full",children:e.jsx(g,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-blue-900",children:"Gestão de Conteúdo Médico"}),e.jsx("p",{className:"text-blue-700",children:"Este painel permite a manutenção de todas as informações médicas, incluindo medicamentos, dosagens, bulas, CID-10 e outros recursos essenciais para profissionais de saúde."})]})]})})})]}),e.jsxs("div",{className:"mb-6 p-4 bg-yellow-50 border-2 border-yellow-200 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-bold text-yellow-800 mb-3",children:"🔍 DEBUG INFO - Sistema de Manutenção"}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Email:"})," ",S?.email||"Não logado"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"User ID:"})," ",S?.id||"N/A"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Full Name:"})," ",S?.full_name||"N/A"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Is Admin:"})," ",S?.is_admin?"✅ Sim":"❌ Não"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Is Super Admin:"})," ",B?"✅ Sim":"❌ Não"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Total Módulos:"})," ",F.length]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Módulos Filtrados:"})," ",F.filter((e=>!e.superAdminOnly||B)).length]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Módulos Super Admin:"})," ",F.filter((e=>e.superAdminOnly)).length]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Emails Super Admin:"})," <EMAIL>, <EMAIL>"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Email Match:"})," ",q.includes(S?.email||"")?"✅ Sim":"❌ Não"]})]})]}),e.jsx("div",{className:"mt-3 p-2 bg-yellow-100 rounded",children:e.jsxs("p",{className:"text-xs",children:[e.jsx("strong",{children:"Módulos Super Admin:"})," ",F.filter((e=>e.superAdminOnly)).map((e=>e.title)).join(", ")]})}),e.jsx("div",{className:"mt-2 p-2 bg-blue-100 rounded",children:e.jsxs("p",{className:"text-xs",children:[e.jsx("strong",{children:"Condição Super Admin:"})," is_admin=",S?.is_admin?"true":"false"," AND email='",S?.email,"' IN ['<EMAIL>', '<EMAIL>']"]})})]}),e.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Módulos Disponíveis"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:F.filter((e=>!e.superAdminOnly||B)).map(((s,a)=>e.jsx(h,{to:s.link,children:e.jsx(i,{className:"h-full hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-primary",children:e.jsxs(t,{className:"pb-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(r,{className:"text-lg",children:s.title}),s.icon]}),e.jsx(o,{children:s.description})]})})},a)))})]})};export{S as default};
