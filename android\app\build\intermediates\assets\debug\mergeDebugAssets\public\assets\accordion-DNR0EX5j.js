import{j as a}from"./radix-core-6kBL75b5.js";import{r}from"./critical-DVX9Inzy.js";import{g as e,I as s,H as o,h as d,i as t}from"./radix-layout-CC8mXA4O.js";import{j as i,aH as l}from"./index-CFnD44mG.js";const n=e,m=r.forwardRef((({className:r,...e},o)=>a.jsx(s,{ref:o,className:i("border-b border-gray-200 dark:border-gray-700",r),...e})));m.displayName="AccordionItem";const c=r.forwardRef((({className:r,children:e,...s},t)=>a.jsx(o,{className:"flex",children:a.jsxs(d,{ref:t,className:i("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180 border border-gray-200 dark:border-gray-700 rounded-md px-4 mb-1 bg-white dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700",r),...s,children:[e,a.jsx(l,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})));c.displayName=d.displayName;const f=r.forwardRef((({className:r,children:e,...s},o)=>a.jsx(t,{ref:o,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:a.jsx("div",{className:i("pb-4 pt-0",r),children:e})})));f.displayName=t.displayName;export{n as A,m as a,c as b,f as c};
