import{j as e}from"./radix-core-6kBL75b5.js";import{r as s,b as a}from"./critical-DVX9Inzy.js";import{L as i}from"./router-BAzpOxbo.js";import{ao as r,a6 as t,U as d,Y as l,_ as n,V as o,Z as c,B as m,j as x,a0 as h,n as j,aM as p,ab as u,a8 as g}from"./index-DQuOk0R3.js";import N from"./Footer-BmzagX2Z.js";import{F as f,a as b}from"./flowchartSEOData-odTQE9i1.js";import{C as v}from"./clock-yhRd8x7-.js";import{S as y}from"./scale-TvLu6nmW.js";import{A as k,a as w,b as C}from"./alert-W1it20m_.js";import{S as I}from"./stethoscope-DpPTC3SB.js";import{S as A}from"./syringe-Ce56EEU5.js";import{C as M}from"./clipboard-list-CwTcn3z9.js";import{H as S}from"./FeedbackTrigger-Bwp4oswe.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-DOFGyRt3.js";import"./rocket-Czd-at64.js";import"./target-CuA2iBUH.js";import"./zap-B06nQ-rd.js";import"./book-open-gHhE7Hhk.js";import"./star-CaqDe8as.js";import"./circle-help-CTIYt4iy.js";const O=({value:s,onChange:a,onCommit:i})=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"age",className:"text-lg font-medium text-red-700 dark:text-red-400 flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5"}),"Idade do Paciente"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{id:"age",type:"number",min:0,max:100,value:s||"",onChange:e=>{const s=parseFloat(e.target.value);if(!isNaN(s)){const e=Math.min(Math.max(0,s),100);a(e),i(e)}},className:"flex-1 bg-white/50 dark:bg-slate-800/50 border-red-200 dark:border-red-900/30 focus:border-red-400 dark:focus:border-red-500 transition-colors text-center text-lg",placeholder:"Digite a idade"}),e.jsx("span",{className:"text-lg font-medium text-red-700 dark:text-red-400 min-w-[3rem]",children:"anos"})]})]}),R=({value:a,onChange:i,onCommit:d})=>{const[l,n]=s.useState(a.toString());return s.useEffect((()=>{n(a.toString())}),[a]),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"weight",className:"text-lg font-medium text-red-700 dark:text-red-400 flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Peso do Paciente"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{id:"weight",type:"number",min:0,max:100,value:l,onChange:e=>{const s=e.target.value;n(s);const a=parseFloat(s);if(!isNaN(a)){const e=Math.min(a,100);i(e),d(e)}},onBlur:()=>{const e=parseFloat(l);isNaN(e)||e<0?(n("0"),i(0),d(0)):e>100?(n("100"),i(100),d(100)):(n(e.toString()),i(e),d(e))},className:"flex-1 bg-white/50 dark:bg-slate-800/50 border-red-200 dark:border-red-900/30 focus:border-red-400 dark:focus:border-red-500 transition-colors text-center text-lg",placeholder:"Digite o peso"}),e.jsx("span",{className:"text-lg font-medium text-red-700 dark:text-red-400 min-w-[3rem]",children:"kg"})]})]})},V=({weight:s,onWeightChange:a,onWeightCommit:i,age:r,onAgeChange:t,onAgeCommit:d})=>e.jsxs("div",{className:"space-y-6 bg-gradient-to-br from-red-50/90 via-white/80 to-red-50/90 dark:from-red-900/20 dark:via-slate-800/70 dark:to-red-900/10 p-6 rounded-xl backdrop-blur-sm border border-red-100 dark:border-red-900/30",children:[e.jsx(R,{value:s,onChange:a,onCommit:i}),e.jsx(O,{value:r,onChange:t,onCommit:d})]}),D=({onBiphasicResponse:s,onResolutionResponse:i,showBiphasicQuestion:r,doseCount:t=1})=>{const[h,j]=a.useState(!1),p=e=>{e||(j(!0),setTimeout((()=>j(!1)),5e3)),i(e)};return e.jsxs(d,{className:"w-full animate-fade-in",children:[e.jsx(l,{className:"text-center",children:e.jsxs(n,{className:"flex items-center justify-center gap-2",children:[e.jsx(I,{className:"h-6 w-6 text-red-500"}),"Monitorização do Paciente",t>1&&e.jsxs("span",{className:"ml-2 inline-flex items-center justify-center px-2.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded-full animate-pulse",children:["Dose ",t,"/3"]})]})}),e.jsxs(o,{className:"space-y-6",children:[h&&!r&&e.jsxs(k,{variant:"destructive",className:"animate-fade-in",children:[e.jsx(c,{className:"h-4 w-4"}),e.jsx(w,{children:"Nova Dose Necessária"}),e.jsxs(C,{children:["Sem melhora após dose ",t,". Preparar nova dose de adrenalina."]})]}),e.jsx("div",{className:"text-center space-y-6 animate-slide-in-up",children:r?e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"font-medium text-gray-800",children:"O paciente apresentou novos sintomas de anafilaxia (resposta bifásica)?"}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(m,{onClick:()=>s(!0),className:"bg-red-500 hover:bg-red-600 text-white min-w-[100px]",children:"Sim"}),e.jsx(m,{onClick:()=>s(!1),variant:"outline",className:x("border-red-200 text-red-700 hover:bg-red-50 min-w-[100px]","transition-all duration-300 hover:scale-105"),children:"Não"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"font-medium text-gray-800",children:"Houve resolução da anafilaxia?"}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(m,{onClick:()=>p(!0),className:"bg-red-500 hover:bg-red-600 text-white min-w-[100px]",children:"Sim"}),e.jsx(m,{onClick:()=>p(!1),variant:"outline",className:x("border-red-200 text-red-700 hover:bg-red-50 min-w-[100px]","transition-all duration-300 hover:scale-105"),children:"Não"})]})]})})]})]})},F=({weight:s,doseNumber:a,adrenalineDose:i,requiresICU:r=!1})=>{return e.jsxs(d,{className:"w-full animate-fade-in",children:[e.jsxs(l,{children:[e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-6 w-6 text-red-500"}),"Plano de Tratamento"]}),e.jsx(h,{children:r?"Tratamento em UTI":`${a}ª dose de Adrenalina`})]}),e.jsx(o,{className:"space-y-4",children:r?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-red-50 p-4 rounded-lg border border-red-200",children:[e.jsx("h3",{className:"font-medium text-red-700 mb-2",children:"Adrenalina IV em infusão contínua"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-red-600",children:["Dose calculada: ",(t=s,`${(.1*t).toFixed(2)}-${(1*t).toFixed(2)}`)," mcg/min"]}),e.jsxs("div",{className:"text-sm text-red-500 space-y-1",children:[e.jsx("p",{children:"Diluição padrão:"}),e.jsxs("ul",{className:"list-disc pl-4",children:[e.jsx("li",{children:"Adrenalina (1 mg/mL) 1 mL + 99 mL de SG 5%"}),e.jsx("li",{children:"Concentração final: 10 microgramas/mL"}),e.jsx("li",{children:"Dose: 0,1-1 microgramas/kg/minuto EV em infusão contínua"})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-medium",children:"Ações Imediatas:"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-1",children:[e.jsx("li",{children:"Encaminhar o paciente para a UTI pediátrica"}),e.jsx("li",{children:"Garantir monitorização contínua de sinais vitais"})]})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-red-50 p-4 rounded-lg border border-red-200",children:[e.jsx("h3",{className:"font-medium text-red-700 mb-2",children:"Administração de Adrenalina IM"}),e.jsxs("p",{className:"text-red-600",children:["Dose calculada: ",i," mL de adrenalina 1 mg/mL (1:1000)"]}),e.jsx("p",{className:"text-sm text-red-500 mt-1",children:"Aplicar no vasto lateral da coxa"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-medium",children:"Monitorização:"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-1",children:[e.jsx("li",{children:"Reavaliar ABCDE"}),e.jsx("li",{children:"Manter o paciente em observação por no mínimo 4 horas"})]})]})]})})]});var t},P=()=>e.jsxs(d,{className:"w-full",children:[e.jsx(l,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-6 w-6 text-green-500"}),"Orientações de Alta"]})}),e.jsxs(o,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-medium",children:"Tempo de Observação:"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-1",children:[e.jsx("li",{children:"6-8 horas: (Caso tenha apenas sintomas respiratórios);"}),e.jsx("li",{children:"12-24 horas: (Caso tenha hipotensão ou disfunção orgânica);"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-medium",children:"Prescrição e Orientações:"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-1",children:[e.jsx("li",{children:"Prednisolona 1x/dia VO 3-7dias;"}),e.jsx("li",{children:"Antihistamínico (Ex. Fexofenadina, Deslpratadina) VO por 3-7 dias;"}),e.jsx("li",{children:"Restrição do fator causal, se suspeito ou conhecido;"}),e.jsx("li",{children:"Encaminhar para o especialista;"}),e.jsx("li",{children:"Fornecer orientações sobre manejo de crises futuras;"})]})]})]})]}),E=({showAdjuvantTherapy:s=!1,weight:a=0,age:i=0})=>{const r=a>=30,t=()=>{if(r)return{fexofenadine:"180mg VO 1 cp/dose. Repetir a dose em 20 min s/n",diphenhydramine:"25-50mg/dose IM/IV/IO a cada 4-6h (máx 50mg/dose)",promethazine:"25-50mg/dose IM a cada 4-6h (máx 50mg/dose)"};const e=Math.min(2*a,50),s=Math.min(2*a,50);let t="";return t=i<=2?"5,0ml/dose":i<=5?"10ml/dose":"20ml/dose",{fexofenadine:`6mg/ml VO - ${t} - Repetir a dose em 20 min s/n`,diphenhydramine:`${e}mg/dose IM/IV/IO a cada 4-6h`,promethazine:`${s}mg/dose IM a cada 4-6h`}},c=()=>r?{prednisolone:"40mg/dose VO",hydrocortisone:"100mg/200mg/500mg/dose IM/IV/IO",methylprednisolone:"40-80mg/dose IM/IV/IO"}:{prednisolone:`${Math.min(1*a,40)}mg/dose VO`,hydrocortisone:`${Math.min(5*a,500)}mg/dose IM/IV/IO`,methylprednisolone:`${Math.min(2*a,60)}mg/dose IM/IV/IO`},m=()=>r?{salbutamol:"4-8 jatos a cada 20 minutos (até 3 doses em 1h)",fenoterol:"10-20 gotas + Brometo de ipratrópio 40 gotas ou spray 4-8 jatos"}:{salbutamol:"2-4 jatos a cada 20 minutos (até 3 doses em 1h)",fenoterol:`${Math.min(a/3,10).toFixed(1)} gotas + Brometo de ipratrópio ${Math.min(a/2,20)} gotas ou spray 4 jatos`};return e.jsx("div",{className:"space-y-6 animate-fade-in",children:e.jsxs(d,{className:"w-full bg-gradient-to-br from-red-50/90 via-white/80 to-red-50/90 border-red-100 dark:from-red-900/20 dark:via-slate-800/70 dark:to-red-900/10 dark:border-red-900/30",children:[e.jsx(l,{children:e.jsxs(n,{className:"flex items-center gap-2 text-red-700 dark:text-red-400",children:[e.jsx(j,{className:"h-6 w-6"}),"Monitorização do Paciente"]})}),e.jsx(o,{className:"space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-white/80 p-4 rounded-lg border border-red-100 dark:bg-slate-800/70 dark:border-red-900/30",children:[e.jsxs("h3",{className:"font-medium text-red-700 dark:text-red-400 mb-2 flex items-center gap-2",children:[e.jsx(S,{className:"h-5 w-5"}),"Avaliação ABC"]}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300",children:[e.jsx("li",{children:"Monitorização cardíaca contínua (ECG)"}),e.jsx("li",{children:"Oximetria de pulso"}),e.jsx("li",{children:"PA e nível de consciência"})]})]}),e.jsxs("div",{className:"bg-white/80 p-4 rounded-lg border border-red-100 dark:bg-slate-800/70 dark:border-red-900/30",children:[e.jsxs("h3",{className:"font-medium text-red-700 dark:text-red-400 mb-2 flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5"}),"Intervenções Imediatas"]}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300",children:[e.jsx("li",{children:"Decúbito dorsal com elevação de MMII"}),e.jsx("li",{children:"Oxigênio inalatório conforme necessário"}),e.jsx("li",{children:"Pesquisar e remover alérgeno (se identificado)"})]})]}),s&&e.jsxs("div",{className:"bg-white/80 p-4 rounded-lg border border-red-100 dark:bg-slate-800/70 dark:border-red-900/30",children:[e.jsxs("h3",{className:"font-medium text-red-700 dark:text-red-400 mb-2 flex items-center gap-2",children:[e.jsx(A,{className:"h-5 w-5"}),"Terapia Adjuvante (",r?"Adulto >30kg":"Lactente/Criança <30kg",")"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-600 dark:text-red-400 mb-1",children:"Anti-histamínico"}),e.jsxs("ul",{className:"text-sm text-gray-700 dark:text-gray-300 list-disc pl-4 space-y-1",children:[e.jsxs("li",{children:["Fexofenadina: ",t().fexofenadine]}),e.jsxs("li",{children:["Difenidramina: ",t().diphenhydramine]}),e.jsxs("li",{children:["Prometazina: ",t().promethazine]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-600 dark:text-red-400 mb-1",children:"Corticosteroides"}),e.jsxs("ul",{className:"text-sm text-gray-700 dark:text-gray-300 list-disc pl-4 space-y-1",children:[e.jsxs("li",{children:["Prednisolona: ",c().prednisolone]}),e.jsxs("li",{children:["Hidrocortisona: ",c().hydrocortisone]}),e.jsxs("li",{children:["Metilprednisolona: ",c().methylprednisolone]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-600 dark:text-red-400 mb-1",children:"Reposição Volêmica"}),e.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:r?"SF 0.9%/Ringer até 2L em bolus IV/IO":`SF 0.9% ${Math.min(20*a,60*a)}ml em bolus IV/IO`})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-600 dark:text-red-400 mb-1",children:"β2 agonista (broncodilatador)"}),e.jsxs("ul",{className:"text-sm text-gray-700 dark:text-gray-300 list-disc pl-4 space-y-1",children:[e.jsxs("li",{children:["Salbutamol: ",m().salbutamol]}),e.jsxs("li",{children:["Fenoterol: ",m().fenoterol]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-600 dark:text-red-400 mb-1",children:"Antiemético"}),e.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line",children:`Ondansetrona ${Math.min(.2*a,16)}mg SL/IM/IV\n*até 8mg: IM ou IV em 30s\n**>8mg: IV lento em SF0,9% 50ml em 15min`})]})]})]})]})})]})})},B=()=>e.jsx("div",{className:"space-y-6 animate-fade-in",children:e.jsxs(d,{className:"w-full",children:[e.jsxs(l,{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-2 text-red-600",children:[e.jsx(c,{className:"h-6 w-6"}),e.jsx(n,{children:"Critérios para Anafilaxia (WAO 2020)"})]}),e.jsx("p",{className:"text-sm text-red-600 font-medium",children:"ANAFILAXIA = RECONHECIMENTO PRECOCE + NÃO RETARDAR ADRENALINA IM"})]}),e.jsxs(o,{className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg border border-purple-200",children:[e.jsx("h3",{className:"font-medium text-purple-800 mb-3",children:"Paciente com Alergia Conhecida/Suspeita"}),e.jsx("p",{className:"text-sm text-gray-600 mb-2 italic",children:"(alérgeno conhecido ou suspeito)"}),e.jsxs("ul",{className:"list-disc pl-4 space-y-2 text-gray-700",children:[e.jsx("li",{children:"Início súbito de hipotensão"}),e.jsx("li",{children:"Broncoespasmo"}),e.jsx("li",{children:"Edema de laringe"})]})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[e.jsx("h3",{className:"font-medium text-green-800 mb-3",children:"Paciente sem Alergia Conhecida"}),e.jsx("p",{className:"text-sm text-gray-600 mb-2 italic",children:"(alérgeno desconhecido)"}),e.jsxs("div",{className:"space-y-2 text-gray-700",children:[e.jsx("p",{className:"font-medium text-sm",children:"Início agudo (minutos/algumas horas) com:"}),e.jsxs("ul",{className:"list-disc pl-4 space-y-2",children:[e.jsx("li",{children:"Comprometimento respiratório (dispneia, sibilos, estridor)"}),e.jsx("li",{children:"Redução de PA ou sinais de choque"}),e.jsx("li",{children:"Dois ou mais sistemas envolvidos:"}),e.jsxs("ul",{className:"list-[circle] pl-6 space-y-1 text-sm",children:[e.jsx("li",{children:"Pele/mucosas"}),e.jsx("li",{children:"Respiratório"}),e.jsx("li",{children:"Cardiovascular"}),e.jsx("li",{children:"Gastrointestinal grave"})]})]})]})]})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[e.jsxs("h3",{className:"font-medium text-blue-800 mb-3 flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5"}),"Avaliação Multissistêmica"]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-blue-700 mb-1",children:"Via Aérea/Respiratório:"}),e.jsxs("ul",{className:"list-disc pl-4 text-sm text-gray-700",children:[e.jsx("li",{children:"Dispneia, sibilos, broncoespasmo"}),e.jsx("li",{children:"Estridor, hipoxemia"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-blue-700 mb-1",children:"Circulação:"}),e.jsxs("ul",{className:"list-disc pl-4 text-sm text-gray-700",children:[e.jsx("li",{children:"Hipotensão"}),e.jsx("li",{children:"Síncope, colapso, incontinência"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-blue-700 mb-1",children:"Pele/Mucosas:"}),e.jsxs("ul",{className:"list-disc pl-4 text-sm text-gray-700",children:[e.jsx("li",{children:"Urticária generalizada"}),e.jsx("li",{children:"Prurido, rubor, angioedema"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-blue-700 mb-1",children:"Gastrointestinal:"}),e.jsxs("ul",{className:"list-disc pl-4 text-sm text-gray-700",children:[e.jsx("li",{children:"Dor abdominal severa"}),e.jsx("li",{children:"Vômitos repetitivos"})]})]})]})]}),e.jsxs("div",{className:"bg-red-50 p-4 rounded-lg border border-red-200",children:[e.jsx("h3",{className:"font-medium text-red-800 mb-3",children:"Reconhecimento Precoce e Intervenção"}),e.jsxs("div",{className:"space-y-2 text-gray-700",children:[e.jsx("p",{children:"Garantir o uso imediato de adrenalina intramuscular e posicionar o paciente adequadamente (posição de Trendelenburg com pernas elevadas)."}),e.jsx("p",{className:"text-sm text-red-600 font-medium",children:"ABCDE primário + posição de Trendelenburg (Elevar Pernas)"})]})]})]})]})}),T=()=>{const[a,r]=s.useState(0),[t,x]=s.useState("initial"),[j,v]=s.useState({}),[y,k]=s.useState(!1),[w,C]=s.useState(1),[I,A]=s.useState(0),M=b.anaphylaxis,O=e=>{e?x("resolution"):w>=3?x("icu"):(C((e=>e+1)),x("treatment"))},R=e=>{e?(C(1),x("treatment")):x("discharge")},T=()=>a?Math.min(.01*a,.5).toFixed(2):"0";return e.jsxs("div",{className:p.gradientBackground("min-h-screen flex flex-col from-red-100 via-white to-red-50 dark:from-red-950 dark:via-slate-900 dark:to-slate-800"),children:[e.jsx(f,{...M}),e.jsx(u,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-8",children:[e.jsxs(i,{to:"/flowcharts",className:"inline-flex items-center gap-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors mb-8",children:[e.jsx(g,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Fluxogramas"})]}),e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 mb-4",children:e.jsx(S,{className:"w-8 h-8 text-red-600 dark:text-red-400"})}),e.jsx("h1",{className:p.gradientHeading("text-4xl font-bold from-red-600 to-red-400 dark:from-red-400 dark:to-red-300"),children:"Fluxograma de Anafilaxia"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Guia para manejo de anafilaxia em pediatria"})]}),e.jsxs("div",{className:"space-y-6",children:["initial"===t&&e.jsxs("div",{className:"space-y-6",children:[e.jsx(V,{weight:a,onWeightChange:r,onWeightCommit:r,age:I,onAgeChange:A,onAgeCommit:A}),e.jsx(B,{}),e.jsxs(d,{className:p.gradientCard("red",""),children:[e.jsxs(l,{children:[e.jsxs(n,{className:"flex items-center gap-2 text-red-700 dark:text-red-400",children:[e.jsx(c,{className:"h-6 w-6"}),"Diagnóstico Inicial"]}),e.jsx(h,{className:"text-gray-600 dark:text-gray-300",children:"Avaliação inicial de suspeita de anafilaxia"})]}),e.jsx(o,{className:"space-y-6",children:e.jsxs("div",{className:"text-center pt-4",children:[e.jsx("p",{className:"font-medium text-red-700 dark:text-red-400 mb-4",children:"O paciente apresenta pelo menos um dos critérios para anafilaxia?"}),e.jsx("div",{className:"flex justify-center gap-4",children:e.jsx(m,{variant:!0===j.initialSuspicion?"default":"outline",onClick:()=>{return e="initialSuspicion",v((s=>({...s,[e]:true}))),void x("treatment");var e},className:"bg-red-600 hover:bg-red-700 text-white dark:bg-red-700 dark:hover:bg-red-800 dark:text-white",children:"Sim"})})]})})]})]}),"treatment"===t&&e.jsxs(e.Fragment,{children:[e.jsx(F,{weight:a,doseNumber:w,adrenalineDose:T()}),e.jsx(E,{showAdjuvantTherapy:!0,weight:a,age:12*I}),e.jsx(D,{onResolutionResponse:O,onBiphasicResponse:R,showBiphasicQuestion:!1,doseCount:w})]}),("resolution"===t||"monitoring"===t)&&e.jsx(D,{onResolutionResponse:O,onBiphasicResponse:R,showBiphasicQuestion:"resolution"===t}),"discharge"===t&&e.jsx(P,{}),"icu"===t&&e.jsx(F,{weight:a,doseNumber:w,adrenalineDose:T(),requiresICU:!0})]})]})]}),e.jsx(N,{})]})};export{T as default};
