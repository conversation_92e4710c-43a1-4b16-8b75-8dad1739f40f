import{j as e}from"./radix-core-6kBL75b5.js";import{m as a,B as s,R as r,an as l,ad as o,ae as i,af as t,ag as n,ai as c,a7 as d,D as m,e as u,f as p,g as x,l as h,aM as j,aa as v}from"./index-D9amGMlQ.js";import{L as b}from"./router-BAzpOxbo.js";import g from"./Footer-BkFd5qSK.js";import{F as y,a as N}from"./flowchartSEOData-B7-hWfhR.js";import{r as f}from"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./target-Cj27UDYs.js";import"./zap-DULtmWB8.js";import"./book-open-CzUd5kBy.js";import"./star-BlLX_9hT.js";import"./circle-help-DFUIKtE9.js";import"./instagram-CuaDlQAQ.js";const C=({question:r,onAnswer:l})=>e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[e.jsx("div",{className:"p-6 rounded-xl bg-blue-50 border border-blue-200",children:"string"==typeof r?e.jsx("p",{className:"text-lg text-gray-800 whitespace-pre-line",children:r}):r}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(s,{onClick:()=>l(!0),variant:"outline",className:"w-32 border-blue-200 hover:bg-blue-50",children:"Sim"}),e.jsx(s,{onClick:()=>l(!1),variant:"outline",className:"w-32 border-blue-200 hover:bg-blue-50",children:"Não"})]})]}),w=({age:s,hasSevereSymptoms:l,hasModerateSymptoms:o})=>{const i=l?{title:"TC Craniana Recomendada",description:"under2"===s?"13,9% da população tem 4,4% de risco de TCEi.":"14% da população tem 4,3% de risco de TCEi.",color:"blue"}:o?{title:"Risco Moderado - Considerar TC ou Observação",description:"under2"===s?"32,6% da população com 0,9% de risco de TCEi.":"27,7% da população com 0,9% de risco de TCEi.",color:"yellow",factors:["Experiência do médico","Achados múltiplos x isolados","Piora dos sinais ou sintomas após observação na unidade de emergência",..."under2"===s?["Idade da criança < 3 meses"]:[]]}:{title:"TC Não Recomendada",description:"under2"===s?"O risco de TCEi é < 0,02%":"O risco de TCEi é < 0,05%",color:"green"};return e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:e.jsxs(r,{className:`p-6 bg-${i.color}-50 border-${i.color}-200`,children:[e.jsx("h3",{className:`text-xl font-semibold text-${i.color}-800 mb-4`,children:i.title}),e.jsx("p",{className:`text-${i.color}-700 mb-4`,children:i.description}),i.factors&&e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsx("h4",{className:"font-semibold text-gray-700",children:"Fatores a considerar na decisão:"}),e.jsx("ul",{className:"list-disc list-inside space-y-2 text-gray-600",children:i.factors.map(((a,s)=>e.jsx("li",{children:a},s)))})]})]})})},S=()=>{const[a,s]=f.useState({eyeOpening:null,motorResponse:null,verbalResponse:null}),r={eyeOpening:[{value:4,label:"Espontânea"},{value:3,label:"Ao chamado"},{value:2,label:"Estímulo álgico"},{value:1,label:"Não responde"}],motorResponse:[{value:6,label:"Movimentos com propósito"},{value:5,label:"Localiza dor"},{value:4,label:"Retira membros à dor"},{value:3,label:"Flexão anormal (decorticação)"},{value:2,label:"Extensão anormal (descerebração)"},{value:1,label:"Não responde"}],verbalResponse:[{value:5,label:"Lalação, sons próprios da idade"},{value:4,label:"Choro consolável"},{value:3,label:"Choro inconsolável"},{value:2,label:"Grunidos ou gemência à dor"},{value:1,label:"Não responde"}]},d=Object.values(a).reduce(((e,a)=>e+(a||0)),0),m=Object.values(a).every((e=>null!==e));return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{children:"Abertura Ocular"}),e.jsxs(o,{value:a.eyeOpening?.toString()||"",onValueChange:e=>s((a=>({...a,eyeOpening:parseInt(e)}))),children:[e.jsx(i,{children:e.jsx(t,{placeholder:"Selecione uma opção"})}),e.jsx(n,{children:r.eyeOpening.map((a=>e.jsxs(c,{value:a.value.toString(),children:[a.label," (",a.value," pontos)"]},a.value)))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{children:"Melhor Resposta Motora"}),e.jsxs(o,{value:a.motorResponse?.toString()||"",onValueChange:e=>s((a=>({...a,motorResponse:parseInt(e)}))),children:[e.jsx(i,{children:e.jsx(t,{placeholder:"Selecione uma opção"})}),e.jsx(n,{children:r.motorResponse.map((a=>e.jsxs(c,{value:a.value.toString(),children:[a.label," (",a.value," pontos)"]},a.value)))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{children:"Melhor Resposta Verbal"}),e.jsxs(o,{value:a.verbalResponse?.toString()||"",onValueChange:e=>s((a=>({...a,verbalResponse:parseInt(e)}))),children:[e.jsx(i,{children:e.jsx(t,{placeholder:"Selecione uma opção"})}),e.jsx(n,{children:r.verbalResponse.map((a=>e.jsxs(c,{value:a.value.toString(),children:[a.label," (",a.value," pontos)"]},a.value)))})]})]})]}),m&&e.jsx("div",{className:"pt-4 border-t",children:e.jsxs("div",{className:"text-center space-y-2",children:[e.jsxs("div",{className:"text-4xl font-bold text-primary",children:[d," pontos"]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:d<=8?"Trauma grave":d<=13?"Trauma moderado":"Trauma leve"})]})})]})},k=()=>{const[r,l]=f.useState(null),[o,i]=f.useState("age"),[t,n]=f.useState(null),[c,j]=f.useState(null),[v,b]=f.useState(!1);return e.jsxs("div",{className:"space-y-8",children:["age"!==o&&e.jsxs(s,{variant:"ghost",className:"flex items-center gap-2",onClick:()=>{l(null),i("age"),n(null),j(null)},children:[e.jsx(d,{className:"h-4 w-4"}),"Recomeçar"]}),e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"space-y-6",children:(()=>{switch(o){case"age":return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800 text-center",children:"A criança tem menos de 2 anos?"}),e.jsxs("div",{className:"flex justify-center gap-4",children:[e.jsx(s,{onClick:()=>{l("under2"),i("severe")},variant:"outline",className:"w-32",children:"Sim"}),e.jsx(s,{onClick:()=>{l("over2"),i("severe")},variant:"outline",className:"w-32",children:"Não"})]})]});case"severe":return e.jsx(C,{question:"under2"===r?e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"A Escala de Glasgow é ≤ 14, há sinais de alteração mental ou fratura de crânio palpável?"}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[e.jsx(h,{className:"h-4 w-4"}),e.jsx("button",{onClick:()=>b(!0),className:"text-primary hover:underline",children:"Calcular Escala de Glasgow"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{children:"A Escala de Glasgow é ≤ 14, há sinais de alteração mental ou fratura de base de crânio?"}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[e.jsx(h,{className:"h-4 w-4"}),e.jsx("button",{onClick:()=>b(!0),className:"text-primary hover:underline",children:"Calcular Escala de Glasgow"})]})]}),onAnswer:e=>{n(e),i(e?"result":"moderate")}});case"moderate":return e.jsx(C,{question:"under2"===r?"Há um dos seguintes critérios?\n- Hematoma subgaleal em região occipital, parietal ou temporal;\n- História de perda de consciência > 5 segundos;\n- Mecanismo de trauma grave;\n- Criança não está agindo normalmente, segundo os pais.":"Há um dos seguintes critérios?\n- História de perda de consciência;\n- Vômitos;\n- Mecanismo de trauma grave;\n- Cefaleia forte.",onAnswer:e=>{j(e),i("result")}});case"result":return e.jsx(w,{age:r,hasSevereSymptoms:t,hasModerateSymptoms:c})}})()},o),e.jsx("div",{className:"text-sm text-muted-foreground border-t pt-4 mt-8",children:e.jsx("p",{className:"italic",children:"Adaptado de Kuppermann N, Holmes JF, Dayan PS, et al. Identification of children at very low risk of clinicallyimportant brain injuries after head trauma: a prospective cohort study. Lancet 2009; 374 (9696): 1160–70"})}),e.jsx(m,{open:v,onOpenChange:b,children:e.jsxs(u,{className:"sm:max-w-[600px]",children:[e.jsx(p,{children:e.jsx(x,{children:"Calculadora de Glasgow"})}),e.jsx(S,{})]})})]})},R=()=>{const a=N.pecarn;return e.jsxs("div",{className:j.gradientBackground("min-h-screen flex flex-col from-blue-100 via-white to-blue-50 dark:from-blue-950 dark:via-slate-900 dark:to-blue-950"),children:[e.jsx(y,{...a}),e.jsx(v,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsx("div",{className:"w-full flex items-center",children:e.jsx(b,{to:"/flowcharts",children:e.jsx(s,{variant:"ghost",size:"icon",className:"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",children:e.jsx(d,{className:"h-5 w-5"})})})}),e.jsx("h1",{className:j.gradientHeading("text-3xl font-bold from-blue-600 to-blue-800 dark:from-blue-400 dark:to-blue-600"),children:"PECARN - Trauma Craniano"})]}),e.jsx(r,{className:j.card("p-6"),children:e.jsx(k,{})})]})}),e.jsx(g,{})]})};export{R as default};
