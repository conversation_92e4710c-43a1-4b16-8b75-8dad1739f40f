import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{d as a}from"./supabase-vendor-qi_Ptfv-.js";import{c as i,R as r,W as n,Z as t,$ as d,U as o,az as m,aA as c,B as l,aD as u,au as x,D as p,e as h,f as j,g as f}from"./index-CrSshpOb.js";import{T as g,a as v,b as _,c as N,d as w,e as y}from"./table-Dcr0sZW7.js";import{U as b}from"./users-HWMvGbPF.js";import"./query-vendor-B-7l6Nb3.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=i("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);function S({admin:i,onSaved:x}){const p=a.useSupabaseClient(),[h,j]=s.useState([]),[f,g]=s.useState([]),[v,_]=s.useState(!1);return s.useEffect((()=>{g([{id:"admin-users",name:"Administradores"},{id:"blog",name:"Blog"},{id:"categories",name:"Categorias de Medicamentos"},{id:"medications",name:"Medicamentos"},{id:"formulas",name:"Fórmulas"},{id:"dosages",name:"Dosagens"},{id:"instructions",name:"Bulas"},{id:"prescription-categories",name:"Categorias de Prescrição"},{id:"icd10",name:"CID-10"},{id:"growth-curves",name:"Curvas de Crescimento"},{id:"growth-curve-metadata",name:"Dados das Curvas"},{id:"question-import",name:"Importar Questões"},{id:"question-formatting",name:"Formatação de Questões"},{id:"format-themes",name:"Formatar Temas"},{id:"breastfeeding-medications-enhancement",name:"Medicamentos na Amamentação"},{id:"condutas-e-manejos",name:"Condutas e Manejos"},{id:"vaccines",name:"Vacinas"},{id:"dnpm",name:"Marcos DNPM"},{id:"settings",name:"Configurações"},{id:"site-settings",name:"Configurações do Site"}]),j(i.permissions)}),[i]),i.is_super_admin?e.jsx(r,{children:e.jsxs(n,{children:[e.jsx(t,{className:"text-lg",children:"Permissões não editáveis"}),e.jsx(d,{children:"Usuários com papel de Super Admin têm acesso completo a todos os módulos."})]})}):e.jsxs(r,{children:[e.jsxs(n,{children:[e.jsxs(t,{className:"text-lg",children:["Gerenciar Permissões: ",i.full_name]}),e.jsx(d,{children:"Selecione os módulos que este administrador poderá acessar"})]}),e.jsx(o,{className:"space-y-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:f.map((s=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(m,{id:`module-${s.id}`,checked:h.includes(s.id),onCheckedChange:()=>{return e=s.id,void j((s=>s.includes(e)?s.filter((s=>s!==e)):[...s,e]));var e}}),e.jsx("label",{htmlFor:`module-${s.id}`,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer",children:s.name})]},s.id)))})}),e.jsxs(c,{className:"flex justify-end gap-2",children:[e.jsx(l,{variant:"outline",onClick:()=>x(),children:"Cancelar"}),e.jsx(l,{disabled:v,onClick:async()=>{try{_(!0);const{error:e}=await p.from("admin_user_permissions").delete().eq("user_id",i.id);if(e)throw e;if(0===h.length)return u({title:"Permissões atualizadas",description:"O administrador agora não tem acesso a nenhum módulo."}),void x();const s=h.map((e=>({user_id:i.id,resource:e}))),{error:a}=await p.from("admin_user_permissions").insert(s);if(a)throw a;u({title:"Permissões atualizadas",description:"As permissões do administrador foram atualizadas com sucesso."}),x()}catch(e){u({variant:"destructive",title:"Erro ao salvar permissões",description:e.message})}finally{_(!1)}},children:v?"Salvando...":"Salvar Permissões"})]})]})}function A(){const i=a.useSupabaseClient(),[m,c]=s.useState([]),[A,k]=s.useState(!0),[P,q]=s.useState(null),[M,z]=s.useState(!1);s.useEffect((()=>{D()}),[]);const D=async()=>{try{k(!0);const{data:e,error:s}=await i.from("secure_profiles").select("id, full_name, is_admin").eq("is_admin",!0).order("full_name",{ascending:!0});if(s)throw s;const{data:a,error:r}=await i.from("admin_roles").select("id, name").eq("name","super_admin").maybeSingle();if(r)throw r;const n=a?.id,t=await Promise.all(e.map((async e=>{const{data:s,error:a}=await i.from("admin_user_roles").select("role_id").eq("user_id",e.id);if(a)throw a;const{data:r,error:t}=await i.from("admin_user_permissions").select("resource").eq("user_id",e.id).order("resource",{ascending:!0});if(t)throw t;const d=s.some((e=>e.role_id===n));return{...e,is_super_admin:d,permissions:r?.map((e=>e.resource))||[]}})));c(t)}catch(e){u({variant:"destructive",title:"Erro ao carregar administradores",description:e.message})}finally{k(!1)}},F=s=>s.is_super_admin?e.jsxs("div",{className:"flex items-center gap-1 text-amber-600 font-semibold",children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{children:"Super Admin"})]}):e.jsxs("div",{className:"flex items-center gap-1 text-blue-600",children:[e.jsx(x,{className:"h-4 w-4"}),e.jsx("span",{children:"Admin"})]});return e.jsxs("div",{className:"container mx-auto py-6",children:[e.jsxs(r,{children:[e.jsx(n,{className:"bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsxs(t,{className:"text-2xl font-bold flex items-center gap-2",children:[e.jsx(b,{className:"h-6 w-6 text-primary"}),"Gerenciamento de Administradores"]}),e.jsx(d,{className:"text-base mt-2",children:"Gerencie usuários administrativos e suas permissões no sistema"})]})})}),e.jsx(o,{className:"pt-6",children:A?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):e.jsxs(g,{children:[e.jsx(v,{children:e.jsxs(_,{children:[e.jsx(N,{className:"w-[250px]",children:"Nome"}),e.jsx(N,{children:"Cargo"}),e.jsx(N,{className:"text-right",children:"Ações"})]})}),e.jsx(w,{children:m.map((s=>e.jsxs(_,{children:[e.jsx(y,{className:"font-medium",children:s.full_name||"Sem nome"}),e.jsx(y,{children:F(s)}),e.jsx(y,{className:"text-right",children:e.jsx(l,{variant:"ghost",className:"gap-1",onClick:()=>(e=>{q(e),z(!0)})(s),disabled:s.is_super_admin,children:s.is_super_admin?e.jsxs(e.Fragment,{children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{children:"Acesso Total"})]}):e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-4 w-4"}),e.jsx("span",{children:"Permissões"})]})})})]},s.id)))})]})})]}),e.jsx(p,{open:M,onOpenChange:z,children:e.jsxs(h,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[e.jsx(j,{children:e.jsxs(f,{className:"flex items-center gap-2 text-xl",children:[e.jsx(x,{className:"h-5 w-5 text-primary"}),"Permissões do Administrador"]})}),P&&e.jsx(S,{admin:P,onSaved:()=>{D(),z(!1)}})]})})]})}export{A as default};
