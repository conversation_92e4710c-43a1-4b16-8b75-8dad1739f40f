import{c as e,t,o as n,x as r,y as i,z as o,A as l,B as s,v as a,C as c,E as u,D as f,F as h,G as d,H as p,I as m,J as g,K as k,L as b,N as x,O as v,P as y}from"./markdown-vendor-C57yw7YK.js";function C(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}function w(e){return"function"==typeof e?e:function(){return e}}const F="phrasing",D=["autolink","link","image","label"];function A(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function L(e){this.config.enter.autolinkProtocol.call(this,e)}function S(e){this.config.exit.autolinkProtocol.call(this,e)}function M(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];n("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function E(e){this.config.exit.autolinkEmail.call(this,e)}function O(e){this.exit(e)}function z(n){!function(n,r,i){const o=e((i||{}).ignore||[]),l=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const e=n[r];t.push([(i=e[0],"string"==typeof i?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i),w(e[1])])}var i;return t}(r);let s=-1;for(;++s<l.length;)t(n,"text",a);function a(e,t){let n,r=-1;for(;++r<t.length;){const e=t[r],i=n?n.children:void 0;if(o(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],r=l[s][0],i=l[s][1];let o=0;const a=n.children.indexOf(e);let c=!1,u=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){const n=f.index,l={index:f.index,input:f.input,stack:[...t,e]};let s=i(...f,l);if("string"==typeof s&&(s=s.length>0?{type:"text",value:s}:void 0),!1===s?r.lastIndex=n+1:(o!==n&&u.push({type:"text",value:e.value.slice(o,n)}),Array.isArray(s)?u.push(...s):s&&u.push(s),o=n+f[0].length,c=!0),!r.global)break;f=r.exec(e.value)}return c?(o<e.value.length&&u.push({type:"text",value:e.value.slice(o)}),n.children.splice(a,1,...u)):u=[e],a+u.length}(e,t)}}(n,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,I],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,T]],{ignore:["link","linkReference"]})}function I(e,t,n,r,i){let o="";if(!R(i))return!1;if(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){const t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n))return!1;const l=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const i=C(e,"(");let o=C(e,")");for(;-1!==r&&i>o;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),o++;return[e,n]}(n+r);if(!l[0])return!1;const s={type:"link",title:null,url:o+t+l[0],children:[{type:"text",value:t+l[0]}]};return l[1]?[s,{type:"text",value:l[1]}]:s}function T(e,t,n,r){return!(!R(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function R(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||r(n)||i(n))&&(!t||47!==n)}function j(){this.buffer()}function P(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function _(){this.buffer()}function B(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function $(e){const t=this.resume(),r=this.stack[this.stack.length-1];n("footnoteReference"===r.type),r.identifier=o(this.sliceSerialize(e)).toLowerCase(),r.label=t}function H(e){this.exit(e)}function q(e){const t=this.resume(),r=this.stack[this.stack.length-1];n("footnoteDefinition"===r.type),r.identifier=o(this.sliceSerialize(e)).toLowerCase(),r.label=t}function W(e){this.exit(e)}function U(e,t,n,r){const i=n.createTracker(r);let o=i.move("[^");const l=n.enter("footnoteReference"),s=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{after:"]",before:o})),s(),l(),o+=i.move("]"),o}function V(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){const o=r.createTracker(i);let l=o.move("[^");const s=r.enter("footnoteDefinition"),a=r.enter("label");return l+=o.move(r.safe(r.associationId(e),{before:l,after:"]"})),a(),l+=o.move("]:"),e.children&&e.children.length>0&&(o.shift(4),l+=o.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,o.current()),t?G:Q))),s(),l},footnoteReference:U},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}}function Q(e,t,n){return 0===t?e:G(e,0,n)}function G(e,t,n){return(n?"":"    ")+e}U.peek=function(){return"["};const J=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function Z(e){this.enter({type:"delete",children:[]},e)}function K(e){this.exit(e)}function N(e,t,n,r){const i=n.createTracker(r),o=n.enter("strikethrough");let l=i.move("~~");return l+=n.containerPhrasing(e,{...i.current(),before:l,after:"~"}),l+=i.move("~~"),o(),l}function X(e){return e.length}function Y(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function ee(e,t,n){return">"+(n?"":" ")+e}function te(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function ne(e,t,n,r){let i=-1;for(;++i<n.unsafe.length;)if("\n"===n.unsafe[i].character&&(te(o=n.stack,(l=n.unsafe[i]).inConstruct,!0)&&!te(o,l.notInConstruct,!1)))return/[ \t]/.test(r.before)?"":" ";var o,l;return"\\\n"}function re(e,t,n){return(n?"":"    ")+e}function ie(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function oe(e){return"&#x"+e.toString(16).toUpperCase()+";"}function le(e,t,n){const r=s(e),i=s(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function se(e,t,n,r){const i=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),o=n.enter("emphasis"),l=n.createTracker(r),s=l.move(i);let a=l.move(n.containerPhrasing(e,{after:i,before:s,...l.current()}));const c=a.charCodeAt(0),u=le(r.before.charCodeAt(r.before.length-1),c,i);u.inside&&(a=oe(c)+a.slice(1));const f=a.charCodeAt(a.length-1),h=le(r.after.charCodeAt(0),f,i);h.inside&&(a=a.slice(0,-1)+oe(f));const d=l.move(i);return o(),n.attentionEncodeSurroundingInfo={after:h.outside,before:u.outside},s+a+d}function ae(e){return e.value||""}function ce(e,t,n,r){const i=ie(n),o='"'===i?"Quote":"Apostrophe",l=n.enter("image");let s=n.enter("label");const a=n.createTracker(r);let c=a.move("![");return c+=a.move(n.safe(e.alt,{before:c,after:"]",...a.current()})),c+=a.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(s=n.enter("destinationRaw"),c+=a.move(n.safe(e.url,{before:c,after:e.title?" ":")",...a.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=a.move(" "+i),c+=a.move(n.safe(e.title,{before:c,after:i,...a.current()})),c+=a.move(i),s()),c+=a.move(")"),l(),c}function ue(e,t,n,r){const i=e.referenceType,o=n.enter("imageReference");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("![");const c=n.safe(e.alt,{before:a,after:"]",...s.current()});a+=s.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...s.current()});return l(),n.stack=u,o(),"full"!==i&&c&&c===f?"shortcut"===i?a=a.slice(0,-1):a+=s.move("]"):a+=s.move(f+"]"),a}function fe(e,t,n){let r=e.value||"",i="`",o=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){const e=n.unsafe[o],t=n.compilePattern(e);let i;if(e.atBreak)for(;i=t.exec(r);){let e=i.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(i.index+1)}}return i+r+i}function he(e,t){const n=c(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function de(e,t,n,r){const i=ie(n),o='"'===i?"Quote":"Apostrophe",l=n.createTracker(r);let s,a;if(he(e,n)){const t=n.stack;n.stack=[],s=n.enter("autolink");let r=l.move("<");return r+=l.move(n.containerPhrasing(e,{before:r,after:">",...l.current()})),r+=l.move(">"),s(),n.stack=t,r}s=n.enter("link"),a=n.enter("label");let c=l.move("[");return c+=l.move(n.containerPhrasing(e,{before:c,after:"](",...l.current()})),c+=l.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(a=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),a(),e.title&&(a=n.enter(`title${o}`),c+=l.move(" "+i),c+=l.move(n.safe(e.title,{before:c,after:i,...l.current()})),c+=l.move(i),a()),c+=l.move(")"),s(),c}function pe(e,t,n,r){const i=e.referenceType,o=n.enter("linkReference");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("[");const c=n.containerPhrasing(e,{before:a,after:"]",...s.current()});a+=s.move(c+"]["),l();const u=n.stack;n.stack=[],l=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...s.current()});return l(),n.stack=u,o(),"full"!==i&&c&&c===f?"shortcut"===i?a=a.slice(0,-1):a+=s.move("]"):a+=s.move(f+"]"),a}function me(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function ge(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}N.peek=function(){return"~"},se.peek=function(e,t,n){return n.options.emphasis||"*"},ae.peek=function(){return"<"},ce.peek=function(){return"!"},ue.peek=function(){return"!"},fe.peek=function(){return"`"},de.peek=function(e,t,n){return he(e,n)?"<":"["},pe.peek=function(){return"["};const ke=e(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function be(e,t,n,r){const i=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),o=n.enter("strong"),l=n.createTracker(r),s=l.move(i+i);let a=l.move(n.containerPhrasing(e,{after:i,before:s,...l.current()}));const c=a.charCodeAt(0),u=le(r.before.charCodeAt(r.before.length-1),c,i);u.inside&&(a=oe(c)+a.slice(1));const f=a.charCodeAt(a.length-1),h=le(r.after.charCodeAt(0),f,i);h.inside&&(a=a.slice(0,-1)+oe(f));const d=l.move(i+i);return o(),n.attentionEncodeSurroundingInfo={after:h.outside,before:u.outside},s+a+d}be.peek=function(e,t,n){return n.options.strong||"*"};const xe={blockquote:function(e,t,n,r){const i=n.enter("blockquote"),o=n.createTracker(r);o.move("> "),o.shift(2);const l=n.indentLines(n.containerFlow(e,o.current()),ee);return i(),l},break:ne,code:function(e,t,n,r){const i=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),o=e.value||"",s="`"===i?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(o,re);return e(),t}const a=n.createTracker(r),c=i.repeat(Math.max(l(o,i)+1,3)),u=n.enter("codeFenced");let f=a.move(c);if(e.lang){const t=n.enter(`codeFencedLang${s}`);f+=a.move(n.safe(e.lang,{before:f,after:" ",encode:["`"],...a.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${s}`);f+=a.move(" "),f+=a.move(n.safe(e.meta,{before:f,after:"\n",encode:["`"],...a.current()})),t()}return f+=a.move("\n"),o&&(f+=a.move(o+"\n")),f+=a.move(c),u(),f},definition:function(e,t,n,r){const i=ie(n),o='"'===i?"Quote":"Apostrophe",l=n.enter("definition");let s=n.enter("label");const a=n.createTracker(r);let c=a.move("[");return c+=a.move(n.safe(n.associationId(e),{before:c,after:"]",...a.current()})),c+=a.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(s=n.enter("destinationRaw"),c+=a.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...a.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=a.move(" "+i),c+=a.move(n.safe(e.title,{before:c,after:i,...a.current()})),c+=a.move(i),s()),l(),c},emphasis:se,hardBreak:ne,heading:function(e,t,n,r){const i=Math.max(Math.min(6,e.depth||1),1),o=n.createTracker(r);if(function(e,t){let n=!1;return a(e,(function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,u})),Boolean((!e.depth||e.depth<3)&&c(e)&&(t.options.setext||n))}(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),l=n.containerPhrasing(e,{...o.current(),before:"\n",after:"\n"});return r(),t(),l+"\n"+(1===i?"=":"-").repeat(l.length-(Math.max(l.lastIndexOf("\r"),l.lastIndexOf("\n"))+1))}const l="#".repeat(i),s=n.enter("headingAtx"),f=n.enter("phrasing");o.move(l+" ");let h=n.containerPhrasing(e,{before:"# ",after:"\n",...o.current()});return/^[\t ]/.test(h)&&(h=oe(h.charCodeAt(0))+h.slice(1)),h=h?l+" "+h:l,n.options.closeAtx&&(h+=" "+l),f(),s(),h},html:ae,image:ce,imageReference:ue,inlineCode:fe,link:de,linkReference:pe,list:function(e,t,n,r){const i=n.enter("list"),o=n.bulletCurrent;let l=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):me(n);const s=e.ordered?"."===l?")":".":function(e){const t=me(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let a=!(!t||!n.bulletLastUsed)&&l===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==l&&"-"!==l||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(a=!0),ge(n)===l&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){a=!0;break}}}}a&&(l=s),n.bulletCurrent=l;const c=n.containerFlow(e,r);return n.bulletLastUsed=l,n.bulletCurrent=o,i(),c},listItem:function(e,t,n,r){const i=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let o=n.bulletCurrent||me(n);t&&"list"===t.type&&t.ordered&&(o=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+o);let l=o.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(l=4*Math.ceil(l/4));const s=n.createTracker(r);s.move(o+" ".repeat(l-o.length)),s.shift(l);const a=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,s.current()),(function(e,t,n){return t?(n?"":" ".repeat(l))+e:(n?o:o+" ".repeat(l-o.length))+e}));return a(),c},paragraph:function(e,t,n,r){const i=n.enter("paragraph"),o=n.enter("phrasing"),l=n.containerPhrasing(e,r);return o(),i(),l},root:function(e,t,n,r){return(e.children.some((function(e){return ke(e)}))?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:be,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(ge(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function ve(e){const t=e._align;this.enter({type:"table",align:t.map((function(e){return"none"===e?null:e})),children:[]},e),this.data.inTable=!0}function ye(e){this.exit(e),this.data.inTable=void 0}function Ce(e){this.enter({type:"tableRow",children:[]},e)}function we(e){this.exit(e)}function Fe(e){this.enter({type:"tableCell",children:[]},e)}function De(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,Ae));const r=this.stack[this.stack.length-1];n("inlineCode"===r.type),r.value=t,this.exit(e)}function Ae(e,t){return"|"===t?t:e}function Le(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=xe.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return s(function(e,t,n){const r=e.children;let i=-1;const o=[],l=t.enter("table");for(;++i<r.length;)o[i]=a(r[i],t,n);return l(),o}(e,n,r),e.align)},tableCell:l,tableRow:function(e,t,n,r){const i=s([a(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function l(e,t,n,r){const i=n.enter("tableCell"),l=n.enter("phrasing"),s=n.containerPhrasing(e,{...r,before:o,after:o});return l(),i(),s}function s(e,t){return function(e,t){const n=t||{},r=(n.align||[]).concat(),i=n.stringLength||X,o=[],l=[],s=[],a=[];let c=0,u=-1;for(;++u<e.length;){const t=[],r=[];let o=-1;for(e[u].length>c&&(c=e[u].length);++o<e[u].length;){const l=null==(f=e[u][o])?"":String(f);if(!1!==n.alignDelimiters){const e=i(l);r[o]=e,(void 0===a[o]||e>a[o])&&(a[o]=e)}t.push(l)}l[u]=t,s[u]=r}var f;let h=-1;if("object"==typeof r&&"length"in r)for(;++h<c;)o[h]=Y(r[h]);else{const e=Y(r);for(;++h<c;)o[h]=e}h=-1;const d=[],p=[];for(;++h<c;){const e=o[h];let t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,a[h]-t.length-r.length);const l=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&(i=t.length+i+r.length,i>a[h]&&(a[h]=i),p[h]=i),d[h]=l}l.splice(1,0,d),s.splice(1,0,p),u=-1;const m=[];for(;++u<l.length;){const e=l[u],t=s[u];h=-1;const r=[];for(;++h<c;){const i=e[h]||"";let l="",s="";if(!1!==n.alignDelimiters){const e=a[h]-(t[h]||0),n=o[h];114===n?l=" ".repeat(e):99===n?e%2?(l=" ".repeat(e/2+.5),s=" ".repeat(e/2-.5)):(l=" ".repeat(e/2),s=l):s=" ".repeat(e)}!1===n.delimiterStart||h||r.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===i||!1===n.delimiterStart&&!h||r.push(" "),!1!==n.alignDelimiters&&r.push(l),r.push(i),!1!==n.alignDelimiters&&r.push(s),!1!==n.padding&&r.push(" "),!1===n.delimiterEnd&&h===c-1||r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function a(e,t,n){const r=e.children;let i=-1;const o=[],s=t.enter("tableRow");for(;++i<r.length;)o[i]=l(r[i],0,t,n);return s(),o}}function Se(e){const t=this.stack[this.stack.length-2];n("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function Me(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];n("paragraph"===e.type);const r=e.children[0];if(r&&"text"===r.type){const n=t.children;let i,o=-1;for(;++o<n.length;){const e=n[o];if("paragraph"===e.type){i=e;break}}i===e&&(r.value=r.value.slice(1),0===r.value.length?e.children.shift():e.position&&r.position&&"number"==typeof r.position.start.offset&&(r.position.start.column++,r.position.start.offset++,e.position.start=Object.assign({},r.position.start)))}}this.exit(e)}function Ee(e,t,n,r){const i=e.children[0],o="boolean"==typeof e.checked&&i&&"paragraph"===i.type,l="["+(e.checked?"x":" ")+"] ",s=n.createTracker(r);o&&s.move(l);let a=xe.listItem(e,t,n,{...r,...s.current()});return o&&(a=a.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,(function(e){return e+l}))),a}const Oe={tokenize:function(e,t,n){let r=0;return function t(o){return(87===o||119===o)&&r<3?(r++,e.consume(o),t):46===o&&3===r?(e.consume(o),i):n(o)};function i(e){return null===e?n(e):t(e)}},partial:!0},ze={tokenize:function(e,t,n){let o,l,s;return a;function a(t){return 46===t||95===t?e.check(Te,u,c)(t):null===t||d(t)||r(t)||45!==t&&i(t)?u(t):(s=!0,e.consume(t),a)}function c(t){return 95===t?o=!0:(l=o,o=void 0),e.consume(t),a}function u(e){return l||o||!s?n(e):t(e)}},partial:!0},Ie={tokenize:function(e,t){let n=0,i=0;return o;function o(s){return 40===s?(n++,e.consume(s),o):41===s&&i<n?l(s):33===s||34===s||38===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||60===s||63===s||93===s||95===s||126===s?e.check(Te,t,l)(s):null===s||d(s)||r(s)?t(s):(e.consume(s),o)}function l(t){return 41===t&&i++,e.consume(t),o}},partial:!0},Te={tokenize:function(e,t,n){return i;function i(s){return 33===s||34===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||63===s||95===s||126===s?(e.consume(s),i):38===s?(e.consume(s),l):93===s?(e.consume(s),o):60===s||null===s||d(s)||r(s)?t(s):n(s)}function o(e){return null===e||40===e||91===e||d(e)||r(e)?t(e):i(e)}function l(e){return h(e)?s(e):n(e)}function s(t){return 59===t?(e.consume(t),i):h(t)?(e.consume(t),s):n(t)}},partial:!0},Re={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return f(e)?n(e):t(e)}},partial:!0},je={name:"wwwAutolink",tokenize:function(e,t,n){const r=this;return function(t){return 87!==t&&119!==t||!He.call(r,r.previous)||Ve(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(Oe,e.attempt(ze,e.attempt(Ie,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:He},Pe={name:"protocolAutolink",tokenize:function(e,t,n){const o=this;let l="",s=!1;return function(t){return 72!==t&&104!==t||!qe.call(o,o.previous)||Ve(o.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),l+=String.fromCodePoint(t),e.consume(t),a)};function a(t){if(h(t)&&l.length<5)return l+=String.fromCodePoint(t),e.consume(t),a;if(58===t){const n=l.toLowerCase();if("http"===n||"https"===n)return e.consume(t),c}return n(t)}function c(t){return 47===t?(e.consume(t),s?u:(s=!0,c)):n(t)}function u(t){return null===t||p(t)||d(t)||r(t)||i(t)?n(t):e.attempt(ze,e.attempt(Ie,f),n)(t)}function f(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:qe},_e={name:"emailAutolink",tokenize:function(e,t,n){const r=this;let i,o;return function(t){return Ue(t)&&We.call(r,r.previous)&&!Ve(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),l(t)):n(t)};function l(t){return Ue(t)?(e.consume(t),l):64===t?(e.consume(t),s):n(t)}function s(t){return 46===t?e.check(Re,c,a)(t):45===t||95===t||f(t)?(o=!0,e.consume(t),s):c(t)}function a(t){return e.consume(t),i=!0,s}function c(l){return o&&i&&h(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(l)):n(l)}},previous:We},Be={};let $e=48;for(;$e<123;)Be[$e]=_e,$e++,58===$e?$e=65:91===$e&&($e=97);function He(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||d(e)}function qe(e){return!h(e)}function We(e){return!(47===e||Ue(e))}function Ue(e){return 43===e||45===e||46===e||95===e||f(e)}function Ve(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}Be[43]=_e,Be[45]=_e,Be[46]=_e,Be[95]=_e,Be[72]=[_e,Pe],Be[104]=[_e,Pe],Be[87]=[_e,je],Be[119]=[_e,je];const Qe={tokenize:function(e,t,n){const r=this;return g(e,(function(e){const i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)}),"gfmFootnoteDefinitionIndent",5)},partial:!0};function Ge(e,t,n){const r=this;let i=r.events.length;const l=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let s;for(;i--;){const e=r.events[i][1];if("labelImage"===e.type){s=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(i){if(!s||!s._balanced)return n(i);const a=o(r.sliceSerialize({start:s.end,end:r.now()}));return 94===a.codePointAt(0)&&l.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(i),e.exit("gfmFootnoteCallLabelMarker"),t(i)):n(i)}}function Je(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const o={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},l={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},s=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",o,t],["enter",l,t],["exit",l,t],["exit",o,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...s),e}function Ze(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l,s=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),a};function a(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",c)}function c(a){if(s>999||93===a&&!l||null===a||91===a||d(a))return n(a);if(93===a){e.exit("chunkString");const l=e.exit("gfmFootnoteCallString");return i.includes(o(r.sliceSerialize(l)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(a)}return d(a)||(l=!0),s++,e.consume(a),92===a?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function Ke(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let l,s,a=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),c};function c(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",u):n(t)}function u(t){if(a>999||93===t&&!s||null===t||91===t||d(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return l=o(r.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),h}return d(t)||(s=!0),a++,e.consume(t),92===t?f:u}function f(t){return 91===t||92===t||93===t?(e.consume(t),a++,u):u(t)}function h(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),i.includes(l)||i.push(l),g(e,p,"gfmFootnoteDefinitionWhitespace")):n(t)}function p(e){return t(e)}}function Ne(e,t,n){return e.check(m,t,e.attempt(Qe,t,n))}function Xe(e){e.exit("gfmFootnoteDefinition")}function Ye(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,r){const i=this.previous,o=this.events;let l=0;return function(t){return 126===i&&"characterEscape"!==o[o.length-1][1].type?r(t):(e.enter("strikethroughSequenceTemporary"),a(t))};function a(o){const c=s(i);if(126===o)return l>1?r(o):(e.consume(o),l++,a);if(l<2&&!t)return r(o);const u=e.exit("strikethroughSequenceTemporary"),f=s(o);return u._open=!f||2===f&&Boolean(c),u._close=!c||2===c&&Boolean(f),n(o)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset===e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";const i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},o={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},l=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",o,t]],s=t.parser.constructs.insideSpan.null;s&&k(l,l.length,0,b(s,e.slice(r+1,n),t)),k(l,l.length,0,[["exit",o,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),k(e,r-1,n-r+3,l),n=r+l.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}class et{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t)return e.map[i][1]+=n,void e.map[i][2].push(...r);i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort((function(e,t){return e[0]-t[0]})),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(const t of r)e.push(t);r=n.pop()}this.map.length=0}}function tt(e,t){let n=!1;const r=[];for(;t<e.length;){const i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}function nt(e,t,n){const r=this;let i,o=0,l=0;return function(e){let t=r.events.length-1;for(;t>-1;){const e=r.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const i=t>-1?r.events[t][1].type:null,o="tableHead"===i||"tableRow"===i?F:s;return o===F&&r.parser.lazy[r.now().line]?n(e):o(e)};function s(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){return 124===e||(i=!0,l+=1),a(e)}(t)}function a(t){return null===t?n(t):x(t)?l>1?(l=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),f):n(t):v(t)?g(e,a,"whitespace")(t):(l+=1,i&&(i=!1,o+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),i=!0,a):(e.enter("data"),c(t)))}function c(t){return null===t||124===t||d(t)?(e.exit("data"),a(t)):(e.consume(t),92===t?u:c)}function u(t){return 92===t||124===t?(e.consume(t),c):c(t)}function f(t){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(t):(e.enter("tableDelimiterRow"),i=!1,v(t)?g(e,h,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):h(t))}function h(t){return 45===t||58===t?m(t):124===t?(i=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):w(t)}function p(t){return v(t)?g(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(l+=1,i=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),k):45===t?(l+=1,k(t)):null===t||x(t)?C(t):w(t)}function k(t){return 45===t?(e.enter("tableDelimiterFiller"),b(t)):w(t)}function b(t){return 45===t?(e.consume(t),b):58===t?(i=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(t))}function y(t){return v(t)?g(e,C,"whitespace")(t):C(t)}function C(n){return 124===n?h(n):(null===n||x(n))&&i&&o===l?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):w(n)}function w(e){return n(e)}function F(t){return e.enter("tableRow"),D(t)}function D(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),D):null===n||x(n)?(e.exit("tableRow"),t(n)):v(n)?g(e,D,"whitespace")(n):(e.enter("data"),A(n))}function A(t){return null===t||124===t||d(t)?(e.exit("data"),D(t)):(e.consume(t),92===t?L:A)}function L(t){return 92===t||124===t?(e.consume(t),A):A(t)}}function rt(e,t){let n,r,i,o=-1,l=!0,s=0,a=[0,0,0,0],c=[0,0,0,0],u=!1,f=0;const h=new et;for(;++o<e.length;){const d=e[o],p=d[1];"enter"===d[0]?"tableHead"===p.type?(u=!1,0!==f&&(ot(h,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},p.start),end:Object.assign({},p.end)},h.add(o,0,[["enter",n,t]])):"tableRow"===p.type||"tableDelimiterRow"===p.type?(l=!0,i=void 0,a=[0,0,0,0],c=[0,o+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},p.start),end:Object.assign({},p.end)},h.add(o,0,[["enter",r,t]])),s="tableDelimiterRow"===p.type?2:r?3:1):!s||"data"!==p.type&&"tableDelimiterMarker"!==p.type&&"tableDelimiterFiller"!==p.type?"tableCellDivider"===p.type&&(l?l=!1:(0!==a[1]&&(c[0]=c[1],i=it(h,t,a,s,void 0,i)),a=c,c=[a[1],o,0,0])):(l=!1,0===c[2]&&(0!==a[1]&&(c[0]=c[1],i=it(h,t,a,s,void 0,i),a=[0,0,0,0]),c[2]=o)):"tableHead"===p.type?(u=!0,f=o):"tableRow"===p.type||"tableDelimiterRow"===p.type?(f=o,0!==a[1]?(c[0]=c[1],i=it(h,t,a,s,o,i)):0!==c[1]&&(i=it(h,t,c,s,o,i)),s=0):!s||"data"!==p.type&&"tableDelimiterMarker"!==p.type&&"tableDelimiterFiller"!==p.type||(c[3]=o)}for(0!==f&&ot(h,t,f,n,r),h.consume(t.events),o=-1;++o<t.events.length;){const e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=tt(t.events,o))}return e}function it(e,t,n,r,i,o){const l=1===r?"tableHeader":2===r?"tableDelimiter":"tableData";0!==n[0]&&(o.end=Object.assign({},lt(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));const s=lt(t.events,n[1]);if(o={type:l,start:Object.assign({},s),end:Object.assign({},s)},e.add(n[1],0,[["enter",o,t]]),0!==n[2]){const i=lt(t.events,n[2]),o=lt(t.events,n[3]),l={type:"tableContent",start:Object.assign({},i),end:Object.assign({},o)};if(e.add(n[2],0,[["enter",l,t]]),2!==r){const r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",l,t]])}return void 0!==i&&(o.end=Object.assign({},lt(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function ot(e,t,n,r,i){const o=[],l=lt(t.events,n);i&&(i.end=Object.assign({},l),o.push(["exit",i,t])),r.end=Object.assign({},l),o.push(["exit",r,t]),e.add(n+1,0,o)}function lt(e,t){const n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}const st={name:"tasklistCheck",tokenize:function(e,t,n){const r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return d(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),o):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),o):n(t)}function o(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),l):n(t)}function l(r){return x(r)?t(r):v(r)?e.check({tokenize:at},t,n)(r):n(r)}}};function at(e,t,n){return g(e,(function(e){return null===e?n(e):t(e)}),"whitespace")}const ct={};function ut(e){const t=e||ct,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),i=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),o=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push(function(e){return y([{text:Be},{document:{91:{name:"gfmFootnoteDefinition",tokenize:Ke,continuation:{tokenize:Ne},exit:Xe}},text:{91:{name:"gfmFootnoteCall",tokenize:Ze},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:Ge,resolveTo:Je}}},Ye(e),{flow:{null:{name:"table",tokenize:nt,resolveAll:rt}}},{text:{91:st}}])}(t)),i.push([{transforms:[z],enter:{literalAutolink:A,literalAutolinkEmail:L,literalAutolinkHttp:L,literalAutolinkWww:L},exit:{literalAutolink:O,literalAutolinkEmail:E,literalAutolinkHttp:S,literalAutolinkWww:M}},{enter:{gfmFootnoteCallString:j,gfmFootnoteCall:P,gfmFootnoteDefinitionLabelString:_,gfmFootnoteDefinition:B},exit:{gfmFootnoteCallString:$,gfmFootnoteCall:H,gfmFootnoteDefinitionLabelString:q,gfmFootnoteDefinition:W}},{canContainEols:["delete"],enter:{strikethrough:Z},exit:{strikethrough:K}},{enter:{table:ve,tableData:Fe,tableHeader:Fe,tableRow:Ce},exit:{codeText:De,table:ye,tableData:we,tableHeader:we,tableRow:we}},{exit:{taskListCheckValueChecked:Se,taskListCheckValueUnchecked:Se,paragraph:Me}}]),o.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:F,notInConstruct:D},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:F,notInConstruct:D},{character:":",before:"[ps]",after:"\\/",inConstruct:F,notInConstruct:D}]},V(e),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:J}],handlers:{delete:N}},Le(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Ee}}]}}(t))}export{C as c,ut as r};
