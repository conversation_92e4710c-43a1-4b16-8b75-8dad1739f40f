import{j as e}from"./radix-core-6kBL75b5.js";import{b as a,r as s}from"./critical-DVX9Inzy.js";import{c as t,R as r,D as o,e as n,f as i,g as l,a5 as c,B as d,z as m,aC as x,s as h,aK as u,ad as p,ae as g,af as f,ag as j,ai as y,d as v,ay as w,aw as N,Y as b,X as k,an as C,ac as _,a8 as S,aa as T}from"./index-DV3Span9.js";import F from"./Footer-DaWfQ4Sj.js";import{a as E,u as q,c as z}from"./query-vendor-B-7l6Nb3.js";import{J as O}from"./index-k36xMONP.js";import{T as A}from"./trash-2-BQ8b_n_c.js";import{F as I}from"./folder-D0G9NrF5.js";import{P as D}from"./plus-CG1D5Wcu.js";import{S as M}from"./star-CdRVV6QC.js";import{C as P}from"./calendar-BTLyX4dA.js";import{B as L}from"./bookmark-CkGGQ4bS.js";import{C as K}from"./copy-DciFRdJd.js";import{a as Q}from"./date-vendor-BOcTQe0E.js";import{p as U}from"./pt-BR-a_BmBHfW.js";import{u as B,S as V,U as H,E as $}from"./editor-vendor-0G6QaH11.js";import{T as W,a as R,b as Y,c as J}from"./tooltip-OePCkPEK.js";import{W as X}from"./wand-sparkles-CnDirEu6.js";import{T as G}from"./timer-DsNvRDa0.js";import{B as Z,I as ee}from"./italic-BI53e-0U.js";import{U as ae}from"./underline-BUYOlcOF.js";import{T as se}from"./tag-BZQ2diYL.js";import{a as te}from"./router-BAzpOxbo.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-OUR0fGD_.js";import"./rocket-DWbYpez3.js";import"./target-qP-p0xEy.js";import"./zap-xHziMQfW.js";import"./book-open-Ca6sFj94.js";import"./circle-help-psGgvKcv.js";import"./instagram-BdLPZF9i.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=t("PencilLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}],["path",{d:"m15 5 3 3",key:"1w25hb"}]]),oe=({id:a,name:s,onSelect:t,onEdit:o,onDelete:n})=>e.jsxs(r,{className:"p-6 flex flex-col items-center gap-4 hover:shadow-lg transition-all relative cursor-pointer bg-white dark:bg-slate-800 border-gray-200 dark:border-gray-700",onClick:()=>t(a),children:[e.jsxs("div",{className:"absolute top-2 right-2 flex gap-2 z-10",children:[e.jsx("button",{type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),o(a,s)},className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors",children:e.jsx(re,{className:"h-4 w-4"})}),e.jsx("button",{type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),n(a)},className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors",children:e.jsx(A,{className:"h-4 w-4"})})]}),e.jsx(I,{className:"h-8 w-8 text-primary dark:text-blue-400"}),e.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:s})]}),ne=({isOpen:a,onOpenChange:s,folderName:t,onFolderNameChange:r,onSubmit:m})=>e.jsx(o,{open:a,onOpenChange:s,children:e.jsxs(n,{children:[e.jsx(i,{children:e.jsx(l,{children:"Criar Nova Pasta"})}),e.jsxs("form",{onSubmit:m,className:"space-y-4",children:[e.jsx(c,{placeholder:"Nome da pasta",value:t,onChange:e=>r(e.target.value)}),e.jsx(d,{type:"submit",className:"w-full",children:"Criar Pasta"})]})]})}),ie=({isOpen:a,onOpenChange:s,folderName:t,onFolderNameChange:r,onSubmit:m})=>e.jsx(o,{open:a,onOpenChange:s,children:e.jsxs(n,{children:[e.jsx(i,{children:e.jsx(l,{children:"Editar Nome da Pasta"})}),e.jsxs("form",{onSubmit:m,className:"space-y-4",children:[e.jsx(c,{placeholder:"Novo nome da pasta",value:t,onChange:e=>r(e.target.value)}),e.jsx(d,{type:"submit",className:"w-full",children:"Salvar"})]})]})}),le=({isOpen:a,onOpenChange:s,onConfirm:t})=>e.jsx(o,{open:a,onOpenChange:s,children:e.jsxs(n,{children:[e.jsxs(i,{children:[e.jsx(l,{children:"Confirmar exclusão"}),e.jsx(m,{children:"Tem certeza que deseja excluir esta pasta?"})]}),e.jsxs(x,{children:[e.jsx(d,{variant:"outline",onClick:()=>s(!1),children:"Cancelar"}),e.jsx(d,{variant:"destructive",onClick:()=>{t()},children:"Excluir"})]})]})}),ce=(e,a)=>{const s=E(),{data:t,isLoading:r}=q({queryKey:["notes",e,a],queryFn:async()=>{let s=h.from("secure_notes").select("*").order("created_at",{ascending:!1});e&&(s=s.or(`title.ilike.%${e}%,content.ilike.%${e}%`)),a&&(s=s.eq("folder_id",a));const{data:t,error:r}=await s;if(r)throw O.error("Erro ao carregar notas"),r;return await Promise.all(t.map((async e=>{const{data:a}=await h.from("pedbook_notes_tags_relations").select("tag_id, pedbook_notes_tags(name)").eq("note_id",e.id),s=a?.map((e=>e.pedbook_notes_tags.name))||[];return{...e,tags:s}})))}});return{notes:t,isLoading:r,toggleFavorite:z({mutationFn:async e=>{const{data:a,error:s}=await h.from("pedbook_notes").update({is_favorite:!e.is_favorite,updated_at:(new Date).toISOString()}).eq("id",e.id).select().single();if(s)throw s;return a},onSuccess:()=>{s.invalidateQueries({queryKey:["notes"]})},onError:e=>{O.error("Erro ao atualizar favorito")}}),createNote:z({mutationFn:async e=>{const{data:a,error:s}=await h.auth.getUser();if(s)throw O.error("Erro ao identificar usuário"),s;const{data:t,error:r}=await h.from("pedbook_notes").insert([{...e,user_id:a.user.id,content:e.content||""}]).select().single();if(r)throw O.error("Erro ao criar nota"),r;return t},onSuccess:()=>{s.invalidateQueries({queryKey:["notes"]}),O.success("Nota criada com sucesso")}}),updateNote:z({mutationFn:async e=>{const{error:a}=await h.from("pedbook_notes").update({title:e.title,content:e.content,folder_id:e.folder_id,updated_at:(new Date).toISOString()}).eq("id",e.id);if(a)throw a},onSuccess:()=>{s.invalidateQueries({queryKey:["notes"]}),O.success("Nota atualizada com sucesso")},onError:e=>{O.error("Erro ao atualizar nota")}}),deleteNote:z({mutationFn:async e=>{const{error:a}=await h.from("pedbook_notes").delete().eq("id",e);if(a)throw a},onSuccess:()=>{s.invalidateQueries({queryKey:["notes"]})}}),addTag:z({mutationFn:async({noteId:e,tagName:a})=>{const{data:s}=await h.from("pedbook_notes_tags").select("id").eq("name",a).single();let t;if(s)t=s.id;else{const{data:e}=await h.auth.getUser(),{data:s,error:r}=await h.from("pedbook_notes_tags").insert([{name:a,user_id:e.user.id}]).select().single();if(r)throw r;t=s.id}const{data:r}=await h.from("pedbook_notes_tags_relations").select("*").eq("note_id",e).eq("tag_id",t).single();if(!r){const{error:a}=await h.from("pedbook_notes_tags_relations").insert([{note_id:e,tag_id:t}]);if(a)throw a}},onSuccess:()=>{s.invalidateQueries({queryKey:["notes"]})},onError:e=>{O.error("Erro ao adicionar tag")}}),removeTag:z({mutationFn:async({noteId:e,tagName:a})=>{const{data:s,error:t}=await h.from("pedbook_notes_tags").select("id").eq("name",a).single();if(t)throw O.error("Erro ao encontrar tag"),t;const{error:r}=await h.from("pedbook_notes_tags_relations").delete().eq("note_id",e).eq("tag_id",s.id);if(r)throw O.error("Erro ao remover tag da nota"),r},onSuccess:()=>{s.invalidateQueries({queryKey:["notes"]})}}),deleteAllNotesInFolder:async e=>{try{const{error:a}=await h.from("pedbook_notes").delete().eq("folder_id",e);if(a)throw a;s.invalidateQueries({queryKey:["notes"]})}catch(a){throw a}}}},de=({onFolderSelect:s})=>{const[t,n]=a.useState(!1),[i,l]=a.useState(!1),[c,d]=a.useState(!1),[m,x]=a.useState(null),[p,g]=a.useState(null),[f,j]=a.useState(""),y=E(),{deleteAllNotesInFolder:v}=ce(),{data:w,isLoading:N}=q({queryKey:["folders"],queryFn:async()=>{const{data:e,error:a}=await h.auth.getUser();if(a)throw a;const{data:s,error:t}=await h.from("secure_notes_folders").select("*").eq("user_id",e.user.id).order("name");if(t)throw t;return s}});return N?e.jsx("div",{children:"Carregando..."}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:[w?.map((a=>e.jsx(oe,{id:a.id,name:a.name,onSelect:s,onEdit:(e,a)=>{x(e),j(a),l(!0)},onDelete:e=>{g(e),d(!0)}},a.id))),e.jsxs(o,{open:t,onOpenChange:n,children:[e.jsx(u,{asChild:!0,children:e.jsxs(r,{className:"p-6 flex flex-col items-center justify-center gap-4 border-2 border-dashed border-gray-200 hover:border-primary/50 cursor-pointer transition-colors",children:[e.jsx(D,{className:"h-8 w-8 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Nova Pasta"})]})}),e.jsx(ne,{isOpen:t,onOpenChange:n,folderName:f,onFolderNameChange:j,onSubmit:async e=>{if(e.preventDefault(),f.trim())try{const{data:e}=await h.auth.getUser(),{data:a,error:s}=await h.from("pedbook_notes_folders").insert([{name:f.trim(),user_id:e.user.id}]).select().single();if(s)throw s;y.setQueryData(["folders"],(e=>[...e||[],a])),O.success("Pasta criada com sucesso"),j(""),n(!1)}catch(a){O.error("Erro ao criar pasta")}}})]}),e.jsx(ie,{isOpen:i,onOpenChange:l,folderName:f,onFolderNameChange:j,onSubmit:async e=>{if(e.preventDefault(),f.trim()&&m)try{const{error:e}=await h.from("pedbook_notes_folders").update({name:f.trim()}).eq("id",m);if(e)throw e;y.setQueryData(["folders"],(e=>e.map((e=>e.id===m?{...e,name:f.trim()}:e)))),O.success("Nome da pasta atualizado"),j(""),l(!1),x(null)}catch(a){O.error("Erro ao atualizar pasta")}}}),e.jsx(le,{isOpen:c,onOpenChange:d,onConfirm:async()=>{if(p)try{await v(p);const{error:e}=await h.from("pedbook_notes_folders").delete().eq("id",p);if(e)throw e;y.setQueryData(["folders"],(e=>(e||[]).filter((e=>e.id!==p)))),O.success("Pasta removida com sucesso"),d(!1),g(null)}catch(e){O.error("Erro ao remover pasta")}}})]})},me=({selectedTag:a,onTagChange:s,showFavorites:t,onFavoritesChange:r,availableTags:o})=>e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs(p,{value:a||"all",onValueChange:e=>s("all"===e?null:e),children:[e.jsx(g,{className:"w-[200px]",children:e.jsx(f,{placeholder:"Filtrar por tag"})}),e.jsxs(j,{children:[e.jsx(y,{value:"all",children:"Todas as tags"}),o.map((a=>e.jsx(y,{value:a,children:a},a)))]})]}),e.jsxs(d,{variant:t?"default":"outline",size:"sm",onClick:()=>r(!t),className:"gap-2",children:[e.jsx(M,{className:"h-4 w-4 "+(t?"fill-current":"")}),"Favoritos"]})]});function xe({note:a,isOpen:s,onClose:t,onEdit:r,onDelete:i,onToggleFavorite:l}){const{toast:c}=v();return e.jsx(o,{open:s,onOpenChange:t,children:e.jsx(n,{className:"max-w-3xl h-[90vh] overflow-hidden flex flex-col p-0 rounded-xl",children:e.jsxs("div",{className:"flex flex-col flex-1 min-h-0",children:[e.jsx("div",{className:"p-4 border-b bg-gradient-to-r from-primary-light to-white rounded-t-xl",children:e.jsx("div",{className:"flex items-center justify-between mb-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl bg-white rounded-full p-2 shadow-sm",children:"📝"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 break-words",children:a.title||"Sem título"}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(P,{className:"h-4 w-4"}),Q(new Date(a.created_at),"dd 'de' MMMM 'de' yyyy",{locale:U})]}),e.jsxs("div",{className:"flex items-center gap-2 mt-2",children:[e.jsx(d,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-primary hover:bg-primary/10 px-2",onClick:r,title:"Editar nota",children:e.jsx(re,{className:"h-4 w-4"})}),e.jsx(d,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-red-600 hover:bg-red-50 px-2",onClick:()=>i(a.id),title:"Excluir nota",children:e.jsx(A,{className:"h-4 w-4"})}),e.jsx(d,{variant:"ghost",size:"sm",onClick:async()=>{try{await l(a),c({description:a.is_favorite?"Nota removida dos favoritos!":"Nota adicionada aos favoritos!",duration:2e3})}catch(e){c({variant:"destructive",description:"Erro ao atualizar favorito",duration:2e3})}},className:"text-gray-500 hover:text-yellow-500 hover:bg-yellow-50 px-2 "+(a.is_favorite?"text-yellow-500":""),title:a.is_favorite?"Remover dos favoritos":"Adicionar aos favoritos",children:e.jsx(M,{className:"h-4 w-4 "+(a.is_favorite?"fill-current":"")})})]})]})]})})}),a.tags&&a.tags.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-2 p-4 border-b bg-gray-50",children:[e.jsx(L,{className:"h-4 w-4 text-gray-400"}),a.tags.map((a=>e.jsx(w,{variant:"secondary",className:"bg-white border shadow-sm text-primary hover:bg-primary-light transition-colors",children:a},a)))]}),e.jsx("div",{className:"flex justify-center p-2 border-b",children:e.jsxs(d,{variant:"outline",size:"sm",onClick:()=>{const e=document.createElement("div");e.innerHTML=a.content;const s=e.innerHTML.replace(/<\/(p|div|h[1-6]|ul|ol|li|blockquote)>/g,"\n\n").replace(/<br\s*\/?>/g,"\n").replace(/<[^>]+>/g,"").replace(/&nbsp;/g," ").replace(/\n\s*\n/g,"\n\n").trim();navigator.clipboard.writeText(s).then((()=>{c({description:"Conteúdo copiado para a área de transferência!",duration:2e3})})).catch((()=>{c({variant:"destructive",description:"Erro ao copiar o conteúdo.",duration:2e3})}))},className:"text-gray-500 hover:text-gray-700",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Copiar texto"]})}),e.jsx(N,{className:"flex-1 min-h-0",children:e.jsx("div",{className:"prose max-w-none p-6 break-words whitespace-pre-wrap",dangerouslySetInnerHTML:{__html:a.content},style:{overflowWrap:"break-word",wordWrap:"break-word",wordBreak:"break-word",whiteSpace:"pre-wrap"}})})]})})})}function he({note:a,onNoteClick:s,onToggleFavorite:t}){return e.jsxs(r,{className:"bg-white dark:bg-gray-800 p-3 flex flex-col h-[140px] transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer relative",onClick:()=>s(a),children:[e.jsxs("div",{className:"flex items-start justify-between gap-2 mb-2",children:[e.jsxs("div",{className:"flex items-start gap-2 flex-1 min-w-0",children:[e.jsx("span",{className:"text-xl flex-shrink-0",children:"📝"}),e.jsx("h3",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm break-words line-clamp-2 overflow-hidden",children:a.title||"Sem título"})]}),e.jsx("button",{className:"text-gray-400 hover:text-yellow-400 transition-colors flex-shrink-0",onClick:e=>{e.stopPropagation(),t(a)},children:e.jsx(M,{className:"h-4 w-4 "+(a.is_favorite?"text-yellow-400 fill-current":"")})})]}),a.tags&&a.tags.length>0&&e.jsx("div",{className:"mt-auto flex flex-wrap gap-1",children:a.tags.map((a=>e.jsx(w,{variant:"secondary",className:"bg-primary-light text-primary text-[10px] px-2 py-0",children:a},a)))})]})}const ue=()=>{const e=E(),{data:a,isLoading:s}=q({queryKey:["folders"],queryFn:async()=>{const{data:e,error:a}=await h.auth.getUser();if(a)throw O.error("Erro ao identificar usuário"),a;const{data:s,error:t}=await h.from("secure_notes_folders").select("*").eq("user_id",e.user.id).order("created_at",{ascending:!0});if(t)throw O.error("Erro ao carregar pastas"),t;return s}});return{folders:a,isLoading:s,createFolder:z({mutationFn:async e=>{const{data:a,error:s}=await h.auth.getUser();if(s)throw O.error("Erro ao identificar usuário"),s;const{data:t,error:r}=await h.from("pedbook_notes_folders").insert([{name:e,user_id:a.user.id}]).select().single();if(r)throw O.error("Erro ao criar pasta"),r;return t},onSuccess:()=>{e.invalidateQueries({queryKey:["folders"]}),O.success("Pasta criada com sucesso")}}),deleteFolder:z({mutationFn:async e=>{const{error:a}=await h.from("pedbook_notes_folders").delete().eq("id",e);if(a)throw O.error("Erro ao deletar pasta"),a},onSuccess:()=>{e.invalidateQueries({queryKey:["folders"]}),O.success("Pasta deletada com sucesso")}})}},pe=()=>{const e=E(),{data:a}=q({queryKey:["text-enhancement"],queryFn:async()=>{const{data:{user:e}}=await h.auth.getUser();if(!e)return null;const{data:a}=await h.from("pedbook_text_enhancements").select("created_at").eq("user_id",e.id).order("created_at",{ascending:!1}).limit(1).single();return a}});return{canEnhance:!a||new Date(a.created_at)<new Date(Date.now()-18e5),lastEnhancement:a,recordEnhancement:async()=>{const{data:{user:a}}=await h.auth.getUser();a&&(await h.from("pedbook_text_enhancements").insert({user_id:a.id}),e.invalidateQueries({queryKey:["text-enhancement"]}))}}},ge=({onEnhance:a,isEnhancing:t})=>{const{canEnhance:r,lastEnhancement:o}=pe(),[n,i]=s.useState(""),{toast:l}=v();return s.useEffect((()=>{if(!r&&o){const e=setInterval((()=>{const a=new Date(o.created_at),s=new Date(a.getTime()+18e5),t=new Date,r=s.getTime()-t.getTime();if(r<=0)return i(""),void clearInterval(e);const n=Math.floor(r/1e3/60),l=Math.floor(r/1e3%60);i(`${n}:${l.toString().padStart(2,"0")}`)}),1e3);return()=>clearInterval(e)}}),[r,o]),e.jsxs("div",{className:"flex items-center gap-2 w-full",children:[e.jsx(W,{children:e.jsxs(R,{children:[e.jsx(Y,{asChild:!0,children:e.jsxs(d,{type:"button",variant:"secondary",size:"sm",onClick:a,disabled:t||!r,className:"flex-1 flex items-center justify-center gap-2 transition-opacity "+(t?"animate-pulse":""),children:[e.jsx(X,{className:"h-4 w-4 "+(t?"animate-spin":"")}),t?"Melhorando texto...":"Melhorar texto",!r&&n&&e.jsxs(e.Fragment,{children:[e.jsx(G,{className:"h-4 w-4"}),e.jsx("span",{className:"text-xs",children:n})]})]})}),e.jsx(J,{children:e.jsx("p",{children:r?"Melhora automaticamente a formatação e clareza do seu texto":`Aguarde ${n} para usar novamente`})})]})}),!r&&e.jsx(d,{type:"button",variant:"ghost",size:"sm",onClick:()=>{const{dismiss:a}=l({title:"Função de Melhoria de Texto",description:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-sm sm:text-base text-gray-700",children:["A função de melhoria de texto pode ser usada a cada 30 minutos.",n&&e.jsxs("span",{className:"block mt-2 font-medium",children:["Você poderá usar novamente em: ",n]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(d,{variant:"outline",onClick:()=>a(),children:"Ok, entendi"})})]}),duration:5e3,className:"fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] max-w-md w-full bg-white shadow-lg rounded-lg border p-6"})},className:"px-2",children:e.jsx(b,{className:"h-4 w-4"})})]})},fe=({content:a,onChange:t})=>{const[r,o]=s.useState(!1),{recordEnhancement:n}=pe(),i=B({extensions:[V.configure({heading:!1,bulletList:!1,orderedList:!1}),H],content:a,onUpdate:({editor:e})=>{t(e.getHTML())},editorProps:{attributes:{class:"h-full min-h-[250px] p-4 focus:outline-none prose-sm sm:prose max-w-none overflow-x-auto break-words whitespace-pre-wrap"}}});return i?e.jsxs("div",{className:"border rounded-lg flex flex-col h-full overflow-hidden space-y-4",children:[e.jsxs("div",{className:"flex gap-2 p-2 border-b overflow-x-auto",children:[e.jsx(d,{type:"button",variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleBold().run(),className:i.isActive("bold")?"bg-secondary":"",children:e.jsx(Z,{className:"h-4 w-4"})}),e.jsx(d,{type:"button",variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleItalic().run(),className:i.isActive("italic")?"bg-secondary":"",children:e.jsx(ee,{className:"h-4 w-4"})}),e.jsx(d,{type:"button",variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleUnderline().run(),className:i.isActive("underline")?"bg-secondary":"",children:e.jsx(ae,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"flex-1 overflow-auto",children:e.jsx($,{editor:i,className:"h-full"})}),e.jsx("div",{className:"p-2 border-t",children:e.jsx(ge,{onEnhance:async()=>{if(!i)return;const e=i.getHTML();try{o(!0),O.loading("Melhorando o texto...");const{data:a,error:s}=await h.functions.invoke("enhance-text",{body:{text:e}});if(s)throw s;a.enhancedText&&(i.commands.setContent(a.enhancedText),t(a.enhancedText),await n(),O.success("Texto melhorado com sucesso!"))}catch(a){O.error("Erro ao melhorar o texto. Tente novamente.")}finally{o(!1)}},isEnhancing:r})})]}):null},je=({noteId:a,selectedTags:t,onTagsChange:r,availableTags:o=[]})=>{const[n,i]=s.useState(!1),[l,m]=s.useState(""),{addTag:x,removeTag:h}=ce(),u=Array.from(new Set(o)).filter((e=>!t.includes(e)));return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex flex-wrap gap-2",children:[t.map((s=>e.jsxs(w,{variant:"secondary",className:"flex items-center gap-1",children:[e.jsx(se,{className:"h-3 w-3"}),s,e.jsx(d,{type:"button",variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-transparent",onClick:e=>{e.preventDefault(),(async e=>{a&&await h.mutateAsync({noteId:a,tagName:e}),r(t.filter((a=>a!==e)))})(s)},children:e.jsx(k,{className:"h-3 w-3"})})]},s))),e.jsxs(d,{type:"button",variant:"ghost",size:"sm",className:"h-6",onClick:e=>{e.preventDefault(),i(!0)},children:[e.jsx(D,{className:"h-4 w-4 mr-1"}),"Adicionar Tag"]})]}),n&&e.jsxs("div",{className:"space-y-2",children:[u.length>0&&e.jsxs(p,{onValueChange:e=>{t.includes(e)||(r([...t,e]),i(!1))},children:[e.jsx(g,{className:"w-full",children:e.jsx(f,{placeholder:"Selecione uma tag existente"})}),e.jsx(j,{children:u.map((a=>e.jsx(y,{value:a,children:a},a)))})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(c,{type:"text",placeholder:"Ou crie uma nova tag",value:l,onChange:e=>m(e.target.value),className:"h-8"}),e.jsx(d,{type:"button",size:"sm",className:"h-8",onClick:async()=>{l.trim()&&(a&&await x.mutateAsync({noteId:a,tagName:l.trim()}),t.includes(l.trim())||r([...t,l.trim()]),m(""),i(!1))},children:"Adicionar"})]})]})]})},ye=({noteId:a,initialTitle:t="",initialContent:r="",initialFolderId:i="",initialTags:l=[],isOpen:m,onClose:x})=>{const[u,v]=s.useState(t),[w,N]=s.useState(r),[b,k]=s.useState(i),[_,S]=s.useState(l),[T,F]=s.useState(!1),[z,A]=s.useState(""),{createNote:I,updateNote:M,addTag:P}=ce(),{folders:L,createFolder:K}=ue(),{tags:Q}=(()=>{const{data:e,isLoading:a}=q({queryKey:["notes-tags"],queryFn:async()=>{const{data:e}=await h.auth.getUser(),{data:a,error:s}=await h.from("pedbook_notes_tags").select("*").eq("user_id",e.user.id).order("name");if(s)throw O.error("Erro ao carregar tags"),s;return a}});return{tags:e,isLoading:a}})(),U=E();return e.jsx(o,{open:m,onOpenChange:x,children:e.jsx(n,{className:"max-w-3xl max-h-[90vh] overflow-hidden flex flex-col",children:e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(a){if(await M.mutateAsync({id:a,title:u,content:w,folder_id:"no-folder"===b?null:b||null}),_.length>0)for(const e of _)await P.mutateAsync({noteId:a,tagName:e});O.success("Nota atualizada com sucesso!")}else{const e=await I.mutateAsync({title:u,content:w,folder_id:"no-folder"===b?null:b||null});if(e&&_.length>0)for(const a of _)await P.mutateAsync({noteId:e.id,tagName:a});O.success("Nota criada com sucesso!")}await U.invalidateQueries({queryKey:["notes"]}),x()}catch(s){O.error("Erro ao salvar nota")}},className:"space-y-6 flex-1 overflow-y-auto px-1",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(C,{htmlFor:"title",children:"Título"}),e.jsx(c,{id:"title",value:u,onChange:e=>{const a=e.target.value;a.length<=30&&v(a)},placeholder:"Digite o título da nota...",maxLength:30,className:"w-full break-words"}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[u.length,"/30 caracteres"]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx(C,{htmlFor:"folder",children:"Pasta"}),e.jsx(d,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>F(!T),children:e.jsx(D,{className:"h-4 w-4"})})]}),T?e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{type:"text",placeholder:"Nome da nova pasta",value:z,onChange:e=>A(e.target.value),className:"h-8 text-sm"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{type:"button",size:"sm",onClick:async()=>{if(z.trim())try{const e=await K.mutateAsync(z);k(e.id),A(""),F(!1),O.success("Pasta criada com sucesso!")}catch(e){O.error("Erro ao criar pasta")}},children:"Criar"}),e.jsx(d,{type:"button",variant:"ghost",size:"sm",onClick:()=>{F(!1),A("")},children:"Cancelar"})]})]}):e.jsxs(p,{value:b,onValueChange:k,children:[e.jsx(g,{className:"w-full",children:e.jsx(f,{placeholder:"Selecione uma pasta"})}),e.jsxs(j,{children:[e.jsx(y,{value:"no-folder",children:"Nenhuma pasta"}),L?.map((a=>e.jsx(y,{value:a.id,children:a.name},a.id)))]})]})]}),e.jsxs("div",{children:[e.jsx(C,{children:"Tags"}),e.jsx(je,{noteId:a||"",selectedTags:_,onTagsChange:S,availableTags:Q?.map((e=>e.name))||[]})]}),e.jsxs("div",{children:[e.jsx(C,{children:"Conteúdo"}),e.jsx("div",{className:"min-h-[300px] max-h-[50vh] overflow-y-auto",children:e.jsx(fe,{content:w,onChange:N})})]})]}),e.jsx(d,{type:"submit",className:"w-full",children:a?"Atualizar Nota":"Criar Nota"})]})})})},ve=({searchTerm:a,folderId:t})=>{const{notes:r,toggleFavorite:o,deleteNote:n}=ce(a,t),[i,l]=s.useState(null),[c,d]=s.useState(!0),[m,x]=s.useState(null),[h,u]=s.useState(!1),p=Array.from(new Set(r?.flatMap((e=>e.tags||[]))||[])),g=r?.filter((e=>{const a=!m||e.tags?.includes(m),s=!h||e.is_favorite;return a&&s}));return e.jsxs(e.Fragment,{children:[e.jsx(me,{selectedTag:m,onTagChange:x,showFavorites:h,onFavoritesChange:u,availableTags:p}),e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4",children:g?.map((a=>e.jsx(he,{note:a,onNoteClick:()=>{l(a),d(!0)},onToggleFavorite:o.mutate},a.id)))}),i&&c&&e.jsx(xe,{isOpen:!!i&&c,onClose:()=>{l(null),d(!0)},note:i,onEdit:()=>d(!1),onDelete:async e=>{try{await n.mutateAsync(e),l(null),O.success("Nota excluída com sucesso")}catch(a){O.error("Erro ao excluir nota")}},onToggleFavorite:o.mutate}),i&&!c&&e.jsx(ye,{isOpen:!!i&&!c,onClose:()=>{l(null),d(!0)},noteId:i.id,initialTitle:i.title,initialContent:i.content,initialFolderId:i.folder_id,initialTags:i.tags})]})},we=({folderId:s})=>{const[t,r]=a.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs(d,{onClick:()=>r(!0),className:"gap-2",children:[e.jsx(D,{className:"h-4 w-4"}),"Nova Anotação"]}),e.jsx(ye,{isOpen:t,onClose:()=>r(!1),initialFolderId:s||""})]})},Ne=({onSearch:a})=>e.jsxs("div",{className:"relative",children:[e.jsx(_,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(c,{type:"search",placeholder:"Buscar anotações...",className:"pl-10",onChange:e=>a(e.target.value)})]}),be=()=>{const[a,t]=s.useState(null),[r,o]=s.useState(""),n=te(),{folders:i,isLoading:l}=ue(),{toast:c}=v();return s.useEffect((()=>{(async()=>{const{data:{session:e}}=await h.auth.getSession();e||n("/")})();const{data:{subscription:e}}=h.auth.onAuthStateChange(((e,a)=>{a||n("/")}));return()=>e.unsubscribe()}),[n]),s.useEffect((()=>{!l&&i&&0===i.length&&c({title:"Bem-vindo ao Sistema de Anotações!",description:e.jsxs("div",{className:"space-y-4 w-full max-w-[calc(100vw-2rem)] sm:max-w-lg mx-auto bg-white/95 p-4 sm:p-6 rounded-lg shadow-lg backdrop-blur-sm sm:max-h-[90vh] overflow-y-auto",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-semibold text-primary text-center",children:"Como funciona?"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-700 font-medium text-center",children:"Organize suas anotações em pastas para melhor organização:"}),e.jsxs("ul",{className:"list-none space-y-2 sm:space-y-3 mt-2 sm:mt-4 text-sm sm:text-base",children:[e.jsxs("li",{className:"flex items-center gap-2 bg-accent-blue p-2 rounded-md",children:[e.jsx("span",{className:"text-primary",children:"📁"}),e.jsx("span",{children:"Crie pastas para organizar suas anotações por temas."})]}),e.jsxs("li",{className:"flex items-center gap-2 bg-accent-purple p-2 rounded-md",children:[e.jsx("span",{className:"text-primary",children:"📝"}),e.jsx("span",{children:"Adicione notas em cada pasta com conteúdo rico."})]}),e.jsxs("li",{className:"flex items-center gap-2 bg-accent-green p-2 rounded-md",children:[e.jsx("span",{className:"text-primary",children:"🔍"}),e.jsx("span",{children:"Pesquise facilmente em todas as suas anotações."})]})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 text-center mt-2 sm:mt-4 italic",children:"Vamos começar criando sua primeira pasta?"}),e.jsx("div",{className:"flex justify-center mt-2 sm:mt-4",children:e.jsx("button",{onClick:()=>{c({duration:2e3,description:"Agora você pode criar sua primeira pasta!"})},className:"bg-primary hover:bg-primary/90 text-white px-6 sm:px-8 py-2 rounded-full font-medium transition-all",children:"Começar"})})]}),duration:0,className:"fixed inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm max-w-none w-screen h-screen m-0 p-4"})}),[i,l,c]),e.jsxs("div",{className:"min-h-screen flex flex-col bg-white",children:[e.jsxs(S,{children:[e.jsx("title",{children:"PedBook | Minhas Anotações"}),e.jsx("meta",{name:"description",content:"Sistema de anotações para profissionais de saúde - organize e acesse suas anotações de forma simples e eficiente."})]}),e.jsx(T,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:a?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("button",{onClick:()=>t(null),className:"text-sm text-primary hover:underline",children:"← Voltar para pastas"}),e.jsx(we,{folderId:a})]}),e.jsx(Ne,{onSearch:o}),e.jsx(ve,{folderId:a,searchTerm:r})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("button",{onClick:()=>n("/"),className:"text-sm text-primary hover:underline",children:"← Ir para o menu inicial"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-6 w-6 text-primary"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Minhas Pastas"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(de,{onFolderSelect:t})})]})}),e.jsx(F,{})]})};export{be as default};
