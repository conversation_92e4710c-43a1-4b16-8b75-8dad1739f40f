import{j as t}from"./radix-core-6kBL75b5.js";import{r as e}from"./critical-DVX9Inzy.js";import{c as i,B as o,I as a,Q as n}from"./index-DQuOk0R3.js";import{B as s}from"./bug-CS708cXG.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r=i("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]]),l=i("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),d=()=>{const[i,d]=e.useState(!1),c=()=>{const t=document.body,e=document.documentElement,i=Math.max(t.scrollHeight,t.offsetHeight,e.clientHeight,e.scrollHeight,e.offsetHeight),o=window.innerHeight,a=e.scrollHeight,n=a>o,s=parseInt(getComputedStyle(e).getPropertyValue("--safe-area-inset-top")||"0px"),r=parseInt(getComputedStyle(e).getPropertyValue("--safe-area-inset-bottom")||"0px"),l=o-s-r;return{pageHeight:i,viewportHeight:o,scrollHeight:a,hasVerticalScroll:n,contentOverflow:i>l,safeAreaTop:s,safeAreaBottom:r,availableHeight:l}},h=t=>{try{const e=document.querySelector(t);if(!e)return{error:`Element not found: ${t}`};const i=e.getBoundingClientRect(),o=getComputedStyle(e);return{dimensions:{width:i.width,height:i.height,top:i.top,bottom:i.bottom,scrollHeight:e.scrollHeight,scrollTop:e.scrollTop,clientHeight:e.clientHeight,offsetHeight:e.offsetHeight},css:{height:o.height,maxHeight:o.maxHeight,minHeight:o.minHeight,overflow:o.overflow,overflowY:o.overflowY,display:o.display,flexDirection:o.flexDirection,flex:o.flex,position:o.position,padding:o.padding,margin:o.margin,backgroundColor:o.backgroundColor,background:o.background,zIndex:o.zIndex},classes:e.className,hasScroll:e.scrollHeight>e.clientHeight,elementInfo:{tagName:e.tagName,id:e.id,dataAttributes:(()=>{const t={};for(let i=0;i<e.attributes.length;i++){const o=e.attributes[i];o.name.startsWith("data-")&&(t[o.name]=o.value)}return t})()}}}catch(e){return{error:`Error analyzing ${t}: ${e}`}}},m=()=>({main:h('[data-debug="optimized-main"]'),content:h('[data-debug="optimized-content"]'),tabs:h('[data-debug="optimized-tabs"]'),tabContent:h('[data-debug="optimized-tab-content"]'),prose:h('[data-debug="optimized-prose"]'),viewport:{width:window.innerWidth,height:window.innerHeight},document:{scrollHeight:document.documentElement.scrollHeight,clientHeight:document.documentElement.clientHeight,scrollTop:document.documentElement.scrollTop},body:{scrollHeight:document.body.scrollHeight,clientHeight:document.body.clientHeight,scrollTop:document.body.scrollTop},suspiciousElements:(()=>{const t=[];try{document.querySelectorAll("*").forEach(((e,i)=>{const o=e,a=getComputedStyle(o),n=o.getBoundingClientRect();if(n.top>50&&n.top<500&&n.height>30){const e=a.backgroundColor,r=a.background,l=a.position,d=a.zIndex;let c=[];if((e.includes("rgb(255, 255, 255)")||e.includes("white")||r.includes("white")||r.includes("rgb(255, 255, 255)"))&&c.push("white background"),"absolute"!==l&&"fixed"!==l||c.push(`position: ${l}`),"auto"!==d&&parseInt(d)>0&&c.push(`z-index: ${d}`),n.height>100&&n.width>200&&c.push("large size"),c.length>0){const e=`${o.tagName.toLowerCase()}${o.id?"#"+o.id:""}${o.className?"."+o.className.split(" ").slice(0,2).join("."):""}[${i}]`;try{const i=h(`${o.tagName.toLowerCase()}:nth-of-type(${Array.from(o.parentNode?.children||[]).filter((t=>t.tagName===o.tagName)).indexOf(o)+1})`);"dimensions"in i&&t.push({selector:e,element:i,reason:c.join(", ")})}catch(s){}}}}))}catch(e){}return t.slice(0,15)})()});return i?t.jsxs("div",{className:"fixed bottom-20 left-4 z-50 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-4 max-w-xs",children:[t.jsxs("div",{className:"flex items-center justify-between mb-3",children:[t.jsx("h3",{className:"font-semibold text-sm",children:"Debug Global"}),t.jsx(o,{onClick:()=>d(!1),variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:"×"})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs(o,{onClick:()=>{const t=Array.from(document.querySelectorAll("img")).map((t=>{const e=t.getBoundingClientRect(),i=getComputedStyle(t),o=e.width/(t.naturalWidth||1),a=e.height/(t.naturalHeight||1);return{src:t.src,alt:t.alt||"No alt text",naturalWidth:t.naturalWidth,naturalHeight:t.naturalHeight,displayWidth:e.width,displayHeight:e.height,scaleX:o,scaleY:a,isOversized:o>2||a>2,css:{objectFit:i.objectFit,width:i.width,height:i.height,transform:i.transform}}})),e=c(),i={timestamp:(new Date).toISOString(),page:"Image Analysis",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},images:t,layout:e};navigator.clipboard.writeText(JSON.stringify(i,null,2)).then((()=>{n({title:"Debug copiado!",description:`Análise de ${t.length} imagens copiada para clipboard`})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[t.jsx(a,{className:"w-3 h-3 mr-1"}),"Analisar Imagens"]}),t.jsxs(o,{onClick:()=>{const t=c(),e={timestamp:(new Date).toISOString(),page:"Layout Analysis",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},layout:t};navigator.clipboard.writeText(JSON.stringify(e,null,2)).then((()=>{n({title:"Debug copiado!",description:"Análise de layout copiada para clipboard"})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[t.jsx(r,{className:"w-3 h-3 mr-1"}),"Analisar Layout"]}),t.jsxs(o,{onClick:()=>{const t=m(),e=c(),i={timestamp:(new Date).toISOString(),page:"OptimizedConductsView Analysis",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},layout:e,optimizedLayout:t};navigator.clipboard.writeText(JSON.stringify(i,null,2)).then((()=>{n({title:"Debug OptimizedConductsView copiado!",description:"Análise detalhada do componente copiada para clipboard"})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[t.jsx(l,{className:"w-3 h-3 mr-1"}),"OptimizedConducts"]}),t.jsxs(o,{onClick:()=>{const t=c(),e=m(),i={timestamp:(new Date).toISOString(),page:"Full Analysis (Layout Only)",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},layout:t,optimizedLayout:e};navigator.clipboard.writeText(JSON.stringify(i,null,2)).then((()=>{n({title:"Debug completo copiado!",description:"Análise completa da página copiada para clipboard"})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[t.jsx(l,{className:"w-3 h-3 mr-1"}),"Análise Completa"]})]})]}):t.jsx(o,{onClick:()=>d(!0),className:"fixed bottom-20 left-4 z-50 bg-red-500 hover:bg-red-600 text-white shadow-lg",size:"sm",children:t.jsx(s,{className:"w-4 h-4"})})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */export{d as GlobalDebugger};
