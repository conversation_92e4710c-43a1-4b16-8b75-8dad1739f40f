import React, { useState, useEffect } from 'react';
import { Capacitor } from '@capacitor/core';

interface SafeAreaDebugData {
  timestamp: string;
  platform: string;
  isNative: boolean;
  userAgent: string;
  viewport?: { width: number; height: number };
  calculations?: {
    statusBarHeight: number;
    navigationBarHeight: number;
    calculatedHeight: number;
    formula: string;
  };
  cssVariables?: {
    top: string;
    bottom: string;
    height: string;
  };
  bodyClasses?: string;
  rootElement?: {
    classes: string;
    computedHeight: string;
    computedPosition: string;
    computedTop: string;
  } | null;
  message?: string;
}

declare global {
  interface Window {
    safeAreaDebugData?: SafeAreaDebugData;
  }
}

export const SafeAreaDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [debugData, setDebugData] = useState<SafeAreaDebugData | null>(null);
  const [realTimeData, setRealTimeData] = useState<any>({});

  const collectDebugData = () => {
    console.log('🔍 [DEBUG] Coletando dados de debug...');
    
    const rootElement = document.getElementById('root');
    const bodyStyles = window.getComputedStyle(document.body);
    const htmlStyles = window.getComputedStyle(document.documentElement);
    
    const currentData: SafeAreaDebugData = {
      timestamp: new Date().toISOString(),
      platform: Capacitor.getPlatform(),
      isNative: Capacitor.isNativePlatform(),
      userAgent: navigator.userAgent,
      viewport: { 
        width: window.innerWidth, 
        height: window.innerHeight 
      },
      calculations: {
        statusBarHeight: 30,
        navigationBarHeight: 48,
        calculatedHeight: window.innerHeight - 30 - 48,
        formula: `${window.innerHeight} - 30 - 48 = ${window.innerHeight - 30 - 48}`
      },
      cssVariables: {
        top: document.documentElement.style.getPropertyValue('--safe-area-top') || 
             getComputedStyle(document.documentElement).getPropertyValue('--safe-area-top'),
        bottom: document.documentElement.style.getPropertyValue('--safe-area-bottom') ||
                getComputedStyle(document.documentElement).getPropertyValue('--safe-area-bottom'),
        height: document.documentElement.style.getPropertyValue('--safe-area-height') ||
                getComputedStyle(document.documentElement).getPropertyValue('--safe-area-height')
      },
      bodyClasses: document.body.className,
      rootElement: rootElement ? {
        classes: rootElement.className,
        computedHeight: window.getComputedStyle(rootElement).height,
        computedPosition: window.getComputedStyle(rootElement).position,
        computedTop: window.getComputedStyle(rootElement).top
      } : null
    };

    // Dados em tempo real adicionais
    const additionalData = {
      currentTime: new Date().toLocaleString(),
      screenInfo: {
        availHeight: screen.availHeight,
        availWidth: screen.availWidth,
        height: screen.height,
        width: screen.width,
        orientation: screen.orientation?.type || 'unknown'
      },
      windowInfo: {
        innerHeight: window.innerHeight,
        innerWidth: window.innerWidth,
        outerHeight: window.outerHeight,
        outerWidth: window.outerWidth,
        devicePixelRatio: window.devicePixelRatio
      },
      documentInfo: {
        documentElementClientHeight: document.documentElement.clientHeight,
        documentElementScrollHeight: document.documentElement.scrollHeight,
        bodyClientHeight: document.body.clientHeight,
        bodyScrollHeight: document.body.scrollHeight
      },
      computedStyles: {
        body: {
          height: bodyStyles.height,
          minHeight: bodyStyles.minHeight,
          maxHeight: bodyStyles.maxHeight,
          position: bodyStyles.position,
          top: bodyStyles.top,
          paddingTop: bodyStyles.paddingTop,
          paddingBottom: bodyStyles.paddingBottom
        },
        html: {
          height: htmlStyles.height,
          minHeight: htmlStyles.minHeight,
          maxHeight: htmlStyles.maxHeight
        },
        root: rootElement ? {
          height: window.getComputedStyle(rootElement).height,
          minHeight: window.getComputedStyle(rootElement).minHeight,
          maxHeight: window.getComputedStyle(rootElement).maxHeight,
          position: window.getComputedStyle(rootElement).position,
          top: window.getComputedStyle(rootElement).top,
          left: window.getComputedStyle(rootElement).left,
          right: window.getComputedStyle(rootElement).right,
          width: window.getComputedStyle(rootElement).width,
          overflowY: window.getComputedStyle(rootElement).overflowY,
          overflowX: window.getComputedStyle(rootElement).overflowX
        } : null
      },
      safeAreaFromWindow: window.safeAreaDebugData || null
    };

    setDebugData(currentData);
    setRealTimeData(additionalData);
    
    console.log('📊 [DEBUG] Dados coletados:', currentData);
    console.log('📊 [DEBUG] Dados adicionais:', additionalData);
  };

  const copyToClipboard = () => {
    const fullData = {
      basicData: debugData,
      realTimeData: realTimeData,
      logs: 'Verifique o console para logs detalhados'
    };
    
    const dataString = JSON.stringify(fullData, null, 2);
    navigator.clipboard.writeText(dataString).then(() => {
      alert('Dados copiados para o clipboard!');
    }).catch(() => {
      // Fallback para dispositivos que não suportam clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = dataString;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Dados copiados para o clipboard!');
    });
  };

  useEffect(() => {
    // Coletar dados iniciais
    collectDebugData();
    
    // Atualizar dados quando a janela redimensionar
    const handleResize = () => {
      collectDebugData();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (!isVisible) {
    // Botão de debug removido para produção
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">🐛 Safe Area Debugger</h2>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ✕
          </button>
        </div>
        
        <div className="space-y-4">
          <div className="flex gap-2">
            <button
              onClick={collectDebugData}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              🔄 Atualizar Dados
            </button>
            <button
              onClick={copyToClipboard}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              📋 Copiar Dados
            </button>
          </div>
          
          {debugData && (
            <div className="space-y-4">
              <div className="bg-gray-100 p-4 rounded">
                <h3 className="font-bold mb-2">📱 Informações Básicas</h3>
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(debugData, null, 2)}
                </pre>
              </div>
              
              <div className="bg-blue-50 p-4 rounded">
                <h3 className="font-bold mb-2">📊 Dados em Tempo Real</h3>
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(realTimeData, null, 2)}
                </pre>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded">
                <h3 className="font-bold mb-2">🎯 Status das Safe Areas</h3>
                <div className="text-sm space-y-1">
                  <p><strong>Plataforma Nativa:</strong> {debugData.isNative ? '✅ Sim' : '❌ Não'}</p>
                  <p><strong>Classe Android:</strong> {debugData.bodyClasses?.includes('android-safe-area') ? '✅ Aplicada' : '❌ Não aplicada'}</p>
                  <p><strong>Variável --safe-area-top:</strong> {debugData.cssVariables?.top || '❌ Não definida'}</p>
                  <p><strong>Variável --safe-area-bottom:</strong> {debugData.cssVariables?.bottom || '❌ Não definida'}</p>
                  <p><strong>Variável --safe-area-height:</strong> {debugData.cssVariables?.height || '❌ Não definida'}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
