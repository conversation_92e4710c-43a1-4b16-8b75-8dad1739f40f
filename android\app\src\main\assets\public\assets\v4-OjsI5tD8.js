const t=[];for(let u=0;u<256;++u)t.push((u+256).toString(16).slice(1));let o;const r=new Uint8Array(16),n={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function e(e,u,d){if(n.randomUUID&&!e)return n.randomUUID();const a=(e=e||{}).random??e.rng?.()??function(){if(!o){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");o=crypto.getRandomValues.bind(crypto)}return o(r)}();if(a.length<16)throw new Error("Random bytes length must be >= 16");return a[6]=15&a[6]|64,a[8]=63&a[8]|128,function(o,r=0){return(t[o[r+0]]+t[o[r+1]]+t[o[r+2]]+t[o[r+3]]+"-"+t[o[r+4]]+t[o[r+5]]+"-"+t[o[r+6]]+t[o[r+7]]+"-"+t[o[r+8]]+t[o[r+9]]+"-"+t[o[r+10]]+t[o[r+11]]+t[o[r+12]]+t[o[r+13]]+t[o[r+14]]+t[o[r+15]]).toLowerCase()}(a)}export{e as v};
