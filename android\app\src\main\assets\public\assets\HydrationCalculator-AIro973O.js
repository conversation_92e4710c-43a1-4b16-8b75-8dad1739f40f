import{j as e}from"./radix-core-6kBL75b5.js";import{R as a,d as r,aM as s,a5 as t,B as i,D as l,aK as o,e as d,f as n,g as c,_ as m,ak as x,ab as u,T as g,aC as p,ap as h,z as b,l as j,S as f,ay as N,an as v,ad as y,ae as k,af as C,ag as w,ai as S,aL as E,aH as L,C as O,aj as A,b2 as I,aD as P,aa as R,a7 as T,aB as G}from"./index-D9amGMlQ.js";import M from"./Footer-BkFd5qSK.js";import{r as F,a as q}from"./critical-DVX9Inzy.js";import{C as $}from"./clock-G6szjEek.js";import{D as V}from"./droplets-DkdNAFNb.js";import{S as _}from"./scale-DPUkT8Aa.js";import{A as D,a as z,b as H}from"./alert-DF0vYpCj.js";import{C as B}from"./circle-help-DFUIKtE9.js";import{B as U}from"./book-open-CzUd5kBy.js";import{C as K}from"./copy-oRvZEVf-.js";import{D as W}from"./download-CUH-YaKO.js";import{B as Q}from"./beaker-DM6Kq39A.js";import{C as J,a as X,b as Y}from"./collapsible-B6HfSnGs.js";import{E as Z}from"./external-link-C-xrS7_q.js";import{F as ee,a as ae}from"./flowchartSEOData-B7-hWfhR.js";import{L as re}from"./lightbulb-Bt215yWD.js";import{T as se}from"./target-Cj27UDYs.js";import{B as te}from"./bug-DnXwJCtk.js";import{a as ie}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./zap-DULtmWB8.js";import"./star-BlLX_9hT.js";import"./instagram-CuaDlQAQ.js";const le=(e,a)=>({nacl_20:3.4,nacl_17_5:2.975,nacl_10:1.71,nacl_0_9:.154,kcl_19_1:2.55,kcl_10:1.34,gluconate_10:100,mg_sulfate_50:4,mg_sulfate_10:.8}[a]||0),oe=e=>({nacl_20:"NaCl 20%",nacl_17_5:"NaCl 17,5%",nacl_10:"NaCl 10%",nacl_0_9:"NaCl 0,9%",kcl_19_1:"KCl 19,1%",kcl_10:"KCl 10%",gluconate_10:"Gluconato de Cálcio 10%",mg_sulfate_50:"Sulfato de Magnésio 50%",mg_sulfate_10:"Sulfato de Magnésio 10%"}[e]||e),de=e=>{if(e.weight<2)return null;const a=ne(e.weight),r=e.offeredVolume||a,s=ce(e.weight,e.electrolytes),t=me(s,r,e.concentrations),i=t.reduce(((e,a)=>e+a.volume),0),l=r-i,o=xe(s.glucose,l),d=ue(s.glucose,r),n=ge(s,r),c=pe(e.weight,r,n,a,s.glucose,o,i),m=he(r),x=je(r);return{dailyNeed:a,offeredVolume:r,hydrationPercentage:r/a*100,hourlyRate:m.hourly,minuteRate:m.minute,dropRate:m.drops,microdropRate:m.microdrops,components:t,preparation:o,nutrition:d,osmolarity:n,validation:c,aliquots:x}},ne=e=>{let a=0;return a=e<=10?100*e:e<=20?1e3+50*(e-10):1500+20*(e-20),Math.min(a,2400)},ce=(e,a)=>({sodium:a.sodium*e,potassium:a.potassium*e,calcium:a.calcium*e,magnesium:a.magnesium*e,glucose:a.tig*e*1440/1e3}),me=(e,a,r)=>{const s=[],t=a/1e3;if(e.sodium>0){const a=le(0,r.sodiumConcentration),i=e.sodium/a,l=e.sodium/t;s.push({name:oe(r.sodiumConcentration),volume:Number(i.toFixed(1)),concentration:Number(l.toFixed(1)),unit:"mEq/L",color:"bg-blue-50 dark:bg-blue-900/30 border-blue-100 dark:border-blue-800/50"})}if(e.potassium>0){const a=le(0,r.potassiumConcentration),i=e.potassium/a,l=e.potassium/t;s.push({name:oe(r.potassiumConcentration),volume:Number(i.toFixed(1)),concentration:Number(l.toFixed(1)),unit:"mEq/L",color:"bg-purple-50 dark:bg-purple-900/30 border-purple-100 dark:border-purple-800/50"})}if(e.calcium>0){const a=le(0,r.calciumConcentration),i=e.calcium/a,l=e.calcium/t;s.push({name:oe(r.calciumConcentration),volume:Number(i.toFixed(1)),concentration:Number(l.toFixed(1)),unit:"mg/L",color:"bg-green-50 dark:bg-green-900/30 border-green-100 dark:border-green-800/50"})}if(e.magnesium>0){const a=le(0,r.magnesiumConcentration),i=e.magnesium/a,l=e.magnesium/t;s.push({name:oe(r.magnesiumConcentration),volume:Number(i.toFixed(1)),concentration:Number(l.toFixed(1)),unit:"mEq/L",color:"bg-yellow-50 dark:bg-yellow-900/30 border-yellow-100 dark:border-yellow-800/50"})}return s},xe=(e,a)=>{const r=e/a*100;let s=5;r>7.5&&(s=10),r>15&&(s=25),r>35&&(s=50);const t=e/(s/100);let i=t;if(t>a)for(;i>a&&s<50;){if(5===s)s=10;else if(10===s)s=25;else{if(25!==s)break;s=50}i=e/(s/100)}i=Math.min(i,a);const l=a-i,o=i*s/100;return{glucoseSolution:{concentration:s,volume:Number(i.toFixed(1))},distilledWater:Number(Math.max(0,l).toFixed(1)),realGlucose:Number(o.toFixed(1))}},ue=(e,a)=>({totalGlucose:Number(e.toFixed(1)),totalCalories:Number((3.4*e).toFixed(1)),glucoseConcentration:Number((e/a*100).toFixed(1))}),ge=(e,a)=>{const r=a/1e3,s=5.55*e.glucose/r+2*e.sodium/r+2*e.potassium/r+2*e.magnesium/r+1*e.calcium/r;return Number(s.toFixed(0))},pe=(e,a,r,s,t,i,l)=>{const o=[],d=[];if(e<3&&d.push("Peso mínimo permitido é 3,0 kg"),a>2400&&d.push("Volume excede o limite máximo de segurança (2400 mL/dia)"),a<.8*s&&o.push("Volume ofertado é menor que 80% da necessidade hídrica"),a>1.5*s&&o.push("Volume ofertado excede 150% da necessidade hídrica"),r>1800?d.push(`🚨 OSMOLARIDADE CRÍTICA: ${r.toFixed(0)} mOsm/L excede limite máximo para via central (1800 mOsm/L). Prescrição bloqueada para proteção do paciente. 💡 SOLUÇÕES: Aumentar volume total ou reduzir eletrólitos.`):r>1200?o.push(`⚠️ Osmolaridade muito elevada (${r.toFixed(0)} mOsm/L) - via central fortemente recomendada devido ao risco de flebite/lesão endotelial. Via periférica apenas em situações excepcionais com supervisão médica contínua.`):r>850&&o.push(`Osmolaridade elevada (${r.toFixed(0)} mOsm/L) - idealmente via central; periférica só em situações específicas com monitoramento rigoroso.`),t&&i){const r=t/a*100,s=i.glucoseSolution.concentration,n=i.glucoseSolution.volume,c=i.distilledWater;i.realGlucose;const m=n+c+(l||0),x=n*s/100,u=Math.abs(t-x)<.5;r>12.5&&d.push(`🚨 CONCENTRAÇÃO CRÍTICA: Glicose ${r.toFixed(1)}% excede limite máximo de segurança (12,5%). Prescrição bloqueada para proteção do paciente. 💡 SOLUÇÕES: Aumentar volume total ou reduzir TIG.`);const g=a-(l||0),p=t/5*100;if(p<=g||u||o.push(`Para ${t.toFixed(1)}g de glicose em SG5%, seriam necessários ${p.toFixed(0)}mL, mas só há ${g}mL disponíveis para diluentes. Considere usar SG10% ou reduzir TIG.`),s>5&&u){const r=1e3*t/(1440*e),i=t/a*100;o.push(`⚙️ Ajuste de glicose: TIG ${r.toFixed(1)} mg/kg/min com ${a} mL requer SG${s}%. Concentração final após diluição: ${i.toFixed(1)}% (${t.toFixed(1)}g total).`)}Math.abs(m-a)>.1&&o.push(`Volume total calculado (${m.toFixed(1)}mL) difere do volume desejado (${a}mL).`)}return{isValid:0===d.length,warnings:o,errors:d}},he=e=>{const a=e/24,r=e/1440;return{hourly:Number(a.toFixed(2)),minute:Number(r.toFixed(2)),drops:Math.round(20*a/60),microdrops:Math.round(a)}},be=e=>{const a=Math.floor(e),r=Math.round(60*(e-a));return 0===a?`${r}min`:0===r?`${a}h`:`${a}h ${r}min`},je=e=>{const a=[];let r=e;for(;r>0;){const s=Math.min(500,r),t=s/e*24,i=s/t,l=Math.round(20*i/60);a.push({volume:Math.round(s),infusionRate:Number(i.toFixed(2)),dropRate:l,infusionTime:Number(t.toFixed(2)),infusionTimeFormatted:be(t)}),r-=s}return a},fe=({aliquots:r})=>r.length?e.jsxs(a,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Taxas de Infusão (Total 24h)"}),e.jsx("div",{className:"space-y-4",children:r.map(((a,r)=>e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsxs("h4",{className:"font-medium text-gray-700 mb-2",children:["Alíquota ",r+1," (",a.volume," mL em ",a.infusionTimeFormatted||`${a.infusionTime}h`,")"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-primary"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Bomba de Infusão"}),e.jsxs("p",{className:"text-lg font-semibold text-primary",children:[a.infusionRate.toFixed(2)," mL/h"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5 text-primary"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Gotejamento"}),e.jsxs("p",{className:"text-lg font-semibold text-primary",children:[a.dropRate," gotas/min"]})]})]})]})]},r)))})]}):null,Ne=()=>{const[l,o]=F.useState(""),[d,n]=F.useState(null),{toast:c}=r();return e.jsxs("div",{className:"space-y-6",children:[e.jsx(a,{className:s.gradientCard("blue","p-6"),children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-2",children:[e.jsx("div",{className:"p-2 rounded-full bg-gradient-to-r from-green-500 to-blue-500 text-white",children:e.jsx(_,{className:"h-5 w-5"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Calculadora Simples"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Cálculo rápido de hidratação com configurações padrão"})]}),e.jsxs("div",{className:"max-w-md mx-auto space-y-4",children:[e.jsxs("label",{className:"flex items-center justify-center gap-2 text-base font-medium text-gray-700 dark:text-gray-300",children:[e.jsx(_,{className:"h-4 w-4 text-primary dark:text-blue-400"}),"Peso do Paciente (kg)"]}),e.jsxs("div",{className:"relative",children:[e.jsx(t,{type:"text",value:l,onChange:e=>{return a=e.target.value,void o(a);var a},placeholder:"Ex: 15,5 ou 15.5",className:`${s.input()} text-center text-lg font-semibold h-12 pr-12`}),e.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm font-medium",children:"kg"})]}),e.jsxs(i,{onClick:()=>{const e=(e=>{if(!e||""===e.trim())return 0;const a=e.replace(",","."),r=parseFloat(a);return isNaN(r)?0:r})(l);!e||e<3.5?c({title:"Erro",description:"Por favor, insira um peso válido (mínimo 3,5 kg).",variant:"destructive"}):n((e=>{const a=de({weight:e,electrolytes:{sodium:2,potassium:1,calcium:0,magnesium:0,tig:4},concentrations:{sodiumConcentration:"nacl_20",potassiumConcentration:"kcl_19_1",calciumConcentration:"gluconate_10",magnesiumConcentration:"mg_sulfate_50"}});return a?{dailyVolume:a.offeredVolume,hourlyRate:a.hourlyRate,minuteRate:a.minuteRate,aliquots:a.aliquots,components:{glucose:a.preparation.glucoseSolution.volume,sodium:a.components.find((e=>e.name.includes("NaCl")))?.volume||0,potassium:a.components.find((e=>e.name.includes("KCl")))?.volume||0}}:null})(e))},className:"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] h-12",size:"lg",children:[e.jsx(_,{className:"h-5 w-5 mr-2"}),"Calcular Hidratação"]})]})]})}),d&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs(a,{className:s.gradientCard("blue","p-4"),children:[e.jsx("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2",children:"Volume Diário"}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[d.dailyVolume.toFixed(2)," mL"]})]}),e.jsxs(a,{className:s.gradientCard("purple","p-4"),children:[e.jsx("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2",children:"Taxa por Hora"}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[d.hourlyRate.toFixed(2)," mL/h"]})]}),e.jsxs(a,{className:s.gradientCard("green","p-4"),children:[e.jsx("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2",children:"Taxa por Minuto"}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[d.minuteRate.toFixed(2)," mL/min"]})]})]}),e.jsx(fe,{aliquots:d.aliquots}),e.jsxs(a,{className:s.card("p-6"),children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Composição da Solução (para cada 500 mL)"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Soro Glicosado 5%"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[d.components.glucose," mL"]})]}),e.jsxs("div",{className:"p-4 bg-green-50 dark:bg-green-900/30 rounded-lg border border-green-100 dark:border-green-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"NaCl 20%"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[d.components.sodium," mL"]})]}),e.jsxs("div",{className:"p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg border border-purple-100 dark:border-purple-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"KCl 19,1%"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[d.components.potassium," mL"]})]})]})]})]})]})},ve={weight:{title:"Peso do Paciente",content:"O peso é fundamental para todos os cálculos de hidratação pediátrica, determinando necessidades hídricas, eletrolíticas e calóricas.",clinicalInfo:"O peso deve ser o mais atual possível. Em pacientes desidratados, considere o peso pré-doença. Para RN prematuros, use peso atual corrigido.",formula:"Necessidade hídrica = f(peso) segundo Holliday-Segar",examples:["RN 3kg: 300mL/dia","Lactente 8kg: 800mL/dia","Escolar 25kg: 1600mL/dia"],references:["Holliday MA, Segar WE. Pediatrics 1957","ESPGHAN Guidelines 2020"]},volume:{title:"Volume Ofertado",content:"Volume total de fluidos a ser administrado em 24 horas, incluindo hidratação de manutenção e reposições adicionais.",clinicalInfo:"Deve considerar perdas insensíveis, função renal, estado cardiovascular e necessidades metabólicas. Ajustar conforme balanço hídrico.",formula:"Volume = Manutenção + Déficit + Perdas anômalas",examples:["Manutenção: 100% necessidade","Desidratação leve: 105-110%","Perdas aumentadas: 120-150%"],references:["AAP Fluid Therapy Guidelines","NICE Intravenous Fluid Therapy"]},sodium:{title:"Sódio (Na+)",content:"Principal cátion extracelular, essencial para manutenção do volume intravascular e função neurológica.",clinicalInfo:"Necessidades variam com idade, função renal e perdas. Hiponatremia é comum em pediatria. Monitorar níveis séricos.",formula:"Volume NaCl 20% (mL) = Sódio total (mEq) ÷ 3,4 mEq/mL",examples:["RN: 2-3 mEq/kg/dia","Lactente: 2-4 mEq/kg/dia","Criança: 2-3 mEq/kg/dia"],references:["Pediatric Nephrology Guidelines","Fluid and Electrolyte Balance - Pediatrics"]},potassium:{title:"Potássio (K+)",content:"Principal cátion intracelular, fundamental para função muscular, cardíaca e neurológica.",clinicalInfo:"Essencial após 24-48h de jejum. Não administrar se oligúria/anúria. Monitorar ECG se >3mEq/kg/dia.",formula:"Volume KCl 19,1% (mL) = Potássio total (mEq) ÷ 2,55 mEq/mL",examples:["Manutenção: 1-2 mEq/kg/dia","Reposição: 2-3 mEq/kg/dia","Máximo seguro: 0,5 mEq/kg/h"],references:["Pediatric Critical Care Guidelines","Electrolyte Management in Children"]},calcium:{title:"Cálcio (Ca++)",content:"Essencial para função muscular, coagulação, transmissão nervosa e mineralização óssea.",clinicalInfo:"Importante em RN, especialmente prematuros. Monitorar sinais de hipocalcemia (Chvostek, Trousseau). Não misturar com fosfato.",formula:"Volume Gluconato Ca 10% (mL) = Cálcio elementar (mg) ÷ 9 mg/mL",examples:["RN: 40-60 mg/kg/dia","Lactente: 30-50 mg/kg/dia","Criança: 20-40 mg/kg/dia"],references:["Neonatal Calcium Homeostasis","Pediatric Mineral Metabolism"]},magnesium:{title:"Magnésio (Mg++)",content:"Cofator enzimático essencial, importante para função muscular e cardiovascular.",clinicalInfo:"Deficiência comum em desnutrição e perdas GI. Hipomagnesemia pode causar hipocalcemia refratária e arritmias.",formula:"Volume Sulfato Mg 50% (mL) = Magnésio (mEq) ÷ 4 mEq/mL",examples:["Manutenção: 0,3-0,5 mEq/kg/dia","Reposição: 0,5-1 mEq/kg/dia","Emergência: 25-50 mg/kg"],references:["Magnesium in Pediatrics","Electrolyte Disorders in Children"]},tig:{title:"TIG - Taxa de Infusão de Glicose",content:"Velocidade de administração de glicose necessária para manter glicemia e suprir necessidades metabólicas.",clinicalInfo:"Fundamental para prevenir hipoglicemia e cetose. RN precisam de TIG mais alta. Ajustar conforme glicemia capilar.",formula:"Glicose (g/dia) = TIG (mg/kg/min) × Peso (kg) × 1440 min ÷ 1000",examples:["RN: 6-8 mg/kg/min","Lactente: 4-6 mg/kg/min","Criança: 3-5 mg/kg/min"],references:["Neonatal Glucose Homeostasis","Pediatric Nutrition Guidelines"]},osmolarity:{title:"Osmolaridade",content:"Concentração total de partículas osmoticamente ativas na solução, determinando via de administração segura.",clinicalInfo:"Soluções hiperosmolares causam flebite e lesão endotelial. Via central obrigatória >600 mOsm/L. Monitorar sinais de infiltração.",formula:"Osmolaridade = Σ(Concentração × Coeficiente osmótico)",examples:["SF 0,9%: 308 mOsm/L","SG 10%: 556 mOsm/L","Solução mista: 400-800 mOsm/L"],references:["Intravenous Therapy Guidelines","Osmolarity and Tonicity in Pediatrics"]},holliday:{title:"Regra de Holliday-Segar",content:"Método padrão-ouro para calcular necessidades hídricas de manutenção em pediatria, baseado em gasto metabólico.",clinicalInfo:"Desenvolvida em 1957, ainda é referência mundial. Considera que necessidade hídrica é proporcional ao gasto calórico basal.",formula:"0-10kg: 100mL/kg/dia | 10-20kg: 1000mL + 50mL/kg(>10kg) | >20kg: 1500mL + 20mL/kg(>20kg)",examples:["5kg: 500mL/dia","15kg: 1250mL/dia","30kg: 1700mL/dia"],references:["Holliday MA, Segar WE. Pediatrics 1957","Modern Pediatric Fluid Therapy"]},dropRate:{title:"Taxa de Gotejamento",content:"Velocidade de infusão em gotas por minuto, calculada para equipos padrão de macrogotas (20 gotas/mL).",clinicalInfo:"Método tradicional de controle de infusão. Menos preciso que bombas, mas útil quando equipamentos não disponíveis.",formula:"Gotas/min = (Volume mL/h × 20 gotas/mL) ÷ 60 min/h",examples:["50mL/h = 17 gotas/min","100mL/h = 33 gotas/min","150mL/h = 50 gotas/min"],references:["Manual de Terapia Intravenosa","Nursing Drug Administration Guidelines"]},microdropRate:{title:"Taxa de Microgotas",content:"Velocidade de infusão em microgotas por minuto, onde 1 microgota equivale a 1mL/h (equipo de 60 microgotas/mL).",clinicalInfo:"Mais preciso para volumes pequenos e pediatria. Preferível para RN e lactentes. Facilita cálculo: μgts/min = mL/h.",formula:"Microgotas/min = mL/h (conversão direta 1:1)",examples:["25mL/h = 25 μgts/min","50mL/h = 50 μgts/min","100mL/h = 100 μgts/min"],references:["Pediatric IV Therapy Standards","Neonatal Fluid Management Protocols"]},calculator:{title:"Calculadora de Hidratação Pediátrica",content:"Ferramenta de apoio clínico para cálculo de hidratação venosa em pediatria, baseada em evidências científicas e diretrizes internacionais.",clinicalInfo:"Esta calculadora implementa a regra de Holliday-Segar, padrão-ouro mundial para hidratação pediátrica. Inclui validações de segurança, alertas de osmolaridade e sugestões de correção automática. Desenvolvida para auxiliar profissionais de saúde na prescrição segura de fluidos intravenosos.",formula:"Holliday-Segar: 0-10kg=100mL/kg/dia | 10-20kg=1000mL+50mL/kg | >20kg=1500mL+20mL/kg",examples:["RN 3kg: 300mL/dia + eletrólitos + glicose","Lactente 8kg: 800mL/dia com TIG 4-6 mg/kg/min","Escolar 25kg: 1600mL/dia com osmolaridade <600 mOsm/L"],references:["Holliday MA, Segar WE. The maintenance need for water in parenteral fluid therapy. Pediatrics 1957","ESPGHAN/ESPEN Guidelines on Paediatric Parenteral Nutrition 2020","AAP Clinical Practice Guideline: Fluid and Electrolyte Therapy","NICE Guidelines: Intravenous fluid therapy in children and young people"]}},ye=({type:a,className:r=""})=>{const[s,t]=F.useState(!1),m=ve[a];return m?e.jsxs(l,{open:s,onOpenChange:t,children:[e.jsx(o,{asChild:!0,children:e.jsx("button",{className:"inline-flex items-center justify-center",children:e.jsx(B,{className:`h-4 w-4 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer transition-colors ${r}`})})}),e.jsxs(d,{className:"w-[95dvw] max-w-2xl max-h-[85dvh] overflow-hidden flex flex-col",children:[e.jsx(n,{className:"pb-3 border-b border-gray-200 dark:border-gray-700",children:e.jsxs(c,{className:"flex items-center gap-2 text-lg",children:[e.jsx(U,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),e.jsx("span",{className:"text-blue-800 dark:text-blue-200",children:m.title})]})}),e.jsxs("div",{className:"flex-1 overflow-y-auto py-4 space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm",children:"📋 Definição"}),e.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed",children:m.content})]}),m.clinicalInfo&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm",children:"🩺 Considerações Clínicas"}),e.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700",children:e.jsx("p",{className:"text-sm text-blue-800 dark:text-blue-200 leading-relaxed",children:m.clinicalInfo})})]}),m.formula&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm",children:"🧮 Fórmula de Cálculo"}),e.jsx("div",{className:"p-3 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600",children:e.jsx("code",{className:"text-sm font-mono text-gray-800 dark:text-gray-200 break-all",children:m.formula})})]}),m.examples&&m.examples.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm",children:"💡 Exemplos Práticos"}),e.jsx("div",{className:"space-y-2",children:m.examples.map(((a,r)=>e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-green-600 dark:text-green-400 text-xs mt-1",children:"•"}),e.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:a})]},r)))})]}),m.references&&m.references.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm",children:"📚 Referências"}),e.jsx("div",{className:"space-y-1",children:m.references.map(((a,r)=>e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-purple-600 dark:text-purple-400 text-xs mt-1",children:"•"}),e.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400 italic",children:a})]},r)))})]})]}),e.jsx("div",{className:"pt-3 border-t border-gray-200 dark:border-gray-700",children:e.jsx(i,{onClick:()=>t(!1),className:"w-full h-8 text-sm bg-blue-600 hover:bg-blue-700 text-white",children:"✅ Entendi"})})]})]}):null},ke=({results:s,weight:t,isSimulating:b=!1})=>{const{toast:j}=r(),[f,N]=F.useState(""),[v,y]=F.useState(!1),k=()=>s?`PRESCRIÇÃO DE HIDRATAÇÃO VENOSA PEDIÁTRICA\n═══════════════════════════════════════════\n\nData: ${(new Date).toLocaleDateString("pt-BR")} - ${(new Date).toLocaleTimeString("pt-BR")}\nPaciente: [Nome do Paciente]\nRegistro: [Número do Registro]\nPeso: ${t} kg\nMédico: [Nome do Médico]\n\nCÁLCULO DE HIDRATAÇÃO (Holliday-Segar)\n─────────────────────────────────────────\n• Necessidade hídrica: ${s.dailyNeed} mL/dia\n• Volume ofertado: ${s.offeredVolume} mL/dia (${s.hydrationPercentage.toFixed(0)}% da necessidade)\n• Taxa horária: ${s.hourlyRate} mL/h\n• Gotejamento: ${s.dropRate} gotas/min\n• Microgotas: ${s.microdropRate} μgts/min\n\nCOMPOSIÇÃO DA SOLUÇÃO\n─────────────────────────────────────────\n${s.components.map((e=>`• ${e.name}: ${e.volume} mL (${e.concentration} ${e.unit})`)).join("\n")}\n\nPREPARO\n─────────────────────────────────────────\n• Soro Glicosado ${s.preparation.glucoseSolution.concentration}%: ${s.preparation.glucoseSolution.volume} mL\n• Água Destilada: ${s.preparation.distilledWater} mL\n\nINFORMAÇÕES NUTRICIONAIS\n─────────────────────────────────────────\n• Glicose total: ${s.nutrition.totalGlucose} g\n• Calorias totais: ${s.nutrition.totalCalories} kcal\n• Concentração de glicose: ${s.nutrition.glucoseConcentration}%\n\nPARÂMETROS DE SEGURANÇA\n─────────────────────────────────────────\n• Osmolaridade: ${s.osmolarity} mOsm/L\n• Concentração de glicose: ${s.nutrition.glucoseConcentration}%\n\n${s.osmolarity>1800||s.nutrition.glucoseConcentration>12.5?`\n🚨🚨🚨 ATENÇÃO CRÍTICA 🚨🚨🚨\nPRESCRIÇÃO COM PARÂMETROS DE ALTO RISCO\n${s.nutrition.glucoseConcentration>12.5?`• GLICOSE ${s.nutrition.glucoseConcentration}% > 12,5% (RISCO DE FLEBITE GRAVE)`:""}\n${s.osmolarity>1800?`• OSMOLARIDADE ${s.osmolarity} mOsm/L > 1800 (RISCO DE LESÃO ENDOTELIAL)`:""}\nREQUER VALIDAÇÃO MÉDICA EXPLÍCITA ANTES DA ADMINISTRAÇÃO\n`:""}\n\nVIA DE ADMINISTRAÇÃO\n─────────────────────────────────────────\n${s.osmolarity>900?"🚨 ACESSO VENOSO CENTRAL OBRIGATÓRIO":s.osmolarity>600?"⚠️ ACESSO VENOSO CENTRAL RECOMENDADO":"• Via periférica ou central conforme protocolo"}\n${s.osmolarity>1500?"🚨 MONITORIZAÇÃO CONTÍNUA DE SINAIS VITAIS":""}\n${s.osmolarity>900?"⚠️ Verificar permeabilidade do cateter antes da infusão":""}\n\n${s.aliquots.length>1?`\nDIVISÃO EM ALÍQUOTAS\n─────────────────────────────────────────\n${s.aliquots.map(((e,a)=>`• Alíquota ${a+1}: ${e.volume} mL em ${e.infusionTimeFormatted||`${e.infusionTime}h`} (${e.infusionRate} mL/h)\n  Gotejamento: ${Math.round(e.infusionRate/3)} gotas/min`)).join("\n")}\n`:""}\n\n${s.validation.warnings.length>0?`\nALERTAS CLÍNICOS\n─────────────────────────────────────────\n${s.validation.warnings.map((e=>`⚠️ ${e}`)).join("\n")}\n`:""}\n\n─────────────────────────────────────────\nCalculado por: PedBook - Calculadora de Hidratação Pediátrica\nBaseado na regra de Holliday-Segar e diretrizes internacionais\nEsta prescrição deve ser validada pelo médico responsável`.trim():"Aguardando cálculo da hidratação...";F.useEffect((()=>{s&&N(k())}),[s,t]),F.useEffect((()=>{if(s){const e=k();N(e)}}),[s?.osmolarity,s?.offeredVolume,s?.dailyNeed,t]);const C=s&&!s.validation.isValid,w=s&&s.validation.warnings.length>0;return e.jsx(a,{className:"p-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsxs(l,{children:[e.jsx(o,{asChild:!0,children:e.jsx(i,{variant:"outline",className:"w-full max-w-md h-12 transition-all duration-300 shadow-md hover:shadow-lg px-2 sm:px-4 "+(C?"bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-300 dark:border-red-700 hover:from-red-100 hover:to-orange-100 dark:hover:from-red-900/30 dark:hover:to-orange-900/30":w?"bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 border-amber-300 dark:border-amber-700 hover:from-amber-100 hover:to-yellow-100 dark:hover:from-amber-900/30 dark:hover:to-yellow-900/30":"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30"),children:e.jsx("div",{className:"flex items-center gap-1 sm:gap-2 justify-center w-full",children:C?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0",children:[e.jsx(m,{className:"h-3 w-3 sm:h-4 sm:w-4 text-red-600 dark:text-red-400"}),e.jsx(x,{className:"h-3 w-3 sm:h-4 sm:w-4 text-red-600 dark:text-red-400"})]}),e.jsxs("span",{className:"font-semibold text-red-700 dark:text-red-300 text-center leading-tight",children:[e.jsx("span",{className:"hidden sm:inline",children:"Prescrição Bloqueada"}),e.jsx("span",{className:"sm:hidden",children:"Bloqueada"})]})]}):w?e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"}),e.jsx("span",{className:"font-medium text-amber-700 dark:text-amber-300",children:"Visualizar Prescrição"})]}):e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),e.jsx("span",{className:"font-medium text-blue-700 dark:text-blue-300",children:"Visualizar Prescrição"})]})})})}),e.jsxs(d,{className:"max-w-4xl max-h-[80vh] overflow-hidden flex flex-col",children:[e.jsx(n,{className:"pb-4 border-b border-gray-200 dark:border-gray-700",children:e.jsx(c,{className:"flex items-center gap-2 pr-8",children:C?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(m,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),e.jsx(x,{className:"h-5 w-5 text-red-600 dark:text-red-400"})]}),e.jsx("span",{className:"text-red-700 dark:text-red-300",children:"Prescrição Bloqueada por Segurança"})]}):w?e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-5 w-5 text-amber-600 dark:text-amber-400"}),e.jsx("span",{className:"text-amber-700 dark:text-amber-300",children:"Editor de Prescrição (Com Alertas)"})]}):e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),e.jsx("span",{className:"text-blue-700 dark:text-blue-300",children:"Editor de Prescrição"})]})})}),C?e.jsxs("div",{className:"flex-1 mt-4 space-y-4 overflow-y-auto max-h-[60vh]",children:[e.jsx("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg",children:e.jsx("div",{className:"flex items-start",children:e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-red-800 dark:text-red-200 mb-2",children:"Prescrição Bloqueada por Parâmetros Críticos"}),e.jsx("p",{className:"text-sm text-red-700 dark:text-red-300 mb-3",children:"Esta prescrição foi automaticamente bloqueada devido a parâmetros que excedem os limites de segurança estabelecidos pelas diretrizes clínicas."}),s?.validation.errors&&s.validation.errors.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"font-medium text-red-800 dark:text-red-200 text-sm",children:"Problemas identificados:"}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-red-700 dark:text-red-300",children:s.validation.errors.map(((a,r)=>e.jsx("li",{children:a},r)))})]}),e.jsx("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded",children:e.jsxs("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[e.jsx("strong",{children:"💡 Para desbloquear:"})," Ajuste os parâmetros usando os botões de correção sugeridos na calculadora ou reduza manualmente os valores que excedem os limites de segurança."]})})]})})}),e.jsx(g,{value:"═══════════════════════════════════════════\n🚨 PRESCRIÇÃO BLOQUEADA POR SEGURANÇA 🚨\n═══════════════════════════════════════════\n\nEsta prescrição foi automaticamente bloqueada devido a\nparâmetros que excedem os limites de segurança estabelecidos\npelas diretrizes clínicas internacionais.\n\n\n📋 PARA DESBLOQUEAR A PRESCRIÇÃO:\n─────────────────────────────────────────\n\n1. Ajuste os parâmetros usando os botões de correção\n   sugeridos na calculadora\n\n2. Ou reduza manualmente os valores que excedem os\n   limites de segurança\n\n3. Recalcule a hidratação após os ajustes\n\n\n⚠️  PARÂMETROS QUE CAUSAM BLOQUEIO:\n─────────────────────────────────────────\n\n• Osmolaridade > 1800 mOsm/L\n• Concentração de glicose > 12,5%\n• Volume excessivo para o peso\n• Eletrólitos em concentrações críticas\n\n\n🛡️  SEGURANÇA EM PRIMEIRO LUGAR\n─────────────────────────────────────────\n\nA segurança do paciente é nossa prioridade máxima.\nEste bloqueio automático previne prescrições que podem\ncausar danos graves como flebite, lesão endotelial ou\nsobrecarga hídrica.\n\n═══════════════════════════════════════════",readOnly:!0,className:"min-h-[300px] font-mono text-xs resize-none bg-gray-50 dark:bg-gray-800/50 text-gray-500 dark:text-gray-400",placeholder:"Prescrição bloqueada por segurança..."})]}):e.jsxs("div",{className:"flex-1 mt-4 space-y-4 overflow-y-auto max-h-[60vh]",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:w?e.jsxs("div",{className:"flex items-center gap-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded",children:[e.jsx(x,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"}),e.jsx("span",{className:"text-amber-700 dark:text-amber-300",children:"Prescrição com alertas de segurança. Edite conforme necessário:"})]}):"Edite o texto da prescrição conforme necessário:"}),e.jsx(g,{value:f,onChange:e=>N(e.target.value),className:"min-h-[400px] font-mono text-xs resize-none",placeholder:"Prescrição será carregada aqui..."})]}),e.jsxs(p,{className:"mt-4 flex gap-3",children:[e.jsx(i,{onClick:async()=>{try{await navigator.clipboard.writeText(f),y(!0),j({title:"Copiado!",description:"Prescrição copiada para a área de transferência.",variant:"default"}),setTimeout((()=>y(!1)),2e3)}catch(e){j({title:"Erro",description:"Não foi possível copiar o texto.",variant:"destructive"})}},variant:"outline",disabled:C,className:C?"bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed":"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-800 hover:from-green-100 hover:to-blue-100 dark:hover:from-green-900/30 dark:hover:to-blue-900/30",children:v?e.jsxs(e.Fragment,{children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Copiado!"]}):e.jsxs(e.Fragment,{children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),C?"Bloqueado":"Copiar"]})}),e.jsxs(i,{onClick:()=>{const e=new Blob([f],{type:"text/plain;charset=utf-8"}),a=URL.createObjectURL(e),r=document.createElement("a");r.href=a,r.download=`hidratacao_${(new Date).toISOString().split("T")[0]}.txt`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a),j({title:"Download realizado!",description:"Arquivo da prescrição baixado com sucesso.",variant:"default"})},disabled:C,className:C?"bg-gray-400 dark:bg-gray-700 text-gray-200 dark:text-gray-400 cursor-not-allowed hover:bg-gray-400 dark:hover:bg-gray-700":"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl",children:[e.jsx(W,{className:"h-4 w-4 mr-2"}),C?"Bloqueado":"Baixar"]})]})]})]})})})},Ce=850,we=1200,Se=1800,Ee=[{maxWeight:2.5,maxTIG:6,description:"prematuro extremo"},{maxWeight:5,maxTIG:7,description:"neonato"},{maxWeight:10,maxTIG:8,description:"lactente jovem"},{maxWeight:20,maxTIG:10,description:"criança pequena"}],Le={level:"safe",classification:"Segura para periférica",conduct:"Pode ser administrada por via periférica com segurança",color:"green",icon:"✅"},Oe={level:"elevated",classification:"Elevada (atenção)",conduct:"Via central é preferencial. Caso via periférica seja necessária, requer avaliação médica e monitoramento rigoroso",color:"yellow",icon:"⚠️"},Ae={level:"very_high",classification:"Muito elevada",conduct:"Via central é fortemente recomendada devido ao risco elevado de flebite e lesão endotelial. Via periférica apenas em situações excepcionais com supervisão médica contínua",color:"orange",icon:"🚨"},Ie={level:"critical",classification:"Crítica",conduct:"Valores acima de 1800 mOsm/L apresentam risco muito elevado. Recomenda-se reduzir osmolaridade ou aumentar volume antes da administração",color:"red",icon:"🔴"},Pe=[{concentration:5,name:"SG5%",maxTheoretical:4},{concentration:10,name:"SG10%",maxTheoretical:8},{concentration:25,name:"SG25%",maxTheoretical:20}],Re=e=>e<=10?100*e:e<=20?1e3+50*(e-10):1500+20*(e-20),Te=e=>{const a=Re(e);return Math.min(1.5*a,150*e)},Ge=e=>{if(!e)return 0;const a=e.replace(",","."),r=parseFloat(a);return isNaN(r)?0:r},Me=e=>e<=Ce?Le:e<=we?Oe:e<=Se?Ae:Ie,Fe=(e,a,r,s)=>{const t=a*e*1440/1e3,i=t/r*100;let l="",o=0,d="",n=!1,c=!0;const m=Pe.find((e=>i<=e.maxTheoretical));return m?(l=m.name,o=m.concentration,5===m.concentration?d=`Volume adequado permite uso de SG5%. Após diluição com eletrólitos e água, a concentração final será de aproximadamente ${s.toFixed(1)}%`:10===m.concentration?(d=`Para atingir TIG de ${a} mg/kg/min com volume de ${r} mL, é necessário SG10%. Após diluição com eletrólitos e água, a concentração final ajustada é de ${s.toFixed(1)}%`,n=!0):25===m.concentration&&(d=`Volume limitado requer SG25% para atingir a TIG desejada. Concentração final após diluição: ${s.toFixed(1)}%. Recomenda-se bomba de infusão com controle rigoroso`,n=!0)):(l="Inviável",o=0,d=`TIG de ${a} mg/kg/min é inalcançável com volume de ${r} mL, mesmo com SG50%. Considere aumentar volume ou reduzir TIG`,c=!1),{glucoseNeeded:t,theoreticalConcentration:i,sgType:l,sgConcentration:o,recommendation:d,isViable:c,needsAdjustment:n,currentConcentration:s,dilutionExplanation:`A concentração final (${s.toFixed(1)}%) é menor que o SG usado devido à diluição com eletrólitos e água destilada`}},qe=[{name:"Manutenção Padrão",description:"Hidratação de manutenção para pacientes estáveis",icon:e.jsx(V,{className:"h-4 w-4"}),color:"bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800",electrolytes:{sodium:2,potassium:1,calcium:0,magnesium:0,tig:4},concentrations:{sodiumConcentration:"nacl_20",potassiumConcentration:"kcl_19_1",calciumConcentration:"gluconate_10",magnesiumConcentration:"mg_sulfate_50"},indications:["Paciente estável","Hidratação de rotina","Pós-operatório simples"]},{name:"Manutenção + Eletrólitos",description:"Hidratação com reposição completa de eletrólitos",icon:e.jsx(Q,{className:"h-4 w-4"}),color:"bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-800",electrolytes:{sodium:3,potassium:2,calcium:30,magnesium:.4,tig:5},concentrations:{sodiumConcentration:"nacl_20",potassiumConcentration:"kcl_19_1",calciumConcentration:"gluconate_10",magnesiumConcentration:"mg_sulfate_50"},indications:["Jejum prolongado","Perdas gastrintestinais","Desnutrição"]},{name:"Cardiopata",description:"Hidratação restritiva para cardiopatas com restrição hídrica e energética controlada",icon:e.jsx("span",{className:"text-red-500",children:"♥"}),color:"bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-800",electrolytes:{sodium:1.5,potassium:1,calcium:30,magnesium:.3,tig:3},concentrations:{sodiumConcentration:"nacl_20",potassiumConcentration:"kcl_19_1",calciumConcentration:"gluconate_10",magnesiumConcentration:"mg_sulfate_50"},indications:["Cardiopatia congênita","Insuficiência cardíaca","Restrição hídrica","Edema pulmonar"]},{name:"Neonatal",description:"Hidratação específica para recém-nascidos e prematuros com doses seguras",icon:e.jsx("span",{className:"text-purple-500",children:"👶"}),color:"bg-purple-50 dark:bg-purple-900/30 border-purple-200 dark:border-purple-800",electrolytes:{sodium:2.5,potassium:1.5,calcium:50,magnesium:.5,tig:6},concentrations:{sodiumConcentration:"nacl_20",potassiumConcentration:"kcl_19_1",calciumConcentration:"gluconate_10",magnesiumConcentration:"mg_sulfate_50"},indications:["Recém-nascido","Prematuro","Primeira semana de vida","Baixo peso ao nascer"]}],$e=()=>{const[m,u]=F.useState(""),[g,h]=F.useState("1000"),[O,A]=F.useState({sodium:2,potassium:1,calcium:0,magnesium:0,tig:4}),[I,P]=F.useState({sodium:"2",potassium:"1",calcium:"0",magnesium:"0",tig:"4"}),[R,T]=F.useState({sodiumConcentration:"nacl_20",potassiumConcentration:"kcl_19_1",calciumConcentration:"gluconate_10",magnesiumConcentration:"mg_sulfate_50"}),[G,M]=F.useState(null),[B,U]=F.useState(!1),[K,W]=F.useState(!1),[J,X]=F.useState(null),[Y,Z]=F.useState(!1),[ee,ae]=F.useState(!1),[re,se]=F.useState({open:!1,type:"",title:"",description:"",action:()=>{}}),{toast:te}=r(),ie=e=>{const a=Ge(m),r=e?.offeredVolume||g,s=e?.electrolytes||O;if(be().length>0)return void ge(!0);const t={weight:a,offeredVolume:Ge(r)||void 0,electrolytes:s,concentrations:R},i=de(t);if(i){if(!i.validation.isValid)return M(i),void te({title:"Parâmetros Críticos Detectados",description:"Cálculo realizado, mas prescrição bloqueada por segurança. Veja os alertas abaixo.",variant:"destructive"});M(i),te({title:"Cálculo Realizado",description:"Hidratação calculada com sucesso!",variant:"default"})}else te({title:"Erro",description:"Não foi possível calcular a hidratação.",variant:"destructive"})},le=(e,a,r)=>{let s="",t="",i=()=>{};switch(e){case"volume":s="🔼 Confirmar Ajuste de Volume",t=`Você está prestes a alterar o volume de ${r} mL para ${a} mL.\n\n✅ Este ajuste irá:\n• Reduzir a osmolaridade da solução\n• Melhorar a segurança para administração\n• Manter as doses de eletrólitos proporcionais\n• Pode aumentar a velocidade de infusão\n\n⚠️ Considere:\n• Verificar se o paciente tolera maior volume\n• Ajustar velocidade de infusão se necessário`,i=()=>ne("volume",a);break;case"tig":s="🔻 Confirmar Ajuste de TIG",t=`Você está prestes a alterar a TIG de ${r} mg/kg/min para ${a} mg/kg/min.\n\n✅ Este ajuste irá:\n• Reduzir a concentração de glicose necessária\n• Permitir uso de SG com menor concentração\n• Manter o aporte calórico adequado\n• Melhorar a segurança da prescrição\n\n⚠️ Considere:\n• Monitorar glicemia mais frequentemente\n• Avaliar necessidade nutricional total`,i=()=>ne("tig",a);break;case"electrolytes":const e=r,l=a;s="🔻 Confirmar Redução de Eletrólitos",t=`Você está prestes a reduzir os eletrólitos:\n\n📊 Alterações propostas:\n• Sódio: ${e.sodium} → ${l.sodium} mEq/kg/dia\n• Cálcio: ${e.calcium} → ${l.calcium} mg/kg/dia\n• Magnésio: ${e.magnesium} → ${l.magnesium} mEq/kg/dia\n\n✅ Este ajuste irá:\n• Reduzir significativamente a osmolaridade\n• Permitir administração mais segura\n• Manter eletrólitos em níveis mínimos adequados\n\n⚠️ Considere:\n• Monitorar eletrólitos séricos mais frequentemente\n• Avaliar necessidade de suplementação adicional`,i=()=>ne("electrolytes",l);break;case"multiple":s="⚖️ Explorar Soluções Múltiplas",t="💡 Sugestões automáticas do sistema:\n\n🔹 Opção 1: Dividir em 2 soluções\n• Solução A: Eletrólitos básicos (Na+, K+)\n• Solução B: Cálcio e magnésio separados\n• Reduz osmolaridade individual\n\n🔹 Opção 2: Escalonamento gradual\n• Iniciar com concentrações reduzidas\n• Aumentar progressivamente conforme tolerância\n\n🔹 Opção 3: Via central obrigatória\n• Manter concentrações atuais\n• Garantir acesso venoso central adequado\n\n⚠️ Estas são sugestões gerais. Sempre considere o contexto clínico específico do paciente.",i=()=>{te({title:"💡 Sugestões Registradas",description:"Considere as opções apresentadas conforme o contexto clínico do paciente.",variant:"default"})};break;default:return}se({open:!0,type:e,title:s,description:t,action:i})},ne=(e,a)=>{if("volume"===e){const e=parseFloat(m)||0,r=Te(e);if(a>r)return void te({title:"Volume Muito Alto",description:`Volume de ${a}mL é muito alto para peso ${e}kg (máximo seguro: ${r}mL). Considere reduzir TIG ou eletrólitos.`,variant:"destructive"})}q.flushSync((()=>{switch(e){case"volume":h(a.toString());break;case"tig":A((e=>({...e,tig:a}))),P((e=>({...e,tig:a.toString()})));break;case"electrolytes":A((e=>({...e,...a}))),P((e=>({...e,...Object.fromEntries(Object.entries(a).map((([e,a])=>[e,a.toString()])))})))}}));const r={};"volume"===e?r.offeredVolume=a.toString():"tig"===e?r.electrolytes={...O,tig:a}:"electrolytes"===e&&(r.electrolytes={...O,...a}),ie(r),setTimeout((()=>{se({open:!0,type:"success",title:"✅ Correção Aplicada com Sucesso!",description:`🎯 **Alteração realizada:**\n${"volume"===e?`Volume atualizado para ${a}mL`:"tig"===e?`TIG ajustada para ${a} mg/kg/min`:"Eletrólitos reduzidos conforme sugerido"}\n\n🔄 **Recálculo automático:**\nA hidratação foi recalculada automaticamente com os novos valores.\n\n📊 **Próximos passos:**\n• Verifique os novos resultados abaixo\n• Confirme se os parâmetros estão adequados\n• Proceda com a prescrição se aprovado`,action:()=>{te({title:"✅ Correção Confirmada",description:"Valores aplicados e hidratação recalculada com sucesso!",variant:"default"})}})}),500),te({title:"Correção Aplicada",description:"Valores atualizados: "+("volume"===e?`volume ${a}mL`:"tig"===e?`TIG ${a} mg/kg/min`:"eletrólitos reduzidos"),variant:"default"})},ce=(e,a)=>{P((r=>({...r,[e]:a}))),A((r=>({...r,[e]:Ge(a)})))},me=e=>{const a=Math.abs(O.sodium-e.electrolytes.sodium)<.1&&Math.abs(O.potassium-e.electrolytes.potassium)<.1&&Math.abs(O.calcium-e.electrolytes.calcium)<1&&Math.abs(O.magnesium-e.electrolytes.magnesium)<.1&&Math.abs(O.tig-e.electrolytes.tig)<.1,r=R.sodiumConcentration===e.concentrations.sodiumConcentration&&R.potassiumConcentration===e.concentrations.potassiumConcentration&&R.calciumConcentration===e.concentrations.calciumConcentration&&R.magnesiumConcentration===e.concentrations.magnesiumConcentration;return a&&r},xe=(e,a)=>{T((r=>({...r,[e]:a})))},[ue,ge]=F.useState(!1),pe=a=>{const r=a.split(/(\. )/g);return e.jsx("div",{className:"space-y-2",children:r.map(((a,r)=>{if(". "===a)return null;const s=a.split(/(\d+(?:\.\d+)?%?|\d+(?:\.\d+)?\s*mOsm\/L|OSMOLARIDADE CRÍTICA|CONCENTRAÇÃO CRÍTICA|💡 SOLUÇÕES|SOLUÇÕES|TIG|SG\d+%)/g);return e.jsx("div",{className:"leading-relaxed",children:s.map(((a,r)=>a.includes("OSMOLARIDADE CRÍTICA")||a.includes("CONCENTRAÇÃO CRÍTICA")?e.jsx("span",{className:"font-bold text-red-800 dark:text-red-200 bg-red-200 dark:bg-red-700/50 px-2 py-1 rounded",children:a},r):"SOLUÇÕES"===a||"💡 SOLUÇÕES"===a?e.jsx("span",{className:"font-bold text-blue-800 dark:text-blue-200 bg-blue-200 dark:bg-blue-700/50 px-2 py-1 rounded ml-2",children:"💡 SOLUÇÕES"},r):/\d+(?:\.\d+)?%|\d+(?:\.\d+)?\s*mOsm\/L/.test(a)?e.jsx("span",{className:"font-semibold text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-800/30 px-1 rounded",children:a},r):"TIG"===a||/SG\d+%/.test(a)?e.jsx("span",{className:"font-medium text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-800/30 px-1 rounded",children:a},r):e.jsx("span",{children:a},r)))},r)}))})},he=a=>{const r=a.split(/(\. )/g);return e.jsx("div",{className:"space-y-2",children:r.map(((a,r)=>{if(". "===a)return null;const s=a.split(/(\d+(?:\.\d+)?%?|\d+(?:\.\d+)?\s*mOsm\/L|TIG \d+(?:\.\d+)? mg\/kg\/min|SG\d+%|⚙️ Ajuste de glicose|Ajuste de glicose|⚠️ Osmolaridade|Osmolaridade)/g);return e.jsx("div",{className:"leading-relaxed",children:s.map(((a,r)=>a.includes("Ajuste de glicose")||a.includes("Osmolaridade")?e.jsx("span",{className:"font-bold text-amber-800 dark:text-amber-200 bg-amber-100 dark:bg-amber-800/30 px-2 py-1 rounded",children:a},r):/\d+(?:\.\d+)?%|\d+(?:\.\d+)?\s*mOsm\/L|TIG \d+(?:\.\d+)? mg\/kg\/min/.test(a)?e.jsx("span",{className:"font-semibold text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-800/30 px-1 rounded",children:a},r):/SG\d+%/.test(a)?e.jsx("span",{className:"font-medium text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-800/30 px-1 rounded",children:a},r):e.jsx("span",{children:a},r)))},r)}))})},be=()=>{const e=[],a=Ge(m);return(!a||a<2)&&e.push("Peso do paciente (mínimo 2,0 kg)"),e};return e.jsxs("div",{className:"space-y-6",children:[e.jsx(l,{open:ue,onOpenChange:ge,children:e.jsxs(d,{className:"max-w-md",children:[e.jsxs(n,{children:[e.jsxs(c,{className:"flex items-center gap-2 text-amber-800 dark:text-amber-200",children:[e.jsx(x,{className:"h-5 w-5"}),"📝 Dados Obrigatórios"]}),e.jsx(b,{className:"text-amber-700 dark:text-amber-300",children:"Para realizar o cálculo, preencha os seguintes campos:"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("ul",{className:"list-disc list-inside space-y-1 text-sm text-amber-700 dark:text-amber-300",children:be().map(((a,r)=>e.jsx("li",{children:a},r)))}),e.jsx("div",{className:"flex justify-end",children:e.jsx(i,{onClick:()=>ge(!1),className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Entendi"})})]})]})}),e.jsx(a,{className:s.card("p-6"),children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(j,{className:"h-6 w-6 text-primary dark:text-blue-400"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Dados do Paciente"})]}),e.jsxs(l,{open:B,onOpenChange:U,children:[e.jsx(o,{asChild:!0,children:e.jsxs(i,{variant:"outline",size:"sm",className:"text-xs",children:[e.jsx(f,{className:"h-3 w-3 mr-1"}),"Presets"]})}),e.jsxs(d,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[e.jsxs(n,{children:[e.jsxs(c,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5"}),"Presets Clínicos"]}),e.jsx(b,{children:"Selecione uma configuração pré-definida baseada em protocolos clínicos estabelecidos. Os presets incluem tanto eletrólitos quanto concentrações otimizadas."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:qe.map(((a,r)=>e.jsxs("div",{className:`p-4 rounded-lg border transition-all cursor-pointer ${a.color} ${me(a)?"ring-2 ring-blue-500 dark:ring-blue-400":"hover:shadow-md hover:scale-[1.02]"}`,children:[e.jsx("div",{className:"flex items-start justify-between mb-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[a.icon,e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200",children:a.name}),me(a)&&e.jsx(N,{variant:"secondary",className:"text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-700",children:"✓ Ativo"})]})}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:a.description}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsx("div",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:"Eletrólitos:"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400",children:[e.jsxs("div",{children:["Na+: ",a.electrolytes.sodium," mEq/kg/dia"]}),e.jsxs("div",{children:["K+: ",a.electrolytes.potassium," mEq/kg/dia"]}),e.jsxs("div",{children:["Ca²⁺: ",a.electrolytes.calcium," mg/kg/dia"]}),e.jsxs("div",{children:["Mg²⁺: ",a.electrolytes.magnesium," mEq/kg/dia"]}),e.jsxs("div",{className:"col-span-2",children:["TIG: ",a.electrolytes.tig," mg/kg/min"]})]}),e.jsx("div",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:"Concentrações:"}),e.jsxs("div",{className:"grid grid-cols-1 gap-1 text-xs text-gray-600 dark:text-gray-400",children:[e.jsxs("div",{children:["NaCl: ",oe(a.concentrations.sodiumConcentration)]}),e.jsxs("div",{children:["KCl: ",oe(a.concentrations.potassiumConcentration)]})]})]}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsx("div",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:"Indicações:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:a.indications.map(((a,r)=>e.jsx(N,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/30",children:a},r)))})]}),e.jsx(i,{onClick:()=>(e=>{A(e.electrolytes),P({sodium:e.electrolytes.sodium.toString(),potassium:e.electrolytes.potassium.toString(),calcium:e.electrolytes.calcium.toString(),magnesium:e.electrolytes.magnesium.toString(),tig:e.electrolytes.tig.toString()}),T(e.concentrations),U(!1),te({title:"Preset Aplicado",description:`Configuração "${e.name}" aplicada com sucesso!`,variant:"default"})})(a),variant:me(a)?"secondary":"default",size:"sm",className:"w-full transition-all duration-200 "+(me(a)?"bg-green-500 hover:bg-green-600 text-white":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"),disabled:me(a),children:me(a)?"✓ Em uso":"Aplicar Preset"})]},r)))}),e.jsx("div",{className:"mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800",children:e.jsxs("p",{className:"text-xs text-amber-800 dark:text-amber-200",children:[e.jsx("strong",{children:"Nota:"})," Os presets são sugestões baseadas em protocolos padrão. Sempre ajuste conforme a condição clínica específica do paciente."]})})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"flex items-center gap-2 text-base font-medium",children:[e.jsx(_,{className:"h-4 w-4 text-primary dark:text-blue-400"}),"Peso (kg) *",e.jsx(ye,{type:"weight"})]}),e.jsx(t,{type:"text",value:m,onChange:e=>u(e.target.value),placeholder:"Ex: 15,5 ou 15.5",className:s.input()})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"flex items-center gap-2 text-base font-medium",children:[e.jsx(V,{className:"h-4 w-4 text-primary dark:text-blue-400"}),"Volume Ofertado (mL)",e.jsx(ye,{type:"volume"})]}),e.jsx(t,{type:"text",value:g,onChange:e=>h(e.target.value),placeholder:"Ex: 1200 ou 1200,5",className:s.input()})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(Q,{className:"h-5 w-5 text-primary dark:text-blue-400"}),"Eletrólitos e Nutrição"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[e.jsxs(v,{className:"text-sm font-medium flex items-center gap-2 mb-2 text-blue-700 dark:text-blue-300",children:[e.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Sódio (mEq/kg/dia)",e.jsx(ye,{type:"sodium"})]}),e.jsx(t,{type:"text",value:I.sodium,onChange:e=>ce("sodium",e.target.value),className:`${s.input()} text-center font-semibold`,placeholder:"2,0 ou 2.0"})]}),e.jsxs("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:[e.jsxs(v,{className:"text-sm font-medium flex items-center gap-2 mb-2 text-green-700 dark:text-green-300",children:[e.jsx("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Potássio (mEq/kg/dia)",e.jsx(ye,{type:"potassium"})]}),e.jsx(t,{type:"text",value:I.potassium,onChange:e=>ce("potassium",e.target.value),className:`${s.input()} text-center font-semibold`,placeholder:"1,0 ou 1.0"})]}),e.jsxs("div",{className:"p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800",children:[e.jsxs(v,{className:"text-sm font-medium flex items-center gap-2 mb-2 text-orange-700 dark:text-orange-300",children:[e.jsx("span",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"Cálcio (mg/kg/dia)",e.jsx(ye,{type:"calcium"})]}),e.jsx(t,{type:"text",value:I.calcium,onChange:e=>ce("calcium",e.target.value),className:`${s.input()} text-center font-semibold`,placeholder:"0 ou 30,5"})]}),e.jsxs("div",{className:"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800",children:[e.jsxs(v,{className:"text-sm font-medium flex items-center gap-2 mb-2 text-purple-700 dark:text-purple-300",children:[e.jsx("span",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"Magnésio (mEq/kg/dia)",e.jsx(ye,{type:"magnesium"})]}),e.jsx(t,{type:"text",value:I.magnesium,onChange:e=>ce("magnesium",e.target.value),className:`${s.input()} text-center font-semibold`,placeholder:"0,0 ou 0.5"})]}),e.jsxs("div",{className:"p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800",children:[e.jsxs(v,{className:"text-sm font-medium flex items-center gap-2 mb-2 text-yellow-700 dark:text-yellow-300",children:[e.jsx("span",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),"TIG (mg/kg/min)",e.jsx(ye,{type:"tig"})]}),e.jsx(t,{type:"text",value:I.tig,onChange:e=>ce("tig",e.target.value),className:`${s.input()} text-center font-semibold`,placeholder:"4,0 ou 4.5"}),(()=>{const a=parseFloat(m)||0,r=O.tig,s=((e,a)=>{if(e<2)return"TIG muito baixa (mínimo 2 mg/kg/min)";for(const r of Ee)if(a<r.maxWeight&&e>r.maxTIG)return`TIG alta para ${r.description} (máximo ${r.maxTIG} mg/kg/min)`;return e>12?"TIG muito alta (máximo 12 mg/kg/min)":null})(r,a);return s&&a>0&&r>0?e.jsx("div",{className:"mt-1 p-2 bg-amber-50 dark:bg-amber-900/20 rounded border border-amber-200 dark:border-amber-700",children:e.jsxs("p",{className:"text-xs text-amber-800 dark:text-amber-200",children:["⚠️ ",s]})}):null})()]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2",children:[e.jsx(Q,{className:"h-5 w-5 text-primary dark:text-blue-400"}),"Concentrações dos Medicamentos"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"text-sm flex items-center gap-1",children:["Sódio (NaCl)",e.jsx(ye,{type:"sodium"})]}),e.jsxs(y,{value:R.sodiumConcentration,onValueChange:e=>xe("sodiumConcentration",e),children:[e.jsx(k,{className:s.input(),children:e.jsx(C,{})}),e.jsxs(w,{children:[e.jsx(S,{value:"nacl_20",children:"✅ NaCl 20% (3,4 mEq/mL) - Mais comum"}),e.jsx(S,{value:"nacl_17_5",children:"NaCl 17,5% (2,975 mEq/mL)"}),e.jsx(S,{value:"nacl_10",children:"NaCl 10% (1,71 mEq/mL)"}),e.jsx(S,{value:"nacl_0_9",children:"NaCl 0,9% (0,154 mEq/mL)"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"text-sm flex items-center gap-1",children:["Potássio (KCl)",e.jsx(ye,{type:"potassium"})]}),e.jsxs(y,{value:R.potassiumConcentration,onValueChange:e=>xe("potassiumConcentration",e),children:[e.jsx(k,{className:s.input(),children:e.jsx(C,{})}),e.jsxs(w,{children:[e.jsx(S,{value:"kcl_19_1",children:"✅ KCl 19,1% (2,55 mEq/mL) - Padrão hospitalar"}),e.jsx(S,{value:"kcl_10",children:"KCl 10% (1,34 mEq/mL) - Menos comum"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"text-sm flex items-center gap-1",children:["Cálcio",e.jsx(ye,{type:"calcium"})]}),e.jsxs(y,{value:R.calciumConcentration,onValueChange:e=>xe("calciumConcentration",e),children:[e.jsx(k,{className:s.input(),children:e.jsx(C,{})}),e.jsx(w,{children:e.jsx(S,{value:"gluconate_10",children:"Gluconato de Cálcio 10% (100 mg/mL)"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(v,{className:"text-sm flex items-center gap-1",children:["Magnésio",e.jsx(ye,{type:"magnesium"})]}),e.jsxs(y,{value:R.magnesiumConcentration,onValueChange:e=>xe("magnesiumConcentration",e),children:[e.jsx(k,{className:s.input(),children:e.jsx(C,{})}),e.jsxs(w,{children:[e.jsx(S,{value:"mg_sulfate_50",children:"Sulfato de Magnésio 50% (4 mEq/mL)"}),e.jsx(S,{value:"mg_sulfate_10",children:"Sulfato de Magnésio 10% (0,8 mEq/mL)"})]})]})]})]})]}),e.jsxs(i,{onClick:ie,className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",size:"lg",children:[e.jsx(j,{className:"h-5 w-5 mr-2"}),"Calcular Hidratação Avançada"]})]})}),G&&e.jsxs("div",{className:"space-y-6",children:[!G.validation.isValid&&e.jsxs(D,{className:"border-red-300 bg-red-50 dark:bg-red-900/20 border-2",children:[e.jsx("div",{className:"text-red-600 text-lg",children:"🚨"}),e.jsx(z,{className:"text-red-800 dark:text-red-200 text-lg font-bold",children:"PARÂMETROS CRÍTICOS DE SEGURANÇA"}),e.jsx(H,{className:"text-red-700 dark:text-red-300",children:e.jsxs("div",{className:"space-y-3 mt-3",children:[G.validation.errors.map(((a,r)=>e.jsx("div",{className:"p-3 bg-red-100 dark:bg-red-800/30 rounded border border-red-200 dark:border-red-600",children:e.jsx("div",{className:"text-sm leading-relaxed",children:pe(a)})},r))),(()=>{Me(G.osmolarity);let a="";return G.osmolarity<=850?null:(a=G.osmolarity<=1200?`Osmolaridade elevada (${G.osmolarity.toFixed(0)} mOsm/L) - idealmente via central; periférica só em situações específicas com monitoramento rigoroso.`:G.osmolarity<=1800?`Osmolaridade muito elevada (${G.osmolarity.toFixed(0)} mOsm/L) - via central fortemente recomendada devido ao risco de flebite/lesão endotelial. Via periférica apenas em situações excepcionais com supervisão médica contínua.`:`Osmolaridade crítica (${G.osmolarity.toFixed(0)} mOsm/L) - valores acima de 1800 mOsm/L apresentam risco muito elevado. Recomenda-se reduzir osmolaridade ou aumentar volume antes da administração.`,a?e.jsx("div",{className:"p-3 bg-orange-100 dark:bg-orange-800/30 rounded border border-orange-200 dark:border-orange-600",children:e.jsx("div",{className:"text-sm leading-relaxed",children:he(a)})}):null)})(),(()=>{const a=parseFloat(m)||0,r=O.tig,s=G.offeredVolume,t=G.nutrition.glucoseConcentration;if(t>12.5){const i=Fe(a,r,s,t);return e.jsxs("div",{className:"p-3 bg-blue-100 dark:bg-blue-800/30 rounded border border-blue-200 dark:border-blue-600",children:[e.jsx("p",{className:"font-semibold text-sm mb-2",children:"⚠️ Ajuste automático de glicose"}),e.jsxs("p",{className:"text-sm",children:["Para atingir a TIG de ",r," mg/kg/min com o volume ofertado de ",s," mL, o sistema recomenda ",i.sgType,"."]}),e.jsxs("p",{className:"text-sm mt-1",children:["➤ Concentração final: ",t.toFixed(1),"% | Glicose total: ",G.nutrition.totalGlucose,"g | TIG real: confirmada em ",r," mg/kg/min"]}),e.jsxs("p",{className:"text-xs mt-2 text-blue-700 dark:text-blue-300",children:["💡 A concentração final (",t.toFixed(1),"%) é menor que o SG usado devido à diluição com eletrólitos e água destilada."]})]})}return null})(),e.jsxs("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-600",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"font-semibold text-blue-800 dark:text-blue-200 text-sm",children:"💡 SOLUÇÕES RÁPIDAS"}),e.jsx(i,{variant:"ghost",size:"sm",onClick:()=>ae(!ee),className:"h-6 px-2 text-blue-700 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-300 dark:hover:text-blue-200 dark:hover:bg-blue-800/50",children:ee?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-3 w-3 mr-1"}),"Ocultar"]}):e.jsxs(e.Fragment,{children:[e.jsx(L,{className:"h-3 w-3 mr-1"}),"Ver Soluções"]})})]}),ee&&e.jsxs("div",{className:"space-y-3 mt-3 pt-3 border-t border-blue-200 dark:border-blue-700",children:[G.nutrition.glucoseConcentration>12.5&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:[e.jsx("strong",{children:"Problema:"})," Glicose ",G.nutrition.glucoseConcentration,"% > 12.5%"]}),(()=>{const a=parseFloat(m)||0,r=O.tig,s=a<=10?100*a:a<=20?1e3+50*(a-10):1500+20*(a-20),t=Math.min(1.5*s,150*a),l=((e,a,r=12.5)=>{const s=a*e*1440/1e3/(r/100)*1e3,t=Te(e),i=(e=>.8*Re(e))(e);return s>t?t:Math.max(s,i)})(a,r,12.5),o=G.offeredVolume<l;return e.jsxs(e.Fragment,{children:[o&&e.jsx("div",{className:"p-2 bg-amber-50 dark:bg-amber-900/20 rounded border border-amber-200 dark:border-amber-700",children:e.jsxs("p",{className:"text-xs text-amber-800 dark:text-amber-200",children:["⚠️ ",e.jsx("strong",{children:"Volume insuficiente:"})," Com TIG ",r," mg/kg/min, seria necessário ",l>t?`mais de ${t}mL (limite seguro)`:`${l}mL`," para glicose ≤12.5%. Considere reduzir TIG."]})}),(()=>{const a=parseFloat(m)||0,r=a<=10?100*a:a<=20?1e3+50*(a-10):1500+20*(a-20),s=Math.min(1.5*r,150*a),t=Math.min(Math.ceil(1.3*G.offeredVolume),s),l=t<=s&&t>G.offeredVolume,o=O.tig>3;return l||o?e.jsxs("div",{className:"grid gap-2 sm:grid-cols-2",children:[l&&e.jsxs(i,{size:"sm",onClick:()=>le("volume",t,G.offeredVolume),className:"h-8 text-xs bg-green-600 hover:bg-green-700 text-white font-medium",children:["🔼 Volume ",t,"mL"]}),o&&e.jsxs(i,{size:"sm",onClick:()=>le("tig",Math.max(.7*O.tig,3),O.tig),className:"h-8 text-xs bg-blue-600 hover:bg-blue-700 text-white font-medium",children:["🔻 TIG ",Math.max(.7*O.tig,3).toFixed(1)," mg/kg/min"]})]}):null})()]})})()]}),G.osmolarity>1800&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:[e.jsx("strong",{children:"Problema:"})," Osmolaridade ",G.osmolarity.toFixed(0)," mOsm/L > 1800"]}),(()=>{const a=parseFloat(m)||0,r=a<=10?100*a:a<=20?1e3+50*(a-10):1500+20*(a-20),s=Math.min(1.5*r,150*a),t=G.osmolarity,l=t/1800,o=Math.ceil(G.offeredVolume*l),d=Math.min(o,s);return e.jsxs(e.Fragment,{children:[t>3e3&&e.jsx("div",{className:"p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-700",children:e.jsxs("p",{className:"text-xs text-red-800 dark:text-red-200",children:["🚨 ",e.jsx("strong",{children:"Osmolaridade extrema:"})," ",t.toFixed(0)," mOsm/L é perigosa mesmo para via central. Reduza eletrólitos urgentemente."]})}),(()=>{const a=O.sodium>1.5||O.calcium>0||O.magnesium>.3,r=d<=s;return a||r?e.jsxs("div",{className:"grid gap-2 sm:grid-cols-3",children:[a&&e.jsx(i,{size:"sm",onClick:()=>le("electrolytes",{sodium:Math.max(O.sodium/2,1.5),calcium:Math.max(O.calcium/2,0),magnesium:Math.max(O.magnesium/2,.3)},O),className:"h-8 text-xs bg-green-600 hover:bg-green-700 text-white font-medium",children:"🔻 Reduzir 50%"}),r&&e.jsxs(i,{size:"sm",onClick:()=>le("volume",d,G.offeredVolume),className:"h-8 text-xs bg-blue-600 hover:bg-blue-700 text-white font-medium",children:["🔼 Volume ",d,"mL"]}),(a||r)&&e.jsx(i,{size:"sm",onClick:()=>le("multiple",null,null),className:"h-8 text-xs bg-purple-600 hover:bg-purple-700 text-white font-medium",children:"⚖️ Explorar"})]}):null})()]})})()]})]})]})]})})]}),K&&e.jsxs(D,{className:"border-orange-200 bg-orange-50 dark:bg-orange-900/20",children:[e.jsx("div",{className:"text-orange-600 text-lg",children:"🧪"}),e.jsx(z,{className:"text-orange-800 dark:text-orange-200",children:"Modo Simulação Ativo"}),e.jsxs(H,{className:"text-orange-700 dark:text-orange-300",children:[e.jsx("p",{className:"mb-3",children:"Você está testando uma correção sugerida. Os valores foram alterados temporariamente."}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(i,{size:"sm",onClick:()=>{W(!1),X(null),setTimeout((()=>{ie()}),100),te({title:"Correção Confirmada e Recalculada",description:"Os novos valores foram aplicados e a hidratação foi recalculada automaticamente.",variant:"default"})},className:"bg-green-600 hover:bg-green-700 text-white",children:"✅ Confirmar Simulação"}),e.jsx(i,{size:"sm",variant:"outline",onClick:()=>{J&&(u(J.weight),h(J.offeredVolume),A(J.electrolytes),P({sodium:J.electrolytes.sodium.toString(),potassium:J.electrolytes.potassium.toString(),calcium:J.electrolytes.calcium.toString(),magnesium:J.electrolytes.magnesium.toString(),tig:J.electrolytes.tig.toString()}),X(null),W(!1),M(null),te({title:"Simulação Revertida",description:"Valores originais restaurados.",variant:"default"}))},className:"border-orange-300 text-orange-700 hover:bg-orange-100",children:"↩️ Reverter"})]})]})]}),G.validation.isValid&&G.validation.warnings.length>0&&e.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-700 rounded-xl p-4 shadow-sm",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center",children:e.jsx(x,{className:"h-4 w-4 text-amber-600 dark:text-amber-400"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h4",{className:"font-semibold text-amber-800 dark:text-amber-200",children:"Avisos de Configuração"}),e.jsxs(N,{variant:"outline",className:"bg-amber-100 text-amber-700 border-amber-300 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-600 text-xs",children:[G.validation.warnings.length," ",1===G.validation.warnings.length?"aviso":"avisos"]})]}),e.jsx(i,{variant:"ghost",size:"sm",onClick:()=>Z(!Y),className:"h-7 px-3 text-amber-700 hover:text-amber-800 hover:bg-amber-100 dark:text-amber-300 dark:hover:text-amber-200 dark:hover:bg-amber-800/30 rounded-lg transition-all duration-200",children:Y?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-3 w-3 mr-1"}),e.jsx("span",{className:"text-xs font-medium",children:"Ocultar"})]}):e.jsxs(e.Fragment,{children:[e.jsx(L,{className:"h-3 w-3 mr-1"}),e.jsx("span",{className:"text-xs font-medium",children:"Mostrar"})]})})]}),Y&&e.jsx("div",{className:"space-y-3 pt-3 border-t border-amber-200 dark:border-amber-700",children:G.validation.warnings.map(((a,r)=>e.jsx("div",{className:"p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-600 shadow-sm",children:e.jsx("div",{className:"text-sm leading-relaxed text-amber-800 dark:text-amber-200",children:he(a)})},r)))})]})]})}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(a,{className:s.gradientCard("blue","p-4"),children:[e.jsxs("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center gap-2",children:["Necessidade Hídrica",e.jsx(ye,{type:"holliday"})]}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[G.dailyNeed," mL"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Holliday-Segar"})]}),e.jsxs(a,{className:s.gradientCard("green","p-4"),children:[e.jsx("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2",children:"Volume Ofertado"}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[G.offeredVolume," mL"]}),e.jsxs(N,{variant:"secondary",className:"mt-1 bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:border-gray-600",children:[G.hydrationPercentage.toFixed(0),"% da necessidade"]})]}),e.jsxs(a,{className:s.gradientCard("purple","p-4"),children:[e.jsx("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2",children:"Taxa Horária"}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[G.hourlyRate," mL/h"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Bomba de infusão"})]}),e.jsxs(a,{className:s.gradientCard("orange","p-4"),children:[e.jsxs("h3",{className:"font-semibold text-gray-700 dark:text-gray-200 mb-2 flex items-center gap-2",children:["Osmolaridade",e.jsx(ye,{type:"osmolarity"})]}),e.jsxs("p",{className:"text-2xl font-bold text-primary dark:text-blue-400",children:[G.osmolarity," mOsm/L"]}),(()=>{const a=Me(G.osmolarity);return e.jsxs(N,{variant:"outline",className:`mt-1 ${{green:"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700",yellow:"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700",orange:"bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700",red:"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700"}[a.color]}`,children:[a.icon," ",a.classification]})})()]})]}),e.jsxs(a,{className:s.card("p-6"),children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Componentes da Solução"}),G.components.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:G.components.map(((a,r)=>e.jsxs("div",{className:`p-4 rounded-lg border ${a.color}`,children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:a.name}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[a.volume," mL"]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("span",{className:"font-medium",children:a.concentration})," ",e.jsx("span",{className:"text-xs",children:a.unit})]})]},r)))}):e.jsx("div",{className:"p-6 text-center bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Nenhum eletrólito adicionado. Configure os valores acima para ver os componentes da solução."})})]}),e.jsxs(a,{className:s.card("p-6"),children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Preparo da Solução"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800/50",children:[e.jsxs("h4",{className:"font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Soro Glicosado ",G.preparation?.glucoseSolution?.concentration||"X","%"]}),e.jsxs("p",{className:"text-2xl font-semibold text-primary dark:text-blue-400",children:[G.preparation?.glucoseSolution?.volume||"0"," mL"]})]}),e.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-900/30 rounded-lg border border-gray-100 dark:border-gray-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Água Destilada"}),e.jsxs("p",{className:"text-2xl font-semibold text-primary dark:text-blue-400",children:[G.preparation?.distilledWater||"0"," mL"]})]})]})]}),G.validation.isValid?(()=>{const a=parseFloat(m)||0,r=O.tig,s=G.offeredVolume,t=G.nutrition.glucoseConcentration,l=Fe(a,r,s,t);if(!l.needsAdjustment&&t<=10)return null;const o=t>12.5?"red":t>10?"orange":"blue";return e.jsxs(D,{className:{red:"border-red-200 bg-red-50 dark:bg-red-900/20",orange:"border-orange-200 bg-orange-50 dark:bg-orange-900/20",blue:"border-blue-200 bg-blue-50 dark:bg-blue-900/20"}[o],children:[e.jsx(x,{className:"h-4 w-4 "+("red"===o?"text-red-600":"orange"===o?"text-orange-600":"text-blue-600")}),e.jsxs(z,{className:"red"===o?"text-red-800 dark:text-red-200":"orange"===o?"text-orange-800 dark:text-orange-200":"text-blue-800 dark:text-blue-200",children:[t>12.5?"🚨":t>10?"⚠️":"💡"," Análise de Ajuste de Glicose"]}),e.jsx(H,{className:"red"===o?"text-red-700 dark:text-red-300":"orange"===o?"text-orange-700 dark:text-orange-300":"text-blue-700 dark:text-blue-300",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("p",{children:[e.jsx("strong",{children:"TIG desejada:"})," ",r," mg/kg/min"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Volume ofertado:"})," ",s," mL"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Glicose necessária:"})," ",l.glucoseNeeded.toFixed(1)," g/dia"]})]}),e.jsxs("div",{children:[e.jsxs("p",{children:[e.jsx("strong",{children:"SG recomendado:"})," ",l.sgType]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Concentração final:"})," ",t.toFixed(1),"% (após diluição)"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Concentração teórica:"})," ",l.theoreticalConcentration.toFixed(1),"% (só glicose)"]})]})]}),e.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-700",children:e.jsxs("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[e.jsx("strong",{children:"💡 Explicação técnica:"})," ",l.dilutionExplanation]})}),e.jsx("div",{className:"p-3 bg-gray-50 dark:bg-gray-800/50 rounded border",children:e.jsxs("p",{className:"text-sm",children:[e.jsx("strong",{children:"📋 Recomendação:"})," ",l.recommendation]})}),t>10&&(()=>{const a=parseFloat(m)||0,t=a<=10?100*a:a<=20?1e3+50*(a-10):1500+20*(a-20),l=Math.min(1.5*t,150*a),o=Math.min(Math.ceil(1.5*s),l),d=Math.max(.7*r,3),n=o<=l&&o>s,c=r>3;return n||c?e.jsxs("div",{className:"grid gap-2 sm:grid-cols-2",children:[n&&e.jsx(i,{size:"sm",onClick:()=>ne("volume",o),className:"h-8 text-xs bg-green-600 hover:bg-green-700 text-white font-medium",children:"🔼 Volume SG10%"}),c&&e.jsxs(i,{size:"sm",onClick:()=>ne("tig",d),className:"h-8 text-xs bg-blue-600 hover:bg-blue-700 text-white font-medium",children:["🔻 TIG ",d.toFixed(1)]})]}):null})(),t>25&&e.jsx("div",{className:"mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-700",children:e.jsxs("p",{className:"text-sm text-red-800 dark:text-red-200",children:[e.jsx("strong",{children:"⚠️ Atenção:"})," Concentração muito alta! Considere bomba de infusão com controle rigoroso ou reavalie os parâmetros."]})})]})})]})})():null,e.jsxs(a,{className:s.card("p-6"),children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Informações Nutricionais"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"p-4 bg-green-50 dark:bg-green-900/30 rounded-lg border border-green-100 dark:border-green-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Glicose Total"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.nutrition.totalGlucose," g"]})]}),e.jsxs("div",{className:"p-4 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg border border-yellow-100 dark:border-yellow-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Calorias Totais"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.nutrition.totalCalories," kcal"]})]}),e.jsxs("div",{className:"p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg border border-purple-100 dark:border-purple-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Concentração de Glicose"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.nutrition.glucoseConcentration,"%"]})]})]})]}),e.jsxs(a,{className:s.card("p-6"),children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Taxas de Infusão"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Por Hora"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.hourlyRate," mL/h"]})]}),e.jsxs("div",{className:"p-4 bg-green-50 dark:bg-green-900/30 rounded-lg border border-green-100 dark:border-green-800/50",children:[e.jsx("h4",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Por Minuto"}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.minuteRate," mL/min"]})]}),e.jsxs("div",{className:"p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg border border-purple-100 dark:border-purple-800/50",children:[e.jsxs("h4",{className:"font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2",children:["Gotejamento",e.jsx(ye,{type:"dropRate"})]}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.dropRate," gotas/min"]})]}),e.jsxs("div",{className:"p-4 bg-orange-50 dark:bg-orange-900/30 rounded-lg border border-orange-100 dark:border-orange-800/50",children:[e.jsxs("h4",{className:"font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2",children:["Microgotas",e.jsx(ye,{type:"microdropRate"})]}),e.jsxs("p",{className:"text-xl font-semibold text-primary dark:text-blue-400",children:[G.microdropRate," μgts/min"]})]})]}),G.aliquots.length>1&&e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"flex items-center gap-3 pb-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg shadow-sm",children:e.jsx($,{className:"h-4 w-4 text-white"})}),e.jsx("h4",{className:"font-semibold text-lg text-gray-900 dark:text-gray-100",children:"Divisão em Alíquotas"}),e.jsx("div",{className:"flex-1 h-px bg-gradient-to-r from-blue-200 to-purple-200 dark:from-blue-700 dark:to-purple-700"})]}),e.jsx("div",{className:"space-y-4",children:G.aliquots.map(((a,r)=>e.jsxs("div",{className:"relative p-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10 rounded-lg border border-blue-200/30 dark:border-blue-700/30 shadow-sm hover:shadow-md transition-all duration-200",children:[e.jsx("div",{className:"absolute top-0 left-4 right-4 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold",children:r+1}),e.jsxs("span",{className:"font-semibold text-gray-800 dark:text-gray-200",children:[a.volume," mL em ",a.infusionTimeFormatted||`${a.infusionTime}h`]})]}),e.jsxs("div",{className:"flex flex-wrap gap-3 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-1.5 px-2.5 py-1 bg-white/60 dark:bg-gray-800/60 rounded-full border border-blue-200/50 dark:border-blue-700/50",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[a.infusionRate," mL/h"]})]}),e.jsxs("div",{className:"flex items-center gap-1.5 px-2.5 py-1 bg-white/60 dark:bg-gray-800/60 rounded-full border border-purple-200/50 dark:border-purple-700/50",children:[e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[a.dropRate," gotas/min"]})]})]})]}),e.jsx("div",{className:"absolute bottom-0 left-4 right-4 h-0.5 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full opacity-50"})]},r)))})]})]}),e.jsx(ke,{results:G,weight:parseFloat(m)||0,isSimulating:K})]}),e.jsx(l,{open:re.open,onOpenChange:e=>se((a=>({...a,open:e}))),children:e.jsxs(d,{className:"w-[95vw] max-w-lg mx-auto",children:[e.jsx(n,{className:"space-y-1 sm:space-y-2 pb-1",children:e.jsxs(c,{className:"flex items-center gap-1.5 sm:gap-2 text-sm sm:text-base",children:["volume"===re.type&&e.jsx("div",{className:"p-1 sm:p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full flex-shrink-0",children:e.jsx("div",{className:"text-sm sm:text-lg",children:"🔼"})}),"tig"===re.type&&e.jsx("div",{className:"p-1 sm:p-1.5 bg-orange-100 dark:bg-orange-900/30 rounded-full flex-shrink-0",children:e.jsx("div",{className:"text-sm sm:text-lg",children:"🔻"})}),"electrolytes"===re.type&&e.jsx("div",{className:"p-1 sm:p-1.5 bg-green-100 dark:bg-green-900/30 rounded-full flex-shrink-0",children:e.jsx("div",{className:"text-sm sm:text-lg",children:"🔻"})}),"multiple"===re.type&&e.jsx("div",{className:"p-1 sm:p-1.5 bg-purple-100 dark:bg-purple-900/30 rounded-full flex-shrink-0",children:e.jsx("div",{className:"text-sm sm:text-lg",children:"⚖️"})}),e.jsxs("span",{className:"font-semibold leading-tight text-xs sm:text-sm",children:[e.jsx("span",{className:"hidden sm:inline",children:re.title}),e.jsx("span",{className:"sm:hidden",children:re.title.length>25?re.title.substring(0,25)+"...":re.title})]})]})}),e.jsx("div",{className:"py-1 sm:py-2",children:e.jsx("div",{className:"p-2 sm:p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"text-xs sm:text-sm leading-relaxed",children:"success"===re.type?(a=>{const r=a.split("\n");return e.jsx("div",{className:"space-y-1.5",children:r.map(((a,r)=>{if(!a.trim())return e.jsx("div",{className:"h-1"},r);const s=a.match(/^(🎯|🔄|📊)\s*\*\*(.*?)\*\*:?$/);if(s){const a=s[1],t=s[2];return e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx("span",{className:"text-sm",children:a}),e.jsxs("span",{className:"font-semibold text-gray-800 dark:text-gray-200 text-sm",children:[t,":"]})]},r)}return a.startsWith("•")?e.jsxs("div",{className:"flex items-start gap-1.5 ml-3",children:[e.jsx("span",{className:"text-blue-600 dark:text-blue-400 text-xs mt-0.5",children:"•"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300 text-xs",children:a.substring(1).trim()})]},r):e.jsx("div",{className:"text-gray-700 dark:text-gray-300 ml-3 text-xs",children:a},r)}))})})(re.description):e.jsx("div",{className:"whitespace-pre-line text-gray-700 dark:text-gray-300",children:re.description})})})}),e.jsxs(p,{className:"flex flex-col sm:flex-row gap-2 pt-2 sm:pt-3 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs(i,{variant:"outline",onClick:()=>se((e=>({...e,open:!1}))),className:"w-full sm:flex-1 h-8 sm:h-9 text-xs sm:text-sm border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",children:[e.jsx("span",{className:"hidden sm:inline",children:"❌ Cancelar"}),e.jsx("span",{className:"sm:hidden",children:"❌"})]}),e.jsx(i,{onClick:()=>{re.action(),se((e=>({...e,open:!1}))),"multiple"!==re.type?te({title:"✅ Ajuste Aplicado",description:"Os valores foram atualizados e serão recalculados automaticamente.",variant:"default"}):te({title:"✅ Informação Confirmada",description:"Orientações compreendidas.",variant:"default"})},className:"w-full sm:flex-1 h-8 sm:h-9 text-xs sm:text-sm bg-green-600 hover:bg-green-700 text-white font-semibold",children:"multiple"===re.type?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"hidden sm:inline",children:"📋 Entendi"}),e.jsx("span",{className:"sm:hidden",children:"📋"})]}):e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"hidden sm:inline",children:"✅ Confirmar"}),e.jsx("span",{className:"sm:hidden",children:"✅"})]})})]})]})})]})},Ve=()=>e.jsx(a,{className:s.gradientCard("blue","p-4"),children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white flex-shrink-0",children:e.jsx(V,{className:"h-5 w-5"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-1",children:"Calculadora de Hidratação Pediátrica"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Cálculo de hidratação venosa baseado na regra de Holliday-Segar com eletrólitos personalizáveis."})]}),e.jsx("div",{className:"flex-shrink-0",children:e.jsxs("div",{className:"flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400",children:[e.jsx(ye,{type:"calculator"}),e.jsx("span",{className:"hidden sm:inline",children:"Ferramenta de apoio clínico"})]})})]})}),_e=()=>{const[r,t]=F.useState(!1);return e.jsx(J,{open:r,onOpenChange:t,children:e.jsxs(a,{className:s.gradientCard("purple","p-4"),children:[e.jsx(X,{asChild:!0,children:e.jsxs(i,{variant:"ghost",className:"w-full justify-between p-3 h-auto rounded-lg hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 dark:hover:from-purple-900/20 dark:hover:to-blue-900/20 transition-all duration-200 border border-transparent hover:border-purple-200 dark:hover:border-purple-800",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 text-white",children:e.jsx(U,{className:"h-4 w-4"})}),e.jsx("h3",{className:"text-base font-semibold text-gray-900 dark:text-gray-100",children:"Referências Bibliográficas"})]}),e.jsx("div",{className:"flex items-center gap-1",children:r?e.jsx(L,{className:"h-4 w-4 text-gray-500 transition-transform duration-200"}):e.jsx(O,{className:"h-4 w-4 text-gray-500 transition-transform duration-200"})})]})}),e.jsx(Y,{className:"mt-4",children:e.jsxs("div",{className:"space-y-4",children:[[{title:"UpToDate - Maintenance intravenous fluid therapy in children",description:"Guia completo sobre terapia de fluidos intravenosos de manutenção em pediatria",url:"https://www.uptodate.com/contents/maintenance-intravenous-fluid-therapy-in-children"},{title:"Holliday MA, Segar WE. The maintenance need for water in parenteral fluid therapy",description:"Artigo original que estabeleceu a regra de Holliday-Segar para cálculo de necessidades hídricas",journal:"Pediatrics. 1957;19(5):823-832"},{title:"ESPGHAN/ESPEN Guidelines on Paediatric Parenteral Nutrition",description:"Diretrizes europeias sobre nutrição parenteral pediátrica incluindo hidratação",journal:"J Pediatr Gastroenterol Nutr. 2018;67(1):1-11"},{title:"Sociedade Brasileira de Pediatria - Manual de Hidratação",description:"Recomendações nacionais para hidratação em pediatria",journal:"SBP - Departamento de Emergência"}].map(((a,r)=>e.jsx("div",{className:"p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-purple-100 dark:border-purple-800/50",children:e.jsxs("div",{className:"flex items-start justify-between gap-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-gray-800 dark:text-gray-200 text-sm mb-1",children:a.title}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-2",children:a.description}),a.journal&&e.jsx("p",{className:"text-xs text-purple-600 dark:text-purple-400 font-medium",children:a.journal})]}),a.url&&e.jsx("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",className:"text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors",children:e.jsx(Z,{className:"h-4 w-4"})})]})},r))),e.jsx("div",{className:"p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800",children:e.jsxs("p",{className:"text-xs text-purple-800 dark:text-purple-200",children:[e.jsx("strong",{children:"Nota:"})," Esta calculadora implementa as diretrizes internacionais mais atuais para hidratação pediátrica, incluindo validações de segurança e alertas clínicos."]})})]})})]})})},De=()=>e.jsxs("div",{className:"space-y-8 mt-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-3",children:[e.jsx("div",{className:"flex items-center justify-center gap-3",children:e.jsx("div",{className:"p-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white",children:e.jsx(re,{className:"h-8 w-8"})})}),e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Fundamentos"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"Conceitos essenciais sobre hidratação venosa em pediatria"})]}),e.jsx(a,{className:"p-6 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"💧 Necessidades Hídricas Pediátricas"}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:"As necessidades hídricas em pediatria são proporcionalmente maiores que em adultos devido ao maior turnover de água, maior superfície corporal relativa e menor capacidade de concentração renal."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"⚖️ Método de Holliday-Segar"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(N,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"0-10kg"}),e.jsx("span",{children:"100 mL/kg/dia"})]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(N,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200",children:"10-20kg"}),e.jsx("span",{children:"1000 mL + 50 mL/kg para cada kg acima de 10kg"})]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx(N,{variant:"outline",className:"bg-purple-50 text-purple-700 border-purple-200",children:">20kg"}),e.jsx("span",{children:"1500 mL + 20 mL/kg para cada kg acima de 20kg"})]})]})})]})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-3",children:[e.jsx("div",{className:"flex items-center justify-center gap-3",children:e.jsx("div",{className:"p-3 rounded-full bg-gradient-to-r from-green-500 to-blue-500 text-white",children:e.jsx(j,{className:"h-8 w-8"})})}),e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent",children:"Cálculos"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"Fórmulas e exemplos práticos para hidratação pediátrica"})]}),e.jsx(a,{className:"p-6 bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-800",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"📊 Cálculo do Volume Total"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"bg-green-100 text-green-800 border-green-200",children:"Exemplo"}),e.jsx("span",{className:"text-sm",children:"Criança de 15kg"})]}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("p",{children:"• Primeiros 10kg: 10 × 100 = 1000 mL"}),e.jsx("p",{children:"• Próximos 5kg: 5 × 50 = 250 mL"}),e.jsx("p",{className:"font-semibold text-green-700 dark:text-green-300",children:"• Total: 1250 mL/dia"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"• Taxa horária: 1250 ÷ 24 = 52 mL/h"})]})]})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"⚗️ Cálculo de Eletrólitos"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Sódio (NaCl)"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("p",{children:"• Necessidade: 2-3 mEq/kg/dia"}),e.jsx("p",{children:"• NaCl 20%: 1 mL = 3,4 mEq Na+"}),e.jsx("p",{children:"• NaCl 10%: 1 mL = 1,7 mEq Na+"}),e.jsx("p",{className:"text-blue-600 dark:text-blue-400",children:"• Para 15kg: 30-45 mEq/dia"})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Potássio (KCl)"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("p",{children:"• Necessidade: 1-2 mEq/kg/dia"}),e.jsx("p",{children:"• KCl 19,1%: 1 mL = 2,56 mEq K+"}),e.jsx("p",{children:"• KCl 10%: 1 mL = 1,34 mEq K+"}),e.jsx("p",{className:"text-blue-600 dark:text-blue-400",children:"• Para 15kg: 15-30 mEq/dia"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"🍯 Taxa de Infusão de Glicose (TIG)"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-sm text-gray-700 dark:text-gray-300 space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Fórmula:"})," TIG = (Concentração de glicose em mg/mL × Volume/h) ÷ (Peso × 60)"]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"* O resultado estará em mg/kg/min (unidade padrão para TIG)"})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-700",children:[e.jsx("p",{className:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:"💡 Concentrações de Glicose:"}),e.jsxs("ul",{className:"text-xs space-y-1 text-blue-700 dark:text-blue-300",children:[e.jsx("li",{children:"• SG 5% = 50 mg/mL"}),e.jsx("li",{children:"• SG 10% = 100 mg/mL"}),e.jsx("li",{children:"• SG 25% = 250 mg/mL"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"bg-green-100 text-green-800 border-green-200",children:"Exemplo"}),e.jsx("span",{className:"text-sm",children:"SG 5% a 52 mL/h para 15kg"})]}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsx("p",{children:"• SG 5% = 50 mg/mL"}),e.jsx("p",{children:"• Volume = 52 mL/h"}),e.jsx("p",{children:"• Peso = 15 kg"}),e.jsx("p",{className:"mt-2",children:"TIG = (50 × 52) ÷ (15 × 60)"}),e.jsx("p",{children:"TIG = 2600 ÷ 900"}),e.jsx("p",{children:e.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"TIG ≈ 2,89 mg/kg/min ✅"})})]})]})})]})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-3",children:[e.jsx("div",{className:"flex items-center justify-center gap-3",children:e.jsx("div",{className:"p-3 rounded-full bg-gradient-to-r from-orange-500 to-red-500 text-white",children:e.jsx(se,{className:"h-8 w-8"})})}),e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:"Prática"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"Aplicação clínica e monitorização da hidratação pediátrica"})]}),e.jsx(a,{className:"p-6 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-orange-200 dark:border-orange-800",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"🎯 Indicações Clínicas"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200",children:"Hidratação de Manutenção"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Jejum pré-operatório"}),e.jsx("li",{children:"• Impossibilidade de via oral"}),e.jsx("li",{children:"• Vômitos persistentes"}),e.jsx("li",{children:"• Diarreia com intolerância oral"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200",children:"Situações Especiais"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Prematuros (ajustar TIG)"}),e.jsx("li",{children:"• Cardiopatias (restrição hídrica)"}),e.jsx("li",{children:"• Nefropatias (ajustar eletrólitos)"}),e.jsx("li",{children:"• Diabetes (monitorar glicemia)"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"⏰ Monitorização"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Parâmetros Clínicos"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Diurese (1-2 mL/kg/h)"}),e.jsx("li",{children:"• Peso diário"}),e.jsx("li",{children:"• Sinais vitais"}),e.jsx("li",{children:"• Estado de hidratação"})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Exames Laboratoriais"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Eletrólitos séricos"}),e.jsx("li",{children:"• Glicemia"}),e.jsx("li",{children:"• Função renal"}),e.jsx("li",{children:"• Gasometria (se indicado)"})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Frequência"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Diurese: a cada 6h"}),e.jsx("li",{children:"• Peso: diário"}),e.jsx("li",{children:"• Eletrólitos: 12-24h"}),e.jsx("li",{children:"• Glicemia: 6-12h"})]})]})]})})]})]})})]})]}),ze=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-3",children:[e.jsx("div",{className:"flex items-center justify-center gap-3",children:e.jsx("div",{className:"p-3 rounded-full bg-gradient-to-r from-red-500 to-pink-500 text-white",children:e.jsx(x,{className:"h-8 w-8"})})}),e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent",children:"Segurança"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"Precauções essenciais e boas práticas para hidratação segura"})]}),e.jsx(a,{className:"p-6 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border-red-200 dark:border-red-800",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"⚠️ Alertas Críticos"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(x,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),e.jsx("h5",{className:"font-medium text-red-800 dark:text-red-200",children:"Osmolaridade Elevada"})]}),e.jsxs("ul",{className:"text-sm space-y-1 text-red-700 dark:text-red-300",children:[e.jsx("li",{children:"• >850 mOsm/L: preferir acesso central"}),e.jsx("li",{children:"• >1200 mOsm/L: acesso central obrigatório"}),e.jsx("li",{children:"• >1800 mOsm/L: risco de trombose"})]})]}),e.jsxs("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(A,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"}),e.jsx("h5",{className:"font-medium text-yellow-800 dark:text-yellow-200",children:"Concentração de Glicose"})]}),e.jsxs("ul",{className:"text-sm space-y-1 text-yellow-700 dark:text-yellow-300",children:[e.jsx("li",{children:"• >12,5%: risco de hiperglicemia"}),e.jsx("li",{children:"• Monitorar glicemia rigorosamente"}),e.jsx("li",{children:"• Considerar insulina se necessário"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"✅ Boas Práticas"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Prescrição"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Sempre calcular osmolaridade"}),e.jsx("li",{children:"• Verificar compatibilidades"}),e.jsx("li",{children:"• Documentar indicação"}),e.jsx("li",{children:"• Revisar diariamente"})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Administração"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Usar bomba de infusão"}),e.jsx("li",{children:"• Filtro 0,22μm se indicado"}),e.jsx("li",{children:"• Trocar equipos conforme protocolo"}),e.jsx("li",{children:"• Monitorar local de infusão"})]})]})]})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3",children:"🚨 Complicações"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Metabólicas"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Hiperglicemia"}),e.jsx("li",{children:"• Distúrbios eletrolíticos"}),e.jsx("li",{children:"• Sobrecarga hídrica"}),e.jsx("li",{children:"• Desidratação"})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h5",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Locais"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsx("li",{children:"• Flebite"}),e.jsx("li",{children:"• Infiltração"}),e.jsx("li",{children:"• Trombose"}),e.jsx("li",{children:"• Infecção"})]})]})]})]})]})})]}),He=({open:a,onOpenChange:r})=>e.jsxs(l,{open:a,onOpenChange:r,modal:!0,children:[e.jsx(o,{asChild:!0,children:e.jsxs(i,{variant:"outline",size:"sm",className:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-700 hover:from-purple-100 hover:to-blue-100 dark:hover:from-purple-900/30 dark:hover:to-blue-900/30 text-purple-700 dark:text-purple-300 hover:text-purple-800 dark:hover:text-purple-200 transition-all duration-200 shadow-sm hover:shadow-md h-9 px-3 text-xs md:h-10 md:px-4 md:text-sm",children:[e.jsx(U,{className:"h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Material de Estudo"}),e.jsx("span",{className:"sm:hidden",children:"Estudo"})]})}),e.jsxs(d,{className:"max-w-4xl max-h-[90vh] overflow-y-auto","aria-describedby":"study-material-description",children:[e.jsxs(n,{children:[e.jsxs(c,{className:"flex items-center gap-3 text-2xl",children:[e.jsx("div",{className:"p-2 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 text-white",children:e.jsx(I,{className:"h-6 w-6"})}),"Material de Estudo - Hidratação Pediátrica"]}),e.jsx(b,{id:"study-material-description",className:"text-base text-gray-600 dark:text-gray-400",children:"Guia completo sobre hidratação venosa em pediatria, fundamentos teóricos e aplicação prática."})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsx(De,{}),e.jsx(ze,{})]})]})]}),Be=()=>{const[a,r]=F.useState(!1);return a?e.jsx("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"🔍 Análise dos Botões Principais"}),e.jsx(i,{onClick:()=>r(!1),variant:"ghost",size:"sm",children:"✕"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(i,{onClick:async()=>{try{const e=(()=>{const e=["Usar Calculadora Simples","Usar Calculadora Avançada"].map((e=>{const a=Array.from(document.querySelectorAll("button")).find((a=>a.textContent?.includes(e)));if(!a)return{text:e,status:"NOT_FOUND"};const r=a.getBoundingClientRect(),s=getComputedStyle(a),t=a.parentElement,i=t?.getBoundingClientRect();return{text:e,status:"FOUND",position:{x:r.x,y:r.y,width:r.width,height:r.height,top:r.top,bottom:r.bottom},viewport:{isVisible:r.top>=0&&r.bottom<=window.innerHeight,isInViewport:r.top<window.innerHeight&&r.bottom>0,escaping:r.bottom>window.innerHeight||r.top<0},css:{position:s.position,zIndex:s.zIndex,transform:s.transform,overflow:s.overflow,display:s.display},parent:t?{tagName:t.tagName,className:t.className,position:i?{x:i.x,y:i.y,width:i.width,height:i.height}:null,css:{position:getComputedStyle(t).position,overflow:getComputedStyle(t).overflow,transform:getComputedStyle(t).transform}}:null}}));return{timestamp:(new Date).toISOString(),viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},buttons:e}})();await navigator.clipboard.writeText(JSON.stringify(e,null,2)),P({title:"Debug copiado!",description:"Dados dos botões copiados para a área de transferência"})}catch(e){P({title:"Erro",description:"Não foi possível copiar os dados",variant:"destructive"})}},className:"w-full",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Copiar Análise dos Botões"]}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("p",{children:'• Foco apenas nos botões "Usar Calculadora Simples/Avançada"'}),e.jsx("p",{children:'• Verifica se estão "escapando" da viewport'}),e.jsx("p",{children:"• Analisa posicionamento e CSS dos elementos pais"})]})]})]})}):e.jsxs(i,{onClick:()=>r(!0),className:"fixed bottom-4 right-4 z-50 bg-red-500 hover:bg-red-600 text-white",size:"sm",children:[e.jsx(te,{className:"w-4 h-4 mr-1"}),"DEBUG BOTÕES"]})},Ue=()=>{const r=ie(),t=ae.hidratacao,[l,o]=F.useState("selection"),[d,n]=F.useState(!1),c=e=>{o(e)},m=()=>{o("selection")},x=()=>window.location.pathname.includes("/flowcharts/")?"/flowcharts":"/calculadoras";return e.jsxs("div",{className:s.gradientBackground("min-h-screen flex flex-col"),children:[e.jsx(ee,{...t}),e.jsx(R,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-2 md:py-8",children:e.jsxs("div",{className:"max-w-6xl mx-auto space-y-3 md:space-y-8",children:[e.jsxs("div",{className:"hidden md:flex items-center gap-4",children:[e.jsx(i,{variant:"ghost",size:"icon",onClick:()=>"selection"===l?r(x()):m(),className:"h-10 w-10 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 shadow-sm hover:shadow-md",children:e.jsx(T,{className:"h-5 w-5 text-gray-600 dark:text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:s.gradientHeading("text-3xl text-center"),children:"selection"===l?"Calculadora de Hidratação Pediátrica":"advanced"===l?"Calculadora Avançada de Hidratação":"Calculadora Simples de Hidratação"}),e.jsx("p",{className:"text-center text-gray-600 dark:text-gray-400 mt-2",children:"selection"===l?"Escolha a versão que melhor atende suas necessidades":"Cálculo preciso de hidratação venosa baseado em evidências científicas"})]}),e.jsx(He,{open:d,onOpenChange:n})]}),e.jsxs("div",{className:"md:hidden space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(i,{variant:"ghost",size:"icon",onClick:()=>"selection"===l?r(x()):m(),className:"h-9 w-9 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 shadow-sm",children:e.jsx(T,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})}),e.jsx(He,{open:d,onOpenChange:n})]}),e.jsxs("div",{className:"text-center space-y-1",children:[e.jsx("h1",{className:s.gradientHeading("text-xl leading-tight"),children:"selection"===l?"Calculadora de Hidratação Pediátrica":"advanced"===l?"Calculadora Avançada":"Calculadora Simples"}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 px-4 leading-tight",children:"selection"===l?"Escolha a versão que melhor atende suas necessidades":"Cálculo preciso de hidratação venosa baseado em evidências científicas"})]})]}),"selection"===l&&e.jsxs(e.Fragment,{children:[e.jsx(Ve,{}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[e.jsx(a,{className:"group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-800 hover:border-green-300 dark:hover:border-green-600",children:e.jsxs("div",{className:"p-8 min-h-full flex flex-col",onClick:()=>c("simple"),children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx("div",{className:"p-3 rounded-full bg-gradient-to-r from-green-500 to-blue-500 text-white group-hover:scale-110 transition-transform duration-300",children:e.jsx(j,{className:"h-8 w-8"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Calculadora Simples"}),e.jsx("p",{className:"text-green-600 dark:text-green-400 font-medium",children:"Rápida e Prática"})]})]}),e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:"Ideal para cálculos rápidos de hidratação de manutenção com configurações padrão."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200",children:"Características:"}),e.jsxs("ul",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Regra de Holliday-Segar"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Eletrólitos padrão (Na+, K+)"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Taxas de infusão automáticas"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Interface simplificada"]})]})]})]}),e.jsx(i,{className:"w-full mt-6 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 shadow-lg hover:shadow-xl transition-all duration-200 relative z-10 flex-shrink-0",children:"Usar Calculadora Simples"})]})}),e.jsx(a,{className:"group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-600",children:e.jsxs("div",{className:"p-8 min-h-full flex flex-col",onClick:()=>c("advanced"),children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx("div",{className:"p-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white group-hover:scale-110 transition-transform duration-300",children:e.jsx(G,{className:"h-8 w-8"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Calculadora Avançada"}),e.jsx("p",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"Completa e Personalizável"})]})]}),e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:"Ferramenta completa com múltiplas concentrações, presets clínicos e exportação de prescrições."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200",children:"Características:"}),e.jsxs("ul",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Múltiplas concentrações (NaCl, KCl, etc.)"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Eletrólitos completos (Ca²⁺, Mg²⁺)"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Presets clínicos especializados"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Exportação de prescrições"]}),e.jsxs("li",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Validações de segurança"]})]})]})]}),e.jsx(i,{className:"w-full mt-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 shadow-lg hover:shadow-xl transition-all duration-200 relative z-10 flex-shrink-0",children:"Usar Calculadora Avançada"})]})})]})]}),"simple"===l&&e.jsx(Ne,{}),"advanced"===l&&e.jsx($e,{}),e.jsx(_e,{})]})}),e.jsx(M,{}),e.jsx(Be,{})]})};export{Ue as default};
