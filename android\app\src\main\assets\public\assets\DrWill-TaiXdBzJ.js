import{j as e}from"./radix-core-6kBL75b5.js";import{r as s,b as r}from"./critical-DVX9Inzy.js";import{m as a,B as t,X as l,aw as i,av as n,n as d,ak as o,aE as c,V as m,u as x,b as h,d as g,s as u,aa as p,a7 as b,aB as j,M as f,q as y,r as v,t as N,T as w,L as k,I as C,aJ as S,D as T,e as D,f as E,g as M,z as L,h as R}from"./index-DV3Span9.js";import{u as I}from"./useUserData-_nbb43iH.js";import{H as U,l as _,i as $,u as O,a as H,T as B,f as A,M as z,b as W,S as F,c as V,d as q,e as P,g as G,h as J}from"./secureStorage-BkptdSny.js";import{A as Q}from"./index-D5c712lJ.js";import{P as K}from"./plus-CG1D5Wcu.js";import{C as Z}from"./clock-whXX1eHh.js";import{T as X}from"./trash-2-BQ8b_n_c.js";import{f as Y}from"./date-vendor-BOcTQe0E.js";import{p as ee}from"./pt-BR-a_BmBHfW.js";import{u as se}from"./use-mobile-DZ3cxmhN.js";import{C as re}from"./chart-column-aLLSZX2P.js";import{D as ae}from"./database-Bw4XDcns.js";import{Z as te}from"./zap-xHziMQfW.js";import{a as le}from"./router-BAzpOxbo.js";import{B as ie}from"./bot-rvqpW4-g.js";import{S as ne}from"./stethoscope-DDurCu3z.js";import{S as de}from"./send-Dmzt5dsn.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./zoom-out-hqJqh803.js";import"./download-BbEua-No.js";import"./book-open-Ca6sFj94.js";import"./chevron-left-2OWC_ZeG.js";const oe=({isOpen:s,onClose:r,threads:d,currentThreadId:o,onSelectThread:c,onNewThread:m,onDeleteThread:x,isLoading:h})=>{const g=e=>{try{return Y(e,{addSuffix:!0,locale:ee})}catch{return"há alguns momentos"}};return e.jsx(Q,{children:s&&e.jsxs(e.Fragment,{children:[e.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 z-40",onClick:r}),e.jsxs(a.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{type:"spring",damping:25,stiffness:200},className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-2xl h-[80vh] bg-white border-2 border-black shadow-2xl z-50 rounded-2xl overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b-2 border-black bg-gradient-to-r from-purple-50 to-indigo-50",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(U,{className:"h-6 w-6 text-purple-600"}),e.jsx("h2",{className:"text-xl font-bold text-gray-800",children:"Histórico de Conversas"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(t,{onClick:m,size:"sm",className:"bg-purple-500 hover:bg-purple-600 text-white border-2 border-black rounded-lg px-4 py-2 font-semibold",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Nova Conversa"]}),e.jsx(t,{onClick:r,variant:"ghost",size:"sm",className:"hover:bg-gray-100 p-2",children:e.jsx(l,{className:"h-5 w-5"})})]})]}),e.jsx(i,{className:"flex-1 h-[calc(80vh-100px)]",children:e.jsx("div",{className:"p-6 space-y-3",children:h?e.jsx("div",{className:"space-y-4",children:[1,2,3,4].map((s=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-20 bg-gray-200 rounded-xl"})},s)))}):0===d.length?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(n,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-600 mb-2",children:"Nenhuma conversa ainda"}),e.jsx("p",{className:"text-gray-500 text-sm mb-4",children:"Suas conversas com Dr. Will aparecerão aqui"}),e.jsxs(t,{onClick:m,className:"bg-purple-500 hover:bg-purple-600 text-white border-2 border-black rounded-lg px-6 py-2 font-semibold",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Iniciar Primeira Conversa"]})]}):e.jsx(Q,{children:d.map((s=>e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 "+(o===s.id?"border-purple-500 bg-purple-50 shadow-lg":"border-gray-200 hover:border-purple-300 hover:bg-purple-25 hover:shadow-md"),onClick:()=>c(s.id),children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"p-2 rounded-lg "+(o===s.id?"bg-purple-500":"bg-gray-300 group-hover:bg-gray-400"),children:e.jsx(n,{className:"h-4 w-4 "+(o===s.id?"text-white":"text-gray-600")})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-semibold text-base mb-2 line-clamp-2 "+(o===s.id?"text-purple-800":"text-gray-800"),children:s.title}),e.jsxs("div",{className:"flex items-center gap-3 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1 text-gray-500",children:[e.jsx(Z,{className:"h-4 w-4"}),e.jsx("span",{children:g(s.lastMessageAt)})]}),e.jsxs("div",{className:"flex items-center gap-1 text-gray-500",children:[e.jsx(n,{className:"h-4 w-4"}),e.jsxs("span",{children:[s.messageCount," mensagens"]})]})]})]}),e.jsx(t,{onClick:e=>{e.stopPropagation(),x(s.id)},variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto hover:bg-red-100 hover:text-red-600",children:e.jsx(X,{className:"h-3 w-3"})})]})},s.id)))})})})]})]})})},ce=({isOpen:r,onClose:l})=>{const[i,n]=s.useState(null),[x,h]=s.useState("healthy");return s.useEffect((()=>{if(!r)return;const e=()=>{const e=$.getDiagnostics(),s=$.getHealthStatus();n(e),h(s)};e();const s=setInterval(e,2e3);return()=>clearInterval(s)}),[r]),r&&i?e.jsx(Q,{children:e.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4",onClick:l,children:e.jsxs(a.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white rounded-xl border-2 border-black max-w-4xl w-full max-h-[80vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[e.jsxs("div",{className:`p-4 border-b-2 border-black ${(()=>{switch(x){case"healthy":return"border-green-500 bg-green-50";case"warning":return"border-yellow-500 bg-yellow-50";case"critical":return"border-red-500 bg-red-50"}})()}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(d,{className:"h-6 w-6"}),e.jsx("h2",{className:"text-xl font-bold",children:"Dr. Will Diagnostics"}),(()=>{switch(x){case"healthy":return e.jsx(m,{className:"h-5 w-5 text-green-500"});case"warning":return e.jsx(o,{className:"h-5 w-5 text-yellow-500"});case"critical":return e.jsx(c,{className:"h-5 w-5 text-red-500"})}})(),e.jsx("span",{className:"text-sm font-medium capitalize",children:x})]}),e.jsx(t,{variant:"outline",size:"sm",onClick:l,children:"✕"})]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Real-time system monitoring and performance metrics"})]}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-blue-50 border-2 border-blue-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(re,{className:"h-4 w-4 text-blue-600"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Success Rate"})]}),e.jsxs("div",{className:"text-2xl font-bold text-blue-900",children:[i.performance.successRate,"%"]})]}),e.jsxs("div",{className:"border-2 rounded-lg p-4 "+(i.performance.avgDuration>5e3?"bg-red-50 border-red-200":i.performance.avgDuration>2e3?"bg-yellow-50 border-yellow-200":"bg-green-50 border-green-200"),children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(Z,{className:"h-4 w-4 "+(i.performance.avgDuration>5e3?"text-red-600":i.performance.avgDuration>2e3?"text-yellow-600":"text-green-600")}),e.jsx("span",{className:"text-sm font-medium "+(i.performance.avgDuration>5e3?"text-red-800":i.performance.avgDuration>2e3?"text-yellow-800":"text-green-800"),children:"Avg Duration"})]}),e.jsxs("div",{className:"text-2xl font-bold "+(i.performance.avgDuration>5e3?"text-red-900":i.performance.avgDuration>2e3?"text-yellow-900":"text-green-900"),children:[i.performance.avgDuration,"ms"]}),i.performance.avgDuration>5e3&&e.jsx("div",{className:"text-xs text-red-600 mt-1",children:"⚠️ Edge functions cold start"})]}),e.jsxs("div",{className:"bg-purple-50 border-2 border-purple-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ae,{className:"h-4 w-4 text-purple-600"}),e.jsx("span",{className:"text-sm font-medium text-purple-800",children:"Operations"})]}),e.jsx("div",{className:"text-2xl font-bold text-purple-900",children:i.performance.totalOperations})]}),e.jsxs("div",{className:"bg-orange-50 border-2 border-orange-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(te,{className:"h-4 w-4 text-orange-600"}),e.jsx("span",{className:"text-sm font-medium text-orange-800",children:"Slow Ops"})]}),e.jsx("div",{className:"text-2xl font-bold text-orange-900",children:i.performance.slowOperations})]})]}),e.jsxs("div",{className:"bg-gray-50 border-2 border-gray-200 rounded-lg p-4",children:[e.jsxs("h3",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[e.jsx(o,{className:"h-5 w-5 text-red-500"}),"Error Analysis"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Last Minute: ",e.jsx("span",{className:"font-bold",children:i.errors.lastMinute})]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Last Hour: ",e.jsx("span",{className:"font-bold",children:i.errors.lastHour})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Recent Errors:"}),i.errors.recent.length>0?e.jsx("div",{className:"space-y-1",children:i.errors.recent.map(((s,r)=>e.jsxs("div",{className:"text-xs bg-red-100 border border-red-200 rounded p-2",children:[e.jsx("p",{className:"font-medium",children:s.operation}),e.jsx("p",{className:"text-red-700",children:s.error}),e.jsx("p",{className:"text-gray-500",children:s.timestamp})]},r)))}):e.jsx("p",{className:"text-sm text-green-600",children:"No recent errors"})]})]})]}),e.jsxs("div",{className:"bg-gray-50 border-2 border-gray-200 rounded-lg p-4",children:[e.jsxs("h3",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[e.jsx(ae,{className:"h-5 w-5 text-blue-500"}),"Thread Status"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Active Threads: ",e.jsx("span",{className:"font-bold",children:i.threads.active})]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Problematic: ",e.jsx("span",{className:"font-bold text-red-600",children:i.threads.problematic.length})]})]}),e.jsx("div",{children:i.threads.problematic.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Problematic Threads:"}),e.jsx("div",{className:"space-y-1",children:i.threads.problematic.map(((s,r)=>e.jsxs("div",{className:"text-xs bg-yellow-100 border border-yellow-200 rounded p-2",children:[e.jsxs("p",{className:"font-medium",children:["Thread ",s.id]}),e.jsxs("p",{children:["Load attempts: ",s.loadAttempts]}),e.jsxs("p",{children:["Errors: ",s.errors]}),e.jsxs("p",{children:["Last activity: ",s.lastActivity]})]},r)))})]})})]})]}),e.jsxs("div",{className:"bg-gray-50 border-2 border-gray-200 rounded-lg p-4",children:[e.jsx("h3",{className:"text-lg font-bold mb-3",children:"System Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Timestamp:"})," ",new Date(i.timestamp).toLocaleString()]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Environment:"})," ","production"]})]}),e.jsxs("div",{children:[e.jsx("p",{children:e.jsx("span",{className:"font-medium",children:"Logger Stats:"})}),e.jsx("div",{className:"ml-4 text-xs",children:(()=>{const s=_.getStats();return e.jsxs(e.Fragment,{children:[e.jsxs("p",{children:["Total logs: ",s.totalLogs]}),e.jsxs("p",{children:["Last minute: ",s.logsLastMinute]}),e.jsxs("p",{children:["Total errors: ",s.totalErrors]}),e.jsxs("p",{children:["Last error: ",s.lastError]})]})})()})]})]})]})]})]})})}):null},me=new Map,xe=r.memo((({content:r,isStreaming:a,messageId:t,isUser:l,isLoading:i,onResourcesClick:n,onResourcesDataDetected:d})=>{const{content:o,ragData:c}=s.useMemo((()=>{if(l||a)return{content:r,ragData:null};let e=(e=>{const s=e.match(/\*\*RAG_RESOURCES_BUTTON:(.*?)\*\*/)||e.match(/RAG_RESOURCES_BUTTON:(.*?)(?=\n|$)/);if(s)try{return{isValid:!0,data:JSON.parse(s[1])}}catch(r){return{isValid:!1,data:null}}return e.match(/\*\*?RAG_RESOURCES_BUTTON:(.*)$/s),{isValid:!1,data:null}})(r).data;return{content:r.replace(/\*\*RAG_RESOURCES_BUTTON:.*?\*\*/g,"").replace(/RAG_RESOURCES_BUTTON:.*?(?=\n|$)/g,"").replace(/\*\*?RAG_RESOURCES_BUTTON:.*$/gms,"").replace(/\n{3,}/g,"\n\n").trim().replace(/\$~~~SUGGESTIONS\$[\s\S]*?(?=\n\n|\n$|$)/g,"").trim(),ragData:e}}),[r,l,a]);s.useEffect((()=>{if(c&&d){const e={directMentions:c.directMentions||[],suggestions:c.suggestions||[],conductMentions:c.conductMentions||[],conductSuggestions:c.conductSuggestions||[],title:`Recursos encontrados (${c.count})`};d(e)}}),[c,d]);const m=s.useMemo((()=>{const e=`${t}_${o.length}_${a}`;if(me.has(e))return me.get(e);const s=o.replace(/^---+$/gm,"").replace(/^\*\*\*+$/gm,"").replace(/^___+$/gm,"").replace(/\n\s*\n\s*\n/g,"\n\n").trim(),r=P(s,a||!1);return me.set(e,r),r}),[o,t,a]);return e.jsxs(e.Fragment,{children:[e.jsx("div",{dangerouslySetInnerHTML:{__html:m},className:"leading-relaxed text-sm",style:{wordBreak:"break-word",overflowWrap:"break-word"}}),!l&&!a&&c&&c.count>0&&e.jsx("div",{className:"mt-4 pt-3 border-t border-gray-100 dark:border-gray-700",children:e.jsx("div",{className:"text-center",children:e.jsxs("button",{onClick:n,className:"inline-flex items-center gap-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 text-sm font-medium hover:bg-green-50 dark:hover:bg-green-900/20 px-3 py-2 rounded-lg transition-all duration-200",children:[e.jsx("span",{className:"text-base",children:"📚"}),e.jsxs("span",{children:["Acessar recursos citados na plataforma (",c.count,")"]})]})})})]})}),((e,s)=>e.content===s.content&&e.isStreaming===s.isStreaming&&e.messageId===s.messageId&&e.isUser===s.isUser&&e.isLoading===s.isLoading&&e.onResourcesClick===s.onResourcesClick&&e.onResourcesDataDetected===s.onResourcesDataDetected)),he=()=>{const r=le(),{user:i}=x(),{profile:n}=I(),d=se(),[c,m]=s.useState(null),[_,$]=s.useState(!1),{currentThreadId:P,threads:Z,loadMessages:X,deleteThread:re,deleteAllThreads:ae,clearCurrentConversation:te,loadThreads:he,saveMessage:ge,createNewThread:ue,getConversationHistory:pe}=O(),{messages:be,isLoading:je,isStreaming:fe,error:ye,sendMessage:ve,clearMessages:Ne,setMessages:we,cancelRequest:ke}=H({currentThreadId:c||P,saveToHistory:ge,createNewThread:ue,getConversationHistory:pe}),Ce=e=>e.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2),Se=s.useRef(null),Te=s.useRef(null),De=s.useRef(null),Ee=s.useRef({}),[Me,Le]=s.useState(""),[Re,Ie]=s.useState(!1),[Ue,_e]=s.useState(!1),[$e,Oe]=s.useState(""),[He,Be]=s.useState(!1),[Ae,ze]=s.useState([]),[We,Fe]=s.useState(!1),[Ve,qe]=s.useState(!1),[Pe,Ge]=s.useState(""),{currentQuestion:Je,sessionId:Qe,sessionTitle:Ke,isInQuestionSession:Ze}=h(),[Xe,Ye]=s.useState(!1),[es,ss]=s.useState(""),[rs,as]=s.useState(null),[ts,ls]=s.useState(!1),[is,ns]=s.useState(null),[ds,os]=s.useState(!1),[cs,ms]=s.useState(null),[xs,hs]=s.useState(!1),[gs,us]=s.useState([]),[ps,bs]=s.useState(!1),{toast:js}=g(),fs=s.useRef(null),ys=s.useRef(!0),[vs,Ns]=s.useState(!0);s.useEffect((()=>{const e=Te.current;if(!e)return;let s=null;const r=()=>{s&&clearTimeout(s),s=setTimeout((()=>{const{scrollTop:s,scrollHeight:r,clientHeight:a}=e,t=r-s-a<100;ys.current=!!t}),100)};return e.addEventListener("scroll",r),()=>{e.removeEventListener("scroll",r),s&&clearTimeout(s)}}),[]),s.useEffect((()=>{void 0!==Z&&Ns(!1)}),[Z]);const ws=s.useRef(""),ks=s.useRef(!1),Cs=s.useRef(0);s.useEffect((()=>{if(0===be.length)return;const e=be[be.length-1];e.isUser?(ws.current=e.id,ks.current=!1,Cs.current=0,setTimeout((()=>{const s=Ee.current[e.id],r=Te.current;if(s&&r){const e=s.offsetTop;r.scrollTo({top:e-20,behavior:"smooth"})}}),100)):e.isStreaming&&!e.isUser&&!ks.current&&ys.current?setTimeout((()=>{const s=Ee.current[e.id],r=Te.current;if(s&&r&&!ks.current){const e=s.offsetTop-20,a=r.scrollTop;if(Math.abs(a-e)<=30)return void(ks.current=!0);r.scrollTo({top:e,behavior:"smooth"})}}),100):e.isStreaming||e.isUser||!e.content||(ks.current=!0)}),[be]);const Ss=s.useCallback((()=>{te&&"function"==typeof te&&te(),m(null)}),[te]),Ts=s.useCallback((()=>{je||(Ne(),Ss(),Le(""),_e(!1),$(!0),Te.current&&(Te.current.scrollTop=0))}),[je,Ne,Ss]),Ds=async e=>{const s=e.target.files;if(!s||0===s.length)return;const r=[];for(let a=0;a<Math.min(s.length,3-gs.length);a++){const e=s[a];if(e.type.startsWith("image/")){const s=URL.createObjectURL(e);r.push({url:s,file:e})}}us((e=>[...e,...r])),fs.current&&(fs.current.value="")},Es=async(e,s)=>{const r=(Me||"").trim();if(!r&&0===gs.length)return;if(je)return;const a=r;let t=[];if(gs.length>0)try{bs(!0);const e=i?.id||localStorage.getItem("auth_user_id");if(!e)throw new Error("Usuário não autenticado. Faça login novamente.");for(let s=0;s<gs.length;s++){const r=gs[s],a=G(e,r.file.name),l=await J("chat-images",a,r.file);t.push(l)}gs.forEach((e=>{URL.revokeObjectURL(e.url)})),us([])}catch(n){return void js({title:"Erro ao enviar imagem",description:n.message||"Erro desconhecido",variant:"destructive"})}finally{bs(!1)}const l=t.length>0?t:void 0;Le(""),R.drWillUserState({user:i?{id:i.id,email:i.email}:null,hasUser:!!i,userId:i?.id,userType:typeof i,authState:{localStorage_userId:localStorage.getItem("auth_user_id"),localStorage_profile:localStorage.getItem("auth_profile")?"exists":"missing"}});try{if(_)$(!1),R.drWillSendMessageCall({type:"forceNewThread",userId:i?.id}),await ve(a,i?.id,null,!0,l);else{const e=c||P;R.drWillSendMessageCall({type:"existingThread",userId:i?.id,threadToUse:e}),await ve(a,i?.id,e,!1,l)}}catch(n){}},Ms=s.useCallback((e=>{ns(e)}),[]),Ls=s.useCallback((()=>{is&&ls(!0)}),[is]),Rs=s.useCallback(((e,s)=>{ms({type:"single",threadId:e,threadTitle:s}),os(!0)}),[]),Is=s.useCallback((()=>{ms({type:"all"}),os(!0)}),[]),Us=s.useCallback((async()=>{if(cs&&!xs){hs(!0);try{"single"===cs.type&&cs.threadId?await re(cs.threadId):"all"===cs.type&&await ae(),await he(!0),os(!1),ms(null)}catch(e){}finally{hs(!1)}}}),[cs,re,ae,he,xs,Z]),_s=s.useCallback((()=>{os(!1),ms(null)}),[]);s.useEffect((()=>(window.openMedicationDialog=Ls,()=>{delete window.openMedicationDialog})),[Ls]);const[$s,Os]=s.useState(1),Hs=s.useCallback((e=>e.includes("📚")&&(e.includes("Sessão Contextual")||e.includes("(")&&e.includes("q)"))),[]),Bs=s.useCallback((e=>{if(!e)return"Sessão";let s=e.replace("📚 ","");return s=s.replace(" - Sessão Contextual",""),s=s.replace(/\s*\(\d+q\)$/,""),s||"Sessão"}),[]),As=s.useCallback((async e=>{try{if(!i?.id)return null;const{data:s,error:r}=await u.from("study_sessions").select("id").eq("user_id",i.id).eq("title",e).single();return r?null:s?.id||null}catch(s){return null}}),[i?.id]),zs=async e=>{try{if(je)return;const s=Z?.find((s=>s.id===e));if(!s)return;if(Hs(s.title||""))return void(async(e,s)=>{ss(e);let r=null;if(s){const e=Z?.find((e=>e.id===s));e&&e.metadata?.sessionId&&(r=e.metadata.sessionId)}r||(r=await As(e)),as(r),Ye(!0)})(Bs(s.title||""),s.id);const r=await X(e);if(m(e),r&&r.length>0){const e=r.map((e=>({id:e.id,content:e.content,isUser:e.isUser,timestamp:e.timestamp,isStreaming:!1})));we(e)}else Ne();Ie(!1),_e(!1)}catch(s){q.error("selecting thread",s)}},Ws=s.useMemo((()=>{if(!Z)return[];let e=Z.filter((e=>!Hs(e.title||"")));return $e.trim()&&(e=e.filter((e=>e.title.toLowerCase().includes($e.toLowerCase())))),e}),[Z,$e,Hs]);return s.useEffect((()=>{Os(1)}),[$e]),s.useEffect((()=>{}),[i?.id]),s.useEffect((()=>{const e=e=>{e.detail&&e.detail.mermaidCode&&(Ge(e.detail.mermaidCode),qe(!0))};if(document.addEventListener("openMermaidModal",e),window.expandMermaidDiagram=e=>{const s=document.getElementById(e+"-code");if(s)try{const e=JSON.parse(s.textContent||"");return Ge(e),void qe(!0)}catch(a){}const r=document.querySelectorAll('[id^="mermaid-"]');if(r.length>0){const e=Array.from(r).pop()?.id;if(e){const s=document.getElementById(e+"-code");if(s)try{const e=JSON.parse(s.textContent||"");return Ge(e),void qe(!0)}catch(a){}}}},void 0!==window.mermaid){const e=document.documentElement.classList.contains("dark");window.mermaid.initialize({startOnLoad:!0,theme:e?"dark":"default",themeVariables:e?{primaryColor:"#8b5cf6",primaryTextColor:"#f3f4f6",primaryBorderColor:"#6366f1",lineColor:"#9ca3af",background:"#1f2937",mainBkg:"#374151",secondBkg:"#4b5563",tertiaryColor:"#6b7280"}:{primaryColor:"#8b5cf6",primaryTextColor:"#1f2937",primaryBorderColor:"#6366f1",lineColor:"#6b7280"}})}return()=>{document.removeEventListener("openMermaidModal",e),delete window.openTableDialog,delete window.expandMermaidDiagram}}),[]),s.useEffect((()=>{me.size>100&&me.clear()}),[be.length]),s.useEffect((()=>{if(d){const e=document.body.style.height,s=document.body.style.maxHeight,r=document.documentElement.style.height,a=document.documentElement.style.maxHeight,t=document.getElementById("root")?.style.height,l=document.getElementById("root")?.style.maxHeight,i=document.getElementById("root")?.style.overflow;document.body.style.height="100vh",document.body.style.maxHeight="100vh",document.documentElement.style.height="100vh",document.documentElement.style.maxHeight="100vh";const n=document.getElementById("root");return n&&(n.style.height="100vh",n.style.maxHeight="100vh",n.style.overflow="hidden"),()=>{document.body.style.height=e,document.body.style.maxHeight=s,document.documentElement.style.height=r,document.documentElement.style.maxHeight=a,n&&(n.style.height=t||"",n.style.maxHeight=l||"",n.style.overflow=i||"")}}}),[d]),e.jsxs("div",{className:"h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex flex-col overflow-hidden",children:[e.jsx(p,{}),e.jsx(oe,{isOpen:Re,onClose:()=>Ie(!1),threads:Z||[],currentThreadId:P,onSelectThread:zs,onNewThread:()=>{Ss(),Ne(),Ie(!1),_e(!1),$(!0)},onDeleteThread:async e=>{try{await re(e),e!==c&&e!==P||(Ne(),m(null)),await he(!0)}catch(s){q.error("deleting thread",s)}},isLoading:je}),e.jsxs("div",{"data-drwill-container":!0,className:`flex-1 container max-w-4xl mx-auto ${d?"px-2 py-2":"px-4 py-1"} flex flex-col overflow-hidden ${d?"pb-20":""} ${d?"":"h-[calc(100vh-80px)]"}`,children:[e.jsxs(a.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:(d?"flex items-center justify-between gap-2 mb-2":"flex items-center gap-4 mb-2")+" flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>r("/plataformadeestudos"),className:"border-2 border-black dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),d?"":"Voltar"]}),e.jsxs(t,{variant:"outline",size:"sm",onClick:async()=>{Ue||await he(),_e(!Ue)},className:"border-2 border-black dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 "+(Ue?"bg-purple-50 dark:bg-purple-900/30 border-purple-500 dark:border-purple-400":""),children:[e.jsx(U,{className:"h-4 w-4"}),!d&&e.jsx("span",{className:"ml-2",children:Ue?"Chat":"Histórico"})]}),e.jsxs(t,{variant:"outline",size:"sm",onClick:Ts,disabled:je,className:"border-2 border-black dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700",children:[e.jsx(K,{className:"h-4 w-4"}),!d&&e.jsx("span",{className:"ml-2",children:"Nova"})]})]}),d?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"p-1.5 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg border-2 border-black",children:[e.jsx(ie,{className:"h-4 w-4 text-white"}),e.jsx(j,{className:"h-2 w-2 text-yellow-300 absolute -top-0.5 -right-0.5 animate-pulse"})]})}),e.jsx("h1",{className:"text-sm font-bold text-gray-800 dark:text-gray-200",children:"Dr. Will"})]}):e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl border-2 border-black",children:[e.jsx(ie,{className:"h-8 w-8 text-white"}),e.jsx(j,{className:"h-4 w-4 text-yellow-300 absolute -top-1 -right-1 animate-pulse"})]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800 dark:text-gray-200",children:"Dr. Will"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"MedEvo"})]})]})]}),e.jsxs(a.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"flex-1 bg-white dark:bg-gray-800 rounded-2xl border-2 border-black dark:border-gray-600 shadow-xl overflow-hidden relative flex flex-col "+(d?"":"h-[calc(100vh-100px)]"),children:[e.jsxs("div",{ref:Te,className:`flex-1 overflow-y-auto ${d?"p-4":"p-6"} space-y-4 relative`,children:[e.jsx(Q,{mode:"wait",children:Ue?e.jsxs(a.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"h-full flex flex-col",children:[e.jsx("div",{className:"p-3 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Conversas Anteriores"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("button",{onClick:Is,disabled:je||!Z||0===Z.length,className:"px-2 py-1 text-xs rounded transition-colors "+(je||!Z||0===Z.length?"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed":"bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50"),title:"Deletar todas as conversas",children:"🗑️ Todas"})})]})}),vs?e.jsx("div",{className:"space-y-3 p-4",children:[1,2,3].map((s=>e.jsxs("div",{className:"flex items-center gap-3 animate-pulse",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-300 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-300 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},s)))}):Z&&Z.length>0?(()=>{const s=Math.ceil(Ws.length/10),r=10*($s-1),t=r+10,l=Ws.slice(r,t);return e.jsxs(e.Fragment,{children:[l.map((s=>e.jsx(a.div,{whileHover:{backgroundColor:"#f8fafc"},onClick:()=>{je||zs(s.id)},className:"p-3 border-b border-gray-100 transition-colors "+(je?"opacity-50 cursor-not-allowed":"cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"),children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm "+(Hs(s.title||"")?"bg-gradient-to-br from-emerald-500 to-teal-600":"bg-gradient-to-br from-blue-500 to-purple-600"),children:Hs(s.title||"")?"🎯":"🧠"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h5",{className:"font-medium text-gray-900 dark:text-gray-100 text-sm truncate",children:s.title||"Conversa com Dr. Will"}),Hs(s.title||"")&&e.jsx("span",{className:"px-2 py-0.5 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 text-xs rounded-full",children:"Contextual"})]}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[(()=>{try{if(s.lastMessageAt){const e=new Date(s.lastMessageAt);if(!isNaN(e.getTime()))return Y(e,{addSuffix:!0,locale:ee})}return"Recente"}catch{return"Recente"}})(),Hs(s.title||"")&&(Ze?(()=>{const r=s.metadata?.sessionId,a=Qe;return r&&a&&r!==a?e.jsx("span",{className:"ml-2 text-red-500",children:"• Sessão diferente"}):r&&a&&r===a?e.jsx("span",{className:"ml-2 text-green-500",children:"• Sessão atual"}):e.jsx("span",{className:"ml-2 text-gray-500",children:"• Sessão"})})():e.jsx("span",{className:"ml-2 text-orange-500",children:"• Sem contexto ativo"}))]})]}),e.jsx("button",{onClick:e=>{e.stopPropagation(),Rs(s.id,s.title||"Conversa com Dr. Will")},disabled:je,className:"p-1 rounded transition-colors "+(je?"text-gray-300 cursor-not-allowed":"text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30"),title:"Deletar conversa",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},s.id))),s>1&&e.jsx("div",{className:"p-3 border-t border-gray-100 dark:border-gray-600 bg-gray-50 dark:bg-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["Página ",$s," de ",s," • ",Ws.length," conversas"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>Os($s-1),disabled:1===$s||je,className:"px-2 py-1 text-xs rounded transition-colors "+(1===$s||je?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-white dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 border border-gray-200 dark:border-gray-500"),children:"←"}),e.jsx("span",{className:"px-2 py-1 text-xs text-gray-600",children:$s}),e.jsx("button",{onClick:()=>Os($s+1),disabled:$s===s||je,className:"px-2 py-1 text-xs rounded transition-colors "+($s===s||je?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-white dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 border border-gray-200 dark:border-gray-500"),children:"→"})]})]})}),P&&e.jsx("div",{className:"p-3 border-t border-gray-100 dark:border-gray-600 bg-blue-50 dark:bg-blue-900/30",children:e.jsx("button",{onClick:()=>{je||_e(!1)},disabled:je,className:"w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 "+(je?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-blue-500 hover:bg-blue-600 text-white shadow-sm"),children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Voltar à Conversa Atual"]})})})]})})():(Z&&Z.length,e.jsxs("div",{className:"p-4 text-center text-gray-500",children:[e.jsx(f,{className:"h-8 w-8 mx-auto mb-2 text-gray-300"}),e.jsx("p",{className:"text-sm",children:"Nenhuma conversa anterior"})]}))]},"history"):e.jsx(a.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},className:"h-full",children:0===be.length?e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"flex flex-col items-center justify-center h-full text-center "+(d?"py-4 px-3":"py-8 px-4"),children:[e.jsxs("div",{className:"relative "+(d?"mb-4":"mb-8"),children:[e.jsx("div",{className:(d?"p-4":"p-6")+" bg-gradient-to-br from-purple-500 to-indigo-600 rounded-3xl border-2 border-black dark:border-gray-600 shadow-xl",children:e.jsx("div",{className:d?"text-3xl":"text-4xl",children:"🧠"})}),e.jsx("div",{className:"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"})]}),e.jsx("h1",{className:`${d?"text-xl":"text-2xl"} font-bold text-gray-800 dark:text-gray-200 ${d?"mb-2":"mb-3"}`,children:"Dr. Will"}),e.jsx("p",{className:`text-gray-600 dark:text-gray-400 ${d?"mb-4 text-sm":"mb-8"} max-w-lg leading-relaxed`,children:"Assistente médico especializado em discussão de casos, criação de tabelas, fluxogramas e análise de imagens"}),e.jsxs("div",{className:`grid grid-cols-2 md:grid-cols-4 ${d?"gap-2 mb-4":"gap-4 mb-8"} w-full max-w-2xl`,children:[e.jsxs("div",{className:`flex flex-col items-center ${d?"p-2":"p-4"} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`,children:[e.jsx("div",{className:d?"text-lg mb-1":"text-2xl mb-2",children:"📊"}),e.jsx("span",{className:(d?"text-[10px]":"text-xs")+" font-medium text-gray-700 dark:text-gray-300 text-center",children:"Tabelas"})]}),e.jsxs("div",{className:`flex flex-col items-center ${d?"p-2":"p-4"} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`,children:[e.jsx("div",{className:d?"text-lg mb-1":"text-2xl mb-2",children:"🗺️"}),e.jsx("span",{className:(d?"text-[10px]":"text-xs")+" font-medium text-gray-700 dark:text-gray-300 text-center",children:"Fluxogramas"})]}),e.jsxs("div",{className:`flex flex-col items-center ${d?"p-2":"p-4"} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`,children:[e.jsx("div",{className:d?"text-lg mb-1":"text-2xl mb-2",children:"🖼️"}),e.jsx("span",{className:(d?"text-[10px]":"text-xs")+" font-medium text-gray-700 dark:text-gray-300 text-center",children:"Análise de Imagens"})]}),e.jsxs("div",{className:`flex flex-col items-center ${d?"p-2":"p-4"} bg-white dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 shadow-sm hover:shadow-md transition-shadow`,children:[e.jsx("div",{className:d?"text-lg mb-1":"text-2xl mb-2",children:"💬"}),e.jsx("span",{className:(d?"text-[10px]":"text-xs")+" font-medium text-gray-700 dark:text-gray-300 text-center",children:"Discussão de Casos"})]})]}),e.jsxs("div",{className:`bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl ${d?"p-3":"p-6"} border border-blue-200 dark:border-blue-700 max-w-md`,children:[e.jsx("p",{className:`${d?"text-xs":"text-sm"} text-gray-700 dark:text-gray-300 ${d?"mb-1":"mb-2"} font-medium`,children:"Pronto para começar?"}),e.jsx("p",{className:(d?"text-[10px]":"text-xs")+" text-gray-500 dark:text-gray-400",children:"Digite sua pergunta ou envie uma imagem para análise"})]})]}):e.jsx(e.Fragment,{children:be.map((s=>e.jsxs(a.div,{ref:e=>Ee.current[s.id]=e,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"flex flex-col","data-message-id":s.id,"data-is-user":s.isUser,style:{scrollMarginTop:"16px"},children:[e.jsx("div",{className:`flex ${s.isUser?"justify-end":"justify-start"} mb-2`,children:s.isUser?e.jsxs(y,{className:(d?"h-8 w-8":"h-10 w-10")+" border-2 border-blue-500",children:[e.jsx(v,{src:i?.user_metadata?.avatar_url||`https://www.gravatar.com/avatar/${i?.email}?d=mp`,alt:n?.full_name||i?.email||"Usuário"}),e.jsx(N,{className:"bg-blue-500 text-white font-bold",children:n?.full_name?Ce(n.full_name):i?.email?Ce(i.email):"U"})]}):e.jsx("div",{className:`p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg ${d?"h-8 w-8":"h-10 w-10"} flex items-center justify-center`,children:e.jsx(ne,{className:(d?"h-4 w-4":"h-5 w-5")+" text-white"})})}),e.jsx("div",{className:s.isUser?"flex justify-end":"flex justify-start",children:e.jsxs("div",{className:d?"max-w-[95%]":"max-w-[90%]",children:[e.jsx("div",{className:"p-4 rounded-2xl "+(s.isUser?"bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto shadow-lg":"bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-600"),children:s.isThinking?e.jsx(B,{content:s.content,isStreaming:s.isStreaming,formatThinkingContent:A}):e.jsxs(e.Fragment,{children:[s.isUser&&s.images&&s.images.length>0&&e.jsx(z,{images:s.images,className:"mb-3"}),e.jsx(xe,{content:s.content,isStreaming:s.isStreaming,messageId:s.id,isUser:s.isUser,isLoading:je,onResourcesClick:Ls,onResourcesDataDetected:Ms}),s.isStreaming&&s.content&&!s.isThinking&&e.jsxs("div",{className:"flex items-center gap-2 mt-3 pt-2 border-t border-gray-200",children:[e.jsxs("div",{className:"flex gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),e.jsx("span",{className:"text-xs text-gray-500",children:"Dr. Will está respondendo..."})]})]})}),e.jsx("div",{className:"text-xs text-gray-500 mt-1 px-2",children:s.timestamp.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]})})]},s.id)))})},"chat")}),e.jsx("div",{ref:Se})]}),e.jsxs("div",{className:`flex-shrink-0 border-t-2 border-black dark:border-gray-600 ${d?"p-3":"p-4"} bg-gray-50 dark:bg-gray-700`,children:[e.jsxs("div",{className:(d?"mt-2 mb-3":"mb-2")+" flex items-center gap-2 justify-center text-[10px] text-gray-500",children:[e.jsx(o,{className:"w-4 h-4"}),e.jsx("p",{children:"Como médico é seu papel discernir todas as informações."})]}),gs.length>0&&e.jsx("div",{className:"mb-3 flex gap-2 flex-wrap",children:gs.map(((s,r)=>e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:s.url,alt:`Preview ${r+1}`,className:"w-16 h-16 object-cover rounded-lg border-2 border-gray-300"}),e.jsx("button",{onClick:()=>(e=>{us((s=>{const r=[...s];return URL.revokeObjectURL(r[e].url),r.splice(e,1),r}))})(r),className:"absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600",children:"×"})]},r)))}),e.jsxs("div",{className:"flex "+(d?"gap-2":"gap-3"),children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(w,{ref:De,value:Me,onChange:e=>Le(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),Es())},placeholder:"Digite sua pergunta...",className:`w-full ${d?"min-h-[50px] max-h-[100px]":"min-h-[60px] max-h-[120px]"} resize-none border-2 border-gray-300 rounded-xl focus:border-purple-500 focus:ring-0 ${d?"pr-12":"pr-16"}`,disabled:je||ps}),d&&e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"file",accept:"image/*",onChange:Ds,ref:fs,className:"hidden",multiple:!0}),e.jsx(t,{type:"button",onClick:()=>fs.current?.click(),disabled:je||ps||gs.length>=3,variant:"ghost",size:"sm",className:"absolute right-2 bottom-2 w-8 h-8 p-0 text-gray-500",children:ps?e.jsx(k,{className:"w-4 h-4 animate-spin text-blue-500"}):e.jsx(C,{className:"w-4 h-4"})})]})]}),!d&&e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"file",accept:"image/*",onChange:Ds,ref:fs,className:"hidden",multiple:!0}),e.jsx(t,{type:"button",onClick:()=>fs.current?.click(),disabled:je||ps||gs.length>=3,variant:"outline",className:"bg-gray-50 dark:bg-gray-600 border-2 border-gray-300 dark:border-gray-500 rounded-xl min-w-12 h-12 p-2 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-500 flex items-center justify-center",children:ps?e.jsx(k,{className:"w-5 h-5 animate-spin text-blue-500"}):e.jsx(C,{className:"w-5 h-5 text-gray-500 dark:text-gray-400"})})]}),je?e.jsx(t,{onClick:ke,className:`bg-red-500 hover:bg-red-600 text-white border-2 border-black dark:border-gray-600 rounded-xl ${d?"px-3 py-2 min-w-12 h-12":"px-6 py-3 min-w-16 h-12"} font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center`,children:e.jsx(l,{className:d?"h-4 w-4":"h-5 w-5"})}):e.jsx(t,{onClick:()=>Es(),disabled:!Me.trim()&&0===gs.length,className:`bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white border-2 border-black dark:border-gray-600 rounded-xl ${d?"px-3 py-2 min-w-12 h-12":"px-6 py-3 min-w-16 h-12"} font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center`,children:e.jsx(de,{className:d?"h-4 w-4":"h-5 w-5"})})]}),je&&!fe&&e.jsx(a.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"mt-4",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 rounded-2xl border-2 border-purple-200 dark:border-purple-700 p-6 shadow-lg",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center",children:e.jsx(S,{className:"h-6 w-6 text-white animate-pulse"})}),e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full animate-ping"}),e.jsx("div",{className:"absolute -bottom-1 -left-1 w-3 h-3 bg-indigo-400 rounded-full animate-ping",style:{animationDelay:"0.5s"}})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"text-lg font-semibold text-purple-800 dark:text-purple-300",children:"Dr. Will está analisando"}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.4s"}})]})]}),e.jsx("p",{className:"text-purple-600 text-sm",children:"Processando sua pergunta médica com raciocínio clínico estruturado"}),e.jsx("div",{className:"mt-3 w-full bg-purple-200 dark:bg-purple-800 rounded-full h-1.5",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-500 to-indigo-500 h-1.5 rounded-full animate-pulse",style:{width:"60%"}})})]})]})})}),ye&&e.jsxs("div",{className:"mt-3 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg",children:[e.jsx("p",{className:"text-sm text-red-600 dark:text-red-400",children:ye}),e.jsx(t,{onClick:()=>window.location.reload(),variant:"outline",size:"sm",className:"mt-2 text-red-600 dark:text-red-400 border-red-300 dark:border-red-700 hover:bg-red-50 dark:hover:bg-red-900/30",children:"Tentar Novamente"})]})]})]})]}),He&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-2 sm:p-4",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-7xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden border border-gray-200 dark:border-gray-600",children:[e.jsx("div",{className:"bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-600 text-white p-4 sm:p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm",children:e.jsx("svg",{className:"h-5 w-5 sm:h-6 sm:w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold",children:"Tabela Diagnóstica"}),e.jsx("p",{className:"text-white text-opacity-90 text-xs sm:text-sm",children:"Dr. Will • MedEvo • Diagnósticos Diferenciais"})]})]}),e.jsx("button",{onClick:()=>Be(!1),className:"p-2 hover:bg-white hover:bg-opacity-20 rounded-xl transition-all duration-200 hover:scale-105",children:e.jsx("svg",{className:"h-5 w-5 sm:h-6 sm:w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),e.jsx("div",{className:"overflow-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]",children:Ae.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"block sm:hidden p-4 space-y-4",children:Ae.slice(1).map(((s,r)=>e.jsxs("div",{className:"bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl p-4 shadow-sm",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white text-sm font-bold",children:r+1})}),e.jsx("div",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:s[0]||"Diagnóstico"}})})]}),s.slice(1).map(((s,r)=>e.jsxs("div",{className:"mb-3 last:mb-0",children:[e.jsx("div",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide mb-1",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:Ae[0]?.[r+1]||`Campo ${r+1}`}})}),e.jsx("div",{className:"text-sm text-gray-700 leading-relaxed",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:s}})})]},r)))]},r)))}),e.jsx("div",{className:"hidden sm:block p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm border border-gray-200 dark:border-gray-600",children:[e.jsx("thead",{className:"bg-gradient-to-r from-gray-50 via-blue-50 to-purple-50 dark:from-gray-700 dark:via-blue-900/30 dark:to-purple-900/30",children:e.jsx("tr",{children:Ae[0]?.map(((s,r)=>e.jsx("th",{className:"px-6 py-4 text-left text-sm font-bold text-gray-900 dark:text-gray-100 border-b-2 border-gray-300 dark:border-gray-600",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:s}})},r)))})}),e.jsx("tbody",{children:Ae.slice(1).map(((s,r)=>e.jsx("tr",{className:(r%2==0?"bg-white dark:bg-gray-800":"bg-gray-50 dark:bg-gray-700")+" hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200",children:s.map(((s,r)=>e.jsx("td",{className:"px-6 py-4 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-600 align-top",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:s}})},r)))},r)))})]})})})]})}),e.jsx("div",{className:"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 px-4 sm:px-6 py-4 border-t border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),e.jsxs("span",{className:"hidden sm:inline",children:[Ae.length-1," diagnósticos diferenciais • Dr. Will MedEvo"]}),e.jsxs("span",{className:"sm:hidden",children:[Ae.length-1," diagnósticos"]})]}),e.jsx("button",{onClick:()=>Be(!1),className:"w-full sm:w-auto bg-gradient-to-r from-emerald-500 to-blue-600 hover:from-emerald-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:scale-105 shadow-lg",children:e.jsxs("span",{className:"flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})}),"Fechar"]})})]})})]})}),e.jsx(ce,{isOpen:We,onClose:()=>Fe(!1)}),e.jsx(W,{isOpen:Ve,onClose:()=>qe(!1),mermaidCode:Pe}),e.jsx(F,{open:Xe,onOpenChange:Ye,sessionTitle:es,sessionId:rs}),is&&e.jsx(V,{open:ts,onOpenChange:ls,directMentions:is.directMentions,suggestions:is.suggestions,conductMentions:is.conductMentions,conductSuggestions:is.conductSuggestions,title:is.title}),e.jsx(T,{open:ds,onOpenChange:os,children:e.jsxs(D,{className:"sm:max-w-md",children:[e.jsxs(E,{children:[e.jsxs(M,{className:"flex items-center gap-2",children:[e.jsx("svg",{className:"w-5 h-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Confirmar Deleção"]}),e.jsx(L,{children:"all"===cs?.type?e.jsxs(e.Fragment,{children:["Tem certeza que deseja deletar ",e.jsx("strong",{children:"TODAS as conversas"}),"?",e.jsx("br",{}),e.jsx("span",{className:"text-red-600 font-medium",children:"Esta ação não pode ser desfeita."})]}):e.jsxs(e.Fragment,{children:["Tem certeza que deseja deletar a conversa:",e.jsx("br",{}),e.jsxs("strong",{children:['"',cs?.threadTitle,'"']}),"?",e.jsx("br",{}),e.jsx("span",{className:"text-red-600 font-medium",children:"Esta ação não pode ser desfeita."})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx(t,{variant:"outline",onClick:_s,disabled:xs,className:"border-gray-300 hover:bg-gray-50",children:"Cancelar"}),e.jsx(t,{onClick:Us,disabled:xs,className:"bg-red-500 hover:bg-red-600 text-white disabled:opacity-50",children:xs?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Deletando..."]}):"all"===cs?.type?"Deletar Todas":"Deletar"})]})]})})]})};export{he as default};
