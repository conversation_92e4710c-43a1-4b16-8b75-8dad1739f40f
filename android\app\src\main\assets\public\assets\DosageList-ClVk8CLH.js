import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{u as t,a as s}from"./query-vendor-B-7l6Nb3.js";import{aT as i,aU as n,an as r,B as l,X as o,a5 as d,aw as c,s as m,ad as g,ae as u,af as h,ag as x,ai as p,T as j,d as v}from"./index-DV3Span9.js";import{S as y}from"./switch-BsgRCeyt.js";import{P as _}from"./plus-CG1D5Wcu.js";import{P as f}from"./pencil-BjwGDekm.js";import{T as b}from"./trash-2-BQ8b_n_c.js";function N({value:a,onChange:t}){return e.jsxs(i,{value:a,onValueChange:e=>t(e),className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"multiplier",id:"multiplier"}),e.jsx(r,{htmlFor:"multiplier",children:"Multiplicador"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"fixed",id:"fixed"}),e.jsx(r,{htmlFor:"fixed",children:"Valor Fixo"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"age",id:"age"}),e.jsx(r,{htmlFor:"age",children:"Baseado em Idade"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"multiplier_by_fixed_age",id:"multiplier_by_fixed_age"}),e.jsx(r,{htmlFor:"age",children:"Multiplicador por idade"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"fixed_by_weight",id:"fixed_by_weight"}),e.jsx(r,{htmlFor:"fixed_by_weight",children:"Valor Fixo por Peso"})]})]})}function C({ranges:a,onRemove:t}){return 0===a.length?null:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Faixas de idade configuradas:"}),a.map(((a,s)=>e.jsxs("div",{className:"flex items-center gap-2 bg-gray-50 p-2 rounded-lg",children:[e.jsxs("span",{className:"flex-1",children:[a.startMonth,"-",a.endMonth," meses: ",a.value]}),e.jsx(l,{type:"button",variant:"ghost",size:"icon",onClick:()=>t(s),children:e.jsx(o,{className:"h-4 w-4"})})]},s)))]})}function w({tagName:a,onTagNameChange:t}){return e.jsx("div",{className:"flex-1",children:e.jsx(d,{placeholder:"Nome da tag",value:a,onChange:e=>t(e.target.value)})})}function R({value:a,onChange:t}){return e.jsx("div",{className:"w-32",children:e.jsx(d,{type:"text",placeholder:"Valor fixo",value:(s=a,s>0&&s<.001?s.toFixed(4):s.toLocaleString("pt-BR",{minimumFractionDigits:0,maximumFractionDigits:4})),onChange:e=>t((e=>{const a=e.replace(/\./g,"").replace(",","."),t=parseFloat(a);return t?Number(t.toFixed(4)):0})(e.target.value))})});var s}function F({startWeight:a,onStartWeightChange:t,endWeight:s,onEndWeightChange:i,value:n,onValueChange:r}){return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Peso inicial",value:a||"",onChange:e=>t(parseFloat(e.target.value)||0),min:"0",step:"0.1"})}),e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Peso final",value:s||"",onChange:e=>i(parseFloat(e.target.value)||0),min:"0",step:"0.1"})}),e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"text",placeholder:"Valor",value:n?(l=n,l.toString().replace(".",",")):"",onChange:e=>r((e=>{const a=parseFloat(e.replace(",","."));return isNaN(a)?0:a})(e.target.value))})})]});var l}function S({tagName:a,onTagNameChange:t,tagType:s,multiplier:i,onMultiplierChange:n,maxValue:o,onMaxValueChange:c,startMonth:m,onStartMonthChange:g,endMonth:u,onEndMonthChange:h,ageValue:x,onAgeValueChange:p,startWeight:j,onStartWeightChange:v,endWeight:f,onEndWeightChange:b,onAddTag:N,onAddAgeRange:C,onAddWeightRange:S,ageRangesCount:M,weightRangesCount:k,roundResult:W,onRoundResultChange:V}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-2 items-start",children:[e.jsx(w,{tagName:a,onTagNameChange:t}),"fixed_by_weight"===s?e.jsxs(e.Fragment,{children:[e.jsx(F,{startWeight:j,onStartWeightChange:v,endWeight:f,onEndWeightChange:b,value:i,onValueChange:n}),e.jsxs(l,{type:"button",onClick:S,disabled:!a||j>f,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Add Faixa"]}),e.jsxs(l,{type:"button",onClick:N,disabled:!a||0===k,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Adicionar Tag"]})]}):"multiplier_by_fixed_age"===s?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Mês inicial",value:m||"",onChange:e=>g(parseInt(e.target.value)||0),min:"0"})}),e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Mês final",value:u||"",onChange:e=>h(parseInt(e.target.value)||0),min:"0"})}),e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Valor",value:x||"",onChange:e=>p(parseFloat(e.target.value)||0),step:"0.00001"})}),e.jsx(R,{value:o||0,onChange:e=>c(e||void 0)}),e.jsxs(l,{type:"button",onClick:C,disabled:!a||m>u,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Add Faixa"]}),e.jsxs(l,{type:"button",onClick:N,disabled:!a||0===M,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Adicionar Tag"]})]}):"age"===s?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Mês inicial",value:m||"",onChange:e=>g(parseInt(e.target.value)||0),min:"0"})}),e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Mês final",value:u||"",onChange:e=>h(parseInt(e.target.value)||0),min:"0"})}),e.jsx("div",{className:"w-24",children:e.jsx(d,{type:"number",placeholder:"Valor",value:x||"",onChange:e=>p(parseFloat(e.target.value)||0),step:"0.00001"})}),e.jsxs(l,{type:"button",onClick:C,disabled:!a||m>u,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Add Faixa"]}),e.jsxs(l,{type:"button",onClick:N,disabled:!a||0===M,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Adicionar Tag"]})]}):e.jsxs(e.Fragment,{children:[e.jsx(R,{value:i,onChange:n}),"multiplier"===s&&e.jsx(R,{value:o||0,onChange:e=>c(e||void 0)}),e.jsxs(l,{type:"button",onClick:N,disabled:!a||!i,children:[e.jsx(_,{className:"h-4 w-4 mr-2"}),"Adicionar Tag"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(y,{id:"round-result",checked:W,onCheckedChange:V}),e.jsx(r,{htmlFor:"round-result",children:"Aproximar resultado para número inteiro"})]})]})}function M({tags:a,onRemoveTag:t,onToggleRounding:s}){const i=e=>{const a=e=>e?e>0&&e<.001?e.toFixed(4):e.toLocaleString("pt-BR",{minimumFractionDigits:0,maximumFractionDigits:4}):"0";return"age"===e.type&&e.ageRanges?`(idade: ${e.ageRanges.map((e=>`${e.startMonth}-${e.endMonth}m: ${a(e.value)}`)).join(", ")})`:"fixed_by_weight"===e.type&&e.weightRanges?`(peso: ${e.weightRanges.map((e=>`${e.startWeight}-${e.endWeight}kg: ${a(e.value)}`)).join(", ")})`:"multiplier_by_fixed_age"===e.type&&e.ageRanges?`(Multiplicador em Idade: ${e.ageRanges.map((t=>`${t.startMonth}-${t.endMonth}m: ${a(t.value)}${e.maxValue?`, máximo: ${a(e.maxValue)}`:""}`)).join(", ")})`:"fixed"===e.type?`(valor fixo: ${a(e.multiplier)})`:`(multiplicador: ${a(e.multiplier)}${e.maxValue?`, máximo: ${a(e.maxValue)}`:""})`};return e.jsx("div",{className:"space-y-2",children:a.map(((a,n)=>e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("div",{className:"flex-1 p-2 bg-gray-50 rounded-lg flex items-center justify-between",children:[e.jsxs("span",{children:[e.jsx("span",{className:"font-medium",children:a.name}),e.jsx("span",{className:"text-gray-500 ml-2",children:i(a)})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(y,{id:`round-${n}`,checked:a.roundResult||!1,onCheckedChange:e=>s(n,e)}),e.jsx(r,{htmlFor:`round-${n}`,className:"text-sm text-muted-foreground",children:"Aproximar"})]}),e.jsx(l,{type:"button",variant:"ghost",size:"icon",onClick:()=>t(n),children:e.jsx(o,{className:"h-4 w-4"})})]})]})},n)))})}function k({tags:s,onTagsChange:i,medicationId:n}){const[l,o]=a.useState(""),[d,g]=a.useState(1),[u,h]=a.useState(),[x,p]=a.useState("multiplier"),[j,v]=a.useState([]),[y,_]=a.useState([]),[f,b]=a.useState(0),[w,R]=a.useState(0),[F,k]=a.useState(0),[W,V]=a.useState(0),[$,T]=a.useState(0),[A,E]=a.useState(!1),{data:I}=t({queryKey:["medication-tags",n],queryFn:async()=>{const{data:e,error:a}=await m.from("pedbook_medication_tags").select("*").eq("medication_id",n);if(a)throw a;return e.map((e=>({name:e.name,type:e.type,multiplier:e.multiplier,maxValue:e.max_value,roundResult:e.round_result,...("age"===e.type||"multiplier_by_fixed_age"===e.type)&&{ageRanges:[{startMonth:e.start_month||0,endMonth:e.end_month||0,value:e.multiplier||0}]},..."fixed_by_weight"===e.type&&{weightRanges:[{startWeight:e.start_weight||0,endWeight:e.end_weight||0,value:e.multiplier||0}]}})))},enabled:!!n});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(r,{children:"Tags para substituição"}),e.jsx(c,{className:"h-[200px] rounded-md border p-4",children:e.jsx(M,{tags:s,onRemoveTag:e=>{const a=s.filter(((a,t)=>t!==e));i(a)},onToggleRounding:(e,a)=>{const t=[...s];t[e]={...t[e],roundResult:a},i(t)}})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(N,{value:x,onChange:e=>{p(e),v([]),_([])}}),e.jsx(S,{tagName:l,onTagNameChange:o,tagType:x,multiplier:d,onMultiplierChange:g,maxValue:u,onMaxValueChange:h,startMonth:f,onStartMonthChange:b,endMonth:w,onEndMonthChange:R,ageValue:F,onAgeValueChange:k,startWeight:W,onStartWeightChange:V,endWeight:$,onEndWeightChange:T,onAddTag:()=>{if(!l)return;const e={name:l,multiplier:d,maxValue:u,type:x,roundResult:A};"age"===x||"multiplier_by_fixed_age"===x?e.ageRanges=[...j]:"fixed_by_weight"===x&&(e.weightRanges=[...y]),i([...s,e]),o(""),g(1),h(void 0),v([]),_([]),E(!1)},onAddAgeRange:()=>{f>w||!l||(v([...j,{startMonth:f,endMonth:w,value:F}]),b(0),R(0),k(0))},onAddWeightRange:()=>{W>$||!l||(_([...y,{startWeight:W,endWeight:$,value:d}]),V(0),T(0),g(0))},ageRangesCount:j.length,weightRangesCount:y.length,roundResult:A,onRoundResultChange:E}),e.jsx(C,{ranges:j,onRemove:e=>{v(j.filter(((a,t)=>t!==e)))}})]})]})}function W({formData:a,setFormData:t,useCases:s,medicationId:i}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"name",children:"Nome da Dosagem"}),e.jsx(d,{id:"name",value:a.name,onChange:e=>t({...a,name:e.target.value}),placeholder:"Ex: Dose padrão"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"age_group",children:"Faixa Etária"}),e.jsxs(g,{value:a.age_group,onValueChange:e=>t({...a,age_group:e}),children:[e.jsx(u,{children:e.jsx(h,{placeholder:"Selecione a faixa etária"})}),e.jsxs(x,{children:[e.jsx(p,{value:"neonatal",children:"Neonatal"}),e.jsx(p,{value:"pediatric",children:"Pediátrica"}),e.jsx(p,{value:"adult",children:"Adulto"})]})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"use_case",children:"Indicação de Uso"}),e.jsxs(g,{value:a.use_case_id,onValueChange:e=>t({...a,use_case_id:e}),children:[e.jsx(u,{children:e.jsx(h,{placeholder:"Selecione uma indicação"})}),e.jsx(x,{children:s.map((a=>e.jsx(p,{value:a.id,children:a.name},a.id)))})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"summary",children:"Resumo"}),e.jsx(j,{id:"summary",value:a.summary,onChange:e=>t({...a,summary:e.target.value}),placeholder:"Breve descrição da dosagem",className:"min-h-[100px] resize-y"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"dosage_template",children:"Template da Dosagem"}),e.jsx(d,{id:"dosage_template",value:a.dosage_template,onChange:e=>t({...a,dosage_template:e.target.value}),placeholder:"Ex: ((peso)) mg/kg"})]}),e.jsxs("div",{children:[e.jsx(r,{children:"Tags da Dosagem"}),e.jsx(k,{tags:a.tags,onTagsChange:e=>t({...a,tags:e}),medicationId:i})]})]})}function V(e,a){return e.map((e=>{const t={medication_id:a,name:e.name,type:e.type,multiplier:e.multiplier,max_value:e.maxValue,round_result:e.roundResult};return"age"===e.type&&e.ageRanges?.[0]||"multiplier_by_fixed_age"===e.type&&e.ageRanges?.[0]||"multiplier_by_fixed_age"===e.type&&e.ageRanges?.[0]?{...t,start_month:e.ageRanges[0].startMonth,end_month:e.ageRanges[0].endMonth,multiplier:e.ageRanges[0].value}:"fixed_by_weight"===e.type&&e.weightRanges?.[0]?{...t,start_weight:e.weightRanges[0].startWeight,end_weight:e.weightRanges[0].endWeight,multiplier:e.weightRanges[0].value}:t}))}const $=({formId:t,storageKey:s,children:i,debug:n=!0})=>{const r=a.useRef(null),l=a.useRef(!1),o=()=>{const e=document.getElementById(t);if(!e)return;r.current=e;const a=new FormData(e),i={};a.forEach(((e,a)=>{i[a]=e})),e.querySelectorAll("textarea").forEach((e=>{i[e.name||e.id]=e.value})),localStorage.setItem(`form_${s}`,JSON.stringify(i))},d=()=>{const e=localStorage.getItem(`form_${s}`);if(e)try{const a=JSON.parse(e),s=document.getElementById(t);if(!s)return;l.current=!0,Object.entries(a).forEach((([e,a])=>{const t=s.elements.namedItem(e);t&&("checkbox"===t.type||"radio"===t.type?t.checked="on"===a||!0===a:t.value=a)})),l.current=!1}catch(a){}};return a.useEffect((()=>{const e=()=>{"hidden"===document.visibilityState?o():"visible"===document.visibilityState&&setTimeout((()=>{d()}),100)};return document.addEventListener("visibilitychange",e),setTimeout((()=>{d()}),500),()=>{document.removeEventListener("visibilitychange",e),o()}}),[t,s]),e.jsx(e.Fragment,{children:i})};function T({medicationId:i,dosage:n,onSuccess:r,onCancel:o}){const[d,c]=a.useState({medication_id:i,name:"",summary:"",dosage_template:"",tags:[],use_case_id:"",age_group:"pediatric"}),{toast:g}=v(),u=s(),{data:h}=t({queryKey:["medication-use-cases",i],queryFn:async()=>{const{data:e,error:a}=await m.from("pedbook_medication_use_cases").select("*").eq("medication_id",i).order("name");if(a)throw a;return e}});return a.useEffect((()=>{(async()=>{if(n){const{data:e}=await m.from("pedbook_medication_tags").select("*").eq("medication_id",i);c({medication_id:n.medication_id||i,name:n.name||"",summary:n.summary||"",dosage_template:n.dosage_template||"",use_case_id:n.use_case_id||"",age_group:n.age_group||"pediatric",tags:e?.map((e=>({name:e.name,multiplier:e.multiplier||0,maxValue:e.max_value,type:e.type,...("age"===e.type||"multiplier_by_fixed_age"===e.type)&&{ageRanges:[{startMonth:e.start_month||0,endMonth:e.end_month||0,value:e.multiplier||0}]},..."fixed_by_weight"===e.type&&{weightRanges:[{startWeight:e.start_weight||0,endWeight:e.end_weight||0,value:e.multiplier||0}]}})))||[]})}})()}),[n,i]),e.jsx($,{formId:"dosage-form",storageKey:`dosage_${n?.id||"new"}_${i}`,debug:!0,children:e.jsxs("form",{id:"dosage-form",onSubmit:async e=>{e.preventDefault();try{const e={medication_id:d.medication_id,name:d.name,summary:d.summary||null,dosage_template:d.dosage_template,use_case_id:d.use_case_id||null,type:"standard",age_group:d.age_group};let a;if(n){const{data:t,error:s}=await m.from("pedbook_medication_dosages").update(e).eq("id",n.id).select().single();if(s)throw s;a=n.id;const{error:r}=await m.from("pedbook_medication_tags").delete().eq("medication_id",i);if(r)throw r;if(d.tags.length>0){const e=V(d.tags,i),{error:a}=await m.from("pedbook_medication_tags").insert(e);if(a)throw a}}else{const{data:t,error:s}=await m.from("pedbook_medication_dosages").insert(e).select().single();if(s)throw s;if(a=t.id,d.tags.length>0){const e=V(d.tags,i),{error:a}=await m.from("pedbook_medication_tags").insert(e);if(a)throw a}}g({title:n?"Dosagem atualizada com sucesso!":"Dosagem criada com sucesso!",description:`A dosagem ${d.name} foi ${n?"atualizada":"adicionada"}.`}),u.invalidateQueries({queryKey:["medication-dosages"]}),u.invalidateQueries({queryKey:["medication-tags"]}),r?.()}catch(a){g({variant:"destructive",title:"Erro ao salvar dosagem",description:a.message})}},className:"space-y-4",children:[e.jsx(W,{formData:d,setFormData:c,useCases:h||[],medicationId:i}),e.jsxs("div",{className:"flex justify-end gap-2",children:[o&&e.jsx(l,{type:"button",variant:"outline",onClick:o,children:"Cancelar"}),e.jsx(l,{type:"submit",children:n?"Atualizar":"Criar"})]})]})})}function A({selectedMedicationId:a,onEdit:i}){const{toast:n}=v(),r=s(),{data:o}=t({queryKey:["medication-dosages",a],queryFn:async()=>{if(!a)return[];const{data:e,error:t}=await m.from("pedbook_medication_dosages").select("\n          *,\n          pedbook_medications (\n            name,\n            slug\n          )\n        ").eq("medication_id",a).order("name");if(t)throw t;return e},enabled:!!a});return a?o?.length?e.jsx("div",{className:"space-y-4",children:o?.map((a=>e.jsxs("div",{className:"flex justify-between items-start p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:a.name}),a.summary&&e.jsx("p",{className:"text-sm text-gray-600",children:a.summary}),a.explanation&&e.jsx("p",{className:"text-sm text-gray-600 whitespace-pre-line",children:a.explanation})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(l,{variant:"outline",size:"icon",onClick:()=>i(a),className:"hover:bg-primary/10",children:e.jsx(f,{className:"h-4 w-4"})}),e.jsx(l,{variant:"ghost",size:"icon",onClick:()=>(async(e,a)=>{try{const{error:t}=await m.from("pedbook_medication_dosages").delete().eq("id",e);if(t)throw t;n({title:"Dosagem excluída com sucesso!",description:`A dosagem ${a} foi excluída.`}),r.invalidateQueries({queryKey:["medication-dosages"]})}catch(t){n({variant:"destructive",title:"Erro ao excluir dosagem",description:t.message})}})(a.id,a.name),className:"hover:bg-destructive/10",children:e.jsx(b,{className:"h-4 w-4"})})]})]},a.id)))}):e.jsx("div",{className:"text-center text-gray-500 py-8",children:"Nenhuma dosagem cadastrada para este medicamento"}):e.jsx("div",{className:"text-center text-gray-500 py-8",children:"Selecione um medicamento para ver suas dosagens"})}export{A as D,$ as F,T as a};
