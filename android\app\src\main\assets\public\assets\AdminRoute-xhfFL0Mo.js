import{j as r}from"./radix-core-6kBL75b5.js";import{r as e}from"./critical-DVX9Inzy.js";import{d as s}from"./supabase-vendor-qi_Ptfv-.js";import{u as t,d as i,ax as a,L as n}from"./index-CR7o3nEo.js";import{u as o,a as d,O as c}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";function u(){const u=s.useSupabaseClient(),m=o(),{user:l,profile:p,isAdmin:f,isLoading:v}=t(),[j,h]=e.useState(null),[A,S]=e.useState(null),[x,g]=e.useState([]),[y,b]=e.useState(!0),[_,w]=e.useState(!1),{toast:P}=i(),N=d(),q=a(),I=m.pathname,V=I.split("/admin/")[1]?.split("/")[0]||"dashboard",E=()=>{try{const r=localStorage.getItem("adminState");if(r){const e=JSON.parse(r);if(e.userId===l?.id)return e.state}}catch(r){}return{isAdmin:null,isSuperAdmin:null,userPermissions:[]}},O=r=>{try{localStorage.setItem("adminState",JSON.stringify({userId:l?.id,state:r,timestamp:Date.now()}))}catch(e){}},J=e.useRef(E());e.useRef(!1);const L=l?.id,R=!v&&l&&f;return e.useEffect((()=>{_&&null!==J.current.isAdmin||(R?(async()=>{if(q.isProtectionActive()){if(null===J.current.isAdmin){const r=E();J.current=r}return null!==J.current.isAdmin&&(h(J.current.isAdmin),S(J.current.isSuperAdmin),g(J.current.userPermissions)),b(!1),void w(!0)}try{if(b(!0),!l)return P({variant:"destructive",title:"Acesso negado",description:"Você precisa estar logado para acessar esta página."}),void N("/");if(!f)return P({variant:"destructive",title:"Acesso negado",description:"Você não tem permissão para acessar esta página."}),void N("/");h(!0),J.current.isAdmin=!0,O(J.current);const{data:r,error:e}=await u.from("admin_user_roles").select("role_id").eq("user_id",l.id);if(e)throw e;const{data:s}=await u.from("admin_roles").select("id, name").eq("name","super_admin").maybeSingle(),t=s?.id,i=r.some((r=>r.role_id===t));if(S(i),J.current.isSuperAdmin=i,O(J.current),J.current={isAdmin:!0,isSuperAdmin:i,userPermissions:i?["all"]:[]},i)return g(["all"]),J.current.userPermissions=["all"],O(J.current),void b(!1);try{const{data:r,error:e}=await u.from("admin_user_permissions").select("resource").eq("user_id",l.id);if(e)throw e;const s=r.map((r=>r.resource)),t=["dashboard",...s];g(t),J.current={...J.current,userPermissions:t},O(J.current),"dashboard"===V||s.includes(V)||(P({variant:"destructive",title:"Acesso restrito",description:"Você não tem permissão para acessar este módulo."}),N("/admin/dashboard"))}catch{g(["all"]),J.current={...J.current,userPermissions:["all"]},O(J.current)}}catch(r){P({variant:"destructive",title:"Erro",description:"Não foi possível verificar suas permissões de administrador."}),N("/")}finally{b(!1),w(!0)}})():v||l||N("/"))}),[L,f,v,_]),e.useEffect((()=>{j&&!y&&!A&&x.length>0&&"all"!==x[0]&&("dashboard"===V||x.includes(V)||(P({variant:"destructive",title:"Acesso restrito",description:"Você não tem permissão para acessar este módulo."}),N("/admin/dashboard")))}),[V,x,j,y,A]),v||y||!_?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(n,{className:"h-8 w-8 animate-spin text-primary"})}):j?r.jsx(c,{}):null}export{u as default};
