import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{u as a,a as t,c as i}from"./query-vendor-B-7l6Nb3.js";import{c as r,d as o,an as l,a5 as n,s as c,ay as d,B as m,X as u,ad as h,ae as g,af as x,ag as p,ai as j,T as v,D as f,aK as b,e as y,f as N,g as w,aq as C,ar as S,as as _,at as k}from"./index-CR7o3nEo.js";import{c as q}from"./form-vendor-rYZw_ur7.js";import{S as z}from"./switch-BlCL6T7n.js";import{u as E,E as F,S as D,U as T,L as P,T as L,C as V}from"./editor-vendor-0G6QaH11.js";import{B as K,I as U}from"./italic-CnA1bIzx.js";import{U as A}from"./underline-D0oLySQL.js";import{L as I}from"./list-CP8FHcAb.js";import{L as Q,H as M}from"./list-ordered-D99m_DXa.js";import{a as O}from"./router-BAzpOxbo.js";import{P as B}from"./pencil-BmDCKKnl.js";import{T as H}from"./trash-2-BHlncC12.js";import{P as $}from"./plus-C7-Pt48y.js";import{S as R}from"./square-pen-DG-j5oX5.js";import{T as G}from"./trash-BH7L-Ic3.js";import{A as Z,a as J,b as W,c as X,d as Y,e as ee,f as se,g as ae}from"./alert-dialog-DjBGocRG.js";import"./supabase-vendor-qi_Ptfv-.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=r("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]),ie=r("Quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function re({imageUrl:a,setImageUrl:t}){const[i,r]=s.useState(!1),{toast:d}=o();return e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"image",children:"Imagem Destacada"}),e.jsx(n,{id:"image",type:"file",accept:"image/*",onChange:async e=>{try{if(r(!0),!e.target.files||0===e.target.files.length)throw new Error("Você precisa selecionar uma imagem para fazer upload.");const s=e.target.files[0],a=s.name.split(".").pop(),i=`${Math.random()}.${a}`,{error:o}=await c.storage.from("blog-images").upload(i,s);if(o)throw o;const{data:{publicUrl:l}}=c.storage.from("blog-images").getPublicUrl(i);t(l),d({title:"Sucesso!",description:"Imagem enviada com sucesso."})}catch(s){d({variant:"destructive",title:"Erro ao fazer upload da imagem",description:s.message})}finally{r(!1)}},disabled:i}),a&&e.jsx("img",{src:a,alt:"Preview",className:"mt-2 max-w-xs rounded-lg shadow-md"})]})}function oe({selectedTags:s,onTagsChange:t}){const{data:i,isLoading:r}=a({queryKey:["blog-tags"],queryFn:async()=>{const{data:e,error:s}=await c.from("pedbook_blog_tags").select("*").order("name");if(s)throw s;return e}}),o=e=>{s.includes(e)?t(s.filter((s=>s!==e))):t([...s,e])};return r?e.jsx("div",{children:"Carregando tags..."}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:i?.map((a=>e.jsxs(d,{variant:s.includes(a.id)?"default":"outline",className:"cursor-pointer",onClick:()=>o(a.id),children:[a.name,s.includes(a.id)&&e.jsx(m,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 hover:bg-transparent",onClick:e=>{e.stopPropagation(),o(a.id)},children:e.jsx(u,{className:"h-3 w-3"})})]},a.id)))})]})}const le=s.forwardRef((({value:s,onValueChange:t},i)=>{const{data:r,isLoading:o}=a({queryKey:["blog-categories"],queryFn:async()=>{const{data:e,error:s}=await c.from("pedbook_blog_categories").select("*").order("name");if(s)throw s;return e}});return o?e.jsx("div",{children:"Carregando categorias..."}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"category",children:"Categoria"}),e.jsxs(h,{value:s,onValueChange:t,children:[e.jsx(g,{ref:i,children:e.jsx(x,{placeholder:"Selecione uma categoria"})}),e.jsx(p,{children:r?.map((s=>e.jsx(j,{value:s.id,children:s.name},s.id)))})]})]})}));function ne({content:s,onChange:a}){const t=E({extensions:[D,T,P,L,V],content:s,onUpdate:({editor:e})=>{a(e.getHTML())}});return t?e.jsxs("div",{className:"border rounded-lg overflow-hidden",children:[e.jsxs("div",{className:"bg-white border-b p-2 flex flex-wrap gap-1",children:[e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleBold().run()},className:t.isActive("bold")?"bg-muted":"",children:e.jsx(K,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleItalic().run()},className:t.isActive("italic")?"bg-muted":"",children:e.jsx(U,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleUnderline().run()},className:t.isActive("underline")?"bg-muted":"",children:e.jsx(A,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleBulletList().run()},className:t.isActive("bulletList")?"bg-muted":"",children:e.jsx(I,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleOrderedList().run()},className:t.isActive("orderedList")?"bg-muted":"",children:e.jsx(Q,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleHeading({level:1}).run()},className:t.isActive("heading",{level:1})?"bg-muted":"",children:e.jsx(te,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleHeading({level:2}).run()},className:t.isActive("heading",{level:2})?"bg-muted":"",children:e.jsx(M,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",variant:"ghost",size:"sm",onClick:e=>{e.preventDefault(),t.chain().focus().toggleBlockquote().run()},className:t.isActive("blockquote")?"bg-muted":"",children:e.jsx(ie,{className:"h-4 w-4"})})]}),e.jsx(F,{editor:t,className:"prose prose-sm max-w-none p-4 min-h-[200px] focus:outline-none selectable-text"})]}):null}function ce({postId:a,initialData:t,onSuccess:i}){const[r,d]=s.useState(t?.featured_image||""),[u,h]=s.useState([]),[g,x]=s.useState(t?.category_id||""),[p,j]=s.useState(t?.content||""),[f,b]=s.useState(!1),{toast:y}=o();O();const{register:N,handleSubmit:w,formState:{errors:C},reset:S,setValue:_}=q({defaultValues:t||{}});return s.useEffect((()=>{t&&(S(t),d(t.featured_image||""),x(t.category_id||""),j(t.content||""))}),[t,S]),e.jsxs("form",{onSubmit:w((async e=>{try{b(!0);const{data:{user:s},error:t}=await c.auth.getUser();if(t)throw t;if(!s)throw new Error("User not authenticated");const o=(new Date).toISOString(),l={title:e.title,slug:e.slug,content:p,excerpt:e.excerpt,category_id:g||null,featured_image:r||null,published:e.published,author_id:s.id,published_at:e.published?o:null,updated_at:o,...a&&{id:a}},{data:n,error:d}=await c.from("pedbook_blog_posts").upsert(l).select().single();if(d)throw d;if(!n)throw new Error("Failed to save post");if(u.length>0){a&&await c.from("pedbook_blog_posts_tags").delete().eq("post_id",a);const e=u.map((e=>({post_id:n.id,tag_id:e}))),{error:s}=await c.from("pedbook_blog_posts_tags").insert(e);if(s)throw s}y({title:"Sucesso!",description:"Post salvo com sucesso."}),i?.()}catch(s){y({variant:"destructive",title:"Erro ao salvar o post",description:s.message})}finally{b(!1)}})),className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"title",children:"Título"}),e.jsx(n,{id:"title",...N("title",{required:"Título é obrigatório"})}),C.title&&e.jsx("p",{className:"text-sm text-red-500",children:C.title.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"slug",children:"Slug"}),e.jsx(n,{id:"slug",...N("slug",{required:"Slug é obrigatório"})}),C.slug&&e.jsx("p",{className:"text-sm text-red-500",children:C.slug.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"content",children:"Conteúdo"}),e.jsx(ne,{content:p,onChange:j}),C.content&&e.jsx("p",{className:"text-sm text-red-500",children:C.content.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"excerpt",children:"Resumo"}),e.jsx(v,{id:"excerpt",...N("excerpt")})]}),e.jsx(re,{imageUrl:r,setImageUrl:d}),e.jsx(le,{value:g,onValueChange:x}),e.jsx(oe,{selectedTags:u,onTagsChange:h}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{id:"published",...N("published")}),e.jsx(l,{htmlFor:"published",children:"Publicar"})]}),e.jsx(m,{type:"submit",disabled:f,children:f?"Salvando...":"Salvar Post"})]})}le.displayName="CategorySelector";const de=({onEdit:s,onDelete:t})=>{const i=O(),{data:r,isLoading:o}=a({queryKey:["admin-blog-posts"],queryFn:async()=>{const{data:e,error:s}=await c.from("pedbook_blog_posts").select("\n          *,\n          author:profiles(full_name),\n          pedbook_blog_categories(name)\n        ").order("created_at",{ascending:!1});if(s)throw s;return e}});return o?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):e.jsx("div",{className:"grid gap-4",children:r?.map((a=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"font-semibold text-lg",children:a.title}),e.jsxs("p",{className:"text-sm text-gray-500",children:["por ",a.author?.full_name," em"," ",new Date(a.created_at).toLocaleDateString()]}),a.pedbook_blog_categories?.name&&e.jsx("span",{className:"inline-block bg-primary/10 text-primary text-xs px-2 py-1 rounded",children:a.pedbook_blog_categories.name})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(m,{variant:"outline",size:"sm",onClick:()=>s(a),children:[e.jsx(B,{className:"h-4 w-4 mr-2"}),"Editar"]}),e.jsxs(m,{variant:"destructive",size:"sm",onClick:()=>t(a),children:[e.jsx(H,{className:"h-4 w-4 mr-2"}),"Excluir"]})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-4",children:[e.jsx("span",{className:"text-sm "+(a.published?"text-green-600":"text-yellow-600"),children:a.published?"Publicado":"Rascunho"}),e.jsx(m,{variant:"link",onClick:()=>i(`/blog/post/${a.id}`),className:"text-primary hover:text-primary/80",children:"Visualizar post"})]})]},a.id)))})};function me(){const[r,d]=s.useState(!1),[u,h]=s.useState(null),[g,x]=s.useState(""),[p,j]=s.useState(""),[C,S]=s.useState(""),{toast:_}=o(),k=t(),{data:q,isLoading:z}=a({queryKey:["blog-categories"],queryFn:async()=>{const{data:e,error:s}=await c.from("pedbook_blog_categories").select("*").order("name");if(s)throw s;return e}}),E=i({mutationFn:async e=>{const{data:s,error:a}=await c.from("pedbook_blog_categories").upsert(e).select().single();if(a)throw a;return s},onSuccess:()=>{k.invalidateQueries({queryKey:["blog-categories"]}),d(!1),D(),_({title:"Sucesso!",description:`Categoria ${u?"atualizada":"criada"} com sucesso.`})},onError:e=>{_({variant:"destructive",title:"Erro",description:e.message})}}),F=i({mutationFn:async e=>{const{error:s}=await c.from("pedbook_blog_categories").delete().eq("id",e);if(s)throw s},onSuccess:()=>{k.invalidateQueries({queryKey:["blog-categories"]}),_({title:"Sucesso!",description:"Categoria excluída com sucesso."})},onError:e=>{_({variant:"destructive",title:"Erro",description:e.message})}}),D=()=>{h(null),x(""),j(""),S("")};return z?e.jsx("div",{children:"Carregando categorias..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Categorias"}),e.jsxs(f,{open:r,onOpenChange:e=>{d(e),e||D()},children:[e.jsx(b,{asChild:!0,children:e.jsxs(m,{children:[e.jsx($,{className:"w-4 h-4 mr-2"}),"Nova Categoria"]})}),e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(w,{children:u?"Editar Categoria":"Nova Categoria"})}),e.jsxs("form",{onSubmit:e=>{e.preventDefault(),E.mutate({id:u?.id,name:g,slug:p,description:C})},className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"name",children:"Nome"}),e.jsx(n,{id:"name",value:g,onChange:e=>x(e.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"slug",children:"Slug"}),e.jsx(n,{id:"slug",value:p,onChange:e=>j(e.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"description",children:"Descrição"}),e.jsx(v,{id:"description",value:C,onChange:e=>S(e.target.value)})]}),e.jsx(m,{type:"submit",children:u?"Atualizar":"Criar"})]})]})]})]}),e.jsx("div",{className:"grid gap-4",children:q?.map((s=>e.jsxs("div",{className:"flex justify-between items-center p-4 bg-white rounded-lg border",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.name}),e.jsx("p",{className:"text-sm text-gray-500",children:s.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{variant:"ghost",size:"icon",onClick:()=>(e=>{h(e),x(e.name),j(e.slug),S(e.description||""),d(!0)})(s),children:e.jsx(R,{className:"w-4 h-4"})}),e.jsx(m,{variant:"ghost",size:"icon",onClick:()=>(async e=>{F.mutate(e)})(s.id),children:e.jsx(G,{className:"w-4 h-4"})})]})]},s.id)))})]})}function ue(){const[r,d]=s.useState(!1),[u,h]=s.useState(null),[g,x]=s.useState(""),[p,j]=s.useState(""),{toast:v}=o(),C=t(),{data:S,isLoading:_}=a({queryKey:["blog-tags"],queryFn:async()=>{const{data:e,error:s}=await c.from("pedbook_blog_tags").select("*").order("name");if(s)throw s;return e}}),k=i({mutationFn:async e=>{const{data:s,error:a}=await c.from("pedbook_blog_tags").upsert(e).select().single();if(a)throw a;return s},onSuccess:()=>{C.invalidateQueries({queryKey:["blog-tags"]}),d(!1),z(),v({title:"Sucesso!",description:`Tag ${u?"atualizada":"criada"} com sucesso.`})},onError:e=>{v({variant:"destructive",title:"Erro",description:e.message})}}),q=i({mutationFn:async e=>{const{error:s}=await c.from("pedbook_blog_tags").delete().eq("id",e);if(s)throw s},onSuccess:()=>{C.invalidateQueries({queryKey:["blog-tags"]}),v({title:"Sucesso!",description:"Tag excluída com sucesso."})},onError:e=>{v({variant:"destructive",title:"Erro",description:e.message})}}),z=()=>{h(null),x(""),j("")};return _?e.jsx("div",{children:"Carregando tags..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Tags"}),e.jsxs(f,{open:r,onOpenChange:e=>{d(e),e||z()},children:[e.jsx(b,{asChild:!0,children:e.jsxs(m,{children:[e.jsx($,{className:"w-4 h-4 mr-2"}),"Nova Tag"]})}),e.jsxs(y,{children:[e.jsx(N,{children:e.jsx(w,{children:u?"Editar Tag":"Nova Tag"})}),e.jsxs("form",{onSubmit:e=>{e.preventDefault(),k.mutate({id:u?.id,name:g,slug:p})},className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"name",children:"Nome"}),e.jsx(n,{id:"name",value:g,onChange:e=>x(e.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"slug",children:"Slug"}),e.jsx(n,{id:"slug",value:p,onChange:e=>j(e.target.value),required:!0})]}),e.jsx(m,{type:"submit",children:u?"Atualizar":"Criar"})]})]})]})]}),e.jsx("div",{className:"grid gap-4",children:S?.map((s=>e.jsxs("div",{className:"flex justify-between items-center p-4 bg-white rounded-lg border",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.name}),e.jsx("p",{className:"text-sm text-gray-500",children:s.slug})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{variant:"ghost",size:"icon",onClick:()=>(e=>{h(e),x(e.name),j(e.slug),d(!0)})(s),children:e.jsx(R,{className:"w-4 h-4"})}),e.jsx(m,{variant:"ghost",size:"icon",onClick:()=>(async e=>{q.mutate(e)})(s.id),children:e.jsx(G,{className:"w-4 h-4"})})]})]},s.id)))})]})}function he(){const[a,i]=s.useState("posts"),[r,l]=s.useState(!1),[n,d]=s.useState(null),[u,h]=s.useState(!1),[g,x]=s.useState(null),{toast:p}=o(),j=t();return e.jsxs("div",{className:"container mx-auto py-8 px-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Gerenciamento do Blog"}),e.jsxs(f,{open:r,onOpenChange:l,children:[e.jsx(b,{asChild:!0,children:e.jsxs(m,{children:[e.jsx($,{className:"w-4 h-4 mr-2"}),"Novo Post"]})}),e.jsxs(y,{className:"max-w-3xl max-h-[90vh] overflow-y-auto",children:[e.jsx(N,{children:e.jsx(w,{children:n?"Editar Post":"Novo Post"})}),e.jsx(ce,{postId:n?.id,initialData:n,onSuccess:async()=>{l(!1),d(null),await j.invalidateQueries({queryKey:["admin-blog-posts"]})}})]})]})]}),e.jsxs(C,{value:a,onValueChange:i,children:[e.jsxs(S,{children:[e.jsx(_,{value:"posts",children:"Posts"}),e.jsx(_,{value:"categories",children:"Categorias"}),e.jsx(_,{value:"tags",children:"Tags"})]}),e.jsx(k,{value:"posts",className:"mt-6",children:e.jsx(de,{onEdit:e=>{d(e),l(!0)},onDelete:e=>{x(e),h(!0)}})}),e.jsx(k,{value:"categories",className:"mt-6",children:e.jsx(me,{})}),e.jsx(k,{value:"tags",className:"mt-6",children:e.jsx(ue,{})})]}),e.jsx(Z,{open:u,onOpenChange:h,children:e.jsxs(J,{children:[e.jsxs(W,{children:[e.jsx(X,{children:"Você tem certeza?"}),e.jsx(Y,{children:"Esta ação não pode ser desfeita. Isso excluirá permanentemente o post."})]}),e.jsxs(ee,{children:[e.jsx(se,{children:"Cancelar"}),e.jsx(ae,{onClick:async()=>{if(g)try{const{error:e}=await c.from("pedbook_blog_posts").delete().eq("id",g.id);if(e)throw e;p({title:"Sucesso!",description:"Post excluído com sucesso."}),await j.invalidateQueries({queryKey:["admin-blog-posts"]})}catch(e){p({variant:"destructive",title:"Erro ao excluir o post",description:e.message})}finally{h(!1),x(null)}},children:"Confirmar"})]})]})})]})}export{he as default};
