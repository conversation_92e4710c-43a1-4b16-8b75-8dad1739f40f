import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{L as s}from"./router-BAzpOxbo.js";import{an as i,a5 as l,R as o,B as r,aT as t,aU as n,aa as c,a7 as d}from"./index-D9amGMlQ.js";import m from"./Footer-BkFd5qSK.js";import{F as x,a as u}from"./flowchartSEOData-B7-hWfhR.js";import{S as h}from"./scale-DPUkT8Aa.js";import{A as p,b as j}from"./alert-DF0vYpCj.js";import{A as g,a as N,b,c as v,d as f,e as y}from"./alert-dialog-DKHxUe7X.js";import{u as C}from"./useWeight-CatlFLFx.js";import{u as w}from"./useAge-C_36_Zbj.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./target-Cj27UDYs.js";import"./zap-DULtmWB8.js";import"./book-open-CzUd5kBy.js";import"./star-BlLX_9hT.js";import"./circle-help-DFUIKtE9.js";import"./instagram-CuaDlQAQ.js";const S=({weight:a,onWeightChange:s,onWeightCommit:o,age:r,onAgeChange:t,onAgeCommit:n})=>e.jsxs("div",{className:"space-y-6 bg-gradient-to-br from-primary/5 via-primary/10 to-transparent p-6 rounded-xl backdrop-blur-sm border border-primary/10",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(i,{htmlFor:"weight",className:"text-lg font-medium text-red-700 flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5"}),"Peso do Paciente"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(l,{id:"weight",type:"number",min:0,max:100,value:a||"",onChange:e=>{const a=parseFloat(e.target.value);if(!isNaN(a)){const e=Math.min(a,100);s(e),o(e)}},className:"flex-1 bg-white/50 border-red-200 focus:border-red-400 transition-colors text-center text-lg",placeholder:"Digite o peso"}),e.jsx("span",{className:"text-lg font-medium text-red-700 min-w-[3rem]",children:"kg"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"age",className:"text-lg font-medium text-primary flex items-center gap-2",children:"Idade do Paciente"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(l,{id:"age",type:"number",min:0,max:18,value:r||"",onChange:e=>{const a=parseFloat(e.target.value);if(!isNaN(a)){const e=Math.min(a,18);t(e),n(e)}},className:"flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center text-lg",placeholder:"Digite a idade"}),e.jsx("span",{className:"text-lg font-medium text-primary min-w-[3rem]",children:"anos"})]})]})]}),A=({onStart:a})=>e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Manejo de Cetoacidose Diabética"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Quadro clínico:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 text-gray-600",children:[e.jsx("li",{children:"Hiperglicemia: Poliúria, polidipsia, fadiga, perda de peso"}),e.jsx("li",{children:"Acidose metabólica: Náuseas, vômitos, dor abdominal, respiração de Kussmaul, hálito cetônico"}),e.jsx("li",{children:"Desidratação: Mucosas secas, taquicardia, hipotensão."}),e.jsx("li",{children:"Alterações neurológicas: Confusão, letargia, podendo evoluir para coma."})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Exames necessários para investigação:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 text-gray-600",children:[e.jsx("li",{children:"Glicemia capilar e venosa"}),e.jsx("li",{children:"Gasometria arterial ou venosa"}),e.jsx("li",{children:"Eletrólitos séricos (sódio, potássio, cloreto)"}),e.jsx("li",{children:"Bicarbonato"}),e.jsx("li",{children:"Corpos cetônicos no sangue e urina"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Já tem os resultados dos exames? Inicie o fluxo."}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-600"})]}),e.jsx(r,{onClick:a,className:"w-full",children:"Iniciar avaliação"})]}),k=({onDiagnosisComplete:s})=>{const[i,t]=a.useState("glucose"),[n,c]=a.useState({glucose:"",acidosisStatus:"",ketones:!1}),d=e=>{c((a=>({...a,ketones:e}))),s(e)};if("glucose"===i)return e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Avaliação da Glicemia"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Glicemia (mg/dL)"}),e.jsx(l,{type:"number",value:n.glucose,onChange:e=>c((a=>({...a,glucose:e.target.value}))),placeholder:"Informe o valor da glicemia"})]}),e.jsx(r,{onClick:()=>{parseFloat(n.glucose)<=200?s(!1):t("acidosis")},disabled:!n.glucose,className:"w-full",children:"Confirmar"})]});if("acidosis"===i){const a=["pH < 7,3","Bicarbonato < 15 mEq/L","Ambos alterados","Nenhum alterado"];return e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Avaliação da Acidose"}),e.jsx("p",{className:"text-gray-600",children:"Qual o pH arterial ou bicarbonato sérico?"}),e.jsx("div",{className:"grid grid-cols-1 gap-2",children:a.map((a=>e.jsx(r,{variant:"outline",className:"justify-start",onClick:()=>{return e=a,c((a=>({...a,acidosisStatus:e}))),void("Nenhum alterado"===e?s(!1):t("ketones"));var e},children:a},a)))})]})}return e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Avaliação de Cetonas"}),e.jsx("p",{className:"text-gray-600",children:"Paciente com cetonemia e/ou cetonúria?"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(r,{onClick:()=>d(!0),variant:"outline",children:"Sim"}),e.jsx(r,{onClick:()=>d(!1),variant:"outline",children:"Não"})]})]})},F=({onAssessmentComplete:s})=>{const[i,l]=a.useState({}),[t,n]=a.useState("");return e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Ferramenta de Avaliação da Desidratação"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Esta ferramenta auxilia na avaliação dos sinais de desidratação, mas não substitui o julgamento clínico."}),e.jsx("div",{className:"space-y-4",children:[{id:"estadoGeral",texto:"Estado geral do paciente",opcoes:["Ativo e alerta","Irritado ou intranquilo","Comatoso ou letárgico"]},{id:"olhos",texto:"Olhos do paciente",opcoes:["Sem alteração","Fundos","Muito fundos"]},{id:"sede",texto:"O paciente está com sede?",opcoes:["Sem sede","Sedento e bebe rápido","Não consegue beber"]},{id:"lagrimas",texto:"Lágrimas",opcoes:["Presentes","Ausentes"]},{id:"boca",texto:"Mucosa oral",opcoes:["Úmida","Seca","Muito seca"]},{id:"prega",texto:"Sinal da prega abdominal",opcoes:["Desaparece imediatamente","Desaparece lentamente","Desaparece muito lentamente (> 2 segundos)"]},{id:"pulso",texto:"Pulso do paciente",opcoes:["Cheio","Fraco ou ausente"]}].map((a=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:a.texto}),e.jsxs("select",{onChange:e=>{return s=a.id,i=e.target.value,void l((e=>({...e,[s]:i})));var s,i},className:"w-full p-2 border rounded-md",value:i[a.id]||"",children:[e.jsx("option",{value:"",children:"Selecione uma opção"}),a.opcoes.map(((a,s)=>e.jsx("option",{value:a,children:a},s)))]})]},a.id)))}),e.jsx(r,{onClick:()=>{let e=0,a=0;"Irritado ou intranquilo"===i.estadoGeral&&e++,"Comatoso ou letárgico"===i.estadoGeral&&a++,"Fundos"===i.olhos&&e++,"Muito fundos"===i.olhos&&a++,"Sedento e bebe rápido"===i.sede&&e++,"Não consegue beber"===i.sede&&a++,"Ausentes"===i.lagrimas&&e++,"Seca"===i.boca&&e++,"Muito seca"===i.boca&&a++,"Desaparece lentamente"===i.prega&&e++,"Desaparece muito lentamente (> 2 segundos)"===i.prega&&a++,"Fraco ou ausente"===i.pulso&&a++,a>=1?(n("Desidratação grave (Plano C)"),s(!0)):e>=2?(n("Com desidratação (Plano B)"),s(!0)):(n("Sem sinais de desidratação (Plano A)"),s(!1))},className:"w-full",variant:"outline",children:"Avaliar Desidratação"}),t&&e.jsx(p,{className:t.includes("grave")?"bg-red-100 border-red-200 text-red-800 font-medium":t.includes("Com desidratação")?"bg-yellow-100 border-yellow-200 text-yellow-800 font-medium":t.includes("Sem sinais")?"bg-green-100 border-green-200 text-green-800 font-medium":"",children:e.jsxs(j,{className:"font-medium",children:["Resultado: ",t]})})]})},E=({weight:s,onComplete:i})=>{const[l,t]=a.useState("initial"),[n,c]=a.useState(!1),d=Math.round(10*s),m=Math.round(20*s);return"initial"===l?e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Manejo Inicial"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[e.jsx("h4",{className:"font-medium text-green-800",children:"Confirmação do Diagnóstico de CAD"}),e.jsx("p",{className:"text-green-700",children:"Avaliar sinais de choque, rebaixamento do nível de consciência/coma"})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[e.jsx("h4",{className:"font-medium text-blue-800",children:"Terapia endovenosa:"}),e.jsxs("p",{className:"text-blue-700",children:[e.jsx("li",{children:"Parâmetro a utilizar: SF 10-20 mL/KG"}),e.jsxs("li",{children:["Valor calculado: SF 0,9% ",d,"-",m," mL EV em 20-30 min podendo ser repetido."]})]})]})]}),e.jsx(r,{onClick:()=>t("dehydration"),className:"w-full",children:"Continuar avaliação"})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Avaliação da Desidratação"}),e.jsx("p",{className:"text-gray-600",children:"Após 1 hora, mantém sinais de desidratação?"}),!n&&e.jsx(r,{onClick:()=>c(!0),variant:"outline",className:"w-full",children:"Abrir Ferramenta de Avaliação"}),e.jsx(p,{className:"bg-blue-50 border-blue-200",children:e.jsxs(j,{className:"space-y-2 text-sm text-blue-800",children:[e.jsx("h4",{className:"font-semibold",children:"Critérios de avaliação de desidrataçãao:"}),e.jsxs("ul",{className:"list-disc pl-4 space-y-1",children:[e.jsx("li",{children:"Estado geral: ativo e alerta (sem desidratação), irritado ou intranquilo (com desidratação), comatoso, letárgico, hipotônico ou inconsciente (desidratação grave)"}),e.jsx("li",{children:"Olhos: sem alteração (sem desidratação), fundos (com desidratação ou desidratação grave)"}),e.jsx("li",{children:"Sede: sem sede (sem desidratação), sedento e bebe rápido (com desidratação), incapaz de beber (desidratação grave)"}),e.jsx("li",{children:"Lágrimas: presentes (sem desidratação), ausentes (com desidratação ou desidratação grave)"}),e.jsx("li",{children:"Boca/Língua: úmida (sem desidratação), seca ou muito seca (com desidratação ou desidratação grave)"}),e.jsx("li",{children:"Sinal da prega abdominal: desaparece imediatamente (sem desidratação), desaparece lentamente (com desidratação), desaparece muito lentamente (> 2 segundos) (desidratação grave)"}),e.jsx("li",{children:"Pulso: cheio (sem desidratação ou com desidratação), fraco ou ausente (desidratação grave)"}),e.jsx("li",{children:"Perda de peso: sem perda (sem desidratação), até 10% (com desidratação), acima de 10% (desidratação grave)"})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(r,{onClick:()=>t("initial"),variant:"outline",children:"Sim, repetir expansão"}),e.jsx(r,{onClick:i,variant:"outline",children:"Não, prosseguir"})]})]}),n&&e.jsx(F,{onAssessmentComplete:e=>{}})]})},I=({weight:a,onSelect:s})=>{const l=(.1*a).toFixed(2),r=(.15*a).toFixed(2);return e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Escolha do Tipo de Insulina para Insulinoterapia"}),e.jsx("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:e.jsx(t,{onValueChange:e=>s(e),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"regular",id:"regular"}),e.jsxs(i,{htmlFor:"regular",className:"flex-1",children:[e.jsx("span",{className:"font-medium",children:"Insulina regular EV"}),e.jsxs("p",{className:"text-sm text-blue-700",children:[l," UI/hora em bomba de infusão contínua"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{value:"ultrafast",id:"ultrafast"}),e.jsxs(i,{htmlFor:"ultrafast",className:"flex-1",children:[e.jsx("span",{className:"font-medium",children:"Insulina ultrarrápida SC"}),e.jsxs("p",{className:"text-sm text-blue-700",children:[r," UI a cada 2 horas"]})]})]})]})})})]})},M=({isLow:a,onReturn:s})=>e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:a?"Ajuste para Glicemia Baixa":"Ajuste para Glicemia Alta"}),e.jsx(p,{className:"bg-yellow-50 border-yellow-200",children:e.jsxs(j,{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium text-yellow-800",children:"Recomendações:"}),e.jsx("ul",{className:"list-disc pl-4 text-yellow-700",children:a?e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Aumentar a TIG"}),e.jsx("li",{children:"Manter insulinoterapia"}),e.jsx("p",{className:"text-yellow-700 mt-2",children:"O aumento da TIG poderá ser realizado através do aumento da concentração de glicose no soro, observando-se o limite máximo de 12,5% no acesso periférico."})]}):e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Reduzir a TIG"}),e.jsx("li",{children:"Manter insulinoterapia"}),e.jsx("p",{className:"text-yellow-700 mt-2",children:"TIG mínima de 2,5-3,5 mg/kg/minuto. Se atingir a TIG mínima não reduzir a TIG."})]})})]})}),e.jsx(r,{onClick:s,className:"w-full",children:"Continuar Monitoramento"})]}),D=({weight:s,onTargetReached:i,showPotassiumInfo:l=!1,insulinType:t})=>{const[n,c]=a.useState(!1),d=e=>{e?i(!0):c(!0)};return e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Monitoramento"}),e.jsxs("div",{className:"space-y-4",children:[l&&e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[e.jsx("h4",{className:"font-medium text-blue-800",children:"Incluir na hidratação de manutenção:"}),e.jsx("p",{className:"text-blue-700",children:"Reposição de potássio: 40 mEq/L EV"})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[e.jsx("h4",{className:"font-medium text-green-800",children:"Insulinoterapia:"}),e.jsx("p",{className:"text-green-700",children:"regular"===t?`${(.1*s).toFixed(2)} unidades/hora EV em bomba de infusão`:`${(.15*s).toFixed(2)} unidades SC a cada 2 horas`}),e.jsx("p",{className:"text-green-700 mt-2",children:"Objetivo: Redução de 50-100 mg/dL por hora"})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[e.jsx("h4",{className:"font-medium text-blue-800",children:"Monitoramento:"}),e.jsxs("ul",{className:"list-disc pl-4 text-blue-700",children:[e.jsx("li",{children:"Reavaliar glicemia e clínica a cada hora"}),e.jsx("li",{children:"Exames laboratoriais a cada 2-4 horas"}),e.jsx("li",{children:"Cetonúria a cada micção"})]})]})]}),e.jsx("p",{children:"Glicemia atingiu a faixa de 250-300 mg/dL?"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r,{variant:"outline",onClick:()=>d(!0),className:"flex-1",children:"Sim"}),e.jsx(r,{variant:"outline",onClick:()=>d(!1),className:"flex-1",children:"Não"})]}),n&&e.jsx("div",{className:"bg-yellow-50 p-4 rounded-lg border border-yellow-200 mt-4",children:e.jsx("p",{className:"text-yellow-800 font-medium",children:"Mantenha a insulinoterapia e continue monitorando."})})]})},P=({weight:s,onPotassiumNormal:i})=>{const[l,t]=a.useState(null),[n,c]=a.useState(null),[d,m]=a.useState(0),[x,u]=a.useState(!1),h=e=>{t(e),e||i(!e)},p=e=>{e?(c(!0),u(!0),setTimeout((()=>{i(!0)}),2e3)):(m((e=>e+1)),t(!0),c(null))};return null===l?e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Avaliação do Potássio"}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4",children:[e.jsx("h4",{className:"font-medium text-blue-800",children:"Recomenda-se manter a hidratação de manutenção com:"}),e.jsx("p",{className:"text-blue-700",children:"SF a 0,9%, 2000 mL/m²/dia, via EV"})]}),e.jsx("p",{children:"O paciente apresenta potássio sérico (< 3,5 mEq/L)?"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r,{variant:"outline",onClick:()=>h(!0),className:"flex-1",children:"Sim"}),e.jsx(r,{variant:"outline",onClick:()=>h(!1),className:"flex-1",children:"Não"})]})]}):l&&null===n?e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4",children:[e.jsx("h4",{className:"font-medium text-blue-800",children:"Manejo para reposição rápida de potássio:"}),e.jsxs("p",{className:"text-blue-700",children:[e.jsx("li",{children:"Parâmetro a ser utilizado: 0,2-0,3 mEq/kg/hora"}),e.jsxs("li",{children:["Valor calculado para o paciente: ",(.2*s).toFixed(1),"-",(.3*s).toFixed(1)," mEq/hora EV em bomba de infusão"]})]}),e.jsx("p",{className:"text-blue-700",children:"Tempo: 3 horas"}),d>0&&e.jsx("p",{className:"text-blue-700 mt-2",children:"Manter reposição rápida de potássio e realizar nova coleta de potássio sérico"})]}),e.jsx("p",{children:d>0?"Faça a coleta novamente do potássio sérico depois de 1 hora e avalie hipocalemia, hipocalemia persiste?":"Faça a coleta de potássio sérico depois de 1 hora e avalie hipocalemia, hipocalemia persiste?"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r,{variant:"outline",onClick:()=>p(!0),className:"flex-1",children:"Não"}),e.jsx(r,{variant:"outline",onClick:()=>p(!1),className:"flex-1",children:"Sim"})]})]}):x?e.jsx(o,{className:"p-6 space-y-4",children:e.jsx("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:e.jsx("p",{className:"text-green-800 font-medium",children:"Suspenda a reposição de potássio"})})}):null},G=({weight:s,onComplete:i})=>{const[l,t]=a.useState("potassium"),[n,c]=a.useState(null),[d,m]=a.useState(null),[x,u]=a.useState(null),h=e=>{u(e),e?j():t("adjustment")},p=e=>{t(e?"glucose-low":"glucose-high")},j=()=>{i({severity:"confirmed",recommendations:["Manter insulinoterapia e hidratação até preenchimento dos critérios de estabilização:","- Normalização clínica","- pH > 7,3","- Bicarbonato > 15 mEq/L","Realizar transição para insulina subcutânea antes da alta."]})};return"potassium"===l?e.jsx(P,{weight:s,onPotassiumNormal:e=>{m(e),t("insulin-selection")}}):"insulin-selection"===l?e.jsx(I,{weight:s,onSelect:e=>{c(e),t("monitoring")}}):"monitoring"===l?e.jsx(D,{weight:s,onTargetReached:h,showPotassiumInfo:d,insulinType:n}):"adjustment"===l?e.jsxs(o,{className:"p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Ajuste Glicêmico"}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200 mb-4",children:[e.jsx("h4",{className:"font-medium text-green-800",children:"Alvos glicêmicos:"}),e.jsxs("ul",{className:"list-disc pl-4 text-green-700",children:[e.jsx("li",{children:"100-150 mg/dL para escolares/adolescentes"}),e.jsx("li",{children:"150-180 mg/dL para crianças menores"})]})]}),null===x&&e.jsxs(e.Fragment,{children:[e.jsx("p",{children:"Após 1 hora, glicemia atingiu o alvo?"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r,{variant:"outline",onClick:()=>h(!0),className:"flex-1",children:"Sim"}),e.jsx(r,{variant:"outline",onClick:()=>h(!1),className:"flex-1",children:"Não"})]})]}),!x&&e.jsxs(e.Fragment,{children:[e.jsx("p",{children:"Glicemia está abaixo do alvo?"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(r,{variant:"outline",onClick:()=>p(!0),className:"flex-1",children:"Sim"}),e.jsx(r,{variant:"outline",onClick:()=>p(!1),className:"flex-1",children:"Não"})]})]})]}):"glucose-low"===l||"glucose-high"===l?e.jsx(M,{isLow:"glucose-low"===l,onReturn:()=>{t("adjustment"),u(null)}}):null},L=({result:a,showResult:s,onReset:i,weight:l})=>s&&a?e.jsx(g,{open:s,children:e.jsxs(N,{children:[e.jsxs(b,{children:[e.jsx(v,{children:null===a.severity?"Avaliação":"Cetoacidose Diabética"}),e.jsxs(f,{className:"space-y-4",children:[a.recommendations.map(((a,s)=>e.jsx("p",{className:"whitespace-pre-line",children:a},s))),"confirmed"===a.severity&&l&&e.jsx("p",{className:"whitespace-pre-line",children:l?`SF 0,9% ${Math.round(10*l)}-${Math.round(20*l)} mL/kg EV em mais 1 hora`:""})]})]}),e.jsx(y,{children:e.jsx(r,{onClick:i,children:"Recomeçar"})})]})}):null,R=()=>{const{weight:i,displayWeight:l,setTempWeight:o,commitWeight:r}=C(),{displayAge:t,setTempAge:n,commitAge:h}=w(),[p,j]=a.useState("initial"),[g,N]=a.useState(null),b=u.dka;return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(x,{...b}),e.jsx(c,{}),e.jsxs("main",{className:"flex-1 container max-w-2xl mx-auto p-4 space-y-6",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs(s,{to:"/flowcharts",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors",children:[e.jsx(d,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Fluxogramas"})]})}),e.jsx("h1",{className:"text-2xl font-bold text-center",children:"Cetoacidose Diabética na Emergência Pediátrica"}),"initial"===p&&e.jsxs(e.Fragment,{children:[e.jsx(S,{weight:l,onWeightChange:o,onWeightCommit:r,age:t,onAgeChange:n,onAgeCommit:h}),e.jsx(A,{onStart:()=>{j("diagnosis")}})]}),"diagnosis"===p&&e.jsx(k,{onDiagnosisComplete:e=>{e?j("treatment"):(N({severity:null,recommendations:["Não há critérios para diagnóstico de CAD. Avaliar outro diagnóstico:","Estado hiperglicêmico hiperosmolar (EHH).","Ingestão de substâncias tóxicas (ex.: metanol, etilenoglicol).","Sepse grave ou choque."]}),j("result"))}}),"treatment"===p&&e.jsx(E,{weight:i,onComplete:()=>{j("maintenance")}}),"maintenance"===p&&e.jsx(G,{weight:i,onComplete:e=>{N(e),j("result")}}),e.jsx(L,{result:g,showResult:"result"===p,onReset:()=>{j("initial"),N(null)},weight:i})]}),e.jsx(m,{})]})};export{R as default};
