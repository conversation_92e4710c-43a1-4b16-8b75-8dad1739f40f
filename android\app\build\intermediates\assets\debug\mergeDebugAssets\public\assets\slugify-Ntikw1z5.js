import{c as e}from"./index-DwykrzWu.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a=e("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);function r(e){const a={"á":"a","à":"a","ã":"a","â":"a","ä":"a","é":"e","è":"e","ê":"e","ë":"e","í":"i","ì":"i","î":"i","ï":"i","ó":"o","ò":"o","õ":"o","ô":"o","ö":"o","ú":"u","ù":"u","û":"u","ü":"u","ý":"y","ÿ":"y","ñ":"n","ç":"c"};return e.toString().toLowerCase().replace(/[áàãâäéèêëíìîïóòõôöúùûüýÿñç]/g,(e=>a[e]||e)).normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[®™]/g,"").replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"").replace(/-+/g,"-")}export{a as A,r as s};
