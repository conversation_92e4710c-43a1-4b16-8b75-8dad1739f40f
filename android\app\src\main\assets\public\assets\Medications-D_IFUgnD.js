import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-lymv0EUp.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-Dq2DDcRF.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-DN7aP5VN.js";import"./FeedbackTrigger-BWleNIUB.js";import"./rocket-CQ12FwVc.js";import"./target-CjP44kbC.js";import"./zap-BS-YR30Y.js";import"./book-open-CQJyFt3x.js";import"./star-VMrI2CfW.js";import"./circle-help-m-47zVGS.js";import"./instagram-BrswTqbB.js";import"./collapsible-B6HfSnGs.js";import"./accordion-BxzF3VFK.js";import"./PatientInfoSection-DjgVVMiR.js";import"./scale-Bxj39MLp.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-DkU4Wz_i.js";import"./user-BaXXO-GK.js";import"./DosageDisplay-C5HO75B0.js";import"./alert-Dq2jwNFL.js";import"./plus-IZJFu_xe.js";import"./lightbulb-BPSu9E9q.js";import"./external-link-CJUxErbf.js";import"./stethoscope-DKiXQdWi.js";import"./syringe-BoQxY2VE.js";import"./wind-B9ngBsmE.js";import"./bug-BSdaGrKP.js";import"./chevron-left-BRVekuj0.js";import"./house-D-uld3es.js";function t(){return r.jsx(o,{})}export{t as default};
