import{j as e}from"./radix-core-6kBL75b5.js";import{r as s,b as a}from"./critical-DVX9Inzy.js";import{L as r}from"./router-BAzpOxbo.js";import{b5 as t,b4 as l,s as i,R as d,U as n,d as o,a8 as c,aa as m,B as x,a7 as h,k as g,ac as b,a5 as u,X as p,j,au as N,ay as v,ak as y,aE as f,W as k,Z as w,C as _,P as C,aj as S,D as L,aK as I,e as E,g as A,bb as B,V as F}from"./index-CFnD44mG.js";import q from"./Footer-BQ6Dqsd-.js";import{u as R,a as z}from"./query-vendor-B-7l6Nb3.js";import{S as T}from"./skeleton-BnnAIqPb.js";import{H as U}from"./house-5Ue8MCCY.js";import{F as D}from"./filter-CE2-CpU0.js";import{F as P}from"./folder-open-B-nGN_Wm.js";import{H as M}from"./FeedbackTrigger-ot4XwGkJ.js";import{S as G}from"./star-ShS_gnKj.js";import{F as H}from"./folder-DHMugfDc.js";import{C as V}from"./clock-BD5TbF1t.js";import{Z as K}from"./zap-DhWHOaH4.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-X0NDZon3.js";import"./rocket-BOvZdSbI.js";import"./target-BX0FTOgV.js";import"./book-open-CpPiu5Sl.js";import"./circle-help-B8etwa8R.js";const O=({message:s,progress:a,showSpinner:r=!0})=>e.jsxs("div",{className:"flex items-center justify-center gap-3 py-8",children:[r&&e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-pink-500"}),e.jsxs("div",{className:"text-center",children:[e.jsx("span",{className:"text-lg text-gray-600 dark:text-gray-400",children:s}),void 0!==a&&e.jsx("div",{className:"mt-2 w-48 bg-gray-200 rounded-full h-2 dark:bg-gray-700",children:e.jsx("div",{className:"bg-pink-500 h-2 rounded-full transition-all duration-300",style:{width:`${a}%`}})})]})]}),W=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx(O,{message:"Carregando medicamentos para amamentação..."}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map(((s,a)=>e.jsx(d,{className:"animate-pulse",children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start gap-3 mb-3",children:[e.jsx(T,{className:"h-10 w-10 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(T,{className:"h-5 w-3/4 mb-2"}),e.jsx(T,{className:"h-4 w-1/2"})]})]}),e.jsx(T,{className:"h-4 w-full mb-2"}),e.jsx(T,{className:"h-4 w-2/3"})]})},a)))}),e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400",children:[e.jsx("div",{className:"animate-pulse h-2 w-2 bg-pink-500 rounded-full"}),e.jsx("span",{children:"Organizando categorias e medicamentos..."})]})})]}),Z=({count:s=6})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx(O,{message:"Carregando categorias...",showSpinner:!0}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:s}).map(((s,a)=>e.jsx(d,{className:"animate-pulse border-2",children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx(T,{className:"h-8 w-8 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(T,{className:"h-5 w-3/4 mb-1"}),e.jsx(T,{className:"h-3 w-1/2"})]}),e.jsx(T,{className:"h-4 w-4"})]}),e.jsx(T,{className:"h-3 w-full mb-2"}),e.jsx(T,{className:"h-3 w-4/5"})]})},a)))})]}),$=({count:s=4})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx(O,{message:"Carregando subcategorias..."}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:s}).map(((s,a)=>e.jsx(d,{className:"animate-pulse border-2 border-blue-200 dark:border-blue-700",children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx(T,{className:"h-8 w-8 rounded-lg bg-blue-200 dark:bg-blue-800"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(T,{className:"h-4 w-2/3 mb-1"}),e.jsx(T,{className:"h-3 w-1/3"})]}),e.jsx(T,{className:"h-4 w-4"})]}),e.jsx(T,{className:"h-3 w-full"})]})},a)))})]}),J=({count:s=6})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx(O,{message:"Carregando medicamentos..."}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:s}).map(((s,a)=>e.jsx(d,{className:"animate-pulse border-2",children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx(T,{className:"h-8 w-8 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(T,{className:"h-5 w-3/4 mb-2"}),e.jsx(T,{className:"h-4 w-1/2"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx(T,{className:"h-6 w-16 mb-1"}),e.jsx(T,{className:"h-3 w-12"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[e.jsxs("div",{children:[e.jsx(T,{className:"h-3 w-16 mb-1"}),e.jsx(T,{className:"h-3 w-full"})]}),e.jsxs("div",{children:[e.jsx(T,{className:"h-3 w-12 mb-1"}),e.jsx(T,{className:"h-3 w-3/4"})]})]}),e.jsx(T,{className:"h-8 w-full rounded"})]})},a)))})]}),Q=({count:s=3})=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 py-6",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"}),e.jsx("span",{className:"text-blue-600 dark:text-blue-400",children:"Pesquisando medicamentos..."})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:s}).map(((s,a)=>e.jsx(d,{className:"animate-pulse border-blue-200 dark:border-blue-700",children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx(T,{className:"h-8 w-8 rounded-full bg-blue-200 dark:bg-blue-800"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(T,{className:"h-5 w-3/4 mb-2"}),e.jsx(T,{className:"h-4 w-1/2"})]})]}),e.jsx(T,{className:"h-6 w-16"})]}),e.jsx(T,{className:"h-4 w-full mb-2"}),e.jsx(T,{className:"h-4 w-2/3"})]})},a)))})]}),X=({type:s,count:a})=>{switch(s){case"structure":default:return e.jsx(W,{});case"sections":return e.jsx(Z,{count:a});case"subsections":return e.jsx($,{count:a});case"medications":return e.jsx(J,{count:a});case"search":return e.jsx(Q,{count:a})}},Y=()=>{const[T,O]=s.useState(""),[W,Z]=s.useState(null),[$,J]=s.useState({}),[Q,Y]=s.useState({}),[ee,se]=s.useState({currentLevel:"sections",currentSectionId:null,currentSubsectionId:null,breadcrumbs:[]});o();const{data:ae,isLoading:re,error:te,refetch:le}=R({queryKey:l.BREASTFEEDING_STRUCTURE,queryFn:async()=>{try{const{data:e,error:s}=await i.rpc("get_breastfeeding_structure_cached");if(s){const{data:e,error:s}=await i.rpc("get_breastfeeding_structure");if(s)throw s;return e}return e}catch(e){throw e}},...t.BREASTFEEDING_STRUCTURE}),[ie,de]=s.useState("");s.useEffect((()=>{const e=setTimeout((()=>{de(T)}),300);return()=>clearTimeout(e)}),[T]);const{data:ne=[],isLoading:oe,error:ce}=(e=>R({queryKey:l.BREASTFEEDING_SEARCH(e),queryFn:async()=>{if(!e||e.length<3)return[];try{const{data:s,error:a}=await i.rpc("search_breastfeeding_medications_optimized",{search_query:e,limit_results:20});if(a){const{data:s,error:a}=await i.from("pedbook_breastfeeding_medications").select("\n              id,\n              name,\n              compatibility_level,\n              usage_description,\n              additional_info,\n              efeitos_no_lactente,\n              alternativas_seguras,\n              orientacoes_uso,\n              section:pedbook_breastfeeding_sections(name),\n              subsection:pedbook_breastfeeding_subsections(name)\n            ").ilike("name",`%${e}%`).limit(20);if(a)throw a;return s||[]}return(s||[]).map((e=>e.search_breastfeeding_medications_optimized||e.medication_data||e))}catch(s){throw s}},enabled:e.length>=3,...t.BREASTFEEDING_SEARCH}))(ie);z();const me=e=>{Z(W===e?null:e)},xe=e=>!W||!!e?.compatibility_level&&e.compatibility_level.toLowerCase()===W.toLowerCase(),he=e=>!(0===(e.medications?.length||0)+(e.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0)||W&&!e.medications?.some((e=>xe(e)))&&!e.subsections?.some((e=>ge(e)))),ge=e=>!(0===(e.medications?.length||0)+(e.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0)||W&&(!e.medications||!e.medications.some((e=>xe(e))))&&(!e.subsections||!e.subsections.some((e=>ge(e))))),be=({medication:s})=>{const a=(s=>{if(!s)return{headerBg:"bg-gradient-to-r from-gray-500 to-gray-600",icon:e.jsx(C,{className:"h-6 w-6 text-white"}),badge:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"};switch(s.toLowerCase()){case"verde":return{headerBg:"bg-gradient-to-r from-green-500 to-green-600",icon:e.jsx(N,{className:"h-6 w-6 text-white"}),badge:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"};case"amarelo":return{headerBg:"bg-gradient-to-r from-yellow-500 to-yellow-600",icon:e.jsx(y,{className:"h-6 w-6 text-white"}),badge:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"};case"vermelho":return{headerBg:"bg-gradient-to-r from-red-500 to-red-600",icon:e.jsx(f,{className:"h-6 w-6 text-white"}),badge:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"};default:return{headerBg:"bg-gradient-to-r from-gray-500 to-gray-600",icon:e.jsx(C,{className:"h-6 w-6 text-white"}),badge:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"}}})(s.compatibility_level);return e.jsxs(E,{className:"max-w-2xl rounded-2xl max-h-[85dvh] overflow-hidden p-0 gap-0",hideCloseButton:!0,children:[e.jsxs("div",{className:j("relative p-6 text-white",a.headerBg),children:[e.jsx("div",{className:"absolute inset-0 bg-black/10"}),e.jsxs("div",{className:"relative flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-3 bg-white/20 backdrop-blur-sm rounded-full",children:a.icon}),e.jsxs("div",{children:[e.jsx(A,{className:"text-2xl font-bold text-white mb-2",children:s.name}),e.jsx(v,{className:j("text-sm font-medium",a.badge),children:s.compatibility_level||"Não definido"})]})]}),e.jsx(B,{asChild:!0,children:e.jsx(x,{variant:"ghost",size:"icon",className:"text-white/80 hover:text-white hover:bg-white/20 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20",children:e.jsx(p,{className:"h-5 w-5"})})})]})]}),e.jsxs("div",{className:"overflow-y-auto max-h-[calc(85dvh-140px)] p-6 pb-8 md:pb-6 space-y-6",children:[e.jsxs("div",{className:"bg-blue-50/50 dark:bg-blue-950/20 p-4 rounded-xl border border-blue-100 dark:border-blue-900/20",children:[e.jsxs("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2",children:[e.jsx(S,{className:"h-5 w-5"}),"Uso na Amamentação"]}),e.jsx("p",{className:"text-blue-800 dark:text-blue-200 leading-relaxed",children:s.usage_description})]}),s.additional_info&&e.jsxs("div",{className:"bg-gray-50/50 dark:bg-gray-950/20 p-4 rounded-xl border border-gray-100 dark:border-gray-800",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5"}),"Informações Adicionais"]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed italic",children:s.additional_info})]}),s.efeitos_no_lactente&&e.jsxs("div",{className:"bg-pink-50/50 dark:bg-pink-950/20 p-4 rounded-xl border border-pink-100 dark:border-pink-900/20",children:[e.jsxs("h3",{className:"font-semibold text-pink-900 dark:text-pink-100 mb-2 flex items-center gap-2",children:[e.jsx(M,{className:"h-5 w-5"}),"Efeitos no Lactente"]}),e.jsx("p",{className:"text-pink-800 dark:text-pink-200 leading-relaxed",children:s.efeitos_no_lactente})]}),s.alternativas_seguras&&s.alternativas_seguras.length>0&&e.jsxs("div",{className:"bg-green-50/50 dark:bg-green-950/20 p-4 rounded-xl border border-green-100 dark:border-green-900/20",children:[e.jsxs("h3",{className:"font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5"}),"Alternativas Seguras"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.alternativas_seguras.map(((s,a)=>e.jsxs("span",{className:"inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800",children:[e.jsx(F,{className:"h-3 w-3 mr-1.5"}),s]},a)))})]}),s.orientacoes_uso&&e.jsxs("div",{className:"bg-amber-50/50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-100 dark:border-amber-900/20",children:[e.jsxs("h3",{className:"font-semibold text-amber-900 dark:text-amber-100 mb-2 flex items-center gap-2",children:[e.jsx(K,{className:"h-5 w-5"}),"Orientações de Uso"]}),e.jsx("p",{className:"text-amber-800 dark:text-amber-200 leading-relaxed",children:s.orientacoes_uso})]})]})]})},ue=s=>{if(!xe(s))return null;const a=(e=>{if(!e)return{border:"border-gray-200 dark:border-gray-700",bg:"bg-gray-50/50 dark:bg-gray-950/20",icon:"text-gray-600 dark:text-gray-400",badge:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"};switch(e.toLowerCase()){case"verde":return{border:"border-green-200 dark:border-green-800",bg:"bg-green-50/50 dark:bg-green-950/20",icon:"text-green-600 dark:text-green-400",badge:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"};case"amarelo":return{border:"border-yellow-200 dark:border-yellow-800",bg:"bg-yellow-50/50 dark:bg-yellow-950/20",icon:"text-yellow-600 dark:text-yellow-400",badge:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"};case"vermelho":return{border:"border-red-200 dark:border-red-800",bg:"bg-red-50/50 dark:bg-red-950/20",icon:"text-red-600 dark:text-red-400",badge:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"};default:return{border:"border-gray-200 dark:border-gray-700",bg:"bg-gray-50/50 dark:bg-gray-950/20",icon:"text-gray-600 dark:text-gray-400",badge:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"}}})(s.compatibility_level);return e.jsxs(L,{children:[e.jsx(I,{asChild:!0,children:e.jsx(d,{className:j("group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1","border-2 rounded-xl overflow-hidden",a.border,a.bg),children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:j("p-2 rounded-full bg-white/80 dark:bg-gray-800/80",a.icon),children:["verde"===s.compatibility_level?.toLowerCase()&&e.jsx(N,{className:"h-5 w-5"}),"amarelo"===s.compatibility_level?.toLowerCase()&&e.jsx(y,{className:"h-5 w-5"}),"vermelho"===s.compatibility_level?.toLowerCase()&&e.jsx(f,{className:"h-5 w-5"}),!s.compatibility_level&&e.jsx(C,{className:"h-5 w-5"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary transition-colors",children:s.name}),e.jsx(v,{className:j("text-xs mt-1",a.badge),children:s.compatibility_level||"Não definido"})]})]}),e.jsx(_,{className:"h-5 w-5 text-gray-400 group-hover:text-primary transition-colors"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed",children:s.usage_description}),(s.efeitos_no_lactente||s.alternativas_seguras?.length||s.orientacoes_uso)&&e.jsxs("div",{className:"flex items-center gap-2 mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50",children:[s.efeitos_no_lactente&&e.jsxs("div",{className:"flex items-center gap-1 text-xs text-pink-600 dark:text-pink-400",children:[e.jsx(M,{className:"h-3 w-3"}),e.jsx("span",{children:"Efeitos"})]}),s.alternativas_seguras?.length&&e.jsxs("div",{className:"flex items-center gap-1 text-xs text-green-600 dark:text-green-400",children:[e.jsx(G,{className:"h-3 w-3"}),e.jsx("span",{children:"Alternativas"})]}),s.orientacoes_uso&&e.jsxs("div",{className:"flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400",children:[e.jsx(S,{className:"h-3 w-3"}),e.jsx("span",{children:"Orientações"})]})]})]})})}),e.jsx(be,{medication:s})]},s.id)};return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-pink-50 via-white to-pink-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[e.jsxs(c,{children:[e.jsx("title",{children:"Medicamentos e Amamentação | PedBook"}),e.jsx("meta",{name:"description",content:"Informações sobre segurança de medicamentos durante a amamentação para profissionais de saúde."})]}),e.jsx(m,{}),e.jsxs("div",{className:"relative bg-gradient-to-r from-pink-500 via-pink-600 to-rose-600 dark:from-pink-800 dark:via-pink-900 dark:to-rose-900",children:[e.jsx("div",{className:"absolute inset-0 bg-black/10 dark:bg-black/20"}),e.jsx("div",{className:"relative container mx-auto px-4 py-8 md:py-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"space-y-6 mb-8 md:mb-12",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-4",children:[e.jsx(r,{to:"/",className:"absolute left-4 md:left-0",children:e.jsx(x,{variant:"ghost",size:"icon",className:"h-10 w-10 md:h-12 md:w-12 text-white/80 hover:text-white hover:bg-white/10 backdrop-blur-sm transition-colors","aria-label":"Voltar ao menu inicial",children:e.jsx(h,{className:"h-5 w-5 md:h-6 md:w-6"})})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 md:p-3 bg-white/20 backdrop-blur-sm rounded-full",children:e.jsx(g,{className:"h-6 w-6 md:h-8 md:w-8 text-white"})}),e.jsx("h1",{className:"text-2xl md:text-4xl lg:text-5xl font-bold text-white text-center",children:"Medicamentos e Amamentação"})]})]}),e.jsx("p",{className:"text-lg md:text-xl text-pink-100 max-w-3xl mx-auto leading-relaxed text-center",children:"Informações seguras e atualizadas sobre o uso de medicamentos durante o período de amamentação"})]}),e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("div",{className:"relative",children:[e.jsx(b,{className:"absolute left-3 md:left-4 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4 md:h-5 md:w-5"}),e.jsx(u,{placeholder:"Pesquisar medicamento...",className:"pl-10 md:pl-12 pr-10 md:pr-12 h-11 md:h-14 text-base md:text-lg bg-white/95 backdrop-blur-sm border-0 shadow-lg rounded-xl focus:ring-2 focus:ring-white/50",value:T,onChange:e=>O(e.target.value)}),T&&e.jsx("button",{className:"absolute right-3 md:right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600",onClick:()=>O(""),children:e.jsx(p,{className:"h-4 w-4 md:h-5 md:w-5"})})]})})]})})]}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-center gap-1 sm:gap-2 md:gap-3 px-2",children:[e.jsxs(x,{variant:"verde"===W?"default":"outline",size:"sm",className:j("h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm md:text-base flex-shrink-0","verde"===W?"bg-green-500 hover:bg-green-600 text-white border-green-500":"border-green-200 text-green-700 hover:bg-green-50 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-950/20"),onClick:()=>me("verde"),children:[e.jsx(N,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Seguros"}),e.jsx("span",{className:"sm:hidden",children:"Seg"}),ae&&e.jsx(v,{variant:"secondary",className:"ml-0.5 sm:ml-2 bg-white/20 text-current text-xs sm:text-sm px-1 sm:px-2 py-0.5",children:ae.sections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"verde"===e.compatibility_level?.toLowerCase())).length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"verde"===e.compatibility_level?.toLowerCase())).length||0)),0)||0)),0)||0})]}),e.jsxs(x,{variant:"amarelo"===W?"default":"outline",size:"sm",className:j("h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm md:text-base flex-shrink-0","amarelo"===W?"bg-yellow-500 hover:bg-yellow-600 text-white border-yellow-500":"border-yellow-200 text-yellow-700 hover:bg-yellow-50 dark:border-yellow-800 dark:text-yellow-400 dark:hover:bg-yellow-950/20"),onClick:()=>me("amarelo"),children:[e.jsx(y,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Criterioso"}),e.jsx("span",{className:"sm:hidden",children:"Crit"}),ae&&e.jsx(v,{variant:"secondary",className:"ml-0.5 sm:ml-2 bg-white/20 text-current text-xs sm:text-sm px-1 sm:px-2 py-0.5",children:ae.sections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"amarelo"===e.compatibility_level?.toLowerCase())).length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"amarelo"===e.compatibility_level?.toLowerCase())).length||0)),0)||0)),0)||0})]}),e.jsxs(x,{variant:"vermelho"===W?"default":"outline",size:"sm",className:j("h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm md:text-base flex-shrink-0","vermelho"===W?"bg-red-500 hover:bg-red-600 text-white border-red-500":"border-red-200 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/20"),onClick:()=>me("vermelho"),children:[e.jsx(f,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Contraindicados"}),e.jsx("span",{className:"sm:hidden",children:"Contr"}),ae&&e.jsx(v,{variant:"secondary",className:"ml-0.5 sm:ml-2 bg-white/20 text-current text-xs sm:text-sm px-1 sm:px-2 py-0.5",children:ae.sections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"vermelho"===e.compatibility_level?.toLowerCase())).length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"vermelho"===e.compatibility_level?.toLowerCase())).length||0)),0)||0)),0)||0})]}),W&&e.jsxs(x,{variant:"ghost",size:"sm",className:"h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 text-xs sm:text-sm md:text-base flex-shrink-0",onClick:()=>Z(null),children:[e.jsx(p,{className:"h-3 w-3 sm:h-4 sm:w-4"}),e.jsx("span",{className:"hidden sm:inline ml-1 sm:ml-2",children:"Limpar"})]})]})}),re?e.jsx(X,{type:"structure"}):te?e.jsx("div",{className:"p-12 text-center",children:e.jsx(d,{className:"max-w-md mx-auto border-red-200 dark:border-red-800",children:e.jsxs(n,{className:"p-8",children:[e.jsx(f,{className:"h-16 w-16 mx-auto text-red-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-lg text-red-600 dark:text-red-400 mb-2",children:"Erro ao carregar dados"}),e.jsx("p",{className:"text-sm text-red-500 dark:text-red-400 mb-6",children:"Não foi possível carregar as informações sobre medicamentos e amamentação."}),e.jsxs(x,{variant:"outline",className:"border-red-200 text-red-600 dark:border-red-800 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30",onClick:()=>{le()},children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Tentar novamente"]})]})})}):ae?.sections&&0!==ae.sections.length?e.jsxs(e.Fragment,{children:[T.length>=3&&e.jsx("div",{className:"mb-8",children:e.jsxs(d,{className:"border-blue-200 dark:border-blue-800 bg-blue-50/30 dark:bg-blue-950/20",children:[e.jsx(k,{children:e.jsxs(w,{className:"flex items-center gap-3 text-blue-900 dark:text-blue-100",children:[e.jsx(b,{className:"h-5 w-5"}),"Resultados da Pesquisa"]})}),e.jsx(n,{children:oe||T!==ie?e.jsx(X,{type:"search",count:3}):ne&&0!==ne.length?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("p",{className:"text-blue-700 dark:text-blue-300 font-medium",children:[ne.length," medicamento(s) encontrado(s)"]}),e.jsxs(v,{variant:"secondary",className:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",children:['Pesquisa: "',T,'"']})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:ne.map((s=>{const a={id:s.id,name:s.name,compatibility_level:s.compatibility_level||"Não definido",usage_description:s.usage_description||"",additional_info:s.additional_info,efeitos_no_lactente:s.efeitos_no_lactente,alternativas_seguras:s.alternativas_seguras,orientacoes_uso:s.orientacoes_uso,section_name:s.section_name,subsection_name:s.subsection_name};return e.jsx("div",{children:ue(a)},a.id)}))})]}):e.jsx("div",{className:"text-center py-12",children:e.jsxs("div",{className:"max-w-sm mx-auto",children:[e.jsx(b,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-gray-600 dark:text-gray-400 mb-2",children:"Nenhum medicamento encontrado"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"Tente buscar por outro termo ou verifique a ortografia"})]})})})]})}),ee.breadcrumbs.length>0&&e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-6",children:[e.jsxs("button",{className:"hover:text-pink-600 dark:hover:text-pink-400 transition-colors flex items-center gap-2",onClick:()=>se({currentLevel:"sections",currentSectionId:null,currentSubsectionId:null,breadcrumbs:[]}),children:[e.jsx(U,{className:"h-4 w-4"}),"Início"]}),ee.breadcrumbs.map(((s,r)=>e.jsxs(a.Fragment,{children:[e.jsx(_,{className:"h-3 w-3"}),e.jsx("button",{className:"hover:text-pink-600 dark:hover:text-pink-400 transition-colors",onClick:()=>{"section"===s.type&&se({currentLevel:"subsections",currentSectionId:s.id,currentSubsectionId:null,breadcrumbs:ee.breadcrumbs.slice(0,r+1)})},children:s.name})]},s.id)))]}),"sections"===ee.currentLevel&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"Categorias de Medicamentos"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Selecione uma categoria para explorar os medicamentos"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:ae.sections.filter((e=>he(e))).sort(((e,s)=>{const a=(e.medications?.length||0)+(e.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0);return(s.medications?.length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0)-a})).map((s=>(s=>{const a=(s.medications?.length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0),r={verde:(s.medications?.filter((e=>"verde"===e.compatibility_level?.toLowerCase())).length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"verde"===e.compatibility_level?.toLowerCase())).length||0)),0)||0),amarelo:(s.medications?.filter((e=>"amarelo"===e.compatibility_level?.toLowerCase())).length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"amarelo"===e.compatibility_level?.toLowerCase())).length||0)),0)||0),vermelho:(s.medications?.filter((e=>"vermelho"===e.compatibility_level?.toLowerCase())).length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.filter((e=>"vermelho"===e.compatibility_level?.toLowerCase())).length||0)),0)||0)};return e.jsxs(d,{className:"group cursor-pointer transition-all duration-200 hover:shadow-xl hover:-translate-y-2 border-2 border-gray-200 dark:border-gray-700 hover:border-pink-300 dark:hover:border-pink-600 rounded-2xl overflow-hidden bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50",onClick:()=>(e=>{se({currentLevel:"subsections",currentSectionId:e.id,currentSubsectionId:null,breadcrumbs:[{id:e.id,name:e.name,type:"section"}]})})(s),children:[e.jsx(k,{className:"pb-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-3 bg-pink-100 dark:bg-pink-900/30 rounded-xl group-hover:bg-pink-200 dark:group-hover:bg-pink-800/40 transition-colors",children:e.jsx(H,{className:"h-6 w-6 text-pink-600 dark:text-pink-400"})}),e.jsxs("div",{children:[e.jsx(w,{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[a," medicamento(s)"]})]})]}),e.jsx(_,{className:"h-5 w-5 text-gray-400 group-hover:text-pink-500 transition-colors"})]})}),e.jsxs(n,{className:"pt-0",children:[s.description&&e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex items-center gap-3 pt-3 border-t border-gray-100 dark:border-gray-700",children:[(!W||"verde"===W)&&r.verde>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:r.verde})]}),(!W||"amarelo"===W)&&r.amarelo>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),e.jsx("span",{className:"text-xs text-yellow-600 dark:text-yellow-400 font-medium",children:r.amarelo})]}),(!W||"vermelho"===W)&&r.vermelho>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-xs text-red-600 dark:text-red-400 font-medium",children:r.vermelho})]})]})]})]},s.id)})(s)))}),W&&0===ae.sections.filter((e=>he(e))).length&&e.jsx("div",{className:"text-center py-12",children:e.jsx(d,{className:"max-w-md mx-auto border-gray-200 dark:border-gray-700",children:e.jsxs(n,{className:"p-8",children:[e.jsx(D,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-gray-600 dark:text-gray-400 mb-2",children:"Nenhuma categoria encontrada"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500 mb-4",children:"Nenhuma medicação encontrada com o filtro selecionado."}),e.jsx(x,{variant:"outline",onClick:()=>Z(null),children:"Limpar filtro"})]})})})]}),"subsections"===ee.currentLevel&&ee.currentSectionId&&(()=>{const s=ae.sections.find((e=>e.id===ee.currentSectionId));return s?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:s.name}),s.description&&e.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:s.description})]}),s.medications&&s.medications.length>0&&e.jsxs("div",{className:"mb-8",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2",children:[e.jsx(C,{className:"h-5 w-5"}),"Medicamentos Gerais"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:s.medications.filter((e=>xe(e))).map((e=>ue(e)))})]}),s.subsections&&s.subsections.length>0&&e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2",children:[e.jsx(P,{className:"h-5 w-5"}),"Subcategorias"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:s.subsections.filter((e=>ge(e))).sort(((e,s)=>{const a=(e.medications?.length||0)+(e.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0);return(s.medications?.length||0)+(s.subsections?.reduce(((e,s)=>e+(s.medications?.length||0)),0)||0)-a})).map((a=>e.jsx(d,{className:"group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 rounded-xl overflow-hidden bg-gradient-to-br from-blue-50/50 to-white dark:from-blue-950/20 dark:to-gray-800",onClick:()=>((e,s)=>{se({currentLevel:"medications",currentSectionId:e.id,currentSubsectionId:s.id,breadcrumbs:[{id:e.id,name:e.name,type:"section"},{id:s.id,name:s.name,type:"subsection"}]})})(s,a),children:e.jsxs(n,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40 transition-colors",children:e.jsx(P,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:a.name}),e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[a.medications?.filter((e=>xe(e))).length||0," medicamento(s)"]})]})]}),e.jsx(_,{className:"h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors"})]}),a.description&&e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:a.description})]})},a.id)))})]}),!s.medications?.some((e=>xe(e)))&&!s.subsections?.some((e=>ge(e)))&&e.jsx("div",{className:"text-center py-12",children:e.jsx(d,{className:"max-w-md mx-auto border-gray-200 dark:border-gray-700",children:e.jsxs(n,{className:"p-8",children:[e.jsx(D,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-gray-600 dark:text-gray-400 mb-2",children:"Nenhum medicamento encontrado"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500 mb-4",children:"Nenhum medicamento encontrado nesta categoria com o filtro atual."}),e.jsx(x,{variant:"outline",onClick:()=>Z(null),children:"Limpar filtro"})]})})})]}):null})(),"medications"===ee.currentLevel&&ee.currentSectionId&&ee.currentSubsectionId&&(()=>{const s=ae.sections.find((e=>e.id===ee.currentSectionId)),a=s?.subsections?.find((e=>e.id===ee.currentSubsectionId));return s&&a?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:a.name}),a.description&&e.jsx("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:a.description})]}),a.medications&&a.medications.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:a.medications.filter((e=>xe(e))).sort(((e,s)=>e.name.localeCompare(s.name,"pt-BR"))).map((e=>ue(e)))}):e.jsx("div",{className:"text-center py-12",children:e.jsx(d,{className:"max-w-md mx-auto border-gray-200 dark:border-gray-700",children:e.jsxs(n,{className:"p-8",children:[e.jsx(C,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-gray-600 dark:text-gray-400 mb-2",children:"Nenhum medicamento disponível"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"Esta subcategoria ainda não possui medicamentos cadastrados."})]})})}),a.medications&&a.medications.length>0&&!a.medications.some((e=>xe(e)))&&e.jsx("div",{className:"text-center py-12",children:e.jsx(d,{className:"max-w-md mx-auto border-gray-200 dark:border-gray-700",children:e.jsxs(n,{className:"p-8",children:[e.jsx(D,{className:"h-16 w-16 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-gray-600 dark:text-gray-400 mb-2",children:"Nenhum medicamento encontrado"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500 mb-4",children:"Nenhum medicamento encontrado nesta subcategoria com o filtro atual."}),e.jsx(x,{variant:"outline",onClick:()=>Z(null),children:"Limpar filtro"})]})})})]}):null})()]}):e.jsx("div",{className:"p-12 text-center",children:e.jsx(d,{className:"max-w-md mx-auto border-amber-200 dark:border-amber-800",children:e.jsxs(n,{className:"p-8",children:[e.jsx(y,{className:"h-16 w-16 mx-auto text-amber-400 mb-4"}),e.jsx("h3",{className:"font-semibold text-lg text-amber-600 dark:text-amber-400 mb-2",children:"Dados não disponíveis"}),e.jsx("p",{className:"text-sm text-amber-600 dark:text-amber-400",children:"As informações sobre medicamentos e amamentação ainda não foram carregadas no sistema."})]})})}),e.jsxs("div",{className:"mt-12 space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-amber-50 via-amber-50/80 to-amber-50 dark:from-amber-900/20 dark:via-amber-900/10 dark:to-amber-900/20 p-6 rounded-xl border border-amber-100 dark:border-amber-800/30 text-center shadow-sm",children:e.jsxs("div",{className:"flex flex-col items-center gap-3",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 p-2 rounded-full shadow-sm",children:e.jsx(y,{className:"h-5 w-5 text-amber-500"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-base font-medium text-gray-700 dark:text-gray-200",children:"⚠️ Esta ferramenta é apenas um guia e não substitui a avaliação clínica individual"}),e.jsx("p",{className:"text-sm text-amber-700 dark:text-amber-300",children:"Sempre consulte um profissional de saúde antes de tomar qualquer decisão sobre medicamentos durante a amamentação."})]})]})}),e.jsx("div",{className:"bg-gradient-to-r from-blue-50 via-blue-50/80 to-blue-50 dark:from-blue-900/20 dark:via-blue-900/10 dark:to-blue-900/20 p-6 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-sm",children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 p-2 rounded-full shadow-sm",children:e.jsx(S,{className:"h-5 w-5 text-blue-500"})}),e.jsxs("div",{className:"space-y-3 text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200",children:"📚 Fontes e Referências"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400 max-w-4xl",children:[e.jsxs("p",{className:"font-medium text-blue-700 dark:text-blue-300",children:[e.jsx("strong",{children:"Fonte Principal:"})," Ministério da Saúde do Brasil"]}),e.jsxs("div",{className:"space-y-2 text-left max-w-2xl mx-auto",children:[e.jsxs("p",{children:["• ",e.jsx("strong",{children:"ANVISA:"})," Agência Nacional de Vigilância Sanitária"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"LactMed (NIH):"})," Base de dados sobre medicamentos e lactação"]})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-500 mt-3 italic",children:"Dados compilados e adaptados para facilitar o acesso a informações baseadas em evidências científicas atualizadas."})]})]})]})})]})]})}),e.jsx(q,{})]})};export{Y as default};
