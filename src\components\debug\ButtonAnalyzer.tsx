import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Bug, Copy } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

export const ButtonAnalyzer: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Foco apenas nos botões principais da calculadora de hidratação
  const analyzeMainButtons = () => {
    const buttons = [
      'Usar Calculadora Simples',
      'Usar Calculadora Avançada'
    ];
    
    const buttonData = buttons.map(buttonText => {
      const button = Array.from(document.querySelectorAll('button')).find(
        btn => btn.textContent?.includes(buttonText)
      );
      
      if (!button) {
        return { text: buttonText, status: 'NOT_FOUND' };
      }
      
      const rect = button.getBoundingClientRect();
      const computedStyle = getComputedStyle(button);
      const parent = button.parentElement;
      const parentRect = parent?.getBoundingClientRect();
      
      return {
        text: buttonText,
        status: 'FOUND',
        position: {
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height,
          top: rect.top,
          bottom: rect.bottom
        },
        viewport: {
          isVisible: rect.top >= 0 && rect.bottom <= window.innerHeight,
          isInViewport: rect.top < window.innerHeight && rect.bottom > 0,
          escaping: rect.bottom > window.innerHeight || rect.top < 0
        },
        css: {
          position: computedStyle.position,
          zIndex: computedStyle.zIndex,
          transform: computedStyle.transform,
          overflow: computedStyle.overflow,
          display: computedStyle.display
        },
        parent: parent ? {
          tagName: parent.tagName,
          className: parent.className,
          position: parentRect ? {
            x: parentRect.x,
            y: parentRect.y,
            width: parentRect.width,
            height: parentRect.height
          } : null,
          css: {
            position: getComputedStyle(parent).position,
            overflow: getComputedStyle(parent).overflow,
            transform: getComputedStyle(parent).transform
          }
        } : null
      };
    });
    
    return {
      timestamp: new Date().toISOString(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      buttons: buttonData
    };
  };

  const collectDebugData = () => {
    const data = analyzeMainButtons();
    console.log('🔍 BUTTON ANALYSIS:', JSON.stringify(data, null, 2));
    return data;
  };

  const copyToClipboard = async () => {
    try {
      const data = collectDebugData();
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2));
      toast({
        title: "Debug copiado!",
        description: "Dados dos botões copiados para a área de transferência",
      });
    } catch (error) {
      console.error('Erro ao copiar:', error);
      toast({
        title: "Erro",
        description: "Não foi possível copiar os dados",
        variant: "destructive",
      });
    }
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-red-500 hover:bg-red-600 text-white"
        size="sm"
      >
        <Bug className="w-4 h-4 mr-1" />
        DEBUG BOTÕES
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">🔍 Análise dos Botões Principais</h3>
          <Button
            onClick={() => setIsVisible(false)}
            variant="ghost"
            size="sm"
          >
            ✕
          </Button>
        </div>
        
        <div className="space-y-4">
          <Button
            onClick={copyToClipboard}
            className="w-full"
          >
            <Copy className="w-4 h-4 mr-2" />
            Copiar Análise dos Botões
          </Button>
          
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <p>• Foco apenas nos botões "Usar Calculadora Simples/Avançada"</p>
            <p>• Verifica se estão "escapando" da viewport</p>
            <p>• Analisa posicionamento e CSS dos elementos pais</p>
          </div>
        </div>
      </div>
    </div>
  );
};
