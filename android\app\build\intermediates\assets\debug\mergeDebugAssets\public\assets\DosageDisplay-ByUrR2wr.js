import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{s as a,ak as r,Y as i,B as s}from"./index-DV3Span9.js";import{A as n,b as o}from"./alert-CRxxoxJe.js";import{P as m}from"./plus-CG1D5Wcu.js";import{u as l}from"./query-vendor-B-7l6Nb3.js";function d(e){if("string"==typeof e&&/UI|IU/.test(e)){const t=e.replace(/[^\d.,]/g,""),a=parseFloat(t.replace(/\./g,"").replace(",",".")),r=new Intl.NumberFormat("pt-BR",{minimumFractionDigits:0,maximumFractionDigits:0}).format(a);return e.replace(t,r)}const t="string"==typeof e?parseFloat(e.replace(/\./g,"").replace(",",".")):e;return Number.isInteger(t)&&Math.abs(t)>=1e3?new Intl.NumberFormat("pt-BR",{minimumFractionDigits:0,maximumFractionDigits:0}).format(t):new Intl.NumberFormat("pt-BR",{minimumFractionDigits:0,maximumFractionDigits:2}).format(t)}const c=async(e,t,r,i,s)=>{if(!e)return{text:""};let n=e,o=!0;try{let e,m,l=s;if(!l){const{data:e,error:t}=await a.from("pedbook_medication_tags").select("name, multiplier, max_value, type, start_month, end_month, start_weight, end_weight, round_result").eq("medication_id",i).eq("is_user_medication",!1);if(t)throw t;l=e||[]}if(l&&l.length>0){const a=l.filter((e=>("age"===e.type||"multiplier_by_fixed_age"===e.type)&&null!==e.start_month&&e.start_month>0)),i=l.filter((e=>"fixed_by_weight"===e.type&&null!==e.start_weight&&e.start_weight>0));a.length>0&&(e=Math.min(...a.map((e=>e.start_month||1/0))),r<e&&(o=!0)),i.length>0&&(m=Math.min(...i.map((e=>e.start_weight||1/0))),t<m&&(o=!0));const s=new Map;for(const e of l){let a;if("fixed_by_weight"===e.type){const r=l.find((a=>a.name===e.name&&"fixed_by_weight"===a.type&&t>=(a.start_weight||0)&&t<=(a.end_weight||1/0)));r?(a=r.multiplier||0,a>0&&(o=!1),r.round_result&&(a=Math.round(a))):a=0}else if("age"===e.type){const t=l.find((t=>t.name===e.name&&"age"===t.type&&r>=(t.start_month||0)&&r<=(t.end_month||1/0)));t?(a=t.multiplier||0,a>0&&(o=!1),t.round_result&&(a=Math.round(a))):a=0}else if("multiplier_by_fixed_age"===e.type){const i=l.find((t=>t.name===e.name&&"multiplier_by_fixed_age"===t.type&&r>=(t.start_month||0)&&r<=(t.end_month||1/0)));i?(a=t*(i.multiplier||0),a>0&&(o=!1),i.max_value&&a>i.max_value&&(a=i.max_value),i.round_result&&(a=Math.round(a))):a=0}else if("fixed"===e.type)a=e.multiplier||0,a>0&&(o=!1),e.round_result&&(a=Math.round(a));else{const r=e.multiplier||0;a=t*r,a>0&&(o=!1),e.max_value&&a>e.max_value&&(a=e.max_value),e.round_result&&(a=Math.round(a))}if(void 0!==a){const t=s.get(e.name);(void 0===t||0!==a&&0===t||0===t&&0===a)&&s.set(e.name,a)}}for(const[e,t]of s.entries()){const a=new RegExp(`\\(\\(${e}\\)\\)`,"g");let r;r=0===t?"0":t>0&&t<.001?t.toFixed(4).replace(".",","):t>0&&t<.01?t.toFixed(3).replace(".",","):d(t),n=n.replace(a,r)}const c=/\(\(([^)]+)\)\)/g;n.match(c),n=n.replace(c,"0")}const c={text:n.replace("{weight}",d(t)).replace("{age}",r.toString())};return o&&(e||m)&&(e&&m?c.restrictions={type:"both",minAge:e,minWeight:m}:e?c.restrictions={type:"age",minAge:e}:m&&(c.restrictions={type:"weight",minWeight:m})),c}catch(m){return{text:e}}},u=({name:a,calculatedDosage:l,summary:d,requiredMeasures:c=["weight","age"],restrictionMessage:u,isCalculating:g=!1})=>{const[p,h]=t.useState(!1);return e.jsx("div",{className:"card-container p-2 sm:p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-lg sm:text-xl font-medium gradient-text",children:a}),u?e.jsxs(n,{className:"bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800/50",children:[e.jsx(r,{className:"h-4 w-4 text-amber-500 dark:text-amber-400"}),e.jsx(o,{className:"text-amber-700 dark:text-amber-300",children:u})]}):"Preencha os valores necessários"===l?e.jsxs(n,{variant:"destructive",className:"bg-destructive/5 dark:bg-destructive/10 text-destructive dark:text-red-400 border-none",children:[e.jsx(i,{className:"h-4 w-4"}),e.jsxs(o,{children:["Preencha o ",(()=>{const e=[];return c.includes("weight")&&e.push("peso"),c.includes("age")&&e.push("idade"),e.join(" e ")})()," do paciente para calcular a dosagem"]})]}):e.jsx("div",{className:"bg-gradient-to-br from-primary-light dark:from-blue-900/20 to-primary/5 dark:to-blue-800/10 rounded-lg p-2 sm:p-4 border border-blue-100 dark:border-blue-900/30 selectable-text",children:g||/\(\([^)]+\)\)/.test(l)?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-primary/30 border-t-primary"}),e.jsx("p",{className:"text-base sm:text-lg text-gray-600 dark:text-gray-300 font-medium",children:"Calculando dosagem..."})]}):e.jsx("p",{className:"text-base sm:text-lg text-gray-700 dark:text-gray-200 font-medium",children:(x=l,"Preencha os valores necessários"===x?x:/\(\([^)]+\)\)/.test(x)||g?"Calculando dosagem...":x.replace(/(\d+(?:\.\d{3})*(?:[,.]\d+)?)/g,(e=>{const t=e.replace(/\./g,"").replace(",","."),a=parseFloat(t);if(!isNaN(a)){if(Math.abs(a)>=1e3){const e=Math.round(a);return new Intl.NumberFormat("pt-BR",{minimumFractionDigits:0,maximumFractionDigits:0,useGrouping:!0}).format(e)}const t=e.includes(",")?e.split(",")[1].length:0;return new Intl.NumberFormat("pt-BR",{minimumFractionDigits:t,maximumFractionDigits:t,useGrouping:!0}).format(a)}return e})))})}),d&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs(s,{variant:"ghost",className:"w-full flex items-center justify-center gap-2 text-primary dark:text-blue-400 hover:text-primary/80 dark:hover:text-blue-300 hover:bg-primary/5 dark:hover:bg-blue-900/20",onClick:()=>h(!p),"aria-label":p?"Ocultar detalhes da dosagem":"Ver mais detalhes da dosagem",children:[e.jsx(m,{className:"h-4 w-4 transition-transform duration-200 "+(p?"rotate-45":"")}),p?"Mostrar menos":"Ver mais detalhes"]}),p&&e.jsx("div",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-300 bg-gray-50/80 dark:bg-slate-700/50 p-2 sm:p-4 rounded-lg border border-primary/5 dark:border-primary/10 animate-slide-in-up whitespace-pre-line selectable-text",children:d})]})]})});var x},g=()=>l({queryKey:["medication-categories","with-medications","v2"],queryFn:async()=>{const{data:e,error:t}=await a.from("pedbook_medication_categories").select("\n          *,\n          pedbook_medications!category_id (\n            id,\n            name,\n            slug,\n            brands\n          )\n        ").order("name");if(t)throw t;return e},staleTime:9e5,gcTime:36e5,refetchOnMount:!1,refetchOnWindowFocus:!1,retry:1}),p=({dosage:r,weight:i,age:s,requiredMeasures:n=["weight","age"]})=>{const[o,m]=t.useState(""),[d,g]=t.useState((()=>/\(\([^)]+\)\)/.test(r.dosage_template))),[p,h]=t.useState(null),{data:x=[],isLoading:_,error:f}=(y=r.medication_id,l({queryKey:["medication-tags",y],queryFn:async()=>{if(!y)return[];const{data:e,error:t}=await a.from("pedbook_medication_tags").select("name, multiplier, max_value, type, start_month, end_month, start_weight, end_weight, round_result, is_user_medication").eq("medication_id",y).eq("is_user_medication",!1);if(t)throw t;return e||[]},enabled:!!y,staleTime:6e5,gcTime:18e5,refetchOnMount:!1,refetchOnWindowFocus:!1}));var y;r.medication_id,t.useEffect((()=>{(async()=>{if(!r.dosage_template)return m(""),void h(null);const e=!n.includes("weight")||n.includes("weight")&&i>0,t=!n.includes("age")||n.includes("age")&&s>=0;if(!e||!t){const a=[];return n.includes("weight")&&!e&&a.push("peso"),n.includes("age")&&!t&&a.push("idade"),m(`Preencha o ${a.join(" e ")} do paciente`),void h(null)}const a=(()=>{if(!x||0===x.length)return null;const e=x.filter((e=>r.dosage_template.includes(`((${e.name}))`)));let t,a;if(e&&e.length>0){const r=e.filter((e=>("age"===e.type||"multiplier_by_fixed_age"===e.type)&&null!==e.start_month&&e.start_month>0)),i=e.filter((e=>"fixed_by_weight"===e.type&&null!==e.start_weight&&e.start_weight>0));r.length>0&&(t=Math.min(...r.map((e=>e.start_month||1/0)))),i.length>0&&(a=Math.min(...i.map((e=>e.start_weight||1/0))))}return t&&a?{type:"both",minAge:t,minWeight:a}:t?{type:"age",minAge:t}:a?{type:"weight",minWeight:a}:null})();let o=!1;if(a&&("age"===a.type&&a.minAge?s<a.minAge&&(o=!0,h(a)):"weight"===a.type&&a.minWeight?i<a.minWeight&&(o=!0,h(a)):"both"===a.type&&a.minAge&&a.minWeight&&(s<a.minAge||i<a.minWeight)&&(o=!0,h(a))),o)return m(""),void g(!1);try{g(!0);const e=await c(r.dosage_template,i,s,r.medication_id,x);g(!1);const t=e.text.match(/(\d*[.,]\d+)/g)?.every((e=>0===parseFloat(e.replace(",","."))));t&&e.restrictions?h(e.restrictions):o||h(e.restrictions||null);const a=e.text.replace(/(\d*[.,]\d+)/g,(e=>{const t=parseFloat(e.replace(",","."));return t>0&&t<.001?t.toFixed(4).replace(".",","):t>0&&t<.01?t.toFixed(3).replace(".",","):e}));m(a)}catch(l){m(""),g(!1)}})()}),[i,s,r,n,x]);const b=e=>{if(e<12)return`${e} ${1===e?"mês":"meses"}`;{const t=Math.floor(e/12),a=e%12;return 0===a?`${t} ${1===t?"ano":"anos"}`:`${t} ${1===t?"ano":"anos"} e ${a} ${1===a?"mês":"meses"}`}};return e.jsx(u,{name:r.name,calculatedDosage:o,summary:r.summary,requiredMeasures:n,restrictionMessage:p?"age"===p.type&&p.minAge?`Esta apresentação é indicada para pacientes a partir de ${b(p.minAge)} de idade.`:"weight"===p.type&&p.minWeight?`Esta apresentação é indicada para pacientes com peso mínimo de ${p.minWeight}kg.`:"both"===p.type&&p.minAge&&p.minWeight?`Esta apresentação é indicada para pacientes a partir de ${b(p.minAge)} de idade e com peso mínimo de ${p.minWeight}kg.`:null:null,isCalculating:d})};export{p as D,c,d as f,g as u};
