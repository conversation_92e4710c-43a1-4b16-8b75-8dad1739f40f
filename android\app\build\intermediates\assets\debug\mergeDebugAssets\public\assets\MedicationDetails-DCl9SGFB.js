import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as r,u as s,c as t,L as i}from"./router-BAzpOxbo.js";import{u as d,a as l}from"./query-vendor-B-7l6Nb3.js";import{c as o,B as n,aH as c,ac as m,a5 as g,aw as p,aJ as u,P as b,Y as x,au as h,a8 as y,D as f,aK as j,ab as k,e as v,f as w,g as N,L as _,s as C,aq as $,ad as M,ae as S,af as W,ag as A,ai as L,at as T,aL as E,ak as I,aj as q,j as z,a9 as B,ay as P,m as O,aM as F,aa as D}from"./index-DwykrzWu.js";import{u as H}from"./useWeight-CatlFLFx.js";import{u as V}from"./useAge-C_36_Zbj.js";import K from"./Footer-CEErUVD6.js";import{C as R,a as G,b as J}from"./collapsible-B6HfSnGs.js";import{A as Z,a as Q,b as U,c as Y}from"./accordion-B6pduOb3.js";import{P as X}from"./PatientInfoSection-B0XzckOx.js";import{D as ee,u as ae}from"./DosageDisplay-ChdMXWCG.js";import{L as re}from"./lightbulb-CeJxLvRY.js";import{B as se}from"./book-open-vsXyzIQN.js";import{E as te}from"./external-link-YG2Q46QJ.js";import{S as ie}from"./stethoscope-BiILX4Ik.js";import{S as de}from"./syringe-D5LgfHhK.js";import{W as le}from"./wind-BbVQggcd.js";import{B as oe}from"./bug-rUAcu1O2.js";import{H as ne}from"./FeedbackTrigger-Cce_pfRl.js";import{C as ce}from"./chevron-left-UiG5RJWe.js";import{H as me}from"./house-DCEvqY-i.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-BgC8q_0n.js";import"./scale-ChsfrFo_.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-hvRXSQ6q.js";import"./user-JQIHs777.js";import"./alert-47AuHV0Y.js";import"./plus-ahDLNnho.js";import"./rocket-BVKdk9NC.js";import"./target-Bq7BybE4.js";import"./zap-sktuObTW.js";import"./star-CVUfjIpQ.js";import"./circle-help-DkV0sebI.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=o("Apple",[["path",{d:"M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z",key:"3s7exb"}],["path",{d:"M10 2c1 .5 2 2 2 5",key:"fcco2y"}]]),pe=o("Bandage",[["path",{d:"M10 10.01h.01",key:"1e9xi7"}],["path",{d:"M10 14.01h.01",key:"ac23bv"}],["path",{d:"M14 10.01h.01",key:"2wfrvf"}],["path",{d:"M14 14.01h.01",key:"8tw8yn"}],["path",{d:"M18 6v11.5",key:"dkbidh"}],["path",{d:"M6 6v12",key:"vkc79e"}],["rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",key:"1wpnh2"}]]),ue=o("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]),be=o("Ear",[["path",{d:"M6 8.5a6.5 6.5 0 1 1 13 0c0 6-6 6-6 10a3.5 3.5 0 1 1-7 0",key:"1dfaln"}],["path",{d:"M15 8.5a2.5 2.5 0 0 0-5 0v1a2 2 0 1 1 0 4",key:"1qnva7"}]]),xe=o("Grid2x2",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M12 3v18",key:"108xh3"}]]),he=o("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),ye=o("Tablets",[["circle",{cx:"7",cy:"7",r:"5",key:"x29byf"}],["circle",{cx:"17",cy:"17",r:"5",key:"1op1d2"}],["path",{d:"M12 17h10",key:"ls21zv"}],["path",{d:"m3.46 10.54 7.08-7.08",key:"1rehiu"}]]),fe=o("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]]),je=({categories:r,currentMedicationId:s,onMedicationSelect:t})=>{const[i,d]=a.useState(!1),[l,o]=a.useState(""),u=r?.flatMap((e=>e.pedbook_medications)).find((e=>e?.id===s)),b=e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase(),x=r?.map((e=>({...e,pedbook_medications:e.pedbook_medications?.filter((e=>((e,a)=>{if(!a)return!0;const r=b(a);if(b(e.name).includes(r))return!0;if(e.brands&&b(e.brands).includes(r))return!0;if(e.slug){const r=a.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,"");if(e.slug.includes(r))return!0}return!1})(e,l)))}))).filter((e=>e.pedbook_medications?.length>0)).sort(((e,a)=>b(e.name).localeCompare(b(a.name))));return e.jsx("div",{className:"w-full bg-white rounded-xl shadow-sm border border-gray-100",children:e.jsxs(R,{open:i,onOpenChange:d,className:"w-full",children:[e.jsx(G,{asChild:!0,children:e.jsxs(n,{variant:"ghost",className:"w-full flex items-center justify-between p-4 hover:bg-gray-50",children:[e.jsx("span",{className:"font-medium",children:u?.name||"Selecionar Medicamento"}),e.jsx(c,{className:"h-4 w-4 transition-transform "+(i?"transform rotate-180":"")})]})}),e.jsxs(J,{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(m,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),e.jsx(g,{placeholder:"Buscar por nome ou marca comercial...",className:"pl-9",value:l,onChange:e=>o(e.target.value)})]}),e.jsx(p,{className:"h-[300px]",children:e.jsx("div",{className:"space-y-4",children:l&&0===x?.length?e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(m,{className:"h-8 w-8 mx-auto mb-3 opacity-50"}),e.jsx("p",{className:"text-sm",children:"Nenhum medicamento encontrado"}),e.jsx("p",{className:"text-xs mt-1",children:"Tente buscar por nome ou marca comercial"})]}):x?.map((a=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-medium text-sm text-primary/80 uppercase tracking-wider",children:a.name}),e.jsx("div",{className:"space-y-1",children:a.pedbook_medications?.map((a=>e.jsx(n,{variant:"ghost",className:"w-full justify-start text-sm h-auto py-2 px-3 "+(a.id===s?"bg-primary text-primary-foreground hover:bg-primary/90":"hover:bg-primary/5"),onClick:()=>{t(a.slug,a),d(!1)},children:e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:a.name}),l&&a.brands&&b(a.brands).includes(b(l))&&e.jsxs("div",{className:"text-xs text-muted-foreground mt-1",children:["Marcas: ",a.brands]})]})},a.id)))})]},a.id)))})})]})]})})},ke={"a41889e8-b942-4ae6-b328-38f27a5b68b2":{icon:fe,gradient:"from-red-100 to-red-50",color:"bg-red-100"},"695e11e3-f436-4be3-9fb1-8fff3f7c1957":{icon:b,gradient:"from-orange-100 to-orange-50",color:"bg-orange-100"},"bae61da7-0bb8-4d3b-a281-f5e063135ea8":{icon:ye,gradient:"from-amber-100 to-amber-50",color:"bg-amber-100"},"35e151d7-e65e-4724-9ede-30367feb6c83":{icon:b,gradient:"from-emerald-100 to-emerald-50",color:"bg-emerald-100"},"3bbc2536-3cb1-4e7c-84a8-8c23bd8bb15d":{icon:oe,gradient:"from-purple-100 to-purple-50",color:"bg-purple-100"},"8decca82-8540-4c0b-a49a-b979ce7831cd":{icon:ne,gradient:"from-rose-100 to-rose-50",color:"bg-rose-100"},"0d2ff8a9-80ba-4be1-866d-2f0f74073e65":{icon:h,gradient:"from-blue-100 to-blue-50",color:"bg-blue-100"},"eca9210a-3be0-4590-afbd-991606bc118b":{icon:oe,gradient:"from-teal-100 to-teal-50",color:"bg-teal-100"},"fbd60aff-b67e-4a94-9a39-1a05018c41df":{icon:ue,gradient:"from-pink-100 to-pink-50",color:"bg-pink-100"},"21e87b46-255e-403d-be01-bd052c9cf35a":{icon:le,gradient:"from-cyan-100 to-cyan-50",color:"bg-cyan-100"},"8b6028b4-3b03-46bc-93a9-aa63984020b1":{icon:de,gradient:"from-red-100 to-red-50",color:"bg-red-100"},"c8462677-99e1-44a0-87a8-64e75f855f06":{icon:x,gradient:"from-violet-100 to-violet-50",color:"bg-violet-100"},"db6e60ae-1716-4db1-9d4b-c03fc03c0f62":{icon:b,gradient:"from-yellow-100 to-yellow-50",color:"bg-yellow-100"},"7c28c48d-09a9-40b9-b644-9176de941992":{icon:be,gradient:"from-indigo-100 to-indigo-50",color:"bg-indigo-100"},"3626fb0e-605d-40e8-9dfa-6e9d9b520781":{icon:pe,gradient:"from-sky-100 to-sky-50",color:"bg-sky-100"},"8c9dc9ea-62e3-40a9-ac57-1cc16fb35dc1":{icon:u,gradient:"from-fuchsia-100 to-fuchsia-50",color:"bg-fuchsia-100"},"e9e704a4-4a90-4fcb-ae6f-578e2782bf05":{icon:ge,gradient:"from-lime-100 to-lime-50",color:"bg-lime-100"}},ve=e=>e&&e.id&&ke[e.id]?ke[e.id]:{icon:ie,gradient:"from-gray-100 to-gray-50",color:"bg-gray-100"},we=({name:a,description:r,slug:s,brands:t,category:i})=>{const d=`Informações detalhadas sobre ${a.toLowerCase()} em pediatria, incluindo dose, posologia e indicações.`,l=(()=>{const e=new Set([`${a} pediátrico`,`dose de ${a}`,`cálculo de ${a}`,`prescrição de ${a}`,"medicamento pediátrico",`pediatria ${a}`,`dosagem ${a}`,`posologia ${a}`]);return i&&(e.add(`${i} pediátrico`),e.add(`medicamentos ${i.toLowerCase()}`)),Array.from(e).join(", ")})(),o=`https://pedb.com.br/medicamentos/${s}`,n=`PedBook | ${a} - Informações Pediátricas`,c="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/1496949.webp";return e.jsxs(y,{children:[e.jsx("title",{children:n}),e.jsx("meta",{name:"description",content:d}),e.jsx("meta",{name:"keywords",content:l}),e.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large"}),e.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),e.jsx("meta",{charSet:"UTF-8"}),e.jsx("link",{rel:"canonical",href:o}),e.jsx("meta",{property:"og:title",content:n}),e.jsx("meta",{property:"og:description",content:d}),e.jsx("meta",{property:"og:type",content:"article"}),e.jsx("meta",{property:"og:url",content:o}),e.jsx("meta",{property:"og:site_name",content:"PedBook"}),e.jsx("meta",{property:"og:locale",content:"pt_BR"}),e.jsx("meta",{property:"og:image",content:c}),e.jsx("meta",{property:"og:image:alt",content:"Ícone de medicamento do PedBook"}),e.jsx("meta",{property:"og:image:width",content:"1200"}),e.jsx("meta",{property:"og:image:height",content:"630"}),e.jsx("meta",{property:"article:publisher",content:"https://pedb.com.br"}),e.jsx("meta",{property:"article:section",content:"Medicamentos"}),e.jsx("meta",{property:"article:tag",content:i||"Medicamentos"}),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:title",content:n}),e.jsx("meta",{name:"twitter:description",content:d}),e.jsx("meta",{name:"twitter:image",content:c}),e.jsx("meta",{name:"twitter:site",content:"@PedBook"}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"MedicalWebPage",name:n,description:d,url:o,keywords:l.split(", "),inLanguage:"pt-BR",mainEntity:{"@type":"MedicalEntity",name:a,description:d},specialty:"Pediatria",audience:{"@type":"MedicalAudience",audienceType:"Profissionais de Saúde"},publisher:{"@type":"Organization",name:"PedBook",url:"https://pedb.com.br",logo:{"@type":"ImageObject",url:"https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/faviconx.png"}},image:{"@type":"ImageObject",url:c,width:1200,height:630},dateModified:(new Date).toISOString()})})]})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function Ne({medicationId:s,medicationName:t,slug:i}){const l=r(),{data:o,isLoading:c}=d({queryKey:["medication-instructions",s],queryFn:async()=>{if(!s)return null;const{data:e,error:a}=await C.from("pedbook_medication_instructions").select("*").eq("medication_id",s).eq("is_published",!0).maybeSingle();return a?(a.code,null):e},enabled:!!s}),m="simple"===o?.format_type?"simple":"standard";a.useEffect((()=>(window.handleImageClick=e=>{window.open(e,"_blank")},()=>{delete window.handleImageClick})),[]);const g=e=>{const a=document.createElement("textarea");return a.innerHTML=e,a.value},p=e=>e?"simple"===m?(e=>{if(!e)return"";let a=e.replace(/(<[^>]*>)*##\.\s*(.*?)(<\/[^>]*>)*/g,((e,a,r)=>`<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${r.replace(/<\/?[^>]+(>|$)/g,"")}</h2>`));return a=a.replace(/##\.\s*[^<\n\r]*(?:<br>|<\/p>|$)/g,""),a=a.replace(/(<[^>]*>)*##;\s*(.*?)(<\/[^>]*>)*/g,((e,a,r)=>`<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">${r.replace(/<\/?[^>]+(>|$)/g,"")}</h4>`)),a=a.replace(/##;\s*[^<\n\r]*(?:<br>|<\/p>|$)/g,""),a=a.replace(/<ul>/g,'<ul class="list-disc pl-5 mb-2 space-y-1">'),a=a.replace(/<li>/g,'<li class="mb-1">'),a=a.replace(/<\/li><br><li/g,"</li><li"),a=a.replace(/<li class="mb-1"><br>/g,'<li class="mb-1">'),a=a.replace(/<br><\/li>/g,"</li>"),a=a.replace(/<br><br>/g,'</p><p class="mb-4">'),a=a.replace(/<br><br><br>/g,'</p><p class="mb-6">'),a=a.replace(/<\/ul><br>/g,"</ul>"),a=a.replace(/<\/p><br>/g,"</p>"),a=a.replace(/<\/li><\/ul><li/g,"</li></ul><li"),a=a.replace(/<li class="mb-1">(.*?)<br><br>(.*?)<\/li>/g,'<li class="mb-1">$1</li></ul><p class="my-4">$2</p><ul class="list-disc pl-5 mb-2 space-y-1">'),a=a.replace(/<li class="mb-1">(.*?)<br>(?!<br>)(.*?)<\/li>/g,'<li class="mb-1">$1<div class="mt-3">$2</div></li>'),a=a.replace(/<\/ul><p class="my-4">(.*?)<\/p><ul class="list-disc pl-5 mb-2 space-y-1"><\/li>/g,'</ul><p class="my-4">$1</p><ul class="list-disc pl-5 mb-2 space-y-1">'),a=a.replace(/<br>$/g,""),a=g(a.replace(/<strong>(.*?)<\/strong>/g,'<span class="font-semibold">$1</span>').replace(/<p>/g,'<p class="mb-2">').replace(/<li><p>/g,'<li class="mb-1">').replace(/<a([^>]*)target="_blank"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g,'<a$1href="$2"$3 target="_self">').replace(/<img([^>]*)>/g,'<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')),a})(e):(e=>e?g(e.replace(/<h2><strong>##\.\s*(.*?)<\/strong><\/h2>/g,'<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">$1</h2>').replace(/<h3><strong>##\.\s*(.*?)<\/strong><\/h3>/g,'<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-3 mb-1">$1</h3>').replace(/<strong>##;\s*(.*?)<\/strong>/g,'<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>').replace(/<h4><strong>(.*?)<\/strong><\/h4>/g,'<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>').replace(/<strong>(.*?)<\/strong>/g,'<span class="font-semibold">$1</span>').replace(/<p>/g,'<p class="mb-2">').replace(/<ul>/g,'<ul class="list-disc pl-5 mb-2 space-y-1">').replace(/<li><p>/g,'<li class="mb-1">').replace(/<a([^>]*)target="_blank"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g,'<a$1href="$2"$3 target="_self">').replace(/<img([^>]*)>/g,'<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')):"")(e):"";return o||c?e.jsxs(f,{children:[e.jsx(j,{asChild:!0,children:e.jsxs(n,{variant:"outline",size:"sm",className:"gap-1 sm:gap-2 border-primary/20 bg-white dark:bg-slate-700 shadow-sm hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-white text-xs sm:text-sm px-2 sm:px-3 flex-shrink-0",onClick:e=>{e.preventDefault(),e.stopPropagation(),i&&l(`/bulas-profissionais/${i}`)},children:[e.jsx(k,{className:"h-3 w-3 sm:h-4 sm:w-4 text-primary"}),e.jsx("span",{className:"whitespace-nowrap",children:"Bula Completa"})]})}),e.jsxs(v,{className:"max-w-3xl max-h-[85vh] overflow-y-auto p-4 md:p-6 dark:bg-slate-800",children:[e.jsx(w,{className:"mb-2",children:e.jsxs(N,{className:"text-xl font-bold text-primary dark:text-white",children:["Bula: ",t]})}),c?e.jsx("div",{className:"flex items-center justify-center py-6",children:e.jsx(_,{className:"h-6 w-6 animate-spin text-primary"})}):o?.content?e.jsx("div",{className:"space-y-2",children:e.jsx(Z,{type:"multiple",className:"space-y-2",children:(e=>{if(!e)return[];const a=[];if("simple"!==m){const r=/(?:<h[2-4]><strong>##\.\s*(.*?)<\/strong><\/h[2-4]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>)([\s\S]*?)(?=<h[2-4]><strong>##\.|<h2>##\.|$)/g,s=Array.from(e.matchAll(r));return 0===s.length?[{title:"Informações Gerais",content:e,subsections:[]}]:(s.forEach(((e,r)=>{const s=(e[1]||e[2]||e[3]||"").trim();let t=e[4]||"";const i=[],d=Array.from(t.matchAll(/<strong>##;\s*(.*?)<\/strong>([\s\S]*?)(?=<strong>##;|<h[2-4]><strong>##\.|$)/g));if(d.length>0){const e=t.indexOf(d[0][0]);-1!==e&&(t=t.substring(0,e).trim()),d.forEach((e=>{const a=e[1].trim(),r=e[2].trim();i.push({title:a,content:r})}))}a.push({title:s,content:t,subsections:i})})),a)}const r=e.split(/(?:<br>|<\/p>|<p>)/);let s="",t="",i="",d="",l=[],o=0;for(let n=0;n<r.length;n++){const e=r[n].trim();if(!e){o++,o<=2&&(t?d+="<br>":s&&(i+="<br>"));continue}o=0;let c=e.match(/(?:<[^>]*>)*##\.\s*(.*?)(?:<\/[^>]*>)*/);if(c&&c[1]||(c=e.match(/##\.\s*(.*?)$/)),c&&c[1]){s&&(t&&(l.push({title:t,content:d.trim()}),t="",d=""),a.push({title:s,content:i.trim(),subsections:l}),l=[]),s=(c[1]||"").replace(/<\/?[^>]+(>|$)/g,"").trim(),i="";continue}let m=e.match(/(?:<[^>]*>)*##;\s*(.*?)(?:<\/[^>]*>)*/);m&&m[1]||(m=e.match(/##;\s*(.*?)$/)),m&&m[1]&&s?(t&&(l.push({title:t,content:d.trim()}),d=""),t=(m[1]||"").replace(/<\/?[^>]+(>|$)/g,"").trim(),d=""):t?e.match(/<\/[^>]+>$/)?d+=e:d+=e+"<br>":s&&(e.match(/<\/[^>]+>$/)?i+=e:i+=e+"<br>")}return s&&(t&&l.push({title:t,content:d.trim()}),a.push({title:s,content:i.trim(),subsections:l})),0===a.length?[{title:"Informações Gerais",content:e,subsections:[]}]:a})(o.content).map(((a,r)=>e.jsxs(Q,{value:`section-${r}`,className:"border border-blue-100 dark:border-blue-900 rounded-lg overflow-hidden",children:[e.jsx(U,{className:"px-3 py-2 hover:bg-blue-50/50 dark:hover:bg-blue-900/30 transition-colors",children:e.jsxs("div",{className:"flex items-center gap-2 text-left",children:[e.jsx("span",{className:"flex-shrink-0 w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 text-xs font-medium",children:r+1}),e.jsx("h2",{className:"text-sm font-medium text-blue-800 dark:text-blue-300",children:a.title})]})}),e.jsxs(Y,{className:"px-3 pt-1 pb-3 bg-blue-50/30 dark:bg-blue-900/20",children:[a.content?e.jsx("div",{className:"prose prose-sm max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-400 mb-4",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:p(a.content)}})}):null,a.subsections.length>0&&e.jsx(Z,{type:"multiple",className:"space-y-2 mt-2",children:a.subsections.map(((a,s)=>e.jsxs(Q,{value:`subsection-${r}-${s}`,className:"border border-blue-100 dark:border-slate-700 rounded-lg overflow-hidden",children:[e.jsx(U,{className:"px-4 py-2 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors",children:e.jsxs("div",{className:"flex items-center gap-2 text-left",children:[e.jsxs("span",{className:"flex-shrink-0 w-5 h-5 flex items-center justify-center rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-500 dark:text-blue-300 text-xs",children:[r+1,".",s+1]}),e.jsx("h3",{className:"text-sm font-medium text-blue-700 dark:text-blue-300",children:a.title})]})}),e.jsx(Y,{className:"px-4 pt-2 pb-3 bg-blue-50/30 dark:bg-blue-900/10",children:e.jsx("div",{className:"prose prose-sm max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:p(a.content)}})})})]},s)))})]})]},r)))})}):e.jsx("div",{className:"text-center p-6 text-muted-foreground dark:text-gray-400",children:e.jsx("p",{children:"Nenhuma informação disponível para este medicamento."})})]})]}):null}const _e=({name:a,description:r,brands:s,category:t,slug:i,id:d})=>e.jsxs(e.Fragment,{children:[e.jsx(we,{name:a,description:r,brands:s,category:t,slug:i}),e.jsx("div",{className:"bg-white/90 dark:bg-slate-800/90 shadow-md rounded-2xl border border-primary/10 dark:border-primary/20 overflow-hidden",children:e.jsxs("div",{className:"relative overflow-hidden",children:[e.jsx("div",{className:"h-2 bg-gradient-to-r from-primary via-primary/80 to-primary/60 dark:from-blue-500 dark:via-blue-400 dark:to-blue-300"}),e.jsxs("div",{className:"p-3 sm:p-5",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-3 sm:gap-4",children:[e.jsxs("div",{className:"space-y-2 sm:space-y-3",children:[e.jsx("h1",{className:"title-gradient text-2xl sm:text-3xl md:text-4xl font-bold animate-fade-in leading-tight",children:a}),t&&e.jsx("div",{className:"flex",children:e.jsx("span",{className:"px-2.5 sm:px-3 py-1 sm:py-1.5 rounded-lg text-xs sm:text-sm bg-primary/10 dark:bg-primary/20 text-primary dark:text-blue-300 font-medium w-fit",children:t})})]}),e.jsxs("div",{className:"flex flex-wrap gap-1.5 sm:gap-2 md:gap-3",children:[s&&e.jsxs(f,{children:[e.jsx(j,{asChild:!0,children:e.jsxs(n,{variant:"outline",size:"sm",className:"gap-1 sm:gap-2 border-primary/20 bg-white dark:bg-slate-700 dark:border-primary/30 shadow-sm hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-white text-xs sm:text-sm px-2 sm:px-3 py-1.5 sm:py-2 h-auto flex-shrink-0",children:[e.jsx(he,{className:"h-3 w-3 sm:h-4 sm:w-4 text-primary dark:text-blue-400"}),e.jsx("span",{className:"whitespace-nowrap",children:"Nomes Comerciais"})]})}),e.jsxs(v,{className:"max-w-md max-h-[80vh] dark:bg-slate-800",children:[e.jsx(w,{children:e.jsx(N,{className:"text-lg sm:text-xl font-bold text-primary dark:text-blue-400",children:"Nomes Comerciais"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground dark:text-gray-300",children:"Este medicamento pode ser encontrado com os seguintes nomes comerciais:"}),e.jsx("div",{className:"max-h-[50vh] overflow-y-auto p-3 bg-primary/5 dark:bg-primary/10 rounded-lg border",children:e.jsx("ul",{className:"grid grid-cols-1 sm:grid-cols-2 gap-1 text-sm",children:s.split(",").map(((a,r)=>e.jsx("li",{className:"text-gray-700 dark:text-gray-200 py-1 px-2 rounded hover:bg-primary/10 dark:hover:bg-primary/20 transition-colors",children:a.trim()},r)))})})]})]})]}),d&&e.jsx(Ne,{medicationId:d,medicationName:a,slug:i})]})]}),r&&e.jsx("div",{className:"mt-3 sm:mt-4 text-muted-foreground dark:text-gray-300 animate-fade-in delay-100 bg-gray-50 dark:bg-slate-700/50 p-3 sm:p-4 rounded-lg border border-gray-100 dark:border-gray-700",children:e.jsx("p",{className:"leading-relaxed text-sm sm:text-base",children:r})})]})]})})]}),Ce=({medication:r,weight:s,age:t,requiredMeasures:i})=>{const d=[...r.pedbook_medication_use_cases].map(((e,a)=>({...e,display_order:e.display_order??a}))).sort(((e,a)=>e.display_order-a.display_order)),[l,o]=a.useState(d[0]?.id||""),[n,m]=a.useState(!1),[g,p]=a.useState(!1),[u,b]=a.useState(!1);return a.useEffect((()=>{d.length>0&&!l&&o(d[0].id)}),[d]),a.useEffect((()=>{d.length>0&&o(d[0].id)}),[r]),r.pedbook_medication_use_cases?.length?e.jsxs("div",{className:"space-y-6 w-full",children:[e.jsxs($,{value:l,className:"w-full",children:[e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm",children:e.jsxs("div",{className:"p-3 sm:p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("div",{className:"w-2 h-2 bg-primary rounded-full"}),e.jsx("span",{className:"text-xs sm:text-sm font-medium text-primary",children:"Selecione uma indicação"})]}),e.jsxs(M,{value:l,onValueChange:o,children:[e.jsx(S,{className:"w-full",children:e.jsx(W,{placeholder:"Escolha a condição clínica..."})}),e.jsx(A,{className:"max-w-[90vw] sm:max-w-2xl",children:d.map((a=>e.jsx(L,{value:a.id,className:"cursor-pointer",children:a.name},a.id)))})]})]})})}),d.map((a=>e.jsxs(T,{value:a.id,className:"space-y-6 animate-fade-in",children:[a.description&&e.jsx("div",{className:"bg-gradient-to-br from-primary/5 via-primary/10 to-transparent dark:from-blue-900/30 dark:via-blue-900/20 dark:to-transparent p-6 rounded-xl border border-primary/10 dark:border-blue-800/40 backdrop-blur-sm",children:e.jsx("div",{className:"max-w-full break-words",children:e.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed whitespace-pre-wrap",children:a.description})})}),a.pedbook_medication_dosages?.length>0?e.jsx("div",{className:"space-y-4",children:a.pedbook_medication_dosages.map((a=>e.jsx(ee,{dosage:{...a,medication_id:a.medication_id||r?.id},weight:s,age:t,requiredMeasures:i},a.id)))}):e.jsx("div",{className:"text-center py-6 bg-muted/20 rounded-xl backdrop-blur-sm",children:e.jsx("p",{className:"text-muted-foreground",children:"Nenhuma dosagem cadastrada para esta indicação"})})]},a.id)))]}),r.contraindications&&e.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm",children:[e.jsxs("button",{onClick:()=>m(!n),className:"w-full p-3 sm:p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-red-100 dark:bg-red-900/30 rounded-lg",children:e.jsx(h,{className:"w-4 h-4 text-red-600 dark:text-red-400"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("h3",{className:"text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100",children:"Contraindicações"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Situações em que não deve ser usado"})]})]}),n?e.jsx(E,{className:"w-4 h-4 text-gray-400"}):e.jsx(c,{className:"w-4 h-4 text-gray-400"})]}),n&&e.jsx("div",{className:"px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"pt-3 flex items-start gap-2",children:[e.jsx(I,{className:"w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0"}),e.jsx("div",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap break-words min-w-0 flex-1",children:r.contraindications})]})})]}),r.guidelines&&e.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm",children:[e.jsxs("button",{onClick:()=>p(!g),className:"w-full p-3 sm:p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:e.jsx(re,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("h3",{className:"text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100",children:"Orientações Clínicas"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Diretrizes para uso adequado"})]})]}),g?e.jsx(E,{className:"w-4 h-4 text-gray-400"}):e.jsx(c,{className:"w-4 h-4 text-gray-400"})]}),g&&e.jsx("div",{className:"px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"pt-3 flex items-start gap-2",children:[e.jsx(q,{className:"w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0"}),e.jsx("div",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap break-words min-w-0 flex-1",children:r.guidelines})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm",children:[e.jsxs("button",{onClick:()=>b(!u),className:"w-full p-3 sm:p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:e.jsx(se,{className:"w-4 h-4 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("h3",{className:"text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100",children:"Referências Científicas"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Fontes bibliográficas e evidências"})]})]}),u?e.jsx(E,{className:"w-4 h-4 text-gray-400"}):e.jsx(c,{className:"w-4 h-4 text-gray-400"})]}),u&&e.jsx("div",{className:"px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"pt-3 flex items-start gap-2",children:[e.jsx(te,{className:"w-4 h-4 text-purple-600 dark:text-purple-400 mt-0.5 flex-shrink-0"}),e.jsx("div",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap break-words min-w-0 flex-1",children:r.scientific_references||"UpToDate. Evidence-based clinical decision support resource. Waltham, MA: Wolters Kluwer."})]})})]})]}):e.jsx("div",{className:"text-center py-8 bg-muted/20 rounded-lg backdrop-blur-sm",children:e.jsx("p",{className:"text-muted-foreground",children:"Nenhuma indicação de uso cadastrada para este medicamento"})})},$e=({medication:a,weight:r,displayWeight:s,setTempWeight:t,setWeight:i,age:d,setAge:l})=>{const o=a?.required_measures||["weight","age"];return e.jsxs("div",{className:"space-y-6",children:[e.jsx(X,{weight:s,onWeightChange:t,onWeightCommit:i,age:d,onAgeChange:l,onAgeCommit:l,requiredMeasures:o}),e.jsx("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-2 sm:p-6 border border-primary/10",children:e.jsx(Ce,{medication:a,weight:r,age:d,requiredMeasures:o})})]})},Me=({name:r,category:s,onClick:t,color:i="blue"})=>{const[d,l]=a.useState(!1);return e.jsxs("div",{className:z("relative w-full p-3 rounded-lg transition-all duration-300 cursor-pointer","bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-sm hover:shadow-md","border border-gray-100 dark:border-gray-700/50","hover:-translate-y-1 flex items-center gap-3",d&&"opacity-75 scale-95 pointer-events-none"),onClick:()=>{d||(l(!0),t?.(),setTimeout((()=>l(!1)),2e3))},children:[e.jsx("div",{className:z("absolute top-0 left-0 right-0 h-1 rounded-t-lg",i.includes("yellow")?"bg-yellow-500":i.includes("purple")?"bg-purple-500":i.includes("blue")?"bg-blue-500":i.includes("pink")?"bg-pink-500":i.includes("green")?"bg-green-500":i.includes("amber")?"bg-amber-500":i.includes("red")?"bg-red-500":i.includes("cyan")?"bg-cyan-500":i.includes("indigo")?"bg-indigo-500":i.includes("rose")?"bg-rose-500":"bg-primary")}),e.jsx("div",{className:"p-2 bg-primary/10 rounded-full dark:bg-blue-800/40 flex-shrink-0",children:d?e.jsx(_,{className:"h-5 w-5 text-primary dark:text-blue-400 animate-spin"}):e.jsx(b,{className:"h-5 w-5 text-primary dark:text-blue-400"})}),e.jsxs("div",{className:"flex-1 text-left min-w-0",children:[e.jsx("h4",{className:"font-medium text-primary dark:text-blue-400 text-sm sm:text-base truncate",children:r}),e.jsx("span",{className:"text-[10px] sm:text-xs text-gray-500 dark:text-gray-400",children:s})]})]})},Se=({selectedCategory:a})=>{const s=r(),{startLoading:t}=B();if(!a)return null;const i=ve(a),d=i.icon;return e.jsx("div",{className:"w-full md:bg-white/80 md:dark:bg-slate-800/80 md:backdrop-blur-sm md:rounded-xl md:border md:border-primary/10 md:dark:border-primary/20 md:shadow-lg",children:e.jsxs("div",{className:"p-2 md:p-6",children:[e.jsxs("div",{className:"card-header",children:[e.jsx("div",{className:`icon-container ${i.color} border border-gray-200 dark:border-gray-700 dark:bg-slate-700/50`,children:e.jsx(d,{className:"h-6 w-6 text-primary dark:text-blue-400 category-icon"})}),e.jsxs("h3",{className:"text-lg font-medium truncate text-gray-800 dark:text-gray-200",children:["Selecione um medicamento da categoria ",a.name,":"]})]}),e.jsx(p,{className:"h-[500px] pr-4",children:e.jsxs("div",{className:"grid gap-2 sm:gap-3",children:[a.pedbook_medications?.map((r=>e.jsx(Me,{name:r.name,category:a.name,onClick:()=>(e=>{t("Carregando medicamento..."),s(`/medicamentos/${e.slug}`)})(r),color:i.color},r.id))),(!a.pedbook_medications||0===a.pedbook_medications.length)&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx("p",{children:"Nenhum medicamento encontrado nesta categoria."}),e.jsxs("p",{className:"text-sm mt-2",children:['Verifique se há medicamentos cadastrados para "',a.name,'".']})]})]})})]})})},We=({title:r,icon:s,color:t,onClick:i,badge:d})=>{const[o,n]=a.useState(!1),c=l(),m=()=>t.includes("yellow")?"text-yellow-600 dark:text-yellow-400":t.includes("purple")?"text-purple-600 dark:text-purple-400":t.includes("blue")?"text-blue-600 dark:text-blue-400":t.includes("pink")?"text-pink-600 dark:text-pink-400":t.includes("green")?"text-green-600 dark:text-green-400":t.includes("amber")?"text-amber-600 dark:text-amber-400":t.includes("red")?"text-red-600 dark:text-red-400":t.includes("cyan")?"text-cyan-600 dark:text-cyan-400":t.includes("indigo")?"text-indigo-600 dark:text-indigo-400":t.includes("rose")?"text-rose-600 dark:text-rose-400":"text-blue-600 dark:text-blue-400";return e.jsxs("div",{className:z("group relative h-full p-4 sm:p-6 rounded-2xl transition-all duration-500 cursor-pointer","bg-gradient-to-br",t.includes("yellow")?"from-yellow-400/20 via-yellow-300/10 to-yellow-500/20":t.includes("purple")?"from-purple-400/20 via-purple-300/10 to-purple-500/20":t.includes("blue")?"from-blue-400/20 via-blue-300/10 to-blue-500/20":t.includes("pink")?"from-pink-400/20 via-pink-300/10 to-pink-500/20":t.includes("green")?"from-green-400/20 via-green-300/10 to-green-500/20":t.includes("amber")?"from-amber-400/20 via-amber-300/10 to-amber-500/20":t.includes("red")?"from-red-400/20 via-red-300/10 to-red-500/20":t.includes("cyan")?"from-cyan-400/20 via-cyan-300/10 to-cyan-500/20":t.includes("indigo")?"from-indigo-400/20 via-indigo-300/10 to-indigo-500/20":t.includes("rose")?"from-rose-400/20 via-rose-300/10 to-rose-500/20":"from-blue-400/20 via-blue-300/10 to-blue-500/20","backdrop-blur-xl border-2 border-gray-200/60 dark:border-white/10","shadow-lg hover:shadow-2xl",t.includes("yellow")?"hover:shadow-yellow-500/25":t.includes("purple")?"hover:shadow-purple-500/25":t.includes("blue")?"hover:shadow-blue-500/25":t.includes("pink")?"hover:shadow-pink-500/25":t.includes("green")?"hover:shadow-green-500/25":t.includes("amber")?"hover:shadow-amber-500/25":t.includes("red")?"hover:shadow-red-500/25":t.includes("cyan")?"hover:shadow-cyan-500/25":t.includes("indigo")?"hover:shadow-indigo-500/25":t.includes("rose")?"hover:shadow-rose-500/25":"hover:shadow-blue-500/25","hover:-translate-y-2 hover:scale-[1.02]","overflow-hidden",o&&"opacity-75 scale-95 pointer-events-none"),onClick:()=>{o||(n(!0),i?.(),setTimeout((()=>n(!1)),2e3))},onMouseEnter:()=>{c.prefetchQuery({queryKey:["medication-categories","with-medications"],staleTime:9e5})},children:[e.jsx("div",{className:"absolute top-2 right-2 w-16 h-16 opacity-10",children:e.jsx("div",{className:"grid grid-cols-4 gap-1 w-full h-full",children:[...Array(16)].map(((a,r)=>e.jsx("div",{className:z("w-1 h-1 rounded-full",m().split(" ")[0])},r)))})}),e.jsxs("div",{className:"relative flex flex-col items-center text-center h-full justify-between",children:[e.jsx("div",{className:z("w-12 h-12 sm:w-16 sm:h-16 rounded-2xl flex items-center justify-center mb-3 sm:mb-4","backdrop-blur-sm border shadow-lg","group-hover:scale-110 transition-transform duration-300",t.includes("yellow")?"bg-yellow-100/80 dark:bg-yellow-900/40 border-yellow-200/50 dark:border-yellow-700/50":t.includes("purple")?"bg-purple-100/80 dark:bg-purple-900/40 border-purple-200/50 dark:border-purple-700/50":t.includes("blue")?"bg-blue-100/80 dark:bg-blue-900/40 border-blue-200/50 dark:border-blue-700/50":t.includes("pink")?"bg-pink-100/80 dark:bg-pink-900/40 border-pink-200/50 dark:border-pink-700/50":t.includes("green")?"bg-green-100/80 dark:bg-green-900/40 border-green-200/50 dark:border-green-700/50":t.includes("amber")?"bg-amber-100/80 dark:bg-amber-900/40 border-amber-200/50 dark:border-amber-700/50":t.includes("red")?"bg-red-100/80 dark:bg-red-900/40 border-red-200/50 dark:border-red-700/50":t.includes("cyan")?"bg-cyan-100/80 dark:bg-cyan-900/40 border-cyan-200/50 dark:border-cyan-700/50":t.includes("indigo")?"bg-indigo-100/80 dark:bg-indigo-900/40 border-indigo-200/50 dark:border-indigo-700/50":t.includes("rose")?"bg-rose-100/80 dark:bg-rose-900/40 border-rose-200/50 dark:border-rose-700/50":"bg-blue-100/80 dark:bg-blue-900/40 border-blue-200/50 dark:border-blue-700/50"),children:o?e.jsx(_,{className:z("w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300 animate-spin",m())}):e.jsx(s,{className:z("w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300","group-hover:scale-110",m())})}),e.jsx("h3",{className:"font-bold text-sm sm:text-base text-gray-800 dark:text-gray-100 line-clamp-2 leading-tight",children:r}),d&&e.jsx("div",{className:"mt-3",children:e.jsx(P,{variant:"outline",className:z("backdrop-blur-sm border-white/30 dark:border-white/20","text-gray-700 dark:text-gray-200 text-[10px] font-medium px-3 py-1","bg-white/50 dark:bg-black/30"),children:d})})]})]})},Ae=({showTitleOnMobile:r=!1})=>{const t=s(),[i,d]=a.useState(null),[l,o]=a.useState(!1),[c,m]=a.useState(null),{startLoading:g,stopLoading:p}=B(),{data:u,isLoading:b}=ae();a.useEffect((()=>{const e=t.state;e?.selectedCategory&&(d(e.selectedCategory),window.history.replaceState({},document.title))}),[t.state]),a.useEffect((()=>{const e=()=>{m(window.innerWidth<768)};return e(),window.addEventListener("resize",e),p(),()=>{window.removeEventListener("resize",e)}}),[p]);const x=u&&Array.isArray(u)?u.find((e=>e.id===i)):void 0;return null===c?null:b?e.jsx("div",{className:"w-full space-y-8 p-6 md:p-10 bg-gradient-to-br from-white/95 via-white/90 to-blue-50/30 dark:from-slate-900/95 dark:via-slate-800/90 dark:to-slate-900/30 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-white/10 overflow-hidden",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsxs("div",{className:"text-center space-y-6 mb-8",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"h-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg w-2/3 mx-auto"}),e.jsx("div",{className:"w-24 h-1 bg-gray-300 dark:bg-gray-600 mx-auto rounded-full"})]}),e.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"})]}),e.jsx("div",{className:"grid gap-4 sm:gap-6 grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4",children:[...Array(12)].map(((a,r)=>e.jsx("div",{className:"h-40 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-2xl"},r)))})]})}):e.jsxs("div",{className:"w-full space-y-8 p-6 md:p-10 bg-gradient-to-br from-white/98 via-white/95 to-blue-50/40 dark:from-slate-900/98 dark:via-slate-800/95 dark:to-slate-900/40 backdrop-blur-xl rounded-3xl shadow-2xl border-2 border-gray-300/50 dark:border-slate-600/50 overflow-hidden relative",children:[e.jsxs("div",{className:"absolute inset-0 opacity-5 dark:opacity-10",children:[e.jsx("div",{className:"absolute top-10 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl"}),e.jsx("div",{className:"absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl"}),e.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-cyan-500 rounded-full blur-2xl"})]}),e.jsx("div",{className:"relative z-10",children:i?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(n,{variant:"ghost",size:"sm",className:"flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-xl px-4 py-2 transition-all duration-200",onClick:()=>{d(null)},children:[e.jsx(ce,{className:"h-4 w-4"}),"Voltar para categorias"]})}),e.jsx(Se,{selectedCategory:x})]}):e.jsxs(e.Fragment,{children:[!c&&e.jsx("div",{className:"text-center space-y-6 mb-8",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent",children:"Categorias de Medicamentos"}),e.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"})]})}),e.jsx("div",{className:"grid gap-4 sm:gap-6 grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4",children:u?.map((a=>{const r=ve(a);return e.jsx("div",{className:"cursor-pointer",children:e.jsx(We,{title:a.name,icon:r.icon,color:r.color,onClick:()=>{d(a.id)},badge:void 0})},a.id)}))})]})})]})},Le=({className:a})=>e.jsxs("div",{className:z("flex-1 space-y-6 animate-fade-in",a),children:[e.jsxs("div",{className:"bg-white/90 dark:bg-slate-800/90 shadow-md rounded-2xl border border-primary/10 dark:border-primary/20 overflow-hidden",children:[e.jsx("div",{className:"h-2 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 animate-pulse"}),e.jsxs("div",{className:"p-6 space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-xl animate-pulse"}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsx("div",{className:"h-6 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg w-2/3 animate-pulse"}),e.jsx("div",{className:"h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/2 animate-pulse"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-full animate-pulse"}),e.jsx("div",{className:"h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-3/4 animate-pulse"})]})]})]}),e.jsx("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 border border-primary/10",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-5 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/3 animate-pulse"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/2 animate-pulse"}),e.jsx("div",{className:"h-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg animate-pulse"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/2 animate-pulse"}),e.jsx("div",{className:"h-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg animate-pulse"})]})]})]})}),e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-6 h-6 border-2 border-primary/20 border-t-primary rounded-full animate-spin"}),e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:"Carregando informações do medicamento..."})]})})]}),Te=({categories:a,currentMedicationId:r,onMedicationSelect:s,isLoading:t,medication:i,weight:d,displayWeight:l,setTempWeight:o,setWeight:n,age:c,setAge:m,slug:g})=>e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(je,{categories:a,currentMedicationId:r,onMedicationSelect:s}),g?t?e.jsx(Le,{}):i?e.jsxs(O.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.4,ease:"easeOut"},className:"space-y-4",children:[e.jsx(_e,{name:i.name,description:i.description,brands:i.brands,category:i.pedbook_medication_categories?.name,slug:i.slug,id:i.id}),e.jsx($e,{medication:i,weight:d,displayWeight:l,setTempWeight:o,setWeight:n,age:c,setAge:m})]},i.id):null:e.jsx(Ae,{})]}),Ee=({categories:s,currentMedicationId:t})=>{const i=r(),[d,l]=a.useState(""),[o,c]=a.useState(!1);a.useEffect((()=>{const e=()=>{c(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]);const u=e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase(),b=s&&Array.isArray(s)?s.map((e=>({...e,pedbook_medications:e.pedbook_medications?.filter((e=>((e,a)=>{if(!a)return!0;const r=u(a);if(u(e.name).includes(r))return!0;if(e.brands&&u(e.brands).includes(r))return!0;if(e.slug){const r=a.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,"");if(e.slug.includes(r))return!0}return!1})(e,d)))}))).filter((e=>e.pedbook_medications?.length>0)).sort(((e,a)=>u(e.name).localeCompare(u(a.name)))):[],x=a.useCallback(((e,a)=>{i(`/medicamentos/${e}`)}),[i]),{debouncedCallback:h}=(e=>{const r=a.useRef(null);return{debouncedCallback:a.useCallback(((...a)=>{r.current&&clearTimeout(r.current),r.current=setTimeout((()=>{e(...a)}),300)}),[e,300]),cancel:a.useCallback((()=>{r.current&&(clearTimeout(r.current),r.current=null)}),[])}})(x);return o?e.jsx(je,{categories:s,currentMedicationId:t,onMedicationSelect:h}):e.jsxs("aside",{className:"w-80 bg-gradient-to-br from-white/98 via-white/95 to-blue-50/40 dark:from-slate-900/98 dark:via-slate-800/95 dark:to-slate-900/40 backdrop-blur-xl rounded-2xl shadow-2xl border-2 border-gray-300/60 dark:border-slate-600/60 p-6 h-[calc(100vh-8rem)] flex flex-col",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(m,{className:"absolute left-3 top-3 h-5 w-5 text-gray-500 dark:text-gray-400"}),e.jsx(g,{placeholder:"Buscar por nome ou marca comercial...",className:"pl-11 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-300/70 dark:border-gray-600/70 rounded-xl h-12 text-sm shadow-sm focus:shadow-lg focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-200",value:d,onChange:e=>l(e.target.value)})]}),e.jsxs(n,{variant:"outline",className:"w-full flex items-center gap-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-300/70 dark:border-gray-600/70 rounded-xl h-12 shadow-sm hover:shadow-lg transition-all duration-200 font-medium",onClick:()=>{i("/medicamentos/painel")},children:[e.jsx(xe,{className:"h-5 w-5"}),"Todas categorias"]})]}),e.jsx(p,{className:"flex-1 pr-2 mt-6",children:e.jsx("nav",{className:"space-y-5",children:d&&0===b?.length?e.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[e.jsx(m,{className:"h-8 w-8 mx-auto mb-3 opacity-50"}),e.jsx("p",{className:"text-sm",children:"Nenhum medicamento encontrado"}),e.jsx("p",{className:"text-xs mt-1",children:"Tente buscar por nome ou marca comercial"})]}):b?.map((a=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-500/15 to-purple-500/15 dark:from-blue-400/15 dark:to-purple-400/15 rounded-xl p-4 border-2 border-blue-200/50 dark:border-blue-700/50 shadow-sm",children:e.jsx("h3",{className:"font-bold text-sm text-blue-800 dark:text-blue-200 uppercase tracking-wider",children:a.name})}),e.jsx("div",{className:"space-y-2 pl-1",children:a.pedbook_medications?.map((a=>e.jsx("div",{className:"group relative rounded-xl border-2 transition-all duration-200 "+(a.id===t?"bg-blue-500 border-blue-500 shadow-lg":"bg-white/70 dark:bg-slate-800/70 border-gray-200/60 dark:border-gray-600/60 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50/80 dark:hover:bg-blue-900/20"),children:e.jsx(n,{variant:"ghost",className:"w-full justify-start text-left h-auto py-3 px-4 rounded-xl border-0 "+(a.id===t?"text-white hover:bg-blue-600":"text-gray-700 dark:text-gray-300 hover:bg-transparent"),onClick:()=>h(a.slug,a),children:e.jsxs("div",{className:"text-xs leading-relaxed break-words",children:[e.jsx("div",{className:"font-medium",children:a.name}),d&&a.brands&&u(a.brands).includes(u(d))&&e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Marcas: ",a.brands]})]})})},a.id)))})]},a.id)))})})]})},Ie=({categories:a,currentMedicationId:r,isLoading:s,medication:t,weight:i,displayWeight:d,setTempWeight:l,setWeight:o,age:n,setAge:c,slug:m})=>e.jsxs("div",{className:"flex gap-6",children:[e.jsx(Ee,{categories:a,currentMedicationId:r}),m?s?e.jsx(Le,{}):t?e.jsxs(O.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.4,ease:"easeOut"},className:"flex-1 space-y-6",children:[e.jsx(_e,{name:t.name,description:t.description,brands:t.brands,category:t.pedbook_medication_categories?.name,slug:t.slug,id:t.id}),e.jsx($e,{medication:t,weight:i,displayWeight:d,setTempWeight:l,setWeight:o,age:n,setAge:c})]},t.id):null:e.jsx(Ae,{})]});function qe(){const l=r(),{slug:o}=t(),c=s(),{weight:m,setWeight:g,displayWeight:p,setTempWeight:u}=H(),{age:b,setAge:x}=V(),[h,y]=a.useState(!1),[f,j]=a.useState(!1),{stopLoading:k}=B(),v=a.useMemo((()=>["medication",o]),[o]);a.useEffect((()=>{const e=()=>{j(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),a.useEffect((()=>{c.state?.selectedCategory&&window.history.replaceState({},document.title)}),[c]);const{data:w,isLoading:N,isFetching:_,error:$}=d({queryKey:v,queryFn:async()=>{if(!o)return k(),null;const{data:e,error:a}=await C.from("pedbook_medications").select("\n          id,\n          name,\n          slug,\n          description,\n          brands,\n          contraindications,\n          guidelines,\n          scientific_references,\n          category_id,\n          pedbook_medication_categories!inner (\n            name\n          ),\n          pedbook_medication_use_cases (\n            id,\n            name,\n            description,\n            display_order,\n            pedbook_medication_dosages (\n              id,\n              name,\n              dosage_template,\n              summary,\n              age_group,\n              medication_id\n            )\n          )\n        ").eq("slug",o).maybeSingle();if(a){if(k(),"PGRST116"===a.code)return null;throw a}return e},enabled:!!o,staleTime:9e5,gcTime:36e5,refetchOnMount:!1,refetchOnWindowFocus:!1,onError:e=>{k()}});a.useEffect((()=>{if(w){document.title=w.name;const e=document.querySelector('meta[name="description"]');if(e)e.setAttribute("content",w.description);else{const e=document.createElement("meta");e.setAttribute("name","description"),e.setAttribute("content",w.description),document.head.appendChild(e)}}}),[w]),a.useEffect((()=>{N||_||!w&&!$||k()}),[N,_,w,$,k]);const{data:M}=ae();return e.jsxs("div",{className:F.pageBackground(),children:[e.jsx(D,{}),e.jsxs("main",{className:"flex-1 container mx-auto py-4 px-1 sm:px-4",children:[e.jsxs(i,{to:"/",className:"flex items-center justify-center gap-1.5 text-primary/60 hover:text-primary transition-colors mb-6 group hidden md:flex dark:text-blue-400/60 dark:hover:text-blue-400",children:[e.jsx(me,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Início"})]}),f?e.jsx(Te,{categories:M||[],currentMedicationId:w?.id||"",onMedicationSelect:e=>l(`/medicamentos/${e}`),isLoading:N,medication:w,weight:m,displayWeight:p,setTempWeight:u,setWeight:g,age:b,setAge:x,slug:o}):e.jsx(Ie,{categories:M||[],currentMedicationId:w?.id||"",isLoading:N,medication:w,weight:m,displayWeight:p,setTempWeight:u,setWeight:g,age:b,setAge:x,slug:o})]}),e.jsx(K,{}),e.jsx(n,{variant:"outline",size:"icon",className:z("fixed bottom-4 right-4 rounded-full transition-all duration-300","bg-gradient-to-r from-primary/20 to-primary/10 backdrop-blur-sm border-primary/20 dark:bg-gradient-to-r dark:from-blue-800/40 dark:to-blue-900/30 dark:border-blue-800/30","hover:shadow-lg hover:shadow-primary/20 dark:hover:shadow-blue-800/20",h?"opacity-100 translate-y-0":"opacity-0 translate-y-4 pointer-events-none"),onClick:()=>{const e=document.querySelector(".medication-details");e?.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Voltar ao topo da página",children:e.jsx(E,{className:"h-4 w-4"})})]})}export{qe as default};
