import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{n as r,o as s}from"./radix-forms-DX-owj97.js";import{j as t}from"./index-D89HBjcn.js";const o=a.forwardRef((({className:a,...o},i)=>e.jsx(r,{className:t("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50","data-[state=checked]:bg-primary data-[state=checked]:border-primary","data-[state=unchecked]:bg-input data-[state=unchecked]:border-gray-300 dark:data-[state=unchecked]:bg-slate-700 dark:data-[state=unchecked]:border-slate-500",a),...o,ref:i,children:e.jsx(s,{className:t("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform","data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0","dark:bg-slate-200")})})));o.displayName=r.displayName;export{o as S};
