/**
 * Script específico para WebView Android
 * Intercepta o botão voltar físico do dispositivo
 */

(function() {
  'use strict';

  // Detectar se está em WebView Android
  function isAndroidWebView() {
    const ua = navigator.userAgent.toLowerCase();
    return /android/i.test(ua) && 
           (/wv/.test(ua) || /webview/.test(ua) || 
            'Android' in window || 'AndroidInterface' in window);
  }

  if (!isAndroidWebView()) {
    return;
  }

  console.log('🤖 WebView Android detectado - Aplicando interceptação do botão voltar');

  // Estratégia 1: Interface global para Android
  window.WebViewBackHandler = {
    onBackPressed: function() {
      console.log('📱 onBackPressed chamado via WebViewBackHandler');
      
      // Verificar se está na home
      if (window.location.pathname === '/') {
        console.log('🏠 Na home - permitindo sair');
        return false; // Permite sair do app
      }

      // Tentar navegar internamente
      try {
        // Disparar evento customizado para o React
        const event = new CustomEvent('webview-back-button', {
          detail: { pathname: window.location.pathname }
        });
        document.dispatchEvent(event);
        
        console.log('🔄 Evento de navegação interna disparado');
        return true; // Indica que foi tratado
      } catch (error) {
        console.error('❌ Erro ao disparar evento:', error);
        return false;
      }
    },

    canGoBack: function() {
      return window.location.pathname !== '/';
    },

    getCurrentPath: function() {
      return window.location.pathname;
    }
  };

  // Estratégia 2: Múltiplas interfaces para compatibilidade
  window.handleBackButton = window.WebViewBackHandler.onBackPressed;
  window.onBackPressed = window.WebViewBackHandler.onBackPressed;

  // Estratégia 3: Interface Android padrão
  if (window.Android) {
    window.Android.onBackPressed = window.WebViewBackHandler.onBackPressed;
  }

  // Estratégia 4: Interface AndroidInterface
  if (window.AndroidInterface) {
    window.AndroidInterface.onBackPressed = window.WebViewBackHandler.onBackPressed;
  }

  // Estratégia 5: Manipulação agressiva do histórico
  let historyLength = 0;
  
  function addHistoryEntry() {
    historyLength++;
    window.history.pushState(
      { webview: true, id: historyLength }, 
      '', 
      window.location.pathname + '#webview-' + historyLength
    );
  }

  // Adicionar entradas iniciais
  for (let i = 0; i < 3; i++) {
    addHistoryEntry();
  }

  // Interceptar popstate
  window.addEventListener('popstate', function(event) {
    console.log('📱 PopState interceptado:', event.state);
    
    if (event.state && event.state.webview) {
      event.preventDefault();
      
      // Chamar handler
      const handled = window.WebViewBackHandler.onBackPressed();
      
      if (handled) {
        // Recriar entradas do histórico
        setTimeout(function() {
          for (let i = 0; i < 2; i++) {
            addHistoryEntry();
          }
        }, 100);
      }
    }
  });

  // Estratégia 6: Interceptar teclas físicas
  document.addEventListener('keydown', function(event) {
    // Teclas que podem ser mapeadas para o botão voltar
    if (event.key === 'Escape' || event.keyCode === 27) {
      const target = event.target;
      
      // Não interceptar se estiver em input
      if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA') {
        event.preventDefault();
        event.stopPropagation();
        
        console.log('⌨️ Tecla Escape interceptada');
        window.WebViewBackHandler.onBackPressed();
      }
    }
  }, true);

  // Estratégia 7: Monitorar mudanças de visibilidade
  document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
      console.log('👁️ App ficou oculto - possível botão voltar');
    } else {
      console.log('👁️ App ficou visível novamente');
    }
  });

  // Estratégia 8: Interceptar beforeunload (removido para evitar popups desnecessários no admin)

  // Notificar que está pronto
  console.log('✅ WebView Android back button handler configurado');
  
  // Disparar evento para notificar o React
  document.dispatchEvent(new CustomEvent('webview-ready', {
    detail: { timestamp: Date.now() }
  }));

})();
