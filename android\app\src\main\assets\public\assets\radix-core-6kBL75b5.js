import{r as e,$ as t,a as n,R as r}from"./critical-DVX9Inzy.js";var o={exports:{}},a={},i=e,c=Symbol.for("react.element"),u=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,l=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(e,t,n){var r,o={},a=null,i=null;for(r in void 0!==n&&(a=""+n),void 0!==t.key&&(a=""+t.key),void 0!==t.ref&&(i=t.ref),t)s.call(t,r)&&!d.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:c,type:e,key:a,ref:i,props:o,_owner:l.current}}a.Fragment=u,a.jsx=f,a.jsxs=f,o.exports=a;var p=o.exports;function m(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function v(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function h(...t){return e.useCallback(v(...t),t)}function g(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=r?.[t]||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const a=e.createContext(o),i=r.length;r=[...r,o];const c=n=>{const{scope:r,children:o,...c}=n,u=r?.[t]?.[i]||a,s=e.useMemo((()=>c),Object.values(c));return p.jsx(u.Provider,{value:s,children:o})};return c.displayName=n+"Provider",[c,function(r,c){const u=c?.[t]?.[i]||a,s=e.useContext(u);if(s)return s;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},y(o,...n)]}function y(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var E=Boolean(globalThis?.document)?e.useLayoutEffect:()=>{},b=t["useId".toString()]||(()=>{}),w=0;function C(t){const[n,r]=e.useState(b());return E((()=>{r((e=>e??String(w++)))}),[t]),n?`radix-${n}`:""}function N(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>n.current?.(...e)),[])}function R({prop:t,defaultProp:n,onChange:r=()=>{}}){const[o,a]=function({defaultProp:t,onChange:n}){const r=e.useState(t),[o]=r,a=e.useRef(o),i=N(n);return e.useEffect((()=>{a.current!==o&&(i(o),a.current=o)}),[o,a,i]),r}({defaultProp:n,onChange:r}),i=void 0!==t,c=i?t:o,u=N(r);return[c,e.useCallback((e=>{if(i){const n="function"==typeof e?e(t):e;n!==t&&u(n)}else a(e)}),[i,t,a,u])]}var O=e.forwardRef(((t,n)=>{const{children:r,...o}=t,a=e.Children.toArray(r),i=a.find(D);if(i){const t=i.props.children,r=a.map((n=>n===i?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n));return p.jsx(x,{...o,ref:n,children:e.isValidElement(t)?e.cloneElement(t,void 0,r):null})}return p.jsx(x,{...o,ref:n,children:r})}));O.displayName="Slot";var x=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}(r);return e.cloneElement(r,{...P(o,r.props),ref:n?v(n,t):t})}return e.Children.count(r)>1?e.Children.only(null):null}));x.displayName="SlotClone";var S=({children:e})=>p.jsx(p.Fragment,{children:e});function D(t){return e.isValidElement(t)&&t.type===S}function P(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}var _=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((t,n)=>{const r=e.forwardRef(((e,t)=>{const{asChild:r,...o}=e,a=r?O:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),p.jsx(a,{...o,ref:t})}));return r.displayName=`Primitive.${n}`,{...t,[n]:r}}),{});function T(e,t){e&&n.flushSync((()=>e.dispatchEvent(t)))}function M(t,n=globalThis?.document){const r=N(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}var L,A="dismissableLayer.update",j=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),k=e.forwardRef(((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:i,onInteractOutside:c,onDismiss:u,...s}=t,l=e.useContext(j),[d,f]=e.useState(null),v=d?.ownerDocument??globalThis?.document,[,g]=e.useState({}),y=h(n,(e=>f(e))),E=Array.from(l.layers),[b]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),w=E.indexOf(b),C=d?E.indexOf(d):-1,R=l.layersWithOutsidePointerEventsDisabled.size>0,O=C>=w,x=function(t,n=globalThis?.document){const r=N(t),o=e.useRef(!1),a=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){F("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...l.branches].some((e=>e.contains(t)));O&&!n&&(a?.(e),c?.(e),e.defaultPrevented||u?.())}),v),S=function(t,n=globalThis?.document){const r=N(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{e.target&&!o.current&&F("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...l.branches].some((e=>e.contains(t)))||(i?.(e),c?.(e),e.defaultPrevented||u?.())}),v);return M((e=>{C===l.layers.size-1&&(o?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))}),v),e.useEffect((()=>{if(d)return r&&(0===l.layersWithOutsidePointerEventsDisabled.size&&(L=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(d)),l.layers.add(d),I(),()=>{r&&1===l.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=L)}}),[d,v,r,l]),e.useEffect((()=>()=>{d&&(l.layers.delete(d),l.layersWithOutsidePointerEventsDisabled.delete(d),I())}),[d,l]),e.useEffect((()=>{const e=()=>g({});return document.addEventListener(A,e),()=>document.removeEventListener(A,e)}),[]),p.jsx(_.div,{...s,ref:y,style:{pointerEvents:R?O?"auto":"none":void 0,...t.style},onFocusCapture:m(t.onFocusCapture,S.onFocusCapture),onBlurCapture:m(t.onBlurCapture,S.onBlurCapture),onPointerDownCapture:m(t.onPointerDownCapture,x.onPointerDownCapture)})}));function I(){const e=new CustomEvent(A);document.dispatchEvent(e)}function F(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?T(o,a):o.dispatchEvent(a)}k.displayName="DismissableLayer",e.forwardRef(((t,n)=>{const r=e.useContext(j),o=e.useRef(null),a=h(n,o);return e.useEffect((()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}}),[r.branches]),p.jsx(_.div,{...t,ref:a})})).displayName="DismissableLayerBranch";var W="focusScope.autoFocusOnMount",B="focusScope.autoFocusOnUnmount",U={bubbles:!1,cancelable:!0},$=e.forwardRef(((t,n)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:a,onUnmountAutoFocus:i,...c}=t,[u,s]=e.useState(null),l=N(a),d=N(i),f=e.useRef(null),m=h(n,(e=>s(e))),v=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect((()=>{if(o){let e=function(e){if(v.paused||!u)return;const t=e.target;u.contains(t)?f.current=t:X(f.current,{select:!0})},t=function(e){if(v.paused||!u)return;const t=e.relatedTarget;null!==t&&(u.contains(t)||X(f.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&X(u)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return u&&r.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[o,u,v.paused]),e.useEffect((()=>{if(u){V.add(v);const e=document.activeElement;if(!u.contains(e)){const t=new CustomEvent(W,U);u.addEventListener(W,l),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(X(r,{select:t}),document.activeElement!==n)return}(K(u).filter((e=>"A"!==e.tagName)),{select:!0}),document.activeElement===e&&X(u))}return()=>{u.removeEventListener(W,l),setTimeout((()=>{const t=new CustomEvent(B,U);u.addEventListener(B,d),u.dispatchEvent(t),t.defaultPrevented||X(e??document.body,{select:!0}),u.removeEventListener(B,d),V.remove(v)}),0)}}}),[u,l,d,v]);const g=e.useCallback((e=>{if(!r&&!o)return;if(v.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[o,a]=function(e){const t=K(e);return[Y(t,e),Y(t.reverse(),e)]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&X(a,{select:!0})):(e.preventDefault(),r&&X(o,{select:!0})):n===t&&e.preventDefault()}}),[r,o,v.paused]);return p.jsx(_.div,{tabIndex:-1,...c,ref:m,onKeyDown:g})}));function K(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Y(e,t){for(const n of e)if(!z(n,{upTo:t}))return n}function z(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function X(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}$.displayName="FocusScope";var V=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Z(e,t),e.unshift(t)},remove(t){e=Z(e,t),e[0]?.resume()}}}();function Z(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var q=e.forwardRef(((t,n)=>{const{container:o,...a}=t,[i,c]=e.useState(!1);E((()=>c(!0)),[]);const u=o||i&&globalThis?.document?.body;return u?r.createPortal(p.jsx(_.div,{...a,ref:n}),u):null}));q.displayName="Portal";var H=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef({}),a=e.useRef(t),i=e.useRef("none"),c=t?"mounted":"unmounted",[u,s]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=G(o.current);i.current="mounted"===u?e:"none"}),[u]),E((()=>{const e=o.current,n=a.current;if(n!==t){const r=i.current,o=G(e);s(t?"MOUNT":"none"===o||"none"===e?.display?"UNMOUNT":n&&r!==o?"ANIMATION_OUT":"UNMOUNT"),a.current=t}}),[t,s]),E((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const i=G(o.current).includes(r.animationName);if(r.target===n&&i&&(s("ANIMATION_END"),!a.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},c=e=>{e.target===n&&(i.current=G(o.current))};return n.addEventListener("animationstart",c),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",c),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}s("ANIMATION_END")}),[n,s]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:e.useCallback((e=>{e&&(o.current=getComputedStyle(e)),r(e)}),[])}}(n),a="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),i=h(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}(a));return"function"==typeof r||o.isPresent?e.cloneElement(a,{ref:i}):null};function G(e){return e?.animationName||"none"}H.displayName="Presence";var J=0;function Q(){e.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ee()),document.body.insertAdjacentElement("beforeend",e[1]??ee()),J++,()=>{1===J&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),J--}}),[])}function ee(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var te=function(){return te=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},te.apply(this,arguments)};function ne(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function re(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var oe="right-scroll-bar-position",ae="width-before-scroll-bar";function ie(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var ce="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,ue=new WeakMap;function se(t,n){var r,o,a=(r=function(e){return t.forEach((function(t){return ie(t,e)}))},(o=e.useState((function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(e){var t=o.value;t!==e&&(o.value=e,o.callback(e,t))}}}}))[0]).callback=r,o.facade);return ce((function(){var e=ue.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach((function(e){r.has(e)||ie(e,null)})),r.forEach((function(e){n.has(e)||ie(e,o)}))}ue.set(a,t)}),[t]),a}function le(e){return e}function de(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=le);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}}}();return t.options=te({async:!0,ssr:!1},e),t}var fe=function(t){var n=t.sideCar,r=ne(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,te({},r))};function pe(e,t){return e.useMedium(t),fe}fe.isSideCarExport=!0;var me=de(),ve=function(){},he=e.forwardRef((function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:ve,onWheelCapture:ve,onTouchMoveCapture:ve}),a=o[0],i=o[1],c=t.forwardProps,u=t.children,s=t.className,l=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noIsolation,v=t.inert,h=t.allowPinchZoom,g=t.as,y=void 0===g?"div":g,E=t.gapMode,b=ne(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),w=p,C=se([r,n]),N=te(te({},b),a);return e.createElement(e.Fragment,null,d&&e.createElement(w,{sideCar:me,removeScrollBar:l,shards:f,noIsolation:m,inert:v,setCallbacks:i,allowPinchZoom:!!h,lockRef:r,gapMode:E}),c?e.cloneElement(e.Children.only(u),te(te({},N),{ref:C})):e.createElement(y,te({},N,{className:s,ref:C}),u))}));he.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},he.classNames={fullWidth:ae,zeroRight:oe};var ge=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ye=function(){var t,n=(t=ge(),function(n,r){e.useEffect((function(){return t.add(n),function(){t.remove()}}),[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},Ee={left:0,top:0,right:0,gap:0},be=function(e){return parseInt(e||"",10)||0},we=ye(),Ce="data-scroll-locked",Ne=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(Ce,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(oe," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(ae," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(oe," .").concat(oe," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(ae," .").concat(ae," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Ce,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},Re=function(){var e=parseInt(document.body.getAttribute(Ce)||"0",10);return isFinite(e)?e:0},Oe=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,a=void 0===o?"margin":o;e.useEffect((function(){return document.body.setAttribute(Ce,(Re()+1).toString()),function(){var e=Re()-1;e<=0?document.body.removeAttribute(Ce):document.body.setAttribute(Ce,e.toString())}}),[]);var i=e.useMemo((function(){return function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Ee;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[be(n),be(r),be(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}}(a)}),[a]);return e.createElement(we,{styles:Ne(i,!n,a,r?"":"!important")})},xe=!1;if("undefined"!=typeof window)try{var Se=Object.defineProperty({},"passive",{get:function(){return xe=!0,!0}});window.addEventListener("test",Se,Se),window.removeEventListener("test",Se,Se)}catch(At){xe=!1}var De=!!xe&&{passive:!1},Pe=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},_e=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Te(e,r)){var o=Me(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Te=function(e,t){return"v"===e?function(e){return Pe(e,"overflowY")}(t):function(e){return Pe(e,"overflowX")}(t)},Me=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Le=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ae=function(e){return[e.deltaX,e.deltaY]},je=function(e){return e&&"current"in e?e.current:e},ke=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Ie=0,Fe=[];function We(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Be=pe(me,(function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),a=e.useState(Ie++)[0],i=e.useState(ye)[0],c=e.useRef(t);e.useEffect((function(){c.current=t}),[t]),e.useEffect((function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(a));var e=re([t.lockRef.current],(t.shards||[]).map(je),!0).filter(Boolean);return e.forEach((function(e){return e.classList.add("allow-interactivity-".concat(a))})),function(){document.body.classList.remove("block-interactivity-".concat(a)),e.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(a))}))}}}),[t.inert,t.lockRef.current,t.shards]);var u=e.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var n,a=Le(e),i=r.current,u="deltaX"in e?e.deltaX:i[0]-a[0],s="deltaY"in e?e.deltaY:i[1]-a[1],l=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===l.type)return!1;var f=_e(d,l);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=_e(d,l)),!f)return!1;if(!o.current&&"changedTouches"in e&&(u||s)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r){var o=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),a=o*r,i=n.target,c=t.contains(i),u=!1,s=a>0,l=0,d=0;do{var f=Me(e,i),p=f[0],m=f[1]-f[2]-o*p;(p||m)&&Te(e,i)&&(l+=m,d+=p),i=i instanceof ShadowRoot?i.host:i.parentNode}while(!c&&i!==document.body||c&&(t.contains(i)||t===i));return(s&&Math.abs(l)<1||!s&&Math.abs(d)<1)&&(u=!0),u}(p,t,e,"h"===p?u:s)}),[]),s=e.useCallback((function(e){var t=e;if(Fe.length&&Fe[Fe.length-1]===i){var r="deltaY"in t?Ae(t):Le(t),o=n.current.filter((function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o}))[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var a=(c.current.shards||[]).map(je).filter(Boolean).filter((function(e){return e.contains(t.target)}));(a.length>0?u(t,a[0]):!c.current.noIsolation)&&t.cancelable&&t.preventDefault()}}}),[]),l=e.useCallback((function(e,t,r,o){var a={name:e,delta:t,target:r,should:o,shadowParent:We(r)};n.current.push(a),setTimeout((function(){n.current=n.current.filter((function(e){return e!==a}))}),1)}),[]),d=e.useCallback((function(e){r.current=Le(e),o.current=void 0}),[]),f=e.useCallback((function(e){l(e.type,Ae(e),e.target,u(e,t.lockRef.current))}),[]),p=e.useCallback((function(e){l(e.type,Le(e),e.target,u(e,t.lockRef.current))}),[]);e.useEffect((function(){return Fe.push(i),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,De),document.addEventListener("touchmove",s,De),document.addEventListener("touchstart",d,De),function(){Fe=Fe.filter((function(e){return e!==i})),document.removeEventListener("wheel",s,De),document.removeEventListener("touchmove",s,De),document.removeEventListener("touchstart",d,De)}}),[]);var m=t.removeScrollBar,v=t.inert;return e.createElement(e.Fragment,null,v?e.createElement(i,{styles:ke(a)}):null,m?e.createElement(Oe,{gapMode:t.gapMode}):null)}));var Ue=e.forwardRef((function(t,n){return e.createElement(he,te({},t,{ref:n,sideCar:Be}))}));Ue.classNames=he.classNames;var $e=new WeakMap,Ke=new WeakMap,Ye={},ze=0,Xe=function(e){return e&&(e.host||Xe(e.parentNode))},Ve=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=Xe(t);return n&&e.contains(n)?n:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);Ye[n]||(Ye[n]=new WeakMap);var a=Ye[n],i=[],c=new Set,u=new Set(o),s=function(e){e&&!c.has(e)&&(c.add(e),s(e.parentNode))};o.forEach(s);var l=function(e){e&&!u.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(c.has(e))l(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=($e.get(e)||0)+1,s=(a.get(e)||0)+1;$e.set(e,u),a.set(e,s),i.push(e),1===u&&o&&Ke.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}}))};return l(t),c.clear(),ze++,function(){i.forEach((function(e){var t=$e.get(e)-1,o=a.get(e)-1;$e.set(e,t),a.set(e,o),t||(Ke.has(e)||e.removeAttribute(r),Ke.delete(e)),o||e.removeAttribute(n)})),--ze||($e=new WeakMap,$e=new WeakMap,Ke=new WeakMap,Ye={})}}(r,o,n,"aria-hidden")):function(){return null}},Ze="Dialog",[qe,He]=g(Ze),[Ge,Je]=qe(Ze),Qe=t=>{const{__scopeDialog:n,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=t,u=e.useRef(null),s=e.useRef(null),[l=!1,d]=R({prop:o,defaultProp:a,onChange:i});return p.jsx(Ge,{scope:n,triggerRef:u,contentRef:s,contentId:C(),titleId:C(),descriptionId:C(),open:l,onOpenChange:d,onOpenToggle:e.useCallback((()=>d((e=>!e))),[d]),modal:c,children:r})};Qe.displayName=Ze;var et="DialogTrigger",tt=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Je(et,n),a=h(t,o.triggerRef);return p.jsx(_.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":bt(o.open),...r,ref:a,onClick:m(e.onClick,o.onOpenToggle)})}));tt.displayName=et;var nt="DialogPortal",[rt,ot]=qe(nt,{forceMount:void 0}),at=t=>{const{__scopeDialog:n,forceMount:r,children:o,container:a}=t,i=Je(nt,n);return p.jsx(rt,{scope:n,forceMount:r,children:e.Children.map(o,(e=>p.jsx(H,{present:r||i.open,children:p.jsx(q,{asChild:!0,container:a,children:e})})))})};at.displayName=nt;var it="DialogOverlay",ct=e.forwardRef(((e,t)=>{const n=ot(it,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Je(it,e.__scopeDialog);return a.modal?p.jsx(H,{present:r||a.open,children:p.jsx(ut,{...o,ref:t})}):null}));ct.displayName=it;var ut=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Je(it,n);return p.jsx(Ue,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:p.jsx(_.div,{"data-state":bt(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})})),st="DialogContent",lt=e.forwardRef(((e,t)=>{const n=ot(st,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Je(st,e.__scopeDialog);return p.jsx(H,{present:r||a.open,children:a.modal?p.jsx(dt,{...o,ref:t}):p.jsx(ft,{...o,ref:t})})}));lt.displayName=st;var dt=e.forwardRef(((t,n)=>{const r=Je(st,t.__scopeDialog),o=e.useRef(null),a=h(n,r.contentRef,o);return e.useEffect((()=>{const e=o.current;if(e)return Ve(e)}),[]),p.jsx(pt,{...t,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:m(t.onCloseAutoFocus,(e=>{e.preventDefault(),r.triggerRef.current?.focus()})),onPointerDownOutside:m(t.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:m(t.onFocusOutside,(e=>e.preventDefault()))})})),ft=e.forwardRef(((t,n)=>{const r=Je(st,t.__scopeDialog),o=e.useRef(!1),a=e.useRef(!1);return p.jsx(pt,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(o.current||r.triggerRef.current?.focus(),e.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(a.current=!0));const n=e.target,i=r.triggerRef.current?.contains(n);i&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&a.current&&e.preventDefault()}})})),pt=e.forwardRef(((t,n)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...c}=t,u=Je(st,r),s=e.useRef(null),l=h(n,s);return Q(),p.jsxs(p.Fragment,{children:[p.jsx($,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:p.jsx(k,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":bt(u.open),...c,ref:l,onDismiss:()=>u.onOpenChange(!1)})}),p.jsxs(p.Fragment,{children:[p.jsx(Rt,{titleId:u.titleId}),p.jsx(Ot,{contentRef:s,descriptionId:u.descriptionId})]})]})})),mt="DialogTitle",vt=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Je(mt,n);return p.jsx(_.h2,{id:o.titleId,...r,ref:t})}));vt.displayName=mt;var ht="DialogDescription",gt=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Je(ht,n);return p.jsx(_.p,{id:o.descriptionId,...r,ref:t})}));gt.displayName=ht;var yt="DialogClose",Et=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Je(yt,n);return p.jsx(_.button,{type:"button",...r,ref:t,onClick:m(e.onClick,(()=>o.onOpenChange(!1)))})}));function bt(e){return e?"open":"closed"}Et.displayName=yt;var wt="DialogTitleWarning",[Ct,Nt]=function(t,n){const r=e.createContext(n),o=t=>{const{children:n,...o}=t,a=e.useMemo((()=>o),Object.values(o));return p.jsx(r.Provider,{value:a,children:n})};return o.displayName=t+"Provider",[o,function(o){const a=e.useContext(r);if(a)return a;if(void 0!==n)return n;throw new Error(`\`${o}\` must be used within \`${t}\``)}]}(wt,{contentName:st,titleName:mt,docsSlug:"dialog"}),Rt=({titleId:t})=>{const n=Nt(wt),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect((()=>{t&&document.getElementById(t)}),[r,t]),null},Ot=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Nt("DialogDescriptionWarning").contentName}}.`;return e.useEffect((()=>{const e=t.current?.getAttribute("aria-describedby");n&&e&&document.getElementById(n)}),[r,t,n]),null},xt=Qe,St=tt,Dt=at,Pt=ct,_t=lt,Tt=vt,Mt=gt,Lt=Et;export{ye as A,re as B,_t as C,Mt as D,Oe as E,$ as F,pe as G,Pt as O,_ as P,Ue as R,O as S,St as T,Ct as W,ne as _,C as a,R as b,m as c,N as d,E as e,M as f,T as g,g as h,v as i,p as j,H as k,q as l,Q as m,Ve as n,He as o,xt as p,Dt as q,S as r,Tt as s,Lt as t,h as u,de as v,se as w,te as x,ae as y,oe as z};
