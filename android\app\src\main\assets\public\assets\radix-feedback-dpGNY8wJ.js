import{r as e}from"./critical-DVX9Inzy.js";import{j as t,P as n,o as r,p as o,T as s,q as a,u as i,W as c,h as l,C as u,c as d,r as p,s as f,D as v,t as m,O as g,f as h,d as y,g as x,a as b,b as w,k as E}from"./radix-core-6kBL75b5.js";import{c as C,R as D,A as T,C as _,a as P}from"./radix-interactive-DJo-0Sg_.js";import{R}from"./radix-toast-1_gbKn9f.js";function j(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var O="Progress",[L,N]=function(n,r=[]){let o=[];const s=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return s.scopeName=n,[function(r,s){const a=e.createContext(s),i=o.length;function c(r){const{scope:o,children:s,...c}=r,l=o?.[n][i]||a,u=e.useMemo((()=>c),Object.values(c));return t.jsx(l.Provider,{value:u,children:s})}return o=[...o,s],c.displayName=r+"Provider",[c,function(t,o){const c=o?.[n][i]||a,l=e.useContext(c);if(l)return l;if(void 0!==s)return s;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},j(s,...r)]}(O),[A,k]=L(O),I=e.forwardRef(((e,r)=>{const{__scopeProgress:o,value:s=null,max:a,getValueLabel:i=F,...c}=e;(a||0===a)&&W(a);const l=W(a)?a:100;null!==s&&H(s,l);const u=H(s,l)?s:null,d=$(u)?i(u,l):void 0;return t.jsx(A,{scope:o,value:u,max:l,children:t.jsx(n.div,{"aria-valuemax":l,"aria-valuemin":0,"aria-valuenow":$(u)?u:void 0,"aria-valuetext":d,role:"progressbar","data-state":B(u,l),"data-value":u??void 0,"data-max":l,...c,ref:r})})}));I.displayName=O;var M="ProgressIndicator",S=e.forwardRef(((e,r)=>{const{__scopeProgress:o,...s}=e,a=k(M,o);return t.jsx(n.div,{"data-state":B(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...s,ref:r})}));function F(e,t){return`${Math.round(e/t*100)}%`}function B(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function $(e){return"number"==typeof e}function W(e){return $(e)&&!isNaN(e)&&e>0}function H(e,t){return $(e)&&!isNaN(e)&&e<=t&&e>=0}S.displayName=M;var z=I,K=S,Y="AlertDialog",[q,X]=l(Y,[r]),V=r(),G=e=>{const{__scopeAlertDialog:n,...r}=e,s=V(n);return t.jsx(o,{...s,...r,modal:!0})};G.displayName=Y;var J=e.forwardRef(((e,n)=>{const{__scopeAlertDialog:r,...o}=e,a=V(r);return t.jsx(s,{...a,...o,ref:n})}));J.displayName="AlertDialogTrigger";var Q=e=>{const{__scopeAlertDialog:n,...r}=e,o=V(n);return t.jsx(a,{...o,...r})};Q.displayName="AlertDialogPortal";var U=e.forwardRef(((e,n)=>{const{__scopeAlertDialog:r,...o}=e,s=V(r);return t.jsx(g,{...s,...o,ref:n})}));U.displayName="AlertDialogOverlay";var Z="AlertDialogContent",[ee,te]=q(Z),ne=e.forwardRef(((n,r)=>{const{__scopeAlertDialog:o,children:s,...a}=n,l=V(o),f=e.useRef(null),v=i(r,f),m=e.useRef(null);return t.jsx(c,{contentName:Z,titleName:re,docsSlug:"alert-dialog",children:t.jsx(ee,{scope:o,cancelRef:m,children:t.jsxs(u,{role:"alertdialog",...l,...a,ref:v,onOpenAutoFocus:d(a.onOpenAutoFocus,(e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})})),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[t.jsx(p,{children:s}),t.jsx(de,{contentRef:f})]})})})}));ne.displayName=Z;var re="AlertDialogTitle",oe=e.forwardRef(((e,n)=>{const{__scopeAlertDialog:r,...o}=e,s=V(r);return t.jsx(f,{...s,...o,ref:n})}));oe.displayName=re;var se="AlertDialogDescription",ae=e.forwardRef(((e,n)=>{const{__scopeAlertDialog:r,...o}=e,s=V(r);return t.jsx(v,{...s,...o,ref:n})}));ae.displayName=se;var ie=e.forwardRef(((e,n)=>{const{__scopeAlertDialog:r,...o}=e,s=V(r);return t.jsx(m,{...s,...o,ref:n})}));ie.displayName="AlertDialogAction";var ce="AlertDialogCancel",le=e.forwardRef(((e,n)=>{const{__scopeAlertDialog:r,...o}=e,{cancelRef:s}=te(ce,r),a=V(r),c=i(n,s);return t.jsx(m,{...a,...o,ref:c})}));le.displayName=ce;var ue,de=({contentRef:t})=>{const n=`\`${Z}\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \`${Z}\` by passing a \`${se}\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Z}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return e.useEffect((()=>{document.getElementById(t.current?.getAttribute("aria-describedby"))}),[n,t]),null},pe=G,fe=J,ve=Q,me=U,ge=ne,he=ie,ye=le,xe=oe,be=ae,we="dismissableLayer.update",Ee=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ce=e.forwardRef(((r,o)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:l,onInteractOutside:u,onDismiss:p,...f}=r,v=e.useContext(Ee),[m,g]=e.useState(null),x=m?.ownerDocument??globalThis?.document,[,b]=e.useState({}),w=i(o,(e=>g(e))),E=Array.from(v.layers),[C]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),D=E.indexOf(C),T=m?E.indexOf(m):-1,_=v.layersWithOutsidePointerEventsDisabled.size>0,P=T>=D,R=function(t,n=globalThis?.document){const r=y(t),o=e.useRef(!1),s=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){Te("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...v.branches].some((e=>e.contains(t)));P&&!n&&(c?.(e),u?.(e),e.defaultPrevented||p?.())}),x),j=function(t,n=globalThis?.document){const r=y(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{e.target&&!o.current&&Te("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...v.branches].some((e=>e.contains(t)))||(l?.(e),u?.(e),e.defaultPrevented||p?.())}),x);return h((e=>{T===v.layers.size-1&&(a?.(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))}),x),e.useEffect((()=>{if(m)return s&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(ue=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(m)),v.layers.add(m),De(),()=>{s&&1===v.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=ue)}}),[m,x,s,v]),e.useEffect((()=>()=>{m&&(v.layers.delete(m),v.layersWithOutsidePointerEventsDisabled.delete(m),De())}),[m,v]),e.useEffect((()=>{const e=()=>b({});return document.addEventListener(we,e),()=>document.removeEventListener(we,e)}),[]),t.jsx(n.div,{...f,ref:w,style:{pointerEvents:_?P?"auto":"none":void 0,...r.style},onFocusCapture:d(r.onFocusCapture,j.onFocusCapture),onBlurCapture:d(r.onBlurCapture,j.onBlurCapture),onPointerDownCapture:d(r.onPointerDownCapture,R.onPointerDownCapture)})}));function De(){const e=new CustomEvent(we);document.dispatchEvent(e)}function Te(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?x(o,s):o.dispatchEvent(s)}Ce.displayName="DismissableLayer",e.forwardRef(((r,o)=>{const s=e.useContext(Ee),a=e.useRef(null),c=i(o,a);return e.useEffect((()=>{const e=a.current;if(e)return s.branches.add(e),()=>{s.branches.delete(e)}}),[s.branches]),t.jsx(n.div,{...r,ref:c})})).displayName="DismissableLayerBranch";var[_e,Pe]=l("Tooltip",[C]),Re=C(),je="TooltipProvider",Oe=700,Le="tooltip.open",[Ne,Ae]=_e(je),ke=n=>{const{__scopeTooltip:r,delayDuration:o=Oe,skipDelayDuration:s=300,disableHoverableContent:a=!1,children:i}=n,[c,l]=e.useState(!0),u=e.useRef(!1),d=e.useRef(0);return e.useEffect((()=>{const e=d.current;return()=>window.clearTimeout(e)}),[]),t.jsx(Ne,{scope:r,isOpenDelayed:c,delayDuration:o,onOpen:e.useCallback((()=>{window.clearTimeout(d.current),l(!1)}),[]),onClose:e.useCallback((()=>{window.clearTimeout(d.current),d.current=window.setTimeout((()=>l(!0)),s)}),[s]),isPointerInTransitRef:u,onPointerInTransitChange:e.useCallback((e=>{u.current=e}),[]),disableHoverableContent:a,children:i})};ke.displayName=je;var Ie="Tooltip",[Me,Se]=_e(Ie),Fe=n=>{const{__scopeTooltip:r,children:o,open:s,defaultOpen:a=!1,onOpenChange:i,disableHoverableContent:c,delayDuration:l}=n,u=Ae(Ie,n.__scopeTooltip),d=Re(r),[p,f]=e.useState(null),v=b(),m=e.useRef(0),g=c??u.disableHoverableContent,h=l??u.delayDuration,y=e.useRef(!1),[x=!1,E]=w({prop:s,defaultProp:a,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(Le))):u.onClose(),i?.(e)}}),C=e.useMemo((()=>x?y.current?"delayed-open":"instant-open":"closed"),[x]),T=e.useCallback((()=>{window.clearTimeout(m.current),y.current=!1,E(!0)}),[E]),_=e.useCallback((()=>{window.clearTimeout(m.current),E(!1)}),[E]),P=e.useCallback((()=>{window.clearTimeout(m.current),m.current=window.setTimeout((()=>{y.current=!0,E(!0)}),h)}),[h,E]);return e.useEffect((()=>()=>window.clearTimeout(m.current)),[]),t.jsx(D,{...d,children:t.jsx(Me,{scope:r,contentId:v,open:x,stateAttribute:C,trigger:p,onTriggerChange:f,onTriggerEnter:e.useCallback((()=>{u.isOpenDelayed?P():T()}),[u.isOpenDelayed,P,T]),onTriggerLeave:e.useCallback((()=>{g?_():window.clearTimeout(m.current)}),[_,g]),onOpen:T,onClose:_,disableHoverableContent:g,children:o})})};Fe.displayName=Ie;var Be="TooltipTrigger",$e=e.forwardRef(((r,o)=>{const{__scopeTooltip:s,...a}=r,c=Se(Be,s),l=Ae(Be,s),u=Re(s),p=e.useRef(null),f=i(o,p,c.onTriggerChange),v=e.useRef(!1),m=e.useRef(!1),g=e.useCallback((()=>v.current=!1),[]);return e.useEffect((()=>()=>document.removeEventListener("pointerup",g)),[g]),t.jsx(T,{asChild:!0,...u,children:t.jsx(n.button,{"aria-describedby":c.open?c.contentId:void 0,"data-state":c.stateAttribute,...a,ref:f,onPointerMove:d(r.onPointerMove,(e=>{"touch"!==e.pointerType&&(m.current||l.isPointerInTransitRef.current||(c.onTriggerEnter(),m.current=!0))})),onPointerLeave:d(r.onPointerLeave,(()=>{c.onTriggerLeave(),m.current=!1})),onPointerDown:d(r.onPointerDown,(()=>{v.current=!0,document.addEventListener("pointerup",g,{once:!0})})),onFocus:d(r.onFocus,(()=>{v.current||c.onOpen()})),onBlur:d(r.onBlur,c.onClose),onClick:d(r.onClick,c.onClose)})})}));$e.displayName=Be;var[We,He]=_e("TooltipPortal",{forceMount:void 0}),ze="TooltipContent",Ke=e.forwardRef(((e,n)=>{const r=He(ze,e.__scopeTooltip),{forceMount:o=r.forceMount,side:s="top",...a}=e,i=Se(ze,e.__scopeTooltip);return t.jsx(E,{present:o||i.open,children:i.disableHoverableContent?t.jsx(Ve,{side:s,...a,ref:n}):t.jsx(Ye,{side:s,...a,ref:n})})})),Ye=e.forwardRef(((n,r)=>{const o=Se(ze,n.__scopeTooltip),s=Ae(ze,n.__scopeTooltip),a=e.useRef(null),c=i(r,a),[l,u]=e.useState(null),{trigger:d,onClose:p}=o,f=a.current,{onPointerInTransitChange:v}=s,m=e.useCallback((()=>{u(null),v(!1)}),[v]),g=e.useCallback(((e,t)=>{const n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,function(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}(r,n.getBoundingClientRect())),s=function(e){const t=e.slice();return t.sort(((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0)),function(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const n=e[r];for(;t.length>=2;){const e=t[t.length-1],r=t[t.length-2];if(!((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x)))break;t.pop()}t.push(n)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const t=e[r];for(;n.length>=2;){const e=n[n.length-1],r=n[n.length-2];if(!((e.x-r.x)*(t.y-r.y)>=(e.y-r.y)*(t.x-r.x)))break;n.pop()}n.push(t)}return n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}(t)}([...o,...function(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())]);u(s),v(!0)}),[v]);return e.useEffect((()=>()=>m()),[m]),e.useEffect((()=>{if(d&&f){const e=e=>g(e,f),t=e=>g(e,d);return d.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}}),[d,f,g,m]),e.useEffect((()=>{if(l){const e=e=>{const t=e.target,n={x:e.clientX,y:e.clientY},r=d?.contains(t)||f?.contains(t),o=!function(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const e=t[s].x,i=t[s].y,c=t[a].x,l=t[a].y;i>r!=l>r&&n<(c-e)*(r-i)/(l-i)+e&&(o=!o)}return o}(n,l);r?m():o&&(m(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}}),[d,f,l,p,m]),t.jsx(Ve,{...n,ref:c})})),[qe,Xe]=_e(Ie,{isInside:!1}),Ve=e.forwardRef(((n,r)=>{const{__scopeTooltip:o,children:s,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:c,...l}=n,u=Se(ze,o),d=Re(o),{onClose:f}=u;return e.useEffect((()=>(document.addEventListener(Le,f),()=>document.removeEventListener(Le,f))),[f]),e.useEffect((()=>{if(u.trigger){const e=e=>{const t=e.target;t?.contains(u.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}}),[u.trigger,f]),t.jsx(Ce,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:t.jsxs(_,{"data-state":u.stateAttribute,...d,...l,ref:r,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[t.jsx(p,{children:s}),t.jsx(qe,{scope:o,isInside:!0,children:t.jsx(R,{id:u.contentId,role:"tooltip",children:a||s})})]})})}));Ke.displayName=ze;var Ge="TooltipArrow";e.forwardRef(((e,n)=>{const{__scopeTooltip:r,...o}=e,s=Re(r);return Xe(Ge,r).isInside?null:t.jsx(P,{...s,...o,ref:n})})).displayName=Ge;var Je=ke,Qe=Fe,Ue=$e,Ze=Ke;export{he as A,ge as C,be as D,K as I,me as O,ve as P,z as R,fe as T,pe as a,xe as b,ye as c,Je as d,Qe as e,Ue as f,Ze as g};
