const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-BGK5KRQu.js","assets/radix-core-6kBL75b5.js","assets/critical-DVX9Inzy.js","assets/query-vendor-B-7l6Nb3.js","assets/supabase-vendor-qi_Ptfv-.js","assets/router-BAzpOxbo.js","assets/form-vendor-rYZw_ur7.js","assets/radix-forms-DX-owj97.js","assets/radix-interactive-DJo-0Sg_.js","assets/radix-toast-1_gbKn9f.js","assets/radix-feedback-dpGNY8wJ.js","assets/radix-popover-DQqTw7_-.js","assets/radix-layout-CC8mXA4O.js","assets/web-9Bdt6ipZ.js","assets/web-CoE2gwkD.js","assets/SearchResults-Sp4oVNId.js","assets/zap-3JoB1_vc.js","assets/bot-D7hKYYUH.js","assets/book-open-JvVCwLVv.js","assets/scroll-text-b5UnMmWF.js","assets/Footer-CliqTtAT.js","assets/FeedbackTrigger-CnktnpVq.js","assets/rocket-BrzzCnNA.js","assets/target-jx5HoNTw.js","assets/star-DPJhgPy1.js","assets/circle-help-Bt3O9hrS.js","assets/instagram-BUkbqcNN.js","assets/UnifiedContentBadge-DLSfq1Of.js","assets/useNewsletters-CGMWyKlT.js","assets/newspaper-BNnmghGn.js","assets/droplets-CMLZo5v8.js","assets/external-link-B-0JBg3h.js","assets/date-vendor-BOcTQe0E.js","assets/pt-BR-a_BmBHfW.js","assets/SiteFeedbackDialog-DMGhaCys.js","assets/AndroidInstallDialog-MwErltrM.js","assets/download-DW9jWE4p.js","assets/MobileThemeToggle-GvGSjv_g.js","assets/FloatingSupport-Oq_WGoBV.js","assets/FloatingChatButton-FeuZhjTw.js","assets/secureStorage-eSV3TdjH.js","assets/zoom-out-CYjedn6d.js","assets/chevron-left-CUbzR6vp.js","assets/index-EkgUeWqU.js","assets/plus-7ON-wNau.js","assets/trash-2-DldO7Ekc.js","assets/send-fFyas2dT.js","assets/MobileBottomNav-YFjm8vPh.js","assets/use-mobile-DZ3cxmhN.js","assets/house-BhFW6iO4.js","assets/user-B_6Qs7t0.js","assets/ScrollToTop-BkeZpmBK.js","assets/BackButtonHandler-heuPxz_v.js","assets/ChunkVersionChecker-BboIJXad.js","assets/SafeAreaProvider-CuOtsltL.js","assets/SafeAreaDebugger-CV2eWuH6.js","assets/GlobalDebugger-u8JiIVCZ.js","assets/bug-Cvma6Un5.js","assets/Medications-DBps0_8T.js","assets/MedicationDetails-hugfMUoS.js","assets/useWeight-CatlFLFx.js","assets/useAge-C_36_Zbj.js","assets/collapsible-B6HfSnGs.js","assets/accordion-DlrGn5T8.js","assets/PatientInfoSection-Dti7R4KL.js","assets/scale-C0VY02RJ.js","assets/useAgeInput-CLfpowYq.js","assets/calendar-Bey2EULq.js","assets/DosageDisplay-CuTo0RSr.js","assets/alert-B4jfnKuF.js","assets/lightbulb-BNaGkaNP.js","assets/stethoscope-C94gb3Zp.js","assets/syringe-CfMhlReq.js","assets/wind-Di0Li-OL.js","assets/ResetPassword-CV6_uFE2.js","assets/ProfessionalInstructions-BUKTQtaf.js","assets/filter-DbEB3AI7.js","assets/MedicationInstructionPage-CtA2KBEr.js","assets/alert-dialog-DUTJqGTa.js","assets/thumbs-up-DQ3oKVo3.js","assets/meh-C1kkAfhD.js","assets/thumbs-down-tXhNbRGw.js","assets/list-Ba9MmWYl.js","assets/QuestionImport-CBu5Sm3f.js","assets/QuestionImport-00QgD_dZ.js","assets/QuestionFormatting-CLp72BiD.js","assets/refresh-cw-CkZd8sz4.js","assets/circle-check-DMeZ92pY.js","assets/arrow-down-ByQAWSep.js","assets/MedicationImport-DL2XKwef.js","assets/index-k36xMONP.js","assets/upload-DHMZMPS5.js","assets/database-O6QBRcB0.js","assets/FormatThemes-6hU7acDC.js","assets/separator-Fn-o4a8E.js","assets/Dashboard-Cj4g3K2V.js","assets/layout-dashboard-CFW423ZE.js","assets/wrench-BSxltnlI.js","assets/bookmark-CgtQ1BKb.js","assets/beaker-BvOT41Qo.js","assets/tag-NUSzLqhK.js","assets/chart-column-Bx6HktL4.js","assets/file-question-BFIq6IV5.js","assets/brain-circuit-DmXC3met.js","assets/milk-VQnJxZ5N.js","assets/DrugInteractionMedications-DXNpm_VD.js","assets/table-BxTxa34h.js","assets/copy-QoO_C4a_.js","assets/BreastfeedingMedications-DWqzMMD3.js","assets/BreastfeedingMedicationsEnhancement-CW7qorIZ.js","assets/MedicationImprovement-0WvR8e25.js","assets/CasDcbValidation-DSxrsKym.js","assets/FeedbackManagement-C7iIQVNw.js","assets/clock-BJRszb4U.js","assets/trending-up-BvXgph-L.js","assets/users-BYtb98PE.js","assets/dollar-sign-Bo0Lkile.js","assets/ActiveIngredientsFormatter-DoW-2kr-.js","assets/building-2-CfNBjYaY.js","assets/save-CD2-jHED.js","assets/rotate-ccw-C0gOgepD.js","assets/DrWillControl-Bjvco9oY.js","assets/switch-Bs48sy8W.js","assets/Anamnese-CylWTg9c.js","assets/tooltip-MkmOFH7B.js","assets/Blog-CdyEyNsR.js","assets/readingTime-CCa0LV7y.js","assets/BlogPost-j86Vh1w0.js","assets/skeleton-C3Q47K9g.js","assets/Prescriptions-p33ePdo_.js","assets/clipboard-list-BexXoRpK.js","assets/PrescriptionStatusIndicator-DWTWvet0.js","assets/pencil-B9pAnueM.js","assets/SharedPrescriptions-XOpXQ8Fu.js","assets/eye-C75tDsJP.js","assets/ICD-Q47aKdlH.js","assets/Childcare-hh7reYAq.js","assets/childcareSEOData-BmQIGg35.js","assets/chart-line-DnBXrGOP.js","assets/pill-bottle-DsqYsEID.js","assets/GrowthCurves-CHqCLi1R.js","assets/slider-DwBUPagM.js","assets/pdf-vendor-C6iMwFa1.js","assets/Vaccines-CEYiUDsR.js","assets/MarkdownRenderer-BYKj7L5I.js","assets/index-BzIohbsX.js","assets/markdown-vendor-C57yw7YK.js","assets/index-Bf1cTgQT.js","assets/Formulas-wQSDpV4V.js","assets/Supplementation-WaS5EKGi.js","assets/supplementationCalculator-5FZSR-ld.js","assets/PatientOverview-DQO8pyLl.js","assets/ruler--K9LRw-Q.js","assets/carousel-vendor-BSJaAqXc.js","assets/DNPM-CE7TePoE.js","assets/AIAssistant-CGfZ6w0_.js","assets/Calculators-DDhgHVAh.js","assets/FinneganCalculator-HjPw5m_P.js","assets/calculatorSEOData-DV731ymA.js","assets/ApgarCalculator-CPWw-j63.js","assets/RodwellCalculator-DENNFKE7.js","assets/CapurroCalculator-DKbNj-yg.js","assets/CapurroNeuroCalculator-sYIzNJ-W.js","assets/GINACalculator-DmPpgBsQ.js","assets/GlasgowCalculator-DVf5reei.js","assets/BMICalculator-U_UP70fT.js","assets/BhutaniCalculator-CLqasPOr.js","assets/chart-js-B1VMWqNt.js","assets/SRICalculator-DTqQUEti.js","assets/Layout-BVScLwHe.js","assets/circle-plus-Cmsb2ZCv.js","assets/Categories-CSjE-QGy.js","assets/Medications-DrjAT4bE.js","assets/slugify-t5oqNdxM.js","assets/DosageList-Cysh0LTu.js","assets/Dosages-CFehfD9h.js","assets/PrescriptionCategories-DHOrXbGG.js","assets/ICD10--iwu_O2S.js","assets/GrowthCurves-tYahrJQf.js","assets/VaccineManagement-CERQpMb9.js","assets/Formulas-BJ6v8FcP.js","assets/DNPM-DvI-NHXm.js","assets/imageOptimization-Di_XtiVZ.js","assets/LazyImage-DkEFVdej.js","assets/ProblemsDebug-rKmkuiTq.js","assets/Blog-DNw7jPHu.js","assets/editor-vendor-0G6QaH11.js","assets/italic-CyRywz9O.js","assets/underline-CsR6GSfr.js","assets/list-ordered-c6qMIUiO.js","assets/square-pen-BYRvTkvV.js","assets/trash-DlkW2QJ2.js","assets/AdminRoute-BuC7xD9a.js","assets/Settings-A7Xh4-50.js","assets/Terms-Chn-5L-U.js","assets/Flowcharts-CCG-8Crp.js","assets/DengueFlowchart-CR3wRWJP.js","assets/flowchartSEOData-ChHaL6QZ.js","assets/DKAFlowchart-DHOCM52y.js","assets/AnaphylaxisFlowchart-BGFhIKDN.js","assets/AsthmaFlowchart-D7KjhsAu.js","assets/SeizureFlowchart-Eiy3UOuy.js","assets/play-BsgwpV8Q.js","assets/PecarnFlowchart-B3MclrT-.js","assets/VenomousAnimalsFlowchart-DAdJS11q.js","assets/ScorpionFlowchart-D_TvGXg7.js","assets/BothropicFlowchart-Cbac_B9l.js","assets/CrotalicFlowchart-CbZdgwOs.js","assets/ElapidicFlowchart-CXKblxMi.js","assets/state-vendor-DwPaWbBF.js","assets/octagon-alert-BtbDiJC0.js","assets/PhoneutriaFlowchart-CnnWeEaF.js","assets/LoxoscelicFlowchart--GHdvPdS.js","assets/Poisonings-DWHdbvq8.js","assets/poisoningSEOData-DhomWJJk.js","assets/PoisoningDetails-B-w6AZMx.js","assets/Notes-B3en7yks.js","assets/folder-BFmlNLtU.js","assets/wand-sparkles-vSaENpjL.js","assets/timer-DVjd26io.js","assets/WhatsAppBot-C1rodjD5.js","assets/DrWill-CexrI4Cx.js","assets/useUserData-Cw5TUSG9.js","assets/DrugInteractions-CtOPeunc.js","assets/v4-OjsI5tD8.js","assets/Newsletters-Qa3p8gOQ.js","assets/PediDrop-5-btotAG.js","assets/MarkdownEditor-CJDENG18.js","assets/PediDropAdmin-DnNOSS6L.js","assets/SiteSettings-0oYbi9oz.js","assets/Settings-BZrvAiMy.js","assets/HydrationCalculator-2y6fevWw.js","assets/ConductsAndManagement-DohkTTxv.js","assets/dnd-vendor-DTrLYOhb.js","assets/link-D9t_Ainp.js","assets/ConductsAndManagementNew-Dsx7LWIW.js","assets/ConductsRouterNew-20d7jGLs.js","assets/folder-open-DiriOFrY.js","assets/ProtectedRoute-D3wxem2q.js","assets/AdminUsers-Jzx8keOi.js","assets/MaintenancePage-DuHcjHkl.js","assets/TestQuestions-Caj2tKdZ.js","assets/QuestionCard-DpUoKTi2.js","assets/ensureUserId-D0cz5H4C.js","assets/PediatricStudyIntro-BontGhFz.js","assets/PediatricStudy-DGXqWgHq.js","assets/Questions-9D-MbuAL.js","assets/Results-Jnb9qHdg.js","assets/award-CVLURNww.js","assets/MedicationInstructions-CizQajPi.js","assets/PrivacyPolicy-D-hoo44x.js","assets/MedicationsBreastfeeding-znCiG_YS.js","assets/MedicationLeaflet-BNF73wNA.js","assets/Maintenance-BcnYZ10m.js","assets/ResidencyBeta-ROUQGqMG.js","assets/AuthCallback-CWT5HD43.js"])))=>i.map(i=>d[i]);
var e,t,r=Object.defineProperty,s=(e,t,s)=>((e,t,s)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s)(e,"symbol"!=typeof t?t+"":t,s);import{j as a,O as n,q as i,C as o,t as l,s as c,D as d,p as u,T as m,S as h,v as p,_ as f,w as g,x,z as y,y as v,A as b,B as w,E as j,G as k,n as E,h as A,P as C,d as _,e as N}from"./radix-core-6kBL75b5.js";import{a as S,r as T,d as P,b as D,$ as R,R as M}from"./critical-DVX9Inzy.js";import{u as I,a as L,Q as O,b as F}from"./query-vendor-B-7l6Nb3.js";import{c as V,d as z,_ as B}from"./supabase-vendor-qi_Ptfv-.js";import{u as $,a as U,L as q,B as W,R as H,b as G,N as K}from"./router-BAzpOxbo.js";import{o as Y,r as Q,a as X,u as J,F as Z,C as ee,b as te,s as re,c as se,d as ae,e as ne}from"./form-vendor-rYZw_ur7.js";import{R as ie,a as oe,I as le,b as ce,d as de,e as ue,T as me,f as he,S as pe,g as fe,P as ge,C as xe,V as ye,L as ve,h as be,i as we,j as je,k as ke,l as Ee,m as Ae,G as Ce}from"./radix-forms-DX-owj97.js";import{R as _e,I as Ne}from"./radix-feedback-dpGNY8wJ.js";import{P as Se,C as Te,R as Pe,T as De}from"./radix-popover-DQqTw7_-.js";import{S as Re,f as Me,P as Ie,g as Le,h as Oe,i as Fe,j as Ve,k as ze,L as Be,l as $e,m as Ue,T as qe}from"./radix-interactive-DJo-0Sg_.js";import{L as We,T as He,C as Ge,R as Ke,a as Ye,V as Qe,b as Xe,S as Je,c as Ze}from"./radix-layout-CC8mXA4O.js";import"./radix-toast-1_gbKn9f.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var et={},tt=S;et.createRoot=tt.createRoot,et.hydrateRoot=tt.hydrateRoot;const rt={USER_DATA:{staleTime:9e5,gcTime:36e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!0,retry:2},BREASTFEEDING_STRUCTURE:{staleTime:36e5,gcTime:72e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!0,retry:2},BREASTFEEDING_SUBSECTIONS:{staleTime:432e5,gcTime:864e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:1},BREASTFEEDING_MEDICATIONS:{staleTime:216e5,gcTime:432e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!0,retry:1},BREASTFEEDING_SEARCH:{staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:2}},st={USER_PROFILE:e=>["user-profile",e],USER_STATS:e=>["user-stats",e],MEDICATIONS:["medications"],MEDICATION_DETAIL:e=>["medication",e],MEDICATION_TAGS:e=>["medication-tags",e],MEDICATION_CATEGORIES:["medication-categories"],QUESTIONS:(e,t)=>["questions",e,t],QUESTION_COUNT:(e,t)=>["question-count",e,t],SEARCH_RESULTS:e=>["search",e],SITE_SETTINGS:["site-settings"],CATEGORIES:["categories"],AGE:["age"],WEIGHT:["weight"],GROQ_ANALYSIS:e=>["groq-analysis",e],BREASTFEEDING_STRUCTURE:["breastfeeding-structure"],BREASTFEEDING_SECTIONS:["breastfeeding-sections"],BREASTFEEDING_SUBSECTIONS:e=>["breastfeeding-subsections",e],BREASTFEEDING_MEDICATIONS:e=>["breastfeeding-medications",e],BREASTFEEDING_SEARCH:e=>["breastfeeding-search",e]},at={defaultOptions:{queries:{staleTime:6e5,gcTime:18e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,refetchInterval:!1,refetchIntervalInBackground:!1,retry:1,retryDelay:e=>Math.min(1e3*2**e,5e3),retryOnMount:!1,networkMode:"online",throwOnError:!1,meta:{timeout:15e3}},mutations:{retry:2,retryDelay:1500,networkMode:"online",meta:{timeout:15e3}}}},nt=new class{constructor(){s(this,"STORAGE_KEY","pedbook-cache"),s(this,"MAX_STORAGE_SIZE",5242880),s(this,"PERSISTENT_KEYS",["site-settings","categories","user-profile","age","weight"])}getUserCacheKey(){const e=localStorage.getItem("auth_user_id");return e?`${this.STORAGE_KEY}-${e}`:this.STORAGE_KEY}save(e,t,r=864e5){try{if(!this.shouldPersist(e))return;const s=this.getCache(),a={data:t,timestamp:Date.now(),expiresAt:Date.now()+r};s[e]=a,this.cleanExpired(s),this.ensureStorageLimit(s),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(s))}catch(s){this.clear()}}get(e){try{const t=this.getCache(),r=t[e];return r?Date.now()>r.expiresAt?(delete t[e],localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t)),null):r.data:null}catch(t){return null}}remove(e){try{const t=this.getCache();delete t[e],localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t))}catch(t){}}clear(){try{localStorage.removeItem(this.STORAGE_KEY)}catch(e){}}initializeWithQueryClient(e){try{const t=this.getCache();Object.entries(t).forEach((([t,r])=>{if(Date.now()<=r.expiresAt){const s=this.parseQueryKey(t);e.setQueryData(s,r.data)}})),this.cleanExpired(t),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t))}catch(t){}}setupAutoSave(e){e.getQueryCache().subscribe((e=>{if("updated"===e.type&&e.query.state.data){const t=this.stringifyQueryKey(e.query.queryKey);this.shouldPersist(t)&&this.save(t,e.query.state.data)}}))}getCache(){try{const e=localStorage.getItem(this.STORAGE_KEY);return e?JSON.parse(e):{}}catch(e){return{}}}shouldPersist(e){return this.PERSISTENT_KEYS.some((t=>e.includes(t)))}cleanExpired(e){const t=Date.now();Object.keys(e).forEach((r=>{e[r].expiresAt<t&&delete e[r]}))}ensureStorageLimit(e){if(JSON.stringify(e).length>this.MAX_STORAGE_SIZE){const t=Object.entries(e).sort((([,e],[,t])=>e.timestamp-t.timestamp));for(;JSON.stringify(e).length>this.MAX_STORAGE_SIZE&&t.length>0;){const[r]=t.shift();delete e[r]}}}stringifyQueryKey(e){return JSON.stringify(e)}parseQueryKey(e){try{return JSON.parse(e)}catch{return[e]}}getStats(){try{const e=this.getCache(),t=Date.now(),r=Object.values(e);return{totalEntries:r.length,totalSize:JSON.stringify(e).length,expiredEntries:r.filter((e=>e.expiresAt<t)).length}}catch{return{totalEntries:0,totalSize:0,expiredEntries:0}}}};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var it,ot;(ot=it||(it={})).Unimplemented="UNIMPLEMENTED",ot.Unavailable="UNAVAILABLE";class lt extends Error{constructor(e,t,r){super(e),this.message=e,this.code=t,this.data=r}}const ct=(e=>e.Capacitor=(e=>{const t=e.CapacitorCustomPlatform||null,r=e.Capacitor||{},s=r.Plugins=r.Plugins||{},a=()=>null!==t?t.name:(e=>{var t,r;return(null==e?void 0:e.androidBridge)?"android":(null===(r=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===r?void 0:r.bridge)?"ios":"web"})(e),n=e=>{var t;return null===(t=r.PluginHeaders)||void 0===t?void 0:t.find((t=>t.name===e))},i=new Map;return r.convertFileSrc||(r.convertFileSrc=e=>e),r.getPlatform=a,r.handleError=t=>e.console.error(t),r.isNativePlatform=()=>"web"!==a(),r.isPluginAvailable=e=>{const t=i.get(e);return!!(null==t?void 0:t.platforms.has(a()))||!!n(e)},r.registerPlugin=(e,o={})=>{const l=i.get(e);if(l)return l.proxy;const c=a(),d=n(e);let u;const m=s=>{let a;const n=(...n)=>{const i=(async()=>(!u&&c in o?u=u="function"==typeof o[c]?await o[c]():o[c]:null!==t&&!u&&"web"in o&&(u=u="function"==typeof o.web?await o.web():o.web),u))().then((t=>{const i=((t,s)=>{var a,n;if(!d){if(t)return null===(n=t[s])||void 0===n?void 0:n.bind(t);throw new lt(`"${e}" plugin is not implemented on ${c}`,it.Unimplemented)}{const n=null==d?void 0:d.methods.find((e=>s===e.name));if(n)return"promise"===n.rtype?t=>r.nativePromise(e,s.toString(),t):(t,a)=>r.nativeCallback(e,s.toString(),t,a);if(t)return null===(a=t[s])||void 0===a?void 0:a.bind(t)}})(t,s);if(i){const e=i(...n);return a=null==e?void 0:e.remove,e}throw new lt(`"${e}.${s}()" is not implemented on ${c}`,it.Unimplemented)}));return"addListener"===s&&(i.remove=async()=>a()),i};return n.toString=()=>`${s.toString()}() { [capacitor code] }`,Object.defineProperty(n,"name",{value:s,writable:!1,configurable:!1}),n},h=m("addListener"),p=m("removeListener"),f=(e,t)=>{const r=h({eventName:e},t),s=async()=>{const s=await r;p({eventName:e,callbackId:s},t)},a=new Promise((e=>r.then((()=>e({remove:s})))));return a.remove=async()=>{await s()},a},g=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?f:h;case"removeListener":return p;default:return m(t)}}});return s[e]=g,i.set(e,{name:e,proxy:g,platforms:new Set([...Object.keys(o),...d?[c]:[]])}),g},r.Exception=lt,r.DEBUG=!!r.DEBUG,r.isLoggingEnabled=!!r.isLoggingEnabled,r})(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),dt=ct.registerPlugin;class ut{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let r=!1;this.listeners[e]||(this.listeners[e]=[],r=!0),this.listeners[e].push(t);const s=this.windowListeners[e];return s&&!s.registered&&this.addWindowListener(s),r&&this.sendRetainedArgumentsForEvent(e),Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,r){const s=this.listeners[e];if(s)s.forEach((e=>e(t)));else if(r){let r=this.retainedEventArguments[e];r||(r=[]),r.push(t),this.retainedEventArguments[e]=r}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new ct.Exception(e,it.Unimplemented)}unavailable(e="not available"){return new ct.Exception(e,it.Unavailable)}async removeListener(e,t){const r=this.listeners[e];if(!r)return;const s=r.indexOf(t);this.listeners[e].splice(s,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach((t=>{this.notifyListeners(e,t)})))}}const mt=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),ht=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class pt extends ut{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach((e=>{if(e.length<=0)return;let[r,s]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");r=ht(r).trim(),s=ht(s).trim(),t[r]=s})),t}async setCookie(e){try{const t=mt(e.key),r=mt(e.value),s=`; expires=${(e.expires||"").replace("expires=","")}`,a=(e.path||"/").replace("path=",""),n=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${r||""}${s}; path=${a}; ${n};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}dt("CapacitorCookies",{web:()=>new pt});const ft=(e,t={})=>{const r=Object.assign({method:e.method||"GET",headers:e.headers},t),s=((e={})=>{const t=Object.keys(e);return Object.keys(e).map((e=>e.toLocaleLowerCase())).reduce(((r,s,a)=>(r[s]=e[t[a]],r)),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)r.body=e.data;else if(s.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[r,s]of Object.entries(e.data||{}))t.set(r,s);r.body=t.toString()}else if(s.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach(((e,r)=>{t.append(r,e)}));else for(const r of Object.keys(e.data))t.append(r,e.data[r]);r.body=t;const s=new Headers(r.headers);s.delete("content-type"),r.headers=s}else(s.includes("application/json")||"object"==typeof e.data)&&(r.body=JSON.stringify(e.data));return r};class gt extends ut{async request(e){const t=ft(e,e.webFetchExtra),r=((e,t=!0)=>e?Object.entries(e).reduce(((e,r)=>{const[s,a]=r;let n,i;return Array.isArray(a)?(i="",a.forEach((e=>{n=t?encodeURIComponent(e):e,i+=`${s}=${n}&`})),i.slice(0,-1)):(n=t?encodeURIComponent(a):a,i=`${s}=${n}`),`${e}&${i}`}),"").substr(1):null)(e.params,e.shouldEncodeUrlParams),s=r?`${e.url}?${r}`:e.url,a=await fetch(s,t),n=a.headers.get("content-type")||"";let i,o,{responseType:l="text"}=a.ok?e:{};switch(n.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":o=await a.blob(),i=await(async e=>new Promise(((t,r)=>{const s=new FileReader;s.onload=()=>{const e=s.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},s.onerror=e=>r(e),s.readAsDataURL(e)})))(o);break;case"json":i=await a.json();break;default:i=await a.text()}const c={};return a.headers.forEach(((e,t)=>{c[t]=e})),{data:i,headers:c,status:a.status,url:a.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}var xt,yt,vt,bt;dt("CapacitorHttp",{web:()=>new gt}),(yt=xt||(xt={})).Dark="DARK",yt.Light="LIGHT",yt.Default="DEFAULT",(bt=vt||(vt={})).None="NONE",bt.Slide="SLIDE",bt.Fade="FADE";const wt=dt("StatusBar"),jt=(...e)=>e.filter(((e,t,r)=>Boolean(e)&&r.indexOf(e)===t)).join(" ")
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */;
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var kt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Et=T.forwardRef((({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:a="",children:n,iconNode:i,...o},l)=>T.createElement("svg",{ref:l,...kt,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:jt("lucide",a),...o},[...i.map((([e,t])=>T.createElement(e,t))),...Array.isArray(n)?n:[n]]))),At=(e,t)=>{const r=T.forwardRef((({className:r,...s},a)=>{return T.createElement(Et,{ref:a,iconNode:t,className:jt(`lucide-${n=e,n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,r),...s});var n}));return r.displayName=`${e}`,r},Ct=At("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),_t=At("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),Nt=At("Baby",[["path",{d:"M9 12h.01",key:"157uk2"}],["path",{d:"M15 12h.01",key:"1k8ypt"}],["path",{d:"M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5",key:"1u7htd"}],["path",{d:"M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1",key:"5yv0yz"}]]),St=At("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]),Tt=At("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),Pt=At("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),Dt=At("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),Rt=At("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Mt=At("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),It=At("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Lt=At("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),Ot=At("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Ft=At("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Vt=At("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),zt=At("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),Bt=At("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]]),$t=At("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),Ut=At("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),qt=At("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),Wt=At("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),Ht=At("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),Gt=At("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Kt=At("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),Yt=At("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),Qt=At("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),Xt=At("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),Jt=At("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Zt=At("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),er=At("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),tr=At("Pill",[["path",{d:"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z",key:"wa1lgi"}],["path",{d:"m8.5 8.5 7 7",key:"rvfmvr"}]]),rr=At("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),sr=At("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ar=At("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),nr=At("Skull",[["path",{d:"m12.5 17-.5-1-.5 1h1z",key:"3me087"}],["path",{d:"M15 22a1 1 0 0 0 1-1v-1a2 2 0 0 0 1.56-3.25 8 8 0 1 0-11.12 0A2 2 0 0 0 8 20v1a1 1 0 0 0 1 1z",key:"1o5pge"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}]]),ir=At("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),or=At("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]]),lr=At("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),cr=At("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]),dr=At("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ur=At("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]]),mr=At("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function hr(e){var t,r,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=hr(e[t]))&&(s&&(s+=" "),s+=r)}else for(r in e)e[r]&&(s&&(s+=" "),s+=r);return s}function pr(){for(var e,t,r=0,s="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=hr(e))&&(s&&(s+=" "),s+=t);return s}const fr=e=>{const t=vr(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{const r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),gr(r,t)||yr(e)},getConflictingClassGroupIds:(e,t)=>{const a=r[e]||[];return t&&s[e]?[...a,...s[e]]:a}}},gr=(e,t)=>{if(0===e.length)return t.classGroupId;const r=e[0],s=t.nextPart.get(r),a=s?gr(e.slice(1),s):void 0;if(a)return a;if(0===t.validators.length)return;const n=e.join("-");return t.validators.find((({validator:e})=>e(n)))?.classGroupId},xr=/^\[(.+)\]$/,yr=e=>{if(xr.test(e)){const t=xr.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},vr=e=>{const{theme:t,prefix:r}=e,s={nextPart:new Map,validators:[]};return kr(Object.entries(e.classGroups),r).forEach((([e,r])=>{br(r,s,e,t)})),s},br=(e,t,r,s)=>{e.forEach((e=>{if("string"!=typeof e){if("function"==typeof e)return jr(e)?void br(e(s),t,r,s):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach((([e,a])=>{br(a,wr(t,e),r,s)}))}else(""===e?t:wr(t,e)).classGroupId=r}))},wr=(e,t)=>{let r=e;return t.split("-").forEach((e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)})),r},jr=e=>e.isThemeGetter,kr=(e,t)=>t?e.map((([e,r])=>[e,r.map((e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,r])=>[t+e,r]))):e))])):e,Er=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,s=new Map;const a=(a,n)=>{r.set(a,n),t++,t>e&&(t=0,s=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=s.get(e))?(a(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):a(e,t)}}},Ar=e=>{const{separator:t,experimentalParseClassName:r}=e,s=1===t.length,a=t[0],n=t.length,i=e=>{const r=[];let i,o=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===o){if(c===a&&(s||e.slice(u,u+n)===t)){r.push(e.slice(l,u)),l=u+n;continue}if("/"===c){i=u;continue}}"["===c?o++:"]"===c&&o--}const c=0===r.length?e:e.substring(l),d=c.startsWith("!");return{modifiers:r,hasImportantModifier:d,baseClassName:d?c.substring(1):c,maybePostfixModifierPosition:i&&i>l?i-l:void 0}};return r?e=>r({className:e,parseClassName:i}):i},Cr=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach((e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)})),t.push(...r.sort()),t},_r=/\s+/;function Nr(){let e,t,r=0,s="";for(;r<arguments.length;)(e=arguments[r++])&&(t=Sr(e))&&(s&&(s+=" "),s+=t);return s}const Sr=e=>{if("string"==typeof e)return e;let t,r="";for(let s=0;s<e.length;s++)e[s]&&(t=Sr(e[s]))&&(r&&(r+=" "),r+=t);return r};function Tr(e,...t){let r,s,a,n=function(o){const l=t.reduce(((e,t)=>t(e)),e());return r=(e=>({cache:Er(e.cacheSize),parseClassName:Ar(e),...fr(e)}))(l),s=r.cache.get,a=r.cache.set,n=i,i(o)};function i(e){const t=s(e);if(t)return t;const n=((e,t)=>{const{parseClassName:r,getClassGroupId:s,getConflictingClassGroupIds:a}=t,n=[],i=e.trim().split(_r);let o="";for(let l=i.length-1;l>=0;l-=1){const e=i[l],{modifiers:t,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(e);let m=Boolean(u),h=s(m?d.substring(0,u):d);if(!h){if(!m){o=e+(o.length>0?" "+o:o);continue}if(h=s(d),!h){o=e+(o.length>0?" "+o:o);continue}m=!1}const p=Cr(t).join(":"),f=c?p+"!":p,g=f+h;if(n.includes(g))continue;n.push(g);const x=a(h,m);for(let r=0;r<x.length;++r){const e=x[r];n.push(f+e)}o=e+(o.length>0?" "+o:o)}return o})(e,r);return a(e,n),n}return function(){return n(Nr.apply(null,arguments))}}const Pr=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},Dr=/^\[(?:([a-z-]+):)?(.+)\]$/i,Rr=/^\d+\/\d+$/,Mr=new Set(["px","full","screen"]),Ir=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Lr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Or=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Fr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Vr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,zr=e=>$r(e)||Mr.has(e)||Rr.test(e),Br=e=>ts(e,"length",rs),$r=e=>Boolean(e)&&!Number.isNaN(Number(e)),Ur=e=>ts(e,"number",$r),qr=e=>Boolean(e)&&Number.isInteger(Number(e)),Wr=e=>e.endsWith("%")&&$r(e.slice(0,-1)),Hr=e=>Dr.test(e),Gr=e=>Ir.test(e),Kr=new Set(["length","size","percentage"]),Yr=e=>ts(e,Kr,ss),Qr=e=>ts(e,"position",ss),Xr=new Set(["image","url"]),Jr=e=>ts(e,Xr,ns),Zr=e=>ts(e,"",as),es=()=>!0,ts=(e,t,r)=>{const s=Dr.exec(e);return!!s&&(s[1]?"string"==typeof t?s[1]===t:t.has(s[1]):r(s[2]))},rs=e=>Lr.test(e)&&!Or.test(e),ss=()=>!1,as=e=>Fr.test(e),ns=e=>Vr.test(e),is=Tr((()=>{const e=Pr("colors"),t=Pr("spacing"),r=Pr("blur"),s=Pr("brightness"),a=Pr("borderColor"),n=Pr("borderRadius"),i=Pr("borderSpacing"),o=Pr("borderWidth"),l=Pr("contrast"),c=Pr("grayscale"),d=Pr("hueRotate"),u=Pr("invert"),m=Pr("gap"),h=Pr("gradientColorStops"),p=Pr("gradientColorStopPositions"),f=Pr("inset"),g=Pr("margin"),x=Pr("opacity"),y=Pr("padding"),v=Pr("saturate"),b=Pr("scale"),w=Pr("sepia"),j=Pr("skew"),k=Pr("space"),E=Pr("translate"),A=()=>["auto",Hr,t],C=()=>[Hr,t],_=()=>["",zr,Br],N=()=>["auto",$r,Hr],S=()=>["","0",Hr],T=()=>[$r,Hr];return{cacheSize:500,separator:":",theme:{colors:[es],spacing:[zr,Br],blur:["none","",Gr,Hr],brightness:T(),borderColor:[e],borderRadius:["none","","full",Gr,Hr],borderSpacing:C(),borderWidth:_(),contrast:T(),grayscale:S(),hueRotate:T(),invert:S(),gap:C(),gradientColorStops:[e],gradientColorStopPositions:[Wr,Br],inset:A(),margin:A(),opacity:T(),padding:C(),saturate:T(),scale:T(),sepia:S(),skew:T(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",Hr]}],container:["container"],columns:[{columns:[Gr]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Hr]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",qr,Hr]}],basis:[{basis:A()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Hr]}],grow:[{grow:S()}],shrink:[{shrink:S()}],order:[{order:["first","last","none",qr,Hr]}],"grid-cols":[{"grid-cols":[es]}],"col-start-end":[{col:["auto",{span:["full",qr,Hr]},Hr]}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":[es]}],"row-start-end":[{row:["auto",{span:[qr,Hr]},Hr]}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Hr]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Hr]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Hr,t]}],"min-w":[{"min-w":[Hr,t,"min","max","fit"]}],"max-w":[{"max-w":[Hr,t,"none","full","min","max","fit","prose",{screen:[Gr]},Gr]}],h:[{h:[Hr,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Hr,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Hr,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Hr,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Gr,Br]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ur]}],"font-family":[{font:[es]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Hr]}],"line-clamp":[{"line-clamp":["none",$r,Ur]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",zr,Hr]}],"list-image":[{"list-image":["none",Hr]}],"list-style-type":[{list:["none","disc","decimal",Hr]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[x]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",zr,Br]}],"underline-offset":[{"underline-offset":["auto",zr,Hr]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Hr]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Hr]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[x]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Qr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Yr]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Jr]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[o]}],"border-w-x":[{"border-x":[o]}],"border-w-y":[{"border-y":[o]}],"border-w-s":[{"border-s":[o]}],"border-w-e":[{"border-e":[o]}],"border-w-t":[{"border-t":[o]}],"border-w-r":[{"border-r":[o]}],"border-w-b":[{"border-b":[o]}],"border-w-l":[{"border-l":[o]}],"border-opacity":[{"border-opacity":[x]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[o]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[o]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[x]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[zr,Hr]}],"outline-w":[{outline:[zr,Br]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:_()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[x]}],"ring-offset-w":[{"ring-offset":[zr,Br]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Gr,Zr]}],"shadow-color":[{shadow:[es]}],opacity:[{opacity:[x]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[s]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Gr,Hr]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[x]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Hr]}],duration:[{duration:T()}],ease:[{ease:["linear","in","out","in-out",Hr]}],delay:[{delay:T()}],animate:[{animate:["none","spin","ping","pulse","bounce",Hr]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[b]}],"scale-x":[{"scale-x":[b]}],"scale-y":[{"scale-y":[b]}],rotate:[{rotate:[qr,Hr]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Hr]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Hr]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Hr]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[zr,Br,Ur]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}));function os(...e){return is(pr(e))}function ls(e,t){const r=Array(t.length+1).fill(null).map((()=>Array(e.length+1).fill(null)));for(let s=0;s<=e.length;s++)r[0][s]=s;for(let s=0;s<=t.length;s++)r[s][0]=s;for(let s=1;s<=t.length;s++)for(let a=1;a<=e.length;a++){const n=e[a-1]===t[s-1]?0:1;r[s][a]=Math.min(r[s][a-1]+1,r[s-1][a]+1,r[s-1][a-1]+n)}return r[t.length][e.length]}const cs=u,ds=m,us=i,ms=l,hs=T.forwardRef((({className:e,...t},r)=>a.jsx(n,{ref:r,className:os("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t})));hs.displayName=n.displayName;const ps=T.forwardRef((({className:e,children:t,hideCloseButton:r=!1,...s},n)=>a.jsxs(us,{children:[a.jsx(hs,{}),a.jsxs(o,{ref:n,className:os("fixed left-[50%] top-[50%] z-50 grid w-[calc(100dvw-2rem)] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-xl max-h-[80dvh] overflow-y-auto sm:rounded-lg sm:w-full no-swipe",e),"data-draggable":"false","data-zoom":"false","aria-describedby":s["aria-describedby"]||"dialog-description",onCloseAutoFocus:()=>{setTimeout((()=>{document.body.style.pointerEvents="auto"}),100)},...s,children:[t,a.jsx("div",{id:"dialog-description",className:"sr-only",children:"Conteúdo do diálogo. Pressione Escape para fechar."}),!r&&a.jsxs(l,{className:"absolute right-3 top-3 p-2 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 focus:ring-offset-2 shadow-sm hover:shadow-md",children:[a.jsx(mr,{className:"h-5 w-5"}),a.jsx("span",{className:"sr-only",children:"Fechar"})]})]})]})));ps.displayName=o.displayName;const fs=({className:e,...t})=>a.jsx("div",{className:os("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});fs.displayName="DialogHeader";const gs=({className:e,...t})=>a.jsx("div",{className:os("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});gs.displayName="DialogFooter";const xs=T.forwardRef((({className:e,...t},r)=>a.jsx(c,{ref:r,className:os("text-lg font-semibold leading-none tracking-tight",e),...t})));xs.displayName=c.displayName;const ys=T.forwardRef((({className:e,...t},r)=>a.jsx(d,{ref:r,className:os("text-sm text-muted-foreground",e),...t})));function vs(e){var t,r,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=vs(e[t]))&&(s&&(s+=" "),s+=r);else for(t in e)e[t]&&(s&&(s+=" "),s+=t);return s}ys.displayName=d.displayName;const bs=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,ws=function(){for(var e,t,r=0,s="";r<arguments.length;)(e=arguments[r++])&&(t=vs(e))&&(s&&(s+=" "),s+=t);return s},js=(e,t)=>r=>{var s;if(null==(null==t?void 0:t.variants))return ws(e,null==r?void 0:r.class,null==r?void 0:r.className);const{variants:a,defaultVariants:n}=t,i=Object.keys(a).map((e=>{const t=null==r?void 0:r[e],s=null==n?void 0:n[e];if(null===t)return null;const i=bs(t)||bs(s);return a[e][i]})),o=r&&Object.entries(r).reduce(((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e}),{}),l=null==t||null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce(((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every((e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...n,...o}[t]):{...n,...o}[t]===r}))?[...e,r,s]:e}),[]);return ws(e,i,l,null==r?void 0:r.class,null==r?void 0:r.className)},ks=js("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",duolingo:"bg-[#58CC02] text-white hover:bg-[#46a302] shadow-md hover:shadow-lg border-b-2 border-[#46a302] active:border-b-0 active:mt-0.5 active:translate-y-px transition-all transform-gpu",success:"bg-green-500 text-white hover:bg-green-600",raised:"border-2 border-black bg-white text-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all",hackRed:"bg-hackathon-red hover:bg-hackathon-red/90 border-2 border-black text-white shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all",hackYellow:"bg-hackathon-yellow hover:bg-hackathon-yellow/90 border-2 border-black text-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all",hackGreen:"bg-hackathon-green hover:bg-hackathon-green/90 border-2 border-black text-black shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10",duolingo:"h-10 px-5 py-2.5 rounded-xl font-bold text-base",hack:"px-6 py-3 font-bold text-base"}},defaultVariants:{variant:"default",size:"default"}}),Es=T.forwardRef((({className:e,variant:t,size:r,asChild:s=!1,...n},i)=>{const o=s?h:"button";return a.jsx(o,{className:os(ks({variant:t,size:r,className:e})),ref:i,...n})}));Es.displayName="Button";const As=T.createContext(void 0),Cs=({children:e})=>{const[t,r]=T.useState({isOpen:!1,type:"info",title:"",description:""}),s=()=>{r((e=>({...e,isOpen:!1}))),t.onClose&&t.onClose()},n=()=>{switch(t.type){case"success":return a.jsx(Ot,{className:"h-6 w-6 text-green-600"});case"error":return a.jsx(Ft,{className:"h-6 w-6 text-red-600"});case"warning":return a.jsx(Lt,{className:"h-6 w-6 text-yellow-600"});default:return a.jsx(Wt,{className:"h-6 w-6 text-blue-600"})}};return a.jsxs(As.Provider,{value:{showDialog:e=>{r({...e,isOpen:!0})},closeDialog:s},children:[e,a.jsx(cs,{open:t.isOpen,onOpenChange:s,children:a.jsxs(ps,{className:"max-w-md",children:[a.jsxs(fs,{children:[a.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[n(),a.jsx(xs,{className:"text-lg",children:t.title})]}),a.jsx(ys,{className:"text-base",children:t.description})]}),a.jsx("div",{className:`p-4 rounded-lg border ${(()=>{switch(t.type){case"success":return"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950";case"error":return"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950";case"warning":return"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950";default:return"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950"}})()}`,children:a.jsxs("div",{className:"flex items-center gap-2",children:[n(),a.jsxs("span",{className:"text-sm font-medium",children:["success"===t.type&&"Operação realizada com sucesso!","error"===t.type&&"Ocorreu um erro na operação.","warning"===t.type&&"Atenção necessária.","info"===t.type&&"Informação importante."]})]})}),a.jsx(gs,{children:a.jsx(Es,{onClick:s,className:"w-full",children:t.buttonText||"Entendi"})})]})})]})},_s=()=>{const e=T.useContext(As);if(!e)throw new Error("useFeedbackDialog must be used within a FeedbackDialogProvider");return e},Ns=()=>{const{showDialog:e}=_s();return(t,r,s,a)=>{e({type:"success",title:t,description:r,buttonText:s,onClose:a})}},Ss=()=>{const{showDialog:e}=_s();return(t,r,s,a)=>{e({type:"error",title:t,description:r,buttonText:s,onClose:a})}},Ts=V("https://bxedpdmgvgatjdfxgxij.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit",storage:window.localStorage,storageKey:"supabase.auth.token",debug:!1},global:{headers:{"X-Client-Info":"pedbook-web"}},db:{schema:"public"},realtime:{params:{eventsPerSecond:2,heartbeatIntervalMs:3e4,reconnectAfterMs:e=>Math.min(2e3*e,1e4)}}}),Ps=T.createContext(void 0),Ds=({children:e})=>{const[t,r]=T.useState((()=>"undefined"!=typeof window&&localStorage.getItem("theme")||"light"));return T.useEffect((()=>{if("undefined"!=typeof window){const e=window.document.documentElement;"dark"===t?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",t)}}),[t]),a.jsx(Ps.Provider,{value:{theme:t,toggleTheme:()=>{r((e=>"light"===e?"dark":"light"))}},children:e})},Rs=()=>{const e=T.useContext(Ps);if(void 0===e)throw new Error("useTheme must be used within a ThemeProvider");return e};var Ms="undefined"!=typeof Element,Is="function"==typeof Map,Ls="function"==typeof Set,Os="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function Fs(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;var r,s,a,n;if(Array.isArray(e)){if((r=e.length)!=t.length)return!1;for(s=r;0!==s--;)if(!Fs(e[s],t[s]))return!1;return!0}if(Is&&e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(n=e.entries();!(s=n.next()).done;)if(!t.has(s.value[0]))return!1;for(n=e.entries();!(s=n.next()).done;)if(!Fs(s.value[1],t.get(s.value[0])))return!1;return!0}if(Ls&&e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(n=e.entries();!(s=n.next()).done;)if(!t.has(s.value[0]))return!1;return!0}if(Os&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if((r=e.length)!=t.length)return!1;for(s=r;0!==s--;)if(e[s]!==t[s])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"==typeof e.valueOf&&"function"==typeof t.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString&&"function"==typeof e.toString&&"function"==typeof t.toString)return e.toString()===t.toString();if((r=(a=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(s=r;0!==s--;)if(!Object.prototype.hasOwnProperty.call(t,a[s]))return!1;if(Ms&&e instanceof Element)return!1;for(s=r;0!==s--;)if(("_owner"!==a[s]&&"__v"!==a[s]&&"__o"!==a[s]||!e.$$typeof)&&!Fs(e[a[s]],t[a[s]]))return!1;return!0}return e!=e&&t!=t}const Vs=P((function(e,t){try{return Fs(e,t)}catch(r){if((r.message||"").match(/stack|recursion/i))return!1;throw r}})),zs=P((function(e,t,r,s,a,n,i,o){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[r,s,a,n,i,o],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}})),Bs=P((function(e,t,r,s){var a=r?r.call(s,e,t):void 0;if(void 0!==a)return!!a;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(var o=Object.prototype.hasOwnProperty.bind(t),l=0;l<n.length;l++){var c=n[l];if(!o(c))return!1;var d=e[c],u=t[c];if(!1===(a=r?r.call(s,d,u,c):void 0)||void 0===a&&d!==u)return!1}return!0}));var $s=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))($s||{}),Us={rel:["amphtml","canonical","alternate"]},qs={type:["application/ld+json"]},Ws={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},Hs=Object.values($s),Gs={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},Ks=Object.entries(Gs).reduce(((e,[t,r])=>(e[r]=t,e)),{}),Ys="data-rh",Qs=(e,t)=>{for(let r=e.length-1;r>=0;r-=1){const s=e[r];if(Object.prototype.hasOwnProperty.call(s,t))return s[t]}return null},Xs=e=>{let t=Qs(e,"title");const r=Qs(e,"titleTemplate");if(Array.isArray(t)&&(t=t.join("")),r&&t)return r.replace(/%s/g,(()=>t));const s=Qs(e,"defaultTitle");return t||s||void 0},Js=e=>Qs(e,"onChangeClientState")||(()=>{}),Zs=(e,t)=>t.filter((t=>void 0!==t[e])).map((t=>t[e])).reduce(((e,t)=>({...e,...t})),{}),ea=(e,t)=>t.filter((e=>void 0!==e.base)).map((e=>e.base)).reverse().reduce(((t,r)=>{if(!t.length){const s=Object.keys(r);for(let a=0;a<s.length;a+=1){const n=s[a].toLowerCase();if(-1!==e.indexOf(n)&&r[n])return t.concat(r)}}return t}),[]),ta=(e,t,r)=>{const s={};return r.filter((t=>!!Array.isArray(t[e])||(void 0!==t[e]&&(t[e],console&&console.warn),!1))).map((t=>t[e])).reverse().reduce(((e,r)=>{const a={};r.filter((e=>{let r;const n=Object.keys(e);for(let s=0;s<n.length;s+=1){const a=n[s],i=a.toLowerCase();-1===t.indexOf(i)||"rel"===r&&"canonical"===e[r].toLowerCase()||"rel"===i&&"stylesheet"===e[i].toLowerCase()||(r=i),-1===t.indexOf(a)||"innerHTML"!==a&&"cssText"!==a&&"itemprop"!==a||(r=a)}if(!r||!e[r])return!1;const i=e[r].toLowerCase();return s[r]||(s[r]={}),a[r]||(a[r]={}),!s[r][i]&&(a[r][i]=!0,!0)})).reverse().forEach((t=>e.push(t)));const n=Object.keys(a);for(let t=0;t<n.length;t+=1){const e=n[t],r={...s[e],...a[e]};s[e]=r}return e}),[]).reverse()},ra=(e,t)=>{if(Array.isArray(e)&&e.length)for(let r=0;r<e.length;r+=1)if(e[r][t])return!0;return!1},sa=e=>Array.isArray(e)?e.join(""):e,aa=(e,t)=>Array.isArray(e)?e.reduce(((e,r)=>(((e,t)=>{const r=Object.keys(e);for(let s=0;s<r.length;s+=1)if(t[r[s]]&&t[r[s]].includes(e[r[s]]))return!0;return!1})(r,t)?e.priority.push(r):e.default.push(r),e)),{priority:[],default:[]}):{default:e,priority:[]},na=(e,t)=>({...e,[t]:void 0}),ia=["noscript","script","style"],oa=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),la=e=>Object.keys(e).reduce(((t,r)=>{const s=void 0!==e[r]?`${r}="${e[r]}"`:`${r}`;return t?`${t} ${s}`:s}),""),ca=(e,t={})=>Object.keys(e).reduce(((t,r)=>(t[Gs[r]||r]=e[r],t)),t),da=(e,t)=>t.map(((t,r)=>{const s={key:r,[Ys]:!0};return Object.keys(t).forEach((e=>{const r=Gs[e]||e;if("innerHTML"===r||"cssText"===r){const e=t.innerHTML||t.cssText;s.dangerouslySetInnerHTML={__html:e}}else s[r]=t[e]})),D.createElement(e,s)})),ua=(e,t,r=!0)=>{switch(e){case"title":return{toComponent:()=>((e,t,r)=>{const s=ca(r,{key:t,[Ys]:!0});return[D.createElement("title",s,t)]})(0,t.title,t.titleAttributes),toString:()=>((e,t,r,s)=>{const a=la(r),n=sa(t);return a?`<${e} ${Ys}="true" ${a}>${oa(n,s)}</${e}>`:`<${e} ${Ys}="true">${oa(n,s)}</${e}>`})(e,t.title,t.titleAttributes,r)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>ca(t),toString:()=>la(t)};default:return{toComponent:()=>da(e,t),toString:()=>((e,t,r=!0)=>t.reduce(((t,s)=>{const a=s,n=Object.keys(a).filter((e=>!("innerHTML"===e||"cssText"===e))).reduce(((e,t)=>{const s=void 0===a[t]?t:`${t}="${oa(a[t],r)}"`;return e?`${e} ${s}`:s}),""),i=a.innerHTML||a.cssText||"",o=-1===ia.indexOf(e);return`${t}<${e} ${Ys}="true" ${n}${o?"/>":`>${i}</${e}>`}`}),""))(e,t,r)}}},ma=e=>{const{baseTag:t,bodyAttributes:r,encode:s=!0,htmlAttributes:a,noscriptTags:n,styleTags:i,title:o="",titleAttributes:l,prioritizeSeoTags:c}=e;let{linkTags:d,metaTags:u,scriptTags:m}=e,h={toComponent:()=>{},toString:()=>""};return c&&({priorityMethods:h,linkTags:d,metaTags:u,scriptTags:m}=(({metaTags:e,linkTags:t,scriptTags:r,encode:s})=>{const a=aa(e,Ws),n=aa(t,Us),i=aa(r,qs);return{priorityMethods:{toComponent:()=>[...da("meta",a.priority),...da("link",n.priority),...da("script",i.priority)],toString:()=>`${ua("meta",a.priority,s)} ${ua("link",n.priority,s)} ${ua("script",i.priority,s)}`},metaTags:a.default,linkTags:n.default,scriptTags:i.default}})(e)),{priority:h,base:ua("base",t,s),bodyAttributes:ua("bodyAttributes",r,s),htmlAttributes:ua("htmlAttributes",a,s),link:ua("link",d,s),meta:ua("meta",u,s),noscript:ua("noscript",n,s),script:ua("script",m,s),style:ua("style",i,s),title:ua("title",{title:o,titleAttributes:l},s)}},ha=[],pa=!("undefined"==typeof window||!window.document||!window.document.createElement),fa=class{constructor(e,t){s(this,"instances",[]),s(this,"canUseDOM",pa),s(this,"context"),s(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?ha:this.instances,add:e=>{(this.canUseDOM?ha:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?ha:this.instances).indexOf(e);(this.canUseDOM?ha:this.instances).splice(t,1)}}}),this.context=e,this.canUseDOM=t||!1,t||(e.helmet=ma({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},ga=D.createContext({}),xa=(e=class extends T.Component{constructor(t){super(t),s(this,"helmetData"),this.helmetData=new fa(this.props.context||{},e.canUseDOM)}render(){return D.createElement(ga.Provider,{value:this.helmetData.value},this.props.children)}},s(e,"canUseDOM",pa),e),ya=(e,t)=>{const r=document.head||document.querySelector("head"),s=r.querySelectorAll(`${e}[${Ys}]`),a=[].slice.call(s),n=[];let i;return t&&t.length&&t.forEach((t=>{const r=document.createElement(e);for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))if("innerHTML"===e)r.innerHTML=t.innerHTML;else if("cssText"===e)r.styleSheet?r.styleSheet.cssText=t.cssText:r.appendChild(document.createTextNode(t.cssText));else{const s=e,a=void 0===t[s]?"":t[s];r.setAttribute(e,a)}r.setAttribute(Ys,"true"),a.some(((e,t)=>(i=t,r.isEqualNode(e))))?a.splice(i,1):n.push(r)})),a.forEach((e=>e.parentNode?.removeChild(e))),n.forEach((e=>r.appendChild(e))),{oldTags:a,newTags:n}},va=(e,t)=>{const r=document.getElementsByTagName(e)[0];if(!r)return;const s=r.getAttribute(Ys),a=s?s.split(","):[],n=[...a],i=Object.keys(t);for(const o of i){const e=t[o]||"";r.getAttribute(o)!==e&&r.setAttribute(o,e),-1===a.indexOf(o)&&a.push(o);const s=n.indexOf(o);-1!==s&&n.splice(s,1)}for(let o=n.length-1;o>=0;o-=1)r.removeAttribute(n[o]);a.length===n.length?r.removeAttribute(Ys):r.getAttribute(Ys)!==i.join(",")&&r.setAttribute(Ys,i.join(","))},ba=(e,t)=>{const{baseTag:r,bodyAttributes:s,htmlAttributes:a,linkTags:n,metaTags:i,noscriptTags:o,onChangeClientState:l,scriptTags:c,styleTags:d,title:u,titleAttributes:m}=e;va("body",s),va("html",a),((e,t)=>{void 0!==e&&document.title!==e&&(document.title=sa(e)),va("title",t)})(u,m);const h={baseTag:ya("base",r),linkTags:ya("link",n),metaTags:ya("meta",i),noscriptTags:ya("noscript",o),scriptTags:ya("script",c),styleTags:ya("style",d)},p={},f={};Object.keys(h).forEach((e=>{const{newTags:t,oldTags:r}=h[e];t.length&&(p[e]=t),r.length&&(f[e]=h[e].oldTags)})),t&&t(),l(e,p,f)},wa=null,ja=class extends T.Component{constructor(){super(...arguments),s(this,"rendered",!1)}shouldComponentUpdate(e){return!Bs(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:t}=this.props.context;let r=null;const s=(a=e.get().map((e=>{const t={...e.props};return delete t.context,t})),{baseTag:ea(["href"],a),bodyAttributes:Zs("bodyAttributes",a),defer:Qs(a,"defer"),encode:Qs(a,"encodeSpecialCharacters"),htmlAttributes:Zs("htmlAttributes",a),linkTags:ta("link",["rel","href"],a),metaTags:ta("meta",["name","charset","http-equiv","property","itemprop"],a),noscriptTags:ta("noscript",["innerHTML"],a),onChangeClientState:Js(a),scriptTags:ta("script",["src","innerHTML"],a),styleTags:ta("style",["cssText"],a),title:Xs(a),titleAttributes:Zs("titleAttributes",a),prioritizeSeoTags:ra(a,"prioritizeSeoTags")});var a,n;xa.canUseDOM?(n=s,wa&&cancelAnimationFrame(wa),n.defer?wa=requestAnimationFrame((()=>{ba(n,(()=>{wa=null}))})):(ba(n),wa=null)):ma&&(r=ma(s)),t(r)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},ka=(t=class extends T.Component{shouldComponentUpdate(e){return!Vs(na(this.props,"helmetData"),na(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,r,s){return{...t,[e.type]:[...t[e.type]||[],{...r,...this.mapNestedChildrenToProps(e,s)}]}}mapObjectTypeChildren(e,t,r,s){switch(e.type){case"title":return{...t,[e.type]:s,titleAttributes:{...r}};case"body":return{...t,bodyAttributes:{...r}};case"html":return{...t,htmlAttributes:{...r}};default:return{...t,[e.type]:{...r}}}}mapArrayTypeChildrenToProps(e,t){let r={...t};return Object.keys(e).forEach((t=>{r={...r,[t]:e[t]}})),r}warnOnInvalidChildren(e,t){return zs(Hs.some((t=>e.type===t)),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${Hs.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),zs(!t||"string"==typeof t||Array.isArray(t)&&!t.some((e=>"string"!=typeof e)),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,t){let r={};return D.Children.forEach(e,(e=>{if(!e||!e.props)return;const{children:s,...a}=e.props,n=Object.keys(a).reduce(((e,t)=>(e[Ks[t]||t]=a[t],e)),{});let{type:i}=e;switch("symbol"==typeof i?i=i.toString():this.warnOnInvalidChildren(e,s),i){case"Symbol(react.fragment)":t=this.mapChildrenToProps(s,t);break;case"link":case"meta":case"noscript":case"script":case"style":r=this.flattenArrayTypeChildren(e,r,n,s);break;default:t=this.mapObjectTypeChildren(e,t,n,s)}})),this.mapArrayTypeChildrenToProps(r,t)}render(){const{children:e,...t}=this.props;let r={...t},{helmetData:s}=t;return e&&(r=this.mapChildrenToProps(e,r)),!s||s instanceof fa||(s=new fa(s.context,!0),delete r.helmetData),s?D.createElement(ja,{...r,context:s.value}):D.createElement(ga.Consumer,null,(e=>D.createElement(ja,{...r,context:e})))}},s(t,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),t);const Ea=new class{constructor(){s(this,"isEnabled",!0),s(this,"logQueue",[]),s(this,"isProcessing",!1)}disable(){this.isEnabled=!1}enable(){this.isEnabled=!0}async logProblem(e){this.isEnabled&&(this.logQueue.push(e),this.processQueue())}async processQueue(){if(!this.isProcessing&&0!==this.logQueue.length){this.isProcessing=!0;try{const e=this.logQueue.shift();if(!e)return;this.getSessionId(),e.url||window.location.pathname,e.type,e.severity,e.component,e.message,(new Date).toISOString(),e.userAgent||navigator.userAgent,window.screen.width,window.screen.height,window.innerWidth,window.innerHeight,navigator.connection,performance.memory&&(performance.memory.usedJSHeapSize,performance.memory.totalJSHeapSize,performance.memory.jsHeapSizeLimit),e.metadata,e.userAgent||navigator.userAgent,document.referrer}catch(e){}finally{this.isProcessing=!1,this.logQueue.length>0&&setTimeout((()=>this.processQueue()),1e3)}}}getSessionId(){let e=sessionStorage.getItem("site_session_id");return e||(e=`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,sessionStorage.setItem("site_session_id",e)),e}getQueueStats(){return{pending:this.logQueue.length,processing:this.isProcessing}}clearQueue(){this.logQueue=[]}},Aa=(e,t,r)=>{Ea.logProblem({type:"CIRCUIT_BREAKER",severity:"HIGH",component:e,message:`Circuit breaker ativado: ${t}`,metadata:{action:t,...r},url:window.location.pathname})},Ca=(e,t,r,s)=>{Ea.logProblem({type:"RATE_LIMIT",severity:"MEDIUM",component:e,message:`Rate limit excedido: ${r}/${t}`,metadata:{limit:t,current:r,...s},url:window.location.pathname})},_a=(e,t,r,s)=>{Ea.logProblem({type:"BANDWIDTH_ALERT",severity:"CRITICAL",component:"BandwidthMonitor",message:`Alerta de bandwidth: ${e} ${t} excede limite ${r}`,metadata:{alert_type:e,value:t,limit:r,...s},url:window.location.pathname})},Na=(e,t,r)=>{Ea.logProblem({type:"SYSTEM_ERROR",severity:"HIGH",component:e,message:`Erro no sistema: ${t.message}`,metadata:{error_name:t.name,error_message:t.message,error_stack:t.stack,...r},url:window.location.pathname})},Sa=(e,t,r,s,a)=>{Ea.logProblem({type:"PERFORMANCE_ISSUE",severity:"MEDIUM",component:e,message:`Performance degradada: ${t} ${r}ms > ${s}ms`,metadata:{metric:t,value:r,threshold:s,...a},url:window.location.pathname})},Ta=()=>{const e=D.useCallback((()=>Ea.getQueueStats()),[]),t=D.useCallback((()=>Ea.clearQueue()),[]),r=D.useCallback((()=>Ea.disable()),[]),s=D.useCallback((()=>Ea.enable()),[]),a=D.useCallback((()=>{Aa("TEST_ENDPOINT","OPEN","Teste de funcionamento do circuit breaker")}),[]);return{logCircuitBreakerEvent:Aa,logRateLimitEvent:Ca,logBandwidthAlert:_a,logSystemError:Na,logPerformanceIssue:Sa,getQueueStats:e,clearQueue:t,disable:r,enable:s,testCircuitBreaker:a}},Pa=Object.freeze(Object.defineProperty({__proto__:null,logBandwidthAlert:_a,logCircuitBreakerEvent:Aa,logPerformanceIssue:Sa,logRateLimitEvent:Ca,logSystemError:Na,problemLogger:Ea,useProblemLogger:Ta},Symbol.toStringTag,{value:"Module"}));class Da extends D.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){e.message?.includes("Loading chunk")||e.message?.includes("Failed to fetch dynamically imported module")||e.message?.includes("MIME type")||e.stack?.includes(".js")?(Na("ErrorBoundary",e,{errorType:"chunk_loading_error",errorInfo:t.componentStack,willReload:!0,userAgent:navigator.userAgent,url:window.location.href}),setTimeout((()=>{window.location.reload()}),1e3)):Na("ErrorBoundary",e,{errorType:"critical_application_error",errorInfo:t.componentStack,willReload:!1,userAgent:navigator.userAgent,url:window.location.href,timestamp:(new Date).toISOString()})}render(){return this.state.hasError?this.state.error?.message?.includes("Loading chunk")||this.state.error?.message?.includes("Failed to fetch dynamically imported module")||this.state.error?.message?.includes("MIME type")?a.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen p-4 bg-blue-50 dark:bg-blue-900/20",children:a.jsxs("div",{className:"max-w-md p-6 bg-white dark:bg-slate-800 rounded-lg shadow-lg text-center",children:[a.jsx("div",{className:"w-16 h-16 mx-auto mb-4 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"}),a.jsx("h2",{className:"text-xl font-bold text-blue-600 dark:text-blue-400 mb-4",children:"Atualizando recursos..."}),a.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-4",children:"Alguns recursos precisam ser atualizados. A página será recarregada automaticamente."}),a.jsx("button",{onClick:()=>window.location.reload(),className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"Recarregar Agora"})]})}):a.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen p-4 bg-red-50 dark:bg-red-900/20",children:a.jsxs("div",{className:"max-w-md p-6 bg-white dark:bg-slate-800 rounded-lg shadow-lg",children:[a.jsx("h2",{className:"text-2xl font-bold text-red-600 dark:text-red-400 mb-4",children:"Ocorreu um erro inesperado"}),a.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-4",children:"Desculpe pelo inconveniente. Tente recarregar a página ou voltar para a página inicial."}),this.state.error&&a.jsx("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded mb-4 overflow-auto",children:a.jsx("p",{className:"font-mono text-sm text-red-800 dark:text-red-300",children:this.state.error.toString()})}),a.jsxs("div",{className:"flex space-x-4",children:[a.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"Recarregar"}),a.jsx("button",{onClick:()=>window.location.href="/",className:"px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:"Página Inicial"})]})]})}):this.props.children}}const Ra=()=>(T.useEffect((()=>{const e=e=>{if(e.touches.length>1){const t=e.target;"INPUT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||t.classList.contains("selectable-text")||t.closest(".selectable-text")||e.preventDefault()}};return document.addEventListener("touchstart",e,{passive:!1}),document.addEventListener("touchmove",e,{passive:!1}),()=>{document.removeEventListener("touchstart",e),document.removeEventListener("touchmove",e)}}),[]),null),Ma=new class{constructor(){s(this,"events",[]),s(this,"maxEvents",0)}addEvent(e,t,r,s=!1){}authContextInit(e){this.addEvent("AuthContext","initAuth started",e)}authContextCacheCheck(e){this.addEvent("AuthContext","cache check",e)}authContextCacheLoaded(e){this.addEvent("AuthContext","profile loaded from cache",e)}authContextSessionFromHook(e){this.addEvent("AuthContext","using session from hook",e)}authContextSessionFromStorage(e){this.addEvent("AuthContext","found session in storage",e)}authContextNoSession(){this.addEvent("AuthContext","no session found anywhere",{})}authContextSavedToStorage(e){this.addEvent("AuthContext","saved to localStorage",e)}authContextCompleted(e){this.addEvent("AuthContext","initAuth completed",e)}authContextSessionChange(e){this.addEvent("AuthContext","session change detected",e)}authContextError(e){this.addEvent("AuthContext","error",{error:e.message||e},!0)}historyHookInit(e){this.addEvent("useDrWillHistory","hook initialized",e)}historyUserChanged(e){this.addEvent("useDrWillHistory","user state changed",e,!0)}historyCreateThreadCalled(e){this.addEvent("useDrWillHistory","createNewThread called",e,!0)}historyCreateThreadAuthCheck(e){this.addEvent("useDrWillHistory","auth state check in createNewThread",e)}historyCreateThreadNoUser(e){this.addEvent("useDrWillHistory","createNewThread failed - no user",e,!0)}historyCreateThreadSuccess(e){this.addEvent("useDrWillHistory","createNewThread success",e)}chatSendMessageCalled(e){this.addEvent("useDrWillChat","sendMessage called",e,!0)}chatAuthStateCheck(e){this.addEvent("useDrWillChat","auth state at sendMessage",e)}chatCreateThreadCalled(e){this.addEvent("useDrWillChat","calling createNewThread",e)}chatCreateThreadResult(e){this.addEvent("useDrWillChat","createNewThread result",e)}floatingChatUserState(e){this.addEvent("FloatingChatButton","user state in handleSendMessage",e)}floatingChatHooksState(e){this.addEvent("FloatingChatButton","hooks state in handleSendMessage",e)}drWillUserState(e){this.addEvent("DrWill","user state in handleSendMessage",e)}drWillSendMessageCall(e){this.addEvent("DrWill","calling sendMessage",e)}getEvents(){return[...this.events]}getEventsByComponent(e){return this.events.filter((t=>t.component===e))}getRecentEvents(e=20){return this.events.slice(-e)}printSummary(){}clear(){this.events=[]}},Ia=T.createContext(void 0),La=()=>{const e=T.useContext(Ia);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Oa=({children:e})=>{const t=z.useSession(),r=z.useSupabaseClient(),[s,n]=T.useState(null),[i,o]=T.useState(null),[l,c]=T.useState(!1),[d,u]=T.useState(!0),[m,h]=T.useState({}),[p,f]=T.useState(!1),g=T.useCallback((e=>{h((t=>{const r={...t};return delete r[e],r})),localStorage.removeItem("auth_profile"),localStorage.removeItem("auth_user_id")}),[]),x=T.useCallback((async(e,t,s=!1)=>{try{if(!s&&m[e]){const r={...m[e],email:t};return o(r),c(r?.is_admin||!1),r}const{data:a,error:n}=await r.from("profiles").select("\n          id, full_name, avatar_url, formation_area, graduation_year,\n          is_student, is_professional, is_admin, specialty, preparation_type,\n          theme_preference, premium, premium_requested, premium_requested_history,\n          professional_email, phone, registration_number, created_at, updated_at\n        ").eq("id",e).single();if(n)return null;const i={...a,email:t};return h((t=>({...t,[e]:i}))),o(i),c(i?.is_admin||!1),i}catch(a){return null}}),[r]),y=T.useCallback((async(e,t)=>await x(e,t,!0)),[x]);T.useEffect((()=>{p||(async()=>{Ma.authContextInit({hasSession:!!t,sessionUserId:t?.user?.id,sessionEmail:t?.user?.email,isInitialized:p});try{const s=localStorage.getItem("auth_profile"),a=localStorage.getItem("auth_user_id");if(Ma.authContextCacheCheck({hasCachedProfile:!!s,cachedUserId:a}),s&&a)try{const e=JSON.parse(s);o(e),c(e?.is_admin||!1),Ma.authContextCacheLoaded({userId:a,profileId:e?.id,isAdmin:e?.is_admin}),h((t=>({...t,[a]:e})))}catch(e){Ma.authContextError(e),localStorage.removeItem("auth_profile"),localStorage.removeItem("auth_user_id")}if(void 0!==t)if(Ma.authContextSessionFromHook({hasUser:!!t?.user,userId:t?.user?.id,email:t?.user?.email}),n(t?.user??null),t?.user){const e=await x(t.user.id,t.user.email);e&&(localStorage.setItem("auth_profile",JSON.stringify(e)),localStorage.setItem("auth_user_id",t.user.id),Ma.authContextSavedToStorage({userId:t.user.id,source:"session"}))}else Ma.authContextNoSession(),o(null),c(!1),localStorage.removeItem("auth_profile"),localStorage.removeItem("auth_user_id");else{const{data:e}=await r.auth.getSession();if(e.session){Ma.authContextSessionFromStorage({userId:e.session.user.id,email:e.session.user.email}),n(e.session.user);const t=await x(e.session.user.id,e.session.user.email);t&&(localStorage.setItem("auth_profile",JSON.stringify(t)),localStorage.setItem("auth_user_id",e.session.user.id),Ma.authContextSavedToStorage({userId:e.session.user.id,source:"storage"}))}else Ma.authContextNoSession(),n(null),o(null),c(!1),localStorage.removeItem("auth_profile"),localStorage.removeItem("auth_user_id")}}catch(a){Ma.authContextError(a)}finally{u(!1),f(!0),Ma.authContextCompleted({hasUser:!!s,userId:s?.id,isLoading:!1,isInitialized:!0})}})()}),[t,r,p]),T.useEffect((()=>{p&&(async()=>{if(Ma.authContextSessionChange({hasSession:!!t,sessionUserId:t?.user?.id,sessionEmail:t?.user?.email,isInitialized:p}),n(t?.user??null),t?.user){const e=await x(t.user.id,t.user.email);e&&(localStorage.setItem("auth_profile",JSON.stringify(e)),localStorage.setItem("auth_user_id",t.user.id),Ma.authContextSavedToStorage({userId:t.user.id,source:"sessionChange"}))}else o(null),c(!1),localStorage.removeItem("auth_profile"),localStorage.removeItem("auth_user_id")})()}),[t,p]);const v={user:s,profile:i,isAdmin:l,isLoading:d,signOut:async()=>{u(!0),await r.auth.signOut(),n(null),o(null),c(!1),localStorage.removeItem("auth_profile"),localStorage.removeItem("auth_user_id"),u(!1)},invalidateProfileCache:g,refreshProfile:y};return a.jsx(Ia.Provider,{value:v,children:e})},Fa=({className:e})=>{const{isLoading:t}=La(),[r,s]=T.useState(!1);return T.useEffect((()=>{let e;return t?e=setTimeout((()=>{s(!0)}),300):s(!1),()=>{clearTimeout(e)}}),[t]),r?a.jsxs("div",{className:os("fixed top-0 left-0 right-0 z-50 flex items-center justify-center bg-primary/10 dark:bg-primary/20 h-1",e),children:[a.jsx("div",{className:"h-1 bg-primary animate-pulse w-full max-w-md"}),a.jsx(Gt,{className:"animate-spin text-primary absolute top-2 right-2 h-4 w-4"})]}):null},Va={gradientHeading:e=>os("font-bold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text dark:from-blue-400 dark:to-blue-600",e),glassContainer:e=>os("bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-primary/10 dark:border-primary/20 shadow-lg",e),card:e=>os("bg-white dark:bg-slate-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg",e),input:e=>os("bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100",e),secondaryText:e=>os("text-gray-500 dark:text-gray-400",e),button:e=>os("bg-white dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700 border border-gray-200 dark:border-gray-700",e),select:e=>os("bg-white dark:bg-slate-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100",e),gradientBackground:e=>os("bg-gradient-to-b from-white via-primary/5 to-primary/10 dark:from-slate-900 dark:via-slate-800/30 dark:to-slate-800/50",e),gradientCard:(e,t)=>{const r={blue:"from-blue-50 to-white dark:from-blue-900/20 dark:to-slate-900 dark:border-blue-900/30",purple:"from-purple-50 to-white dark:from-purple-900/20 dark:to-slate-900 dark:border-purple-900/30",green:"from-green-50 to-white dark:from-green-900/20 dark:to-slate-900 dark:border-green-900/30",amber:"from-amber-50 to-white dark:from-amber-900/20 dark:to-slate-900 dark:border-amber-900/30",red:"from-red-50 to-white dark:from-red-900/20 dark:to-slate-900 dark:border-red-900/30",orange:"from-orange-50 to-white dark:from-orange-900/20 dark:to-slate-900 dark:border-orange-900/30",yellow:"from-yellow-50 to-white dark:from-yellow-900/20 dark:to-slate-900 dark:border-yellow-900/30",indigo:"from-indigo-50 to-white dark:from-indigo-900/20 dark:to-slate-900 dark:border-indigo-900/30",pink:"from-pink-50 to-white dark:from-pink-900/20 dark:to-slate-900 dark:border-pink-900/30",cyan:"from-cyan-50 to-white dark:from-cyan-900/20 dark:to-slate-900 dark:border-cyan-900/30"};return os(`bg-gradient-to-br ${r[e]||r.blue} border border-gray-200 dark:border-gray-700`,t)},pageBackground:e=>os("min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",e)},za=T.createContext(void 0),Ba=({children:e})=>{const[t,r]=T.useState(!1),[s,n]=T.useState({title:"",description:"",type:"info",buttonText:"Fechar"});return a.jsxs(za.Provider,{value:{showNotification:e=>{n(e),r(!0)}},children:[e,a.jsx(cs,{open:t,onOpenChange:r,children:a.jsxs(ps,{className:"sm:max-w-[425px] rounded-xl overflow-hidden p-0 z-[99999]","aria-describedby":"notification-description",children:[a.jsx("div",{className:"w-full py-3 "+("success"===s.type?"bg-green-50 dark:bg-green-900/20":"error"===s.type?"bg-red-50 dark:bg-red-900/20":"bg-blue-50 dark:bg-blue-900/20"),children:a.jsxs(fs,{className:"flex flex-col items-center text-center gap-2 px-6",children:[a.jsxs("div",{className:"w-16 h-16 rounded-full flex items-center justify-center "+("success"===s.type?"bg-green-100 dark:bg-green-800/30":"error"===s.type?"bg-red-100 dark:bg-red-800/30":"bg-blue-100 dark:bg-blue-800/30"),children:["success"===s.type&&a.jsx(Ot,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),"error"===s.type&&a.jsx(Lt,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),"info"===s.type&&a.jsx(Wt,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})]}),a.jsx(xs,{className:"text-xl font-bold mt-2",children:s.title})]})}),a.jsxs("div",{className:"p-6",children:[a.jsx(ys,{id:"notification-description",className:"text-center text-base mb-6",children:s.description}),a.jsx(gs,{children:a.jsx(Es,{className:"w-full py-6 text-base font-medium rounded-lg",onClick:()=>{s.onButtonClick&&s.onButtonClick(),r(!1)},variant:"success"===s.type?"default":"error"===s.type?"destructive":"outline",children:s.buttonText||"Fechar"})})]})]})})]})},$a=()=>{const e=T.useContext(za);if(void 0===e)throw new Error("useNotification must be used within a NotificationProvider");return e},Ua=({isLoading:e,message:t="Carregando...",className:r})=>e?a.jsx("div",{className:os("fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm animate-fade-in",r),children:a.jsxs("div",{className:"flex flex-col items-center gap-3 p-4 rounded-lg",children:[a.jsx(Gt,{className:"h-10 w-10 text-primary dark:text-blue-400 animate-spin"}),a.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t})]})}):null,qa=T.createContext(void 0),Wa=()=>{const e=T.useContext(qa);if(!e)throw new Error("useLoading must be used within a LoadingProvider");return e},Ha=({children:e})=>{const[t,r]=T.useState(!1),[s,n]=T.useState("Carregando...");return a.jsxs(qa.Provider,{value:{isLoading:t,startLoading:e=>{e&&n(e),r(!0)},stopLoading:()=>{r(!1),n("Carregando...")}},children:[e,a.jsx(Ua,{isLoading:t,message:s})]})},Ga=T.createContext(void 0),Ka=({children:e})=>{const[t,r]=T.useState(!1),[s,n]=T.useState("");return a.jsx(Ga.Provider,{value:{isNavigationLocked:t,lockNavigation:e=>{r(!0),n(e)},unlockNavigation:()=>{r(!1),n("")},lockReason:s},children:e})},Ya=()=>{const e=T.useContext(Ga);if(void 0===e)throw new Error("useNavigationLock must be used within a NavigationLockProvider");return e},Qa=T.createContext(void 0),Xa=({children:e})=>{const[t,r]=T.useState(null),[s,n]=T.useState(0),[i,o]=T.useState(0),[l,c]=T.useState(null),[d,u]=T.useState(null),m={currentQuestion:t,currentQuestionIndex:s,totalQuestions:i,sessionId:l,sessionTitle:d,isInQuestionSession:null!==t&&null!==l,setCurrentQuestion:T.useCallback((e=>{r(e)}),[]),setQuestionIndex:T.useCallback((e=>{n(e)}),[]),setTotalQuestions:T.useCallback((e=>{o(e)}),[]),setSessionId:T.useCallback((e=>{c(e)}),[]),setSessionTitle:T.useCallback((e=>{u(e)}),[]),clearQuestionContext:T.useCallback((()=>{r(null),n(0),o(0),c(null),u(null)}),[]),updateQuestionContext:T.useCallback((e=>{void 0!==e.question&&r(e.question),void 0!==e.questionIndex&&n(e.questionIndex),void 0!==e.totalQuestions&&o(e.totalQuestions),void 0!==e.sessionId&&c(e.sessionId),void 0!==e.sessionTitle&&u(e.sessionTitle)}),[])};return a.jsx(Qa.Provider,{value:m,children:e})},Ja=()=>{const e=T.useContext(Qa);if(void 0===e)throw new Error("useCurrentQuestion deve ser usado dentro de um CurrentQuestionProvider");return e},Za=()=>{const{currentQuestion:e,currentQuestionIndex:t,totalQuestions:r,sessionTitle:s,isInQuestionSession:a}=Ja();return{questionNumber:t+1,totalQuestions:r,sessionTitle:s,isInQuestionSession:a,questionId:e?.id||null,specialty:e?.specialty?.name||null,theme:e?.theme?.name||null,focus:e?.focus?.name||null}},en={current:!1};let tn=null,rn=null,sn=null,an=null,nn=null,on=null,ln=!1;function cn(e=!0){return T.useEffect((()=>{if(!ln)return ln=!0,tn=window.fetch,rn=XMLHttpRequest.prototype.open,sn=Ts.auth.getSession,an=Ts.auth.getUser,sn.call(Ts.auth).then((e=>{nn=e})),an.call(Ts.auth).then((e=>{on=e})),document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&(en.current=!0,window.fetch=function(e,t){const r="string"==typeof e?e:e instanceof URL?e.toString():e instanceof Request?e.url:"";return en.current&&(r.includes("auth")||r.includes("admin")||r.includes("breastfeeding"))?Promise.resolve(new Response("{}",{status:200,headers:{"Content-Type":"application/json"}})):tn(e,t)},XMLHttpRequest.prototype.open=function(e,t,...r){if(!en.current||"string"!=typeof t||!(t.includes("auth")||t.includes("admin")||t.includes("breastfeeding")))return rn.apply(this,[e,t,...r])},Ts.auth.getSession=async function(){return en.current?nn||{data:{session:null},error:null}:sn.apply(this)},Ts.auth.getUser=async function(){return en.current?on||{data:{user:null},error:null}:an.apply(this)},setTimeout((()=>{en.current=!1,tn&&(window.fetch=tn),rn&&(XMLHttpRequest.prototype.open=rn),sn&&(Ts.auth.getSession=sn),an&&(Ts.auth.getUser=an)}),5e3))})),()=>{}}),[e]),{isProtectionActive:()=>en.current}}const dn=({children:e})=>(cn(),T.useEffect((()=>{}),[]),a.jsx(a.Fragment,{children:e})),un=()=>{const{profile:e,isLoading:t}=La(),r=U(),s=$(),[a,n]=T.useState(!0),i=e?.is_admin&&["<EMAIL>","<EMAIL>"].includes(e?.email||""),{data:o,isLoading:l,refetch:c}=I({queryKey:["maintenance-status"],queryFn:async()=>{const{data:e,error:t}=await Ts.from("site_maintenance").select("is_active, message, estimated_duration, activated_at").single();return t?{is_active:!1,message:"Site em manutenção. Voltaremos em breve!"}:e},refetchInterval:!1,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchIntervalInBackground:!1,staleTime:3e5,gcTime:6e5,enabled:!t});return T.useEffect((()=>{t||l||(n(!1),o?.is_active&&!i?"/maintenance"!==s.pathname&&r("/maintenance",{replace:!0}):o?.is_active||"/maintenance"!==s.pathname||r("/",{replace:!0}))}),[o,i,t,l,r,s.pathname]),{maintenanceStatus:o,isMaintenanceActive:o?.is_active||!1,isSuperAdmin:i,isLoading:t||l||a,toggleMaintenance:async(t,r,s)=>{if(!i)throw new Error("Apenas super admin pode alterar o modo de manutenção");const{data:a}=await Ts.from("site_maintenance").select("id").single();if(!a)throw new Error("Registro de manutenção não encontrado");const{error:n}=await Ts.from("site_maintenance").update({is_active:t,message:r||"Site em manutenção. Voltaremos em breve!",estimated_duration:s,activated_by:e?.id,updated_at:(new Date).toISOString()}).eq("id",a.id);if(n)throw n;await c()},refetch:c}},mn=new class{constructor(e){s(this,"metrics",[]),s(this,"alerts",[]),s(this,"config"),s(this,"isMonitoring",!1),this.config=e}startMonitoring(){this.isMonitoring||(this.isMonitoring=!0,this.interceptFetch(),setInterval((()=>{this.checkMetrics()}),6e4),setInterval((()=>{this.cleanOldData()}),36e5))}stopMonitoring(){this.isMonitoring=!1}interceptFetch(){const e=window.fetch;window.fetch=async(...t)=>{const r=Date.now();try{const s=await e(...t),a=Date.now(),n=s.headers.get("content-length"),i=n?parseInt(n):0;return this.recordRequest(i,a-r),s}catch(s){throw this.recordRequest(0,Date.now()-r),s}}}recordRequest(e,t){const r=Date.now(),s=Math.floor(r/6e4);let a=this.metrics.find((e=>Math.floor(e.timestamp/6e4)===s));a||(a={requestsPerMinute:0,totalRequests:0,averageResponseSize:0,totalBandwidth:0,timestamp:r},this.metrics.push(a)),a.requestsPerMinute++,a.totalRequests++,a.totalBandwidth+=e,a.averageResponseSize=a.totalBandwidth/a.totalRequests}checkMetrics(){const e=Date.now(),t=this.metrics[this.metrics.length-1];if(!t)return;t.requestsPerMinute>this.config.maxRequestsPerMinute&&this.generateAlert("CRITICAL",`Requests excessivos: ${t.requestsPerMinute}/min (limite: ${this.config.maxRequestsPerMinute}/min)`,t);const r=this.metrics.filter((t=>e-t.timestamp<36e5)).reduce(((e,t)=>e+t.totalBandwidth),0)/1048576;r>this.config.maxBandwidthPerHour&&this.generateAlert("CRITICAL",`Bandwidth excessivo: ${r.toFixed(2)}MB/h (limite: ${this.config.maxBandwidthPerHour}MB/h)`,t);const s=this.metrics.slice(-5);if(5===s.length){const e=s.reduce(((e,t)=>e+t.requestsPerMinute),0)/5;e>.8*this.config.maxRequestsPerMinute&&this.generateAlert("WARNING",`Possível loop detectado: ${e.toFixed(1)} requests/min consistentes`,t)}}generateAlert(e,t,r){const s={type:e,message:t,metrics:r,timestamp:Date.now()};this.alerts.push(s),("CRITICAL"===e?console.error:console.warn)(`🚨 [BandwidthMonitor] ${e}: ${t}`),B((async()=>{const{logBandwidthAlert:e}=await Promise.resolve().then((()=>Pa));return{logBandwidthAlert:e}}),void 0).then((({logBandwidthAlert:s})=>{s(e,r.requestsPerMinute,this.config.maxRequestsPerMinute,{message:t,totalBandwidth:r.totalBandwidth,averageResponseSize:r.averageResponseSize,totalRequests:r.totalRequests,timestamp:r.timestamp})})),this.config.alertCallback&&this.config.alertCallback(s),this.alerts.length>100&&(this.alerts=this.alerts.slice(-50))}cleanOldData(){const e=Date.now(),t=e-216e5;this.metrics=this.metrics.filter((e=>e.timestamp>t));const r=e-864e5;this.alerts=this.alerts.filter((e=>e.timestamp>r))}getMetrics(){const e=Date.now(),t=this.metrics.filter((t=>e-t.timestamp<36e5));return{current:this.metrics[this.metrics.length-1]||null,hourly:t,alerts:this.alerts.slice(-10)}}reset(){this.metrics=[],this.alerts=[]}}({maxRequestsPerMinute:100,maxBandwidthPerHour:50,alertCallback:e=>{e.type}}),hn=class e{constructor(){s(this,"isInitialized",!1)}static getInstance(){return e.instance||(e.instance=new e),e.instance}initialize(){this.isInitialized||(window.expandMermaidDiagram=e=>{this.expandMermaidDiagram(e)},window.openTableDialog=e=>{this.openTableDialog(e)},this.isInitialized=!0)}expandMermaidDiagram(e){try{const r=document.getElementById(e+"-code");if(!r)return;let s;try{s=JSON.parse(r.textContent||"")}catch(t){return}const a=new CustomEvent("openMermaidModal",{detail:{mermaidCode:s},bubbles:!0});document.dispatchEvent(a)}catch(r){this.fallbackMermaidDisplay(e)}}openTableDialog(e){try{const r=document.getElementById(`table-data-${e}`);if(!r)return;let s;try{s=JSON.parse(r.textContent||"[]")}catch(t){return}const a=new CustomEvent("openTableModal",{detail:{tableData:s},bubbles:!0});document.dispatchEvent(a)}catch(r){}}fallbackMermaidDisplay(e){try{const t=document.getElementById(e+"-code");if(t){const e=JSON.parse(t.textContent||""),r=window.open("","_blank","width=800,height=600");r&&(r.document.write(`\n            <html>\n              <head>\n                <title>Diagrama Mermaid</title>\n                <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"><\/script>\n              </head>\n              <body>\n                <div id="mermaid-container">\n                  <pre class="mermaid">${e}</pre>\n                </div>\n                <script>\n                  mermaid.initialize({ startOnLoad: true });\n                <\/script>\n              </body>\n            </html>\n          `),r.document.close())}}catch(t){}}cleanup(){this.isInitialized&&(delete window.expandMermaidDiagram,delete window.openTableDialog,this.isInitialized=!1)}};s(hn,"instance");const pn=hn.getInstance(),fn=dt("SplashScreen",{web:()=>B((()=>import("./web-BGK5KRQu.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12])).then((e=>new e.SplashScreenWeb))});var gn,xn,yn,vn;(xn=gn||(gn={})).Dark="DARK",xn.Light="LIGHT",xn.Default="DEFAULT",(vn=yn||(yn={})).Body="body",vn.Ionic="ionic",vn.Native="native",vn.None="none";const bn=dt("Keyboard");var wn,jn,kn,En;(jn=wn||(wn={})).Heavy="HEAVY",jn.Medium="MEDIUM",jn.Light="LIGHT",(En=kn||(kn={})).Success="SUCCESS",En.Warning="WARNING",En.Error="ERROR";const An=dt("Haptics",{web:()=>B((()=>import("./web-9Bdt6ipZ.js")),__vite__mapDeps([13,1,2,3,4,5,6,7,8,9,10,11,12])).then((e=>new e.HapticsWeb))});var Cn,_n,Nn,Sn,Tn,Pn;(_n=Cn||(Cn={})).Prompt="PROMPT",_n.Camera="CAMERA",_n.Photos="PHOTOS",(Sn=Nn||(Nn={})).Rear="REAR",Sn.Front="FRONT",(Pn=Tn||(Tn={})).Uri="uri",Pn.Base64="base64",Pn.DataUrl="dataUrl";class Dn extends ut{async getPhoto(e){return new Promise((async(t,r)=>{if(e.webUseInput||e.source===Cn.Photos)this.fileInputExperience(e,t,r);else if(e.source===Cn.Prompt){let s=document.querySelector("pwa-action-sheet");s||(s=document.createElement("pwa-action-sheet"),document.body.appendChild(s)),s.header=e.promptLabelHeader||"Photo",s.cancelable=!1,s.options=[{title:e.promptLabelPhoto||"From Photos"},{title:e.promptLabelPicture||"Take Picture"}],s.addEventListener("onSelection",(async s=>{0===s.detail?this.fileInputExperience(e,t,r):this.cameraExperience(e,t,r)}))}else this.cameraExperience(e,t,r)}))}async pickImages(e){return new Promise((async(e,t)=>{this.multipleFileInputExperience(e,t)}))}async cameraExperience(e,t,r){if(customElements.get("pwa-camera-modal")){const a=document.createElement("pwa-camera-modal");a.facingMode=e.direction===Nn.Front?"user":"environment",document.body.appendChild(a);try{await a.componentOnReady(),a.addEventListener("onPhoto",(async s=>{const n=s.detail;null===n?r(new lt("User cancelled photos app")):n instanceof Error?r(n):t(await this._getCameraPhoto(n,e)),a.dismiss(),document.body.removeChild(a)})),a.present()}catch(s){this.fileInputExperience(e,t,r)}}else this.fileInputExperience(e,t,r)}fileInputExperience(e,t,r){let s=document.querySelector("#_capacitor-camera-input");const a=()=>{var e;null===(e=s.parentNode)||void 0===e||e.removeChild(s)};s||(s=document.createElement("input"),s.id="_capacitor-camera-input",s.type="file",s.hidden=!0,document.body.appendChild(s),s.addEventListener("change",(r=>{const n=s.files[0];let i="jpeg";if("image/png"===n.type?i="png":"image/gif"===n.type&&(i="gif"),"dataUrl"===e.resultType||"base64"===e.resultType){const r=new FileReader;r.addEventListener("load",(()=>{if("dataUrl"===e.resultType)t({dataUrl:r.result,format:i});else if("base64"===e.resultType){const e=r.result.split(",")[1];t({base64String:e,format:i})}a()})),r.readAsDataURL(n)}else t({webPath:URL.createObjectURL(n),format:i}),a()})),s.addEventListener("cancel",(e=>{r(new lt("User cancelled photos app")),a()}))),s.accept="image/*",s.capture=!0,e.source===Cn.Photos||e.source===Cn.Prompt?s.removeAttribute("capture"):e.direction===Nn.Front?s.capture="user":e.direction===Nn.Rear&&(s.capture="environment"),s.click()}multipleFileInputExperience(e,t){let r=document.querySelector("#_capacitor-camera-input-multiple");const s=()=>{var e;null===(e=r.parentNode)||void 0===e||e.removeChild(r)};r||(r=document.createElement("input"),r.id="_capacitor-camera-input-multiple",r.type="file",r.hidden=!0,r.multiple=!0,document.body.appendChild(r),r.addEventListener("change",(t=>{const a=[];for(let e=0;e<r.files.length;e++){const t=r.files[e];let s="jpeg";"image/png"===t.type?s="png":"image/gif"===t.type&&(s="gif"),a.push({webPath:URL.createObjectURL(t),format:s})}e({photos:a}),s()})),r.addEventListener("cancel",(e=>{t(new lt("User cancelled photos app")),s()}))),r.accept="image/*",r.click()}_getCameraPhoto(e,t){return new Promise(((r,s)=>{const a=new FileReader,n=e.type.split("/")[1];"uri"===t.resultType?r({webPath:URL.createObjectURL(e),format:n,saved:!1}):(a.readAsDataURL(e),a.onloadend=()=>{const e=a.result;"dataUrl"===t.resultType?r({dataUrl:e,format:n,saved:!1}):r({base64String:e.split(",")[1],format:n,saved:!1})},a.onerror=e=>{s(e)})}))}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(await window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch(e){throw this.unavailable("Camera permissions are not available in this browser")}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}async pickLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}async getLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}}const Rn=dt("Camera",{web:()=>new Dn});var Mn,In,Ln,On;(In=Mn||(Mn={})).Documents="DOCUMENTS",In.Data="DATA",In.Library="LIBRARY",In.Cache="CACHE",In.External="EXTERNAL",In.ExternalStorage="EXTERNAL_STORAGE",In.ExternalCache="EXTERNAL_CACHE",In.LibraryNoCloud="LIBRARY_NO_CLOUD",In.Temporary="TEMPORARY",(On=Ln||(Ln={})).UTF8="utf8",On.ASCII="ascii",On.UTF16="utf16";const Fn=dt("Filesystem",{web:()=>B((()=>import("./web-CoE2gwkD.js")),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12])).then((e=>new e.FilesystemWeb))});!function(e=!1){typeof window>"u"||(window.CapacitorUtils=window.CapacitorUtils||{},void 0===window.Capacitor||e?void 0!==window.cordova&&function(e){e.CapacitorUtils.Synapse=new Proxy({},{get:(t,r)=>e.cordova.plugins[r]})}(window):function(e){e.CapacitorUtils.Synapse=new Proxy({},{get:(t,r)=>new Proxy({},{get:(t,s)=>(t,a,n)=>{const i=e.Capacitor.Plugins[r];void 0!==i?"function"==typeof i[s]?(async()=>{try{const e=await i[s](t);a(e)}catch(e){n(e)}})():n(new Error(`Method ${s} not found in Capacitor plugin ${r}`)):n(new Error(`Capacitor plugin ${r} not found`))}})})}(window))}();const Vn=()=>ct.isNativePlatform(),zn=()=>"android"===ct.getPlatform(),Bn=()=>"ios"===ct.getPlatform(),$n=new class{constructor(){s(this,"initialized",!1)}async initialize(){if(Vn()&&!this.initialized)try{await this.setupStatusBar(),await this.setupSplashScreen(),await this.setupKeyboard(),this.initialized=!0}catch(e){}}async setupStatusBar(){if(Vn())try{(()=>{switch(ct.getPlatform()){case"android":return{statusBarStyle:"dark",navigationBarColor:"#ffffff",splashScreenDelay:2e3,allowMixedContent:!0};case"ios":;}})(),zn()?(await wt.setStyle({style:xt.Dark}),await wt.setBackgroundColor({color:"#2563eb"})):Bn()&&await wt.setStyle({style:xt.Light}),await wt.show()}catch(e){}}async setupSplashScreen(){if(Vn())try{setTimeout((async()=>{await fn.hide()}),2e3)}catch(e){}}async setupKeyboard(){if(Vn())try{bn.addListener("keyboardWillShow",(e=>{document.body.style.paddingBottom=`${e.keyboardHeight}px`})),bn.addListener("keyboardWillHide",(()=>{document.body.style.paddingBottom="0px"}))}catch(e){}}async hapticFeedback(e="light"){if(Vn())try{const t={light:wn.Light,medium:wn.Medium,heavy:wn.Heavy}[e];await An.impact({style:t})}catch(t){}}async capturePhoto(){if(!Vn())return null;try{return(await Rn.getPhoto({quality:90,allowEditing:!1,resultType:Tn.DataUrl,source:Cn.Camera})).dataUrl||null}catch(e){return null}}async saveFile(e,t){if(!Vn())return!1;try{return await Fn.writeFile({path:e,data:t,directory:Mn.Documents}),!0}catch(r){return!1}}async readFile(e){if(!Vn())return null;try{return(await Fn.readFile({path:e,directory:Mn.Documents})).data}catch(t){return null}}async fileExists(e){if(!Vn())return!1;try{return await Fn.stat({path:e,directory:Mn.Documents}),!0}catch{return!1}}async updateStatusBarForTheme(e){if(Vn())try{zn()?(await wt.setStyle({style:e?xt.Dark:xt.Light}),await wt.setBackgroundColor({color:e?"#1f2937":"#2563eb"})):Bn()&&await wt.setStyle({style:e?xt.Dark:xt.Light})}catch(t){}}};var Un=function(e,t){for(var r={};e.length;){var s=e[0],a=s.code,n=s.message,i=s.path.join(".");if(!r[i])if("unionErrors"in s){var o=s.unionErrors[0].errors[0];r[i]={message:o.message,type:o.code}}else r[i]={message:n,type:a};if("unionErrors"in s&&s.unionErrors.forEach((function(t){return t.errors.forEach((function(t){return e.push(t)}))})),t){var l=r[i].types,c=l&&l[s.code];r[i]=X(i,t,r,a,c?[].concat(c,s.message):s.message)}e.shift()}return r},qn=function(e,t,r){return void 0===r&&(r={}),function(s,a,n){try{return Promise.resolve(function(a,i){try{var o=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](s,t)).then((function(e){return n.shouldUseNativeValidation&&Y({},n),{errors:{},values:r.raw?s:e}}))}catch(l){return i(l)}return o&&o.then?o.then(void 0,i):o}(0,(function(e){if(t=e,Array.isArray(null==t?void 0:t.errors))return{values:{},errors:Q(Un(e.errors,!n.shouldUseNativeValidation&&"all"===n.criteriaMode),n)};var t;throw e})))}catch(i){return Promise.reject(i)}}};const Wn=js("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Hn=T.forwardRef((({className:e,...t},r)=>a.jsx(ie,{ref:r,className:os(Wn(),e),...t})));Hn.displayName=ie.displayName;const Gn=Z,Kn=T.createContext({}),Yn=({...e})=>a.jsx(Kn.Provider,{value:{name:e.name},children:a.jsx(ee,{...e})}),Qn=()=>{const e=T.useContext(Kn),t=T.useContext(Xn),{getFieldState:r,formState:s}=J(),a=r(e.name,s);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...a}},Xn=T.createContext({}),Jn=T.forwardRef((({className:e,...t},r)=>{const s=T.useId();return a.jsx(Xn.Provider,{value:{id:s},children:a.jsx("div",{ref:r,className:os("space-y-2",e),...t})})}));Jn.displayName="FormItem";const Zn=T.forwardRef((({className:e,...t},r)=>{const{error:s,formItemId:n}=Qn();return a.jsx(Hn,{ref:r,className:os(s&&"text-destructive",e),htmlFor:n,...t})}));Zn.displayName="FormLabel";const ei=T.forwardRef((({...e},t)=>{const{error:r,formItemId:s,formDescriptionId:n,formMessageId:i}=Qn();return a.jsx(h,{ref:t,id:s,"aria-describedby":r?`${n} ${i}`:`${n}`,"aria-invalid":!!r,...e})}));ei.displayName="FormControl";const ti=T.forwardRef((({className:e,...t},r)=>{const{formDescriptionId:s}=Qn();return a.jsx("p",{ref:r,id:s,className:os("text-sm text-muted-foreground",e),...t})}));ti.displayName="FormDescription";const ri=T.forwardRef((({className:e,children:t,...r},s)=>{const{error:n,formMessageId:i}=Qn(),o=n?String(n?.message):t;return o?a.jsx("p",{ref:s,id:i,className:os("text-sm font-medium text-destructive",e),...r,children:o}):null}));ri.displayName="FormMessage";const si=T.forwardRef((({className:e,type:t,...r},s)=>a.jsx("input",{type:t,className:os("flex h-10 w-full rounded-md border border-input bg-white dark:bg-slate-800 px-3 py-2 text-sm text-foreground dark:text-gray-100 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700",e),ref:s,...r})));si.displayName="Input";const ai=T.forwardRef((({className:e,value:t,...r},s)=>a.jsx(_e,{ref:s,className:os("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:a.jsx(Ne,{className:"h-full w-full flex-1 bg-gradient-to-r from-blue-400 to-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})));ai.displayName=_e.displayName;const ni=()=>a.jsxs("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),a.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),a.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),a.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),ii=({onSuccess:e,mode:t="signin",disabled:r=!1})=>{const[s,n]=T.useState(!1),{showNotification:i}=$a();if((()=>{const e=navigator.userAgent.toLowerCase(),t=/android/i.test(e),r=/wv/.test(e)||/webview/.test(e),s="Android"in window||"AndroidInterface"in window,a=/chrome/.test(e)&&!/edg/.test(e),n=/samsungbrowser/.test(e);return t&&(r||s||!a&&!n)})())return null;const o="signin"===t?"Entrar com Google":"Criar conta com Google";return a.jsxs("div",{className:"relative",children:[a.jsxs(Es,{type:"button",variant:"outline",onClick:async()=>{try{n(!0);const e="localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname?"http://localhost:8080/auth/callback":"https://pedb.com.br/auth/callback",{data:t,error:r}=await Ts.auth.signInWithOAuth({provider:"google",options:{redirectTo:e,queryParams:{access_type:"offline",prompt:"consent"}}});if(r)throw r;i({title:"Redirecionando...",description:"Você será redirecionado para o Google para fazer login.",type:"info",buttonText:"Ok"})}catch(e){let t="Erro inesperado ao fazer login com Google.";e.message?.includes("popup")?t="Pop-up bloqueado. Por favor, permita pop-ups para este site.":e.message?.includes("network")?t="Erro de conexão. Verifique sua internet e tente novamente.":e.message?.includes("unauthorized")&&(t="Acesso não autorizado. Verifique as configurações do Google OAuth."),i({title:"Erro no login",description:t,type:"error",buttonText:"Tentar novamente"})}finally{n(!1)}},disabled:r||s,className:"w-full flex items-center justify-center gap-3 h-11 border-gray-300 hover:bg-gray-50 transition-colors",children:[s?a.jsx(Gt,{className:"h-4 w-4 animate-spin"}):a.jsx(ni,{}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:s?"Conectando...":o})]}),a.jsx("div",{className:"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-md",children:"Novo"})]})},oi=te({email:re().email("Email inválido").min(5,"Email muito curto").refine((e=>{const t=e.split("@")[1];return t&&t.includes(".")&&t.length>3}),"Domínio de email inválido"),password:re().min(6,"A senha deve ter pelo menos 6 caracteres").refine((e=>!/^\s|\s$/.test(e)),"Senha não pode começar ou terminar com espaços")}),li=({onModeChange:e,onResetPassword:t,onSuccess:r})=>{const[s,n]=T.useState(!1),[i,o]=T.useState("idle"),[l,c]=T.useState(0),[d,u]=T.useState(0),[m,h]=T.useState(null),p=T.useRef(null),{showNotification:f}=$a();T.useEffect((()=>{"true"===new URLSearchParams(window.location.search).get("reset")&&(window.history.replaceState({},"",window.location.pathname),setTimeout((()=>{t()}),500))}),[t]);const g=()=>a.jsxs("div",{className:"text-center p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[a.jsx("p",{className:"text-sm text-blue-800",children:a.jsx("strong",{children:"💡 Login com Google"})}),a.jsx("p",{className:"text-xs text-blue-700 mt-1",children:"Temporariamente indisponível no aplicativo. Use email e senha para fazer login."})]}),x=se({resolver:qn(oi),defaultValues:{email:"",password:""}});return T.useEffect((()=>{if(s&&"error"!==i&&"success"!==i){const e=setInterval((()=>{c((e=>e>=70&&e<90&&"loading-profile"===i?e+.5:Math.min(e+2,95)))}),100);return()=>clearInterval(e)}"success"===i&&c(100)}),[s,i]),T.useEffect((()=>(s&&(p.current=setTimeout((()=>{"success"!==i&&(o("error"),h("Tempo limite excedido. A rede pode estar lenta ou instável."),n(!1),f({title:"Tempo limite excedido",description:"A conexão está lenta. Tente novamente ou verifique sua internet.",type:"error",buttonText:"Tentar novamente"}))}),1e4)),()=>{p.current&&clearTimeout(p.current)})),[s,i,f]),a.jsx("div",{className:"space-y-6 py-4",children:a.jsx(Gn,{...x,children:a.jsxs("form",{onSubmit:x.handleSubmit((async e=>{try{n(!0),c(0),o("authenticating"),h(null),u((e=>e+1));const{data:t,error:s}=await Ts.auth.signInWithPassword({email:e.email,password:e.password});if(s)throw o("error"),s;o("loading-profile"),await new Promise((e=>setTimeout(e,500))),o("success");const a=t.user?.user_metadata,i=a?.full_name||"",l=(new Date).getHours();let d="Bem-vindo";d=l>=5&&l<12?"Bom dia":l>=12&&l<18?"Boa tarde":"Boa noite";const m=i?`${d}, ${i.split(" ")[0]}! Que bom ter você de volta.`:`${d}! Que bom ter você de volta.`;f({title:"Login realizado com sucesso!",description:m,type:"success",buttonText:"Continuar",onButtonClick:r}),setTimeout((()=>{}),300)}catch(t){let e="Verifique suas credenciais e tente novamente.";t.message?.includes("Invalid login")?e="Email ou senha incorretos.":t.message?.includes("network")?e="Problema de conexão. Verifique sua internet.":t.message?.includes("timeout")&&(e="Tempo limite excedido. Tente novamente mais tarde."),h(e),o("error")}finally{"success"!==i&&n(!1)}})),className:"space-y-6 w-full",children:[a.jsx(Yn,{control:x.control,name:"email",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{className:"text-sm sm:text-base font-medium",children:"Email"}),a.jsx(ei,{children:a.jsx(si,{placeholder:"<EMAIL>",...e,type:"email",autoComplete:"email",className:"w-full text-sm sm:text-base bg-white/5 border-primary/20 focus:border-primary transition-all"})}),a.jsx(ri,{className:"text-xs sm:text-sm"})]})}),a.jsx(Yn,{control:x.control,name:"password",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{className:"text-sm sm:text-base font-medium",children:"Senha"}),a.jsx(ei,{children:a.jsx(si,{type:"password",autoComplete:"current-password",...e,className:"w-full text-sm sm:text-base bg-white/5 border-primary/20 focus:border-primary transition-all"})}),a.jsx(ri,{className:"text-xs sm:text-sm"})]})}),a.jsxs("div",{className:"flex flex-col gap-4",children:[s&&a.jsxs("div",{className:"mb-2 space-y-2",children:[a.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[a.jsxs("span",{children:["authenticating"===i&&"Verificando credenciais...","loading-profile"===i&&"Carregando seu perfil...","success"===i&&"Login realizado com sucesso!","error"===i&&"Erro ao fazer login"]}),a.jsxs("span",{children:[Math.round(l),"%"]})]}),a.jsx(ai,{value:l,className:"h-1.5"})]}),a.jsx(Es,{type:"submit",className:"w-full text-sm sm:text-base bg-primary hover:bg-primary/90 text-white transition-all relative",disabled:s,children:s?a.jsxs("span",{className:"flex items-center gap-2",children:[a.jsx(Gt,{className:"h-4 w-4 animate-spin"}),"authenticating"===i&&"Autenticando...","loading-profile"===i&&"Carregando perfil...","success"===i&&"Redirecionando...","error"===i&&"Erro ao entrar"]}):"Entrar"}),m&&a.jsxs("div",{className:"text-sm text-destructive bg-destructive/10 p-3 rounded-md border border-destructive/30 flex items-start gap-2",children:[a.jsx(Lt,{className:"h-5 w-5 flex-shrink-0 mt-0.5"}),a.jsxs("div",{children:[a.jsx("p",{className:"font-medium",children:"Não foi possível fazer login"}),a.jsx("p",{className:"text-xs mt-1",children:m})]})]}),a.jsxs(Es,{type:"button",variant:"ghost",className:"text-sm text-muted-foreground hover:text-primary flex items-center gap-2 justify-center",onClick:t,disabled:s,children:[a.jsx(Ht,{className:"h-4 w-4"}),"Esqueceu sua senha?"]}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("span",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:a.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"ou"})})]}),(()=>{const e=navigator.userAgent.toLowerCase(),t=/android/i.test(e),r=/wv/.test(e)||/webview/.test(e),s="Android"in window||"AndroidInterface"in window,a=/chrome/.test(e)&&!/edg/.test(e),n=/samsungbrowser/.test(e);return t&&(r||s||!a&&!n)})()?a.jsx(g,{}):a.jsx(ii,{mode:"signin",onSuccess:r,disabled:s}),a.jsxs(Es,{type:"button",variant:"outline",className:"w-full text-sm sm:text-base group relative overflow-hidden bg-gradient-to-r from-accent-purple/20 via-accent-blue/20 to-accent-pink/20 hover:from-accent-purple/30 hover:via-accent-blue/30 hover:to-accent-pink/30 border-primary/20 transition-all duration-300",onClick:e,disabled:s,children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-accent-purple/10 via-accent-blue/10 to-accent-pink/10 group-hover:opacity-100 transition-opacity"}),a.jsxs("span",{className:"relative flex items-center justify-center gap-2",children:[a.jsx(ir,{className:"h-4 w-4 text-primary animate-pulse"}),"Cadastre-se GRATUITAMENTE!"]})]})]})]})})})},ci=T.forwardRef((({className:e,...t},r)=>a.jsx(oe,{className:os("grid gap-2",e),...t,ref:r})));ci.displayName=oe.displayName;const di=T.forwardRef((({className:e,...t},r)=>a.jsx(le,{ref:r,className:os("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:a.jsx(ce,{className:"flex items-center justify-center",children:a.jsx(Vt,{className:"h-2.5 w-2.5 fill-current text-current"})})})));di.displayName=le.displayName;const ui=({form:e})=>a.jsxs(a.Fragment,{children:[a.jsx(Yn,{control:e.control,name:"fullName",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Nome"}),a.jsx(ei,{children:a.jsx(si,{placeholder:"Seu nome completo",autoComplete:"name",...e})}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:e.control,name:"email",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Email"}),a.jsx(ei,{children:a.jsx(si,{placeholder:"<EMAIL>",type:"email",autoComplete:"email",...e})}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:e.control,name:"password",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Senha"}),a.jsx(ei,{children:a.jsx(si,{type:"password",autoComplete:"new-password",...e})}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:e.control,name:"formationArea",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Área de formação"}),a.jsx(ei,{children:a.jsx(si,{placeholder:"Ex: Medicina",autoComplete:"organization-title",...e})}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:e.control,name:"graduationYear",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Ano de formação"}),a.jsx(ei,{children:a.jsx(si,{placeholder:"Ex: 2024",type:"text",...e})}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:e.control,name:"type",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Tipo de usuário"}),a.jsx(ei,{children:a.jsxs(ci,{onValueChange:e.onChange,defaultValue:e.value,className:"flex flex-col space-y-1",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(di,{value:"student",id:"student"}),a.jsx("label",{htmlFor:"student",children:"Sou Estudante"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(di,{value:"professional",id:"professional"}),a.jsx("label",{htmlFor:"professional",children:"Sou Profissional"})]})]})}),a.jsx(ri,{})]})})]}),mi=T.forwardRef((({className:e,...t},r)=>a.jsx(de,{ref:r,className:os("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:a.jsx(ue,{className:os("flex items-center justify-center text-current"),children:a.jsx(Dt,{className:"h-4 w-4"})})})));mi.displayName=de.displayName;const hi=({form:e})=>a.jsxs(a.Fragment,{children:[a.jsx(Yn,{control:e.control,name:"isAdult",render:({field:e})=>a.jsxs(Jn,{className:"flex flex-row items-start space-x-3 space-y-0",children:[a.jsx(ei,{children:a.jsx(mi,{checked:e.value,onCheckedChange:e.onChange})}),a.jsx("div",{className:"space-y-1 leading-none",children:a.jsx(Zn,{children:"Tenho mais de 17 anos"})}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:e.control,name:"acceptTerms",render:({field:e})=>a.jsxs(Jn,{className:"flex flex-row items-start space-x-3 space-y-0",children:[a.jsx(ei,{children:a.jsx(mi,{checked:e.value,onCheckedChange:e.onChange})}),a.jsx("div",{className:"space-y-1 leading-none",children:a.jsxs(Zn,{children:["Aceito os ",a.jsx(q,{to:"/terms",className:"text-primary hover:underline",children:"termos e condições"})]})}),a.jsx(ri,{})]})})]}),pi=te({fullName:re().min(3,"Nome deve ter pelo menos 3 caracteres").max(100,"Nome muito longo").regex(/^[a-zA-ZÀ-ÿ\s]+$/,"Nome deve conter apenas letras e espaços").refine((e=>e.trim().split(" ").length>=2),"Digite seu nome completo (nome e sobrenome)"),email:re().email("Email inválido").min(5,"Email muito curto").max(100,"Email muito longo").refine((e=>!["test@","exemplo@","seuemail@","email@","user@"].some((t=>e.toLowerCase().includes(t)))),"Por favor, use um email válido").refine((e=>{const t=e.split("@")[1];return t&&t.includes(".")&&t.length>3}),"Domínio de email inválido"),password:re().min(8,"A senha deve ter pelo menos 8 caracteres").max(100,"Senha muito longa").refine((e=>!/^\s|\s$/.test(e)),"Senha não pode começar ou terminar com espaços").refine((e=>/[A-Za-z]/.test(e)),"Senha deve conter pelo menos uma letra").refine((e=>/[0-9]/.test(e)),"Senha deve conter pelo menos um número"),formationArea:re().min(2,"Área de formação muito curta"),graduationYear:re().min(4,"Ano de formação inválido"),type:ne(["student","professional"]),isAdult:ae().refine((e=>!0===e),"Você deve ter mais de 17 anos"),acceptTerms:ae().refine((e=>!0===e),"Você deve aceitar os termos")}),fi=({onModeChange:e,onSuccess:t})=>{const[r,s]=T.useState(!1),{showNotification:n}=$a(),i=()=>a.jsxs("div",{className:"text-center p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[a.jsx("p",{className:"text-sm text-blue-800",children:a.jsx("strong",{children:"💡 Cadastro com Google"})}),a.jsx("p",{className:"text-xs text-blue-700 mt-1",children:"Temporariamente indisponível no aplicativo. Use email e senha para criar sua conta."})]}),o=se({resolver:qn(pi),defaultValues:{fullName:"",email:"",password:"",formationArea:"",graduationYear:"",type:"professional",isAdult:!1,acceptTerms:!1}});return a.jsx("div",{className:"space-y-4 py-2 pb-4",children:a.jsx(Gn,{...o,children:a.jsxs("form",{onSubmit:o.handleSubmit((async e=>{try{s(!0);const{error:r}=await Ts.auth.signUp({email:e.email,password:e.password,options:{data:{full_name:e.fullName,formation_area:e.formationArea,graduation_year:e.graduationYear,is_student:"student"===e.type,is_professional:"professional"===e.type}}});if(r){if("User already registered"===r.message)return void n({title:"Email já cadastrado",description:"Este email já está em uso. Por favor, faça login ou use outro email.",type:"error",buttonText:"Entendi"});throw r}await(async(e,t)=>{try{if(!(await fetch("https://bxedpdmgvgatjdfxgxij.supabase.co/functions/v1/welcome-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,name:t})})).ok)return}catch(r){return}})(e.email,e.fullName),n({title:"Conta criada com sucesso!",description:"Seja bem-vindo.",type:"success",buttonText:"Continuar",onButtonClick:t})}catch(r){n({title:"Erro ao criar conta",description:"Verifique os dados e tente novamente.",type:"error",buttonText:"Tentar novamente"})}finally{s(!1)}})),className:"space-y-4 w-full",children:[a.jsx(ui,{form:o}),a.jsx(hi,{form:o}),a.jsxs("div",{className:"flex flex-col gap-2",children:[a.jsx(Es,{type:"submit",className:"w-full bg-primary text-white hover:bg-primary/90",disabled:r,children:r?"Criando conta...":"Criar conta"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("span",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:a.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"ou"})})]}),(()=>{const e=navigator.userAgent.toLowerCase(),t=/android/i.test(e),r=/wv/.test(e)||/webview/.test(e),s="Android"in window||"AndroidInterface"in window,a=/chrome/.test(e)&&!/edg/.test(e),n=/samsungbrowser/.test(e);return t&&(r||s||!a&&!n)})()?a.jsx(i,{}):a.jsx(ii,{mode:"signup",onSuccess:t,disabled:r}),a.jsx(Es,{type:"button",variant:"ghost",className:"w-full",onClick:e,children:"Já tem uma conta? Entre"})]})]})})})},gi=T.forwardRef((({className:e,...t},r)=>a.jsx("div",{ref:r,className:os("rounded-lg border border-gray-200 bg-white text-card-foreground shadow-sm dark:bg-slate-800 dark:border-gray-700 dark:text-gray-100",e),...t})));gi.displayName="Card";const xi=T.forwardRef((({className:e,...t},r)=>a.jsx("div",{ref:r,className:os("flex flex-col space-y-1.5 p-6",e),...t})));xi.displayName="CardHeader";const yi=T.forwardRef((({className:e,...t},r)=>a.jsx("h3",{ref:r,className:os("text-2xl font-semibold leading-none tracking-tight dark:text-gray-100",e),...t})));yi.displayName="CardTitle";const vi=T.forwardRef((({className:e,...t},r)=>a.jsx("p",{ref:r,className:os("text-sm text-muted-foreground dark:text-gray-400",e),...t})));vi.displayName="CardDescription";const bi=T.forwardRef((({className:e,...t},r)=>a.jsx("div",{ref:r,className:os("p-6 pt-0",e),...t})));bi.displayName="CardContent";const wi=T.forwardRef((({className:e,...t},r)=>a.jsx("div",{ref:r,className:os("flex items-center p-6 pt-0",e),...t})));wi.displayName="CardFooter";const ji=te({email:re().email("Email inválido").min(5,"Email muito curto").refine((e=>{const t=e.split("@")[1];return t&&t.includes(".")&&t.length>3}),"Domínio de email inválido")}),ki=te({code:re().length(6,"O código deve ter 6 dígitos"),password:re().min(8,"A senha deve ter pelo menos 8 caracteres").regex(/^(?=.*[a-zA-Z])(?=.*\d)/,"A senha deve conter pelo menos uma letra e um número"),confirmPassword:re()}).refine((e=>e.password===e.confirmPassword),{message:"As senhas não coincidem",path:["confirmPassword"]});function Ei({onBack:e}){const[t,r]=T.useState("email"),[s,n]=T.useState(""),[i,o]=T.useState(!1),[l,c]=T.useState(""),[d,u]=T.useState(["","","","","",""]),{showNotification:m}=$a(),h=T.useRef([]),p=se({resolver:qn(ji),defaultValues:{email:""}}),f=se({resolver:qn(ki),defaultValues:{code:"",password:"",confirmPassword:""}});T.useEffect((()=>{"code"===t&&(u(["","","","","",""]),f.setValue("code",""),f.setValue("password",""),f.setValue("confirmPassword",""),f.clearErrors())}),[t,f]);return"email"===t?a.jsxs(gi,{className:"w-full",children:[a.jsxs(xi,{className:"space-y-1",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx(Es,{variant:"ghost",size:"sm",onClick:e,className:"p-1 h-8 w-8",children:a.jsx(_t,{className:"h-4 w-4"})}),a.jsx("div",{className:"w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center",children:a.jsx(Qt,{className:"h-6 w-6 text-blue-600"})})]}),a.jsx(yi,{className:"text-center",children:"Recuperar Senha"}),a.jsx(vi,{className:"text-center",children:"Digite seu email para receber um código de recuperação"})]}),a.jsx(bi,{children:a.jsx(Gn,{...p,children:a.jsxs("form",{onSubmit:p.handleSubmit((async e=>{try{o(!0),c("");const{data:t,error:s}=await Ts.auth.resetPasswordForEmail(e.email,{redirectTo:`${window.location.origin}/reset-password`});if(s)throw s;n(e.email),r("code")}catch(t){c(t.message||"Erro ao enviar código")}finally{o(!1)}})),className:"space-y-4",children:[a.jsx(Yn,{control:p.control,name:"email",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Email"}),a.jsx(ei,{children:a.jsx(si,{placeholder:"<EMAIL>",type:"email",...e,className:"bg-gray-50 border-gray-200 focus:border-primary"})}),a.jsx(ri,{})]})}),l&&a.jsx("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded-lg",children:l}),a.jsx(Es,{type:"submit",className:"w-full bg-primary text-white hover:bg-primary/90",disabled:i,children:i?a.jsxs(a.Fragment,{children:[a.jsx(Gt,{className:"mr-2 h-4 w-4 animate-spin"}),"Enviando código..."]}):a.jsxs(a.Fragment,{children:[a.jsx(Qt,{className:"mr-2 h-4 w-4"}),"Enviar código"]})})]})})})]}):"code"===t?a.jsxs(gi,{className:"w-full overflow-hidden",children:[a.jsxs(xi,{className:"space-y-1",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx(Es,{variant:"ghost",size:"sm",onClick:()=>{r("email"),c(""),u(["","","","","",""]),p.reset(),f.reset()},className:"p-1 h-8 w-8",children:a.jsx(_t,{className:"h-4 w-4"})}),a.jsx("div",{className:"w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center",children:a.jsx(Kt,{className:"h-6 w-6 text-blue-600"})})]}),a.jsx(yi,{className:"text-center",children:"Digite o Código"}),a.jsxs(vi,{className:"text-center",children:["Código enviado para ",s]})]}),a.jsx(bi,{children:a.jsx(Gn,{...f,children:a.jsxs("form",{onSubmit:f.handleSubmit((async e=>{try{o(!0),c("");const{data:t,error:a}=await Ts.auth.verifyOtp({email:s,token:e.code,type:"recovery"});if(a)throw new Error("Código inválido ou expirado");if(!t.user)throw new Error("Erro na validação do código");const{error:n}=await Ts.auth.updateUser({password:e.password});if(n)throw n;r("success"),m({title:"Senha atualizada!",description:"Sua senha foi alterada com sucesso. Recarregando página...",type:"success",buttonText:"Continuar"}),setTimeout((()=>{window.location.reload()}),1500)}catch(t){try{await Ts.auth.signOut()}catch(a){}t.message?.includes("should be different")||t.message?.includes("same password")?(c("⚠️ A nova senha deve ser diferente da senha atual. Você foi deslogado por segurança. Solicite um novo código para tentar novamente."),setTimeout((()=>{r("email"),u(["","","","","",""]),f.reset(),c("")}),3e3)):t.message?.includes("weak")||t.message?.includes("too short")?(c("⚠️ Senha muito fraca. Você foi deslogado por segurança. Solicite um novo código para tentar novamente."),setTimeout((()=>{r("email"),u(["","","","","",""]),f.reset(),c("")}),3e3)):t.message?.includes("expired")||t.message?.includes("invalid")||t.message?.includes("Forbidden")?(c("🔒 Código expirado ou inválido. Solicite um novo código."),setTimeout((()=>{r("email"),u(["","","","","",""]),f.reset(),c("")}),2e3)):(c("❌ Erro inesperado. Você foi deslogado por segurança. Solicite um novo código."),setTimeout((()=>{r("email"),u(["","","","","",""]),f.reset(),c("")}),3e3))}finally{o(!1)}})),className:"space-y-6",children:[a.jsxs("div",{className:"space-y-3",children:[a.jsx("label",{className:"text-sm font-medium text-center block",children:"Código de verificação"}),a.jsx("div",{className:"flex gap-1 sm:gap-2 justify-center flex-wrap max-w-full",children:d.map(((e,t)=>a.jsx(si,{ref:e=>h.current[t]=e,type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>{const r=e.target.value.replace(/\D/g,"");if(r.length<=1){const e=[...d];e[t]=r,u(e),f.setValue("code",e.join("")),r&&t<5&&h.current[t+1]?.focus()}},onKeyDown:r=>{"Backspace"===r.key&&!e&&t>0&&h.current[t-1]?.focus()},className:"w-10 h-10 sm:w-12 sm:h-12 text-center text-lg sm:text-xl font-mono bg-gray-50 border-gray-200 focus:border-primary flex-shrink-0"},t)))})]}),a.jsx(Yn,{control:f.control,name:"password",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Nova senha"}),a.jsx(ei,{children:a.jsx(si,{type:"password",placeholder:"Nova senha (diferente da atual)",...e,className:"bg-gray-50 border-gray-200 focus:border-primary"})}),a.jsxs(ti,{className:"text-xs text-gray-500",children:["Mínimo 8 caracteres com letras e números. ",a.jsx("strong",{children:"Deve ser diferente da senha atual."})]}),a.jsx(ri,{})]})}),a.jsx(Yn,{control:f.control,name:"confirmPassword",render:({field:e})=>a.jsxs(Jn,{children:[a.jsx(Zn,{children:"Confirmar senha"}),a.jsx(ei,{children:a.jsx(si,{type:"password",placeholder:"Confirme sua nova senha",...e,className:"bg-gray-50 border-gray-200 focus:border-primary"})}),a.jsx(ri,{})]})}),l&&a.jsx("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded-lg",children:l}),a.jsx(Es,{type:"submit",className:"w-full bg-primary text-white hover:bg-primary/90",disabled:i,children:i?a.jsxs(a.Fragment,{children:[a.jsx(Gt,{className:"mr-2 h-4 w-4 animate-spin"}),"Atualizando senha..."]}):a.jsxs(a.Fragment,{children:[a.jsx(Kt,{className:"mr-2 h-4 w-4"}),"Atualizar senha"]})}),a.jsxs(Es,{type:"button",variant:"outline",onClick:()=>{r("email"),u(["","","","","",""]),c(""),f.reset()},className:"w-full mt-2",children:[a.jsx(Qt,{className:"mr-2 h-4 w-4"}),"Solicitar novo código"]})]})})})]}):a.jsx(gi,{className:"w-full",children:a.jsxs(bi,{className:"pt-6 pb-8 px-6 flex flex-col items-center text-center",children:[a.jsx("div",{className:"w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6",children:a.jsx(Ot,{className:"h-8 w-8 text-green-600"})}),a.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Senha Atualizada!"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Sua senha foi alterada com sucesso. Você pode fazer login agora."}),a.jsx(Es,{variant:"duolingo",className:"w-full",onClick:e,children:"Fazer login"})]})})}const Ai=({defaultOpen:e,onOpenChange:t,hidden:r,open:s,onSuccess:n}={})=>{const[i,o]=T.useState(e||!1),[l,c]=T.useState("signin"),d=e=>{c(e)},u=e=>{o(e),t?.(e)},m=()=>{u(!1),n?.()},h=void 0!==s?s:i;return a.jsxs(cs,{open:h,onOpenChange:u,children:[!r&&a.jsx(ds,{asChild:!0,children:a.jsxs(Es,{variant:"outline",className:"flex items-center gap-2",children:[a.jsx(ur,{className:"h-4 w-4"}),"Entrar"]})}),a.jsxs(ps,{className:"auth-dialog-content mobile-optimized",style:{top:window.innerWidth<=640?"50dvh":"50%",transform:(window.innerWidth,"translate(-50%, -50%)"),maxHeight:window.innerWidth<=640?"85dvh":"80dvh",width:window.innerWidth<=640?"95dvw":"calc(100vw-2rem)",maxWidth:window.innerWidth<=640?"450px":"550px",minHeight:"auto",overflow:"auto"},children:[a.jsxs(fs,{children:[a.jsx(xs,{className:"text-xl sm:text-2xl text-center",children:(()=>{switch(l){case"signin":return"Entrar na sua conta";case"signup":return"Criar sua conta";case"code-reset":return"Recuperar senha"}})()}),a.jsx(ys,{className:"text-center text-muted-foreground",children:(()=>{switch(l){case"signin":return"Faça login para acessar sua conta";case"signup":return"Crie sua conta para começar";case"code-reset":return"Receba um código de 6 dígitos por email"}})()})]}),"signin"===l?a.jsx(li,{onModeChange:()=>d("signup"),onResetPassword:()=>d("code-reset"),onSuccess:m}):"signup"===l?a.jsx(fi,{onModeChange:()=>d("signin"),onSuccess:m}):a.jsx(Ei,{onBack:()=>d("signin")}),a.jsx("p",{className:"text-[10px] text-muted-foreground/70 text-center mt-6 font-light leading-tight max-w-[80%] mx-auto",children:"Esta plataforma é voltada para profissionais de saúde e fornece suporte à prática clínica, sem substituir o julgamento médico ou diretrizes institucionais."})]})]})},Ci=({className:e,size:t="md",variant:r="default"})=>{const{theme:s,toggleTheme:n}=Rs();T.useEffect((()=>{}),[s]);const i={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"};return"icon"===r?a.jsx("button",{onClick:n,className:os("rounded-full flex items-center justify-center transition-all duration-300 relative","hover:bg-primary/10 dark:hover:bg-primary/20 focus:outline-none","dark"===s?"text-yellow-300":"text-gray-500",e),"aria-label":"dark"===s?"Alternar para modo claro":"Alternar para modo escuro",title:"dark"===s?"Alternar para modo claro":"Alternar para modo escuro",children:"dark"===s?a.jsx(lr,{className:i[t]}):a.jsx(Zt,{className:i[t]})}):a.jsx("button",{onClick:n,className:os({sm:"w-8 h-8",md:"w-10 h-10",lg:"w-12 h-12"}[t],"rounded-full flex items-center justify-center transition-all duration-300 relative","hover:bg-primary/10 dark:hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-primary/20 dark:focus:ring-blue-500/30","dark"===s?"text-yellow-300 bg-slate-800 hover:bg-slate-700 shadow-inner":"text-primary bg-white hover:bg-gray-100 shadow-sm",e),"aria-label":"dark"===s?"Alternar para modo claro":"Alternar para modo escuro",title:"dark"===s?"Alternar para modo claro":"Alternar para modo escuro",children:"dark"===s?a.jsx(lr,{className:i[t]}):a.jsx(Zt,{className:i[t]})})},_i=()=>{const e=Rs(),{data:t}=I({queryKey:["site-settings","v2"],queryFn:async()=>{const{data:e,error:t}=await Ts.from("pedbook_site_settings").select("*");if(t)throw t;return e.reduce(((e,t)=>(e[t.key]=t.value,e)),{})}});return a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(q,{to:"/",className:"flex items-center group shrink-0",children:a.jsx("img",{src:t?.logo_url||"/faviconx.webp",alt:"PedBook Logo",width:"48",height:"48",className:"h-10 w-10 transition-transform hover:scale-105",fetchpriority:"high",loading:"eager",decoding:"sync"})}),e&&a.jsx(Ci,{className:"ml-1",size:"sm"})]})};var Ni=.999,Si=/[\\\/_+.#"@\[\(\{&]/,Ti=/[\\\/_+.#"@\[\(\{&]/g,Pi=/[\s-]/,Di=/[\s-]/g;function Ri(e,t,r,s,a,n,i){if(n===t.length)return a===e.length?1:.99;var o=`${a},${n}`;if(void 0!==i[o])return i[o];for(var l,c,d,u,m=s.charAt(n),h=r.indexOf(m,a),p=0;h>=0;)(l=Ri(e,t,r,s,h+1,n+1,i))>p&&(h===a?l*=1:Si.test(e.charAt(h-1))?(l*=.8,(d=e.slice(a,h-1).match(Ti))&&a>0&&(l*=Math.pow(Ni,d.length))):Pi.test(e.charAt(h-1))?(l*=.9,(u=e.slice(a,h-1).match(Di))&&a>0&&(l*=Math.pow(Ni,u.length))):(l*=.17,a>0&&(l*=Math.pow(Ni,h-a))),e.charAt(h)!==t.charAt(n)&&(l*=.9999)),(l<.1&&r.charAt(h-1)===s.charAt(n+1)||s.charAt(n+1)===s.charAt(n)&&r.charAt(h-1)!==s.charAt(n))&&.1*(c=Ri(e,t,r,s,h+1,n+2,i))>l&&(l=.1*c),l>p&&(p=l),h=r.indexOf(m,h+1);return i[o]=p,p}function Mi(e){return e.toLowerCase().replace(Di," ")}function Ii(e,t,r){return Ri(e=r&&r.length>0?e+" "+r.join(" "):e,t,Mi(e),Mi(t),0,0,{})}function Li(){return Li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},Li.apply(null,arguments)}function Oi(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(null==e||e(s),!1===r||!s.defaultPrevented)return null==t?void 0:t(s)}}function Fi(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function Vi(...e){return T.useCallback(Fi(...e),e)}function zi(...e){const t=e[0];if(1===e.length)return t;const r=()=>{const r=e.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(e){const s=r.reduce(((t,{useScope:r,scopeName:s})=>({...t,...r(e)[`__scope${s}`]})),{});return T.useMemo((()=>({[`__scope${t.scopeName}`]:s})),[s])}};return r.scopeName=t.scopeName,r}const Bi=Boolean(null===globalThis||void 0===globalThis?void 0:globalThis.document)?T.useLayoutEffect:()=>{},$i=R["useId".toString()]||(()=>{});let Ui=0;function qi(e){const[t,r]=T.useState($i());return Bi((()=>{r((e=>null!=e?e:String(Ui++)))}),[e]),e||(t?`radix-${t}`:"")}function Wi(e){const t=T.useRef(e);return T.useEffect((()=>{t.current=e})),T.useMemo((()=>(...e)=>{var r;return null===(r=t.current)||void 0===r?void 0:r.call(t,...e)}),[])}const Hi=T.forwardRef(((e,t)=>{const{children:r,...s}=e,a=T.Children.toArray(r),n=a.find(Yi);if(n){const e=n.props.children,r=a.map((t=>t===n?T.Children.count(e)>1?T.Children.only(null):T.isValidElement(e)?e.props.children:null:t));return T.createElement(Gi,Li({},s,{ref:t}),T.isValidElement(e)?T.cloneElement(e,void 0,r):null)}return T.createElement(Gi,Li({},s,{ref:t}),r)}));Hi.displayName="Slot";const Gi=T.forwardRef(((e,t)=>{const{children:r,...s}=e;return T.isValidElement(r)?T.cloneElement(r,{...Qi(s,r.props),ref:t?Fi(t,r.ref):r.ref}):T.Children.count(r)>1?T.Children.only(null):null}));Gi.displayName="SlotClone";const Ki=({children:e})=>T.createElement(T.Fragment,null,e);function Yi(e){return T.isValidElement(e)&&e.type===Ki}function Qi(e,t){const r={...t};for(const s in t){const a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}const Xi=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((e,t)=>{const r=T.forwardRef(((e,r)=>{const{asChild:s,...a}=e,n=s?Hi:t;return T.useEffect((()=>{window[Symbol.for("radix-ui")]=!0}),[]),T.createElement(n,Li({},a,{ref:r}))}));return r.displayName=`Primitive.${t}`,{...e,[t]:r}}),{}),Ji="dismissableLayer.update";let Zi;const eo=T.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),to=T.forwardRef(((e,t)=>{var r;const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:a,onPointerDownOutside:n,onFocusOutside:i,onInteractOutside:o,onDismiss:l,...c}=e,d=T.useContext(eo),[u,m]=T.useState(null),h=null!==(r=null==u?void 0:u.ownerDocument)&&void 0!==r?r:null===globalThis||void 0===globalThis?void 0:globalThis.document,[,p]=T.useState({}),f=Vi(t,(e=>m(e))),g=Array.from(d.layers),[x]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),y=g.indexOf(x),v=u?g.indexOf(u):-1,b=d.layersWithOutsidePointerEventsDisabled.size>0,w=v>=y,j=function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const r=Wi(e),s=T.useRef(!1),a=T.useRef((()=>{}));return T.useEffect((()=>{const e=e=>{if(e.target&&!s.current){let s=function(){so("dismissableLayer.pointerDownOutside",r,n,{discrete:!0})};const n={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=s,t.addEventListener("click",a.current,{once:!0})):s()}else t.removeEventListener("click",a.current);s.current=!1},n=window.setTimeout((()=>{t.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(n),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}}),[t,r]),{onPointerDownCapture:()=>s.current=!0}}((e=>{const t=e.target,r=[...d.branches].some((e=>e.contains(t)));w&&!r&&(null==n||n(e),null==o||o(e),e.defaultPrevented||null==l||l())}),h),k=function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const r=Wi(e),s=T.useRef(!1);return T.useEffect((()=>{const e=e=>{e.target&&!s.current&&so("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)}),[t,r]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}((e=>{const t=e.target;[...d.branches].some((e=>e.contains(t)))||(null==i||i(e),null==o||o(e),e.defaultPrevented||null==l||l())}),h);return function(e,t=(null===globalThis||void 0===globalThis?void 0:globalThis.document)){const r=Wi(e);T.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)}),[r,t])}((e=>{v===d.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&l&&(e.preventDefault(),l()))}),h),T.useEffect((()=>{if(u)return s&&(0===d.layersWithOutsidePointerEventsDisabled.size&&(Zi=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(u)),d.layers.add(u),ro(),()=>{s&&1===d.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=Zi)}}),[u,h,s,d]),T.useEffect((()=>()=>{u&&(d.layers.delete(u),d.layersWithOutsidePointerEventsDisabled.delete(u),ro())}),[u,d]),T.useEffect((()=>{const e=()=>p({});return document.addEventListener(Ji,e),()=>document.removeEventListener(Ji,e)}),[]),T.createElement(Xi.div,Li({},c,{ref:f,style:{pointerEvents:b?w?"auto":"none":void 0,...e.style},onFocusCapture:Oi(e.onFocusCapture,k.onFocusCapture),onBlurCapture:Oi(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:Oi(e.onPointerDownCapture,j.onPointerDownCapture)}))}));function ro(){const e=new CustomEvent(Ji);document.dispatchEvent(e)}function so(e,t,r,{discrete:s}){const a=r.originalEvent.target,n=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),s?function(e,t){e&&S.flushSync((()=>e.dispatchEvent(t)))}(a,n):a.dispatchEvent(n)}const ao="focusScope.autoFocusOnMount",no="focusScope.autoFocusOnUnmount",io={bubbles:!1,cancelable:!0},oo=T.forwardRef(((e,t)=>{const{loop:r=!1,trapped:s=!1,onMountAutoFocus:a,onUnmountAutoFocus:n,...i}=e,[o,l]=T.useState(null),c=Wi(a),d=Wi(n),u=T.useRef(null),m=Vi(t,(e=>l(e))),h=T.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;T.useEffect((()=>{if(s){let e=function(e){if(h.paused||!o)return;const t=e.target;o.contains(t)?u.current=t:mo(u.current,{select:!0})},t=function(e){if(h.paused||!o)return;const t=e.relatedTarget;null!==t&&(o.contains(t)||mo(u.current,{select:!0}))},r=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&mo(o)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const s=new MutationObserver(r);return o&&s.observe(o,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),s.disconnect()}}}),[s,o,h.paused]),T.useEffect((()=>{if(o){ho.add(h);const e=document.activeElement;if(!o.contains(e)){const t=new CustomEvent(ao,io);o.addEventListener(ao,c),o.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){const r=document.activeElement;for(const s of e)if(mo(s,{select:t}),document.activeElement!==r)return}(lo(o).filter((e=>"A"!==e.tagName)),{select:!0}),document.activeElement===e&&mo(o))}return()=>{o.removeEventListener(ao,c),setTimeout((()=>{const t=new CustomEvent(no,io);o.addEventListener(no,d),o.dispatchEvent(t),t.defaultPrevented||mo(null!=e?e:document.body,{select:!0}),o.removeEventListener(no,d),ho.remove(h)}),0)}}}),[o,c,d,h]);const p=T.useCallback((e=>{if(!r&&!s)return;if(h.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){const t=e.currentTarget,[s,n]=function(e){const t=lo(e);return[co(t,e),co(t.reverse(),e)]}(t);s&&n?e.shiftKey||a!==n?e.shiftKey&&a===s&&(e.preventDefault(),r&&mo(n,{select:!0})):(e.preventDefault(),r&&mo(s,{select:!0})):a===t&&e.preventDefault()}}),[r,s,h.paused]);return T.createElement(Xi.div,Li({tabIndex:-1},i,{ref:m,onKeyDown:p}))}));function lo(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function co(e,t){for(const r of e)if(!uo(r,{upTo:t}))return r}function uo(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function mo(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}const ho=function(){let e=[];return{add(t){const r=e[0];t!==r&&(null==r||r.pause()),e=po(e,t),e.unshift(t)},remove(t){var r;e=po(e,t),null===(r=e[0])||void 0===r||r.resume()}}}();function po(e,t){const r=[...e],s=r.indexOf(t);return-1!==s&&r.splice(s,1),r}const fo=T.forwardRef(((e,t)=>{var r;const{container:s=(null===globalThis||void 0===globalThis||null===(r=globalThis.document)||void 0===r?void 0:r.body),...a}=e;return s?M.createPortal(T.createElement(Xi.div,Li({},a,{ref:t})),s):null})),go=e=>{const{present:t,children:r}=e,s=function(e){const[t,r]=T.useState(),s=T.useRef({}),a=T.useRef(e),n=T.useRef("none"),i=e?"mounted":"unmounted",[o,l]=function(e,t){return T.useReducer(((e,r)=>{const s=t[e][r];return null!=s?s:e}),e)}(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return T.useEffect((()=>{const e=xo(s.current);n.current="mounted"===o?e:"none"}),[o]),Bi((()=>{const t=s.current,r=a.current;if(r!==e){const s=n.current,i=xo(t);e?l("MOUNT"):"none"===i||"none"===(null==t?void 0:t.display)?l("UNMOUNT"):l(r&&s!==i?"ANIMATION_OUT":"UNMOUNT"),a.current=e}}),[e,l]),Bi((()=>{if(t){const e=e=>{const r=xo(s.current).includes(e.animationName);e.target===t&&r&&S.flushSync((()=>l("ANIMATION_END")))},r=e=>{e.target===t&&(n.current=xo(s.current))};return t.addEventListener("animationstart",r),t.addEventListener("animationcancel",e),t.addEventListener("animationend",e),()=>{t.removeEventListener("animationstart",r),t.removeEventListener("animationcancel",e),t.removeEventListener("animationend",e)}}l("ANIMATION_END")}),[t,l]),{isPresent:["mounted","unmountSuspended"].includes(o),ref:T.useCallback((e=>{e&&(s.current=getComputedStyle(e)),r(e)}),[])}}(t),a="function"==typeof r?r({present:s.isPresent}):T.Children.only(r),n=Vi(s.ref,a.ref);return"function"==typeof r||s.isPresent?T.cloneElement(a,{ref:n}):null};function xo(e){return(null==e?void 0:e.animationName)||"none"}go.displayName="Presence";let yo=0;function vo(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var bo=p(),wo=function(){},jo=T.forwardRef((function(e,t){var r=T.useRef(null),s=T.useState({onScrollCapture:wo,onWheelCapture:wo,onTouchMoveCapture:wo}),a=s[0],n=s[1],i=e.forwardProps,o=e.children,l=e.className,c=e.removeScrollBar,d=e.enabled,u=e.shards,m=e.sideCar,h=e.noIsolation,p=e.inert,y=e.allowPinchZoom,v=e.as,b=void 0===v?"div":v,w=f(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),j=m,k=g([r,t]),E=x(x({},w),a);return T.createElement(T.Fragment,null,d&&T.createElement(j,{sideCar:bo,removeScrollBar:c,shards:u,noIsolation:h,inert:p,setCallbacks:n,allowPinchZoom:!!y,lockRef:r}),i?T.cloneElement(T.Children.only(o),x(x({},E),{ref:k})):T.createElement(b,x({},E,{className:l,ref:k}),o))}));jo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},jo.classNames={fullWidth:v,zeroRight:y};var ko=!1;if("undefined"!=typeof window)try{var Eo=Object.defineProperty({},"passive",{get:function(){return ko=!0,!0}});window.addEventListener("test",Eo,Eo),window.removeEventListener("test",Eo,Eo)}catch(Hb){ko=!1}var Ao=!!ko&&{passive:!1},Co=function(e,t){var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===r[t])},_o=function(e,t){var r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),No(e,r)){var s=So(e,r);if(s[1]>s[2])return!0}r=r.parentNode}while(r&&r!==document.body);return!1},No=function(e,t){return"v"===e?function(e){return Co(e,"overflowY")}(t):function(e){return Co(e,"overflowX")}(t)},So=function(e,t){return"v"===e?[(r=t).scrollTop,r.scrollHeight,r.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var r},To=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Po=function(e){return[e.deltaX,e.deltaY]},Do=function(e){return e&&"current"in e?e.current:e},Ro=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Mo=0,Io=[];const Lo=k(bo,(function(e){var t=T.useRef([]),r=T.useRef([0,0]),s=T.useRef(),a=T.useState(Mo++)[0],n=T.useState((function(){return b()}))[0],i=T.useRef(e);T.useEffect((function(){i.current=e}),[e]),T.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=w([e.lockRef.current],(e.shards||[]).map(Do),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(a))})),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(a))}))}}}),[e.inert,e.lockRef.current,e.shards]);var o=T.useCallback((function(e,t){if("touches"in e&&2===e.touches.length)return!i.current.allowPinchZoom;var a,n=To(e),o=r.current,l="deltaX"in e?e.deltaX:o[0]-n[0],c="deltaY"in e?e.deltaY:o[1]-n[1],d=e.target,u=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var m=_o(u,d);if(!m)return!0;if(m?a=u:(a="v"===u?"h":"v",m=_o(u,d)),!m)return!1;if(!s.current&&"changedTouches"in e&&(l||c)&&(s.current=a),!a)return!0;var h=s.current||a;return function(e,t,r,s){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),n=a*s,i=r.target,o=t.contains(i),l=!1,c=n>0,d=0,u=0;do{var m=So(e,i),h=m[0],p=m[1]-m[2]-a*h;(h||p)&&No(e,i)&&(d+=p,u+=h),i=i.parentNode}while(!o&&i!==document.body||o&&(t.contains(i)||t===i));return c&&0===d?l=!0:c||0!==u||(l=!0),l}(h,t,e,"h"===h?l:c)}),[]),l=T.useCallback((function(e){var r=e;if(Io.length&&Io[Io.length-1]===n){var s="deltaY"in r?Po(r):To(r),a=t.current.filter((function(e){return e.name===r.type&&e.target===r.target&&(t=e.delta,a=s,t[0]===a[0]&&t[1]===a[1]);var t,a}))[0];if(a&&a.should)r.cancelable&&r.preventDefault();else if(!a){var l=(i.current.shards||[]).map(Do).filter(Boolean).filter((function(e){return e.contains(r.target)}));(l.length>0?o(r,l[0]):!i.current.noIsolation)&&r.cancelable&&r.preventDefault()}}}),[]),c=T.useCallback((function(e,r,s,a){var n={name:e,delta:r,target:s,should:a};t.current.push(n),setTimeout((function(){t.current=t.current.filter((function(e){return e!==n}))}),1)}),[]),d=T.useCallback((function(e){r.current=To(e),s.current=void 0}),[]),u=T.useCallback((function(t){c(t.type,Po(t),t.target,o(t,e.lockRef.current))}),[]),m=T.useCallback((function(t){c(t.type,To(t),t.target,o(t,e.lockRef.current))}),[]);T.useEffect((function(){return Io.push(n),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:m}),document.addEventListener("wheel",l,Ao),document.addEventListener("touchmove",l,Ao),document.addEventListener("touchstart",d,Ao),function(){Io=Io.filter((function(e){return e!==n})),document.removeEventListener("wheel",l,Ao),document.removeEventListener("touchmove",l,Ao),document.removeEventListener("touchstart",d,Ao)}}),[]);var h=e.removeScrollBar,p=e.inert;return T.createElement(T.Fragment,null,p?T.createElement(n,{styles:Ro(a)}):null,h?T.createElement(j,{gapMode:"margin"}):null)}));var Oo=T.forwardRef((function(e,t){return T.createElement(jo,x({},e,{ref:t,sideCar:Lo}))}));Oo.classNames=jo.classNames;const Fo="Dialog",[Vo,zo]=function(e,t=[]){let r=[];const s=()=>{const t=r.map((e=>T.createContext(e)));return function(r){const s=(null==r?void 0:r[e])||t;return T.useMemo((()=>({[`__scope${e}`]:{...r,[e]:s}})),[r,s])}};return s.scopeName=e,[function(t,s){const a=T.createContext(s),n=r.length;function i(t){const{scope:r,children:s,...i}=t,o=(null==r?void 0:r[e][n])||a,l=T.useMemo((()=>i),Object.values(i));return T.createElement(o.Provider,{value:l},s)}return r=[...r,s],i.displayName=t+"Provider",[i,function(r,i){const o=(null==i?void 0:i[e][n])||a,l=T.useContext(o);if(l)return l;if(void 0!==s)return s;throw new Error(`\`${r}\` must be used within \`${t}\``)}]},zi(s,...t)]}(Fo),[Bo,$o]=Vo(Fo),Uo="DialogPortal",[qo,Wo]=Vo(Uo,{forceMount:void 0}),Ho="DialogOverlay",Go=T.forwardRef(((e,t)=>{const r=Wo(Ho,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=$o(Ho,e.__scopeDialog);return n.modal?T.createElement(go,{present:s||n.open},T.createElement(Ko,Li({},a,{ref:t}))):null})),Ko=T.forwardRef(((e,t)=>{const{__scopeDialog:r,...s}=e,a=$o(Ho,r);return T.createElement(Oo,{as:Hi,allowPinchZoom:!0,shards:[a.contentRef]},T.createElement(Xi.div,Li({"data-state":el(a.open)},s,{ref:t,style:{pointerEvents:"auto",...s.style}})))})),Yo="DialogContent",Qo=T.forwardRef(((e,t)=>{const r=Wo(Yo,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=$o(Yo,e.__scopeDialog);return T.createElement(go,{present:s||n.open},n.modal?T.createElement(Xo,Li({},a,{ref:t})):T.createElement(Jo,Li({},a,{ref:t})))})),Xo=T.forwardRef(((e,t)=>{const r=$o(Yo,e.__scopeDialog),s=T.useRef(null),a=Vi(t,r.contentRef,s);return T.useEffect((()=>{const e=s.current;if(e)return E(e)}),[]),T.createElement(Zo,Li({},e,{ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Oi(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:Oi(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()})),onFocusOutside:Oi(e.onFocusOutside,(e=>e.preventDefault()))}))})),Jo=T.forwardRef(((e,t)=>{const r=$o(Yo,e.__scopeDialog),s=T.useRef(!1),a=T.useRef(!1);return T.createElement(Zo,Li({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(s.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),s.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));const o=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(o))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}}))})),Zo=T.forwardRef(((e,t)=>{const{__scopeDialog:r,trapFocus:s,onOpenAutoFocus:a,onCloseAutoFocus:n,...i}=e,o=$o(Yo,r),l=Vi(t,T.useRef(null));return T.useEffect((()=>{var e,t;const r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:vo()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:vo()),yo++,()=>{1===yo&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),yo--}}),[]),T.createElement(T.Fragment,null,T.createElement(oo,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:a,onUnmountAutoFocus:n},T.createElement(to,Li({role:"dialog",id:o.contentId,"aria-describedby":o.descriptionId,"aria-labelledby":o.titleId,"data-state":el(o.open)},i,{ref:l,onDismiss:()=>o.onOpenChange(!1)}))),!1)}));function el(e){return e?"open":"closed"}const tl=e=>{const{__scopeDialog:t,children:r,open:s,defaultOpen:a,onOpenChange:n,modal:i=!0}=e,o=T.useRef(null),l=T.useRef(null),[c=!1,d]=function({prop:e,defaultProp:t,onChange:r=()=>{}}){const[s,a]=function({defaultProp:e,onChange:t}){const r=T.useState(e),[s]=r,a=T.useRef(s),n=Wi(t);return T.useEffect((()=>{a.current!==s&&(n(s),a.current=s)}),[s,a,n]),r}({defaultProp:t,onChange:r}),n=void 0!==e,i=n?e:s,o=Wi(r);return[i,T.useCallback((t=>{if(n){const r="function"==typeof t?t(e):t;r!==e&&o(r)}else a(t)}),[n,e,a,o])]}({prop:s,defaultProp:a,onChange:n});return T.createElement(Bo,{scope:t,triggerRef:o,contentRef:l,contentId:qi(),titleId:qi(),descriptionId:qi(),open:c,onOpenChange:d,onOpenToggle:T.useCallback((()=>d((e=>!e))),[d]),modal:i},r)},rl=e=>{const{__scopeDialog:t,forceMount:r,children:s,container:a}=e,n=$o(Uo,t);return T.createElement(qo,{scope:t,forceMount:r},T.Children.map(s,(e=>T.createElement(go,{present:r||n.open},T.createElement(fo,{asChild:!0,container:a},e)))))},sl=Go,al=Qo;var nl='[cmdk-group=""]',il='[cmdk-group-items=""]',ol='[cmdk-item=""]',ll=`${ol}:not([aria-disabled="true"])`,cl="cmdk-item-select",dl="data-value",ul=(e,t,r)=>Ii(e,t,r),ml=T.createContext(void 0),hl=()=>T.useContext(ml),pl=T.createContext(void 0),fl=()=>T.useContext(pl),gl=T.createContext(void 0),xl=T.forwardRef(((e,t)=>{let r=Pl((()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",filtered:{count:0,items:new Map,groups:new Set}}})),s=Pl((()=>new Set)),a=Pl((()=>new Map)),n=Pl((()=>new Map)),i=Pl((()=>new Set)),o=Sl(e),{label:l,children:c,value:d,onValueChange:u,filter:m,shouldFilter:h,loop:p,disablePointerSelection:f=!1,vimBindings:g=!0,...x}=e,y=T.useId(),v=T.useId(),b=T.useId(),w=T.useRef(null),j=Il();Tl((()=>{if(void 0!==d){let e=d.trim();r.current.value=e,k.emit()}}),[d]),Tl((()=>{j(6,S)}),[]);let k=T.useMemo((()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>r.current,setState:(e,t,s)=>{var a,n,i;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)N(),C(),j(1,_);else if("value"===e&&(s||j(5,S),void 0!==(null==(a=o.current)?void 0:a.value))){let e=null!=t?t:"";return void(null==(i=(n=o.current).onValueChange)||i.call(n,e))}k.emit()}},emit:()=>{i.current.forEach((e=>e()))}})),[]),E=T.useMemo((()=>({value:(e,t,s)=>{var a;t!==(null==(a=n.current.get(e))?void 0:a.value)&&(n.current.set(e,{value:t,keywords:s}),r.current.filtered.items.set(e,A(t,s)),j(2,(()=>{C(),k.emit()})))},item:(e,t)=>(s.current.add(e),t&&(a.current.has(t)?a.current.get(t).add(e):a.current.set(t,new Set([e]))),j(3,(()=>{N(),C(),r.current.value||_(),k.emit()})),()=>{n.current.delete(e),s.current.delete(e),r.current.filtered.items.delete(e);let t=P();j(4,(()=>{N(),(null==t?void 0:t.getAttribute("id"))===e&&_(),k.emit()}))}),group:e=>(a.current.has(e)||a.current.set(e,new Set),()=>{n.current.delete(e),a.current.delete(e)}),filter:()=>o.current.shouldFilter,label:l||e["aria-label"],disablePointerSelection:f,listId:y,inputId:b,labelId:v,listInnerRef:w})),[]);function A(e,t){var s,a;let n=null!=(a=null==(s=o.current)?void 0:s.filter)?a:ul;return e?n(e,r.current.search,t):0}function C(){if(!r.current.search||!1===o.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach((r=>{let s=a.current.get(r),n=0;s.forEach((t=>{let r=e.get(t);n=Math.max(r,n)})),t.push([r,n])}));let s=w.current;D().sort(((t,r)=>{var s,a;let n=t.getAttribute("id"),i=r.getAttribute("id");return(null!=(s=e.get(i))?s:0)-(null!=(a=e.get(n))?a:0)})).forEach((e=>{let t=e.closest(il);t?t.appendChild(e.parentElement===t?e:e.closest(`${il} > *`)):s.appendChild(e.parentElement===s?e:e.closest(`${il} > *`))})),t.sort(((e,t)=>t[1]-e[1])).forEach((e=>{let t=w.current.querySelector(`${nl}[${dl}="${encodeURIComponent(e[0])}"]`);null==t||t.parentElement.appendChild(t)}))}function _(){let e=D().find((e=>"true"!==e.getAttribute("aria-disabled"))),t=null==e?void 0:e.getAttribute(dl);k.setState("value",t||void 0)}function N(){var e,t,i,l;if(!r.current.search||!1===o.current.shouldFilter)return void(r.current.filtered.count=s.current.size);r.current.filtered.groups=new Set;let c=0;for(let a of s.current){let s=A(null!=(t=null==(e=n.current.get(a))?void 0:e.value)?t:"",null!=(l=null==(i=n.current.get(a))?void 0:i.keywords)?l:[]);r.current.filtered.items.set(a,s),s>0&&c++}for(let[s,n]of a.current)for(let e of n)if(r.current.filtered.items.get(e)>0){r.current.filtered.groups.add(s);break}r.current.filtered.count=c}function S(){var e,t,r;let s=P();s&&((null==(e=s.parentElement)?void 0:e.firstChild)===s&&(null==(r=null==(t=s.closest(nl))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),s.scrollIntoView({block:"nearest"}))}function P(){var e;return null==(e=w.current)?void 0:e.querySelector(`${ol}[aria-selected="true"]`)}function D(){var e;return Array.from(null==(e=w.current)?void 0:e.querySelectorAll(ll))}function R(e){let t=D()[e];t&&k.setState("value",t.getAttribute(dl))}function M(e){var t;let r=P(),s=D(),a=s.findIndex((e=>e===r)),n=s[a+e];null!=(t=o.current)&&t.loop&&(n=a+e<0?s[s.length-1]:a+e===s.length?s[0]:s[a+e]),n&&k.setState("value",n.getAttribute(dl))}function I(e){let t,r=P(),s=null==r?void 0:r.closest(nl);for(;s&&!t;)s=e>0?_l(s,nl):Nl(s,nl),t=null==s?void 0:s.querySelector(ll);t?k.setState("value",t.getAttribute(dl)):M(e)}let L=()=>R(D().length-1),O=e=>{e.preventDefault(),e.metaKey?L():e.altKey?I(1):M(1)},F=e=>{e.preventDefault(),e.metaKey?R(0):e.altKey?I(-1):M(-1)};return T.createElement(Xi.div,{ref:t,tabIndex:-1,...x,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=x.onKeyDown)||t.call(x,e),!e.defaultPrevented)switch(e.key){case"n":case"j":g&&e.ctrlKey&&O(e);break;case"ArrowDown":O(e);break;case"p":case"k":g&&e.ctrlKey&&F(e);break;case"ArrowUp":F(e);break;case"Home":e.preventDefault(),R(0);break;case"End":e.preventDefault(),L();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=P();if(t){let e=new Event(cl);t.dispatchEvent(e)}}}}},T.createElement("label",{"cmdk-label":"",htmlFor:E.inputId,id:E.labelId,style:Ol},l),Ll(e,(e=>T.createElement(pl.Provider,{value:k},T.createElement(ml.Provider,{value:E},e)))))})),yl=T.forwardRef(((e,t)=>{var r,s;let a=T.useId(),n=T.useRef(null),i=T.useContext(gl),o=hl(),l=Sl(e),c=null!=(s=null==(r=l.current)?void 0:r.forceMount)?s:null==i?void 0:i.forceMount;Tl((()=>{if(!c)return o.item(a,null==i?void 0:i.id)}),[c]);let d=Ml(a,n,[e.value,e.children,n],e.keywords),u=fl(),m=Rl((e=>e.value&&e.value===d.current)),h=Rl((e=>!(!c&&!1!==o.filter())||!e.search||e.filtered.items.get(a)>0));function p(){var e,t;f(),null==(t=(e=l.current).onSelect)||t.call(e,d.current)}function f(){u.setState("value",d.current,!0)}if(T.useEffect((()=>{let t=n.current;if(t&&!e.disabled)return t.addEventListener(cl,p),()=>t.removeEventListener(cl,p)}),[h,e.onSelect,e.disabled]),!h)return null;let{disabled:g,value:x,onSelect:y,forceMount:v,keywords:b,...w}=e;return T.createElement(Xi.div,{ref:Dl([n,t]),...w,id:a,"cmdk-item":"",role:"option","aria-disabled":!!g,"aria-selected":!!m,"data-disabled":!!g,"data-selected":!!m,onPointerMove:g||o.disablePointerSelection?void 0:f,onClick:g?void 0:p},e.children)})),vl=T.forwardRef(((e,t)=>{let{heading:r,children:s,forceMount:a,...n}=e,i=T.useId(),o=T.useRef(null),l=T.useRef(null),c=T.useId(),d=hl(),u=Rl((e=>!(!a&&!1!==d.filter())||!e.search||e.filtered.groups.has(i)));Tl((()=>d.group(i)),[]),Ml(i,o,[e.value,e.heading,l]);let m=T.useMemo((()=>({id:i,forceMount:a})),[a]);return T.createElement(Xi.div,{ref:Dl([o,t]),...n,"cmdk-group":"",role:"presentation",hidden:!u||void 0},r&&T.createElement("div",{ref:l,"cmdk-group-heading":"","aria-hidden":!0,id:c},r),Ll(e,(e=>T.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?c:void 0},T.createElement(gl.Provider,{value:m},e)))))})),bl=T.forwardRef(((e,t)=>{let{alwaysRender:r,...s}=e,a=T.useRef(null),n=Rl((e=>!e.search));return r||n?T.createElement(Xi.div,{ref:Dl([a,t]),...s,"cmdk-separator":"",role:"separator"}):null})),wl=T.forwardRef(((e,t)=>{let{onValueChange:r,...s}=e,a=null!=e.value,n=fl(),i=Rl((e=>e.search)),o=Rl((e=>e.value)),l=hl(),c=T.useMemo((()=>{var e;let t=null==(e=l.listInnerRef.current)?void 0:e.querySelector(`${ol}[${dl}="${encodeURIComponent(o)}"]`);return null==t?void 0:t.getAttribute("id")}),[]);return T.useEffect((()=>{null!=e.value&&n.setState("search",e.value)}),[e.value]),T.createElement(Xi.input,{ref:t,...s,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":l.listId,"aria-labelledby":l.labelId,"aria-activedescendant":c,id:l.inputId,type:"text",value:a?e.value:i,onChange:e=>{a||n.setState("search",e.target.value),null==r||r(e.target.value)}})})),jl=T.forwardRef(((e,t)=>{let{children:r,label:s="Suggestions",...a}=e,n=T.useRef(null),i=T.useRef(null),o=hl();return T.useEffect((()=>{if(i.current&&n.current){let e,t=i.current,r=n.current,s=new ResizeObserver((()=>{e=requestAnimationFrame((()=>{let e=t.offsetHeight;r.style.setProperty("--cmdk-list-height",e.toFixed(1)+"px")}))}));return s.observe(t),()=>{cancelAnimationFrame(e),s.unobserve(t)}}}),[]),T.createElement(Xi.div,{ref:Dl([n,t]),...a,"cmdk-list":"",role:"listbox","aria-label":s,id:o.listId},Ll(e,(e=>T.createElement("div",{ref:Dl([i,o.listInnerRef]),"cmdk-list-sizer":""},e))))})),kl=T.forwardRef(((e,t)=>{let{open:r,onOpenChange:s,overlayClassName:a,contentClassName:n,container:i,...o}=e;return T.createElement(tl,{open:r,onOpenChange:s},T.createElement(rl,{container:i},T.createElement(sl,{"cmdk-overlay":"",className:a}),T.createElement(al,{"aria-label":e.label,"cmdk-dialog":"",className:n},T.createElement(xl,{ref:t,...o}))))})),El=T.forwardRef(((e,t)=>Rl((e=>0===e.filtered.count))?T.createElement(Xi.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null)),Al=T.forwardRef(((e,t)=>{let{progress:r,children:s,label:a="Loading...",...n}=e;return T.createElement(Xi.div,{ref:t,...n,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},Ll(e,(e=>T.createElement("div",{"aria-hidden":!0},e))))})),Cl=Object.assign(xl,{List:jl,Item:yl,Input:wl,Group:vl,Separator:bl,Dialog:kl,Empty:El,Loading:Al});function _l(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}function Nl(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}function Sl(e){let t=T.useRef(e);return Tl((()=>{t.current=e})),t}var Tl="undefined"==typeof window?T.useEffect:T.useLayoutEffect;function Pl(e){let t=T.useRef();return void 0===t.current&&(t.current=e()),t}function Dl(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function Rl(e){let t=fl(),r=()=>e(t.snapshot());return T.useSyncExternalStore(t.subscribe,r,r)}function Ml(e,t,r,s=[]){let a=T.useRef(),n=hl();return Tl((()=>{var i;let o=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():a.current}})(),l=s.map((e=>e.trim()));n.value(e,o,l),null==(i=t.current)||i.setAttribute(dl,o),a.current=o})),a}var Il=()=>{let[e,t]=T.useState(),r=Pl((()=>new Map));return Tl((()=>{r.current.forEach((e=>e())),r.current=new Map}),[e]),(e,s)=>{r.current.set(e,s),t({})}};function Ll({asChild:e,children:t},r){return e&&T.isValidElement(t)?T.cloneElement(function(e){let t=e.type;return"function"==typeof t?t(e.props):"render"in t?t.render(e.props):e}(t),{ref:t.ref},r(t.props.children)):r(t)}var Ol={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};const Fl=T.forwardRef((({className:e,...t},r)=>a.jsx(Cl,{ref:r,className:os("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...t})));Fl.displayName=Cl.displayName,T.forwardRef((({className:e,...t},r)=>a.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[a.jsx(rr,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),a.jsx(Cl.Input,{ref:r,className:os("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...t})]}))).displayName=Cl.Input.displayName;const Vl=T.forwardRef((({className:e,...t},r)=>a.jsx(Cl.List,{ref:r,className:os("overflow-y-auto overflow-x-hidden",e),...t})));Vl.displayName=Cl.List.displayName,T.forwardRef(((e,t)=>a.jsx(Cl.Empty,{ref:t,className:"py-6 text-center text-sm",...e}))).displayName=Cl.Empty.displayName;const zl=T.forwardRef((({className:e,headingClassName:t,...r},s)=>{const n=r.heading,i=t?{...r,heading:void 0}:r;return a.jsx(Cl.Group,{ref:s,className:os("overflow-hidden p-1 text-foreground",t?"":"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...i,children:n&&t?a.jsxs(a.Fragment,{children:[a.jsx("div",{"cmdk-group-heading":"",className:t,children:n}),r.children]}):r.children})}));zl.displayName=Cl.Group.displayName,T.forwardRef((({className:e,...t},r)=>a.jsx(Cl.Separator,{ref:r,className:os("-mx-1 h-px bg-border",e),...t}))).displayName=Cl.Separator.displayName;const Bl=T.forwardRef((({className:e,...t},r)=>a.jsx(Cl.Item,{ref:r,className:os("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",e),...t})));Bl.displayName=Cl.Item.displayName;const $l=Pe,Ul=De,ql=T.forwardRef((({className:e,align:t="center",sideOffset:r=4,...s},n)=>a.jsx(Se,{children:a.jsx(Te,{ref:n,align:t,sideOffset:r,className:os("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})})));ql.displayName=Te.displayName;let Wl=0;const Hl=new Map,Gl=e=>{if(Hl.has(e))return;const t=setTimeout((()=>{Hl.delete(e),Xl({type:"REMOVE_TOAST",toastId:e})}),1e6);Hl.set(e,t)},Kl=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case"DISMISS_TOAST":{const{toastId:r}=t;return r?Gl(r):e.toasts.forEach((e=>{Gl(e.id)})),{...e,toasts:e.toasts.map((e=>e.id===r||void 0===r?{...e,open:!1}:e))}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))}}},Yl=[];let Ql={toasts:[]};function Xl(e){Ql=Kl(Ql,e),Yl.forEach((e=>{e(Ql)}))}function Jl({...e}){const t=(Wl=(Wl+1)%Number.MAX_SAFE_INTEGER,Wl.toString()),r=()=>Xl({type:"DISMISS_TOAST",toastId:t});return Xl({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>Xl({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function Zl(){const[e,t]=T.useState(Ql);return T.useEffect((()=>(Yl.push(t),()=>{const e=Yl.indexOf(t);e>-1&&Yl.splice(e,1)})),[e]),{...e,toast:Jl,dismiss:e=>Xl({type:"DISMISS_TOAST",toastId:e})}}const ec=[{id:"benzodiazepinicos",name:"Benzodiazepínicos",type:"Sedativa-hipnótica",antidote:"Flumazenil",usage:"Reversão da depressão do SNC causada por benzodiazepínicos",precautions:["Contraindicado em pacientes com risco de convulsões (ex.: uso combinado com antidepressivos tricíclicos)","Monitorar a função respiratória durante a administração"],color:"bg-blue-50 hover:bg-blue-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de CUSTÓDIO, Viviane Imaculada do Carmo; CUPO, Palmira. Emergências Pediátricas - Intoxicações. Ribeirão Preto: Hospital das Clínicas da Faculdade de Medicina de Ribeirão Preto da Universidade de São Paulo, 2022."},{id:"opioides",name:"Opioides",type:"Narcótica",antidote:"Naloxona",usage:"Reversão de depressão respiratória induzida por opioides",precautions:["Observar possível recidiva dos sintomas, pois a meia-vida da naloxona é menor que a de muitos opioides"],color:"bg-purple-50 hover:bg-purple-100",requiresWeight:!0,requiresAge:!0,reference:"Adaptado de CUSTÓDIO, Viviane Imaculada do Carmo; CUPO, Palmira. Emergências Pediátricas - Intoxicações. Ribeirão Preto: Hospital das Clínicas da Faculdade de Medicina de Ribeirão Preto da Universidade de São Paulo, 2022."},{id:"anticolinergicos",name:"Anticolinérgicos",type:"Anticolinérgica",antidote:"Fisostigmina",usage:"Tratamento de delírio grave causado por intoxicação anticolinérgica",precautions:["Risco de convulsões e bradicardia se administrado muito rápido","Evitar em pacientes com intoxicação por antidepressivos tricíclicos"],color:"bg-emerald-50 hover:bg-emerald-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de SOCIEDADE DE PEDIATRIA DE SÃO PAULO. Intoxicações agudas na infância e adolescência. São Paulo, 2023."},{id:"simpatomimeticos",name:"Simpatomiméticos",type:"Simpatomimética",antidote:"Não há antídoto específico",usage:"Manejo sintomático e suporte",precautions:["Monitorar sinais vitais continuamente","Atenção especial à temperatura corporal"],examples:["Cocaína","Anfetaminas","Descongestionantes"],management:["Controle da agitação com benzodiazepínicos","Monitorização contínua de sinais vitais","Hipertermia: resfriamento físico"],color:"bg-red-50 hover:bg-red-100",requiresWeight:!1,requiresAge:!1,reference:"Adaptado de CUSTÓDIO, Viviane Imaculada do Carmo; CUPO, Palmira. Emergências Pediátricas - Intoxicações. Ribeirão Preto: Hospital das Clínicas da Faculdade de Medicina de Ribeirão Preto da Universidade de São Paulo, 2022."},{id:"colinergicos",name:"Colinérgicos",type:"Colinérgica",antidote:"Atropina e Pralidoxima",usage:"Tratamento de intoxicação por organofosforados e carbamatos",precautions:["Monitorar para sinais de crise colinérgica contínua","Avaliar necessidade de suporte ventilatório"],examples:["Organofosforados","Carbamatos"],color:"bg-orange-50 hover:bg-orange-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de SOCIEDADE DE PEDIATRIA DE SÃO PAULO. Intoxicações agudas na infância e adolescência. São Paulo, 2023."},{id:"metemoglobinemia",name:"Metemoglobinemia",type:"Metemoglobinêmica",antidote:"Azul de metileno",usage:"Tratamento de metemoglobinemia sintomática",precautions:["Contraindicado em pacientes com deficiência de G6PD","Monitorar níveis de metemoglobina"],examples:["Nitratos","Dapsona"],color:"bg-indigo-50 hover:bg-indigo-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de CUSTÓDIO, Viviane Imaculada do Carmo; CUPO, Palmira. Emergências Pediátricas - Intoxicações. Ribeirão Preto: Hospital das Clínicas da Faculdade de Medicina de Ribeirão Preto da Universidade de São Paulo, 2022."},{id:"paracetamol",name:"Paracetamol",type:"Hepatotóxica",antidote:"N-Acetilcisteína (NAC)",usage:"Prevenção de lesão hepática em intoxicação por paracetamol",precautions:["Iniciar tratamento idealmente em até 8 horas após a ingestão","Monitorar função hepática"],color:"bg-yellow-50 hover:bg-yellow-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de DEPARTAMENTO CIENTÍFICO DE TOXICOLOGIA. Atualização sobre intoxicações agudas por medicamentos de uso comum em pediatria. São Paulo, 2018."},{id:"antidepressivos_triciclicos",name:"Antidepressivos Tricíclicos",type:"Cardiotóxica",antidote:"Bicarbonato de sódio",usage:"Tratamento de cardiotoxicidade em intoxicação por antidepressivos tricíclicos",precautions:["Monitorar ECG para alterações de condução","Avaliar pH sanguíneo","Observar sinais de alcalose metabólica"],color:"bg-pink-50 hover:bg-pink-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de DEPARTAMENTO CIENTÍFICO DE TOXICOLOGIA. Atualização sobre intoxicações agudas por medicamentos de uso comum em pediatria. São Paulo, 2018."},{id:"betabloqueadores",name:"Bloqueadores Beta-adrenérgicos",type:"Cardiodespressora",antidote:"Glucagon",usage:"Tratamento de bradicardia e hipotensão por betabloqueadores",precautions:["Monitorar glicemia, devido ao risco de hiperglicemia","Monitorização cardíaca contínua","Avaliar sinais de insuficiência cardíaca"],color:"bg-rose-50 hover:bg-rose-100",requiresWeight:!0,requiresAge:!1,reference:"Adaptado de SOCIEDADE DE PEDIATRIA DE SÃO PAULO. Intoxicações agudas na infância e adolescência. São Paulo, 2023. DEPARTAMENTO CIENTÍFICO DE TOXICOLOGIA. Atualização sobre intoxicações agudas por medicamentos de uso comum em pediatria. São Paulo, 2018."}];function tc(e){return Array.isArray?Array.isArray(e):"[object Array]"===oc(e)}function rc(e){return"string"==typeof e}function sc(e){return"number"==typeof e}function ac(e){return"object"==typeof e}function nc(e){return null!=e}function ic(e){return!e.trim().length}function oc(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const lc=Object.prototype.hasOwnProperty;class cc{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach((e=>{let r=dc(e);this._keys.push(r),this._keyMap[r.id]=r,t+=r.weight})),this._keys.forEach((e=>{e.weight/=t}))}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function dc(e){let t=null,r=null,s=null,a=1,n=null;if(rc(e)||tc(e))s=e,t=uc(e),r=mc(e);else{if(!lc.call(e,"name"))throw new Error("Missing name property in key");const i=e.name;if(s=i,lc.call(e,"weight")&&(a=e.weight,a<=0))throw new Error((e=>`Property 'weight' in key '${e}' must be a positive integer`)(i));t=uc(i),r=mc(i),n=e.getFn}return{path:t,id:r,weight:a,src:s,getFn:n}}function uc(e){return tc(e)?e:e.split(".")}function mc(e){return tc(e)?e.join("."):e}var hc={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(e,t){let r=[],s=!1;const a=(e,t,n)=>{if(nc(e))if(t[n]){const i=e[t[n]];if(!nc(i))return;if(n===t.length-1&&(rc(i)||sc(i)||function(e){return!0===e||!1===e||function(e){return ac(e)&&null!==e}(e)&&"[object Boolean]"==oc(e)}(i)))r.push(function(e){return null==e?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-1/0?"-0":t}(e)}(i));else if(tc(i)){s=!0;for(let e=0,r=i.length;e<r;e+=1)a(i[e],t,n+1)}else t.length&&a(i,t,n+1)}else r.push(e)};return a(e,rc(t)?t.split("."):t,0),s?r:r[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const pc=/[^ ]+/g;class fc{constructor({getFn:e=hc.getFn,fieldNormWeight:t=hc.fieldNormWeight}={}){this.norm=function(e=1,t=3){const r=new Map,s=Math.pow(10,t);return{get(t){const a=t.match(pc).length;if(r.has(a))return r.get(a);const n=1/Math.pow(a,.5*e),i=parseFloat(Math.round(n*s)/s);return r.set(a,i),i},clear(){r.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach(((e,t)=>{this._keysMap[e.id]=t}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,rc(this.docs[0])?this.docs.forEach(((e,t)=>{this._addString(e,t)})):this.docs.forEach(((e,t)=>{this._addObject(e,t)})),this.norm.clear())}add(e){const t=this.size();rc(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,r=this.size();t<r;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!nc(e)||ic(e))return;let r={v:e,i:t,n:this.norm.get(e)};this.records.push(r)}_addObject(e,t){let r={i:t,$:{}};this.keys.forEach(((t,s)=>{let a=t.getFn?t.getFn(e):this.getFn(e,t.path);if(nc(a))if(tc(a)){let e=[];const t=[{nestedArrIndex:-1,value:a}];for(;t.length;){const{nestedArrIndex:r,value:s}=t.pop();if(nc(s))if(rc(s)&&!ic(s)){let t={v:s,i:r,n:this.norm.get(s)};e.push(t)}else tc(s)&&s.forEach(((e,r)=>{t.push({nestedArrIndex:r,value:e})}))}r.$[s]=e}else if(rc(a)&&!ic(a)){let e={v:a,n:this.norm.get(a)};r.$[s]=e}})),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function gc(e,t,{getFn:r=hc.getFn,fieldNormWeight:s=hc.fieldNormWeight}={}){const a=new fc({getFn:r,fieldNormWeight:s});return a.setKeys(e.map(dc)),a.setSources(t),a.create(),a}function xc(e,{errors:t=0,currentLocation:r=0,expectedLocation:s=0,distance:a=hc.distance,ignoreLocation:n=hc.ignoreLocation}={}){const i=t/e.length;if(n)return i;const o=Math.abs(s-r);return a?i+o/a:o?1:i}const yc=32;function vc(e){let t={};for(let r=0,s=e.length;r<s;r+=1){const a=e.charAt(r);t[a]=(t[a]||0)|1<<s-r-1}return t}const bc=String.prototype.normalize?e=>e.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):e=>e;class wc{constructor(e,{location:t=hc.location,threshold:r=hc.threshold,distance:s=hc.distance,includeMatches:a=hc.includeMatches,findAllMatches:n=hc.findAllMatches,minMatchCharLength:i=hc.minMatchCharLength,isCaseSensitive:o=hc.isCaseSensitive,ignoreDiacritics:l=hc.ignoreDiacritics,ignoreLocation:c=hc.ignoreLocation}={}){if(this.options={location:t,threshold:r,distance:s,includeMatches:a,findAllMatches:n,minMatchCharLength:i,isCaseSensitive:o,ignoreDiacritics:l,ignoreLocation:c},e=o?e:e.toLowerCase(),e=l?bc(e):e,this.pattern=e,this.chunks=[],!this.pattern.length)return;const d=(e,t)=>{this.chunks.push({pattern:e,alphabet:vc(e),startIndex:t})},u=this.pattern.length;if(u>yc){let e=0;const t=u%yc,r=u-t;for(;e<r;)d(this.pattern.substr(e,yc),e),e+=yc;if(t){const e=u-yc;d(this.pattern.substr(e),e)}}else d(this.pattern,0)}searchIn(e){const{isCaseSensitive:t,ignoreDiacritics:r,includeMatches:s}=this.options;if(e=t?e:e.toLowerCase(),e=r?bc(e):e,this.pattern===e){let t={isMatch:!0,score:0};return s&&(t.indices=[[0,e.length-1]]),t}const{location:a,distance:n,threshold:i,findAllMatches:o,minMatchCharLength:l,ignoreLocation:c}=this.options;let d=[],u=0,m=!1;this.chunks.forEach((({pattern:t,alphabet:r,startIndex:h})=>{const{isMatch:p,score:f,indices:g}=function(e,t,r,{location:s=hc.location,distance:a=hc.distance,threshold:n=hc.threshold,findAllMatches:i=hc.findAllMatches,minMatchCharLength:o=hc.minMatchCharLength,includeMatches:l=hc.includeMatches,ignoreLocation:c=hc.ignoreLocation}={}){if(t.length>yc)throw new Error("Pattern length exceeds max of 32.");const d=t.length,u=e.length,m=Math.max(0,Math.min(s,u));let h=n,p=m;const f=o>1||l,g=f?Array(u):[];let x;for(;(x=e.indexOf(t,p))>-1;){let e=xc(t,{currentLocation:x,expectedLocation:m,distance:a,ignoreLocation:c});if(h=Math.min(e,h),p=x+d,f){let e=0;for(;e<d;)g[x+e]=1,e+=1}}p=-1;let y=[],v=1,b=d+u;const w=1<<d-1;for(let k=0;k<d;k+=1){let s=0,n=b;for(;s<n;)xc(t,{errors:k,currentLocation:m+n,expectedLocation:m,distance:a,ignoreLocation:c})<=h?s=n:b=n,n=Math.floor((b-s)/2+s);b=n;let o=Math.max(1,m-n+1),l=i?u:Math.min(m+n,u)+d,x=Array(l+2);x[l+1]=(1<<k)-1;for(let i=l;i>=o;i-=1){let s=i-1,n=r[e.charAt(s)];if(f&&(g[s]=+!!n),x[i]=(x[i+1]<<1|1)&n,k&&(x[i]|=(y[i+1]|y[i])<<1|1|y[i+1]),x[i]&w&&(v=xc(t,{errors:k,currentLocation:s,expectedLocation:m,distance:a,ignoreLocation:c}),v<=h)){if(h=v,p=s,p<=m)break;o=Math.max(1,2*m-p)}}if(xc(t,{errors:k+1,currentLocation:m,expectedLocation:m,distance:a,ignoreLocation:c})>h)break;y=x}const j={isMatch:p>=0,score:Math.max(.001,v)};if(f){const e=function(e=[],t=hc.minMatchCharLength){let r=[],s=-1,a=-1,n=0;for(let i=e.length;n<i;n+=1){let i=e[n];i&&-1===s?s=n:i||-1===s||(a=n-1,a-s+1>=t&&r.push([s,a]),s=-1)}return e[n-1]&&n-s>=t&&r.push([s,n-1]),r}(g,o);e.length?l&&(j.indices=e):j.isMatch=!1}return j}(e,t,r,{location:a+h,distance:n,threshold:i,findAllMatches:o,minMatchCharLength:l,includeMatches:s,ignoreLocation:c});p&&(m=!0),u+=f,p&&g&&(d=[...d,...g])}));let h={isMatch:m,score:m?u/this.chunks.length:1};return m&&s&&(h.indices=d),h}}class jc{constructor(e){this.pattern=e}static isMultiMatch(e){return kc(e,this.multiRegex)}static isSingleMatch(e){return kc(e,this.singleRegex)}search(){}}function kc(e,t){const r=e.match(t);return r?r[1]:null}class Ec extends jc{constructor(e,{location:t=hc.location,threshold:r=hc.threshold,distance:s=hc.distance,includeMatches:a=hc.includeMatches,findAllMatches:n=hc.findAllMatches,minMatchCharLength:i=hc.minMatchCharLength,isCaseSensitive:o=hc.isCaseSensitive,ignoreDiacritics:l=hc.ignoreDiacritics,ignoreLocation:c=hc.ignoreLocation}={}){super(e),this._bitapSearch=new wc(e,{location:t,threshold:r,distance:s,includeMatches:a,findAllMatches:n,minMatchCharLength:i,isCaseSensitive:o,ignoreDiacritics:l,ignoreLocation:c})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class Ac extends jc{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,r=0;const s=[],a=this.pattern.length;for(;(t=e.indexOf(this.pattern,r))>-1;)r=t+a,s.push([t,r-1]);const n=!!s.length;return{isMatch:n,score:n?0:1,indices:s}}}const Cc=[class extends jc{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){const t=e===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},Ac,class extends jc{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){const t=e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends jc{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){const t=!e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends jc{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){const t=!e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends jc{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){const t=e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},class extends jc{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){const t=-1===e.indexOf(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},Ec],_c=Cc.length,Nc=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Sc=new Set([Ec.type,Ac.type]);const Tc=[];function Pc(e,t){for(let r=0,s=Tc.length;r<s;r+=1){let s=Tc[r];if(s.condition(e,t))return new s(e,t)}return new wc(e,t)}const Dc="$and",Rc="$path",Mc=e=>!(!e[Dc]&&!e.$or),Ic=e=>({[Dc]:Object.keys(e).map((t=>({[t]:e[t]})))});function Lc(e,t,{auto:r=!0}={}){const s=e=>{let a=Object.keys(e);const n=(e=>!!e[Rc])(e);if(!n&&a.length>1&&!Mc(e))return s(Ic(e));if((e=>!tc(e)&&ac(e)&&!Mc(e))(e)){const s=n?e[Rc]:a[0],i=n?e.$val:e[s];if(!rc(i))throw new Error((e=>`Invalid value for key ${e}`)(s));const o={keyId:mc(s),pattern:i};return r&&(o.searcher=Pc(i,t)),o}let i={children:[],operator:a[0]};return a.forEach((t=>{const r=e[t];tc(r)&&r.forEach((e=>{i.children.push(s(e))}))})),i};return Mc(e)||(e=Ic(e)),s(e)}function Oc(e,t){const r=e.matches;t.matches=[],nc(r)&&r.forEach((e=>{if(!nc(e.indices)||!e.indices.length)return;const{indices:r,value:s}=e;let a={indices:r,value:s};e.key&&(a.key=e.key.src),e.idx>-1&&(a.refIndex=e.idx),t.matches.push(a)}))}function Fc(e,t){t.score=e.score}class Vc{constructor(e,t={},r){this.options={...hc,...t},this.options.useExtendedSearch,this._keyStore=new cc(this.options.keys),this.setCollection(e,r)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof fc))throw new Error("Incorrect 'index' type");this._myIndex=t||gc(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){nc(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const t=[];for(let r=0,s=this._docs.length;r<s;r+=1){const a=this._docs[r];e(a,r)&&(this.removeAt(r),r-=1,s-=1,t.push(a))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){const{includeMatches:r,includeScore:s,shouldSort:a,sortFn:n,ignoreFieldNorm:i}=this.options;let o=rc(e)?rc(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(e,{ignoreFieldNorm:t=hc.ignoreFieldNorm}){e.forEach((e=>{let r=1;e.matches.forEach((({key:e,norm:s,score:a})=>{const n=e?e.weight:null;r*=Math.pow(0===a&&n?Number.EPSILON:a,(n||1)*(t?1:s))})),e.score=r}))}(o,{ignoreFieldNorm:i}),a&&o.sort(n),sc(t)&&t>-1&&(o=o.slice(0,t)),function(e,t,{includeMatches:r=hc.includeMatches,includeScore:s=hc.includeScore}={}){const a=[];return r&&a.push(Oc),s&&a.push(Fc),e.map((e=>{const{idx:r}=e,s={item:t[r],refIndex:r};return a.length&&a.forEach((t=>{t(e,s)})),s}))}(o,this._docs,{includeMatches:r,includeScore:s})}_searchStringList(e){const t=Pc(e,this.options),{records:r}=this._myIndex,s=[];return r.forEach((({v:e,i:r,n:a})=>{if(!nc(e))return;const{isMatch:n,score:i,indices:o}=t.searchIn(e);n&&s.push({item:e,idx:r,matches:[{score:i,value:e,norm:a,indices:o}]})})),s}_searchLogical(e){const t=Lc(e,this.options),r=(e,t,s)=>{if(!e.children){const{keyId:r,searcher:a}=e,n=this._findMatches({key:this._keyStore.get(r),value:this._myIndex.getValueForItemAtKeyId(t,r),searcher:a});return n&&n.length?[{idx:s,item:t,matches:n}]:[]}const a=[];for(let n=0,i=e.children.length;n<i;n+=1){const i=e.children[n],o=r(i,t,s);if(o.length)a.push(...o);else if(e.operator===Dc)return[]}return a},s=this._myIndex.records,a={},n=[];return s.forEach((({$:e,i:s})=>{if(nc(e)){let i=r(t,e,s);i.length&&(a[s]||(a[s]={idx:s,item:e,matches:[]},n.push(a[s])),i.forEach((({matches:e})=>{a[s].matches.push(...e)})))}})),n}_searchObjectList(e){const t=Pc(e,this.options),{keys:r,records:s}=this._myIndex,a=[];return s.forEach((({$:e,i:s})=>{if(!nc(e))return;let n=[];r.forEach(((r,s)=>{n.push(...this._findMatches({key:r,value:e[s],searcher:t}))})),n.length&&a.push({idx:s,item:e,matches:n})})),a}_findMatches({key:e,value:t,searcher:r}){if(!nc(t))return[];let s=[];if(tc(t))t.forEach((({v:t,i:a,n:n})=>{if(!nc(t))return;const{isMatch:i,score:o,indices:l}=r.searchIn(t);i&&s.push({score:o,key:e,value:t,idx:a,norm:n,indices:l})}));else{const{v:a,n:n}=t,{isMatch:i,score:o,indices:l}=r.searchIn(a);i&&s.push({score:o,key:e,value:a,norm:n,indices:l})}return s}}Vc.version="7.1.0",Vc.createIndex=gc,Vc.parseIndex=function(e,{getFn:t=hc.getFn,fieldNormWeight:r=hc.fieldNormWeight}={}){const{keys:s,records:a}=e,n=new fc({getFn:t,fieldNormWeight:r});return n.setKeys(s),n.setIndexRecords(a),n},Vc.config=hc,Vc.parseQuery=Lc,function(...e){Tc.push(...e)}(class{constructor(e,{isCaseSensitive:t=hc.isCaseSensitive,ignoreDiacritics:r=hc.ignoreDiacritics,includeMatches:s=hc.includeMatches,minMatchCharLength:a=hc.minMatchCharLength,ignoreLocation:n=hc.ignoreLocation,findAllMatches:i=hc.findAllMatches,location:o=hc.location,threshold:l=hc.threshold,distance:c=hc.distance}={}){this.query=null,this.options={isCaseSensitive:t,ignoreDiacritics:r,includeMatches:s,minMatchCharLength:a,findAllMatches:i,ignoreLocation:n,location:o,threshold:l,distance:c},e=t?e:e.toLowerCase(),e=r?bc(e):e,this.pattern=e,this.query=function(e,t={}){return e.split("|").map((e=>{let r=e.trim().split(Nc).filter((e=>e&&!!e.trim())),s=[];for(let a=0,n=r.length;a<n;a+=1){const e=r[a];let n=!1,i=-1;for(;!n&&++i<_c;){const r=Cc[i];let a=r.isMultiMatch(e);a&&(s.push(new r(a,t)),n=!0)}if(!n)for(i=-1;++i<_c;){const r=Cc[i];let a=r.isSingleMatch(e);if(a){s.push(new r(a,t));break}}}return s}))}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){const t=this.query;if(!t)return{isMatch:!1,score:1};const{includeMatches:r,isCaseSensitive:s,ignoreDiacritics:a}=this.options;e=s?e:e.toLowerCase(),e=a?bc(e):e;let n=0,i=[],o=0;for(let l=0,c=t.length;l<c;l+=1){const s=t[l];i.length=0,n=0;for(let t=0,a=s.length;t<a;t+=1){const a=s[t],{isMatch:l,indices:c,score:d}=a.search(e);if(!l){o=0,n=0,i.length=0;break}if(n+=1,o+=d,r){const e=a.constructor.type;Sc.has(e)?i=[...i,...c]:i.push(c)}}if(n){let e={isMatch:!0,score:o/n};return r&&(e.indices=i),e}}return{isMatch:!1,score:1}}});const zc=(e,t,r,s=10,a=.6)=>{if(!t||t.length<2)return[];const n=((e,t)=>new Vc(e,{keys:t,threshold:.4,distance:100,minMatchCharLength:2,includeScore:!0,includeMatches:!0,ignoreLocation:!0,findAllMatches:!0,useExtendedSearch:!0}))(e,r),i=(e=>{const t=[e],r=e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s]/g,"").trim();r!==e.toLowerCase()&&t.push(r);const s=[e.replace(/([a-z])([a-z])/g,"$2$1"),e.substring(1),e.substring(0,e.length-1),e.replace(/([aeiou])/g,"$1$1"),e.replace(/ph/g,"f"),e.replace(/k/g,"c"),e.replace(/y/g,"i")];return t.push(...s.filter((t=>t&&t!==e))),[...new Set(t)]})(t),o=[];return i.forEach((e=>{n.search(e).forEach((e=>{void 0!==e.score&&e.score<a&&o.push({item:e.item,score:e.score,matches:e.matches})}))})),o.filter(((e,t,r)=>t===r.findIndex((t=>JSON.stringify(t.item)===JSON.stringify(e.item))))).sort(((e,t)=>e.score-t.score)).slice(0,s)},Bc=[{id:"apgar",name:"Calculadora de Apgar",path:"/calculadoras/apgar"},{id:"rodwell",name:"Calculadora de Rodwell",path:"/calculadoras/rodwell"},{id:"capurro",name:"Calculadora de Capurro",path:"/calculadoras/capurro"},{id:"capurro-neuro",name:"Calculadora de Capurro Neurológico",path:"/calculadoras/capurro-neuro"},{id:"finnegan",name:"Calculadora de Finnegan",path:"/calculadoras/finnegan"},{id:"gina",name:"Calculadora GINA",path:"/calculadoras/gina"},{id:"glasgow",name:"Calculadora de Glasgow",path:"/calculadoras/glasgow"},{id:"bmi",name:"Calculadora de IMC",path:"/calculadoras/imc"},{id:"sri",name:"Calculadora de SRI",path:"/calculadoras/sri"}],$c=[{id:"dengue",name:"Fluxograma de Dengue",path:"/flowcharts/dengue",description:"Manejo de casos suspeitos de dengue"},{id:"asthma",name:"Fluxograma de Crise Asmática",path:"/flowcharts/asthma",description:"Manejo de crise asmática em pediatria"},{id:"dka",name:"Fluxograma de Cetoacidose Diabética",path:"/flowcharts/dka",description:"Manejo de cetoacidose diabética em pediatria"},{id:"anaphylaxis",name:"Fluxograma de Anafilaxia",path:"/flowcharts/anaphylaxis",description:"Manejo de anafilaxia em pediatria"},{id:"seizure",name:"Fluxograma de Crise Convulsiva",path:"/flowcharts/seizure",description:"Manejo de crise convulsiva em pediatria"},{id:"pecarn",name:"Fluxograma PECARN - Trauma Craniano",path:"/flowcharts/pecarn",description:"Avaliação de trauma craniano em pediatria"},{id:"hydration",name:"Hidratação Venosa de Manutenção",path:"/calculadoras/hidratacao",description:"Cálculo da hidratação venosa (Holliday-Segar)"},{id:"scorpion",name:"Fluxograma de Acidente Escorpiônico (Escorpião Amarelo)",path:"/flowcharts/venomous/scorpion",description:"Manejo de acidentes com escorpião"},{id:"bothropic",name:"Fluxograma de Acidente Botrópico (Jararaca)",path:"/flowcharts/venomous/bothropic",description:"Manejo de acidentes com jararaca"},{id:"crotalic",name:"Fluxograma de Acidente Crotálico (Cascavel)",path:"/flowcharts/venomous/crotalic",description:"Manejo de acidentes com cascavel"},{id:"elapidic",name:"Fluxograma de Acidente Elapídico (Coral Verdadeira)",path:"/flowcharts/venomous/elapidic",description:"Manejo de acidentes com coral verdadeira"},{id:"phoneutria",name:"Fluxograma de Acidente Fonêutrico (Aranha Armadeira)",path:"/flowcharts/venomous/phoneutria",description:"Manejo de acidentes com aranha armadeira"},{id:"loxoscelic",name:"Fluxograma de Acidente Loxoscélico (Aranha Marrom)",path:"/flowcharts/venomous/loxoscelic",description:"Manejo de acidentes com aranha marrom"}],Uc=[{id:"growth",name:"Curvas de Crescimento",path:"/puericultura/curva-de-crescimento"},{id:"vaccines",name:"Vacinas",path:"/puericultura/calendario-vacinal"},{id:"formulas",name:"Fórmulas Infantis",path:"/puericultura/formulas"},{id:"supplementation",name:"Suplementação",path:"/puericultura/suplementacao-infantil"}],qc=e=>e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,""),Wc=(e,t,r,s=10)=>e.filter((e=>{const s=qc(e.name),a=e.description?qc(e.description):"";return e.name.toLowerCase().includes(t.toLowerCase())||s.includes(r)||e.description&&(e.description.toLowerCase().includes(t.toLowerCase())||a.includes(r))})).slice(0,s),Hc=T.memo((({searchTerm:e,setSearchTerm:t,onFocus:r,customPlaceholder:s,inputRef:n})=>a.jsxs("div",{className:"relative group",children:[a.jsx("div",{className:"absolute inset-0 bg-primary/10 rounded-full blur-md opacity-30 group-hover:opacity-50 transition-opacity"}),a.jsx(rr,{className:"absolute left-4 top-1/2 -translate-y-1/2 text-primary/70 group-hover:text-primary transition-colors",size:18}),a.jsx("input",{ref:n,type:"text",placeholder:s||"Buscar medicamentos, bulas, condutas, calculadoras...",value:e,onChange:e=>t(e.target.value),onFocus:r,onClick:e=>{e.stopPropagation()},className:"w-full pl-12 sm:pr-4 pr-12 py-3 rounded-full border-none focus:outline-none focus:ring-2 focus:ring-primary/30\r\n                  bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md transition-all\r\n                  text-sm text-gray-700 dark:text-gray-200"}),a.jsx(q,{to:"/",className:"absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 flex items-center justify-center sm:hidden",children:a.jsx("img",{src:"/faviconx.webp",alt:"PedBook Logo",width:"32",height:"32",className:"h-8 w-8 transition-transform hover:scale-105",fetchpriority:"high",loading:"eager",decoding:"sync"})}),e&&a.jsx(Es,{variant:"ghost",size:"icon",className:"absolute sm:right-2 right-11 top-1/2 -translate-y-1/2 h-7 w-7 rounded-full bg-gray-100/80 dark:bg-slate-700/80 hover:bg-gray-200 dark:hover:bg-slate-600",onClick:()=>{t(""),n.current?.focus()},children:a.jsx(mr,{className:"h-3.5 w-3.5 text-gray-500 dark:text-gray-400"})})]}))),Gc=e=>{if(!e)return"";const t=e.split(",").map((e=>e.trim()));return t.slice(0,3).join(", ")+(t.length>3?"...":"")};function Kc(e,t){const[r,s]=T.useState(e);return T.useEffect((()=>{const r=setTimeout((()=>{s(e)}),t);return()=>{clearTimeout(r)}}),[e,t]),r}const Yc=T.lazy((()=>B((()=>import("./SearchResults-Sp4oVNId.js")),__vite__mapDeps([15,1,2,16,4,5,17,18,19,3,6,7,8,9,10,11,12])).then((e=>({default:e.SearchResults}))))),Qc=({customPlaceholder:e})=>{const t=U(),[r,s]=T.useState(""),[n,i]=T.useState(!1),o=T.useRef(null),l=Kc(r,300),{searchResults:c,isLoading:d,isFetching:u}=(e=>{const{toast:t}=Zl(),r=e.trim().toLowerCase(),{data:s=[],isLoading:a,isFetching:n}=I({queryKey:["search",r],queryFn:async()=>{if(!r||r.length<3)return[];try{const e=qc(r),t=r.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,""),s=e.replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,""),a=((e,t,r,s)=>{const a=[`name.ilike.%${e}%`,`brands.ilike.%${e}%`,`name.ilike.%${t}%`,`brands.ilike.%${t}%`,`slug.eq.${s}`,`slug.ilike.%${r}%`];return t.split(/\s+/).filter((e=>e.length>=3)).forEach((e=>{a.push(`name.ilike.%${e}%`),a.push(`brands.ilike.%${e}%`),a.push(`slug.ilike.%${e}%`)})),a.join(",")})(r,e,s,t),n=((e,t,r,s)=>{const a=[`title.ilike.%${e}%`,`title.ilike.%${t}%`,`slug.eq.${s}`,`slug.ilike.%${r}%`];return t.split(/\s+/).filter((e=>e.length>=3)).forEach((e=>{a.push(`title.ilike.%${e}%`),a.push(`slug.ilike.%${e}%`)})),a.join(",")})(r,e,s,t),i=async()=>{const[e,t]=await Promise.all([Ts.from("pedbook_conducts_summaries").select("\n                id,\n                title,\n                slug,\n                topic_id,\n                pedbook_conducts_topics!inner (\n                  id,\n                  name,\n                  slug,\n                  category_id,\n                  pedbook_conducts_categories!inner (\n                    id,\n                    name,\n                    slug\n                  )\n                )\n              ").or(n).eq("published",!0).order("title").limit(10),Ts.from("pedbook_conducts_optimized").select("\n                id,\n                title,\n                slug,\n                topic_id,\n                pedbook_conducts_topics!inner (\n                  id,\n                  name,\n                  slug,\n                  category_id,\n                  pedbook_conducts_categories!inner (\n                    id,\n                    name,\n                    slug\n                  )\n                )\n              ").or(n).eq("published",!0).order("title").limit(10)]);return{traditional:e.data||[],optimized:t.data||[],error:e.error||t.error}},[{data:o,error:l},{data:c,error:d},{data:u,error:m},{data:h,error:p},f,{data:g,error:x}]=await Promise.all([Ts.from("pedbook_medications").select("\n              id,\n              name,\n              brands,\n              slug,\n              pedbook_medication_categories (\n                id,\n                name\n              )\n            ").or(a).order("name").limit(20),Ts.from("pedbook_medication_categories").select("id, name").or(`name.ilike.%${r}%,name.ilike.%${e}%`).order("name").limit(10),Ts.from("unified_cids").select("id, code, name, description").or(`name.ilike.%${r}%,code.ilike.%${r}%,name.ilike.%${e}%`).order("code").limit(15),Ts.from("pedbook_vaccines").select("id, name").ilike("name",`%${r}%`).order("name").limit(10),i(),Ts.rpc("search_professional_leaflets",{search_query:r,limit_results:8})]),y=f.traditional||[],v=f.optimized||[];if(l)throw l;if(d)throw d;if(m)throw m;if(p)throw p;if(f.error)throw f.error;if(x)throw x;const b=ec.filter((t=>{const s=r.toLowerCase(),a=qc(t.name),n=qc(t.antidote),i=t.name.toLowerCase().includes(s)||a.includes(e),o=t.antidote.toLowerCase().includes(s)||n.includes(e);return i||o})).slice(0,10).map((e=>({id:e.id,name:e.name,type:"toxidrome",path:`/poisonings/${e.id}`,description:`Antídoto: ${e.antidote}`}))),w=Wc(Bc,r,e,8).map((e=>({...e,type:"calculator"}))),j=Wc($c,r,e,10).map((e=>({...e,type:"flowchart"}))),k=Wc(Uc,r,e,5).map((e=>({...e,type:"childcare"}))),E=(o?.length||0)+(c?.length||0)+(w.length||0)+(j.length||0)+(k.length||0)+(y?.length||0)+(v?.length||0)+(h?.length||0)+(u?.length||0)+(g?.length||0);let A={medications:[],categories:[],calculators:[],flowcharts:[],childcare:[],conducts:[],vaccines:[],icd10:[],leaflets:[]};if(0===E||E<2&&r.length>4){const e=async()=>{const[e,t]=await Promise.all([Ts.from("pedbook_conducts_summaries").select("id, title, slug").eq("published",!0).limit(25),Ts.from("pedbook_conducts_optimized").select("id, title, slug").eq("published",!0).limit(25)]);return[...e.data||[],...t.data||[]]},[{data:t},{data:s},a,{data:n},{data:i},{data:l}]=await Promise.all([Ts.from("pedbook_medications").select("id, name, brands, slug, pedbook_medication_categories(id, name)").limit(150),Ts.from("pedbook_medication_categories").select("id, name").limit(50),e(),Ts.from("pedbook_vaccines").select("id, name").limit(50),Ts.from("pedbook_icd10_categories").select("id, name, code_range").limit(100),Ts.from("pedbook_medication_instructions").select("\n                id,\n                medication_id,\n                pedbook_medications!inner(id, name, slug, brands)\n              ").eq("is_published",!0).limit(100)]);A=((e,t,r=3)=>{const s={medications:[],categories:[],calculators:[],flowcharts:[],childcare:[],conducts:[],vaccines:[],icd10:[],leaflets:[]};if(e.medications.length>0){const a=zc(e.medications,t,["name","brands"],r,.6);s.medications=a.map((e=>e.item))}if(e.categories.length>0){const a=zc(e.categories,t,["name"],r,.5);s.categories=a.map((e=>e.item))}if(e.calculators.length>0){const a=zc(e.calculators,t,["name","description"],r,.5);s.calculators=a.map((e=>e.item))}if(e.flowcharts.length>0){const a=zc(e.flowcharts,t,["name","description"],r,.5);s.flowcharts=a.map((e=>e.item))}if(e.childcare.length>0){const a=zc(e.childcare,t,["name","description"],r,.5);s.childcare=a.map((e=>e.item))}if(e.conducts.length>0){const a=zc(e.conducts,t,["name"],r,.5);s.conducts=a.map((e=>e.item))}if(e.vaccines.length>0){const a=zc(e.vaccines,t,["name"],r,.5);s.vaccines=a.map((e=>e.item))}if(e.icd10.length>0){const a=zc(e.icd10,t,["name","code_range"],r,.5);s.icd10=a.map((e=>e.item))}if(e.leaflets&&e.leaflets.length>0){const a=zc(e.leaflets,t,["title","active_ingredient","medication_name","manufacturer"],r,.6);s.leaflets=a.map((e=>e.item))}return s})({medications:t||[],categories:s||[],calculators:Bc,flowcharts:$c,childcare:Uc,conducts:a||[],vaccines:n||[],icd10:i||[],leaflets:l||[]},r,3),A.medications=A.medications.filter((e=>!o?.some((t=>t.id===e.id)))),A.categories=A.categories.filter((e=>!c?.some((t=>t.id===e.id)))),A.conducts=A.conducts.filter((e=>!y?.some((t=>t.id===e.id))&&!v?.some((t=>t.id===e.id)))),A.vaccines=A.vaccines.filter((e=>!h?.some((t=>t.id===e.id)))),A.icd10=A.icd10.filter((e=>!u?.some((t=>t.id===e.id)))),A.leaflets=A.leaflets.filter((e=>!g?.some((t=>t.id===e.id)))),A.calculators=A.calculators.filter((e=>!w.some((t=>t.id===e.id)))),A.flowcharts=A.flowcharts.filter((e=>!j.some((t=>t.id===e.id)))),A.childcare=A.childcare.filter((e=>!k.some((t=>t.id===e.id))))}return[...o?.map((e=>({id:e.id,name:e.name,brands:e.brands,category:e.pedbook_medication_categories,type:"medication",slug:e.slug})))||[],...A.medications.map((e=>({id:e.id,name:e.name,brands:e.brands,category:e.pedbook_medication_categories,type:"medication",slug:e.slug,isFuzzyMatch:!0}))),...c?.map((e=>({id:e.id,name:e.name,type:"category"})))||[],...A.categories.map((e=>({id:e.id,name:e.name,type:"category",isFuzzyMatch:!0}))),...b,...w,...A.calculators.map((e=>({...e,isFuzzyMatch:!0}))),...j,...A.flowcharts.map((e=>({...e,isFuzzyMatch:!0}))),...k,...A.childcare.map((e=>({...e,isFuzzyMatch:!0}))),...y?.map((e=>({id:e.id,name:e.title,type:"conduct",path:`/condutas-e-manejos/${e.pedbook_conducts_topics.pedbook_conducts_categories.slug}/${e.pedbook_conducts_topics.slug}`,description:`${e.pedbook_conducts_topics.pedbook_conducts_categories.name} > ${e.pedbook_conducts_topics.name}`,parent_slug:e.pedbook_conducts_topics.pedbook_conducts_categories.slug})))||[],...v?.map((e=>({id:e.id,name:e.title,type:"conduct",path:`/condutas-e-manejos/${e.pedbook_conducts_topics.pedbook_conducts_categories.slug}/${e.pedbook_conducts_topics.slug}`,description:`${e.pedbook_conducts_topics.pedbook_conducts_categories.name} > ${e.pedbook_conducts_topics.name}`,parent_slug:e.pedbook_conducts_topics.pedbook_conducts_categories.slug})))||[],...A.conducts.map((e=>({id:e.id,name:e.title||e.name,type:"conduct",path:`/condutas-e-manejos/${e.slug}`,isFuzzyMatch:!0}))),...h?.map((e=>({id:e.id,name:e.name,type:"vaccine",path:"/puericultura/calendario-vacinal"})))||[],...A.vaccines.map((e=>({id:e.id,name:e.name,type:"vaccine",path:"/puericultura/calendario-vacinal",isFuzzyMatch:!0}))),...u?.map((e=>({id:e.id,name:e.name,code_range:e.code,description:e.description,type:"icd10"})))||[],...A.icd10.map((e=>({id:e.id,name:e.name,code_range:e.code_range,type:"icd10",isFuzzyMatch:!0}))),...g?.map((e=>({id:e.id,name:e.medication_name,type:"leaflet",path:`/bulas-profissionais/${e.medication_slug}`,medication_name:e.medication_name,medication_id:e.medication_id})))||[],...A.leaflets.map((e=>({id:e.id,name:e.pedbook_medications?.name,type:"leaflet",path:`/bulas-profissionais/${e.pedbook_medications?.slug}`,medication_name:e.pedbook_medications?.name,medication_id:e.medication_id,isFuzzyMatch:!0})))]}catch(e){return t({variant:"destructive",title:"Erro na busca",description:"Não foi possível completar a busca. Tente novamente."}),[]}},enabled:r.length>=3,staleTime:6e5,gcTime:36e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:1,retryDelay:1e3,networkMode:"online",placeholderData:[]});return{searchResults:s,isLoading:a,isFetching:n}})(l);T.useEffect((()=>{const e=e=>{"Escape"===e.key&&(i(!1),o.current?.blur())};if(n)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[n]);const m=T.useCallback((e=>{i(!1),s(""),"category"===e.type?t("/medicamentos/painel",{state:{selectedCategory:e.id}}):"medication"===e.type?t(`/medicamentos/${e.slug}`):"icd10"===e.type?t(`/icd?category=${e.id}`):"calculator"===e.type?t(e.path||"/calculators"):"flowchart"===e.type?t(e.path||"/flowcharts"):"childcare"===e.type?t(e.path||"/childcare"):"toxidrome"===e.type?t(e.path||"/poisonings"):"conduct"===e.type?t(e.path||"/condutas-e-manejos"):"vaccine"===e.type?t("/puericultura/calendario-vacinal"):"leaflet"===e.type&&t(e.path||"/bulas-profissionais")}),[t]);return a.jsx("div",{className:"flex-1 sm:max-w-xl w-full mx-auto px-1 sm:px-2",children:a.jsxs($l,{open:n,onOpenChange:i,children:[a.jsx(Ul,{asChild:!0,children:a.jsx("div",{className:"relative group cursor-text",children:a.jsx(Hc,{searchTerm:r,setSearchTerm:s,customPlaceholder:e,onFocus:()=>{i(!0)},inputRef:o})})}),a.jsx(ql,{className:"w-[calc(100vw-2rem)] sm:w-[500px] p-0 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-none shadow-xl max-h-[80vh] rounded-xl overflow-hidden",align:"center",side:"bottom",sideOffset:8,onOpenAutoFocus:e=>{e.preventDefault()},children:a.jsx(Fl,{className:"bg-transparent",children:a.jsx(T.Suspense,{fallback:a.jsx("div",{className:"p-4 text-center",children:a.jsxs("div",{className:"flex items-center justify-center gap-2 text-muted-foreground",children:[a.jsx("div",{className:"w-2 h-2 bg-primary/60 rounded-full animate-pulse"}),a.jsx("p",{className:"text-sm",children:"Carregando busca..."})]})}),children:a.jsx(Yc,{results:c,onSelect:m,formatBrands:Gc,isLoading:d,isFetching:u,showEmptyMessage:n&&!r,searchTerm:l,currentInput:r,onClose:()=>i(!1)})})})})]})})},Xc=({hideSearch:e,isIndexPage:t})=>t||e?null:a.jsx("div",{className:"w-full sm:max-w-md",children:a.jsx(Qc,{})}),Jc=js("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground border-border",filter:"px-3 py-1.5 text-sm font-medium border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"}},defaultVariants:{variant:"default"}});function Zc({className:e,variant:t,...r}){return a.jsx("div",{className:os(Jc({variant:t}),e),...r})}var ed="Avatar",[td,rd]=A(ed),[sd,ad]=td(ed),nd=T.forwardRef(((e,t)=>{const{__scopeAvatar:r,...s}=e,[n,i]=T.useState("idle");return a.jsx(sd,{scope:r,imageLoadingStatus:n,onImageLoadingStatusChange:i,children:a.jsx(C.span,{...s,ref:t})})}));nd.displayName=ed;var id="AvatarImage",od=T.forwardRef(((e,t)=>{const{__scopeAvatar:r,src:s,onLoadingStatusChange:n=()=>{},...i}=e,o=ad(id,r),l=function(e,t){const[r,s]=T.useState("idle");return N((()=>{if(!e)return void s("error");let r=!0;const a=new window.Image,n=e=>()=>{r&&s(e)};return s("loading"),a.onload=n("loaded"),a.onerror=n("error"),a.src=e,t&&(a.referrerPolicy=t),()=>{r=!1}}),[e,t]),r}(s,i.referrerPolicy),c=_((e=>{n(e),o.onImageLoadingStatusChange(e)}));return N((()=>{"idle"!==l&&c(l)}),[l,c]),"loaded"===l?a.jsx(C.img,{...i,ref:t,src:s}):null}));od.displayName=id;var ld="AvatarFallback",cd=T.forwardRef(((e,t)=>{const{__scopeAvatar:r,delayMs:s,...n}=e,i=ad(ld,r),[o,l]=T.useState(void 0===s);return T.useEffect((()=>{if(void 0!==s){const e=window.setTimeout((()=>l(!0)),s);return()=>window.clearTimeout(e)}}),[s]),o&&"loaded"!==i.imageLoadingStatus?a.jsx(C.span,{...n,ref:t}):null}));cd.displayName=ld;var dd=nd,ud=od,md=cd;const hd=T.forwardRef((({className:e,...t},r)=>a.jsx(dd,{ref:r,className:os("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t})));hd.displayName=dd.displayName;const pd=T.forwardRef((({className:e,...t},r)=>a.jsx(ud,{ref:r,className:os("aspect-square h-full w-full",e),...t})));pd.displayName=ud.displayName;const fd=T.forwardRef((({className:e,...t},r)=>a.jsx(md,{ref:r,className:os("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t})));fd.displayName=md.displayName;const gd=Ue,xd=qe;T.forwardRef((({className:e,inset:t,children:r,...s},n)=>a.jsxs(Re,{ref:n,className:os("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...s,children:[r,a.jsx(Mt,{className:"ml-auto h-4 w-4"})]}))).displayName=Re.displayName,T.forwardRef((({className:e,...t},r)=>a.jsx(Me,{ref:r,className:os("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-white dark:bg-slate-800 text-gray-800 dark:text-gray-200 shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}))).displayName=Me.displayName;const yd=T.forwardRef((({className:e,sideOffset:t=4,...r},s)=>a.jsx(Ie,{children:a.jsx(Le,{ref:s,sideOffset:t,className:os("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-white dark:bg-slate-800 text-gray-800 dark:text-gray-200 shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})})));yd.displayName=Le.displayName;const vd=T.forwardRef((({className:e,inset:t,...r},s)=>a.jsx(Oe,{ref:s,className:os("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...r})));vd.displayName=Oe.displayName,T.forwardRef((({className:e,children:t,checked:r,...s},n)=>a.jsxs(Fe,{ref:n,className:os("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...s,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(Ve,{children:a.jsx(Dt,{className:"h-4 w-4"})})}),t]}))).displayName=Fe.displayName,T.forwardRef((({className:e,children:t,...r},s)=>a.jsxs(ze,{ref:s,className:os("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(Ve,{children:a.jsx(Vt,{className:"h-2 w-2 fill-current"})})}),t]}))).displayName=ze.displayName;const bd=T.forwardRef((({className:e,inset:t,...r},s)=>a.jsx(Be,{ref:s,className:os("px-2 py-1.5 text-sm font-semibold text-gray-800 dark:text-gray-200",t&&"pl-8",e),...r})));bd.displayName=Be.displayName;const wd=T.forwardRef((({className:e,...t},r)=>a.jsx($e,{ref:r,className:os("-mx-1 my-1 h-px bg-gray-200 dark:bg-slate-600",e),...t})));wd.displayName=$e.displayName;var jd,kd,Ed={exports:{}},Ad={exports:{}};jd="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",kd={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&kd.rotl(e,8)|4278255360&kd.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=kd.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],r=0,s=0;r<e.length;r++,s+=8)t[s>>>5]|=e[r]<<24-s%32;return t},wordsToBytes:function(e){for(var t=[],r=0;r<32*e.length;r+=8)t.push(e[r>>>5]>>>24-r%32&255);return t},bytesToHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},bytesToBase64:function(e){for(var t=[],r=0;r<e.length;r+=3)for(var s=e[r]<<16|e[r+1]<<8|e[r+2],a=0;a<4;a++)8*r+6*a<=8*e.length?t.push(jd.charAt(s>>>6*(3-a)&63)):t.push("=");return t.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var t=[],r=0,s=0;r<e.length;s=++r%4)0!=s&&t.push((jd.indexOf(e.charAt(r-1))&Math.pow(2,-2*s+8)-1)<<2*s|jd.indexOf(e.charAt(r))>>>6-2*s);return t}},Ad.exports=kd;var Cd,_d,Nd,Sd,Td,Pd=Ad.exports,Dd={utf8:{stringToBytes:function(e){return Dd.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(Dd.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}},Rd=Dd;function Md(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}Cd=Pd,_d=Rd.utf8,Nd=function(e){return null!=e&&(Md(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&Md(e.slice(0,0))}(e)||!!e._isBuffer)},Sd=Rd.bin,Td=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?Sd.stringToBytes(e):_d.stringToBytes(e):Nd(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var r=Cd.bytesToWords(e),s=8*e.length,a=1732584193,n=-271733879,i=-1732584194,o=271733878,l=0;l<r.length;l++)r[l]=16711935&(r[l]<<8|r[l]>>>24)|4278255360&(r[l]<<24|r[l]>>>8);r[s>>>5]|=128<<s%32,r[14+(s+64>>>9<<4)]=s;var c=Td._ff,d=Td._gg,u=Td._hh,m=Td._ii;for(l=0;l<r.length;l+=16){var h=a,p=n,f=i,g=o;a=c(a,n,i,o,r[l+0],7,-680876936),o=c(o,a,n,i,r[l+1],12,-389564586),i=c(i,o,a,n,r[l+2],17,606105819),n=c(n,i,o,a,r[l+3],22,-1044525330),a=c(a,n,i,o,r[l+4],7,-176418897),o=c(o,a,n,i,r[l+5],12,1200080426),i=c(i,o,a,n,r[l+6],17,-1473231341),n=c(n,i,o,a,r[l+7],22,-45705983),a=c(a,n,i,o,r[l+8],7,1770035416),o=c(o,a,n,i,r[l+9],12,-1958414417),i=c(i,o,a,n,r[l+10],17,-42063),n=c(n,i,o,a,r[l+11],22,-1990404162),a=c(a,n,i,o,r[l+12],7,1804603682),o=c(o,a,n,i,r[l+13],12,-40341101),i=c(i,o,a,n,r[l+14],17,-1502002290),a=d(a,n=c(n,i,o,a,r[l+15],22,1236535329),i,o,r[l+1],5,-165796510),o=d(o,a,n,i,r[l+6],9,-1069501632),i=d(i,o,a,n,r[l+11],14,643717713),n=d(n,i,o,a,r[l+0],20,-373897302),a=d(a,n,i,o,r[l+5],5,-701558691),o=d(o,a,n,i,r[l+10],9,38016083),i=d(i,o,a,n,r[l+15],14,-660478335),n=d(n,i,o,a,r[l+4],20,-405537848),a=d(a,n,i,o,r[l+9],5,568446438),o=d(o,a,n,i,r[l+14],9,-1019803690),i=d(i,o,a,n,r[l+3],14,-187363961),n=d(n,i,o,a,r[l+8],20,1163531501),a=d(a,n,i,o,r[l+13],5,-1444681467),o=d(o,a,n,i,r[l+2],9,-51403784),i=d(i,o,a,n,r[l+7],14,1735328473),a=u(a,n=d(n,i,o,a,r[l+12],20,-1926607734),i,o,r[l+5],4,-378558),o=u(o,a,n,i,r[l+8],11,-2022574463),i=u(i,o,a,n,r[l+11],16,1839030562),n=u(n,i,o,a,r[l+14],23,-35309556),a=u(a,n,i,o,r[l+1],4,-1530992060),o=u(o,a,n,i,r[l+4],11,1272893353),i=u(i,o,a,n,r[l+7],16,-155497632),n=u(n,i,o,a,r[l+10],23,-1094730640),a=u(a,n,i,o,r[l+13],4,681279174),o=u(o,a,n,i,r[l+0],11,-358537222),i=u(i,o,a,n,r[l+3],16,-722521979),n=u(n,i,o,a,r[l+6],23,76029189),a=u(a,n,i,o,r[l+9],4,-640364487),o=u(o,a,n,i,r[l+12],11,-421815835),i=u(i,o,a,n,r[l+15],16,530742520),a=m(a,n=u(n,i,o,a,r[l+2],23,-995338651),i,o,r[l+0],6,-198630844),o=m(o,a,n,i,r[l+7],10,1126891415),i=m(i,o,a,n,r[l+14],15,-1416354905),n=m(n,i,o,a,r[l+5],21,-57434055),a=m(a,n,i,o,r[l+12],6,1700485571),o=m(o,a,n,i,r[l+3],10,-1894986606),i=m(i,o,a,n,r[l+10],15,-1051523),n=m(n,i,o,a,r[l+1],21,-2054922799),a=m(a,n,i,o,r[l+8],6,1873313359),o=m(o,a,n,i,r[l+15],10,-30611744),i=m(i,o,a,n,r[l+6],15,-1560198380),n=m(n,i,o,a,r[l+13],21,1309151649),a=m(a,n,i,o,r[l+4],6,-145523070),o=m(o,a,n,i,r[l+11],10,-1120210379),i=m(i,o,a,n,r[l+2],15,718787259),n=m(n,i,o,a,r[l+9],21,-343485551),a=a+h>>>0,n=n+p>>>0,i=i+f>>>0,o=o+g>>>0}return Cd.endian([a,n,i,o])},Td._ff=function(e,t,r,s,a,n,i){var o=e+(t&r|~t&s)+(a>>>0)+i;return(o<<n|o>>>32-n)+t},Td._gg=function(e,t,r,s,a,n,i){var o=e+(t&s|r&~s)+(a>>>0)+i;return(o<<n|o>>>32-n)+t},Td._hh=function(e,t,r,s,a,n,i){var o=e+(t^r^s)+(a>>>0)+i;return(o<<n|o>>>32-n)+t},Td._ii=function(e,t,r,s,a,n,i){var o=e+(r^(t|~s))+(a>>>0)+i;return(o<<n|o>>>32-n)+t},Td._blocksize=16,Td._digestsize=16,Ed.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var r=Cd.wordsToBytes(Td(e,t));return t&&t.asBytes?r:t&&t.asString?Sd.bytesToString(r):Cd.bytesToHex(r)};const Id=P(Ed.exports),Ld=T.forwardRef((({className:e,...t},r)=>a.jsx("textarea",{className:os("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t})));Ld.displayName="Textarea";const Od=Ee,Fd=Ce,Vd=Ae,zd=T.forwardRef((({className:e,children:t,...r},s)=>a.jsxs(me,{ref:s,className:os("flex min-h-[2.5rem] h-auto items-start justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-slate-800 dark:border-slate-700 dark:text-gray-200",e),...r,children:[t,a.jsx(he,{className:"mt-0.5 flex-shrink-0",children:a.jsx(Rt,{className:"h-4 w-4 opacity-50"})})]})));zd.displayName=me.displayName;const Bd=T.forwardRef((({className:e,...t},r)=>a.jsx(pe,{ref:r,className:os("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(It,{className:"h-4 w-4"})})));Bd.displayName=pe.displayName;const $d=T.forwardRef((({className:e,...t},r)=>a.jsx(fe,{ref:r,className:os("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(Rt,{className:"h-4 w-4"})})));$d.displayName=fe.displayName;const Ud=T.forwardRef((({className:e,children:t,position:r="popper",...s},n)=>a.jsx(ge,{children:a.jsxs(xe,{ref:n,className:os("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-lg border bg-white text-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:bg-slate-800 dark:border-slate-700 dark:text-gray-200","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...s,children:[a.jsx(Bd,{}),a.jsx(ye,{className:os("p-2","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx($d,{})]})})));Ud.displayName=xe.displayName,T.forwardRef((({className:e,...t},r)=>a.jsx(ve,{ref:r,className:os("py-2 pl-8 pr-2 text-sm font-semibold text-primary dark:text-primary-foreground",e),...t}))).displayName=ve.displayName;const qd=T.forwardRef((({className:e,children:t,...r},s)=>a.jsxs(be,{ref:s,className:os("relative flex w-full cursor-default select-none items-center py-2 pl-8 pr-2 text-sm outline-none dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/30 focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 transition-colors",e),...r,children:[a.jsx("span",{className:"absolute left-2 flex h-4 w-4 items-center justify-center text-primary",children:a.jsx(we,{children:a.jsx(Dt,{className:"h-4 w-4"})})}),a.jsx(je,{className:"font-medium",children:t})]})));function Wd({onSuccess:e}){const[t,r]=T.useState([]),[s,n]=T.useState(""),[i,o]=T.useState(""),[l,c]=T.useState(""),[d,u]=T.useState(!1),[m,h]=T.useState(!0),[p,f]=T.useState(!1),{user:g}=La(),{toast:x}=Zl(),y=window.innerWidth<768;return T.useEffect((()=>{(async()=>{try{const{data:e,error:t}=await Ts.from("pedbook_feedback_types").select("*");if(t)throw t;r(e)}catch(e){x({title:"Erro ao carregar tipos de feedback",description:e.message,variant:"destructive"})}finally{h(!1)}})()}),[x]),m?a.jsx("div",{className:"max-w-2xl mx-auto",children:a.jsx("div",{className:"p-8 text-center bg-gradient-to-br from-white via-blue-50/30 to-white dark:from-slate-800 dark:via-slate-800/90 dark:to-slate-800 rounded-2xl shadow-lg border border-blue-100 dark:border-slate-700",children:a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"w-12 h-12 mx-auto border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200",children:"Carregando..."}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Preparando os tipos de feedback"})]})]})})}):p?a.jsx("div",{className:"max-w-2xl mx-auto px-2 sm:px-0",children:a.jsx("div",{className:"p-8 text-center bg-gradient-to-br from-green-50 via-emerald-50/30 to-green-50 dark:from-slate-800 dark:via-emerald-900/20 dark:to-slate-800 rounded-2xl shadow-lg border border-green-200 dark:border-emerald-700 backdrop-blur-sm",children:a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"w-20 h-20 mx-auto bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg animate-pulse",children:a.jsx("span",{className:"text-3xl text-white",children:"✅"})}),a.jsx("div",{className:"absolute inset-0 w-20 h-20 mx-auto bg-green-400 rounded-full animate-ping opacity-20"})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx("h3",{className:"text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",children:"Feedback Enviado!"}),a.jsx("p",{className:"text-gray-700 dark:text-gray-300 text-lg",children:"🎉 Obrigado por compartilhar sua opinião!"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Sua mensagem foi recebida e será analisada pela nossa equipe. Entraremos em contato se necessário."})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Redirecionando em instantes..."}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1",children:a.jsx("div",{className:"bg-gradient-to-r from-green-500 to-emerald-500 h-1 rounded-full animate-pulse",style:{width:"100%"}})})]})]})})}):a.jsx("div",{className:"max-w-2xl mx-auto px-2 sm:px-0",children:a.jsxs("form",{onSubmit:async r=>{if(r.preventDefault(),g){u(!0);try{const{error:r}=await Ts.from("pedbook_feedbacks").insert([{user_id:g.id,type_id:s,message:i,whatsapp:l,title:t.find((e=>e.id===s))?.name||"Feedback"}]);if(r)throw r;f(!0),x({title:"✅ Feedback enviado com sucesso!",description:"Obrigado por compartilhar sua opinião! Entraremos em contato se necessário.",duration:5e3}),setTimeout((()=>{n(""),o(""),c(""),f(!1),e?.()}),2e3)}catch(a){x({title:"Erro ao enviar feedback",description:a.message,variant:"destructive"})}finally{u(!1)}}},className:"space-y-4 sm:space-y-6 p-4 sm:p-6 bg-gradient-to-br from-white via-blue-50/30 to-white dark:from-slate-800 dark:via-slate-800/90 dark:to-slate-800 rounded-2xl shadow-lg border border-blue-100 dark:border-slate-700 backdrop-blur-sm",children:[a.jsxs("div",{className:"text-center space-y-2 pb-3 sm:pb-4 border-b border-blue-100 dark:border-slate-700",children:[a.jsx("h2",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Novo Feedback"}),a.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 px-2",children:"Compartilhe suas ideias, sugestões ou reporte problemas"})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("label",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2",children:[a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Tipo de Feedback"]}),a.jsxs(Od,{value:s,onValueChange:n,required:!0,children:[a.jsx(zd,{className:"h-12 bg-white/80 dark:bg-slate-700/80 border-2 border-blue-200 dark:border-slate-600 rounded-xl hover:border-blue-300 dark:hover:border-slate-500 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 shadow-sm",children:a.jsx(Vd,{placeholder:"🎯 Selecione o tipo de feedback"})}),a.jsx(Ud,{className:"bg-white/95 dark:bg-slate-800/95 border-blue-200 dark:border-slate-600 rounded-xl backdrop-blur-sm",children:t.map((e=>a.jsx(qd,{value:e.id,className:"hover:bg-blue-50 dark:hover:bg-slate-700 rounded-lg mx-1 my-0.5",children:e.name},e.id)))})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("label",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2",children:[a.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"WhatsApp (opcional)"]}),a.jsx("div",{className:"relative",children:a.jsx("input",{type:"text",value:l,onChange:e=>c(e.target.value),placeholder:"📱 (11) 99999-9999",className:"w-full h-12 px-4 py-3 border-2 border-green-200 dark:border-slate-600 rounded-xl bg-white/80 dark:bg-slate-700/80 dark:text-gray-200 focus:ring-2 focus:ring-green-500/20 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 outline-none shadow-sm hover:border-green-300 dark:hover:border-slate-500"})}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1",children:"💡 Para contato sobre seu feedback, se necessário"})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("label",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2",children:[a.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"Mensagem"]}),a.jsx(Ld,{value:i,onChange:e=>o(e.target.value),placeholder:"✍️ Descreva detalhadamente seu feedback, sugestão ou problema...",required:!0,className:"resize-none dark:text-gray-200 border-2 border-purple-200 dark:border-slate-600 rounded-xl bg-white/80 dark:bg-slate-700/80 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 shadow-sm hover:border-purple-300 dark:hover:border-slate-500 "+(y?"min-h-[120px]":"min-h-[160px]")})]}),a.jsx(Es,{type:"submit",disabled:d,className:"w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:transform-none disabled:opacity-70",children:d?a.jsxs("div",{className:"flex items-center justify-center gap-3",children:[a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),a.jsx("span",{children:"Enviando feedback..."})]}):a.jsxs("div",{className:"flex items-center justify-center gap-2",children:[a.jsx("span",{children:"🚀"}),a.jsx("span",{children:"Enviar Feedback"})]})})]})})}qd.displayName=be.displayName,T.forwardRef((({className:e,...t},r)=>a.jsx(ke,{ref:r,className:os("-mx-1 my-2 h-px bg-blue-100 dark:bg-blue-800/30",e),...t}))).displayName=ke.displayName;const Hd=Ke,Gd=T.forwardRef((({className:e,...t},r)=>a.jsx(We,{ref:r,className:os("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t})));Gd.displayName=We.displayName;const Kd=T.forwardRef((({className:e,...t},r)=>a.jsx(He,{ref:r,className:os("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t})));Kd.displayName=He.displayName;const Yd=T.forwardRef((({className:e,...t},r)=>a.jsx(Ge,{ref:r,className:os("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t})));Yd.displayName=Ge.displayName;const Qd=T.forwardRef((({className:e,children:t,...r},s)=>a.jsxs(Ye,{ref:s,className:os("relative overflow-hidden",e),...r,children:[a.jsx(Qe,{className:"h-full w-full rounded-[inherit]",children:t}),a.jsx(Xd,{}),a.jsx(Xe,{})]})));Qd.displayName=Ye.displayName;const Xd=T.forwardRef((({className:e,orientation:t="vertical",...r},s)=>a.jsx(Je,{ref:s,orientation:t,className:os("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:a.jsx(Ze,{className:"relative flex-1 rounded-full bg-border"})})));Xd.displayName=Je.displayName;const Jd=T.createContext({});function Zd(e){const t=T.useRef(null);return null===t.current&&(t.current=e()),t.current}const eu=T.createContext(null),tu=T.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});function ru(e=!0){const t=T.useContext(eu);if(null===t)return[!0,null];const{isPresent:r,onExitComplete:s,register:a}=t,n=T.useId();T.useEffect((()=>{e&&a(n)}),[e]);const i=T.useCallback((()=>e&&s&&s(n)),[n,s,e]);return!r&&s?[!1,i]:[!0]}const su="undefined"!=typeof window,au=su?T.useLayoutEffect:T.useEffect,nu=e=>e;let iu=nu;function ou(e){let t;return()=>(void 0===t&&(t=e()),t)}const lu=(e,t,r)=>{const s=t-e;return 0===s?1:(r-e)/s},cu=e=>1e3*e,du=e=>e/1e3,uu=["read","resolveKeyframes","update","preRender","render","postRender"];function mu(e,t){let r=!1,s=!0;const a={delta:0,timestamp:0,isProcessing:!1},n=()=>r=!0,i=uu.reduce(((e,t)=>(e[t]=function(e){let t=new Set,r=new Set,s=!1,a=!1;const n=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1};function o(t){n.has(t)&&(l.schedule(t),e()),t(i)}const l={schedule:(e,a=!1,i=!1)=>{const o=i&&s?t:r;return a&&n.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),n.delete(e)},process:e=>{i=e,s?a=!0:(s=!0,[t,r]=[r,t],t.forEach(o),t.clear(),s=!1,a&&(a=!1,l.process(e)))}};return l}(n),e)),{}),{read:o,resolveKeyframes:l,update:c,preRender:d,render:u,postRender:m}=i,h=()=>{const n=performance.now();r=!1,a.delta=s?1e3/60:Math.max(Math.min(n-a.timestamp,40),1),a.timestamp=n,a.isProcessing=!0,o.process(a),l.process(a),c.process(a),d.process(a),u.process(a),m.process(a),a.isProcessing=!1,r&&t&&(s=!1,e(h))};return{schedule:uu.reduce(((t,n)=>{const o=i[n];return t[n]=(t,n=!1,i=!1)=>(r||(r=!0,s=!0,a.isProcessing||e(h)),o.schedule(t,n,i)),t}),{}),cancel:e=>{for(let t=0;t<uu.length;t++)i[uu[t]].cancel(e)},state:a,steps:i}}const{schedule:hu,cancel:pu,state:fu,steps:gu}=mu("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:nu,!0),xu=T.createContext({strict:!1}),yu={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},vu={};for(const Kb in yu)vu[Kb]={isEnabled:e=>yu[Kb].some((t=>!!e[t]))};const bu=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function wu(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||bu.has(e)}let ju=e=>!wu(e);try{(ku=require("@emotion/is-prop-valid").default)&&(ju=e=>e.startsWith("on")?!wu(e):ku(e))}catch(Gb){}var ku;function Eu(e){if("undefined"==typeof Proxy)return e;const t=new Map;return new Proxy(((...t)=>e(...t)),{get:(r,s)=>"create"===s?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}const Au=T.createContext({});function Cu(e){return"string"==typeof e||Array.isArray(e)}function _u(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}const Nu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Su=["initial",...Nu];function Tu(e){return _u(e.animate)||Su.some((t=>Cu(e[t])))}function Pu(e){return Boolean(Tu(e)||e.variants)}function Du(e){return Array.isArray(e)?e.join(" "):e}const Ru=Symbol.for("motionComponentSymbol");function Mu(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function Iu(e,t,r){return T.useCallback((s=>{s&&e.onMount&&e.onMount(s),t&&(s?t.mount(s):t.unmount()),r&&("function"==typeof r?r(s):Mu(r)&&(r.current=s))}),[t])}const Lu=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ou="data-"+Lu("framerAppearId"),{schedule:Fu}=mu(queueMicrotask,!1),Vu=T.createContext({});function zu(e,t,r,s,a){var n,i;const{visualElement:o}=T.useContext(Au),l=T.useContext(xu),c=T.useContext(eu),d=T.useContext(tu).reducedMotion,u=T.useRef(null);s=s||l.renderer,!u.current&&s&&(u.current=s(e,{visualState:t,parent:o,props:r,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:d}));const m=u.current,h=T.useContext(Vu);!m||m.projection||!a||"html"!==m.type&&"svg"!==m.type||function(e,t,r,s){const{layoutId:a,layout:n,drag:i,dragConstraints:o,layoutScroll:l,layoutRoot:c}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:Bu(e.parent)),e.projection.setOptions({layoutId:a,layout:n,alwaysMeasureLayout:Boolean(i)||o&&Mu(o),visualElement:e,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:c})}(u.current,r,a,h);const p=T.useRef(!1);T.useInsertionEffect((()=>{m&&p.current&&m.update(r,c)}));const f=r[Ou],g=T.useRef(Boolean(f)&&!(null===(n=window.MotionHandoffIsComplete)||void 0===n?void 0:n.call(window,f))&&(null===(i=window.MotionHasOptimisedAnimation)||void 0===i?void 0:i.call(window,f)));return au((()=>{m&&(p.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),Fu.render(m.render),g.current&&m.animationState&&m.animationState.animateChanges())})),T.useEffect((()=>{m&&(!g.current&&m.animationState&&m.animationState.animateChanges(),g.current&&(queueMicrotask((()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,f)})),g.current=!1))})),m}function Bu(e){if(e)return!1!==e.options.allowProjection?e.projection:Bu(e.parent)}function $u({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:s,Component:n}){var i,o;function l(e,i){let o;const l={...T.useContext(tu),...e,layoutId:Uu(e)},{isStatic:c}=l,d=function(e){const{initial:t,animate:r}=function(e,t){if(Tu(e)){const{initial:t,animate:r}=e;return{initial:!1===t||Cu(t)?t:void 0,animate:Cu(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,T.useContext(Au));return T.useMemo((()=>({initial:t,animate:r})),[Du(t),Du(r)])}(e),u=s(e,c);if(!c&&su){T.useContext(xu).strict;const e=function(e){const{drag:t,layout:r}=vu;if(!t&&!r)return{};const s={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(l);o=e.MeasureLayout,d.visualElement=zu(n,u,l,t,e.ProjectionNode)}return a.jsxs(Au.Provider,{value:d,children:[o&&d.visualElement?a.jsx(o,{visualElement:d.visualElement,...l}):null,r(n,e,Iu(u,d.visualElement,i),u,c,d.visualElement)]})}e&&function(e){for(const t in e)vu[t]={...vu[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof n?n:`create(${null!==(o=null!==(i=n.displayName)&&void 0!==i?i:n.name)&&void 0!==o?o:""})`}`;const c=T.forwardRef(l);return c[Ru]=n,c}function Uu({layoutId:e}){const t=T.useContext(Jd).id;return t&&void 0!==e?t+"-"+e:e}const qu=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Wu(e){return"string"==typeof e&&!e.includes("-")&&!!(qu.indexOf(e)>-1||/[A-Z]/u.test(e))}function Hu(e){const t=[{},{}];return null==e||e.values.forEach(((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()})),t}function Gu(e,t,r,s){if("function"==typeof t){const[a,n]=Hu(s);t=t(void 0!==r?r:e.custom,a,n)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){const[a,n]=Hu(s);t=t(void 0!==r?r:e.custom,a,n)}return t}const Ku=e=>Array.isArray(e),Yu=e=>Boolean(e&&e.getVelocity);function Qu(e){const t=Yu(e)?e.get():e;return r=t,Boolean(r&&"object"==typeof r&&r.mix&&r.toValue)?t.toValue():t;var r}const Xu=e=>(t,r)=>{const s=T.useContext(Au),a=T.useContext(eu),n=()=>function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:r},s,a,n){const i={latestValues:Ju(s,a,n,e),renderState:t()};return r&&(i.onMount=e=>r({props:s,current:e,...i}),i.onUpdate=e=>r(e)),i}(e,t,s,a);return r?n():Zd(n)};function Ju(e,t,r,s){const a={},n=s(e,{});for(const m in n)a[m]=Qu(n[m]);let{initial:i,animate:o}=e;const l=Tu(e),c=Pu(e);t&&c&&!l&&!1!==e.inherit&&(void 0===i&&(i=t.initial),void 0===o&&(o=t.animate));let d=!!r&&!1===r.initial;d=d||!1===i;const u=d?o:i;if(u&&"boolean"!=typeof u&&!_u(u)){const t=Array.isArray(u)?u:[u];for(let r=0;r<t.length;r++){const s=Gu(e,t[r]);if(s){const{transitionEnd:e,transition:t,...r}=s;for(const s in r){let e=r[s];Array.isArray(e)&&(e=e[d?e.length-1:0]),null!==e&&(a[s]=e)}for(const s in e)a[s]=e[s]}}}return a}const Zu=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],em=new Set(Zu),tm=e=>t=>"string"==typeof t&&t.startsWith(e),rm=tm("--"),sm=tm("var(--"),am=e=>!!sm(e)&&nm.test(e.split("/*")[0].trim()),nm=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,im=(e,t)=>t&&"number"==typeof e?t.transform(e):e,om=(e,t,r)=>r>t?t:r<e?e:r,lm={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},cm={...lm,transform:e=>om(0,1,e)},dm={...lm,default:1},um=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),mm=um("deg"),hm=um("%"),pm=um("px"),fm=um("vh"),gm=um("vw"),xm={...hm,parse:e=>hm.parse(e)/100,transform:e=>hm.transform(100*e)},ym={borderWidth:pm,borderTopWidth:pm,borderRightWidth:pm,borderBottomWidth:pm,borderLeftWidth:pm,borderRadius:pm,radius:pm,borderTopLeftRadius:pm,borderTopRightRadius:pm,borderBottomRightRadius:pm,borderBottomLeftRadius:pm,width:pm,maxWidth:pm,height:pm,maxHeight:pm,top:pm,right:pm,bottom:pm,left:pm,padding:pm,paddingTop:pm,paddingRight:pm,paddingBottom:pm,paddingLeft:pm,margin:pm,marginTop:pm,marginRight:pm,marginBottom:pm,marginLeft:pm,backgroundPositionX:pm,backgroundPositionY:pm},vm={rotate:mm,rotateX:mm,rotateY:mm,rotateZ:mm,scale:dm,scaleX:dm,scaleY:dm,scaleZ:dm,skew:mm,skewX:mm,skewY:mm,distance:pm,translateX:pm,translateY:pm,translateZ:pm,x:pm,y:pm,z:pm,perspective:pm,transformPerspective:pm,opacity:cm,originX:xm,originY:xm,originZ:pm},bm={...lm,transform:Math.round},wm={...ym,...vm,zIndex:bm,size:pm,fillOpacity:cm,strokeOpacity:cm,numOctaves:bm},jm={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},km=Zu.length;function Em(e,t,r){const{style:s,vars:a,transformOrigin:n}=e;let i=!1,o=!1;for(const l in t){const e=t[l];if(em.has(l))i=!0;else if(rm(l))a[l]=e;else{const t=im(e,wm[l]);l.startsWith("origin")?(o=!0,n[l]=t):s[l]=t}}if(t.transform||(i||r?s.transform=function(e,t,r){let s="",a=!0;for(let n=0;n<km;n++){const i=Zu[n],o=e[i];if(void 0===o)continue;let l=!0;if(l="number"==typeof o?o===(i.startsWith("scale")?1:0):0===parseFloat(o),!l||r){const e=im(o,wm[i]);l||(a=!1,s+=`${jm[i]||i}(${e}) `),r&&(t[i]=e)}}return s=s.trim(),r?s=r(t,a?"":s):a&&(s="none"),s}(t,e.transform,r):s.transform&&(s.transform="none")),o){const{originX:e="50%",originY:t="50%",originZ:r=0}=n;s.transformOrigin=`${e} ${t} ${r}`}}const Am={offset:"stroke-dashoffset",array:"stroke-dasharray"},Cm={offset:"strokeDashoffset",array:"strokeDasharray"};function _m(e,t,r){return"string"==typeof e?e:pm.transform(t+r*e)}function Nm(e,{attrX:t,attrY:r,attrScale:s,originX:a,originY:n,pathLength:i,pathSpacing:o=1,pathOffset:l=0,...c},d,u){if(Em(e,c,u),d)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:m,style:h,dimensions:p}=e;m.transform&&(p&&(h.transform=m.transform),delete m.transform),p&&(void 0!==a||void 0!==n||h.transform)&&(h.transformOrigin=function(e,t,r){return`${_m(t,e.x,e.width)} ${_m(r,e.y,e.height)}`}(p,void 0!==a?a:.5,void 0!==n?n:.5)),void 0!==t&&(m.x=t),void 0!==r&&(m.y=r),void 0!==s&&(m.scale=s),void 0!==i&&function(e,t,r=1,s=0,a=!0){e.pathLength=1;const n=a?Am:Cm;e[n.offset]=pm.transform(-s);const i=pm.transform(t),o=pm.transform(r);e[n.array]=`${i} ${o}`}(m,i,o,l,!1)}const Sm=e=>"string"==typeof e&&"svg"===e.toLowerCase();function Tm(e,{style:t,vars:r},s,a){Object.assign(e.style,t,a&&a.getProjectionStyles(s));for(const n in r)e.style.setProperty(n,r[n])}const Pm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Dm(e,t,r,s){Tm(e,t,void 0,s);for(const a in t.attrs)e.setAttribute(Pm.has(a)?a:Lu(a),t.attrs[a])}const Rm={};function Mm(e,{layout:t,layoutId:r}){return em.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!Rm[e]||"opacity"===e)}function Im(e,t,r){var s;const{style:a}=e,n={};for(const i in a)(Yu(a[i])||t.style&&Yu(t.style[i])||Mm(i,e)||void 0!==(null===(s=null==r?void 0:r.getValue(i))||void 0===s?void 0:s.liveStyle))&&(n[i]=a[i]);return n}function Lm(e,t,r){const s=Im(e,t,r);for(const a in e)(Yu(e[a])||Yu(t[a]))&&(s[-1!==Zu.indexOf(a)?"attr"+a.charAt(0).toUpperCase()+a.substring(1):a]=e[a]);return s}const Om=["x","y","width","height","cx","cy","r"],Fm={useVisualState:Xu({scrapeMotionValuesFromProps:Lm,createRenderState:()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),onUpdate:({props:e,prevProps:t,current:r,renderState:s,latestValues:a})=>{if(!r)return;let n=!!e.drag;if(!n)for(const o in a)if(em.has(o)){n=!0;break}if(!n)return;let i=!t;if(t)for(let o=0;o<Om.length;o++){const r=Om[o];e[r]!==t[r]&&(i=!0)}i&&hu.read((()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(r){t.dimensions={x:0,y:0,width:0,height:0}}}(r,s),hu.render((()=>{Nm(s,a,Sm(r.tagName),e.transformTemplate),Dm(r,s)}))}))}})},Vm={useVisualState:Xu({scrapeMotionValuesFromProps:Im,createRenderState:()=>({style:{},transform:{},transformOrigin:{},vars:{}})})};function zm(e,t,r){for(const s in t)Yu(t[s])||Mm(s,r)||(e[s]=t[s])}function Bm(e,t){const r={},s=function(e,t){const r={};return zm(r,e.style||{},e),Object.assign(r,function({transformTemplate:e},t){return T.useMemo((()=>{const r={style:{},transform:{},transformOrigin:{},vars:{}};return Em(r,t,e),Object.assign({},r.vars,r.style)}),[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===e.drag?"none":"pan-"+("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=s,r}function $m(e,t,r,s){const a=T.useMemo((()=>{const r={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Nm(r,t,Sm(s),e.transformTemplate),{...r.attrs,style:{...r.style}}}),[t]);if(e.style){const t={};zm(t,e.style,e),a.style={...t,...a.style}}return a}function Um(e=!1){return(t,r,s,{latestValues:a},n)=>{const i=(Wu(t)?$m:Bm)(r,a,n,t),o=function(e,t,r){const s={};for(const a in e)"values"===a&&"object"==typeof e.values||(ju(a)||!0===r&&wu(a)||!t&&!wu(a)||e.draggable&&a.startsWith("onDrag"))&&(s[a]=e[a]);return s}(r,"string"==typeof t,e),l=t!==T.Fragment?{...o,...i,ref:s}:{},{children:c}=r,d=T.useMemo((()=>Yu(c)?c.get():c),[c]);return T.createElement(t,{...l,children:d})}}function qm(e,t){return function(r,{forwardMotionProps:s}={forwardMotionProps:!1}){return $u({...Wu(r)?Fm:Vm,preloadedFeatures:e,useRender:Um(s),createVisualElement:t,Component:r})}}function Wm(e,t){if(!Array.isArray(t))return!1;const r=t.length;if(r!==e.length)return!1;for(let s=0;s<r;s++)if(t[s]!==e[s])return!1;return!0}function Hm(e,t,r){const s=e.getProps();return Gu(s,t,void 0!==r?r:s.custom,e)}const Gm=ou((()=>void 0!==window.ScrollTimeline));class Km{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map((e=>"finished"in e?e.finished:e)))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e,t){const r=this.animations.map((r=>Gm()&&r.attachTimeline?r.attachTimeline(e):"function"==typeof t?t(r):void 0));return()=>{r.forEach(((e,t)=>{e&&e(),this.animations[t].stop()}))}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach((t=>t[e]()))}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Ym extends Km{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function Qm(e,t){return e?e[t]||e.default||e:void 0}const Xm=2e4;function Jm(e){let t=0,r=e.next(t);for(;!r.done&&t<Xm;)t+=50,r=e.next(t);return t>=Xm?1/0:t}function Zm(e){return"function"==typeof e}function eh(e,t){e.timeline=t,e.onfinish=null}const th=e=>Array.isArray(e)&&"number"==typeof e[0],rh={linearEasing:void 0};function sh(e,t){const r=ou(e);return()=>{var e;return null!==(e=rh[t])&&void 0!==e?e:r()}}const ah=sh((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0}),"linearEasing"),nh=(e,t,r=10)=>{let s="";const a=Math.max(Math.round(t/r),2);for(let n=0;n<a;n++)s+=e(lu(0,a-1,n))+", ";return`linear(${s.substring(0,s.length-2)})`};function ih(e){return Boolean("function"==typeof e&&ah()||!e||"string"==typeof e&&(e in lh||ah())||th(e)||Array.isArray(e)&&e.every(ih))}const oh=([e,t,r,s])=>`cubic-bezier(${e}, ${t}, ${r}, ${s})`,lh={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:oh([0,.65,.55,1]),circOut:oh([.55,0,1,.45]),backIn:oh([.31,.01,.66,-.59]),backOut:oh([.33,1.53,.69,.99])};function ch(e,t){return e?"function"==typeof e&&ah()?nh(e,t):th(e)?oh(e):Array.isArray(e)?e.map((e=>ch(e,t)||lh.easeOut)):lh[e]:void 0}const dh={x:!1,y:!1};function uh(){return dh.x||dh.y}function mh(e,t){const r=function(e){if(e instanceof Element)return[e];if("string"==typeof e){const t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),s=new AbortController;return[r,{passive:!0,...t,signal:s.signal},()=>s.abort()]}function hh(e){return t=>{"touch"===t.pointerType||uh()||e(t)}}const ph=(e,t)=>!!t&&(e===t||ph(e,t.parentElement)),fh=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,gh=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),xh=new WeakSet;function yh(e){return t=>{"Enter"===t.key&&e(t)}}function vh(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function bh(e){return fh(e)&&!uh()}const wh=new Set(["width","height","top","left","right","bottom",...Zu]);let jh;function kh(){jh=void 0}const Eh={now:()=>(void 0===jh&&Eh.set(fu.isProcessing?fu.timestamp:performance.now()),jh),set:e=>{jh=e,queueMicrotask(kh)}};function Ah(e,t){-1===e.indexOf(t)&&e.push(t)}function Ch(e,t){const r=e.indexOf(t);r>-1&&e.splice(r,1)}class _h{constructor(){this.subscriptions=[]}add(e){return Ah(this.subscriptions,e),()=>Ch(this.subscriptions,e)}notify(e,t,r){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](e,t,r);else for(let a=0;a<s;a++){const s=this.subscriptions[a];s&&s(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Nh(e,t){return t?e*(1e3/t):0}class Sh{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{const r=Eh.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){var t;this.current=e,this.updatedAt=Eh.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=(t=this.current,!isNaN(parseFloat(t))))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new _h);const r=this.events[e].add(t);return"change"===e?()=>{r(),hu.read((()=>{this.events.change.getSize()||this.stop()}))}:r}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=Eh.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;const t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Nh(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise((t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Th(e,t){return new Sh(e,t)}function Ph(e,t,r){e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,Th(r))}function Dh(e,t){const r=e.getValue("willChange");if(s=r,Boolean(Yu(s)&&s.add))return r.add(t);var s}function Rh(e){return e.props[Ou]}const Mh=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function Ih(e,t,r,s){if(e===t&&r===s)return nu;return a=>0===a||1===a?a:Mh(function(e,t,r,s,a){let n,i,o=0;do{i=t+(r-t)/2,n=Mh(i,s,a)-e,n>0?r=i:t=i}while(Math.abs(n)>1e-7&&++o<12);return i}(a,0,1,e,r),t,s)}const Lh=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Oh=e=>t=>1-e(1-t),Fh=Ih(.33,1.53,.69,.99),Vh=Oh(Fh),zh=Lh(Vh),Bh=e=>(e*=2)<1?.5*Vh(e):.5*(2-Math.pow(2,-10*(e-1))),$h=e=>1-Math.sin(Math.acos(e)),Uh=Oh($h),qh=Lh($h),Wh=e=>/^0[^.\s]+$/u.test(e),Hh=e=>Math.round(1e5*e)/1e5,Gh=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Kh=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Yh=(e,t)=>r=>Boolean("string"==typeof r&&Kh.test(r)&&r.startsWith(e)||t&&!function(e){return null==e}(r)&&Object.prototype.hasOwnProperty.call(r,t)),Qh=(e,t,r)=>s=>{if("string"!=typeof s)return s;const[a,n,i,o]=s.match(Gh);return{[e]:parseFloat(a),[t]:parseFloat(n),[r]:parseFloat(i),alpha:void 0!==o?parseFloat(o):1}},Xh={...lm,transform:e=>Math.round((e=>om(0,255,e))(e))},Jh={test:Yh("rgb","red"),parse:Qh("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:s=1})=>"rgba("+Xh.transform(e)+", "+Xh.transform(t)+", "+Xh.transform(r)+", "+Hh(cm.transform(s))+")"},Zh={test:Yh("#"),parse:function(e){let t="",r="",s="",a="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),s=e.substring(5,7),a=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),s=e.substring(3,4),a=e.substring(4,5),t+=t,r+=r,s+=s,a+=a),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(s,16),alpha:a?parseInt(a,16)/255:1}},transform:Jh.transform},ep={test:Yh("hsl","hue"),parse:Qh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:s=1})=>"hsla("+Math.round(e)+", "+hm.transform(Hh(t))+", "+hm.transform(Hh(r))+", "+Hh(cm.transform(s))+")"},tp={test:e=>Jh.test(e)||Zh.test(e)||ep.test(e),parse:e=>Jh.test(e)?Jh.parse(e):ep.test(e)?ep.parse(e):Zh.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?Jh.transform(e):ep.transform(e)},rp=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,sp="number",ap="color",np=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ip(e){const t=e.toString(),r=[],s={color:[],number:[],var:[]},a=[];let n=0;const i=t.replace(np,(e=>(tp.test(e)?(s.color.push(n),a.push(ap),r.push(tp.parse(e))):e.startsWith("var(")?(s.var.push(n),a.push("var"),r.push(e)):(s.number.push(n),a.push(sp),r.push(parseFloat(e))),++n,"${}"))).split("${}");return{values:r,split:i,indexes:s,types:a}}function op(e){return ip(e).values}function lp(e){const{split:t,types:r}=ip(e),s=t.length;return e=>{let a="";for(let n=0;n<s;n++)if(a+=t[n],void 0!==e[n]){const t=r[n];a+=t===sp?Hh(e[n]):t===ap?tp.transform(e[n]):e[n]}return a}}const cp=e=>"number"==typeof e?0:e,dp={test:function(e){var t,r;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(Gh))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(rp))||void 0===r?void 0:r.length)||0)>0},parse:op,createTransformer:lp,getAnimatableNone:function(e){const t=op(e);return lp(e)(t.map(cp))}},up=new Set(["brightness","contrast","saturate","opacity"]);function mp(e){const[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[s]=r.match(Gh)||[];if(!s)return e;const a=r.replace(s,"");let n=up.has(t)?1:0;return s!==r&&(n*=100),t+"("+n+a+")"}const hp=/\b([a-z-]*)\(.*?\)/gu,pp={...dp,getAnimatableNone:e=>{const t=e.match(hp);return t?t.map(mp).join(" "):e}},fp={...wm,color:tp,backgroundColor:tp,outlineColor:tp,fill:tp,stroke:tp,borderColor:tp,borderTopColor:tp,borderRightColor:tp,borderBottomColor:tp,borderLeftColor:tp,filter:pp,WebkitFilter:pp},gp=e=>fp[e];function xp(e,t){let r=gp(e);return r!==pp&&(r=dp),r.getAnimatableNone?r.getAnimatableNone(t):void 0}const yp=new Set(["auto","none","0"]),vp=e=>e===lm||e===pm,bp=(e,t)=>parseFloat(e.split(", ")[t]),wp=(e,t)=>(r,{transform:s})=>{if("none"===s||!s)return 0;const a=s.match(/^matrix3d\((.+)\)$/u);if(a)return bp(a[1],t);{const t=s.match(/^matrix\((.+)\)$/u);return t?bp(t[1],e):0}},jp=new Set(["x","y","z"]),kp=Zu.filter((e=>!jp.has(e))),Ep={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:wp(4,13),y:wp(5,14)};Ep.translateX=Ep.x,Ep.translateY=Ep.y;const Ap=new Set;let Cp=!1,_p=!1;function Np(){if(_p){const e=Array.from(Ap).filter((e=>e.needsMeasurement)),t=new Set(e.map((e=>e.element))),r=new Map;t.forEach((e=>{const t=function(e){const t=[];return kp.forEach((r=>{const s=e.getValue(r);void 0!==s&&(t.push([r,s.get()]),s.set(r.startsWith("scale")?1:0))})),t}(e);t.length&&(r.set(e,t),e.render())})),e.forEach((e=>e.measureInitialState())),t.forEach((e=>{e.render();const t=r.get(e);t&&t.forEach((([t,r])=>{var s;null===(s=e.getValue(t))||void 0===s||s.set(r)}))})),e.forEach((e=>e.measureEndState())),e.forEach((e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)}))}_p=!1,Cp=!1,Ap.forEach((e=>e.complete())),Ap.clear()}function Sp(){Ap.forEach((e=>{e.readKeyframes(),e.needsMeasurement&&(_p=!0)}))}class Tp{constructor(e,t,r,s,a,n=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=s,this.element=a,this.isAsync=n}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Ap.add(this),Cp||(Cp=!0,hu.read(Sp),hu.resolveKeyframes(Np))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:t,element:r,motionValue:s}=this;for(let a=0;a<e.length;a++)if(null===e[a])if(0===a){const a=null==s?void 0:s.get(),n=e[e.length-1];if(void 0!==a)e[0]=a;else if(r&&t){const s=r.readValue(t,n);null!=s&&(e[0]=s)}void 0===e[0]&&(e[0]=n),s&&void 0===a&&s.set(e[0])}else e[a]=e[a-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Ap.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Ap.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Pp=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Dp=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Rp(e,t,r=1){const[s,a]=function(e){const t=Dp.exec(e);if(!t)return[,];const[,r,s,a]=t;return[`--${null!=r?r:s}`,a]}(e);if(!s)return;const n=window.getComputedStyle(t).getPropertyValue(s);if(n){const e=n.trim();return Pp(e)?parseFloat(e):e}return am(a)?Rp(a,t,r+1):a}const Mp=e=>t=>t.test(e),Ip=[lm,pm,hm,mm,gm,fm,{test:e=>"auto"===e,parse:e=>e}],Lp=e=>Ip.find(Mp(e));class Op extends Tp{constructor(e,t,r,s,a){super(e,t,r,s,a,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let o=0;o<e.length;o++){let r=e[o];if("string"==typeof r&&(r=r.trim(),am(r))){const s=Rp(r,t.current);void 0!==s&&(e[o]=s),o===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!wh.has(r)||2!==e.length)return;const[s,a]=e,n=Lp(s),i=Lp(a);if(n!==i)if(vp(n)&&vp(i))for(let o=0;o<e.length;o++){const t=e[o];"string"==typeof t&&(e[o]=parseFloat(t))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:t}=this,r=[];for(let a=0;a<e.length;a++)("number"==typeof(s=e[a])?0===s:null===s||"none"===s||"0"===s||Wh(s))&&r.push(a);var s;r.length&&function(e,t,r){let s,a=0;for(;a<e.length&&!s;){const t=e[a];"string"==typeof t&&!yp.has(t)&&ip(t).values.length&&(s=e[a]),a++}if(s&&r)for(const n of t)e[n]=xp(r,s)}(e,r,t)}measureInitialState(){const{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ep[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;const s=t[t.length-1];void 0!==s&&e.getValue(r,s).jump(s,!1)}measureEndState(){var e;const{element:t,name:r,unresolvedKeyframes:s}=this;if(!t||!t.current)return;const a=t.getValue(r);a&&a.jump(this.measuredOrigin,!1);const n=s.length-1,i=s[n];s[n]=Ep[r](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==i&&void 0===this.finalKeyframe&&(this.finalKeyframe=i),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach((([e,r])=>{t.getValue(e).set(r)})),this.resolveNoneKeyframes()}}const Fp=(e,t)=>!("zIndex"===t||"number"!=typeof e&&!Array.isArray(e)&&("string"!=typeof e||!dp.test(e)&&"0"!==e||e.startsWith("url("))),Vp=e=>null!==e;function zp(e,{repeat:t,repeatType:r="loop"},s){const a=e.filter(Vp),n=t&&"loop"!==r&&t%2==1?0:a.length-1;return n&&void 0!==s?s:a[n]}class Bp{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:s=0,repeatDelay:a=0,repeatType:n="loop",...i}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Eh.now(),this.options={autoplay:e,delay:t,type:r,repeat:s,repeatDelay:a,repeatType:n,...i},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(Sp(),Np()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=Eh.now(),this.hasAttemptedResolve=!0;const{name:r,type:s,velocity:a,delay:n,onComplete:i,onUpdate:o,isGenerator:l}=this.options;if(!l&&!function(e,t,r,s){const a=e[0];if(null===a)return!1;if("display"===t||"visibility"===t)return!0;const n=e[e.length-1],i=Fp(a,t),o=Fp(n,t);return!(!i||!o)&&(function(e){const t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||Zm(r))&&s)}(e,r,s,a)){if(!n)return o&&o(zp(e,this.options,t)),i&&i(),void this.resolveFinishedPromise();this.options.duration=0}const c=this.initPlayback(e,t);!1!==c&&(this._resolved={keyframes:e,finalKeyframe:t,...c},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise((e=>{this.resolveFinishedPromise=e}))}}const $p=(e,t,r)=>e+(t-e)*r;function Up(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function qp(e,t){return r=>r>0?t:e}const Wp=(e,t,r)=>{const s=e*e,a=r*(t*t-s)+s;return a<0?0:Math.sqrt(a)},Hp=[Zh,Jh,ep];function Gp(e){const t=(r=e,Hp.find((e=>e.test(r))));var r;if(!Boolean(t))return!1;let s=t.parse(e);return t===ep&&(s=function({hue:e,saturation:t,lightness:r,alpha:s}){e/=360,r/=100;let a=0,n=0,i=0;if(t/=100){const s=r<.5?r*(1+t):r+t-r*t,o=2*r-s;a=Up(o,s,e+1/3),n=Up(o,s,e),i=Up(o,s,e-1/3)}else a=n=i=r;return{red:Math.round(255*a),green:Math.round(255*n),blue:Math.round(255*i),alpha:s}}(s)),s}const Kp=(e,t)=>{const r=Gp(e),s=Gp(t);if(!r||!s)return qp(e,t);const a={...r};return e=>(a.red=Wp(r.red,s.red,e),a.green=Wp(r.green,s.green,e),a.blue=Wp(r.blue,s.blue,e),a.alpha=$p(r.alpha,s.alpha,e),Jh.transform(a))},Yp=(e,t)=>r=>t(e(r)),Qp=(...e)=>e.reduce(Yp),Xp=new Set(["none","hidden"]);function Jp(e,t){return r=>$p(e,t,r)}function Zp(e){return"number"==typeof e?Jp:"string"==typeof e?am(e)?qp:tp.test(e)?Kp:rf:Array.isArray(e)?ef:"object"==typeof e?tp.test(e)?Kp:tf:qp}function ef(e,t){const r=[...e],s=r.length,a=e.map(((e,r)=>Zp(e)(e,t[r])));return e=>{for(let t=0;t<s;t++)r[t]=a[t](e);return r}}function tf(e,t){const r={...e,...t},s={};for(const a in r)void 0!==e[a]&&void 0!==t[a]&&(s[a]=Zp(e[a])(e[a],t[a]));return e=>{for(const t in s)r[t]=s[t](e);return r}}const rf=(e,t)=>{const r=dp.createTransformer(t),s=ip(e),a=ip(t);return s.indexes.var.length===a.indexes.var.length&&s.indexes.color.length===a.indexes.color.length&&s.indexes.number.length>=a.indexes.number.length?Xp.has(e)&&!a.values.length||Xp.has(t)&&!s.values.length?function(e,t){return Xp.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):Qp(ef(function(e,t){var r;const s=[],a={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){const i=t.types[n],o=e.indexes[i][a[i]],l=null!==(r=e.values[o])&&void 0!==r?r:0;s[n]=l,a[i]++}return s}(s,a),a.values),r):qp(e,t)};function sf(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?$p(e,t,r):Zp(e)(e,t)}function af(e,t,r){const s=Math.max(t-5,0);return Nh(r-e(s),t-s)}const nf=.01,of=2,lf=.005,cf=.5;const df=12;function uf(e,t){return e*Math.sqrt(1-t*t)}const mf=["duration","bounce"],hf=["stiffness","damping","mass"];function pf(e,t){return t.some((t=>void 0!==e[t]))}function ff(e=.3,t=.3){const r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:s,restDelta:a}=r;const n=r.keyframes[0],i=r.keyframes[r.keyframes.length-1],o={done:!1,value:n},{stiffness:l,damping:c,mass:d,duration:u,velocity:m,isResolvedFromDuration:h}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!pf(e,hf)&&pf(e,mf))if(e.visualDuration){const r=e.visualDuration,s=2*Math.PI/(1.2*r),a=s*s,n=2*om(.05,1,1-(e.bounce||0))*Math.sqrt(a);t={...t,mass:1,stiffness:a,damping:n}}else{const r=function({duration:e=800,bounce:t=.3,velocity:r=0,mass:s=1}){let a,n,i=1-t;i=om(.05,1,i),e=om(.01,10,du(e)),i<1?(a=t=>{const s=t*i,a=s*e;return.001-(s-r)/uf(t,i)*Math.exp(-a)},n=t=>{const s=t*i*e,n=s*r+r,o=Math.pow(i,2)*Math.pow(t,2)*e,l=Math.exp(-s),c=uf(Math.pow(t,2),i);return(.001-a(t)>0?-1:1)*((n-o)*l)/c}):(a=t=>Math.exp(-t*e)*((t-r)*e+1)-.001,n=t=>Math.exp(-t*e)*(e*e*(r-t)));const o=function(e,t,r){let s=r;for(let a=1;a<df;a++)s-=e(s)/t(s);return s}(a,n,5/e);if(e=cu(e),isNaN(o))return{stiffness:100,damping:10,duration:e};{const t=Math.pow(o,2)*s;return{stiffness:t,damping:2*i*Math.sqrt(s*t),duration:e}}}(e);t={...t,...r,mass:1},t.isResolvedFromDuration=!0}return t}({...r,velocity:-du(r.velocity||0)}),p=m||0,f=c/(2*Math.sqrt(l*d)),g=i-n,x=du(Math.sqrt(l/d)),y=Math.abs(g)<5;let v;if(s||(s=y?nf:of),a||(a=y?lf:cf),f<1){const e=uf(x,f);v=t=>{const r=Math.exp(-f*x*t);return i-r*((p+f*x*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}}else if(1===f)v=e=>i-Math.exp(-x*e)*(g+(p+x*g)*e);else{const e=x*Math.sqrt(f*f-1);v=t=>{const r=Math.exp(-f*x*t),s=Math.min(e*t,300);return i-r*((p+f*x*g)*Math.sinh(s)+e*g*Math.cosh(s))/e}}const b={calculatedDuration:h&&u||null,next:e=>{const t=v(e);if(h)o.done=e>=u;else{let r=0;f<1&&(r=0===e?cu(p):af(v,e,t));const n=Math.abs(r)<=s,l=Math.abs(i-t)<=a;o.done=n&&l}return o.value=o.done?i:t,o},toString:()=>{const e=Math.min(Jm(b),Xm),t=nh((t=>b.next(e*t).value),e,30);return e+"ms "+t}};return b}function gf({keyframes:e,velocity:t=0,power:r=.8,timeConstant:s=325,bounceDamping:a=10,bounceStiffness:n=500,modifyTarget:i,min:o,max:l,restDelta:c=.5,restSpeed:d}){const u=e[0],m={done:!1,value:u},h=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l;let p=r*t;const f=u+p,g=void 0===i?f:i(f);g!==f&&(p=g-u);const x=e=>-p*Math.exp(-e/s),y=e=>g+x(e),v=e=>{const t=x(e),r=y(e);m.done=Math.abs(t)<=c,m.value=m.done?g:r};let b,w;const j=e=>{var t;t=m.value,(void 0!==o&&t<o||void 0!==l&&t>l)&&(b=e,w=ff({keyframes:[m.value,h(m.value)],velocity:af(y,e,m.value),damping:a,stiffness:n,restDelta:c,restSpeed:d}))};return j(0),{calculatedDuration:null,next:e=>{let t=!1;return w||void 0!==b||(t=!0,v(e),j(e)),void 0!==b&&e>=b?w.next(e-b):(!t&&v(e),m)}}}const xf=Ih(.42,0,1,1),yf=Ih(0,0,.58,1),vf=Ih(.42,0,.58,1),bf={linear:nu,easeIn:xf,easeInOut:vf,easeOut:yf,circIn:$h,circInOut:qh,circOut:Uh,backIn:Vh,backInOut:zh,backOut:Fh,anticipate:Bh},wf=e=>{if(th(e)){iu(4===e.length);const[t,r,s,a]=e;return Ih(t,r,s,a)}return"string"==typeof e?bf[e]:e};function jf({duration:e=300,keyframes:t,times:r,ease:s="easeInOut"}){const a=(e=>Array.isArray(e)&&"number"!=typeof e[0])(s)?s.map(wf):wf(s),n={done:!1,value:t[0]},i=function(e,t){return e.map((e=>e*t))}(r&&r.length===t.length?r:function(e){const t=[0];return function(e,t){const r=e[e.length-1];for(let s=1;s<=t;s++){const a=lu(0,t,s);e.push($p(r,1,a))}}(t,e.length-1),t}(t),e),o=function(e,t,{clamp:r=!0,ease:s,mixer:a}={}){const n=e.length;if(iu(n===t.length),1===n)return()=>t[0];if(2===n&&t[0]===t[1])return()=>t[1];const i=e[0]===e[1];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=function(e,t,r){const s=[],a=r||sf,n=e.length-1;for(let i=0;i<n;i++){let r=a(e[i],e[i+1]);if(t){const e=Array.isArray(t)?t[i]||nu:t;r=Qp(e,r)}s.push(r)}return s}(t,s,a),l=o.length,c=r=>{if(i&&r<e[0])return t[0];let s=0;if(l>1)for(;s<e.length-2&&!(r<e[s+1]);s++);const a=lu(e[s],e[s+1],r);return o[s](a)};return r?t=>c(om(e[0],e[n-1],t)):c}(i,t,{ease:Array.isArray(a)?a:(l=t,c=a,l.map((()=>c||vf)).splice(0,l.length-1))});var l,c;return{calculatedDuration:e,next:t=>(n.value=o(t),n.done=t>=e,n)}}const kf=e=>{const t=({timestamp:t})=>e(t);return{start:()=>hu.update(t,!0),stop:()=>pu(t),now:()=>fu.isProcessing?fu.timestamp:Eh.now()}},Ef={decay:gf,inertia:gf,tween:jf,keyframes:jf,spring:ff},Af=e=>e/100;class Cf extends Bp{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()};const{name:t,motionValue:r,element:s,keyframes:a}=this.options,n=(null==s?void 0:s.KeyframeResolver)||Tp;this.resolver=new n(a,((e,t)=>this.onKeyframesResolved(e,t)),t,r,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:t="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:a,velocity:n=0}=this.options,i=Zm(t)?t:Ef[t]||jf;let o,l;i!==jf&&"number"!=typeof e[0]&&(o=Qp(Af,sf(e[0],e[1])),e=[0,100]);const c=i({...this.options,keyframes:e});"mirror"===a&&(l=i({...this.options,keyframes:[...e].reverse(),velocity:-n})),null===c.calculatedDuration&&(c.calculatedDuration=Jm(c));const{calculatedDuration:d}=c,u=d+s;return{generator:c,mirroredGenerator:l,mapPercentToKeyframes:o,calculatedDuration:d,resolvedDuration:u,totalDuration:u*(r+1)-s}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){const{resolved:r}=this;if(!r){const{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}const{finalKeyframe:s,generator:a,mirroredGenerator:n,mapPercentToKeyframes:i,keyframes:o,calculatedDuration:l,totalDuration:c,resolvedDuration:d}=r;if(null===this.startTime)return a.next(0);const{delay:u,repeat:m,repeatType:h,repeatDelay:p,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-c/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const g=this.currentTime-u*(this.speed>=0?1:-1),x=this.speed>=0?g<0:g>c;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=c);let y=this.currentTime,v=a;if(m){const e=Math.min(this.currentTime,c)/d;let t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,t=Math.min(t,m+1),Boolean(t%2)&&("reverse"===h?(r=1-r,p&&(r-=p/d)):"mirror"===h&&(v=n)),y=om(0,1,r)*d}const b=x?{done:!1,value:o[0]}:v.next(y);i&&(b.value=i(b.value));let{done:w}=b;x||null===l||(w=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&void 0!==s&&(b.value=zp(o,this.options,s)),f&&f(b.value),j&&this.finish(),b}get duration(){const{resolved:e}=this;return e?du(e.calculatedDuration):0}get time(){return du(this.currentTime)}set time(e){e=cu(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=du(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:e=kf,onPlay:t,startTime:r}=this.options;this.driver||(this.driver=e((e=>this.tick(e)))),t&&t();const s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=r?r:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;this._resolved?(this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const _f=new Set(["opacity","clipPath","filter","transform"]);function Nf(e,t,r,{delay:s=0,duration:a=300,repeat:n=0,repeatType:i="loop",ease:o="easeInOut",times:l}={}){const c={[t]:r};l&&(c.offset=l);const d=ch(o,a);return Array.isArray(d)&&(c.easing=d),e.animate(c,{delay:s,duration:a,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:n+1,direction:"reverse"===i?"alternate":"normal"})}const Sf=ou((()=>Object.hasOwnProperty.call(Element.prototype,"animate"))),Tf={anticipate:Bh,backInOut:zh,circInOut:qh};class Pf extends Bp{constructor(e){super(e);const{name:t,motionValue:r,element:s,keyframes:a}=this.options;this.resolver=new Op(a,((e,t)=>this.onKeyframesResolved(e,t)),t,r,s),this.resolver.scheduleResolve()}initPlayback(e,t){let{duration:r=300,times:s,ease:a,type:n,motionValue:i,name:o,startTime:l}=this.options;if(!i.owner||!i.owner.current)return!1;if("string"==typeof a&&ah()&&a in Tf&&(a=Tf[a]),function(e){return Zm(e.type)||"spring"===e.type||!ih(e.ease)}(this.options)){const{onComplete:t,onUpdate:i,motionValue:o,element:l,...c}=this.options,d=function(e,t){const r=new Cf({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:e[0]};const a=[];let n=0;for(;!s.done&&n<2e4;)s=r.sample(n),a.push(s.value),n+=10;return{times:void 0,keyframes:a,duration:n-10,ease:"linear"}}(e,c);1===(e=d.keyframes).length&&(e[1]=e[0]),r=d.duration,s=d.times,a=d.ease,n="keyframes"}const c=Nf(i.owner.current,o,e,{...this.options,duration:r,times:s,ease:a});return c.startTime=null!=l?l:this.calcStartTime(),this.pendingTimeline?(eh(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:r}=this.options;i.set(zp(e,this.options,t)),r&&r(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:s,type:n,ease:a,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:t}=e;return du(t)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:t}=e;return du(t.currentTime||0)}set time(e){const{resolved:t}=this;if(!t)return;const{animation:r}=t;r.currentTime=cu(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:t}=e;return t.playbackRate}set speed(e){const{resolved:t}=this;if(!t)return;const{animation:r}=t;r.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:t}=e;return t.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){const{resolved:t}=this;if(!t)return nu;const{animation:r}=t;eh(r,e)}else this.pendingTimeline=e;return nu}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:t,keyframes:r,duration:s,type:a,ease:n,times:i}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){const{motionValue:e,onUpdate:t,onComplete:o,element:l,...c}=this.options,d=new Cf({...c,keyframes:r,duration:s,type:a,ease:n,times:i,isGenerator:!0}),u=cu(this.time);e.setWithVelocity(d.sample(u-10).value,d.sample(u).value,10)}const{onStop:o}=this.options;o&&o(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:t,name:r,repeatDelay:s,repeatType:a,damping:n,type:i}=e;if(!(t&&t.owner&&t.owner.current instanceof HTMLElement))return!1;const{onUpdate:o,transformTemplate:l}=t.owner.getProps();return Sf()&&r&&_f.has(r)&&!o&&!l&&!s&&"mirror"!==a&&0!==n&&"inertia"!==i}}const Df={type:"spring",stiffness:500,damping:25,restSpeed:10},Rf={type:"keyframes",duration:.8},Mf={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},If=(e,{keyframes:t})=>t.length>2?Rf:em.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:Df:Mf,Lf=(e,t,r,s={},a,n)=>i=>{const o=Qm(s,e)||{},l=o.delay||s.delay||0;let{elapsed:c=0}=s;c-=cu(l);let d={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-c,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:n?void 0:a};(function({when:e,delay:t,delayChildren:r,staggerChildren:s,staggerDirection:a,repeat:n,repeatType:i,repeatDelay:o,from:l,elapsed:c,...d}){return!!Object.keys(d).length})(o)||(d={...d,...If(e,d)}),d.duration&&(d.duration=cu(d.duration)),d.repeatDelay&&(d.repeatDelay=cu(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let u=!1;if((!1===d.type||0===d.duration&&!d.repeatDelay)&&(d.duration=0,0===d.delay&&(u=!0)),u&&!n&&void 0!==t.get()){const e=zp(d.keyframes,o);if(void 0!==e)return hu.update((()=>{d.onUpdate(e),d.onComplete()})),new Ym([])}return!n&&Pf.supports(d)?new Pf(d):new Cf(d)};function Of({protectedKeys:e,needsAnimating:t},r){const s=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,s}function Ff(e,t,{delay:r=0,transitionOverride:s,type:a}={}){var n;let{transition:i=e.getDefaultTransition(),transitionEnd:o,...l}=t;s&&(i=s);const c=[],d=a&&e.animationState&&e.animationState.getState()[a];for(const u in l){const t=e.getValue(u,null!==(n=e.latestValues[u])&&void 0!==n?n:null),s=l[u];if(void 0===s||d&&Of(d,u))continue;const a={delay:r,...Qm(i||{},u)};let o=!1;if(window.MotionHandoffAnimation){const t=Rh(e);if(t){const e=window.MotionHandoffAnimation(t,u,hu);null!==e&&(a.startTime=e,o=!0)}}Dh(e,u),t.start(Lf(u,t,s,e.shouldReduceMotion&&wh.has(u)?{type:!1}:a,e,o));const m=t.animation;m&&c.push(m)}return o&&Promise.all(c).then((()=>{hu.update((()=>{o&&function(e,t){const r=Hm(e,t);let{transitionEnd:s={},transition:a={},...n}=r||{};n={...n,...s};for(const o in n)Ph(e,o,(i=n[o],Ku(i)?i[i.length-1]||0:i));var i}(e,o)}))})),c}function Vf(e,t,r={}){var s;const a=Hm(e,t,"exit"===r.type?null===(s=e.presenceContext)||void 0===s?void 0:s.custom:void 0);let{transition:n=e.getDefaultTransition()||{}}=a||{};r.transitionOverride&&(n=r.transitionOverride);const i=a?()=>Promise.all(Ff(e,a,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(s=0)=>{const{delayChildren:a=0,staggerChildren:i,staggerDirection:o}=n;return function(e,t,r=0,s=0,a=1,n){const i=[],o=(e.variantChildren.size-1)*s,l=1===a?(e=0)=>e*s:(e=0)=>o-e*s;return Array.from(e.variantChildren).sort(zf).forEach(((e,s)=>{e.notify("AnimationStart",t),i.push(Vf(e,t,{...n,delay:r+l(s)}).then((()=>e.notify("AnimationComplete",t))))})),Promise.all(i)}(e,t,a+s,i,o,r)}:()=>Promise.resolve(),{when:l}=n;if(l){const[e,t]="beforeChildren"===l?[i,o]:[o,i];return e().then((()=>t()))}return Promise.all([i(),o(r.delay)])}function zf(e,t){return e.sortNodePosition(t)}const Bf=Su.length;function $f(e){if(!e)return;if(!e.isControllingVariants){const t=e.parent&&$f(e.parent)||{};return void 0!==e.props.initial&&(t.initial=e.props.initial),t}const t={};for(let r=0;r<Bf;r++){const s=Su[r],a=e.props[s];(Cu(a)||!1===a)&&(t[s]=a)}return t}const Uf=[...Nu].reverse(),qf=Nu.length;function Wf(e){let t=function(e){return t=>Promise.all(t.map((({animation:t,options:r})=>function(e,t,r={}){let s;if(e.notify("AnimationStart",t),Array.isArray(t)){const a=t.map((t=>Vf(e,t,r)));s=Promise.all(a)}else if("string"==typeof t)s=Vf(e,t,r);else{const a="function"==typeof t?Hm(e,t,r.custom):t;s=Promise.all(Ff(e,a,r))}return s.then((()=>{e.notify("AnimationComplete",t)}))}(e,t,r))))}(e),r=Gf(),s=!0;const a=t=>(r,s)=>{var a;const n=Hm(e,s,"exit"===t?null===(a=e.presenceContext)||void 0===a?void 0:a.custom:void 0);if(n){const{transition:e,transitionEnd:t,...s}=n;r={...r,...s,...t}}return r};function n(n){const{props:i}=e,o=$f(e.parent)||{},l=[],c=new Set;let d={},u=1/0;for(let t=0;t<qf;t++){const p=Uf[t],f=r[p],g=void 0!==i[p]?i[p]:o[p],x=Cu(g),y=p===n?f.isActive:null;!1===y&&(u=t);let v=g===o[p]&&g!==i[p]&&x;if(v&&s&&e.manuallyAnimateOnMount&&(v=!1),f.protectedKeys={...d},!f.isActive&&null===y||!g&&!f.prevProp||_u(g)||"boolean"==typeof g)continue;const b=(m=f.prevProp,"string"==typeof(h=g)?h!==m:!!Array.isArray(h)&&!Wm(h,m));let w=b||p===n&&f.isActive&&!v&&x||t>u&&x,j=!1;const k=Array.isArray(g)?g:[g];let E=k.reduce(a(p),{});!1===y&&(E={});const{prevResolvedValues:A={}}=f,C={...A,...E},_=t=>{w=!0,c.has(t)&&(j=!0,c.delete(t)),f.needsAnimating[t]=!0;const r=e.getValue(t);r&&(r.liveStyle=!1)};for(const e in C){const t=E[e],r=A[e];if(d.hasOwnProperty(e))continue;let s=!1;s=Ku(t)&&Ku(r)?!Wm(t,r):t!==r,s?null!=t?_(e):c.add(e):void 0!==t&&c.has(e)?_(e):f.protectedKeys[e]=!0}f.prevProp=g,f.prevResolvedValues=E,f.isActive&&(d={...d,...E}),s&&e.blockInitialAnimation&&(w=!1),w&&(!v||!b||j)&&l.push(...k.map((e=>({animation:e,options:{type:p}}))))}var m,h;if(c.size){const t={};c.forEach((r=>{const s=e.getBaseTarget(r),a=e.getValue(r);a&&(a.liveStyle=!0),t[r]=null!=s?s:null})),l.push({animation:t})}let p=Boolean(l.length);return!s||!1!==i.initial&&i.initial!==i.animate||e.manuallyAnimateOnMount||(p=!1),s=!1,p?t(l):Promise.resolve()}return{animateChanges:n,setActive:function(t,s){var a;if(r[t].isActive===s)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach((e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,s)})),r[t].isActive=s;const i=n(t);for(const e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=Gf(),s=!0}}}function Hf(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Gf(){return{animate:Hf(!0),whileInView:Hf(),whileHover:Hf(),whileTap:Hf(),whileDrag:Hf(),whileFocus:Hf(),exit:Hf()}}class Kf{constructor(e){this.isMounted=!1,this.node=e}update(){}}let Yf=0;const Qf={animation:{Feature:class extends Kf{constructor(e){super(e),e.animationState||(e.animationState=Wf(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();_u(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}},exit:{Feature:class extends Kf{constructor(){super(...arguments),this.id=Yf++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const s=this.node.animationState.setActive("exit",!e);t&&!e&&s.then((()=>t(this.id)))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}};function Xf(e,t,r,s={passive:!0}){return e.addEventListener(t,r,s),()=>e.removeEventListener(t,r)}function Jf(e){return{point:{x:e.pageX,y:e.pageY}}}function Zf(e,t,r,s){return Xf(e,t,(e=>t=>fh(t)&&e(t,Jf(t)))(r),s)}const eg=(e,t)=>Math.abs(e-t);class tg{constructor(e,t,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:a=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=ag(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){const r=eg(e.x,t.x),s=eg(e.y,t.y);return Math.sqrt(r**2+s**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;const{point:s}=e,{timestamp:a}=fu;this.history.push({...s,timestamp:a});const{onStart:n,onMove:i}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),i&&i(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rg(t,this.transformPagePoint),hu.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:r,onSessionEnd:s,resumeAnimation:a}=this.handlers;if(this.dragSnapToOrigin&&a&&a(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const n=ag("pointercancel"===e.type?this.lastMoveEventInfo:rg(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,n),s&&s(e,n)},!fh(e))return;this.dragSnapToOrigin=a,this.handlers=t,this.transformPagePoint=r,this.contextWindow=s||window;const n=rg(Jf(e),this.transformPagePoint),{point:i}=n,{timestamp:o}=fu;this.history=[{...i,timestamp:o}];const{onSessionStart:l}=t;l&&l(e,ag(n,this.history)),this.removeListeners=Qp(Zf(this.contextWindow,"pointermove",this.handlePointerMove),Zf(this.contextWindow,"pointerup",this.handlePointerUp),Zf(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),pu(this.updatePoint)}}function rg(e,t){return t?{point:t(e.point)}:e}function sg(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ag({point:e},t){return{point:e,delta:sg(e,ig(t)),offset:sg(e,ng(t)),velocity:og(t,.1)}}function ng(e){return e[0]}function ig(e){return e[e.length-1]}function og(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,s=null;const a=ig(e);for(;r>=0&&(s=e[r],!(a.timestamp-s.timestamp>cu(t)));)r--;if(!s)return{x:0,y:0};const n=du(a.timestamp-s.timestamp);if(0===n)return{x:0,y:0};const i={x:(a.x-s.x)/n,y:(a.y-s.y)/n};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function lg(e){return e.max-e.min}function cg(e,t,r,s=.5){e.origin=s,e.originPoint=$p(t.min,t.max,e.origin),e.scale=lg(r)/lg(t),e.translate=$p(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function dg(e,t,r,s){cg(e.x,t.x,r.x,s?s.originX:void 0),cg(e.y,t.y,r.y,s?s.originY:void 0)}function ug(e,t,r){e.min=r.min+t.min,e.max=e.min+lg(t)}function mg(e,t,r){e.min=t.min-r.min,e.max=e.min+lg(t)}function hg(e,t,r){mg(e.x,t.x,r.x),mg(e.y,t.y,r.y)}function pg(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function fg(e,t){let r=t.min-e.min,s=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,s]=[s,r]),{min:r,max:s}}const gg=.35;function xg(e,t,r){return{min:yg(e,t),max:yg(e,r)}}function yg(e,t){return"number"==typeof e?e:e[t]||0}const vg=()=>({x:{min:0,max:0},y:{min:0,max:0}});function bg(e){return[e("x"),e("y")]}function wg({top:e,left:t,right:r,bottom:s}){return{x:{min:t,max:r},y:{min:e,max:s}}}function jg(e){return void 0===e||1===e}function kg({scale:e,scaleX:t,scaleY:r}){return!jg(e)||!jg(t)||!jg(r)}function Eg(e){return kg(e)||Ag(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Ag(e){return Cg(e.x)||Cg(e.y)}function Cg(e){return e&&"0%"!==e}function _g(e,t,r){return r+t*(e-r)}function Ng(e,t,r,s,a){return void 0!==a&&(e=_g(e,a,s)),_g(e,r,s)+t}function Sg(e,t=0,r=1,s,a){e.min=Ng(e.min,t,r,s,a),e.max=Ng(e.max,t,r,s,a)}function Tg(e,{x:t,y:r}){Sg(e.x,t.translate,t.scale,t.originPoint),Sg(e.y,r.translate,r.scale,r.originPoint)}const Pg=.999999999999,Dg=1.0000000000001;function Rg(e,t){e.min=e.min+t,e.max=e.max+t}function Mg(e,t,r,s,a=.5){Sg(e,t,r,$p(e.min,e.max,a),s)}function Ig(e,t){Mg(e.x,t.x,t.scaleX,t.scale,t.originX),Mg(e.y,t.y,t.scaleY,t.scale,t.originY)}function Lg(e,t){return wg(function(e,t){if(!t)return e;const r=t({x:e.left,y:e.top}),s=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:s.y,right:s.x}}(e.getBoundingClientRect(),t))}const Og=({current:e})=>e?e.ownerDocument.defaultView:null,Fg=new WeakMap;class Vg{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e,{snapToCursor:t=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;const{dragSnapToOrigin:s}=this.getProps();this.panSession=new tg(e,{onSessionStart:e=>{const{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(Jf(e).point)},onStart:(e,t)=>{const{drag:r,dragPropagation:s,onDragStart:a}=this.getProps();if(r&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(n=r)||"y"===n?dh[n]?null:(dh[n]=!0,()=>{dh[n]=!1}):dh.x||dh.y?null:(dh.x=dh.y=!0,()=>{dh.x=dh.y=!1}),!this.openDragLock))return;var n;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),bg((e=>{let t=this.getAxisMotionValue(e).get()||0;if(hm.test(t)){const{projection:r}=this.visualElement;if(r&&r.layout){const s=r.layout.layoutBox[e];s&&(t=lg(s)*(parseFloat(t)/100))}}this.originPoint[e]=t})),a&&hu.postRender((()=>a(e,t))),Dh(this.visualElement,"transform");const{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:r,dragDirectionLock:s,onDirectionLock:a,onDrag:n}=this.getProps();if(!r&&!this.openDragLock)return;const{offset:i}=t;if(s&&null===this.currentDirection)return this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(i),void(null!==this.currentDirection&&a&&a(this.currentDirection));this.updateAxis("x",t.point,i),this.updateAxis("y",t.point,i),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>bg((e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:Og(this.visualElement)})}stop(e,t){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=t;this.startAnimation(s);const{onDragEnd:a}=this.getProps();a&&hu.postRender((()=>a(e,t)))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){const{drag:s}=this.getProps();if(!r||!zg(e,s,this.currentDirection))return;const a=this.getAxisMotionValue(e);let n=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:r},s){return void 0!==t&&e<t?e=s?$p(t,e,s.min):Math.max(e,t):void 0!==r&&e>r&&(e=s?$p(r,e,s.max):Math.min(e,r)),e}(n,this.constraints[e],this.elastic[e])),a.set(n)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,a=this.constraints;t&&Mu(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!s)&&function(e,{top:t,left:r,bottom:s,right:a}){return{x:pg(e.x,r,a),y:pg(e.y,t,s)}}(s.layoutBox,t),this.elastic=function(e=gg){return!1===e?e=0:!0===e&&(e=gg),{x:xg(e,"left","right"),y:xg(e,"top","bottom")}}(r),a!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&bg((e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(s.layoutBox[e],this.constraints[e]))}))}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!Mu(e))return!1;const r=e.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const a=function(e,t,r){const s=Lg(e,r),{scroll:a}=t;return a&&(Rg(s.x,a.offset.x),Rg(s.y,a.offset.y)),s}(r,s.root,this.visualElement.getTransformPagePoint());let n=function(e,t){return{x:fg(e.x,t.x),y:fg(e.y,t.y)}}(s.layout.layoutBox,a);if(t){const e=t(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(n));this.hasMutatedConstraints=!!e,e&&(n=wg(e))}return n}startAnimation(e){const{drag:t,dragMomentum:r,dragElastic:s,dragTransition:a,dragSnapToOrigin:n,onDragTransitionEnd:i}=this.getProps(),o=this.constraints||{},l=bg((i=>{if(!zg(i,t,this.currentDirection))return;let l=o&&o[i]||{};n&&(l={min:0,max:0});const c=s?200:1e6,d=s?40:1e7,u={type:"inertia",velocity:r?e[i]:0,bounceStiffness:c,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...a,...l};return this.startAxisValueAnimation(i,u)}));return Promise.all(l).then(i)}startAxisValueAnimation(e,t){const r=this.getAxisMotionValue(e);return Dh(this.visualElement,e),r.start(Lf(e,r,0,t,this.visualElement,!1))}stopAnimation(){bg((e=>this.getAxisMotionValue(e).stop()))}pauseAnimation(){bg((e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()}))}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){bg((t=>{const{drag:r}=this.getProps();if(!zg(t,r,this.currentDirection))return;const{projection:s}=this.visualElement,a=this.getAxisMotionValue(t);if(s&&s.layout){const{min:r,max:n}=s.layout.layoutBox[t];a.set(e[t]-$p(r,n,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!Mu(t)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};bg((e=>{const t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){const r=t.get();s[e]=function(e,t){let r=.5;const s=lg(e),a=lg(t);return a>s?r=lu(t.min,t.max-s,e.min):s>a&&(r=lu(e.min,e.max-a,t.min)),om(0,1,r)}({min:r,max:r},this.constraints[e])}}));const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),bg((t=>{if(!zg(t,e,null))return;const r=this.getAxisMotionValue(t),{min:a,max:n}=this.constraints[t];r.set($p(a,n,s[t]))}))}addListeners(){if(!this.visualElement.current)return;Fg.set(this.visualElement,this);const e=Zf(this.visualElement.current,"pointerdown",(e=>{const{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)})),t=()=>{const{dragConstraints:e}=this.getProps();Mu(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,s=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),hu.read(t);const a=Xf(window,"resize",(()=>this.scalePositionWithinConstraints())),n=r.addEventListener("didUpdate",(({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(bg((t=>{const r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))})),this.visualElement.render())}));return()=>{a(),e(),s(),n&&n()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:a=!1,dragElastic:n=gg,dragMomentum:i=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:s,dragConstraints:a,dragElastic:n,dragMomentum:i}}}function zg(e,t,r){return!(!0!==t&&t!==e||null!==r&&r!==e)}const Bg=e=>(t,r)=>{e&&hu.postRender((()=>e(t,r)))},$g={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ug(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const qg={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!pm.test(e))return e;e=parseFloat(e)}return`${Ug(e,t.target.x)}% ${Ug(e,t.target.y)}%`}},Wg={correct:(e,{treeScale:t,projectionDelta:r})=>{const s=e,a=dp.parse(e);if(a.length>5)return s;const n=dp.createTransformer(e),i="number"!=typeof a[0]?1:0,o=r.x.scale*t.x,l=r.y.scale*t.y;a[0+i]/=o,a[1+i]/=l;const c=$p(o,l,.5);return"number"==typeof a[2+i]&&(a[2+i]/=c),"number"==typeof a[3+i]&&(a[3+i]/=c),n(a)}};class Hg extends T.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:s}=this.props,{projection:a}=e;var n;n=Kg,Object.assign(Rm,n),a&&(t.group&&t.group.add(a),r&&r.register&&s&&r.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",(()=>{this.safeToRemove()})),a.setOptions({...a.options,onExitComplete:()=>this.safeToRemove()})),$g.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:r,drag:s,isPresent:a}=this.props,n=r.projection;return n?(n.isPresent=a,s||e.layoutDependency!==t||void 0===t?n.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?n.promote():n.relegate()||hu.postRender((()=>{const e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Fu.postRender((()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Gg(e){const[t,r]=ru(),s=T.useContext(Jd);return a.jsx(Hg,{...e,layoutGroup:s,switchLayoutGroup:T.useContext(Vu),isPresent:t,safeToRemove:r})}const Kg={borderRadius:{...qg,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:qg,borderTopRightRadius:qg,borderBottomLeftRadius:qg,borderBottomRightRadius:qg,boxShadow:Wg},Yg=(e,t)=>e.depth-t.depth;class Qg{constructor(){this.children=[],this.isDirty=!1}add(e){Ah(this.children,e),this.isDirty=!0}remove(e){Ch(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Yg),this.isDirty=!1,this.children.forEach(e)}}const Xg=["TopLeft","TopRight","BottomLeft","BottomRight"],Jg=Xg.length,Zg=e=>"string"==typeof e?parseFloat(e):e,ex=e=>"number"==typeof e||pm.test(e);function tx(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const rx=ax(0,.5,Uh),sx=ax(.5,.95,nu);function ax(e,t,r){return s=>s<e?0:s>t?1:r(lu(e,t,s))}function nx(e,t){e.min=t.min,e.max=t.max}function ix(e,t){nx(e.x,t.x),nx(e.y,t.y)}function ox(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function lx(e,t,r,s,a){return e=_g(e-=t,1/r,s),void 0!==a&&(e=_g(e,1/a,s)),e}function cx(e,t,[r,s,a],n,i){!function(e,t=0,r=1,s=.5,a,n=e,i=e){if(hm.test(t)&&(t=parseFloat(t),t=$p(i.min,i.max,t/100)-i.min),"number"!=typeof t)return;let o=$p(n.min,n.max,s);e===n&&(o-=t),e.min=lx(e.min,t,r,o,a),e.max=lx(e.max,t,r,o,a)}(e,t[r],t[s],t[a],t.scale,n,i)}const dx=["x","scaleX","originX"],ux=["y","scaleY","originY"];function mx(e,t,r,s){cx(e.x,t,dx,r?r.x:void 0,s?s.x:void 0),cx(e.y,t,ux,r?r.y:void 0,s?s.y:void 0)}function hx(e){return 0===e.translate&&1===e.scale}function px(e){return hx(e.x)&&hx(e.y)}function fx(e,t){return e.min===t.min&&e.max===t.max}function gx(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function xx(e,t){return gx(e.x,t.x)&&gx(e.y,t.y)}function yx(e){return lg(e.x)/lg(e.y)}function vx(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class bx{constructor(){this.members=[]}add(e){Ah(this.members,e),e.scheduleRender()}remove(e){if(Ch(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex((t=>e===t));if(0===t)return!1;let r;for(let s=t;s>=0;s--){const e=this.members[s];if(!1!==e.isPresent){r=e;break}}return!!r&&(this.promote(r),!0)}promote(e,t){const r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:s}=e.options;!1===s&&r.hide()}}exitAnimationComplete(){this.members.forEach((e=>{const{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()}))}scheduleRender(){this.members.forEach((e=>{e.instance&&e.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const wx={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},jx="undefined"!=typeof window&&void 0!==window.MotionDebug,kx=["","X","Y","Z"],Ex={visibility:"hidden"};let Ax=0;function Cx(e,t,r,s){const{latestValues:a}=t;a[e]&&(r[e]=a[e],t.setStaticValue(e,0),s&&(s[e]=0))}function _x(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const r=Rh(t);if(window.MotionHasOptimisedAnimation(r,"transform")){const{layout:t,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",hu,!(t||s))}const{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&_x(s)}function Nx({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:s,resetTransform:a}){return class{constructor(e={},r=(null==t?void 0:t())){this.id=Ax++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,jx&&(wx.totalNodes=wx.resolvedTargetDeltas=wx.recalculatedProjection=0),this.nodes.forEach(Px),this.nodes.forEach(Fx),this.nodes.forEach(Vx),this.nodes.forEach(Dx),jx&&window.MotionDebug.record(wx)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Qg)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new _h),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){const r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;var s;this.isSVG=(s=t)instanceof SVGElement&&"svg"!==s.tagName,this.instance=t;const{layoutId:a,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||a)&&(this.isLayoutDirty=!0),e){let r;const s=()=>this.root.updateBlockedByResize=!1;e(t,(()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e){const t=Eh.now(),r=({timestamp:s})=>{const a=s-t;a>=250&&(pu(r),e(a-250))};return hu.read(r,!0),()=>pu(r)}(s),$g.hasAnimatedSinceResize&&($g.hasAnimatedSinceResize=!1,this.nodes.forEach(Ox))}))}a&&this.root.registerSharedNode(a,this),!1!==this.options.animate&&i&&(a||n)&&this.addEventListener("didUpdate",(({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:s})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const a=this.options.transition||i.getDefaultTransition()||Wx,{onLayoutAnimationStart:n,onLayoutAnimationComplete:o}=i.getProps(),l=!this.targetLayout||!xx(this.targetLayout,s)||r,c=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||c||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,c);const t={...Qm(a,"layout"),onPlay:n,onComplete:o};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||Ox(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,pu(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(zx),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&_x(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){const e=this.path[a];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;const s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Mx);this.isUpdating||this.nodes.forEach(Ix),this.isUpdating=!1,this.nodes.forEach(Lx),this.nodes.forEach(Sx),this.nodes.forEach(Tx),this.clearAllSnapshots();const e=Eh.now();fu.delta=om(0,1e3/60,e-fu.timestamp),fu.timestamp=e,fu.isProcessing=!0,gu.update.process(fu),gu.preRender.process(fu),gu.render.process(fu),fu.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Fu.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Rx),this.sharedNodes.forEach(Bx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,hu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){hu.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let r=0;r<this.path.length;r++)this.path[r].updateScroll();const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){const t=s(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!a)return;const e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!px(this.projectionDelta),r=this.getTransformTemplate(),s=r?r(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;e&&(t||Eg(this.latestValues)||n)&&(a(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){const t=this.measurePageBox();let r=this.removeElementScroll(t);var s;return e&&(r=this.removeTransform(r)),Kx((s=r).x),Kx(s.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const r=t.measureViewportBox();if(!(null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)&&!this.path.some(Qx)){const{scroll:e}=this.root;e&&(Rg(r.x,e.offset.x),Rg(r.y,e.offset.y))}return r}removeElementScroll(e){var t;const r={x:{min:0,max:0},y:{min:0,max:0}};if(ix(r,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return r;for(let s=0;s<this.path.length;s++){const t=this.path[s],{scroll:a,options:n}=t;t!==this.root&&a&&n.layoutScroll&&(a.wasRoot&&ix(r,e),Rg(r.x,a.offset.x),Rg(r.y,a.offset.y))}return r}applyTransform(e,t=!1){const r={x:{min:0,max:0},y:{min:0,max:0}};ix(r,e);for(let s=0;s<this.path.length;s++){const e=this.path[s];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&Ig(r,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Eg(e.latestValues)&&Ig(r,e.latestValues)}return Eg(this.latestValues)&&Ig(r,this.latestValues),r}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};ix(t,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];if(!e.instance)continue;if(!Eg(e.latestValues))continue;kg(e.latestValues)&&e.updateSnapshot();const s={x:{min:0,max:0},y:{min:0,max:0}};ix(s,e.measurePageBox()),mx(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,s)}return Eg(this.latestValues)&&mx(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==fu.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t;const r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);const s=Boolean(this.resumingFrom)||this!==r;if(!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:a,layoutId:n}=this.options;if(this.layout&&(a||n)){if(this.resolvedRelativeTargetAt=fu.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},hg(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),ix(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var i,o,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),i=this.target,o=this.relativeTarget,l=this.relativeParent.target,ug(i.x,o.x,l.x),ug(i.y,o.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):ix(this.target,this.layout.layoutBox),Tg(this.target,this.targetDelta)):ix(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},hg(this.relativeTargetOrigin,this.target,e.target),ix(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}jx&&wx.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!kg(this.parent.latestValues)&&!Ag(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),r=Boolean(this.resumingFrom)||this!==t;let s=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(s=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===fu.timestamp&&(s=!1),s)return;const{layout:a,layoutId:n}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!a&&!n)return;ix(this.layoutCorrected,this.layout.layoutBox);const i=this.treeScale.x,o=this.treeScale.y;!function(e,t,r,s=!1){const a=r.length;if(!a)return;let n,i;t.x=t.y=1;for(let o=0;o<a;o++){n=r[o],i=n.projectionDelta;const{visualElement:a}=n.options;a&&a.props.style&&"contents"===a.props.style.display||(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&Ig(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,Tg(e,i)),s&&Eg(n.latestValues)&&Ig(e,n.latestValues))}t.x<Dg&&t.x>Pg&&(t.x=1),t.y<Dg&&t.y>Pg&&(t.y=1)}(this.layoutCorrected,this.treeScale,this.path,r),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=t;l?(this.projectionDelta&&this.prevProjectionDelta?(ox(this.prevProjectionDelta.x,this.projectionDelta.x),ox(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dg(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===i&&this.treeScale.y===o&&vx(this.projectionDelta.x,this.prevProjectionDelta.x)&&vx(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),jx&&wx.recalculatedProjection++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(e,t=!1){const r=this.snapshot,s=r?r.latestValues:{},a={...this.latestValues},n={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const i={x:{min:0,max:0},y:{min:0,max:0}},o=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,d=Boolean(o&&!c&&!0===this.options.crossfade&&!this.path.some(qx));let u;this.animationProgress=0,this.mixTargetDelta=t=>{const r=t/1e3;var l,m,h,p,f,g;$x(n.x,e.x,r),$x(n.y,e.y,r),this.setTargetDelta(n),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(hg(i,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,f=i,g=r,Ux(h.x,p.x,f.x,g),Ux(h.y,p.y,f.y,g),u&&(l=this.relativeTarget,m=u,fx(l.x,m.x)&&fx(l.y,m.y))&&(this.isProjectionDirty=!1),u||(u={x:{min:0,max:0},y:{min:0,max:0}}),ix(u,this.relativeTarget)),o&&(this.animationValues=a,function(e,t,r,s,a,n){a?(e.opacity=$p(0,void 0!==r.opacity?r.opacity:1,rx(s)),e.opacityExit=$p(void 0!==t.opacity?t.opacity:1,0,sx(s))):n&&(e.opacity=$p(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,s));for(let i=0;i<Jg;i++){const a=`border${Xg[i]}Radius`;let n=tx(t,a),o=tx(r,a);void 0===n&&void 0===o||(n||(n=0),o||(o=0),0===n||0===o||ex(n)===ex(o)?(e[a]=Math.max($p(Zg(n),Zg(o),s),0),(hm.test(o)||hm.test(n))&&(e[a]+="%")):e[a]=o)}(t.rotate||r.rotate)&&(e.rotate=$p(t.rotate||0,r.rotate||0,s))}(a,s,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(pu(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=hu.update((()=>{$g.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){const s=Yu(0)?0:Th(0);return s.start(Lf("",s,1e3,r)),s.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:r,layout:s,latestValues:a}=e;if(t&&r&&s){if(this!==e&&this.layout&&s&&Yx(this.options.animationType,this.layout.layoutBox,s.layoutBox)){r=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=lg(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;const s=lg(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+s}ix(t,r),Ig(t,a),dg(this.projectionDeltaWithTransform,this.layoutCorrected,t,a)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new bx),this.sharedNodes.get(e).add(t);const r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){const s=this.getStack();s&&s.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;const s={};r.z&&Cx("z",e,s,this.animationValues);for(let a=0;a<kx.length;a++)Cx(`rotate${kx[a]}`,e,s,this.animationValues),Cx(`skew${kx[a]}`,e,s,this.animationValues);e.render();for(const a in s)e.setStaticValue(a,s[a]),this.animationValues&&(this.animationValues[a]=s[a]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Ex;const s={visibility:""},a=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=Qu(null==e?void 0:e.pointerEvents)||"",s.transform=a?a(this.latestValues,""):"none",s;const n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Qu(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!Eg(this.latestValues)&&(t.transform=a?a({},""):"none",this.hasProjected=!1),t}const i=n.animationValues||n.latestValues;this.applyTransformsToTarget(),s.transform=function(e,t,r){let s="";const a=e.x.translate/t.x,n=e.y.translate/t.y,i=(null==r?void 0:r.z)||0;if((a||n||i)&&(s=`translate3d(${a}px, ${n}px, ${i}px) `),1===t.x&&1===t.y||(s+=`scale(${1/t.x}, ${1/t.y}) `),r){const{transformPerspective:e,rotate:t,rotateX:a,rotateY:n,skewX:i,skewY:o}=r;e&&(s=`perspective(${e}px) ${s}`),t&&(s+=`rotate(${t}deg) `),a&&(s+=`rotateX(${a}deg) `),n&&(s+=`rotateY(${n}deg) `),i&&(s+=`skewX(${i}deg) `),o&&(s+=`skewY(${o}deg) `)}const o=e.x.scale*t.x,l=e.y.scale*t.y;return 1===o&&1===l||(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),a&&(s.transform=a(i,s.transform));const{x:o,y:l}=this.projectionDelta;s.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,n.animationValues?s.opacity=n===this?null!==(r=null!==(t=i.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:s.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(const c in Rm){if(void 0===i[c])continue;const{correct:e,applyTo:t}=Rm[c],r="none"===s.transform?i[c]:e(i[c],n);if(t){const e=t.length;for(let a=0;a<e;a++)s[t[a]]=r}else s[c]=r}return this.options.layoutId&&(s.pointerEvents=n===this?Qu(null==e?void 0:e.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()})),this.root.nodes.forEach(Mx),this.root.sharedNodes.clear()}}}function Sx(e){e.updateLayout()}function Tx(e){var t;const r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:s}=e.layout,{animationType:a}=e.options,n=r.source!==e.layout.source;"size"===a?bg((e=>{const s=n?r.measuredBox[e]:r.layoutBox[e],a=lg(s);s.min=t[e].min,s.max=s.min+a})):Yx(a,r.layoutBox,t)&&bg((s=>{const a=n?r.measuredBox[s]:r.layoutBox[s],i=lg(t[s]);a.max=a.min+i,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[s].max=e.relativeTarget[s].min+i)}));const i={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};dg(i,t,r.layoutBox);const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};n?dg(o,e.applyTransform(s,!0),r.measuredBox):dg(o,t,r.layoutBox);const l=!px(i);let c=!1;if(!e.resumeFrom){const s=e.getClosestProjectingParent();if(s&&!s.resumeFrom){const{snapshot:a,layout:n}=s;if(a&&n){const i={x:{min:0,max:0},y:{min:0,max:0}};hg(i,r.layoutBox,a.layoutBox);const o={x:{min:0,max:0},y:{min:0,max:0}};hg(o,t,n.layoutBox),xx(i,o)||(c=!0),s.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=i,e.relativeParent=s)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:o,layoutDelta:i,hasLayoutChanged:l,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Px(e){jx&&wx.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Dx(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Rx(e){e.clearSnapshot()}function Mx(e){e.clearMeasurements()}function Ix(e){e.isLayoutDirty=!1}function Lx(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ox(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Fx(e){e.resolveTargetDelta()}function Vx(e){e.calcProjection()}function zx(e){e.resetSkewAndRotation()}function Bx(e){e.removeLeadSnapshot()}function $x(e,t,r){e.translate=$p(t.translate,0,r),e.scale=$p(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function Ux(e,t,r,s){e.min=$p(t.min,r.min,s),e.max=$p(t.max,r.max,s)}function qx(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const Wx={duration:.45,ease:[.4,0,.1,1]},Hx=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Gx=Hx("applewebkit/")&&!Hx("chrome/")?Math.round:nu;function Kx(e){e.min=Gx(e.min),e.max=Gx(e.max)}function Yx(e,t,r){return"position"===e||"preserve-aspect"===e&&(s=yx(t),a=yx(r),!(Math.abs(s-a)<=.2));var s,a}function Qx(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}const Xx=Nx({attachResizeListener:(e,t)=>Xf(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Jx={current:void 0},Zx=Nx({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Jx.current){const e=new Xx({});e.mount(window),e.setOptions({layoutScroll:!0}),Jx.current=e}return Jx.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),ey={pan:{Feature:class extends Kf{constructor(){super(...arguments),this.removePointerDownListener=nu}onPointerDown(e){this.session=new tg(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Og(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:Bg(e),onStart:Bg(t),onMove:r,onEnd:(e,t)=>{delete this.session,s&&hu.postRender((()=>s(e,t)))}}}mount(){this.removePointerDownListener=Zf(this.node.current,"pointerdown",(e=>this.onPointerDown(e)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Kf{constructor(e){super(e),this.removeGroupControls=nu,this.removeListeners=nu,this.controls=new Vg(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||nu}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Zx,MeasureLayout:Gg}};function ty(e,t,r){const{props:s}=e;e.animationState&&s.whileHover&&e.animationState.setActive("whileHover","Start"===r);const a=s["onHover"+r];a&&hu.postRender((()=>a(t,Jf(t))))}function ry(e,t,r){const{props:s}=e;e.animationState&&s.whileTap&&e.animationState.setActive("whileTap","Start"===r);const a=s["onTap"+("End"===r?"":r)];a&&hu.postRender((()=>a(t,Jf(t))))}const sy=new WeakMap,ay=new WeakMap,ny=e=>{const t=sy.get(e.target);t&&t(e)},iy=e=>{e.forEach(ny)};const oy={some:0,all:1},ly={inView:{Feature:class extends Kf{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:s="some",once:a}=e,n={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof s?s:oy[s]};return function(e,t,r){const s=function({root:e,...t}){const r=e||document;ay.has(r)||ay.set(r,{});const s=ay.get(r),a=JSON.stringify(t);return s[a]||(s[a]=new IntersectionObserver(iy,{root:e,...t})),s[a]}(t);return sy.set(e,r),s.observe(e),()=>{sy.delete(e),s.unobserve(e)}}(this.node.current,n,(e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,a&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:r,onViewportLeave:s}=this.node.getProps(),n=t?r:s;n&&n(e)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Kf{mount(){const{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){const[s,a,n]=mh(e,r),i=e=>{const s=e.currentTarget;if(!bh(e)||xh.has(s))return;xh.add(s);const n=t(e),i=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),bh(e)&&xh.has(s)&&(xh.delete(s),"function"==typeof n&&n(e,{success:t}))},o=e=>{i(e,r.useGlobalTarget||ph(s,e.target))},l=e=>{i(e,!1)};window.addEventListener("pointerup",o,a),window.addEventListener("pointercancel",l,a)};return s.forEach((e=>{(function(e){return gh.has(e.tagName)||-1!==e.tabIndex})(e)||null!==e.getAttribute("tabindex")||(e.tabIndex=0),(r.useGlobalTarget?window:e).addEventListener("pointerdown",i,a),e.addEventListener("focus",(e=>((e,t)=>{const r=e.currentTarget;if(!r)return;const s=yh((()=>{if(xh.has(r))return;vh(r,"down");const e=yh((()=>{vh(r,"up")}));r.addEventListener("keyup",e,t),r.addEventListener("blur",(()=>vh(r,"cancel")),t)}));r.addEventListener("keydown",s,t),r.addEventListener("blur",(()=>r.removeEventListener("keydown",s)),t)})(e,a)),a)})),n}(e,(e=>(ry(this.node,e,"Start"),(e,{success:t})=>ry(this.node,e,t?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Kf{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Qp(Xf(this.node.current,"focus",(()=>this.onFocus())),Xf(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Kf{mount(){const{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){const[s,a,n]=mh(e,r),i=hh((e=>{const{target:r}=e,s=t(e);if("function"!=typeof s||!r)return;const n=hh((e=>{s(e),r.removeEventListener("pointerleave",n)}));r.addEventListener("pointerleave",n,a)}));return s.forEach((e=>{e.addEventListener("pointerenter",i,a)})),n}(e,(e=>(ty(this.node,e,"Start"),e=>ty(this.node,e,"End")))))}unmount(){}}}},cy={layout:{ProjectionNode:Zx,MeasureLayout:Gg}},dy={current:null},uy={current:!1},my=[...Ip,tp,dp],hy=new WeakMap,py=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class fy{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:a,visualState:n},i={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const e=Eh.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,hu.render(this.render,!1,!0))};const{latestValues:o,renderState:l,onUpdate:c}=n;this.onUpdate=c,this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=i,this.blockInitialAnimation=Boolean(a),this.isControllingVariants=Tu(t),this.isVariantNode=Pu(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(e&&e.current);const{willChange:d,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(const m in u){const e=u[m];void 0!==o[m]&&Yu(e)&&e.set(o[m],!1)}}mount(e){this.current=e,hy.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((e,t)=>this.bindToMotionValue(t,e))),uy.current||function(){if(uy.current=!0,su)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>dy.current=e.matches;e.addListener(t),t()}else dy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||dy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){hy.delete(this.current),this.projection&&this.projection.unmount(),pu(this.notifyUpdate),pu(this.render),this.valueSubscriptions.forEach((e=>e())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const r=em.has(e),s=t.on("change",(t=>{this.latestValues[e]=t,this.props.onUpdate&&hu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)})),a=t.on("renderRequest",this.scheduleRender);let n;window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,(()=>{s(),a(),n&&n(),t.owner&&t.stop()}))}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in vu){const t=vu[e];if(!t)continue;const{isEnabled:r,Feature:s}=t;if(!this.features[e]&&s&&r(this.props)&&(this.features[e]=new s(this)),this.features[e]){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let r=0;r<py.length;r++){const t=py[r];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const s=e["on"+t];s&&(this.propEventSubscriptions[t]=this.on(t,s))}this.prevMotionValues=function(e,t,r){for(const s in t){const a=t[s],n=r[s];if(Yu(a))e.addValue(s,a);else if(Yu(n))e.addValue(s,Th(a,{owner:e}));else if(n!==a)if(e.hasValue(s)){const t=e.getValue(s);!0===t.liveStyle?t.jump(a):t.hasAnimated||t.set(a)}else{const t=e.getStaticValue(s);e.addValue(s,Th(void 0!==t?t:a,{owner:e}))}}for(const s in r)void 0===t[s]&&e.removeValue(s);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){const r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=Th(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){var r;let s=void 0===this.latestValues[e]&&this.current?null!==(r=this.getBaseTargetFromProps(this.props,e))&&void 0!==r?r:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];var a;return null!=s&&("string"==typeof s&&(Pp(s)||Wh(s))?s=parseFloat(s):(a=s,!my.find(Mp(a))&&dp.test(t)&&(s=xp(e,t))),this.setBaseTarget(e,Yu(s)?s.get():s)),Yu(s)?s.get():s}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:r}=this.props;let s;if("string"==typeof r||"object"==typeof r){const a=Gu(this.props,r,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);a&&(s=a[e])}if(r&&void 0!==s)return s;const a=this.getBaseTargetFromProps(this.props,e);return void 0===a||Yu(a)?void 0!==this.initialValues[e]&&void 0===s?void 0:this.baseTarget[e]:a}on(e,t){return this.events[e]||(this.events[e]=new _h),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class gy extends fy{constructor(){super(...arguments),this.KeyframeResolver=Op}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;Yu(e)&&(this.childSubscription=e.on("change",(e=>{this.current&&(this.current.textContent=`${e}`)})))}}class xy extends gy{constructor(){super(...arguments),this.type="html",this.renderInstance=Tm}readValueFromInstance(e,t){if(em.has(t)){const e=gp(t);return e&&e.default||0}{const s=(r=e,window.getComputedStyle(r)),a=(rm(t)?s.getPropertyValue(t):s[t])||0;return"string"==typeof a?a.trim():a}var r}measureInstanceViewportBox(e,{transformPagePoint:t}){return Lg(e,t)}build(e,t,r){Em(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return Im(e,t,r)}}class yy extends gy{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=vg}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(em.has(t)){const e=gp(t);return e&&e.default||0}return t=Pm.has(t)?t:Lu(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return Lm(e,t,r)}build(e,t,r){Nm(e,t,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,r,s){Dm(e,t,0,s)}mount(e){this.isSVGTag=Sm(e.tagName),super.mount(e)}}const vy=(e,t)=>Wu(e)?new yy(t):new xy(t,{allowProjection:e!==T.Fragment}),by=Eu(qm({...Qf,...ly,...ey,...cy},vy));function wy({responses:e,isResolved:t,newResponse:r,onResponseChange:s,onSendResponse:n}){return a.jsxs("div",{className:"space-y-4",children:[a.jsx(Qd,{className:"h-[300px] rounded-lg border border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-800 p-4",children:a.jsx("div",{className:"space-y-4",children:e?.map(((e,t)=>a.jsx(by.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"group",children:a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-lg transform -skew-x-3 group-hover:skew-x-0 transition-transform duration-300"}),a.jsxs("div",{className:"relative bg-white dark:bg-slate-800 p-4 rounded-lg border border-primary/20 dark:border-slate-600 shadow-md transform hover:-translate-y-1 transition-all duration-300",children:[a.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[a.jsx("span",{className:"font-semibold text-gray-900 dark:text-gray-100",children:e.user.full_name}),e.user.is_admin&&a.jsx(Zc,{variant:"default",className:"bg-gradient-to-r from-primary to-primary/70 text-white font-medium",children:"Admin"})]}),a.jsx("div",{className:"text-gray-800 dark:text-gray-200 leading-relaxed font-normal whitespace-pre-wrap",children:e.message})]})]})},e.id)))})}),t?a.jsx(by.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"p-4 rounded-lg bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-800 text-center text-green-700 dark:text-green-300 font-medium",children:"✅ Este feedback foi marcado como resolvido e não aceita mais respostas."}):a.jsxs(by.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-3",children:[a.jsx(Ld,{placeholder:"Digite sua resposta...",value:r,onChange:e=>s(e.target.value),className:"min-h-[100px] bg-white dark:bg-slate-800 border-primary/20 dark:border-slate-600 focus:border-primary/40 focus:ring-primary/40 text-gray-800 dark:text-gray-200 placeholder:text-gray-500 dark:placeholder:text-gray-400"}),a.jsxs(Es,{onClick:n,disabled:!r?.trim(),className:"w-full bg-gradient-to-r from-primary to-primary/70 hover:from-primary/90 hover:to-primary/60 text-white font-medium shadow-md transform hover:-translate-y-1 transition-all duration-300",children:[a.jsx(Jt,{className:"h-4 w-4 mr-2"}),"Enviar Resposta"]})]})]})}function jy({status:e}){const t=(e=>{switch(e){case"aguardando":return{label:"Aguardando",className:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800"};case"em_andamento":return{label:"Em Andamento",className:"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800"};case"resolvido":return{label:"Resolvido",className:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"};default:return{label:e,className:"bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-gray-200 dark:border-gray-700"}}})(e);return a.jsx(by.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},whileHover:{scale:1.05},className:"transform-gpu",children:a.jsx(Zc,{variant:"outline",className:`${t.className} px-3 py-1 text-sm font-medium border shadow-sm`,children:t.label})})}function ky(){const[e,t]=T.useState([]),[r,s]=T.useState(!0),[n,i]=T.useState({}),{user:o}=La(),{toast:l}=Zl(),c=async()=>{if(o){s(!0);try{const{data:e,error:r}=await Ts.from("pedbook_feedbacks").select("*").eq("user_id",o.id).order("created_at",{ascending:!1});if(r)return void l({title:"Erro ao carregar feedbacks",description:r.message,variant:"destructive"});if(e){const r=e.filter((e=>"dr_will"!==e.feedback_category&&"dr_will"!==e.feedback_type)),s=r.map((e=>e.id)),{data:a}=await Ts.from("pedbook_feedback_responses").select("\n            id,\n            feedback_id,\n            message,\n            created_at,\n            user_id\n          ").in("feedback_id",s).order("created_at",{ascending:!0}),n=[...new Set(a?.map((e=>e.user_id))||[])],{data:i}=await Ts.from("public_profiles").select("id, full_name, is_admin").in("id",n),o=r.map((e=>{const t=(a?.filter((t=>t.feedback_id===e.id))||[]).map((e=>{const t=i?.find((t=>t.id===e.user_id))||{full_name:"Usuário",is_admin:!1};return{...e,user:t}})).sort(((e,t)=>new Date(e.created_at).getTime()-new Date(t.created_at).getTime()));return{...e,responses:t}}));t(o)}else t([])}catch(e){l({title:"Erro ao carregar feedbacks",description:"Ocorreu um erro ao carregar os feedbacks",variant:"destructive"})}finally{s(!1)}}};return T.useEffect((()=>{o&&c()}),[o]),r?a.jsx("div",{className:"flex items-center justify-center min-h-[300px]",children:a.jsxs("div",{className:"text-center space-y-4",children:[a.jsx("div",{className:"w-16 h-16 mx-auto border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200",children:"Carregando feedbacks..."}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Buscando seus feedbacks"})]})]})}):0===e.length?a.jsx(by.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center p-6 sm:p-12 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-800 dark:via-slate-800 dark:to-slate-800 rounded-2xl border border-blue-200 dark:border-slate-700 shadow-lg mx-2 sm:mx-0",children:a.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[a.jsx("div",{className:"text-4xl sm:text-6xl",children:"📝"}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-200",children:"Nenhum feedback ainda"}),a.jsx("p",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-400 px-2",children:"Você ainda não enviou nenhum feedback. Que tal compartilhar suas ideias?"})]})]})}):a.jsx("div",{className:"space-y-6",children:e.map(((e,t)=>a.jsx(by.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:a.jsx(gi,{className:"overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-white dark:from-slate-800 dark:via-slate-800/90 dark:to-slate-800 border border-blue-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300 sm:hover:scale-[1.02] rounded-2xl mx-2 sm:mx-0",children:a.jsxs("div",{className:"p-3 sm:p-6 space-y-4 sm:space-y-6",children:[a.jsx("div",{className:"space-y-3",children:a.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-4",children:[a.jsxs("div",{className:"space-y-2 flex-1 min-w-0",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex-shrink-0"}),a.jsx("h3",{className:"text-lg sm:text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent break-words",children:e.title})]}),a.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed bg-gray-50 dark:bg-slate-700/50 p-3 sm:p-4 rounded-xl border border-gray-200 dark:border-slate-600 whitespace-pre-wrap text-sm sm:text-base break-words overflow-hidden",children:e.message}),a.jsxs("div",{className:"flex items-center gap-2 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:[a.jsx("span",{children:"📅"}),a.jsx("span",{className:"break-words",children:new Date(e.created_at).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),a.jsx("div",{className:"flex-shrink-0 self-start",children:a.jsx(jy,{status:e.status})})]})}),a.jsxs("div",{className:"border-t border-blue-100 dark:border-slate-700 pt-4 sm:pt-6",children:[a.jsxs("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-3 sm:mb-4 flex items-center gap-2 text-sm sm:text-base",children:[a.jsx("span",{children:"💬"}),a.jsx("span",{children:"Respostas"}),e.responses.length>0&&a.jsx("span",{className:"bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full text-xs font-medium",children:e.responses.length})]}),a.jsx("div",{className:"overflow-hidden",children:a.jsx(wy,{responses:e.responses,feedbackId:e.id,isResolved:"resolvido"===e.status,newResponse:n[e.id]||"",onResponseChange:t=>i((r=>({...r,[e.id]:t}))),onSendResponse:()=>(async e=>{if(!o)return;const t=n[e];if(t?.trim())try{const{error:r}=await Ts.from("pedbook_feedback_responses").insert({feedback_id:e,message:t.trim(),user_id:o.id});if(r)throw r;l({title:"Resposta enviada",description:"Sua resposta foi enviada com sucesso!"}),i((t=>({...t,[e]:""}))),c()}catch(r){l({title:"Erro ao enviar resposta",description:r.message,variant:"destructive"})}})(e.id)})})]})]})})},e.id)))})}function Ey(){const{user:e}=La();return e?a.jsxs("div",{className:"container mx-auto px-2 sm:px-4 py-4 sm:py-6 space-y-4 sm:space-y-6 mobile-feedback-container",children:[a.jsxs("div",{className:"text-center space-y-2 sm:space-y-3 pb-4 sm:pb-6",children:[a.jsx("h1",{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Central de Feedback"}),a.jsx("p",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-400 max-w-2xl mx-auto px-2",children:"Compartilhe suas ideias, reporte problemas ou acompanhe suas solicitações"})]}),a.jsxs(Hd,{defaultValue:"new",className:"space-y-4 sm:space-y-6",children:[a.jsxs(Gd,{className:"w-full grid grid-cols-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-800 p-1 rounded-2xl border border-blue-200 dark:border-slate-700 shadow-lg h-auto min-h-[3rem] sm:min-h-[3.5rem]",children:[a.jsx(Kd,{value:"new",className:"data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl font-semibold transition-all duration-200 data-[state=active]:scale-105 h-auto py-2 sm:py-3 px-2 sm:px-4",children:a.jsxs("span",{className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2",children:[a.jsx("span",{className:"text-lg sm:text-base",children:"✨"}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:"Novo Feedback"})]})}),a.jsx(Kd,{value:"history",className:"data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl font-semibold transition-all duration-200 data-[state=active]:scale-105 h-auto py-2 sm:py-3 px-2 sm:px-4",children:a.jsxs("span",{className:"flex flex-col sm:flex-row items-center gap-1 sm:gap-2",children:[a.jsx("span",{className:"text-lg sm:text-base",children:"📋"}),a.jsx("span",{className:"text-xs sm:text-sm font-medium",children:"Meus Feedbacks"})]})})]}),a.jsx(Yd,{value:"new",className:"mt-4 sm:mt-8",children:a.jsx(Wd,{})}),a.jsx(Yd,{value:"history",className:"mt-4 sm:mt-8",children:a.jsx(ky,{})})]})]}):a.jsx("div",{className:"p-6 text-center",children:a.jsx("p",{children:"Faça login para enviar um feedback."})})}const Ay=({user:e,profile:t,isAdmin:r,onFeedbackClick:s,onLogout:n})=>{const i=U(),[o,l]=T.useState(!1),[c,d]=T.useState(!1),{showNotification:u}=$a(),m=e=>`https://www.gravatar.com/avatar/${Id(e.toLowerCase().trim())}?d=mp`;return a.jsxs("div",{className:"flex items-center gap-2 shrink-0",children:[r&&a.jsxs(Es,{variant:"outline",className:"flex items-center gap-2 bg-gradient-to-r from-primary/10 to-transparent hover:from-primary/20",onClick:()=>i("/admin/dashboard"),children:[a.jsx(ar,{className:"h-4 w-4"}),a.jsx("span",{className:"hidden sm:inline",children:"Painel Admin"}),a.jsx(Zc,{variant:"secondary",className:"ml-2 bg-primary/20",children:"Admin"})]}),e&&a.jsxs(gd,{children:[a.jsx(xd,{className:"focus:outline-none",children:a.jsxs("div",{className:"flex items-center gap-2 p-1.5 rounded-full bg-gradient-to-r from-primary/10 to-transparent hover:from-primary/20 transition-colors",children:[a.jsxs(hd,{children:[a.jsx(pd,{src:t?.avatar_url||m(e.email||""),alt:t?.full_name||"User"}),a.jsx(fd,{children:(t?.full_name||"U").charAt(0)})]}),a.jsx("span",{className:"hidden sm:block text-sm font-medium",children:t?.full_name})]})}),a.jsxs(yd,{align:"end",className:os("w-64 p-2 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl","border border-gray-200/50 dark:border-slate-700/50 shadow-xl","rounded-xl animate-in fade-in-0 zoom-in-95 duration-200"),children:[a.jsx("div",{className:"px-3 py-2 mb-2",children:a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsxs(hd,{className:"h-8 w-8 ring-2 ring-primary/20",children:[a.jsx(pd,{src:t?.avatar_url||m(e.email||""),alt:t?.full_name||"User"}),a.jsx(fd,{className:"bg-primary/10 text-primary font-semibold",children:(t?.full_name||"U").charAt(0)})]}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 truncate",children:t?.full_name||"Usuário"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:e.email})]})]})}),a.jsx(wd,{className:"bg-gray-200/50 dark:bg-slate-700/50"}),a.jsxs("div",{className:"space-y-1 py-1",children:[a.jsxs(vd,{onClick:()=>i("/settings"),className:os("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white","hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200","focus:bg-gray-100/80 dark:focus:bg-slate-800/80"),children:[a.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30",children:a.jsx(sr,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium",children:"Configurações"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Gerencie sua conta"})]})]}),a.jsxs(vd,{onClick:()=>{d(!0)},className:os("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white","hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200","focus:bg-gray-100/80 dark:focus:bg-slate-800/80"),children:[a.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30",children:a.jsx(Ut,{className:"h-4 w-4 text-green-600 dark:text-green-400"})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium",children:"Suporte"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Fale conosco via WhatsApp"})]})]}),a.jsxs(vd,{onClick:e=>{e.preventDefault(),e.stopPropagation(),l(!0),s()},className:os("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white","hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200","focus:bg-gray-100/80 dark:focus:bg-slate-800/80"),children:[a.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30",children:a.jsx(Jt,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium",children:"Feedback"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Envie sugestões"})]})]})]}),a.jsx(wd,{className:"bg-gray-200/50 dark:bg-slate-700/50 my-2"}),a.jsxs(vd,{onClick:async()=>{n(),u({title:"Logout realizado com sucesso",description:"Você foi desconectado da sua conta.",type:"success",buttonText:"Continuar",onButtonClick:()=>i("/")})},className:os("flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer","text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300","hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200","focus:bg-red-50 dark:focus:bg-red-900/20"),children:[a.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 dark:bg-red-900/30",children:a.jsx(Yt,{className:"h-4 w-4 text-red-600 dark:text-red-400"})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium",children:"Sair"}),a.jsx("p",{className:"text-xs text-red-500/70 dark:text-red-400/70",children:"Desconectar da conta"})]})]})]})]}),a.jsx(cs,{open:c,onOpenChange:d,children:a.jsxs(ps,{className:"sm:max-w-md",children:[a.jsxs(fs,{className:"text-center",children:[a.jsx("div",{className:"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4",children:a.jsx(Xt,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),a.jsx(xs,{className:"text-xl font-semibold",children:"Precisa de Ajuda?"}),a.jsx(ys,{className:"text-gray-600 dark:text-gray-400",children:"Entre em contato conosco via WhatsApp para suporte rápido e personalizado."})]}),a.jsxs("div",{className:"space-y-4 py-4",children:[a.jsxs("div",{className:"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-4",children:[a.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[a.jsx(er,{className:"h-4 w-4 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Número de Suporte"})]}),a.jsx("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"(64) 99319-8433"})]}),a.jsx("div",{className:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800",children:a.jsxs("div",{className:"flex items-start gap-3",children:[a.jsx(Xt,{className:"h-5 w-5 text-green-600 dark:text-green-400 mt-0.5"}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-1",children:"Horário de Atendimento"}),a.jsxs("p",{className:"text-sm text-green-700 dark:text-green-400",children:["Segunda a Sexta: 8h às 18h",a.jsx("br",{}),"Sábado: 8h às 12h"]})]})]})})]}),a.jsxs("div",{className:"flex gap-3",children:[a.jsx(Es,{variant:"outline",onClick:()=>d(!1),className:"flex-1",children:"Cancelar"}),a.jsxs(Es,{onClick:()=>{const e=`https://wa.me/5564993198433?text=${encodeURIComponent("Olá! Preciso de suporte com o PedBook.")}`;window.open(e,"_blank"),d(!1)},className:"flex-1 bg-green-600 hover:bg-green-700 text-white",children:[a.jsx(Xt,{className:"h-4 w-4 mr-2"}),"Abrir WhatsApp"]})]})]})}),e&&a.jsx(cs,{open:o,onOpenChange:e=>{l(e),e||(document.body.style.pointerEvents="auto")},children:a.jsxs(ps,{className:"max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800",children:[a.jsx(fs,{children:a.jsx(xs,{children:"Feedback"})}),a.jsx(Ey,{})]})})]})},Cy=({hideSearch:e})=>{const t=U(),r=$(),{user:s,profile:n,isAdmin:i,signOut:o}=La(),{showNotification:l}=$a(),c="/"===r.pathname;return a.jsx("header",{className:"hidden sm:block w-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-sm z-50",children:a.jsx("div",{className:"container mx-auto px-4 py-2",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex items-center w-[180px]",children:a.jsx(_i,{})}),a.jsx("div",{className:"flex-1 flex justify-center",children:c?a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx(Xc,{hideSearch:e,isIndexPage:c}),a.jsx("button",{onClick:()=>t("/estudos"),className:"hidden lg:flex items-center gap-2 px-3 py-1.5 rounded-full bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border border-blue-200/50 dark:border-blue-800/30 backdrop-blur-sm hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40 hover:border-blue-300/60 dark:hover:border-blue-700/40 hover:scale-105 transition-all duration-200 cursor-pointer group",children:a.jsxs("div",{className:"flex items-center gap-1.5",children:[a.jsx("div",{className:"w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse group-hover:animate-none"}),a.jsx("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-800 dark:group-hover:text-gray-200",children:"Estudando?"}),a.jsx("span",{className:"text-xs font-semibold text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300",children:"Experimente nossa nova plataforma GRATUITAMENTE!"})]})})]}):a.jsx(Xc,{hideSearch:e,isIndexPage:c})}),a.jsx("div",{className:"flex items-center gap-2 w-[180px] justify-end",children:s?a.jsx(Ay,{user:s,profile:n,isAdmin:i,onFeedbackClick:()=>{},onLogout:async()=>{await o(),l({title:"Logout realizado com sucesso",description:"Você foi desconectado da sua conta.",type:"success",buttonText:"Continuar"})}}):a.jsx(Ai,{})})]})})})},_y=({title:e,description:t,icon:r,color:s,path:n,imageUrl:i,badge:o,onClick:l,hideDescription:c,comingSoon:d,requiresAuth:u,showNewBadge:m,customBadgeText:h,customBadgeColor:p,showFeatureBadges:f,isSpecial:g,specialGlow:x,longTitle:y})=>{const v=U(),b=z.useUser(),[w,j]=T.useState(!1);s.split("-")[1],s.includes("amber")||s.includes("emerald")||s.includes("purple")||s.includes("blue")||s.includes("red")||s.includes("cyan")||s.includes("green")||s.includes("yellow")||s.includes("indigo")||s.includes("rose")||s.includes("orange")||s.includes("pink")||s.includes("violet")||s.includes("teal")||s.includes("sky")||s.includes("fuchsia")||s.includes("lime");const k=s.includes("amber")?"bg-amber-50":s.includes("emerald")?"bg-emerald-50":s.includes("purple")?"bg-purple-50":s.includes("blue")?"bg-blue-50":s.includes("red")?"bg-red-50":s.includes("cyan")?"bg-cyan-50":s.includes("green")?"bg-green-50":s.includes("yellow")?"bg-yellow-50":s.includes("indigo")?"bg-indigo-50":s.includes("rose")?"bg-rose-50":s.includes("orange")?"bg-orange-50":s.includes("pink")?"bg-pink-50":s.includes("violet")?"bg-violet-50":s.includes("teal")?"bg-teal-50":s.includes("sky")?"bg-sky-50":s.includes("fuchsia")?"bg-fuchsia-50":s.includes("lime")?"bg-lime-50":"bg-white",E=s.includes("amber")?"dark:bg-amber-900/30":s.includes("emerald")?"dark:bg-emerald-900/30":s.includes("purple")?"dark:bg-purple-900/30":s.includes("blue")?"dark:bg-blue-900/30":s.includes("red")?"dark:bg-red-900/30":s.includes("cyan")?"dark:bg-cyan-900/30":s.includes("green")?"dark:bg-green-900/30":s.includes("yellow")?"dark:bg-yellow-900/30":s.includes("indigo")?"dark:bg-indigo-900/30":s.includes("rose")?"dark:bg-rose-900/30":s.includes("orange")?"dark:bg-orange-900/30":s.includes("pink")?"dark:bg-pink-900/30":s.includes("violet")?"dark:bg-violet-900/30":s.includes("teal")?"dark:bg-teal-900/30":s.includes("sky")?"dark:bg-sky-900/30":s.includes("fuchsia")?"dark:bg-fuchsia-900/30":s.includes("lime")?"dark:bg-lime-900/30":"dark:bg-slate-700/50",A=s.includes("amber")?"text-amber-700":s.includes("emerald")?"text-emerald-700":s.includes("purple")?"text-purple-700":s.includes("blue")?"text-blue-700":s.includes("red")?"text-red-700":s.includes("cyan")?"text-cyan-700":s.includes("green")?"text-green-700":s.includes("yellow")?"text-yellow-700":s.includes("indigo")?"text-indigo-700":s.includes("rose")?"text-rose-700":s.includes("orange")?"text-orange-700":s.includes("pink")?"text-pink-700":s.includes("violet")?"text-violet-700":s.includes("teal")?"text-teal-700":s.includes("sky")?"text-sky-700":s.includes("fuchsia")?"text-fuchsia-700":s.includes("lime")?"text-lime-700":"text-gray-700",C=s.includes("amber")?"dark:text-amber-300":s.includes("emerald")?"dark:text-emerald-300":s.includes("purple")?"dark:text-purple-300":s.includes("blue")?"dark:text-blue-300":s.includes("red")?"dark:text-red-300":s.includes("cyan")?"dark:text-cyan-300":s.includes("green")?"dark:text-green-300":s.includes("yellow")?"dark:text-yellow-300":s.includes("indigo")?"dark:text-indigo-300":s.includes("rose")?"dark:text-rose-300":s.includes("orange")?"dark:text-orange-300":s.includes("pink")?"dark:text-pink-300":s.includes("violet")?"dark:text-violet-300":s.includes("teal")?"dark:text-teal-300":s.includes("sky")?"dark:text-sky-300":s.includes("fuchsia")?"dark:text-fuchsia-300":s.includes("lime")?"dark:text-lime-300":"dark:text-gray-300";return a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:os("relative h-full p-4 sm:p-5 rounded-xl cursor-pointer","bg-white/90 dark:bg-slate-800/90 shadow-md","border border-gray-100 dark:border-gray-700/50","transition-all duration-300 hover:shadow-lg hover:-translate-y-1",g&&"ring-2 ring-pink-300/50 dark:ring-pink-500/30",x&&"shadow-pink-200/50 dark:shadow-pink-500/20 shadow-xl",d&&"opacity-75 cursor-not-allowed"),onClick:()=>{!u||b?d||(l?l():v(n)):j(!0)},style:x?{background:s.includes("amber")||s.includes("yellow")?"linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 50%, rgba(217, 119, 6, 0.1) 100%)":s.includes("emerald")||s.includes("green")?"linear-gradient(135deg, rgba(52, 211, 153, 0.1) 0%, rgba(16, 185, 129, 0.05) 50%, rgba(5, 150, 105, 0.1) 100%)":s.includes("purple")||s.includes("violet")?"linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(124, 58, 237, 0.1) 100%)":s.includes("orange")?"linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(249, 115, 22, 0.05) 50%, rgba(234, 88, 12, 0.1) 100%)":s.includes("indigo")||s.includes("blue")?"linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, rgba(37, 99, 235, 0.1) 100%)":s.includes("red")||s.includes("rose")?"linear-gradient(135deg, rgba(248, 113, 113, 0.1) 0%, rgba(239, 68, 68, 0.05) 50%, rgba(220, 38, 38, 0.1) 100%)":s.includes("cyan")||s.includes("teal")?"linear-gradient(135deg, rgba(34, 211, 238, 0.1) 0%, rgba(6, 182, 212, 0.05) 50%, rgba(8, 145, 178, 0.1) 100%)":s.includes("pink")?"linear-gradient(135deg, rgba(251, 207, 232, 0.1) 0%, rgba(244, 114, 182, 0.05) 50%, rgba(236, 72, 153, 0.1) 100%)":"linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.05) 50%, rgba(29, 78, 216, 0.1) 100%)",boxShadow:g?s.includes("amber")||s.includes("yellow")?"0 0 30px rgba(217, 119, 6, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("emerald")||s.includes("green")?"0 0 30px rgba(5, 150, 105, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("purple")||s.includes("violet")?"0 0 30px rgba(124, 58, 237, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("orange")?"0 0 30px rgba(234, 88, 12, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("indigo")||s.includes("blue")?"0 0 30px rgba(37, 99, 235, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("red")||s.includes("rose")?"0 0 30px rgba(220, 38, 38, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("cyan")||s.includes("teal")?"0 0 30px rgba(8, 145, 178, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":s.includes("pink")?"0 0 30px rgba(236, 72, 153, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":"0 0 30px rgba(29, 78, 216, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)":void 0}:void 0,children:[a.jsx("div",{className:os("absolute top-0 left-0 right-0 rounded-t-xl",g?"h-2":"h-1.5",g?s.includes("amber")||s.includes("yellow")?"bg-gradient-to-r from-amber-400 via-yellow-400 to-amber-500":s.includes("emerald")||s.includes("green")?"bg-gradient-to-r from-emerald-400 via-green-400 to-emerald-500":s.includes("purple")||s.includes("violet")?"bg-gradient-to-r from-purple-400 via-violet-400 to-purple-500":s.includes("orange")?"bg-gradient-to-r from-orange-400 via-amber-400 to-orange-500":s.includes("indigo")||s.includes("blue")?"bg-gradient-to-r from-indigo-400 via-blue-400 to-indigo-500":s.includes("red")||s.includes("rose")?"bg-gradient-to-r from-red-400 via-rose-400 to-red-500":s.includes("cyan")||s.includes("teal")?"bg-gradient-to-r from-cyan-400 via-teal-400 to-cyan-500":s.includes("pink")?"bg-gradient-to-r from-pink-400 via-rose-400 to-pink-500":"bg-gradient-to-r from-primary via-blue-500 to-primary":s.includes("amber")?"bg-amber-500":s.includes("emerald")?"bg-emerald-500":s.includes("purple")?"bg-purple-500":s.includes("blue")?"bg-blue-500":s.includes("red")?"bg-red-500":s.includes("cyan")?"bg-cyan-500":s.includes("green")?"bg-green-500":s.includes("yellow")?"bg-yellow-500":s.includes("indigo")?"bg-indigo-500":s.includes("rose")?"bg-rose-500":"bg-primary",g&&(s.includes("amber")||s.includes("yellow")?"shadow-lg shadow-amber-200/50":s.includes("emerald")||s.includes("green")?"shadow-lg shadow-emerald-200/50":s.includes("purple")||s.includes("violet")?"shadow-lg shadow-purple-200/50":s.includes("orange")?"shadow-lg shadow-orange-200/50":s.includes("indigo")||s.includes("blue")?"shadow-lg shadow-indigo-200/50":s.includes("red")||s.includes("rose")?"shadow-lg shadow-red-200/50":s.includes("cyan")||s.includes("teal")?"shadow-lg shadow-cyan-200/50":s.includes("pink")?"shadow-lg shadow-pink-200/50":"shadow-lg shadow-blue-200/50"))}),m&&a.jsx("div",{className:"absolute -top-2 -right-2 z-10",children:a.jsxs("div",{className:os("px-2 py-0.5 text-white text-xs font-medium rounded-md shadow-md flex items-center gap-1",g?s.includes("amber")||s.includes("yellow")?"bg-gradient-to-r from-amber-500 to-yellow-500 shadow-amber-300/50":s.includes("emerald")||s.includes("green")?"bg-gradient-to-r from-emerald-500 to-green-500 shadow-emerald-300/50":s.includes("purple")||s.includes("violet")?"bg-gradient-to-r from-purple-500 to-violet-500 shadow-purple-300/50":s.includes("orange")?"bg-gradient-to-r from-orange-500 to-amber-500 shadow-orange-300/50":s.includes("indigo")||s.includes("blue")?"bg-gradient-to-r from-indigo-500 to-blue-500 shadow-indigo-300/50":s.includes("red")||s.includes("rose")?"bg-gradient-to-r from-red-500 to-rose-500 shadow-red-300/50":s.includes("cyan")||s.includes("teal")?"bg-gradient-to-r from-cyan-500 to-teal-500 shadow-cyan-300/50":s.includes("pink")?"bg-gradient-to-r from-pink-500 to-rose-500 shadow-pink-300/50":"bg-gradient-to-r from-blue-500 to-indigo-500 shadow-blue-300/50":p?`bg-${p}-500`:"bg-red-500"),children:[g&&a.jsx(ir,{className:"w-3 h-3"}),h||"Novo"]})}),a.jsxs("div",{className:"flex flex-col items-center text-center h-full justify-start pt-2 space-y-2",children:[i?a.jsx("div",{className:"w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden shadow-md",children:a.jsx("img",{src:i,alt:e,className:"w-full h-full object-cover"})}):r&&a.jsx("div",{className:os("w-14 h-14 sm:w-16 sm:h-16 rounded-lg flex items-center justify-center shadow-sm",k,E),children:a.jsx(r,{className:os("w-7 h-7 sm:w-8 sm:h-8",A,C)})}),a.jsx("h3",{className:os("font-bold text-gray-800 dark:text-gray-200",y?"line-clamp-3 leading-tight":"line-clamp-2",c?"text-lg sm:text-xl":"text-sm sm:text-base md:text-lg"),children:e}),!c&&a.jsx("p",{className:"hidden sm:block text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1.5 min-h-[32px]",children:t||" "}),a.jsx("div",{className:"flex-1"}),f&&a.jsx("div",{className:"hidden sm:flex flex-wrap gap-1 justify-center",children:a.jsxs("div",{className:os("flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80","text-[10px] px-1.5 py-0.5 rounded-md",s.includes("orange")?"text-orange-700 dark:text-orange-400":s.includes("amber")?"text-amber-700 dark:text-amber-400":"text-primary dark:text-blue-400"),children:[a.jsx(ir,{className:"w-2.5 h-2.5"}),a.jsx("span",{children:"Todas especialidades"})]})}),(o||d)&&a.jsxs("div",{className:"hidden sm:flex gap-1.5 flex-wrap justify-center",children:[o&&a.jsx(Zc,{variant:"outline",className:"bg-gray-100/80 dark:bg-slate-700/80 border-none text-gray-700 dark:text-gray-300 text-[10px] font-medium px-2 py-0.5 h-auto",children:o}),d&&a.jsx(Zc,{variant:"outline",className:"bg-yellow-100/80 dark:bg-yellow-900/30 border-none text-yellow-700 dark:text-yellow-400 text-[10px] font-medium px-2 py-0.5 h-auto",children:"Em breve"})]})]})]}),u&&a.jsx(Ai,{open:w,onOpenChange:j,hidden:!0})]})},Ny=({onClick:e})=>{const t=U(),r=z.useSession(),[s,n]=D.useState(!1);return a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute -top-2 -right-2 z-10",children:a.jsxs("div",{className:"px-2 py-0.5 text-white text-xs font-medium rounded-md shadow-md flex items-center gap-1 bg-gradient-to-r from-indigo-500 to-blue-500 shadow-indigo-300/50",children:[a.jsx(ir,{className:"w-3 h-3"}),"Pergunte"]})}),a.jsxs("div",{className:os("relative h-full p-4 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer overflow-hidden group","bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg hover:-translate-y-1","border border-gray-100 dark:border-gray-700/50","ring-2 ring-indigo-300/50 dark:ring-indigo-500/30","shadow-indigo-200/50 dark:shadow-indigo-500/20 shadow-xl","hover:shadow-indigo-300/60 dark:hover:shadow-indigo-400/30 hover:shadow-2xl"),onClick:()=>{r?e?e():t("/dr-will"):n(!0)},style:{background:"linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, rgba(37, 99, 235, 0.1) 100%)",boxShadow:"0 0 30px rgba(37, 99, 235, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)"},children:[a.jsx("div",{className:"absolute top-0 left-0 right-0 h-2 rounded-t-xl bg-gradient-to-r from-indigo-400 via-blue-400 to-indigo-500 shadow-lg shadow-indigo-200/50"}),a.jsxs("div",{className:"flex flex-col items-center text-center h-full relative z-10 justify-between pt-2",children:[a.jsx("div",{className:"relative",children:a.jsxs("div",{className:"w-14 h-14 sm:w-16 sm:h-16 rounded-lg flex items-center justify-center mb-3\r\n                            bg-indigo-50 dark:bg-indigo-900/30 shadow-sm overflow-hidden relative",children:[a.jsx(Tt,{className:"w-7 h-7 sm:w-8 sm:h-8 text-indigo-600 dark:text-indigo-400"}),a.jsx("div",{className:"absolute -top-0.5 -right-0.5 text-yellow-400",children:a.jsx(ir,{className:"w-3 h-3"})})]})}),a.jsx("h3",{className:"font-bold text-gray-800 dark:text-gray-200 line-clamp-2 text-base sm:text-lg",children:"Dr. Will"}),a.jsx("p",{className:"hidden sm:block text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1.5 min-h-[32px]",children:"IA médica avançada com conhecimento em todas as especialidades"}),a.jsxs("div",{className:"mt-2 flex flex-wrap gap-1.5 justify-center",children:[a.jsxs("div",{className:"flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80 text-indigo-700 dark:text-indigo-400\r\n                            text-[10px] px-1.5 py-0.5 rounded-md",children:[a.jsx(ir,{className:"w-2.5 h-2.5"}),a.jsx("span",{className:"hidden sm:inline",children:"Especialidades"})]}),a.jsxs("div",{className:"flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80 text-indigo-700 dark:text-indigo-400\r\n                            text-[10px] px-1.5 py-0.5 rounded-md",children:[a.jsx(qt,{className:"w-2.5 h-2.5"}),a.jsx("span",{className:"hidden sm:inline",children:"Imagens"})]}),a.jsxs("div",{className:"flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80 text-indigo-700 dark:text-indigo-400\r\n                            text-[10px] px-1.5 py-0.5 rounded-md",children:[a.jsx(Bt,{className:"w-2.5 h-2.5"}),a.jsx("span",{className:"hidden sm:inline",children:"Fluxogramas"})]}),a.jsxs("div",{className:"flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80 text-indigo-700 dark:text-indigo-400\r\n                            text-[10px] px-1.5 py-0.5 rounded-md",children:[a.jsx(cr,{className:"w-2.5 h-2.5"}),a.jsx("span",{className:"hidden sm:inline",children:"Tabelas"})]})]}),a.jsx("div",{className:"mt-2 hidden sm:flex gap-1.5 flex-wrap justify-center",children:a.jsx("div",{className:"inline-flex items-center rounded-md bg-gray-100/80 dark:bg-slate-700/80 border-none\r\n                            px-2 py-0.5 text-[10px] text-gray-700 dark:text-gray-300 font-medium h-auto",children:"Atualizado"})})]})]})]}),a.jsx(Ai,{open:s,onOpenChange:n,hidden:!0})]})},Sy=[{title:"Cálculo de Medicamentos",description:"Dosagens automáticas de forma fácil e rápida.",icon:tr,color:"bg-gradient-to-br from-amber-100 via-amber-200 to-yellow-200 border-amber-300",path:"/medicamentos/painel",requiresAuth:!1,badge:"Automático",isSpecial:!0,specialGlow:!0},{title:"Condutas e Manejos",description:"Protocolos e condutas para prática clínica.",icon:zt,color:"bg-gradient-to-br from-emerald-100 via-emerald-200 to-green-200 border-emerald-300",path:"/condutas-e-manejos",requiresAuth:!1,badge:"Novo",showNewBadge:!1,isSpecial:!0,specialGlow:!0},{title:"Puericultura",description:"Crescimento, vacinas, DNPM e fórmulas infantis.",icon:Nt,color:"bg-gradient-to-br from-purple-100 via-purple-200 to-violet-200 border-purple-300",path:"/puericultura",requiresAuth:!1,badge:"Completo e Automático",isSpecial:!0,specialGlow:!0},{title:"Bulas Profissionais",description:"Bulas completas de medicamentos para profissionais.",icon:zt,color:"bg-gradient-to-br from-orange-100 via-orange-200 to-amber-200 border-orange-300",path:"/bulas-profissionais",requiresAuth:!1,badge:"Completo",showNewBadge:!1,showFeatureBadges:!0,isSpecial:!0,specialGlow:!0},{title:"Banco de Questões Comentadas",description:"Experiemente agora diretamente em nosso site uma prévia de pediatria GRATUITAMENTE!",icon:$t,color:"bg-gradient-to-br from-violet-100 via-violet-200 to-purple-200 border-violet-300",path:"/estudos",requiresAuth:!1,badge:"Prévia Gratuita",showNewBadge:!0,customBadgeText:"MedEvo",customBadgeColor:"purple",isSpecial:!0,specialGlow:!0,longTitle:!0},{title:"Assistente IA Dr. Will 2.0",description:"IA médica avançada com conhecimento em todas as especialidades.",icon:Tt,color:"bg-gradient-to-br from-indigo-100 via-indigo-200 to-blue-200 border-indigo-300",path:"/dr-will",requiresAuth:!0,badge:"Novo",showNewBadge:!0,customBadgeText:"2.0",customBadgeColor:"green",isSpecial:!0,specialGlow:!0},{title:"Medicamentos na Amamentação",description:"Segurança de medicamentos durante a amamentação.",icon:Nt,color:"bg-gradient-to-br from-pink-100 via-pink-200 to-rose-200 border-pink-300",path:"/medicamentos-amamentacao",requiresAuth:!1,isSpecial:!0,specialGlow:!0,longTitle:!0},{title:"Interações medicamentosas",description:"Verifique interações entre medicamentos e evite riscos.",icon:dr,color:"bg-gradient-to-br from-amber-100 via-amber-200 to-yellow-200 border-amber-300",path:"/interacoes-medicamentosas",requiresAuth:!1,badge:"Automático",showNewBadge:!1,showFeatureBadges:!0,isSpecial:!0,specialGlow:!0},{title:"Prescrições",description:"Crie e gerencie prescrições personalizadas.",icon:St,color:"bg-gradient-to-br from-red-100 via-red-200 to-rose-200 border-red-300",path:"/prescriptions",requiresAuth:!0,badge:"Crie suas prescrições",isSpecial:!0,specialGlow:!0},{title:"Calculadoras e Escalas",description:"Calculadoras pediátricas de escores para uso diário.",icon:Pt,color:"bg-gradient-to-br from-blue-100 via-blue-200 to-sky-200 border-blue-300",path:"/calculadoras",requiresAuth:!1,badge:"Automático",isSpecial:!0,specialGlow:!0},{title:"Fluxogramas",description:"Manejo de urgências e emergências pediátricas.",icon:Ct,color:"bg-gradient-to-br from-cyan-100 via-cyan-200 to-teal-200 border-cyan-300",path:"/flowcharts",requiresAuth:!1,badge:"Automático",isSpecial:!0,specialGlow:!0},{title:"Intoxicações",description:"Toxíndromes, antídotos e doses calculadas.",icon:nr,color:"bg-gradient-to-br from-rose-100 via-rose-200 to-pink-200 border-rose-300",path:"/poisonings",requiresAuth:!1,isSpecial:!0,specialGlow:!0},{title:"Anotações",description:"Organize suas anotações de forma prática e rápida, com acesso fácil de qualquer lugar.",icon:or,color:"bg-gradient-to-br from-yellow-100 via-yellow-200 to-amber-200 border-yellow-300",path:"/notes",requiresAuth:!0,isSpecial:!0,specialGlow:!0},{title:"CID-10",description:"Consulta rápida de códigos CID.",icon:St,color:"bg-gradient-to-br from-green-100 via-green-200 to-emerald-200 border-green-300",path:"/icd",requiresAuth:!1,isSpecial:!0,specialGlow:!0},{title:"Bot WhatsApp",description:"Acesse as dosagens pediátricas 24 horas por dia através do nosso bot.",icon:Jt,color:"bg-gradient-to-br from-green-100 via-green-200 to-emerald-200 border-green-300",path:"/whatsapp-bot",requiresAuth:!1,badge:"24/7",isSpecial:!0,specialGlow:!0}],Ty=()=>{L();const e=U();Zl();const[t,r]=T.useState(!1),[s,n]=T.useState(null),i=async(t,s)=>{if(s){const{data:{session:e}}=await Ts.auth.getSession();if(!e)return n(t),void r(!0)}e(t)};return a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-4 max-w-6xl mx-auto",children:Sy.map(((e,t)=>e.title.includes("Dr. Will")?a.jsx("div",{className:"relative opacity-100",children:a.jsx(Ny,{onClick:()=>i(e.path,!0)})},t):a.jsx("div",{className:"relative opacity-100",children:a.jsx(_y,{...e,onClick:()=>i(e.path,e.requiresAuth||!1)})},t)))}),t&&a.jsx(Ai,{defaultOpen:!0,hidden:!0,onOpenChange:e=>{r(e),e||n(null)}})]})},Py=T.memo((({searchPlaceholder:e})=>a.jsx("div",{className:"w-full max-w-6xl mx-auto relative z-10 flex items-center gap-2 px-2",children:a.jsx("div",{className:"flex-1 relative group",children:a.jsx(Qc,{customPlaceholder:e||"Buscar medicamentos, condutas, calculadoras..."})})}))),Dy=e=>a.jsx(ka,{...e}),Ry=({categories:e=[]})=>{const t="Calculadora pediátrica completa para profissionais da saúde. Calcule doses de medicamentos pediátricos, acesse prescrições digitais e fluxogramas clínicos com precisão e segurança baseada em evidências científicas.",r="PedBook - Calculadora de Doses Pediátricas | Medicamentos e Prescrições",s={"@context":"https://schema.org","@type":"WebApplication",name:"PedBook",description:t,url:"https://pedb.com.br",applicationCategory:"MedicalApplication",operatingSystem:"Web",author:{"@type":"Organization",name:"MedUnity",url:"https://medunity.com.br"},audience:{"@type":"MedicalAudience",audienceType:"Pediatras e profissionais da saúde"},featureList:["Cálculo automático de doses pediátricas","Prescrições médicas digitais","Fluxogramas clínicos interativos","Curvas de crescimento infantil","Calendário vacinal completo","DNPM (Desenvolvimento Neuropsicomotor)","Medicamentos para amamentação","Calculadoras médicas especializadas"],applicationSubCategory:"Calculadora Médica",downloadUrl:"https://pedb.com.br",screenshot:"https://pedb.com.br/faviconx.webp",softwareVersion:"2.0",dateModified:(new Date).toISOString(),inLanguage:"pt-BR",isAccessibleForFree:!0,usageInfo:"Gratuito para profissionais da saúde",specialty:"Pediatria"};return a.jsxs(Dy,{children:[a.jsx("title",{children:r}),a.jsx("meta",{name:"description",content:t}),a.jsx("meta",{name:"keywords",content:["calculadora pediátrica","doses pediátricas","medicamentos pediátricos","prescrição pediátrica","pediatria","cálculo de dose","posologia pediátrica","farmacologia pediátrica","medicina pediátrica","dosagem infantil","bulas profissionais","condutas pediátricas","fluxogramas clínicos","calculadoras médicas","puericultura","intoxicações pediátricas",...e.slice(0,8).map((e=>`${e.toLowerCase()} pediátrico`))].join(", ")}),a.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large, max-snippet:-1"}),a.jsx("meta",{name:"googlebot",content:"index, follow"}),a.jsx("meta",{name:"bingbot",content:"index, follow"}),a.jsx("meta",{name:"geo.region",content:"BR"}),a.jsx("meta",{name:"geo.country",content:"Brazil"}),a.jsx("meta",{name:"language",content:"Portuguese"}),a.jsx("meta",{property:"og:title",content:r}),a.jsx("meta",{property:"og:description",content:t}),a.jsx("meta",{property:"og:type",content:"website"}),a.jsx("meta",{property:"og:url",content:"https://pedb.com.br"}),a.jsx("meta",{property:"og:image",content:"https://pedb.com.br/faviconx.webp"}),a.jsx("meta",{property:"og:image:alt",content:"PedBook - Calculadora Pediátrica"}),a.jsx("meta",{property:"og:site_name",content:"PedBook"}),a.jsx("meta",{property:"og:locale",content:"pt_BR"}),a.jsx("meta",{property:"og:updated_time",content:(new Date).toISOString()}),a.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),a.jsx("meta",{name:"twitter:title",content:r}),a.jsx("meta",{name:"twitter:description",content:t}),a.jsx("meta",{name:"twitter:image",content:"https://pedb.com.br/faviconx.webp"}),a.jsx("meta",{name:"twitter:site",content:"@pedbook"}),a.jsx("link",{rel:"canonical",href:"https://pedb.com.br"}),a.jsx("script",{type:"application/ld+json",children:JSON.stringify(s)}),a.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"Como calcular doses pediátricas?",acceptedAnswer:{"@type":"Answer",text:"O PedBook oferece calculadora automática para medicamentos pediátricos. Basta inserir peso e idade da criança para obter a dose correta baseada em protocolos médicos validados."}},{"@type":"Question",name:"Quais medicamentos estão disponíveis?",acceptedAnswer:{"@type":"Answer",text:"O PedBook inclui medicamentos essenciais da pediatria organizados por categorias como antibióticos, anti-inflamatórios, broncodilatadores e outros, todos com cálculos automáticos de dose."}},{"@type":"Question",name:"O PedBook é gratuito?",acceptedAnswer:{"@type":"Answer",text:"Sim, o PedBook é completamente gratuito para profissionais da saúde. Oferecemos acesso livre a todas as funcionalidades de cálculo de doses e prescrições."}},{"@type":"Question",name:"O PedBook é confiável para uso clínico?",acceptedAnswer:{"@type":"Answer",text:"Sim, todas as fórmulas e dosagens são baseadas em protocolos médicos reconhecidos e literatura científica atualizada. Sempre consulte as diretrizes locais e use seu julgamento clínico."}}]})}),a.jsx("link",{rel:"preconnect",href:"https://bxedpdmgvgatjdfxgxij.supabase.co"}),a.jsx("link",{rel:"dns-prefetch",href:"https://bxedpdmgvgatjdfxgxij.supabase.co"})]})},My=T.lazy((()=>B((()=>import("./Footer-CliqTtAT.js")),__vite__mapDeps([20,1,2,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12])))),Iy=T.lazy((()=>B((()=>import("./UnifiedContentBadge-DLSfq1Of.js")),__vite__mapDeps([27,1,2,3,28,5,29,30,26,31,32,33,4,6,7,8,9,10,11,12])).then((e=>({default:e.UnifiedContentBadge}))))),Ly=T.lazy((()=>B((()=>import("./SiteFeedbackDialog-DMGhaCys.js")),__vite__mapDeps([34,1,2,24,3,4,5,6,7,8,9,10,11,12])).then((e=>({default:e.SiteFeedbackDialog}))))),Oy=T.lazy((()=>B((()=>import("./AndroidInstallDialog-MwErltrM.js")),__vite__mapDeps([35,1,2,36,3,4,5,6,7,8,9,10,11,12])).then((e=>({default:e.AndroidInstallDialog}))))),Fy=T.lazy((()=>B((()=>import("./MobileThemeToggle-GvGSjv_g.js")),__vite__mapDeps([37,1,2,3,4,5,6,7,8,9,10,11,12])))),Vy=()=>{const[e,t]=T.useState(""),[r,s]=T.useState(!1),n=["Buscar por Amoxicilina...","Buscar por Dipirona...","Buscar por Prednisolona...","Buscar por Salbutamol...","Buscar por Asma.."],{data:i,isLoading:o}=I({queryKey:["homepage-seo-data"],queryFn:async()=>{const{data:e,error:t}=await Ts.from("pedbook_medication_categories").select("name").order("name");if(t)throw t;return{categories:e?.map((e=>e.name))||[]}},staleTime:36e5,gcTime:864e5});return T.useEffect((()=>{const e=()=>{s(window.innerWidth<640)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),T.useEffect((()=>{const e=setTimeout((()=>{let e,r=0,s=0,a=!1;const i=()=>{const o=n[r];a?(t(o.substring(0,s-1)),s--):(t(o.substring(0,s+1)),s++);let l=a?30:80;a||s!==o.length?a&&0===s&&(a=!1,r=(r+1)%n.length,l=300):(l=1500,a=!0),e=setTimeout(i,l)};return i(),()=>clearTimeout(e)}),5e3);return()=>clearTimeout(e)}),[]),a.jsxs("div",{className:"min-h-screen flex flex-col relative",children:[a.jsx("div",{className:"fixed inset-0 w-full h-full pointer-events-none z-0 bg-gradient-to-b from-blue-50/80 to-white dark:from-slate-900/90 dark:to-slate-800/80"}),a.jsx("div",{className:"fixed inset-0 w-full h-full pointer-events-none z-0 opacity-5 dark:opacity-10",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231E40AF' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}),a.jsx(Ry,{categories:i?.categories}),a.jsx(Cy,{hideSearch:!0}),a.jsx("main",{className:"flex-1 container mx-auto px-4 py-4 md:py-6 relative z-20 pb-24 sm:pb-0",children:a.jsxs("div",{className:"space-y-2 animate-fade-in-up",children:[a.jsxs("div",{className:"text-center space-y-1 mb-4 hidden md:block",children:[a.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-center bg-gradient-to-r from-primary to-blue-600 text-transparent bg-clip-text","data-text":"PedBook",children:"PedBook"}),a.jsx("p",{className:"text-sm md:text-base text-gray-600 dark:text-gray-300",children:"Calculadora de Doses Pediátricas"})]}),a.jsx(Py,{searchPlaceholder:e}),a.jsx("div",{className:"py-2 animate-fade-in",style:{animationDelay:"300ms"},children:a.jsx(T.Suspense,{fallback:a.jsx("div",{className:"h-8 bg-gray-100 dark:bg-gray-800 rounded animate-pulse"}),children:a.jsx(Iy,{})})}),a.jsx(Ty,{})]})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Fy,{})}),a.jsx("div",{className:"hidden sm:block relative z-10",children:a.jsx(T.Suspense,{fallback:a.jsx("div",{className:"h-16 bg-gray-100 dark:bg-gray-800"}),children:a.jsx(My,{})})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Ly,{})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Oy,{})})]})},zy=D.lazy((()=>B((()=>import("./FloatingSupport-Oq_WGoBV.js")),__vite__mapDeps([38,1,2])).then((e=>({default:e.FloatingSupport}))))),By=D.lazy((()=>B((()=>import("./FloatingChatButton-FeuZhjTw.js")),__vite__mapDeps([39,1,2,40,4,3,41,36,5,18,42,43,44,45,46,32,33,6,7,8,9,10,11,12])))),$y=D.lazy((()=>B((()=>import("./MobileBottomNav-YFjm8vPh.js")),__vite__mapDeps([47,1,2,21,22,23,16,18,24,25,5,48,42,17,49,50,3,4,6,7,8,9,10,11,12])))),Uy=D.lazy((()=>B((()=>import("./ScrollToTop-BkeZpmBK.js")),__vite__mapDeps([51,2,5])))),qy=D.lazy((()=>B((()=>import("./BackButtonHandler-heuPxz_v.js")),__vite__mapDeps([52,2,4,1,5,3,6,7,8,9,10,11,12])))),Wy=D.lazy((()=>B((()=>import("./ChunkVersionChecker-BboIJXad.js")),__vite__mapDeps([53,2])))),Hy=D.lazy((()=>B((()=>import("./SafeAreaProvider-CuOtsltL.js")),__vite__mapDeps([54,1,2,3,4,5,6,7,8,9,10,11,12])).then((e=>({default:e.SafeAreaProvider}))))),Gy=D.lazy((()=>B((()=>import("./SafeAreaDebugger-CV2eWuH6.js")),__vite__mapDeps([55,1,2,3,4,5,6,7,8,9,10,11,12])).then((e=>({default:e.SafeAreaDebugger}))))),Ky=D.lazy((()=>B((()=>import("./GlobalDebugger-u8JiIVCZ.js")),__vite__mapDeps([56,1,2,57,3,4,5,6,7,8,9,10,11,12])).then((e=>({default:e.GlobalDebugger}))))),Yy=D.lazy((()=>B((()=>import("./Medications-DBps0_8T.js")),__vite__mapDeps([58,1,2,59,5,3,60,61,20,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,62,63,64,65,66,67,50,68,69,44,70,31,71,72,73,57,42,49])))),Qy=D.lazy((()=>B((()=>import("./ResetPassword-CV6_uFE2.js")),__vite__mapDeps([74,1,2,6,69,5,3,4,7,8,9,10,11,12])))),Xy=D.lazy((()=>B((()=>import("./ProfessionalInstructions-BUKTQtaf.js")),__vite__mapDeps([75,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,76])))),Jy=D.lazy((()=>B((()=>import("./MedicationInstructionPage-CtA2KBEr.js")),__vite__mapDeps([77,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,63,78,79,80,81,82])))),Zy=D.lazy((()=>B((()=>import("./QuestionImport-CBu5Sm3f.js")),__vite__mapDeps([83,1,2,84,69,45,3,4,5,6,7,8,9,10,11,12])))),ev=D.lazy((()=>B((()=>import("./QuestionFormatting-CLp72BiD.js")),__vite__mapDeps([85,1,2,69,86,87,88,3,4,5,6,7,8,9,10,11,12])))),tv=D.lazy((()=>B((()=>import("./MedicationImport-DL2XKwef.js")),__vite__mapDeps([89,1,2,69,90,45,91,92,3,4,5,6,7,8,9,10,11,12])))),rv=D.lazy((()=>B((()=>import("./FormatThemes-6hU7acDC.js")),__vite__mapDeps([93,1,2,69,94,12,8,9,7,5,86,3,4,6,10,11])))),sv=D.lazy((()=>B((()=>import("./Dashboard-Cj4g3K2V.js")),__vite__mapDeps([95,1,2,5,96,97,29,98,99,100,92,101,102,18,103,71,72,104,3,4,6,7,8,9,10,11,12])))),av=D.lazy((()=>B((()=>import("./DrugInteractionMedications-DXNpm_VD.js")),__vite__mapDeps([105,1,2,106,69,107,3,4,5,6,7,8,9,10,11,12])))),nv=D.lazy((()=>B((()=>import("./BreastfeedingMedications-DWqzMMD3.js")),__vite__mapDeps([108,1,2,69,3,4,5,6,7,8,9,10,11,12])))),iv=D.lazy((()=>B((()=>import("./BreastfeedingMedicationsEnhancement-CW7qorIZ.js")),__vite__mapDeps([109,1,2,69,106,3,104,86,92,42,4,5,6,7,8,9,10,11,12])))),ov=D.lazy((()=>B((()=>import("./MedicationImprovement-0WvR8e25.js")),__vite__mapDeps([110,1,2,69,3,4,5,6,7,8,9,10,11,12])))),lv=D.lazy((()=>B((()=>import("./CasDcbValidation-DSxrsKym.js")),__vite__mapDeps([111,1,2,3,69,4,5,6,7,8,9,10,11,12])))),cv=D.lazy((()=>B((()=>import("./FeedbackManagement-C7iIQVNw.js")),__vite__mapDeps([112,1,2,87,113,114,24,115,67,23,70,116,78,10,8,9,5,86,45,42,3,4,6,7,11,12])))),dv=D.lazy((()=>B((()=>import("./ActiveIngredientsFormatter-DoW-2kr-.js")),__vite__mapDeps([117,1,2,94,12,8,9,7,118,116,67,50,69,103,16,119,120,5,92,3,4,6,10,11])))),uv=D.lazy((()=>B((()=>import("./DrWillControl-Bjvco9oY.js")),__vite__mapDeps([121,1,2,122,7,8,9,69,5,103,3,4,6,10,11,12])))),mv=D.lazy((()=>B((()=>import("./Anamnese-CylWTg9c.js")),__vite__mapDeps([123,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,122,50,62,124,71,107,119,86])))),hv=D.lazy((()=>B((()=>import("./Blog-CdyEyNsR.js")),__vite__mapDeps([125,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,126,113,32,33])))),pv=D.lazy((()=>B((()=>import("./BlogPost-j86Vh1w0.js")),__vite__mapDeps([127,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,128,126,113,32,33,100,79,81])))),fv=D.lazy((()=>B((()=>import("./MedicationDetails-hugfMUoS.js")),__vite__mapDeps([59,1,2,5,3,60,61,20,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,62,63,64,65,66,67,50,68,69,44,70,31,71,72,73,57,42,49])))),gv=D.lazy((()=>B((()=>import("./Prescriptions-p33ePdo_.js")),__vite__mapDeps([129,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,68,69,44,71,130,131,66,107,132,45,32])))),xv=D.lazy((()=>B((()=>import("./SharedPrescriptions-XOpXQ8Fu.js")),__vite__mapDeps([133,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,131,79,81,134,44,45,43])))),yv=D.lazy((()=>B((()=>import("./ICD-Q47aKdlH.js")),__vite__mapDeps([135,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,107])))),vv=D.lazy((()=>B((()=>import("./Childcare-hh7reYAq.js")),__vite__mapDeps([136,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,137,138,72,139])))),bv=D.lazy((()=>B((()=>import("./GrowthCurves-CHqCLi1R.js")),__vite__mapDeps([140,1,2,3,5,141,7,8,9,142,4,36,42,137,20,21,22,23,16,18,24,25,26,6,10,11,12])))),wv=D.lazy((()=>B((()=>import("./Vaccines-CEYiUDsR.js")),__vite__mapDeps([143,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,144,145,146,147,72,43,67,137])))),jv=D.lazy((()=>B((()=>import("./Formulas-wQSDpV4V.js")),__vite__mapDeps([148,1,2,3,5,43,20,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,137])))),kv=D.lazy((()=>B((()=>import("./Supplementation-WaS5EKGi.js")),__vite__mapDeps([149,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,122,65,150,63,137])))),Ev=D.lazy((()=>B((()=>import("./PatientOverview-DQO8pyLl.js")),__vite__mapDeps([151,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,67,50,65,152,122,138,69,114,76,87,150,63,142,36,113,72,139,153,101,137])))),Av=D.lazy((()=>B((()=>import("./DNPM-CE7TePoE.js")),__vite__mapDeps([154,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,137,42])))),Cv=D.lazy((()=>B((()=>import("./AIAssistant-CGfZ6w0_.js")),__vite__mapDeps([155,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,107,141])))),_v=D.lazy((()=>B((()=>import("./Calculators-DDhgHVAh.js")),__vite__mapDeps([156,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,138,67,71,73,134,65])))),Nv=D.lazy((()=>B((()=>import("./FinneganCalculator-HjPw5m_P.js")),__vite__mapDeps([157,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,158,42])))),Sv=D.lazy((()=>B((()=>import("./ApgarCalculator-CPWw-j63.js")),__vite__mapDeps([159,1,2,5,158,42,3,4,6,7,8,9,10,11,12])))),Tv=D.lazy((()=>B((()=>import("./RodwellCalculator-DENNFKE7.js")),__vite__mapDeps([160,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,158,42])))),Pv=D.lazy((()=>B((()=>import("./CapurroCalculator-DKbNj-yg.js")),__vite__mapDeps([161,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,158,42])))),Dv=D.lazy((()=>B((()=>import("./CapurroNeuroCalculator-sYIzNJ-W.js")),__vite__mapDeps([162,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,158,42])))),Rv=D.lazy((()=>B((()=>import("./GINACalculator-DmPpgBsQ.js")),__vite__mapDeps([163,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,158,42])))),Mv=D.lazy((()=>B((()=>import("./GlasgowCalculator-DVf5reei.js")),__vite__mapDeps([164,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,158,42,134])))),Iv=D.lazy((()=>B((()=>import("./BMICalculator-U_UP70fT.js")),__vite__mapDeps([165,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,64,65,66,67,50,60,61,158])))),Lv=D.lazy((()=>B((()=>import("./BhutaniCalculator-CLqasPOr.js")),__vite__mapDeps([166,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,167,158])))),Ov=D.lazy((()=>B((()=>import("./SRICalculator-DTqQUEti.js")),__vite__mapDeps([168,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,101,86,70,50,71,73,152])))),Fv=D.lazy((()=>B((()=>import("./Layout-BVScLwHe.js")),__vite__mapDeps([169,1,2,5,4,96,115,170,101,20,21,22,23,16,18,24,25,26,3,6,7,8,9,10,11,12])))),Vv=D.lazy((()=>B((()=>import("./Categories-CSjE-QGy.js")),__vite__mapDeps([171,1,2,3,78,10,8,9,91,45,44,132,4,5,6,7,11,12])))),zv=D.lazy((()=>B((()=>import("./Medications-DrjAT4bE.js")),__vite__mapDeps([172,1,2,3,173,78,10,8,9,174,122,7,44,132,45,88,4,5,6,11,12])))),Bv=D.lazy((()=>B((()=>import("./Dosages-CFehfD9h.js")),__vite__mapDeps([175,1,2,3,174,122,7,8,9,44,132,45,78,10,4,5,6,11,12])))),$v=D.lazy((()=>B((()=>import("./PrescriptionCategories-DHOrXbGG.js")),__vite__mapDeps([176,1,2,3,78,10,8,9,45,5,44,132,4,6,7,11,12])))),Uv=D.lazy((()=>B((()=>import("./ICD10--iwu_O2S.js")),__vite__mapDeps([177,1,2,3,91,44,4,5,6,7,8,9,10,11,12])))),qv=D.lazy((()=>B((()=>import("./GrowthCurves-tYahrJQf.js")),__vite__mapDeps([178,1,2,3,106,44,4,5,6,7,8,9,10,11,12])))),Wv=D.lazy((()=>B((()=>import("./VaccineManagement-CERQpMb9.js")),__vite__mapDeps([179,1,2,3,145,146,147,78,10,8,9,144,44,132,45,4,5,6,7,11,12])))),Hv=D.lazy((()=>B((()=>import("./Formulas-BJ6v8FcP.js")),__vite__mapDeps([180,1,2,3,91,78,10,8,9,132,45,44,4,5,6,7,11,12])))),Gv=D.lazy((()=>B((()=>import("./DNPM-DvI-NHXm.js")),__vite__mapDeps([181,1,2,3,182,183,128,78,10,8,9,45,4,5,6,7,11,12])))),Kv=D.lazy((()=>B((()=>import("./ProblemsDebug-rKmkuiTq.js")),__vite__mapDeps([184,1,2,3,5,86,57,114,16,32,33,4,6,7,8,9,10,11,12])))),Yv=D.lazy((()=>B((()=>import("./Blog-DNw7jPHu.js")),__vite__mapDeps([185,1,2,3,6,122,7,8,9,186,187,188,82,189,5,132,45,44,190,191,78,10,4,11,12])))),Qv=D.lazy((()=>B((()=>import("./AdminRoute-BuC7xD9a.js")),__vite__mapDeps([192,1,2,4,5,3,6,7,8,9,10,11,12])))),Xv=D.lazy((()=>B((()=>import("./Settings-A7Xh4-50.js")),__vite__mapDeps([193,1,2,4,5,20,21,22,23,16,18,24,25,26,3,6,7,8,9,10,11,12,122,50,118,78,45,42])))),Jv=D.lazy((()=>B((()=>import("./Terms-Chn-5L-U.js")),__vite__mapDeps([194,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12])))),Zv=D.lazy((()=>B((()=>import("./Flowcharts-CCG-8Crp.js")),__vite__mapDeps([195,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12])))),eb=D.lazy((()=>B((()=>import("./DengueFlowchart-CR3wRWJP.js")),__vite__mapDeps([196,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,88])))),tb=D.lazy((()=>B((()=>import("./DKAFlowchart-DHOCM52y.js")),__vite__mapDeps([198,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,65,69,78,60,61])))),rb=D.lazy((()=>B((()=>import("./AnaphylaxisFlowchart-BGFhIKDN.js")),__vite__mapDeps([199,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,113,65,69,71,72,130])))),sb=D.lazy((()=>B((()=>import("./AsthmaFlowchart-D7KjhsAu.js")),__vite__mapDeps([200,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197])))),ab=D.lazy((()=>B((()=>import("./SeizureFlowchart-Eiy3UOuy.js")),__vite__mapDeps([201,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,65,71,72,202,197])))),nb=D.lazy((()=>B((()=>import("./PecarnFlowchart-B3MclrT-.js")),__vite__mapDeps([203,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197])))),ib=D.lazy((()=>B((()=>import("./VenomousAnimalsFlowchart-DAdJS11q.js")),__vite__mapDeps([204,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12])))),ob=D.lazy((()=>B((()=>import("./ScorpionFlowchart-D_TvGXg7.js")),__vite__mapDeps([205,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,88])))),lb=D.lazy((()=>B((()=>import("./BothropicFlowchart-Cbac_B9l.js")),__vite__mapDeps([206,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,88])))),cb=D.lazy((()=>B((()=>import("./CrotalicFlowchart-CbZdgwOs.js")),__vite__mapDeps([207,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,88,63])))),db=D.lazy((()=>B((()=>import("./ElapidicFlowchart-CXKblxMi.js")),__vite__mapDeps([208,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,209,210])))),ub=D.lazy((()=>B((()=>import("./PhoneutriaFlowchart-CnnWeEaF.js")),__vite__mapDeps([211,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,209,61,210,86])))),mb=D.lazy((()=>B((()=>import("./LoxoscelicFlowchart--GHdvPdS.js")),__vite__mapDeps([212,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,197,210])))),hb=D.lazy((()=>B((()=>import("./Poisonings-DWHdbvq8.js")),__vite__mapDeps([213,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,214])))),pb=D.lazy((()=>B((()=>import("./PoisoningDetails-B-w6AZMx.js")),__vite__mapDeps([215,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,63,214])))),fb=D.lazy((()=>B((()=>import("./Notes-B3en7yks.js")),__vite__mapDeps([216,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,90,45,217,44,67,98,107,32,33,186,124,218,219,187,188,100])))),gb=D.lazy((()=>B((()=>import("./WhatsAppBot-C1rodjD5.js")),__vite__mapDeps([220,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,113])))),xb=D.lazy((()=>B((()=>import("./DrWill-CexrI4Cx.js")),__vite__mapDeps([221,1,2,222,3,40,4,41,36,5,18,42,43,44,113,45,32,33,48,101,92,16,17,71,46,6,7,8,9,10,11,12])))),yb=D.lazy((()=>B((()=>import("./DrugInteractions-CtOPeunc.js")),__vite__mapDeps([223,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,69,122,170,79,80,81,224])))),vb=D.lazy((()=>B((()=>import("./Newsletters-Qa3p8gOQ.js")),__vite__mapDeps([225,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,128,28,94,67,100,31,32,33,113,82,76,42,29]))));D.lazy((()=>B((()=>import("./PediDrop-5-btotAG.js")),__vite__mapDeps([226,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,128,227,145,146,147,67,113,98,30,114,80,79,46,115,42,86]))));const bb=D.lazy((()=>B((()=>import("./PediDropAdmin-DnNOSS6L.js")),__vite__mapDeps([228,1,2,128,227,3,145,146,147,119,67,18,44,45,113,115,24,114,80,101,5,42,30,134,76,190,4,6,7,8,9,10,11,12])))),wb=D.lazy((()=>B((()=>import("./SiteSettings-0oYbi9oz.js")),__vite__mapDeps([229,1,2,3,182,16,86,91,36,94,12,8,9,7,4,5,6,10,11])))),jb=D.lazy((()=>B((()=>import("./Settings-BZrvAiMy.js")),__vite__mapDeps([230,1,2,44,84,3,4,5,6,7,8,9,10,11,12])))),kb=D.lazy((()=>B((()=>import("./HydrationCalculator-2y6fevWw.js")),__vite__mapDeps([231,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,113,30,65,69,107,36,99,62,31,197,70])))),Eb=D.lazy((()=>B((()=>import("./ConductsAndManagement-DohkTTxv.js")),__vite__mapDeps([232,1,2,3,78,10,8,9,6,91,173,4,186,233,88,44,45,189,234,224,218,187,82,190,191,5,7,11,12])))),Ab=D.lazy((()=>B((()=>import("./ConductsAndManagementNew-Dsx7LWIW.js")),__vite__mapDeps([235,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,113,42])))),Cb=D.lazy((()=>B((()=>import("./ConductsRouterNew-20d7jGLs.js")),__vite__mapDeps([236,1,2,3,20,5,21,22,23,16,18,24,25,26,4,6,7,8,9,10,11,12,217,237,42,63,78,79,80,81,41,183,146,147,82,48])))),_b=D.lazy((()=>B((()=>import("./ProtectedRoute-D3wxem2q.js")),__vite__mapDeps([238,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12])))),Nb=D.lazy((()=>B((()=>import("./AdminUsers-Jzx8keOi.js")),__vite__mapDeps([239,1,2,4,106,115,3,5,6,7,8,9,10,11,12])))),Sb=D.lazy((()=>B((()=>import("./MaintenancePage-DuHcjHkl.js")),__vite__mapDeps([240,1,2,122,7,8,9,78,10,97,113,69,3,4,5,6,11,12])))),Tb=D.lazy((()=>B((()=>import("./TestQuestions-Caj2tKdZ.js")),__vite__mapDeps([241,1,2,242,69,243,57,46,134,25,42,79,81,183,3,4,5,6,7,8,9,10,11,12])))),Pb=D.lazy((()=>B((()=>import("./PediatricStudyIntro-BontGhFz.js")),__vite__mapDeps([244,4,1,2,20,5,21,22,23,16,18,24,25,26,3,6,7,8,9,10,11,12,43,118,67,102,202,71,114,113])))),Db=D.lazy((()=>B((()=>import("./PediatricStudy-DGXqWgHq.js").then((e=>e.b))),__vite__mapDeps([245,1,2,3,222,128,94,12,8,9,7,124,10,243,141,5,90,23,118,67,102,21,22,16,18,24,25,20,26,4,6,11])))),Rb=D.lazy((()=>B((()=>import("./Questions-9D-MbuAL.js")),__vite__mapDeps([246,1,2,242,69,243,57,46,134,25,42,79,81,183,245,3,222,128,94,12,8,9,7,124,10,141,5,90,23,118,67,102,21,22,16,18,24,20,26,4,6,11])))),Mb=D.lazy((()=>B((()=>import("./Results-Jnb9qHdg.js")),__vite__mapDeps([247,1,2,23,113,248,18,5,31,120,21,22,16,24,25,49,20,26,3,4,6,7,8,9,10,11,12])))),Ib=D.lazy((()=>B((()=>import("./MedicationInstructions-CizQajPi.js")),__vite__mapDeps([249,1,2,3,122,7,8,9,186,119,189,234,187,82,4,5,6,10,11,12])))),Lb=D.lazy((()=>B((()=>import("./PrivacyPolicy-D-hoo44x.js")),__vite__mapDeps([250,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12])))),Ob=D.lazy((()=>B((()=>import("./MedicationsBreastfeeding-znCiG_YS.js")),__vite__mapDeps([251,1,2,5,20,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,128,49,76,237,217,113])))),Fb=D.lazy((()=>B((()=>import("./MedicationLeaflet-BNF73wNA.js")),__vite__mapDeps([252,1,2,3,5,19,113,4,6,7,8,9,10,11,12])))),Vb=D.lazy((()=>B((()=>import("./Maintenance-BcnYZ10m.js")),__vite__mapDeps([253,1,2,5,97,113,86,49,248,18,22,219,3,4,6,7,8,9,10,11,12])))),zb=D.lazy((()=>B((()=>import("./ResidencyBeta-ROUQGqMG.js")),__vite__mapDeps([254,1,2,20,5,21,22,23,16,18,24,25,26,3,4,6,7,8,9,10,11,12,113,115,248])))),Bb=D.lazy((()=>B((()=>import("./AuthCallback-CWT5HD43.js")),__vite__mapDeps([255,1,2,5,3,4,6,7,8,9,10,11,12])))),$b=({type:e="page"})=>"minimal"===e?a.jsx("div",{className:"flex items-center justify-center p-4",children:a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"})}):"component"===e?a.jsx("div",{className:"flex items-center justify-center p-8",children:a.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"})}):a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"})}),Ub=()=>{const e=$();(()=>{const e=$();T.useEffect((()=>{const t=new URLSearchParams(e.search);if(t.get("token"),t.get("type"),t.get("code"),t.get("access_token"),t.get("refresh_token"),e.hash){const t=new URLSearchParams(e.hash.substring(1));t.get("access_token"),t.get("refresh_token"),t.get("type")}}),[e])})();const t=["/settings","/prescriptions","/shared-prescriptions","/notes","/estudos/filtros","/questions","/results"].some((t=>e.pathname===t||e.pathname.startsWith(t+"/"))),r=["/calculadoras","/flowcharts","/puericultura","/condutas-e-manejos"].some((t=>e.pathname===t&&!e.pathname.includes("/",t.length)));return un(),(()=>{const e=L(),t=T.useRef(Date.now()),r=T.useRef(),s=T.useRef(!1),a=T.useCallback((()=>{t.current=Date.now()}),[]),n=T.useCallback((async()=>{if(!s.current){s.current=!0;try{const{data:t,error:r}=await Ts.auth.getSession(),s=[["medications"],["medication-categories"],["medications-with-instructions"],["medication-instructions"]];for(const a of s)await e.invalidateQueries({queryKey:a});await e.refetchQueries({type:"active",stale:!0})}catch(t){}finally{s.current=!1}}}),[e]),i=T.useCallback((()=>{"visible"===document.visibilityState&&(Date.now()-t.current>3e5&&n(),a())}),[n,a]),o=T.useCallback((()=>{Date.now()-t.current>12e4&&(r.current&&clearTimeout(r.current),r.current=setTimeout((()=>{n()}),1e3)),a()}),[n,a]),l=T.useCallback((()=>{a()}),[a]);T.useEffect((()=>{const e=["click","keydown","scroll","touchstart"];return document.addEventListener("visibilitychange",i),window.addEventListener("focus",o),e.forEach((e=>{document.addEventListener(e,l,{passive:!0})})),()=>{document.removeEventListener("visibilitychange",i),window.removeEventListener("focus",o),e.forEach((e=>{document.removeEventListener(e,l)})),r.current&&clearTimeout(r.current)}}),[i,o,l]),T.useEffect((()=>{const e=setInterval((()=>{Date.now()-t.current>18e5&&n()}),18e5);return()=>clearInterval(e)}),[n]),s.current})(),(()=>{const e=T.useRef(null),t=T.useRef(0);T.useEffect((()=>((async()=>{const{data:{session:r}}=await Ts.auth.getSession();if(!r)return;const s=async()=>{try{const e=Date.now();if(e-t.current<3e5)return;const{data:r,error:s}=await Ts.auth.refreshSession();if(s){const{data:{session:e}}=await Ts.auth.getSession()}else r.session&&(t.current=e)}catch(e){}};e.current=setInterval(s,144e5);const a=r.expires_at;a&&1e3*a-Date.now()<36e5&&s()})(),()=>{e.current&&clearInterval(e.current)})),[]),T.useEffect((()=>{const e=async()=>{if("visible"===document.visibilityState){const{data:{session:t}}=await Ts.auth.getSession();if(t){const r=t.expires_at;if(r&&1e3*r-Date.now()<18e5)try{await Ts.auth.refreshSession()}catch(e){}}}};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e)}}),[]),T.useEffect((()=>{const e=async()=>{const{data:{session:e}}=await Ts.auth.getSession();if(e){const r=e.expires_at;if(r&&1e3*r-Date.now()<9e5)try{await Ts.auth.refreshSession()}catch(t){}}};return window.addEventListener("focus",e),()=>{window.removeEventListener("focus",e)}}),[])})(),T.useEffect((()=>{mn.startMonitoring()}),[]),T.useEffect((()=>(pn.initialize(),$n.initialize(),()=>{pn.cleanup()})),[]),((e={})=>{const{maxRetries:t=3,retryDelay:r=1e3,showUserMessage:s=!0}=e;T.useEffect((()=>{let e=0,a=!1;const n=n=>{const i=n.message?.toLowerCase()||"";if((i.includes("loading chunk")||i.includes("failed to fetch dynamically imported module")||i.includes("mime type")||i.includes("module script")||n.filename?.includes(".js"))&&!a)if(e<t){if(e++,s&&1===e){const e=document.createElement("div");e.style.cssText="\n              position: fixed;\n              top: 20px;\n              right: 20px;\n              background: #3b82f6;\n              color: white;\n              padding: 12px 16px;\n              border-radius: 8px;\n              font-size: 14px;\n              z-index: 10000;\n              box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n              max-width: 300px;\n              animation: slideIn 0.3s ease-out;\n            ",e.innerHTML='\n              <div style="display: flex; align-items: center; gap: 8px;">\n                <div style="width: 16px; height: 16px; border: 2px solid white; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>\n                <span>Atualizando recursos...</span>\n              </div>\n            ';const t=document.createElement("style");t.textContent="\n              @keyframes slideIn {\n                from { transform: translateX(100%); opacity: 0; }\n                to { transform: translateX(0); opacity: 1; }\n              }\n              @keyframes spin {\n                to { transform: rotate(360deg); }\n              }\n            ",document.head.appendChild(t),document.body.appendChild(e),setTimeout((()=>{e.parentNode&&(e.style.animation="slideIn 0.3s ease-out reverse",setTimeout((()=>{e.remove(),t.remove()}),300))}),3e3)}setTimeout((()=>{a||(a=!0,window.location.reload())}),r*e)}else if(s){const e=document.createElement("div");e.style.cssText="\n              position: fixed;\n              top: 20px;\n              right: 20px;\n              background: #ef4444;\n              color: white;\n              padding: 16px;\n              border-radius: 8px;\n              font-size: 14px;\n              z-index: 10000;\n              box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n              max-width: 350px;\n              animation: slideIn 0.3s ease-out;\n            ",e.innerHTML='\n              <div>\n                <div style="font-weight: 600; margin-bottom: 8px;">Erro de Carregamento</div>\n                <div style="margin-bottom: 12px;">Alguns recursos não puderam ser carregados. Recarregue a página manualmente.</div>\n                <button onclick="window.location.reload()" style="\n                  background: rgba(255,255,255,0.2);\n                  border: 1px solid rgba(255,255,255,0.3);\n                  color: white;\n                  padding: 6px 12px;\n                  border-radius: 4px;\n                  cursor: pointer;\n                  font-size: 12px;\n                ">Recarregar Página</button>\n              </div>\n            ',document.body.appendChild(e)}},i=e=>{const t=e.reason?.message?.toLowerCase()||"";(t.includes("loading chunk")||t.includes("failed to fetch dynamically imported module")||t.includes("mime type")||t.includes("module script"))&&!a&&n({message:e.reason?.message||"Chunk loading error",filename:""})};return window.addEventListener("error",n),window.addEventListener("unhandledrejection",i),()=>{window.removeEventListener("error",n),window.removeEventListener("unhandledrejection",i)}}),[t,r,s])})({maxRetries:2,retryDelay:1e3,showUserMessage:!0}),(()=>{const e=z.useSession();T.useEffect((()=>{const t=setTimeout((async()=>{if(e?.user?.id)try{const{data:t,error:r}=await Ts.from("profiles").select("id").eq("id",e.user.id).single();if(r&&"PGRST116"===r.code){const{error:t}=await Ts.from("profiles").upsert({id:e.user.id,full_name:e.user.user_metadata?.full_name||"Usuário",formation_area:e.user.user_metadata?.formation_area||"Não informado",graduation_year:e.user.user_metadata?.graduation_year||"Não informado",is_student:e.user.user_metadata?.is_student||!1,is_professional:e.user.user_metadata?.is_professional||!1,avatar_url:e.user.user_metadata?.avatar_url||null,professional_email:"",phone:"",registration_number:""},{onConflict:"id"})}}catch(t){}}),1e3);return()=>clearTimeout(t)}),[e?.user?.id])})(),D.useEffect((()=>{(()=>{const e=new URLSearchParams(window.location.search);e.has("token")&&"recovery"===e.get("type")&&"/reset-password"===window.location.pathname||(async()=>{try{const{data:{session:e}}=await Ts.auth.getSession();if(e?.user){(e.user.app_metadata?.amr||[]).some((e=>"recovery"===e.method))&&"/reset-password"!==window.location.pathname&&(await Ts.auth.signOut(),sessionStorage.setItem("recovery_login_detected","true"),sessionStorage.setItem("recovery_user_id",e.user.id),window.location.href="/reset-password")}}catch(e){}})()})();const{data:{subscription:e}}=Ts.auth.onAuthStateChange(((e,t)=>{"SIGNED_IN"===e&&t?.user&&(t.user.app_metadata?.amr||[]).some((e=>"recovery"===e.method))&&"/reset-password"!==window.location.pathname&&setTimeout((async()=>{await Ts.auth.signOut(),sessionStorage.setItem("recovery_login_detected","true"),sessionStorage.setItem("recovery_user_id",t.user.id),window.location.href="/reset-password"}),100)}));return()=>{e.unsubscribe()}}),[]),a.jsxs("div",{className:t?"":r?Va.gradientBackground("pb-16 sm:pb-0 md:pb-0"):"pb-16 sm:pb-0 md:pb-0",children:[a.jsx(Fa,{}),a.jsx(Ra,{}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Uy,{})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(qy,{})}),a.jsx(T.Suspense,{fallback:a.jsx($b,{}),children:a.jsxs(H,{children:[a.jsx(G,{path:"/",element:a.jsx(Vy,{})}),a.jsx(G,{path:"/medicamentos",element:a.jsx(Yy,{})}),a.jsx(G,{path:"/reset-password",element:a.jsx(Qy,{})}),a.jsx(G,{path:"/auth/callback",element:a.jsx(Bb,{})}),a.jsx(G,{path:"/bulas-profissionais",element:a.jsx(Xy,{})}),a.jsx(G,{path:"/bulas-profissionais/:slug",element:a.jsx(Jy,{})}),a.jsx(G,{path:"/medicamentos-amamentacao",element:a.jsx(Ob,{})}),a.jsx(G,{path:"/blog",element:a.jsx(hv,{})}),a.jsx(G,{path:"/blog/post/:id",element:a.jsx(pv,{})}),a.jsx(G,{path:"/medicamentos/:slug",element:a.jsx(fv,{})}),a.jsx(G,{path:"/medicamentos/:medicationId/bula",element:a.jsx(Fb,{})}),a.jsx(G,{path:"/medicamentos/painel",element:a.jsx(fv,{})}),a.jsx(G,{path:"/prescriptions",element:a.jsx(_b,{children:a.jsx(gv,{})})}),a.jsx(G,{path:"/shared-prescriptions",element:a.jsx(_b,{children:a.jsx(xv,{})})}),a.jsx(G,{path:"/settings",element:a.jsx(_b,{children:a.jsx(Xv,{})})}),a.jsx(G,{path:"/condutas-e-manejos",Component:Ab}),a.jsx(G,{path:"/condutas-e-manejos/:categorySlug/:subcategorySlug/:topicSlug",element:a.jsx(Cb,{})}),a.jsx(G,{path:"/condutas-e-manejos/:categorySlug/:subcategorySlug",element:a.jsx(Cb,{})}),a.jsx(G,{path:"/condutas-e-manejos/:categorySlug",element:a.jsx(Cb,{})}),a.jsx(G,{path:"/terms",element:a.jsx(Jv,{})}),a.jsx(G,{path:"/icd",element:a.jsx(yv,{})}),a.jsx(G,{path:"/puericultura",element:a.jsx(vv,{})}),a.jsx(G,{path:"/puericultura/curva-de-crescimento",element:a.jsx(bv,{})}),a.jsx(G,{path:"/puericultura/calendario-vacinal",element:a.jsx(wv,{})}),a.jsx(G,{path:"/puericultura/formulas",element:a.jsx(jv,{})}),a.jsx(G,{path:"/puericultura/suplementacao-infantil",element:a.jsx(kv,{})}),a.jsx(G,{path:"/puericultura/patient-overview",element:a.jsx(Ev,{})}),a.jsx(G,{path:"/dnpm",element:a.jsx(Av,{})}),a.jsx(G,{path:"/ai-assistant",element:a.jsx(Cv,{})}),a.jsx(G,{path:"/anamnese",element:a.jsx(mv,{})}),a.jsx(G,{path:"/calculadoras",element:a.jsx(_v,{})}),a.jsx(G,{path:"/calculadoras/apgar",element:a.jsx(Sv,{})}),a.jsx(G,{path:"/calculadoras/rodwell",element:a.jsx(Tv,{})}),a.jsx(G,{path:"/calculadoras/capurro",element:a.jsx(Pv,{})}),a.jsx(G,{path:"/calculadoras/capurro-neuro",element:a.jsx(Dv,{})}),a.jsx(G,{path:"/calculadoras/finnegan",element:a.jsx(Nv,{})}),a.jsx(G,{path:"/calculadoras/gina",element:a.jsx(Rv,{})}),a.jsx(G,{path:"/calculadoras/glasgow",element:a.jsx(Mv,{})}),a.jsx(G,{path:"/calculadoras/imc",element:a.jsx(Iv,{})}),a.jsx(G,{path:"/calculadoras/bhutani",element:a.jsx(Lv,{})}),a.jsx(G,{path:"/calculadoras/sri",element:a.jsx(Ov,{})}),a.jsx(G,{path:"/flowcharts",element:a.jsx(Zv,{})}),a.jsx(G,{path:"/flowcharts/seizure",element:a.jsx(ab,{})}),a.jsx(G,{path:"/flowcharts/dengue",element:a.jsx(eb,{})}),a.jsx(G,{path:"/flowcharts/dka",element:a.jsx(tb,{})}),a.jsx(G,{path:"/flowcharts/anaphylaxis",element:a.jsx(rb,{})}),a.jsx(G,{path:"/flowcharts/asthma",element:a.jsx(sb,{})}),a.jsx(G,{path:"/flowcharts/pecarn",element:a.jsx(nb,{})}),a.jsx(G,{path:"/flowcharts/venomous",element:a.jsx(ib,{})}),a.jsx(G,{path:"/flowcharts/venomous/scorpion",element:a.jsx(ob,{})}),a.jsx(G,{path:"/flowcharts/venomous/bothropic",element:a.jsx(lb,{})}),a.jsx(G,{path:"/flowcharts/venomous/crotalic",element:a.jsx(cb,{})}),a.jsx(G,{path:"/flowcharts/venomous/elapidic",element:a.jsx(db,{})}),a.jsx(G,{path:"/flowcharts/venomous/phoneutria",element:a.jsx(ub,{})}),a.jsx(G,{path:"/flowcharts/venomous/loxoscelic",element:a.jsx(mb,{})}),a.jsx(G,{path:"/flowcharts/hidratacao",element:a.jsx(kb,{})}),a.jsx(G,{path:"/poisonings",element:a.jsx(hb,{})}),a.jsx(G,{path:"/poisonings/:id",element:a.jsx(pb,{})}),a.jsx(G,{path:"/notes",element:a.jsx(_b,{children:a.jsx(fb,{})})}),a.jsx(G,{path:"/whatsapp-bot",element:a.jsx(gb,{})}),a.jsx(G,{path:"/dr-will",element:a.jsx(_b,{children:a.jsx(xb,{})})}),a.jsx(G,{path:"/interacoes-medicamentosas",element:a.jsx(yb,{})}),a.jsx(G,{path:"/newsletters",element:a.jsx(vb,{})}),a.jsx(G,{path:"/residencia-beta",element:a.jsx(zb,{})}),a.jsx(G,{path:"/test-questions",element:a.jsx(Tb,{})}),a.jsx(G,{path:"/estudos",element:a.jsx(Pb,{})}),a.jsx(G,{path:"/estudos/filtros",element:a.jsx(_b,{children:a.jsx(Db,{})})}),a.jsx(G,{path:"/questions",element:a.jsx(_b,{children:a.jsx(Rb,{})})}),a.jsx(G,{path:"/questions/:sessionId",element:a.jsx(_b,{children:a.jsx(Rb,{})})}),a.jsx(G,{path:"/results/:sessionId",element:a.jsx(_b,{children:a.jsx(Mb,{})})}),a.jsx(G,{path:"/admin",element:a.jsx(Qv,{}),children:a.jsxs(G,{element:a.jsx(Fv,{}),children:[a.jsx(G,{path:"dashboard",element:a.jsx(sv,{})}),a.jsx(G,{path:"blog",element:a.jsx(Yv,{})}),a.jsx(G,{path:"categories",element:a.jsx(Vv,{})}),a.jsx(G,{path:"medications",element:a.jsx(zv,{})}),a.jsx(G,{path:"dosages",element:a.jsx(Bv,{})}),a.jsx(G,{path:"instructions",element:a.jsx(Ib,{})}),a.jsx(G,{path:"condutas-e-manejos",element:a.jsx(Eb,{})}),a.jsx(G,{path:"prescription-categories",element:a.jsx($v,{})}),a.jsx(G,{path:"icd10",element:a.jsx(Uv,{})}),a.jsx(G,{path:"growth-curves",element:a.jsx(qv,{})}),a.jsx(G,{path:"vaccines",element:a.jsx(Wv,{})}),a.jsx(G,{path:"formulas",element:a.jsx(Hv,{})}),a.jsx(G,{path:"dnpm",element:a.jsx(Gv,{})}),a.jsx(G,{path:"site-settings",element:a.jsx(wb,{})}),a.jsx(G,{path:"settings",element:a.jsx(jb,{})}),a.jsx(G,{path:"question-import",element:a.jsx(Zy,{})}),a.jsx(G,{path:"question-formatting",element:a.jsx(ev,{})}),a.jsx(G,{path:"medication-import",element:a.jsx(tv,{})}),a.jsx(G,{path:"format-themes",element:a.jsx(rv,{})}),a.jsx(G,{path:"admin-users",element:a.jsx(Nb,{})}),a.jsx(G,{path:"maintenance",element:a.jsx(Sb,{})}),a.jsx(G,{path:"drug-interaction-medications",element:a.jsx(av,{})}),a.jsx(G,{path:"breastfeeding-medications",element:a.jsx(nv,{})}),a.jsx(G,{path:"breastfeeding-medications-enhancement",element:a.jsx(iv,{})}),a.jsx(G,{path:"medication-improvement",element:a.jsx(ov,{})}),a.jsx(G,{path:"cas-dcb-validation",element:a.jsx(lv,{})}),a.jsx(G,{path:"problems-debug",element:a.jsx(Kv,{})}),a.jsx(G,{path:"feedback-management",element:a.jsx(cv,{})}),a.jsx(G,{path:"active-ingredients-formatter",element:a.jsx(dv,{})}),a.jsx(G,{path:"active-ingredients-formatter",element:a.jsx(dv,{})}),a.jsx(G,{path:"dr-will-control",element:a.jsx(uv,{})}),a.jsx(G,{path:"pedidrop",element:a.jsx(bb,{})})]})}),a.jsx(G,{path:"/politica-privacidade",element:a.jsx(Lb,{})}),a.jsx(G,{path:"/maintenance",element:a.jsx(Vb,{})}),a.jsx(G,{path:"*",element:a.jsx(K,{to:"/",replace:!0})})]})}),a.jsx(T.Suspense,{fallback:a.jsx($b,{type:"minimal"}),children:a.jsx(zy,{})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(By,{})}),a.jsx(T.Suspense,{fallback:a.jsx($b,{type:"minimal"}),children:a.jsx($y,{})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Wy,{checkInterval:120,enabled:!1})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Gy,{})}),a.jsx(T.Suspense,{fallback:null,children:a.jsx(Ky,{})})]})},qb=()=>(T.useEffect((()=>{const e=document.querySelector('link[href*="facebook.com/tr"]');if(e&&e.setAttribute("as","image"),(new Image).src="/faviconx.webp",ct.isNativePlatform()){const e=30,t=48,r=window.innerHeight,s=window.innerWidth,a=r-e-t;document.documentElement.style.setProperty("--safe-area-top",e+"px"),document.documentElement.style.setProperty("--safe-area-bottom",t+"px"),document.documentElement.style.setProperty("--safe-area-height",a+"px"),document.body.classList.add("android-safe-area");const n=document.getElementById("root");window.safeAreaDebugData={timestamp:(new Date).toISOString(),platform:ct.getPlatform(),isNative:ct.isNativePlatform(),userAgent:navigator.userAgent,viewport:{width:s,height:r},calculations:{statusBarHeight:e,navigationBarHeight:t,calculatedHeight:a,formula:`${r} - ${e} - ${t} = ${a}`},cssVariables:{top:document.documentElement.style.getPropertyValue("--safe-area-top"),bottom:document.documentElement.style.getPropertyValue("--safe-area-bottom"),height:document.documentElement.style.getPropertyValue("--safe-area-height")},bodyClasses:document.body.className,rootElement:n?{classes:n.className,computedHeight:window.getComputedStyle(n).height,computedPosition:window.getComputedStyle(n).position,computedTop:window.getComputedStyle(n).top}:null}}else window.safeAreaDebugData={timestamp:(new Date).toISOString(),platform:ct.getPlatform(),isNative:ct.isNativePlatform(),userAgent:navigator.userAgent,message:"Safe areas não aplicadas - não é plataforma nativa"}}),[]),T.useEffect((()=>{const e=()=>{};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e)}}),[]),a.jsx(Da,{children:a.jsx(xa,{children:a.jsx(dn,{children:a.jsx(z.SessionContextProvider,{supabaseClient:Ts,initialSession:null,children:a.jsx(Oa,{children:a.jsx(Ds,{children:a.jsx(W,{children:a.jsx(Ba,{children:a.jsx(Ha,{children:a.jsx(Ka,{children:a.jsx(Xa,{children:a.jsx(Cs,{children:a.jsx(T.Suspense,{fallback:a.jsx($b,{type:"minimal"}),children:a.jsx(Hy,{children:a.jsx(Ub,{})})})})})})})})})})})})})})}));"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then((function(e){for(let t of e)t.unregister()})).catch((function(e){}));const Wb=new O({...at});nt.initializeWithQueryClient(Wb),nt.setupAutoSave(Wb),nt.remove(JSON.stringify(["medications"])),nt.remove(JSON.stringify(["medication-categories"])),ct.isNativePlatform()&&(wt.setStyle({style:xt.Dark}).then((()=>{})).catch((e=>{})),wt.setOverlayWebView({overlay:!1}).then((()=>{})).catch((e=>{})),wt.show().then((()=>{})).catch((e=>{}))),et.createRoot(document.getElementById("root")).render(a.jsx(D.StrictMode,{children:a.jsx(F,{client:Wb,children:a.jsx(qb,{})})}));export{Kt as $,Ai as A,Es as B,Mt as C,cs as D,er as E,Ey as F,Id as G,Ut as H,qt as I,dt as J,Vn as K,Gt as L,Xt as M,wt as N,ct as O,tr as P,Jl as Q,qn as R,sr as S,Ld as T,gi as U,bi as V,Ot as W,mr as X,xi as Y,Lt as Z,yi as _,Za as a,bd as a$,vi as a0,Gn as a1,Yn as a2,Jn as a3,Zn as a4,ei as a5,si as a6,ri as a7,_t as a8,Dy as a9,mi as aA,wi as aB,ir as aC,gs as aD,Ft as aE,js as aF,Qt as aG,Rt as aH,Kc as aI,Tt as aJ,ds as aK,It as aL,Va as aM,tu as aN,Zd as aO,eu as aP,ru as aQ,au as aR,Jd as aS,ci as aT,di as aU,ks as aV,ur as aW,Rs as aX,Li as aY,lr as aZ,pr as a_,Wa as aa,Cy as ab,zt as ac,rr as ad,Od as ae,zd as af,Vd as ag,Ud as ah,Fd as ai,qd as aj,Wt as ak,dr as al,Ns as am,Ss as an,Hn as ao,ai as ap,Dt as aq,Hd as ar,Gd as as,Kd as at,Yd as au,ar as av,Jt as aw,Qd as ax,cn as ay,Zc as az,Ja as b,ls as b0,Ta as b1,$t as b2,ec as b3,st as b4,rt as b5,ka as b6,ti as b7,zc as b8,un as b9,Ya as ba,ms as bb,Zt as bc,ut as bd,wn as be,kn as bf,ft as bg,Ln as bh,zl as bi,Bl as bj,Vl as bk,At as c,Zl as d,ps as e,fs as f,xs as g,Ma as h,$a as i,os as j,Nt as k,Pt as l,by as m,Ct as n,gd as o,xd as p,hd as q,pd as r,Ts as s,fd as t,La as u,yd as v,wd as w,vd as x,Yt as y,ys as z};
