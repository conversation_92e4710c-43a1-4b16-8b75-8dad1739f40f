/**
 * Utilitários para detecção e configuração do ambiente Capacitor
 */

import { Capacitor } from '@capacitor/core';

/**
 * Verifica se está rodando no Capacitor (app nativo)
 */
export const isCapacitor = (): boolean => {
  return Capacitor.isNativePlatform();
};

/**
 * Verifica se está rodando no Android
 */
export const isAndroid = (): boolean => {
  return Capacitor.getPlatform() === 'android';
};

/**
 * Verifica se está rodando no iOS
 */
export const isIOS = (): boolean => {
  return Capacitor.getPlatform() === 'ios';
};

/**
 * Verifica se está rodando na web
 */
export const isWeb = (): boolean => {
  return Capacitor.getPlatform() === 'web';
};

/**
 * Obtém informações da plataforma
 */
export const getPlatformInfo = () => {
  return {
    platform: Capacitor.getPlatform(),
    isNative: Capacitor.isNativePlatform(),
    isPluginAvailable: (pluginName: string) => Capacitor.isPluginAvailable(pluginName)
  };
};

/**
 * Configurações específicas para cada plataforma
 */
export const getPlatformConfig = () => {
  const platform = Capacitor.getPlatform();
  
  switch (platform) {
    case 'android':
      return {
        statusBarStyle: 'dark',
        navigationBarColor: '#ffffff',
        splashScreenDelay: 2000,
        allowMixedContent: true
      };
    case 'ios':
      return {
        statusBarStyle: 'light',
        splashScreenDelay: 2000,
        scrollEnabled: true
      };
    default:
      return {
        statusBarStyle: 'default',
        splashScreenDelay: 0
      };
  }
};

/**
 * URLs base para diferentes ambientes
 */
export const getBaseUrl = (): string => {
  if (isCapacitor()) {
    // Em produção, usar a URL real do site
    return 'https://pedb.com.br';
  }
  
  // Para desenvolvimento web
  return window.location.origin;
};

/**
 * Configurações de rede para Capacitor
 */
export const getNetworkConfig = () => {
  return {
    timeout: isCapacitor() ? 30000 : 10000, // Timeout maior para mobile
    retries: isCapacitor() ? 3 : 1,
    cacheFirst: isCapacitor() // Cache first em mobile
  };
};
