import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-B2TOw806.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-CrSshpOb.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-ClHMSbsi.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";import"./collapsible-B6HfSnGs.js";import"./accordion-R4Om_XcG.js";import"./PatientInfoSection-PgYG6-ht.js";import"./scale-zgGFIpV3.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-zjm19wJF.js";import"./user-C-nz09Xa.js";import"./DosageDisplay-CV6AAND_.js";import"./alert-BQiXO7k2.js";import"./plus-BVM45PkF.js";import"./lightbulb-1Cac0QY8.js";import"./external-link-BmPF7vzZ.js";import"./stethoscope-DxW-nyRB.js";import"./syringe-BOmxt2Jt.js";import"./wind-D3164uUJ.js";import"./bug-BwG1PuTR.js";import"./chevron-left-CuYzwBha.js";import"./house-B0qpbnGW.js";function t(){return r.jsx(o,{})}export{t as default};
