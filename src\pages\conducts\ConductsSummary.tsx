import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";

import { Card } from "@/components/ui/card";
import { ChevronLeft, BookOpen, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ConductMetaTags } from "@/components/conducts/ConductMetaTags";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ConductsFeedback } from "@/components/feedback/ConductsFeedback";
import { ImageViewer } from "@/components/ImageViewer";
import { OptimizedConductsView } from "@/components/conducts/OptimizedConductsView";
import { useIsMobile } from "@/hooks/use-mobile";

// Analytics removido completamente

// Cache simples para evitar requisições duplicadas
const summaryCache = new Map<string, any>();
const CACHE_DURATION = 300000; // 5 minutos - cache mais agressivo

// Debounce para evitar execuções múltiplas
let fetchTimeout: NodeJS.Timeout | null = null;

interface SubSection {
  title: string;
  content: string;
}

interface Section {
  title: string;
  content: string;
  subsections: SubSection[];
}

interface Summary {
  id: string;
  title: string;
  content: string;
  content_type: string;
  format_type?: "standard" | "simple" | "optimized";
  sections: Section[];
}



interface OptimizedSummary {
  id: string;
  title: string;
  slug: string;
  conducts_content: string;
  treatment_content: string;
  format_type: "optimized";
  has_treatment: boolean;
  published: boolean;
}

export const ConductsSummary = React.memo(() => {
  const { categorySlug, subcategorySlug, topicSlug } = useParams();

  // Analytics removido completamente
  const [summary, setSummary] = useState<Summary | null>(null);
  const [optimizedSummary, setOptimizedSummary] = useState<OptimizedSummary | null>(null);
  const [topicName, setTopicName] = useState("");
  const [categoryName, setCategoryName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isOptimizedFormat, setIsOptimizedFormat] = useState(false);
  const [activeTab, setActiveTab] = useState("conducts");
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // 🎯 Listener para mudanças de aba vindas do OptimizedConductsView
  useEffect(() => {
    const handleTabChangeFromOptimized = (event: CustomEvent) => {
      const newTab = event.detail;
      if (newTab) {
        setActiveTab(newTab);
      }
    };

    // Escutar mudanças de aba do OptimizedConductsView
    window.addEventListener('optimizedTabChange', handleTabChangeFromOptimized as EventListener);
    return () => window.removeEventListener('optimizedTabChange', handleTabChangeFromOptimized as EventListener);
  }, []);

  useEffect(() => {
    // Limpar timeout anterior
    if (fetchTimeout) {
      clearTimeout(fetchTimeout);
    }

    // Evitar execução dupla em desenvolvimento
    let isCancelled = false;



    const fetchSummary = async () => {
      if (isCancelled) return;



      try {
        // Validar parâmetros obrigatórios
        if (!categorySlug) {
          throw new Error('categorySlug é obrigatório');
        }

        // Buscar categoria (com cache)
        const categoryKey = `summary_category_${categorySlug}`;
        let categoryData, categoryError;

        // 🔧 OTIMIZAÇÃO: Usar cache compartilhado do Router
        const sharedCache = (window as any).__SUMMARY_CACHE__ || new Map();
        const cachedCategory = sharedCache.get(categoryKey) || summaryCache.get(categoryKey);

        if (cachedCategory && Date.now() - cachedCategory.timestamp < CACHE_DURATION) {
          categoryData = cachedCategory.data;
          categoryError = cachedCategory.error;
        } else {
          const result = await supabase
            .from('pedbook_conducts_categories')
            .select('id, name')
            .eq('slug', categorySlug)
            .maybeSingle();

          categoryData = result.data;
          categoryError = result.error;

          // Armazenar no cache
          summaryCache.set(categoryKey, {
            data: categoryData,
            error: categoryError,
            timestamp: Date.now()
          });
        }

        if (categoryError) {
          console.error('❌ Erro ao buscar categoria:', categoryError);
          throw categoryError;
        }

        if (!categoryData) {
          console.error('❌ Categoria não encontrada:', categorySlug);
          throw new Error('Categoria não encontrada');
        }

        setCategoryName(categoryData.name);

        // 🔧 OTIMIZAÇÃO: Buscar tópico do cache compartilhado primeiro
        const topicSlugToFind = topicSlug || subcategorySlug;
        const topicKey = `topic-${categoryData.id}-${topicSlugToFind}`;
        const cachedTopic = sharedCache.get(topicKey);

        let topicData, topicError;
        if (cachedTopic && Date.now() - cachedTopic.timestamp < CACHE_DURATION) {
          topicData = cachedTopic.data;
          topicError = cachedTopic.error;
        } else {
          const result = await supabase
            .from('pedbook_conducts_topics')
            .select('id, name')
            .eq('category_id', categoryData.id)
            .eq('slug', topicSlugToFind)
            .single();

          topicData = result.data;
          topicError = result.error;
        }

        if (topicError || !topicData) {
          console.error('❌ ConductsSummary - Tópico não encontrado:', topicSlugToFind);
          throw new Error('Tópico não encontrado');
        }

        setTopicName(topicData.name);

        // Primeiro, tentar buscar conteúdo otimizado

        // Tentar múltiplas abordagens para encontrar o conteúdo
        let optimizedData = null;
        let optimizedError = null;

        // Abordagem 1: Query com published = true
        const { data: optimizedData1, error: optimizedError1 } = await supabase
          .from('pedbook_conducts_optimized')
          .select('*')
          .eq('topic_id', topicData.id)
          .eq('published', true)
          .maybeSingle();

        if (optimizedData1 && !optimizedError1) {
          optimizedData = optimizedData1;
        } else {
          // Abordagem 2: Query sem filtro de published
          const { data: optimizedData2, error: optimizedError2 } = await supabase
            .from('pedbook_conducts_optimized')
            .select('*')
            .eq('topic_id', topicData.id)
            .maybeSingle();



          optimizedData = optimizedData2;
          optimizedError = optimizedError2;
        }

        if (optimizedData) {
          // Conteúdo otimizado encontrado
          setOptimizedSummary(optimizedData);
          setIsOptimizedFormat(true);
          setLoading(false);
          return;
        }

        // Se não encontrou otimizado, buscar formato tradicional
        const { data: summaryData, error: summaryError } = await supabase
          .from('pedbook_conducts_summaries')
          .select('*')
          .eq('topic_id', topicData.id)
          .eq('published', true)
          .maybeSingle();



        if (summaryError) {
          console.error('❌ Erro ao buscar resumo:', summaryError);
          throw summaryError;
        }

        if (!summaryData) {
          toast({
            title: "Conteúdo não encontrado",
            description: "O resumo deste tópico ainda não está disponível."
          });
          return;
        }



        // Determinar o formato a ser usado
        let formatType: "standard" | "simple" = "standard"; // Valor padrão

        if (summaryData.format_type === "simple") {
          formatType = "simple";
        } else if (summaryData.format_type === "standard") {
          formatType = "standard";
        } else {
          // Detectar automaticamente baseado no conteúdo
          const content = summaryData.content as string;
          const simpleFormatDetected = /<[^>]*>.*?##[\.;].*?<\/[^>]*>/g.test(content) ||
                                      content.includes('##.') ||
                                      content.includes('##;');

          if (simpleFormatDetected) {
            formatType = "simple";
          } else {
          }
        }


        // Processar o conteúdo com base no formato
        let processedContent = summaryData.content as string;
        let sections;

        // Caso específico para o teste
        if (processedContent.includes('<p>##. teste<br><br>##; teste</p>')) {

          // Criar seções manualmente para o caso de teste
          sections = [
            {
              title: "teste",
              content: "",
              subsections: [
                {
                  title: "teste",
                  content: ""
                }
              ]
            }
          ];
        } else {
          // Processamento normal
          if (formatType === "simple") {
            processedContent = processContent(processedContent, "simple");
          }

          // Extrair seções com base no formato
          if (formatType === "simple") {
            sections = extractSectionsFromContent(processedContent);
          } else {
            sections = extractSectionsFromContent(summaryData.content as string);
          }
        }

        const processedSummary = {
          ...summaryData,
          format_type: formatType,
          sections: sections
        };

        setSummary(processedSummary as Summary);



      } catch (error: any) {
        toast({
          title: "Erro ao carregar conteúdo",
          description: error.message || "Não foi possível carregar o conteúdo"
        });
      } finally {
        setLoading(false);
      }
    };

    // Verificar se temos os parâmetros necessários
    const hasRequiredParams = (topicSlug || subcategorySlug) && categorySlug;

    if (hasRequiredParams) {
      // Executar com debounce de 50ms
      fetchTimeout = setTimeout(() => {
        fetchSummary();
      }, 50);
    }

    return () => {
      isCancelled = true;
      if (fetchTimeout) {
        clearTimeout(fetchTimeout);
      }
    };
  }, [topicSlug, subcategorySlug, categorySlug, toast]);

  const decodeHtmlEntities = (text: string) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  const extractSectionsFromContent = (content: string): Section[] => {
    const sections: Section[] = [];

    // Primeiro, encontrar todas as seções principais (adicionando novo padrão)
    const mainSectionRegex = /(?:<h[23]><strong>##\.\s*(.*?)<\/strong><\/h[23]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>|<h2[^>]*>(.*?)<\/h2>)([\s\S]*?)(?=<h[23]><strong>##\.|<h2>##\.|<h2|$)/g;
    const mainSections = Array.from(content.matchAll(mainSectionRegex));



    mainSections.forEach((sectionMatch, index) => {
      // Ajuste para considerar o novo grupo de captura
      const title = (sectionMatch[1] || sectionMatch[2] || sectionMatch[3] || sectionMatch[4]).trim();
      let mainContent = sectionMatch[5];



      // Normalizar subseções em texto puro para HTML, considerando ':' e ';'
      mainContent = mainContent.replace(
        /(?:\n|^)##;\s*(.*?)(?::|;)?(?=\n|$)/g,
        '\n<h3><strong>##; $1</strong></h3>'
      );

      // Normalizar qualquer variação de título de subseção, incluindo ':' e ';'
      mainContent = mainContent.replace(
        /<h[34]>([^<]*?)(?::|;)?\s*<\/h[34]>/g,
        '<h3><strong>##; $1</strong></h3>'
      );

      // Regex mais flexível para encontrar subseções, incluindo h3 gerados pelo processamento
      const subsectionRegex = /(?:<h[34]>(?:(?:<strong>)?##;\s*([^<]*?)(?::|;)?(?:<\/strong>)?|##;\s*([^<]*?)(?::|;)?)<\/h[34]>|<h3[^>]*>(.*?)<\/h3>)([\s\S]*?)(?=<h[34]>(?:(?:<strong>)?##;|<strong>)|<h[23]><strong>##\.|<h[23]|$)/g;
      const subsectionMatches = Array.from(mainContent.matchAll(subsectionRegex));



      const subsections: SubSection[] = [];

      // Processar cada subseção encontrada
      subsectionMatches.forEach((subMatch, subIndex) => {
        const subTitle = decodeHtmlEntities((subMatch[1] || subMatch[2] || subMatch[3]).trim());
        let subContent = subMatch[4];



        if (subContent) {
          // Remover apenas tags de seção, preservando formatação
          subContent = subContent
            .replace(/<\/?(?:h[34])>/g, '') // Remove apenas h3 e h4
            .replace(/^\s*(?:<br\s*\/?>\s*)*/, '')
            .replace(/^[:\s;]+/, '')
            .trim();

          // Remover tags h3/h4 residuais se ainda existirem
          ['h3', 'h4'].forEach(tag => {
            const closeTagIndex = subContent.indexOf(`</${tag}>`);
            if (closeTagIndex !== -1) {
              subContent = subContent.substring(closeTagIndex + 5);
            }
          });

          subContent = subContent.trim();
        }


        subsections.push({
          title: subTitle,
          content: subContent || ""
        });

        // Se esta é a primeira subseção, ajustar o conteúdo principal
        if (subIndex === 0) {
          const firstSubsectionStart = mainContent.indexOf(subMatch[0]);
          if (firstSubsectionStart !== -1) {
            mainContent = mainContent.substring(0, firstSubsectionStart).trim();
          }
        }
      });

      sections.push({
        title,
        content: mainContent.trim(),
        subsections
      });
    });


    return sections;
  };

  // Função para converter JSON de seções para markdown
  const convertSectionsToMarkdown = (content: string): string => {
    if (!content) return '';

    // Se já é markdown (começa com #), retorna como está
    if (content.trim().startsWith('#')) {
      return content;
    }

    // Se não parece ser JSON, retorna como está
    if (!content.trim().startsWith('{')) {
      return content;
    }

    // Tentar converter JSON para markdown
    try {
      const parsed = JSON.parse(content);

      if (!parsed.sections || !Array.isArray(parsed.sections)) {
        return content;
      }

      let markdown = '';

      parsed.sections.forEach((section: any) => {
        if (section.title && section.content) {
          // Adicionar título da seção como H2
          markdown += `## ${section.title}\n\n`;

          // Adicionar conteúdo da seção
          markdown += `${section.content}\n\n`;
        }
      });

      return markdown.trim();
    } catch (error) {
      return content;
    }
  };

  const processContent = (content: string, formatType: "standard" | "simple" = "standard") => {


    if (!content) {

      return '';
    }

    if (formatType === "simple") {


      // Caso específico para o teste
      if (content.includes('<p>##. teste<br><br>##; teste</p>')) {

        return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">teste</h2><h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">teste</h3>`;
      }

      // Substituir <p>##. Título</p> por <h2>Título</h2>
      let processedContent = content.replace(
        /<p>##\.\s*(.*?)(?:<\/p>|<br>)/g,
        (match, title) => {

          return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${title}</h2>`;
        }
      );

      // Substituir ##. Título no início de uma linha por <h2>Título</h2>
      processedContent = processedContent.replace(
        /##\.\s*(.*?)(?=<br>|<\/p>|$)/g,
        (match, title) => {

          return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${title}</h2>`;
        }
      );

      // Substituir <p>##; Subtítulo</p> por <h3>Subtítulo</h3>
      processedContent = processedContent.replace(
        /<p>##;\s*(.*?)(?:<\/p>|<br>)/g,
        (match, subtitle) => {

          return `<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">${subtitle}</h3>`;
        }
      );

      // Substituir ##; Subtítulo no início de uma linha por <h3>Subtítulo</h3>
      processedContent = processedContent.replace(
        /##;\s*(.*?)(?=<br>|<\/p>|$)/g,
        (match, subtitle) => {

          return `<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-4 mb-1">${subtitle}</h3>`;
        }
      );


      return processedContent;
    }

    // No modo padrão, retornamos o conteúdo sem alterações
    // Isso garante que o conteúdo formatado pelo editor seja exibido corretamente

    return content;
  };

  useEffect(() => {
    window.handleImageClick = (src: string) => {
      setSelectedImage(src);
    };

    return () => {
      delete window.handleImageClick;
    };
  }, []);

  // 🎯 CONTROLE DE ALTURA DA PÁGINA - Evitar scroll externo mas permitir scroll interno
  useEffect(() => {
    if (isMobile) {
      // Salvar estilos originais
      const originalBodyHeight = document.body.style.height;
      const originalBodyMaxHeight = document.body.style.maxHeight;
      const originalBodyOverflow = document.body.style.overflow;
      const originalHtmlHeight = document.documentElement.style.height;
      const originalHtmlMaxHeight = document.documentElement.style.maxHeight;

      const rootElement = document.getElementById('root');
      const originalRootHeight = rootElement?.style.height;
      const originalRootMaxHeight = rootElement?.style.maxHeight;
      const originalRootOverflow = rootElement?.style.overflow;

      // Aplicar altura fixa mas permitir scroll interno
      document.body.style.height = '100vh';
      document.body.style.maxHeight = '100vh';
      // ✅ CORREÇÃO: Permitir scroll no body para não cortar conteúdo
      document.body.style.overflow = 'auto';
      document.documentElement.style.height = '100vh';
      document.documentElement.style.maxHeight = '100vh';

      if (rootElement) {
        rootElement.style.height = '100vh';
        rootElement.style.maxHeight = '100vh';
        // ✅ CORREÇÃO: Permitir scroll interno removendo overflow hidden
        rootElement.style.overflow = 'auto';
      }

      // Cleanup: restaurar estilos originais quando sair da página
      return () => {
        document.body.style.height = originalBodyHeight;
        document.body.style.maxHeight = originalBodyMaxHeight;
        document.body.style.overflow = originalBodyOverflow;
        document.documentElement.style.height = originalHtmlHeight;
        document.documentElement.style.maxHeight = originalHtmlMaxHeight;

        if (rootElement) {
          rootElement.style.height = originalRootHeight || '';
          rootElement.style.maxHeight = originalRootMaxHeight || '';
          rootElement.style.overflow = originalRootOverflow || '';
        }
      };
    }
  }, [isMobile]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400 rounded-full"
        />
        <div className="mt-4 text-center">
          <p className="text-gray-600">Carregando resumo...</p>
        </div>
      </div>
    );
  }



  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800 overflow-hidden">
      <ConductMetaTags
        name={topicName}
        description={summary?.content}
        slug={topicSlug || ''}
        categoryName={categoryName}
        categorySlug={categorySlug}
      />

      <Header />

      {/* Header de Navegação - Full Width no Mobile apenas */}
      <div className="flex-shrink-0 w-full lg:hidden border-b border-gray-200/30 dark:border-gray-700/30 bg-gradient-to-r from-background/90 via-background/95 to-background/90 backdrop-blur-md shadow-sm">
        <div className="w-full px-4 py-3">
          <div className="flex items-center gap-3">
            <Link
              to={`/condutas-e-manejos/${categorySlug}`}
            >
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-xl hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 hover:scale-105">
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-semibold text-foreground truncate">
                {topicName}
              </h1>
            </div>

            {/* Tabs Mobile - Integrados no Header */}
            {isOptimizedFormat && optimizedSummary && (() => {
              // Verificar se há imagens globais no conteúdo
              const hasGlobalImages = () => {
                const conductsContent = optimizedSummary.conducts_content || '';
                const treatmentContent = optimizedSummary.treatment_content || '';

                // Procurar por tags de imagem no conteúdo
                const imageRegex = /<ImageWithCaption[^>]*\/>/g;
                const conductsImages = conductsContent.match(imageRegex) || [];
                const treatmentImages = treatmentContent.match(imageRegex) || [];

                return conductsImages.length > 0 || treatmentImages.length > 0;
              };

              return (
                <div className="lg:hidden">
                  <div className="flex bg-gradient-to-r from-gray-50 to-gray-50/80 dark:from-gray-800 dark:to-gray-800/80 rounded-xl p-0.5 gap-0.5 border border-gray-200/60 dark:border-gray-700/60 shadow-sm backdrop-blur-sm">
                    <button
                      onClick={() => {
                        if (activeTab === 'conducts') return;
                        const event = new CustomEvent('tabChange', { detail: 'conducts' });
                        window.dispatchEvent(event);
                      }}
                      className={`flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 transform hover:scale-105 ${
                        activeTab === 'conducts'
                          ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md cursor-default'
                          : 'hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-100/80 dark:hover:from-gray-700 dark:hover:to-gray-700/80 text-gray-700 dark:text-gray-200 cursor-pointer hover:shadow-sm'
                      }`}
                      disabled={activeTab === 'conducts'}
                    >
                      <span className="text-sm">📋</span>
                      <span className="hidden sm:inline">Condutas</span>
                    </button>
                    {optimizedSummary.has_treatment && (
                      <button
                        onClick={() => {
                          if (activeTab === 'treatment') return;
                          const event = new CustomEvent('tabChange', { detail: 'treatment' });
                          window.dispatchEvent(event);
                        }}
                        className={`flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 transform hover:scale-105 ${
                          activeTab === 'treatment'
                            ? 'bg-gradient-to-r from-green-600 to-green-500 text-white shadow-md cursor-default'
                            : 'hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-100/80 dark:hover:from-gray-700 dark:hover:to-gray-700/80 text-gray-700 dark:text-gray-200 cursor-pointer hover:shadow-sm'
                        }`}
                        disabled={activeTab === 'treatment'}
                      >
                        <span className="text-sm">💊</span>
                        <span className="hidden sm:inline">Prescrição</span>
                      </button>
                    )}
                    {hasGlobalImages() && (
                      <button
                        onClick={() => {
                          if (activeTab === 'images') return;
                          const event = new CustomEvent('tabChange', { detail: 'images' });
                          window.dispatchEvent(event);
                        }}
                        className={`flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 transform hover:scale-105 ${
                          activeTab === 'images'
                            ? 'bg-gradient-to-r from-purple-600 to-purple-500 text-white shadow-md cursor-default'
                            : 'hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-100/80 dark:hover:from-gray-700 dark:hover:to-gray-700/80 text-gray-700 dark:text-gray-200 cursor-pointer hover:shadow-sm'
                        }`}
                        disabled={activeTab === 'images'}
                      >
                        <span className="text-sm">🖼️</span>
                        <span className="hidden sm:inline">Imagens</span>
                      </button>
                    )}
                  </div>
                </div>
              );
            })()}

            {/* 🎯 DESKTOP: Botões de navegação centralizados (apenas para conteúdo otimizado) */}
            {isOptimizedFormat && optimizedSummary && (() => {
              // Verificar se há imagens globais no conteúdo
              const hasGlobalImages = () => {
                const conductsContent = optimizedSummary.conducts_content || '';
                const treatmentContent = optimizedSummary.treatment_content || '';

                // Procurar por tags de imagem no conteúdo
                const imageRegex = /<ImageWithCaption[^>]*\/>/g;
                const conductsImages = conductsContent.match(imageRegex) || [];
                const treatmentImages = treatmentContent.match(imageRegex) || [];

                return conductsImages.length > 0 || treatmentImages.length > 0;
              };

              return (
                <div className="hidden lg:flex items-center justify-start flex-1 ml-16">
                  <div className="flex bg-gray-50 dark:bg-gray-800 rounded-lg p-1 gap-1 border border-gray-200 dark:border-gray-700">
                    <button
                      onClick={() => {
                        if (activeTab === 'conducts') {
                          return;
                        }
                        const event = new CustomEvent('tabChange', { detail: 'conducts' });
                        window.dispatchEvent(event);
                      }}
                      className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                        activeTab === 'conducts'
                          ? 'bg-blue-600 text-white shadow-sm cursor-default'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer'
                      }`}
                      data-tab-trigger="conducts"
                      disabled={activeTab === 'conducts'}
                    >
                      <span>📋</span>
                      <span>Condutas</span>
                    </button>
                    {optimizedSummary.has_treatment && (
                      <button
                        onClick={() => {
                          if (activeTab === 'treatment') {
                            return;
                          }
                          const event = new CustomEvent('tabChange', { detail: 'treatment' });
                          window.dispatchEvent(event);
                        }}
                        className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                          activeTab === 'treatment'
                            ? 'bg-green-600 text-white shadow-sm cursor-default'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer'
                        }`}
                        data-tab-trigger="treatment"
                        disabled={activeTab === 'treatment'}
                      >
                        <span>💊</span>
                        <span>Prescrições</span>
                      </button>
                    )}
                    {/* 🔧 CORREÇÃO: Só mostrar botão de imagens se houver imagens */}
                    {hasGlobalImages() && (
                      <button
                        onClick={() => {
                          if (activeTab === 'images') {
                            return;
                          }
                          const event = new CustomEvent('tabChange', { detail: 'images' });
                          window.dispatchEvent(event);
                        }}
                        className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                          activeTab === 'images'
                            ? 'bg-purple-600 text-white shadow-sm cursor-default'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer'
                        }`}
                        data-tab-trigger="images"
                        disabled={activeTab === 'images'}
                      >
                        <span>🖼️</span>
                        <span>Imagens</span>
                      </button>
                    )}
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      </div>

      <main className="flex-1 w-full px-0 lg:container lg:mx-auto lg:px-4 pb-0">
        <div className="w-full lg:max-w-7xl lg:mx-auto h-full flex flex-col">

          {/* Header de Navegação - Desktop apenas (limitado) */}
          <div className="hidden lg:flex flex-shrink-0 px-4 pt-3 pb-2">
            <div className="w-full bg-gradient-to-r from-background/90 via-background/95 to-background/90 backdrop-blur-md shadow-sm border border-gray-200/30 dark:border-gray-700/30 rounded-2xl px-4 py-3">
            <div className="flex items-center gap-3 w-full">
              <Link
                to={`/condutas-e-manejos/${categorySlug}`}
                className="flex-shrink-0"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-xl hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 hover:scale-105"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </Link>

              <div className="flex-1 min-w-0">
                <h1 className="text-lg font-semibold text-foreground truncate">
                  {topicName}
                </h1>
              </div>

              {/* Tabs Desktop - Lógica Condicional */}
              {isOptimizedFormat && optimizedSummary && (() => {
                // Verificar se há imagens globais no conteúdo
                const hasGlobalImages = () => {
                  const conductsContent = optimizedSummary.conducts_content || '';
                  const treatmentContent = optimizedSummary.treatment_content || '';

                  // Procurar por tags de imagem no conteúdo
                  const imageRegex = /<ImageWithCaption[^>]*\/>/g;
                  const conductsImages = conductsContent.match(imageRegex) || [];
                  const treatmentImages = treatmentContent.match(imageRegex) || [];

                  return conductsImages.length > 0 || treatmentImages.length > 0;
                };

                return (
                  <div className="flex items-center justify-start flex-1 ml-16">
                    <div className="flex bg-gray-50 dark:bg-gray-800 rounded-lg p-1 gap-1 border border-gray-200 dark:border-gray-700">
                      {/* Aba Condutas - sempre presente */}
                      <button
                        onClick={() => {
                          if (activeTab === 'conducts') return;
                          const event = new CustomEvent('tabChange', { detail: 'conducts' });
                          window.dispatchEvent(event);
                        }}
                        className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                          activeTab === 'conducts'
                            ? 'bg-blue-600 text-white shadow-sm cursor-default'
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer'
                        }`}
                        data-tab-trigger="conducts"
                        disabled={activeTab === 'conducts'}
                      >
                        <span>📋</span>
                        <span>Condutas</span>
                      </button>

                      {/* Aba Prescrições - apenas se houver conteúdo */}
                      {optimizedSummary.has_treatment && (
                        <button
                          onClick={() => {
                            if (activeTab === 'treatment') return;
                            const event = new CustomEvent('tabChange', { detail: 'treatment' });
                            window.dispatchEvent(event);
                          }}
                          className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                            activeTab === 'treatment'
                              ? 'bg-green-600 text-white shadow-sm cursor-default'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer'
                          }`}
                          data-tab-trigger="treatment"
                          disabled={activeTab === 'treatment'}
                        >
                          <span>💊</span>
                          <span>Prescrições</span>
                        </button>
                      )}

                      {/* Aba Imagens - apenas se houver imagens globais */}
                      {hasGlobalImages() && (
                        <button
                          onClick={() => {
                            if (activeTab === 'images') return;
                            const event = new CustomEvent('tabChange', { detail: 'images' });
                            window.dispatchEvent(event);
                          }}
                          className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                            activeTab === 'images'
                              ? 'bg-purple-600 text-white shadow-sm cursor-default'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 cursor-pointer'
                          }`}
                          data-tab-trigger="images"
                          disabled={activeTab === 'images'}
                        >
                          <span>🖼️</span>
                          <span>Imagens</span>
                        </button>
                      )}
                    </div>
                  </div>
                );
              })()}
            </div>
            </div>
          </div>

          <div className="flex-1">
            {isOptimizedFormat && optimizedSummary ? (
              <OptimizedConductsView
                title={topicName}
                conductsContent={convertSectionsToMarkdown(optimizedSummary.conducts_content)}
                treatmentContent={convertSectionsToMarkdown(optimizedSummary.treatment_content)}
                hasTreatment={optimizedSummary.has_treatment}
              />
            ) : summary ? (
              <div className="h-full bg-white flex flex-col overflow-hidden">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="flex-1 overflow-y-auto px-4 lg:p-4 py-4"
                >
                  <div className="space-y-4">
              <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-lg border-blue-100 dark:border-slate-700">
                <Accordion type="multiple" className="space-y-4">
                  {summary.sections.map((section, index) => (
                    <AccordionItem
                      key={index}
                      value={`section-${index}`}
                      className="rounded-xl overflow-hidden border border-blue-100 dark:border-gray-700 bg-white/80 backdrop-blur-sm shadow-md transition-all hover:shadow-lg"
                    >
                      <AccordionTrigger
                        className="px-6 py-4 hover:bg-blue-50/50 data-[state=open]:bg-blue-50/80 dark:hover:bg-blue-900/20 transition-colors duration-300"
                      >
                        <div className="flex items-center gap-3 text-left">
                          <span className="flex items-center justify-center rounded-full bg-primary text-white p-2">
                            <FileText className="h-5 w-5" />
                          </span>
                          <div>
                            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                              {section.title}
                            </h2>
                            {section.subsections.length > 0 && (
                              <p className="text-xs text-gray-500 mt-1">
                                {section.subsections.length} {section.subsections.length === 1 ? 'subtópico' : 'subtópicos'}
                              </p>
                            )}
                          </div>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 pt-3 pb-6 bg-white dark:bg-slate-800">
                        <div>
                          {section.content && (
                            <>

                              <div
                                className="prose max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300"
                                dangerouslySetInnerHTML={{ __html: processContent(section.content, summary?.format_type as "standard" | "simple") }}
                              />
                            </>
                          )}

                          {section.subsections.length > 0 && (
                            <Accordion type="multiple" className="space-y-2 mt-4">
                              {section.subsections.map((subsection, subIndex) => (
                                <AccordionItem
                                  key={subIndex}
                                  value={`subsection-${index}-${subIndex}`}
                                  className="border border-blue-100 dark:border-slate-700 rounded-lg overflow-hidden"
                                >
                                  <AccordionTrigger className="px-6 py-3 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors">
                                    <div className="flex items-center gap-2 text-left">
                                      <span className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs">
                                        {index + 1}.{subIndex + 1}
                                      </span>
                                      <h3 className="text-lg font-medium text-blue-700 dark:text-blue-300">
                                        {subsection.title}
                                      </h3>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent className="px-6 pt-2 pb-4 bg-blue-50/30 dark:bg-blue-900/10">
                                    <div>
                                      <>

                                        <div
                                          className="prose max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300"
                                          dangerouslySetInnerHTML={{ __html: processContent(subsection.content, summary?.format_type as "standard" | "simple") }}
                                        />
                                      </>
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              ))}
                            </Accordion>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>

                <div className="px-6 py-8">
                  <ConductsFeedback
                    summaryId={summary.id}
                    summaryTitle={summary.title}
                  />
                </div>
              </Card>
                  </div>
                </motion.div>
              </div>
            ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center py-12"
            >
              <h3 className="text-xl font-medium text-gray-600 dark:text-gray-300">
                Nenhum resumo encontrado para este tópico
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mt-2">
                O conteúdo pode estar em desenvolvimento ou não publicado.
              </p>
            </motion.div>
          )}
          </div>
        </div>
      </main>

      <ImageViewer
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        imageUrl={selectedImage || ''}
        alt="Imagem do resumo"
      />


    </div>
  );
});

ConductsSummary.displayName = 'ConductsSummary';


