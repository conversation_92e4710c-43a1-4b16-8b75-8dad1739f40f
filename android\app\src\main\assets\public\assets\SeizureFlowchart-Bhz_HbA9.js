import{j as e}from"./radix-core-6kBL75b5.js";import{L as s}from"./router-BAzpOxbo.js";import{c as a,an as i,a5 as r,R as l,Y as t,aJ as c,aM as n,B as d,aa as o,a7 as m}from"./index-CFnD44mG.js";import x from"./Footer-BQ6Dqsd-.js";import{r as h}from"./critical-DVX9Inzy.js";import{S as j}from"./scale-DoQjTjLO.js";import{S as p}from"./stethoscope-DLVdj0oT.js";import{S as g}from"./syringe-BNnDekth.js";import{A as u}from"./FeedbackTrigger-ot4XwGkJ.js";import{P as N}from"./play-Dt4OVlzJ.js";import{F as b,a as v}from"./flowchartSEOData-1N0kBY5B.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-X0NDZon3.js";import"./rocket-BOvZdSbI.js";import"./target-BX0FTOgV.js";import"./zap-DhWHOaH4.js";import"./book-open-CpPiu5Sl.js";import"./star-ShS_gnKj.js";import"./circle-help-B8etwa8R.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=a("Hospital",[["path",{d:"M12 6v4",key:"16clxf"}],["path",{d:"M14 14h-4",key:"esezmu"}],["path",{d:"M14 18h-4",key:"16mqa2"}],["path",{d:"M14 8h-4",key:"z8ypaz"}],["path",{d:"M18 12h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-9a2 2 0 0 1 2-2h2",key:"b1k337"}],["path",{d:"M18 22V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v18",key:"16g51d"}]]),y=({value:s,onChange:a,onCommit:l})=>{const[t,c]=h.useState(s.toString());return h.useEffect((()=>{c(s.toString())}),[s]),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(i,{htmlFor:"weight",className:"text-lg font-medium text-red-700 flex items-center gap-2",children:[e.jsx(j,{className:"h-5 w-5"}),"Peso do Paciente"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{id:"weight",type:"number",min:0,max:100,value:t,onChange:e=>{const s=e.target.value;c(s);const i=parseFloat(s);if(!isNaN(i)){const e=Math.min(Math.max(0,i),100);a(e),l(e)}},onBlur:()=>{const e=parseFloat(t);isNaN(e)||e<0?(c("0"),a(0),l(0)):e>100?(c("100"),a(100),l(100)):(c(e.toString()),a(e),l(e))},className:"flex-1 bg-white/50 border-red-200 focus:border-red-400 transition-colors text-center text-lg",placeholder:"Digite o peso"}),e.jsx("span",{className:"text-lg font-medium text-red-700 min-w-[3rem]",children:"kg"})]})]})},k=({weight:s})=>{const a=(2.5*s).toFixed(1),i=.03*s,r=Math.min(i,1).toFixed(2);return e.jsxs("div",{className:"space-y-6",children:[e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-yellow-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(p,{className:"h-6 w-6 text-yellow-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-yellow-800",children:"1.1 Proteção da Via Aérea"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsx("li",{children:"Administração de oxigênio a 100% (máscara facial ou cateter nasal)"}),e.jsx("li",{children:"Garantir boa posição do paciente (de lado) para prevenir aspiração"}),e.jsx("li",{children:"Monitoramento contínuo de oximetria de pulso"})]})]})]})}),e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-blue-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(t,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-blue-800",children:"1.2 Coleta de Exames"}),e.jsxs("div",{className:"space-y-2 text-gray-700",children:[e.jsx("p",{className:"font-medium",children:"Exames laboratoriais básicos:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[e.jsx("li",{children:"Hemograma completo"}),e.jsx("li",{children:"Dosagem de glicose e eletrólitos (incluindo magnésio e fósforo)"}),e.jsx("li",{children:"Função hepática e renal"}),e.jsx("li",{children:"Hemocultura ou urocultura, se necessário"})]})]})]})]})}),e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-green-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(g,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-green-800",children:"1.3 Controle de Glicemia"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsx("li",{children:"Avaliar glicemia capilar à beira do leito"}),e.jsxs("li",{children:["Se hipoglicemia: Administrar soro glicosado 10% ",a," mL IV"]}),e.jsxs("li",{children:["Se não for possível acesso periférico: Glucagon ",r," mg IM ou SC (máximo 1 mg)"]})]})]})]})})]})},w=({weight:s})=>{const a=(.2*s).toFixed(1),i=(.2*s).toFixed(1),r=Math.min(parseFloat(a),5).toFixed(1),c=(.2*s).toFixed(1),n=(.5*s).toFixed(1);return e.jsxs("div",{className:"space-y-6",children:[e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-blue-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(g,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-blue-800",children:"Benzodiazepínicos (1ª Linha)"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsxs("li",{children:["Diazepam: ",r," mg IV (máx. 5 mg) por 1 minuto"]}),e.jsxs("li",{children:["Se não houver acesso venoso:",e.jsxs("ul",{className:"list-disc list-inside ml-4 mt-1",children:[e.jsxs("li",{children:["Midazolam: ",i," mg IM ou nasal"]}),e.jsxs("li",{children:["Diazepam: ",c,"–",n," mg via retal"]})]})]})]})]})]})}),e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-yellow-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(t,{className:"h-6 w-6 text-yellow-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-yellow-800",children:"Avaliação da Resposta"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsx("li",{children:"Reavaliar após 5 minutos"}),e.jsx("li",{children:"Se a crise não cessar, repetir a dose do Benzodiazepínico"})]})]})]})})]})},M=({weight:s})=>{const a=Math.round(25*s),i=Math.round(30*s);return e.jsxs("div",{className:"space-y-6",children:[e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-purple-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(g,{className:"h-6 w-6 text-purple-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-purple-800",children:"Fenitoína ou Fosfenitoína"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsxs("li",{children:["Fenitoína: ",a,"-",i," mg IV (máx. 50 mg/min)"]}),e.jsxs("li",{children:["Fosfenitoína: ",a,"-",i," mg IV (máx. 150 mg/min)"]})]})]})]})}),e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-red-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(t,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-red-800",children:"Atenção"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsx("li",{children:"Infusão rápida pode causar arritmias cardíacas e hipotensão"}),e.jsx("li",{children:"Em pacientes em uso crônico de Fenitoína, prefira Fenobarbital"})]})]})]})})]})},C=({weight:s})=>{const a=Math.round(20*s),i=Math.round(30*s),r=Math.round(40*s),t=Math.round(40*s);return e.jsx("div",{className:"space-y-6",children:e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-red-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(g,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-red-800",children:"Medicações de Terceira Linha"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsxs("li",{children:["Fenobarbital: ",a,"-",i," mg IV"]}),e.jsxs("li",{children:["Levetiracetam: ",r," mg IV (infusão a 5 mg/min)"]}),e.jsxs("li",{children:["Ácido Valpróico: ",t," mg IV (infusão a 5 mg/min)"]})]})]})]})})})},F=({weight:s})=>{const a=(.2*s).toFixed(1),i=(.1*s).toFixed(1),r=Math.round(1*s),t=Math.round(3*s),n=Math.round(3*s),d=Math.round(5*s),o=Math.round(1*s),m=Math.round(2*s),x=Math.round(10*s),h=Math.round(2*s),j=Math.round(3*s),g=Math.round(4*s),u=Math.round(10*s);return e.jsxs("div",{className:"space-y-6",children:[e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-indigo-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(f,{className:"h-6 w-6 text-indigo-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-indigo-800",children:"Drogas de Monitoramento Contínuo"}),e.jsxs("ul",{className:"list-disc list-inside space-y-3 text-gray-700",children:[e.jsxs("li",{children:["Midazolam:",e.jsxs("ul",{className:"list-disc list-inside ml-4 mt-1",children:[e.jsxs("li",{children:["Dose de ataque: ",a," mg IV"]}),e.jsxs("li",{children:["Infusão contínua: ",i," mg/kg/h"]})]})]}),e.jsxs("li",{children:["Tiopental:",e.jsxs("ul",{className:"list-disc list-inside ml-4 mt-1",children:[e.jsxs("li",{children:["Dose de ataque: ",r,"-",t," mg IV"]}),e.jsxs("li",{children:["Infusão contínua: ",n,"-",d," mg/kg/h"]})]})]}),e.jsxs("li",{children:["Propofol:",e.jsxs("ul",{className:"list-disc list-inside ml-4 mt-1",children:[e.jsxs("li",{children:["Dose inicial: ",o," mg IV"]}),e.jsxs("li",{children:["Manutenção: ",m,"-",x," ","mg/kg/h"]})]})]}),e.jsxs("li",{children:["Lidocaína:",e.jsxs("ul",{className:"list-disc list-inside ml-4 mt-1",children:[e.jsxs("li",{children:["Dose inicial: ",h,"-",j," mg IV"]}),e.jsxs("li",{children:["Manutenção: ",g,"-",u," ","mg/kg/h"]})]})]})]})]})]})}),e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-blue-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(p,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-blue-800",children:"Suporte Avançado"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsx("li",{children:"Monitorização contínua de sinais vitais"}),e.jsx("li",{children:"Controle ventilatório e hemodinâmico"})]})]})]})}),e.jsx(l,{className:"p-6 bg-white/80 backdrop-blur-sm border border-green-200",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(c,{className:"h-6 w-6 text-green-600"})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-semibold text-lg text-green-800",children:"Investigação Complementar"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700",children:[e.jsx("li",{children:"EEG (eletroencefalograma)"}),e.jsx("li",{children:"Ressonância magnética (RM) ou tomografia computadorizada (TC) de crânio"}),e.jsx("li",{children:"Punção lombar, caso não haja contraindicações (como hipertensão intracraniana)"})]})]})]})})]})},z=()=>{const[s,a]=h.useState(20),[i,r]=h.useState("intro"),c=()=>{switch(i){case"intro":r("input");break;case"initial":r("benzo");break;case"benzo":r("second");break;case"second":r("status");break;case"status":r("icu")}};return e.jsx("div",{className:n.gradientBackground("min-h-screen"),children:e.jsx("div",{className:"max-w-3xl mx-auto p-6",children:e.jsxs("div",{className:n.card("bg-white dark:bg-slate-800 p-6"),children:[(()=>{switch(i){case"intro":return e.jsxs("div",{className:"space-y-6 animate-slide-in-up",children:[e.jsx(l,{className:n.gradientCard("orange","p-6"),children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"mt-1",children:e.jsx(t,{className:"h-6 w-6 text-orange-600 dark:text-orange-400"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:"Este fluxograma foi projetado para orientar o manejo de crises convulsivas prolongadas, especialmente em situações críticas na emergência pediátrica, onde a crise dura mais de 5 minutos ou há crises recorrentes sem recuperação da consciência."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-lg text-orange-800 dark:text-orange-300",children:"Quando usar este fluxograma:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300",children:[e.jsx("li",{children:"Crises com duração superior a 5 minutos."}),e.jsx("li",{children:"Pacientes que apresentam múltiplas crises sem recuperação completa entre elas (possível estado de mal epiléptico)."}),e.jsx("li",{children:"Casos de crise focal ou generalizada (com manifestações motoras ou não)."})]})]})]})]})}),e.jsxs(d,{onClick:c,className:"w-full flex items-center justify-center gap-2 bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700",children:["Iniciar Fluxograma",e.jsx(N,{className:"h-4 w-4"})]})]});case"input":return e.jsxs("div",{className:"space-y-6 animate-slide-in-up",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-800 dark:text-gray-100",children:"Manejo de Crise Convulsiva"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:"Insira o peso do paciente para iniciar o fluxograma"})]}),e.jsx(y,{value:s,onChange:a,onCommit:()=>{}}),e.jsxs(d,{onClick:()=>{r("initial")},className:"w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700",children:["Prosseguir com o Fluxograma",e.jsx(u,{className:"h-4 w-4"})]})]});case"initial":return e.jsxs("div",{className:"space-y-6",children:[e.jsx(k,{weight:s}),e.jsxs(d,{onClick:c,className:"w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700",children:["Crise persiste após 5 minutos?",e.jsx(u,{className:"h-4 w-4"})]})]});case"benzo":return e.jsxs("div",{className:"space-y-6",children:[e.jsx(w,{weight:s}),e.jsxs(d,{onClick:c,className:"w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700",children:["Crise persiste após 10 minutos?",e.jsx(u,{className:"h-4 w-4"})]})]});case"second":return e.jsxs("div",{className:"space-y-6",children:[e.jsx(M,{weight:s}),e.jsxs(d,{onClick:c,className:"w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700",children:["Crise persiste após 60 minutos?",e.jsx(u,{className:"h-4 w-4"})]})]});case"status":return e.jsxs("div",{className:"space-y-6",children:[e.jsx(C,{weight:s}),e.jsxs(d,{onClick:c,className:"w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700",children:["Necessário cuidados em UTI?",e.jsx(u,{className:"h-4 w-4"})]})]});case"icu":return e.jsxs("div",{className:"space-y-6",children:[e.jsx(F,{weight:s}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 text-center mt-8 italic",children:'Fonte: Adaptado de "Manejo das Crises Convulsivas na Emergência Pediátrica", Juliana Beirão de Almeida Guaragna et al.'})]});default:return null}})(),"intro"!==i&&"icu"!==i&&e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 text-center mt-8 italic",children:'Fonte: Adaptado de "Manejo das Crises Convulsivas na Emergência Pediátrica", Juliana Beirão de Almeida Guaragna et al.'})]})})})},I=()=>{const a=v.seizure;return e.jsxs("div",{className:n.gradientBackground("min-h-screen flex flex-col from-yellow-100 via-white to-orange-50 dark:from-yellow-950 dark:via-slate-900 dark:to-orange-950"),children:[e.jsx(b,{...a}),e.jsx(o,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs(s,{to:"/flowcharts",className:"inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors",children:[e.jsx(m,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Fluxogramas"})]}),e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-orange-100 dark:bg-orange-900/30",children:e.jsx(c,{className:"w-8 h-8 text-orange-600 dark:text-orange-400"})}),e.jsx("h1",{className:n.gradientHeading("text-4xl font-bold from-orange-600 to-amber-600 dark:from-orange-400 dark:to-amber-400"),children:"Manejo de Crise Convulsiva"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Fluxograma interativo para manejo de crises convulsivas em pediatria"})]}),e.jsx(z,{})]})}),e.jsx(x,{})]})};export{I as default};
