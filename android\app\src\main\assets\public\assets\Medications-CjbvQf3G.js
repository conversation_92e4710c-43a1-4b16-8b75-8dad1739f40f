import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-DCl9SGFB.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-DwykrzWu.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-CEErUVD6.js";import"./FeedbackTrigger-Cce_pfRl.js";import"./rocket-BVKdk9NC.js";import"./target-Bq7BybE4.js";import"./zap-sktuObTW.js";import"./book-open-vsXyzIQN.js";import"./star-CVUfjIpQ.js";import"./circle-help-DkV0sebI.js";import"./instagram-BgC8q_0n.js";import"./collapsible-B6HfSnGs.js";import"./accordion-B6pduOb3.js";import"./PatientInfoSection-B0XzckOx.js";import"./scale-ChsfrFo_.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-hvRXSQ6q.js";import"./user-JQIHs777.js";import"./DosageDisplay-ChdMXWCG.js";import"./alert-47AuHV0Y.js";import"./plus-ahDLNnho.js";import"./lightbulb-CeJxLvRY.js";import"./external-link-YG2Q46QJ.js";import"./stethoscope-BiILX4Ik.js";import"./syringe-D5LgfHhK.js";import"./wind-BbVQggcd.js";import"./bug-rUAcu1O2.js";import"./chevron-left-UiG5RJWe.js";import"./house-DCEvqY-i.js";function t(){return r.jsx(o,{})}export{t as default};
