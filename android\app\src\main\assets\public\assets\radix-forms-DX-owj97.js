import{r as e,a as t}from"./critical-DVX9Inzy.js";import{j as n,P as r,b as o,h as a,u as s,c as i,k as l,f as c,d,g as u,e as p,l as f,a as h,n as v,m,R as w,S as b,F as y}from"./radix-core-6kBL75b5.js";import{b as g,u as x,d as C,I as S,e as k,c as E,A as R,C as j,R as P,a as D}from"./radix-interactive-DJo-0Sg_.js";import{c as _,V as I}from"./radix-toast-1_gbKn9f.js";var T=e.forwardRef(((e,t)=>n.jsx(r.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}})));T.displayName="Label";var L=T;function N(t){const n=e.useRef({value:t,previous:t});return e.useMemo((()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous)),[t])}var O="Radio",[M,A]=a(O),[H,V]=M(O),B=e.forwardRef(((t,o)=>{const{__scopeRadio:a,name:l,checked:c=!1,required:d,disabled:u,value:p="on",onCheck:f,form:h,...v}=t,[m,w]=e.useState(null),b=s(o,(e=>w(e))),y=e.useRef(!1),g=!m||h||!!m.closest("form");return n.jsxs(H,{scope:a,checked:c,disabled:u,children:[n.jsx(r.button,{type:"button",role:"radio","aria-checked":c,"data-state":K(c),"data-disabled":u?"":void 0,disabled:u,value:p,...v,ref:b,onClick:i(t.onClick,(e=>{c||f?.(),g&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())}))}),g&&n.jsx(q,{control:m,bubbles:!y.current,name:l,value:p,checked:c,required:d,disabled:u,form:h,style:{transform:"translateX(-100%)"}})]})}));B.displayName=O;var F="RadioIndicator",W=e.forwardRef(((e,t)=>{const{__scopeRadio:o,forceMount:a,...s}=e,i=V(F,o);return n.jsx(l,{present:a||i.checked,children:n.jsx(r.span,{"data-state":K(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:t})})}));W.displayName=F;var q=t=>{const{control:r,checked:o,bubbles:a=!0,...s}=t,i=e.useRef(null),l=N(o),c=k(r);return e.useEffect((()=>{const e=i.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(l!==o&&n){const t=new Event("click",{bubbles:a});n.call(e,o),e.dispatchEvent(t)}}),[l,o,a]),n.jsx("input",{type:"radio","aria-hidden":!0,defaultChecked:o,...s,tabIndex:-1,ref:i,style:{...t.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function K(e){return e?"checked":"unchecked"}var z=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],U="RadioGroup",[G,X]=a(U,[g,A]),Y=g(),Z=A(),[$,J]=G(U),Q=e.forwardRef(((e,t)=>{const{__scopeRadioGroup:a,name:s,defaultValue:i,value:l,required:c=!1,disabled:d=!1,orientation:u,dir:p,loop:f=!0,onValueChange:h,...v}=e,m=Y(a),w=x(p),[b,y]=o({prop:l,defaultProp:i,onChange:h});return n.jsx($,{scope:a,name:s,required:c,disabled:d,value:b,onValueChange:y,children:n.jsx(C,{asChild:!0,...m,orientation:u,dir:w,loop:f,children:n.jsx(r.div,{role:"radiogroup","aria-required":c,"aria-orientation":u,"data-disabled":d?"":void 0,dir:w,...v,ref:t})})})}));Q.displayName=U;var ee="RadioGroupItem",te=e.forwardRef(((t,r)=>{const{__scopeRadioGroup:o,disabled:a,...l}=t,c=J(ee,o),d=c.disabled||a,u=Y(o),p=Z(o),f=e.useRef(null),h=s(r,f),v=c.value===l.value,m=e.useRef(!1);return e.useEffect((()=>{const e=e=>{z.includes(e.key)&&(m.current=!0)},t=()=>m.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}}),[]),n.jsx(S,{asChild:!0,...u,focusable:!d,active:v,children:n.jsx(B,{disabled:d,required:c.required,checked:v,...p,...l,name:c.name,ref:h,onCheck:()=>c.onValueChange(l.value),onKeyDown:i((e=>{"Enter"===e.key&&e.preventDefault()})),onFocus:i(l.onFocus,(()=>{m.current&&f.current?.click()}))})})}));te.displayName=ee;var ne=e.forwardRef(((e,t)=>{const{__scopeRadioGroup:r,...o}=e,a=Z(r);return n.jsx(W,{...a,...o,ref:t})}));ne.displayName="RadioGroupIndicator";var re=Q,oe=te,ae=ne,se="Checkbox",[ie,le]=a(se),[ce,de]=ie(se),ue=e.forwardRef(((t,a)=>{const{__scopeCheckbox:l,name:c,checked:d,defaultChecked:u,required:p,disabled:f,value:h="on",onCheckedChange:v,form:m,...w}=t,[b,y]=e.useState(null),g=s(a,(e=>y(e))),x=e.useRef(!1),C=!b||m||!!b.closest("form"),[S=!1,k]=o({prop:d,defaultProp:u,onChange:v}),E=e.useRef(S);return e.useEffect((()=>{const e=b?.form;if(e){const t=()=>k(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}}),[b,k]),n.jsxs(ce,{scope:l,state:S,disabled:f,children:[n.jsx(r.button,{type:"button",role:"checkbox","aria-checked":ve(S)?"mixed":S,"aria-required":p,"data-state":me(S),"data-disabled":f?"":void 0,disabled:f,value:h,...w,ref:g,onKeyDown:i(t.onKeyDown,(e=>{"Enter"===e.key&&e.preventDefault()})),onClick:i(t.onClick,(e=>{k((e=>!!ve(e)||!e)),C&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())}))}),C&&n.jsx(he,{control:b,bubbles:!x.current,name:c,value:h,checked:S,required:p,disabled:f,form:m,style:{transform:"translateX(-100%)"},defaultChecked:!ve(u)&&u})]})}));ue.displayName=se;var pe="CheckboxIndicator",fe=e.forwardRef(((e,t)=>{const{__scopeCheckbox:o,forceMount:a,...s}=e,i=de(pe,o);return n.jsx(l,{present:a||ve(i.state)||!0===i.state,children:n.jsx(r.span,{"data-state":me(i.state),"data-disabled":i.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})}));fe.displayName=pe;var he=t=>{const{control:r,checked:o,bubbles:a=!0,defaultChecked:s,...i}=t,l=e.useRef(null),c=N(o),d=k(r);e.useEffect((()=>{const e=l.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(c!==o&&n){const t=new Event("click",{bubbles:a});e.indeterminate=ve(o),n.call(e,!ve(o)&&o),e.dispatchEvent(t)}}),[c,o,a]);const u=e.useRef(!ve(o)&&o);return n.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s??u.current,...i,tabIndex:-1,ref:l,style:{...t.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function ve(e){return"indeterminate"===e}function me(e){return ve(e)?"indeterminate":e?"checked":"unchecked"}var we=ue,be=fe;function ye(e,[t,n]){return Math.min(n,Math.max(t,e))}var ge,xe="dismissableLayer.update",Ce=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Se=e.forwardRef(((t,o)=>{const{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:p,onInteractOutside:f,onDismiss:h,...v}=t,m=e.useContext(Ce),[w,b]=e.useState(null),y=w?.ownerDocument??globalThis?.document,[,g]=e.useState({}),x=s(o,(e=>b(e))),C=Array.from(m.layers),[S]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),k=C.indexOf(S),E=w?C.indexOf(w):-1,R=m.layersWithOutsidePointerEventsDisabled.size>0,j=E>=k,P=function(t,n=globalThis?.document){const r=d(t),o=e.useRef(!1),a=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){Ee("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...m.branches].some((e=>e.contains(t)));j&&!n&&(u?.(e),f?.(e),e.defaultPrevented||h?.())}),y),D=function(t,n=globalThis?.document){const r=d(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{e.target&&!o.current&&Ee("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...m.branches].some((e=>e.contains(t)))||(p?.(e),f?.(e),e.defaultPrevented||h?.())}),y);return c((e=>{E===m.layers.size-1&&(l?.(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))}),y),e.useEffect((()=>{if(w)return a&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(ge=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(w)),m.layers.add(w),ke(),()=>{a&&1===m.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=ge)}}),[w,y,a,m]),e.useEffect((()=>()=>{w&&(m.layers.delete(w),m.layersWithOutsidePointerEventsDisabled.delete(w),ke())}),[w,m]),e.useEffect((()=>{const e=()=>g({});return document.addEventListener(xe,e),()=>document.removeEventListener(xe,e)}),[]),n.jsx(r.div,{...v,ref:x,style:{pointerEvents:R?j?"auto":"none":void 0,...t.style},onFocusCapture:i(t.onFocusCapture,D.onFocusCapture),onBlurCapture:i(t.onBlurCapture,D.onBlurCapture),onPointerDownCapture:i(t.onPointerDownCapture,P.onPointerDownCapture)})}));function ke(){const e=new CustomEvent(xe);document.dispatchEvent(e)}function Ee(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?u(o,a):o.dispatchEvent(a)}Se.displayName="DismissableLayer",e.forwardRef(((t,o)=>{const a=e.useContext(Ce),i=e.useRef(null),l=s(o,i);return e.useEffect((()=>{const e=i.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}}),[a.branches]),n.jsx(r.div,{...t,ref:l})})).displayName="DismissableLayerBranch";var Re=[" ","Enter","ArrowUp","ArrowDown"],je=[" ","Enter"],Pe="Select",[De,_e,Ie]=_(Pe),[Te,Le]=a(Pe,[Ie,E]),Ne=E(),[Oe,Me]=Te(Pe),[Ae,He]=Te(Pe),Ve=t=>{const{__scopeSelect:r,children:a,open:s,defaultOpen:i,onOpenChange:l,value:c,defaultValue:d,onValueChange:u,dir:p,name:f,autoComplete:v,disabled:m,required:w,form:b}=t,y=Ne(r),[g,C]=e.useState(null),[S,k]=e.useState(null),[E,R]=e.useState(!1),j=x(p),[D=!1,_]=o({prop:s,defaultProp:i,onChange:l}),[I,T]=o({prop:c,defaultProp:d,onChange:u}),L=e.useRef(null),N=!g||b||!!g.closest("form"),[O,M]=e.useState(new Set),A=Array.from(O).map((e=>e.props.value)).join(";");return n.jsx(P,{...y,children:n.jsxs(Oe,{required:w,scope:r,trigger:g,onTriggerChange:C,valueNode:S,onValueNodeChange:k,valueNodeHasChildren:E,onValueNodeHasChildrenChange:R,contentId:h(),value:I,onValueChange:T,open:D,onOpenChange:_,dir:j,triggerPointerDownPosRef:L,disabled:m,children:[n.jsx(De.Provider,{scope:r,children:n.jsx(Ae,{scope:t.__scopeSelect,onNativeOptionAdd:e.useCallback((e=>{M((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:e.useCallback((e=>{M((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:a})}),N?n.jsxs(Rt,{"aria-hidden":!0,required:w,tabIndex:-1,name:f,autoComplete:v,value:I,onChange:e=>T(e.target.value),disabled:m,form:b,children:[void 0===I?n.jsx("option",{value:""}):null,Array.from(O)]},A):null]})})};Ve.displayName=Pe;var Be="SelectTrigger",Fe=e.forwardRef(((t,o)=>{const{__scopeSelect:a,disabled:l=!1,...c}=t,d=Ne(a),u=Me(Be,a),p=u.disabled||l,f=s(o,u.onTriggerChange),h=_e(a),v=e.useRef("touch"),[m,w,b]=jt((e=>{const t=h().filter((e=>!e.disabled)),n=t.find((e=>e.value===u.value)),r=Pt(t,e,n);void 0!==r&&u.onValueChange(r.value)})),y=e=>{p||(u.onOpenChange(!0),b()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return n.jsx(R,{asChild:!0,...d,children:n.jsx(r.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:p,"data-disabled":p?"":void 0,"data-placeholder":Et(u.value)?"":void 0,...c,ref:f,onClick:i(c.onClick,(e=>{e.currentTarget.focus(),"mouse"!==v.current&&y(e)})),onPointerDown:i(c.onPointerDown,(e=>{v.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())})),onKeyDown:i(c.onKeyDown,(e=>{const t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||w(e.key),t&&" "===e.key||Re.includes(e.key)&&(y(),e.preventDefault())}))})})}));Fe.displayName=Be;var We="SelectValue",qe=e.forwardRef(((e,t)=>{const{__scopeSelect:o,className:a,style:i,children:l,placeholder:c="",...d}=e,u=Me(We,o),{onValueNodeHasChildrenChange:f}=u,h=void 0!==l,v=s(t,u.onValueNodeChange);return p((()=>{f(h)}),[f,h]),n.jsx(r.span,{...d,ref:v,style:{pointerEvents:"none"},children:Et(u.value)?n.jsx(n.Fragment,{children:c}):l})}));qe.displayName=We;var Ke=e.forwardRef(((e,t)=>{const{__scopeSelect:o,children:a,...s}=e;return n.jsx(r.span,{"aria-hidden":!0,...s,ref:t,children:a||"▼"})}));Ke.displayName="SelectIcon";var ze=e=>n.jsx(f,{asChild:!0,...e});ze.displayName="SelectPortal";var Ue="SelectContent",Ge=e.forwardRef(((r,o)=>{const a=Me(Ue,r.__scopeSelect),[s,i]=e.useState();if(p((()=>{i(new DocumentFragment)}),[]),!a.open){const e=s;return e?t.createPortal(n.jsx(Ye,{scope:r.__scopeSelect,children:n.jsx(De.Slot,{scope:r.__scopeSelect,children:n.jsx("div",{children:r.children})})}),e):null}return n.jsx($e,{...r,ref:o})}));Ge.displayName=Ue;var Xe=10,[Ye,Ze]=Te(Ue),$e=e.forwardRef(((t,r)=>{const{__scopeSelect:o,position:a="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:c,onPointerDownOutside:d,side:u,sideOffset:p,align:f,alignOffset:h,arrowPadding:g,collisionBoundary:x,collisionPadding:C,sticky:S,hideWhenDetached:k,avoidCollisions:E,...R}=t,j=Me(Ue,o),[P,D]=e.useState(null),[_,I]=e.useState(null),T=s(r,(e=>D(e))),[L,N]=e.useState(null),[O,M]=e.useState(null),A=_e(o),[H,V]=e.useState(!1),B=e.useRef(!1);e.useEffect((()=>{if(P)return v(P)}),[P]),m();const F=e.useCallback((e=>{const[t,...n]=A().map((e=>e.ref.current)),[r]=n.slice(-1),o=document.activeElement;for(const a of e){if(a===o)return;if(a?.scrollIntoView({block:"nearest"}),a===t&&_&&(_.scrollTop=0),a===r&&_&&(_.scrollTop=_.scrollHeight),a?.focus(),document.activeElement!==o)return}}),[A,_]),W=e.useCallback((()=>F([L,P])),[F,L,P]);e.useEffect((()=>{H&&W()}),[H,W]);const{onOpenChange:q,triggerPointerDownPosRef:K}=j;e.useEffect((()=>{if(P){let e={x:0,y:0};const t=t=>{e={x:Math.abs(Math.round(t.pageX)-(K.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(K.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():P.contains(n.target)||q(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[P,q,K]),e.useEffect((()=>{const e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[q]);const[z,U]=jt((e=>{const t=A().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=Pt(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),G=e.useCallback(((e,t,n)=>{const r=!B.current&&!n;(void 0!==j.value&&j.value===t||r)&&(N(e),r&&(B.current=!0))}),[j.value]),X=e.useCallback((()=>P?.focus()),[P]),Y=e.useCallback(((e,t,n)=>{const r=!B.current&&!n;(void 0!==j.value&&j.value===t||r)&&M(e)}),[j.value]),Z="popper"===a?Qe:Je,$=Z===Qe?{side:u,sideOffset:p,align:f,alignOffset:h,arrowPadding:g,collisionBoundary:x,collisionPadding:C,sticky:S,hideWhenDetached:k,avoidCollisions:E}:{};return n.jsx(Ye,{scope:o,content:P,viewport:_,onViewportChange:I,itemRefCallback:G,selectedItem:L,onItemLeave:X,itemTextRefCallback:Y,focusSelectedItem:W,selectedItemText:O,position:a,isPositioned:H,searchRef:z,children:n.jsx(w,{as:b,allowPinchZoom:!0,children:n.jsx(y,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:i(l,(e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()})),children:n.jsx(Se,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:n.jsx(Z,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...R,...$,onPlaced:()=>V(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:i(R.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>F(t))),e.preventDefault()}}))})})})})})}));$e.displayName="SelectContentImpl";var Je=e.forwardRef(((t,o)=>{const{__scopeSelect:a,onPlaced:i,...l}=t,c=Me(Ue,a),d=Ze(Ue,a),[u,f]=e.useState(null),[h,v]=e.useState(null),m=s(o,(e=>v(e))),w=_e(a),b=e.useRef(!1),y=e.useRef(!0),{viewport:g,selectedItem:x,selectedItemText:C,focusSelectedItem:S}=d,k=e.useCallback((()=>{if(c.trigger&&c.valueNode&&u&&h&&g&&x&&C){const e=c.trigger.getBoundingClientRect(),t=h.getBoundingClientRect(),n=c.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==c.dir){const o=r.left-t.left,a=n.left-o,s=e.left-a,i=e.width+s,l=Math.max(i,t.width),c=window.innerWidth-Xe,d=ye(a,[Xe,Math.max(Xe,c-l)]);u.style.minWidth=i+"px",u.style.left=d+"px"}else{const o=t.right-r.right,a=window.innerWidth-n.right-o,s=window.innerWidth-e.right-a,i=e.width+s,l=Math.max(i,t.width),c=window.innerWidth-Xe,d=ye(a,[Xe,Math.max(Xe,c-l)]);u.style.minWidth=i+"px",u.style.right=d+"px"}const o=w(),a=window.innerHeight-2*Xe,s=g.scrollHeight,l=window.getComputedStyle(h),d=parseInt(l.borderTopWidth,10),p=parseInt(l.paddingTop,10),f=parseInt(l.borderBottomWidth,10),v=d+p+s+parseInt(l.paddingBottom,10)+f,m=Math.min(5*x.offsetHeight,v),y=window.getComputedStyle(g),S=parseInt(y.paddingTop,10),k=parseInt(y.paddingBottom,10),E=e.top+e.height/2-Xe,R=a-E,j=x.offsetHeight/2,P=d+p+(x.offsetTop+j),D=v-P;if(P<=E){const e=o.length>0&&x===o[o.length-1].ref.current;u.style.bottom="0px";const t=h.clientHeight-g.offsetTop-g.offsetHeight,n=P+Math.max(R,j+(e?k:0)+t+f);u.style.height=n+"px"}else{const e=o.length>0&&x===o[0].ref.current;u.style.top="0px";const t=Math.max(E,d+g.offsetTop+(e?S:0)+j)+D;u.style.height=t+"px",g.scrollTop=P-E+g.offsetTop}u.style.margin=`${Xe}px 0`,u.style.minHeight=m+"px",u.style.maxHeight=a+"px",i?.(),requestAnimationFrame((()=>b.current=!0))}}),[w,c.trigger,c.valueNode,u,h,g,x,C,c.dir,i]);p((()=>k()),[k]);const[E,R]=e.useState();p((()=>{h&&R(window.getComputedStyle(h).zIndex)}),[h]);const j=e.useCallback((e=>{e&&!0===y.current&&(k(),S?.(),y.current=!1)}),[k,S]);return n.jsx(et,{scope:a,contentWrapper:u,shouldExpandOnScrollRef:b,onScrollButtonChange:j,children:n.jsx("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:n.jsx(r.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})}));Je.displayName="SelectItemAlignedPosition";var Qe=e.forwardRef(((e,t)=>{const{__scopeSelect:r,align:o="start",collisionPadding:a=Xe,...s}=e,i=Ne(r);return n.jsx(j,{...i,...s,ref:t,align:o,collisionPadding:a,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));Qe.displayName="SelectPopperPosition";var[et,tt]=Te(Ue,{}),nt="SelectViewport",rt=e.forwardRef(((t,o)=>{const{__scopeSelect:a,nonce:l,...c}=t,d=Ze(nt,a),u=tt(nt,a),p=s(o,d.onViewportChange),f=e.useRef(0);return n.jsxs(n.Fragment,{children:[n.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),n.jsx(De.Slot,{scope:a,children:n.jsx(r.div,{"data-radix-select-viewport":"",role:"presentation",...c,ref:p,style:{position:"relative",flex:1,overflow:"hidden auto",...c.style},onScroll:i(c.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){const e=Math.abs(f.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*Xe,o=parseFloat(n.style.minHeight),a=parseFloat(n.style.height),s=Math.max(o,a);if(s<r){const o=s+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop}))})})]})}));rt.displayName=nt;var ot="SelectGroup",[at,st]=Te(ot),it=e.forwardRef(((e,t)=>{const{__scopeSelect:o,...a}=e,s=h();return n.jsx(at,{scope:o,id:s,children:n.jsx(r.div,{role:"group","aria-labelledby":s,...a,ref:t})})}));it.displayName=ot;var lt="SelectLabel",ct=e.forwardRef(((e,t)=>{const{__scopeSelect:o,...a}=e,s=st(lt,o);return n.jsx(r.div,{id:s.id,...a,ref:t})}));ct.displayName=lt;var dt="SelectItem",[ut,pt]=Te(dt),ft=e.forwardRef(((t,o)=>{const{__scopeSelect:a,value:l,disabled:c=!1,textValue:d,...u}=t,p=Me(dt,a),f=Ze(dt,a),v=p.value===l,[m,w]=e.useState(d??""),[b,y]=e.useState(!1),g=s(o,(e=>f.itemRefCallback?.(e,l,c))),x=h(),C=e.useRef("touch"),S=()=>{c||(p.onValueChange(l),p.onOpenChange(!1))};if(""===l)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return n.jsx(ut,{scope:a,value:l,disabled:c,textId:x,isSelected:v,onItemTextChange:e.useCallback((e=>{w((t=>t||(e?.textContent??"").trim()))}),[]),children:n.jsx(De.ItemSlot,{scope:a,value:l,disabled:c,textValue:m,children:n.jsx(r.div,{role:"option","aria-labelledby":x,"data-highlighted":b?"":void 0,"aria-selected":v&&b,"data-state":v?"checked":"unchecked","aria-disabled":c||void 0,"data-disabled":c?"":void 0,tabIndex:c?void 0:-1,...u,ref:g,onFocus:i(u.onFocus,(()=>y(!0))),onBlur:i(u.onBlur,(()=>y(!1))),onClick:i(u.onClick,(()=>{"mouse"!==C.current&&S()})),onPointerUp:i(u.onPointerUp,(()=>{"mouse"===C.current&&S()})),onPointerDown:i(u.onPointerDown,(e=>{C.current=e.pointerType})),onPointerMove:i(u.onPointerMove,(e=>{C.current=e.pointerType,c?f.onItemLeave?.():"mouse"===C.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:i(u.onPointerLeave,(e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()})),onKeyDown:i(u.onKeyDown,(e=>{""!==f.searchRef?.current&&" "===e.key||(je.includes(e.key)&&S()," "===e.key&&e.preventDefault())}))})})})}));ft.displayName=dt;var ht="SelectItemText",vt=e.forwardRef(((o,a)=>{const{__scopeSelect:i,className:l,style:c,...d}=o,u=Me(ht,i),f=Ze(ht,i),h=pt(ht,i),v=He(ht,i),[m,w]=e.useState(null),b=s(a,(e=>w(e)),h.onItemTextChange,(e=>f.itemTextRefCallback?.(e,h.value,h.disabled))),y=m?.textContent,g=e.useMemo((()=>n.jsx("option",{value:h.value,disabled:h.disabled,children:y},h.value)),[h.disabled,h.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:C}=v;return p((()=>(x(g),()=>C(g))),[x,C,g]),n.jsxs(n.Fragment,{children:[n.jsx(r.span,{id:h.textId,...d,ref:b}),h.isSelected&&u.valueNode&&!u.valueNodeHasChildren?t.createPortal(d.children,u.valueNode):null]})}));vt.displayName=ht;var mt="SelectItemIndicator",wt=e.forwardRef(((e,t)=>{const{__scopeSelect:o,...a}=e;return pt(mt,o).isSelected?n.jsx(r.span,{"aria-hidden":!0,...a,ref:t}):null}));wt.displayName=mt;var bt="SelectScrollUpButton",yt=e.forwardRef(((t,r)=>{const o=Ze(bt,t.__scopeSelect),a=tt(bt,t.__scopeSelect),[i,l]=e.useState(!1),c=s(r,a.onScrollButtonChange);return p((()=>{if(o.viewport&&o.isPositioned){let e=function(){const e=t.scrollTop>0;l(e)};const t=o.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[o.viewport,o.isPositioned]),i?n.jsx(Ct,{...t,ref:c,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=o;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));yt.displayName=bt;var gt="SelectScrollDownButton",xt=e.forwardRef(((t,r)=>{const o=Ze(gt,t.__scopeSelect),a=tt(gt,t.__scopeSelect),[i,l]=e.useState(!1),c=s(r,a.onScrollButtonChange);return p((()=>{if(o.viewport&&o.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;l(n)};const t=o.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[o.viewport,o.isPositioned]),i?n.jsx(Ct,{...t,ref:c,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=o;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));xt.displayName=gt;var Ct=e.forwardRef(((t,o)=>{const{__scopeSelect:a,onAutoScroll:s,...l}=t,c=Ze("SelectScrollButton",a),d=e.useRef(null),u=_e(a),f=e.useCallback((()=>{null!==d.current&&(window.clearInterval(d.current),d.current=null)}),[]);return e.useEffect((()=>()=>f()),[f]),p((()=>{const e=u().find((e=>e.ref.current===document.activeElement));e?.ref.current?.scrollIntoView({block:"nearest"})}),[u]),n.jsx(r.div,{"aria-hidden":!0,...l,ref:o,style:{flexShrink:0,...l.style},onPointerDown:i(l.onPointerDown,(()=>{null===d.current&&(d.current=window.setInterval(s,50))})),onPointerMove:i(l.onPointerMove,(()=>{c.onItemLeave?.(),null===d.current&&(d.current=window.setInterval(s,50))})),onPointerLeave:i(l.onPointerLeave,(()=>{f()}))})})),St=e.forwardRef(((e,t)=>{const{__scopeSelect:o,...a}=e;return n.jsx(r.div,{"aria-hidden":!0,...a,ref:t})}));St.displayName="SelectSeparator";var kt="SelectArrow";function Et(e){return""===e||void 0===e}e.forwardRef(((e,t)=>{const{__scopeSelect:r,...o}=e,a=Ne(r),s=Me(kt,r),i=Ze(kt,r);return s.open&&"popper"===i.position?n.jsx(D,{...a,...o,ref:t}):null})).displayName=kt;var Rt=e.forwardRef(((t,r)=>{const{value:o,...a}=t,i=e.useRef(null),l=s(r,i),c=N(o);return e.useEffect((()=>{const e=i.current,t=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(c!==o&&n){const t=new Event("change",{bubbles:!0});n.call(e,o),e.dispatchEvent(t)}}),[c,o]),n.jsx(I,{asChild:!0,children:n.jsx("select",{...a,ref:l,defaultValue:o})})}));function jt(t){const n=d(t),r=e.useRef(""),o=e.useRef(0),a=e.useCallback((e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),s=e.useCallback((()=>{r.current="",window.clearTimeout(o.current)}),[]);return e.useEffect((()=>()=>window.clearTimeout(o.current)),[]),[r,a,s]}function Pt(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let a=(s=e,i=Math.max(o,0),s.map(((e,t)=>s[(i+t)%s.length])));var s,i;1===r.length&&(a=a.filter((e=>e!==n)));const l=a.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return l!==n?l:void 0}Rt.displayName="BubbleSelect";var Dt=Ve,_t=Fe,It=qe,Tt=Ke,Lt=ze,Nt=Ge,Ot=rt,Mt=it,At=ct,Ht=ft,Vt=vt,Bt=wt,Ft=yt,Wt=xt,qt=St,Kt="Switch",[zt,Ut]=a(Kt),[Gt,Xt]=zt(Kt),Yt=e.forwardRef(((t,a)=>{const{__scopeSwitch:l,name:c,checked:d,defaultChecked:u,required:p,disabled:f,value:h="on",onCheckedChange:v,form:m,...w}=t,[b,y]=e.useState(null),g=s(a,(e=>y(e))),x=e.useRef(!1),C=!b||m||!!b.closest("form"),[S=!1,k]=o({prop:d,defaultProp:u,onChange:v});return n.jsxs(Gt,{scope:l,checked:S,disabled:f,children:[n.jsx(r.button,{type:"button",role:"switch","aria-checked":S,"aria-required":p,"data-state":Qt(S),"data-disabled":f?"":void 0,disabled:f,value:h,...w,ref:g,onClick:i(t.onClick,(e=>{k((e=>!e)),C&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())}))}),C&&n.jsx(Jt,{control:b,bubbles:!x.current,name:c,value:h,checked:S,required:p,disabled:f,form:m,style:{transform:"translateX(-100%)"}})]})}));Yt.displayName=Kt;var Zt="SwitchThumb",$t=e.forwardRef(((e,t)=>{const{__scopeSwitch:o,...a}=e,s=Xt(Zt,o);return n.jsx(r.span,{"data-state":Qt(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t})}));$t.displayName=Zt;var Jt=t=>{const{control:r,checked:o,bubbles:a=!0,...s}=t,i=e.useRef(null),l=N(o),c=k(r);return e.useEffect((()=>{const e=i.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(l!==o&&n){const t=new Event("click",{bubbles:a});n.call(e,o),e.dispatchEvent(t)}}),[l,o,a]),n.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...s,tabIndex:-1,ref:i,style:{...t.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function Qt(e){return e?"checked":"unchecked"}var en=Yt,tn=$t;export{Nt as C,Mt as G,oe as I,At as L,Lt as P,L as R,Ft as S,_t as T,Ot as V,re as a,ae as b,ye as c,we as d,be as e,Tt as f,Wt as g,Ht as h,Bt as i,Vt as j,qt as k,Dt as l,It as m,en as n,tn as o,N as u};
