import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{c as a,m as t,B as r,X as i,R as l,W as c,Z as n,U as d,an as x,a5 as m,ay as o,aL as h,aH as g,s as p,av as j,b6 as u,ac as y}from"./index-Dq2DDcRF.js";import{S as N}from"./skeleton-C5-Vbcfa.js";import{a as f,b,c as v,u as k,d as _,e as w}from"./MarkdownEditor-efqAD0DU.js";import{S as C}from"./save-C0jWm1ja.js";import{C as S}from"./calendar-DkU4Wz_i.js";import{B as P}from"./book-open-CQJyFt3x.js";import{P as D}from"./plus-IZJFu_xe.js";import{T}from"./trash-2-CQoB25P5.js";import{C as A}from"./clock-BOr3Qjov.js";import{u as F}from"./query-vendor-B-7l6Nb3.js";import{U as E}from"./users-CUuQfIkj.js";import{S as z}from"./star-VMrI2CfW.js";import{T as R}from"./trending-up-RiA121gT.js";import{M}from"./meh-DlMIvG1_.js";import{C as $}from"./chart-column-J4Wmsddv.js";import{a as L}from"./router-BAzpOxbo.js";import{C as q}from"./chevron-left-BRVekuj0.js";import{D as B}from"./droplets-EvAT4YLu.js";import{E as I}from"./eye-CHtq0Ywx.js";import{F as U}from"./filter-5p82S4AF.js";import{S as V}from"./square-pen-CV76YGwd.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./index-Bsdbnpgm.js";import"./markdown-vendor-C57yw7YK.js";import"./index-Bf1cTgQT.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=a("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),K=({drop:a,onClose:p})=>{const j=()=>{const e=new Date;return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}T${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`},[u,y]=s.useState({title:"",summary:"",main_topic:"",reading_time:5,pub_date:j(),is_published:!1}),[N,k]=s.useState([]),[_,w]=s.useState([{title:"",url:""}]),[F,E]=s.useState(new Set),z=f(),R=b();s.useEffect((()=>{if(a){const e=e=>{const s=new Date(e),a=6e4*s.getTimezoneOffset();return new Date(s.getTime()-a).toISOString().slice(0,16)};y({title:a.title||"",summary:a.summary||"",main_topic:a.main_topic||"",reading_time:a.reading_time||5,pub_date:a.pub_date?e(a.pub_date):j(),is_published:a.is_published||!1}),a.sections&&a.sections.length>0&&k(a.sections.map(((e,s)=>({id:`section-${s}`,title:e.title||"",content:e.content||""})))),a.references&&a.references.length>0?w(a.references.map((e=>({title:e.title||e.reference_text||"",url:e.url||""})))):w([{title:"",url:""}])}}),[a]);const M=(e,s)=>{y((a=>({...a,[e]:s})))},$=(e,s,a)=>{k((t=>t.map((t=>t.id===e?{...t,[s]:a}:t))))},L=(e,s,a)=>{w((t=>t.map(((t,r)=>r===e?{...t,[s]:a}:t))))},q=z.isPending||R.isPending;return e.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-slate-900",children:e.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-4",children:[e.jsxs(t.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:a?"Editar PediDrop":"Novo PediDrop"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Crie atualizações clínicas estruturadas e envolventes"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(r,{variant:"outline",onClick:p,children:[e.jsx(i,{className:"h-4 w-4 mr-2"}),"Cancelar"]}),e.jsxs(r,{onClick:async()=>{try{let e;e=u.pub_date?new Date(u.pub_date).toISOString():(new Date).toISOString();const s={title:u.title,summary:u.summary,main_topic:u.main_topic,reading_time:u.reading_time,pub_date:e,is_published:u.is_published,sections:N.map((e=>({title:e.title,content:e.content}))),references:_.filter((e=>""!==e.title.trim()||""!==e.url.trim()))};a?.id?await R.mutateAsync({...s,id:a.id}):await z.mutateAsync(s),p()}catch(e){alert("Erro ao salvar PediDrop. Tente novamente.")}},disabled:q||!u.title||!u.summary,className:"bg-blue-600 hover:bg-blue-700",children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),q?"Salvando...":"Salvar"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[e.jsxs("div",{className:"lg:col-span-3 space-y-4",children:[e.jsxs(l,{children:[e.jsx(c,{className:"pb-3",children:e.jsxs(n,{className:"flex items-center gap-2 text-lg",children:[e.jsx(S,{className:"h-4 w-4"}),"Informações Básicas"]})}),e.jsxs(d,{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx(x,{htmlFor:"title",children:"Título *"}),e.jsx(m,{id:"title",value:u.title,onChange:e=>M("title",e.target.value),placeholder:"Ex: 🍯 VYKAT XR: primeira arma contra a hiperfagia na Prader-Willi",className:"mt-1"})]}),e.jsx("div",{children:e.jsx(v,{label:"Resumo/Introdução *",value:u.summary,onChange:e=>M("summary",e),placeholder:"👋 Bom dia, Droppers! Hoje no PediDrop: ...",height:120})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(x,{htmlFor:"reading_time",children:"Tempo de Leitura (min)"}),e.jsx(m,{id:"reading_time",type:"number",value:u.reading_time,onChange:e=>M("reading_time",parseInt(e.target.value)||5),min:"1",max:"15",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"pub_date",children:"Data de Publicação"}),e.jsx(m,{id:"pub_date",type:"datetime-local",value:u.pub_date,onChange:e=>M("pub_date",e.target.value),className:"mt-1"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"is_published",checked:u.is_published,onChange:e=>M("is_published",e.target.checked),className:"rounded border-gray-300"}),e.jsx(x,{htmlFor:"is_published",className:"text-sm font-medium",children:"Publicar imediatamente"})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"main_topic",children:"Tópico Principal"}),e.jsx(m,{id:"main_topic",value:u.main_topic,onChange:e=>M("main_topic",e.target.value),placeholder:"Ex: VYKAT XR (diazoxide choline) - Primeira terapia para hiperfagia em Síndrome de Prader-Willi",className:"mt-1"})]})]})]}),e.jsxs(l,{children:[e.jsx(c,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(n,{className:"flex items-center gap-2 text-lg",children:[e.jsx(P,{className:"h-4 w-4"}),"Seções de Conteúdo"]}),e.jsxs(r,{onClick:()=>{const e={id:`section-${Date.now()}`,title:"",content:""};k((s=>[...s,e])),E((s=>new Set([...s,e.id])))},size:"sm",children:[e.jsx(D,{className:"h-4 w-4 mr-1"}),"Adicionar"]})]})}),e.jsx(d,{children:0===N.length?e.jsxs("div",{className:"text-center py-6 text-gray-500 dark:text-gray-400",children:[e.jsx(P,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),e.jsx("p",{className:"text-sm",children:"Nenhuma seção criada ainda."}),e.jsx("p",{className:"text-xs",children:'Clique em "Adicionar" para começar.'})]}):e.jsx("div",{className:"space-y-3",children:N.map(((s,a)=>e.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800",onClick:()=>{return e=s.id,void E((s=>{const a=new Set(s);return a.has(e)?a.delete(e):a.add(e),a}));var e},children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(G,{className:"h-4 w-4 text-gray-400"}),e.jsxs(o,{variant:"outline",children:["Seção ",a+1]}),e.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:s.title||"Título da seção"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:e=>{var a;e.stopPropagation(),a=s.id,k((e=>e.filter((e=>e.id!==a)))),E((e=>{const s=new Set(e);return s.delete(a),s}))},className:"text-red-600 hover:text-red-700",children:e.jsx(T,{className:"h-4 w-4"})}),F.has(s.id)?e.jsx(h,{className:"h-4 w-4"}):e.jsx(g,{className:"h-4 w-4"})]})]}),F.has(s.id)&&e.jsxs("div",{className:"p-3 border-t border-gray-200 dark:border-gray-700 space-y-3",children:[e.jsxs("div",{children:[e.jsx(x,{className:"text-sm",children:"Título da Seção"}),e.jsx(m,{value:s.title,onChange:e=>$(s.id,"title",e.target.value),placeholder:"Ex: Aplicação Prática, Pérolas Clínicas...",className:"mt-1"})]}),e.jsx("div",{children:e.jsx(v,{label:"Conteúdo",value:s.content,onChange:e=>$(s.id,"content",e),placeholder:"Escreva o conteúdo desta seção...",height:150})})]})]},s.id)))})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(l,{children:[e.jsx(c,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(n,{className:"flex items-center gap-2 text-lg",children:[e.jsx(P,{className:"h-4 w-4"}),"Referências"]}),e.jsx(r,{onClick:()=>{w((e=>[...e,{title:"",url:""}]))},size:"sm",variant:"outline",children:e.jsx(D,{className:"h-4 w-4"})})]})}),e.jsx(d,{className:"space-y-3",children:_.map(((s,a)=>e.jsxs("div",{className:"space-y-2 p-3 border border-gray-200 dark:border-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(x,{className:"text-xs font-medium",children:["Referência ",a+1]}),_.length>1&&e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>(e=>{w((s=>s.filter(((s,a)=>a!==e))))})(a),className:"text-red-600 hover:text-red-700 h-6 w-6 p-0",children:e.jsx(T,{className:"h-3 w-3"})})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:`ref-title-${a}`,className:"text-xs text-gray-600 dark:text-gray-400",children:"Título"}),e.jsx(m,{id:`ref-title-${a}`,value:s.title,onChange:e=>L(a,"title",e.target.value),placeholder:"Ex: FDA Approves VYKAT XR...",className:"mt-1 text-sm"})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:`ref-url-${a}`,className:"text-xs text-gray-600 dark:text-gray-400",children:"URL"}),e.jsx(m,{id:`ref-url-${a}`,value:s.url,onChange:e=>L(a,"url",e.target.value),placeholder:"https://www.fda.gov/...",className:"mt-1 text-sm"})]})]},a)))})]}),e.jsxs(l,{children:[e.jsx(c,{className:"pb-3",children:e.jsxs(n,{className:"flex items-center gap-2 text-lg",children:[e.jsx(A,{className:"h-4 w-4"}),"Informações"]})}),e.jsxs(d,{className:"space-y-2 text-xs",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Seções:"}),e.jsx("span",{className:"font-medium",children:N.length})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Referências:"}),e.jsx("span",{className:"font-medium",children:_.filter((e=>e.title.trim()||e.url.trim())).length})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Tempo:"}),e.jsxs("span",{className:"font-medium",children:[u.reading_time," min"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Status:"}),e.jsx("span",{className:"font-medium "+(u.is_published?"text-green-600":"text-yellow-600"),children:u.is_published?"Publicado":"Rascunho"})]})]})]})]})]})]})})},O=()=>{const{data:s,isLoading:a,isError:t}=F({queryKey:["pedidrop-feedback-stats"],queryFn:async()=>{const{data:e,error:s}=await p.from("pedbook_feedbacks").select("*").eq("feedback_category","pedidrop").order("created_at",{ascending:!1});if(s)throw s;const a=e?.length||0,t=e?.filter((e=>"continue"===e.feedback_type)).length||0,r=e?.filter((e=>"improve"===e.feedback_type)).length||0,i=e?.filter((e=>"discontinue"===e.feedback_type)).length||0,l=a>0?e.reduce(((e,s)=>e+(s.rating||0)),0)/a:0,c=e?.reduce(((e,s)=>{const a=e.find((e=>e.post_id===s.post_id));return a?(a.feedback_count++,a.total_rating+=s.rating||0,a.average_rating=a.total_rating/a.feedback_count,"continue"===s.feedback_type?a.continue_count++:"improve"===s.feedback_type?a.improve_count++:"discontinue"===s.feedback_type&&a.discontinue_count++):e.push({post_id:s.post_id||"general",feedback_count:1,total_rating:s.rating||0,average_rating:s.rating||0,continue_count:"continue"===s.feedback_type?1:0,improve_count:"improve"===s.feedback_type?1:0,discontinue_count:"discontinue"===s.feedback_type?1:0}),e}),[])||[];c.sort(((e,s)=>s.feedback_count-e.feedback_count));const n=e?.filter((e=>e.message&&""!==e.message.trim()&&!e.message.startsWith("Feedback do PediDrop:"))).slice(0,10).map((e=>({id:e.id,post_id:e.post_id||"general",feedback_type:e.feedback_type||"unknown",rating:e.rating||0,message:e.message||"",created_at:e.created_at})))||[];return{total_feedback:a,continue_count:t,improve_count:r,discontinue_count:i,average_rating:Math.round(10*l)/10,feedback_by_post:c,recent_comments:n}},staleTime:3e5,gcTime:18e5});if(a)return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:Array.from({length:4}).map(((s,a)=>e.jsx(l,{children:e.jsxs(d,{className:"p-4",children:[e.jsx(N,{className:"h-4 w-20 mb-2"}),e.jsx(N,{className:"h-8 w-16 mb-2"}),e.jsx(N,{className:"h-3 w-24"})]})},a)))});if(t||!s)return e.jsx(l,{className:"mb-6",children:e.jsx(d,{className:"p-4",children:e.jsx("p",{className:"text-red-600 dark:text-red-400",children:"Erro ao carregar estatísticas de feedback"})})});const r={continue:s.total_feedback>0?s.continue_count/s.total_feedback*100:0,improve:s.total_feedback>0?s.improve_count/s.total_feedback*100:0,discontinue:s.total_feedback>0?s.discontinue_count/s.total_feedback*100:0};return e.jsxs("div",{className:"space-y-6 mb-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg",children:e.jsx(E,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total Feedbacks"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:s.total_feedback})]})]})})}),e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-lg",children:e.jsx(z,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avaliação Média"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:s.average_rating.toFixed(1)}),e.jsx("div",{className:"flex items-center gap-1",children:Array.from({length:5}).map(((a,t)=>e.jsx(z,{className:"h-3 w-3 "+(t<Math.round(s.average_rating)?"text-yellow-400 fill-current":"text-gray-300")},t)))})]})]})})}),e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg",children:e.jsx(R,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Continue Assim"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:s.continue_count}),e.jsxs(o,{variant:"secondary",className:"text-xs",children:[r.continue.toFixed(1),"%"]})]})]})})}),e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-lg",children:e.jsx(M,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Pode Melhorar"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:s.improve_count}),e.jsxs(o,{variant:"secondary",className:"text-xs",children:[r.improve.toFixed(1),"%"]})]})]})})})]}),s.feedback_by_post.length>0&&e.jsxs(l,{children:[e.jsx(c,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5"}),"Feedback por Post"]})}),e.jsx(d,{children:e.jsx("div",{className:"space-y-3",children:s.feedback_by_post.slice(0,5).map((s=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:"general"===s.post_id?"Feedback Geral":`Post: ${s.post_id}`}),e.jsxs("div",{className:"flex items-center gap-4 mt-1",children:[e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[s.feedback_count," feedbacks"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(z,{className:"h-3 w-3 text-yellow-400 fill-current"}),e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:s.average_rating.toFixed(1)})]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[s.continue_count>0&&e.jsxs(o,{variant:"secondary",className:"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300",children:["+",s.continue_count]}),s.improve_count>0&&e.jsxs(o,{variant:"secondary",className:"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300",children:["~",s.improve_count]}),s.discontinue_count>0&&e.jsxs(o,{variant:"secondary",className:"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300",children:["-",s.discontinue_count]})]})]},s.post_id)))})})]}),s.recent_comments.length>0&&e.jsxs(l,{children:[e.jsx(c,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(j,{className:"h-5 w-5"}),"Comentários Recentes"]})}),e.jsx(d,{children:e.jsx("div",{className:"space-y-4",children:s.recent_comments.slice(0,3).map((s=>e.jsxs("div",{className:"border-l-4 border-blue-500 pl-4 py-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(o,{variant:"secondary",className:"continue"===s.feedback_type?"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300":"improve"===s.feedback_type?"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300":"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300",children:"continue"===s.feedback_type?"Continue":"improve"===s.feedback_type?"Melhorar":"Repensar"}),e.jsx("div",{className:"flex items-center gap-1",children:Array.from({length:s.rating}).map(((s,a)=>e.jsx(z,{className:"h-3 w-3 text-yellow-400 fill-current"},a)))})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[e.jsx(A,{className:"h-3 w-3"}),new Date(s.created_at).toLocaleDateString("pt-BR")]})]}),e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300 mb-1",children:['"',s.message,'"']}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Post: ","general"===s.post_id?"Feedback Geral":s.post_id]})]},s.id)))})})]})]})},W=()=>{const a=L(),[i,c]=s.useState(""),[n,x]=s.useState(!1),[h,g]=s.useState(null),[p,j]=s.useState(1),{data:f,isLoading:b,isError:v,refetch:C}=k({limit:10,offset:10*(p-1),searchTerm:i||void 0,includeUnpublished:!0}),{data:P,isLoading:F}=_({searchTerm:i||void 0,includeUnpublished:!0}),E=w(),z=Math.ceil((P||0)/10),R=()=>{g(null),x(!0)};return n?e.jsx(K,{drop:h,onClose:()=>{x(!1),g(null),C()}}):e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-slate-900",children:[e.jsx(u,{children:e.jsx("title",{children:"Administração PediDrop | PedBook Admin"})}),e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[e.jsxs(t.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(r,{variant:"ghost",onClick:()=>a("/admin"),className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-4 w-4"}),"Voltar ao Admin"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(B,{className:"h-8 w-8 text-blue-600"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:"Administração PediDrop"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Gerenciar atualizações clínicas diárias"})]})]})]}),e.jsxs(r,{onClick:R,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Novo PediDrop"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg",children:e.jsx(B,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total PediDrops"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:F?"...":P||0})]})]})})}),e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg",children:e.jsx(S,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Este Mês"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"--"})]})]})})}),e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-lg",children:e.jsx(A,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Tempo Médio"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"5 min"})]})]})})}),e.jsx(l,{children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg",children:e.jsx(I,{className:"h-5 w-5 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Visualizações"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"--"})]})]})})})]}),e.jsx(O,{}),e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs("div",{className:"relative flex-1 max-w-md",children:[e.jsx(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),e.jsx(m,{placeholder:"Buscar PediDrops...",value:i,onChange:e=>c(e.target.value),className:"pl-10"})]}),e.jsxs(r,{variant:"outline",className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-4 w-4"}),"Filtros"]})]})]}),e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:[b?e.jsx("div",{className:"space-y-4",children:Array.from({length:5}).map(((s,a)=>e.jsx(l,{children:e.jsx(d,{className:"p-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 space-y-3",children:[e.jsx(N,{className:"h-6 w-3/4"}),e.jsx(N,{className:"h-4 w-full"}),e.jsx(N,{className:"h-4 w-1/2"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{className:"h-6 w-16"}),e.jsx(N,{className:"h-6 w-20"})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{className:"h-8 w-8"}),e.jsx(N,{className:"h-8 w-8"}),e.jsx(N,{className:"h-8 w-8"})]})]})})},a)))}):v?e.jsx(l,{children:e.jsx(d,{className:"p-8 text-center",children:e.jsx("p",{className:"text-red-600 dark:text-red-400",children:"Erro ao carregar PediDrops. Tente novamente."})})}):f&&f.length>0?e.jsx("div",{className:"space-y-4",children:f.map(((s,a)=>{return e.jsx(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*a},children:e.jsx(l,{className:"hover:shadow-md transition-shadow",children:e.jsx(d,{className:"p-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 line-clamp-1",children:s.title}),e.jsxs(o,{variant:"outline",className:"text-xs",children:[e.jsx(A,{className:"h-3 w-3 mr-1"}),s.reading_time||5," min"]}),s.category&&e.jsx(o,{variant:"secondary",className:"text-xs",children:s.category}),!s.is_published&&e.jsx(o,{variant:"outline",className:"text-xs text-orange-600 border-orange-300",children:"Rascunho"})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 line-clamp-2 mb-3",children:s.summary}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(S,{className:"h-4 w-4"}),(i=s.pub_date,new Date(i).toLocaleDateString("pt-BR",{day:"2-digit",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}))]}),s.sections&&e.jsxs("div",{children:[s.sections.length," seções"]}),s.references&&e.jsxs("div",{children:[s.references.length," referências"]}),e.jsxs("div",{children:[s.views_count," visualizações"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>window.open("/pedidrop","_blank"),children:e.jsx(I,{className:"h-4 w-4"})}),e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>(e=>{g(e),x(!0)})(s),children:e.jsx(V,{className:"h-4 w-4"})}),e.jsx(r,{variant:"ghost",size:"sm",onClick:()=>(async e=>{if(window.confirm("Tem certeza que deseja excluir este PediDrop?"))try{await E.mutateAsync(e)}catch(s){alert("Erro ao deletar PediDrop. Tente novamente.")}})(s.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:e.jsx(T,{className:"h-4 w-4"})})]})]})})})},s.id);var i}))}):e.jsx(l,{children:e.jsxs(d,{className:"p-8 text-center",children:[e.jsx(B,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"Nenhum PediDrop encontrado"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Comece criando seu primeiro PediDrop clicando no botão acima."}),e.jsxs(r,{onClick:R,children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Criar Primeiro PediDrop"]})]})}),z>1&&e.jsxs("div",{className:"flex items-center justify-center gap-2 mt-8",children:[e.jsx(r,{variant:"outline",onClick:()=>j((e=>Math.max(1,e-1))),disabled:1===p,children:"Anterior"}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Página ",p," de ",z]}),e.jsx(r,{variant:"outline",onClick:()=>j((e=>Math.min(z,e+1))),disabled:p===z,children:"Próximo"})]})]})]})]})};export{W as default};
