import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,aM as r,ab as l,B as o,U as t,ao as i,ae as n,af as c,ag as d,ah as m,aj as x,aw as p}from"./index-CNG-Xj2g.js";import u from"./Footer-BgCSiPkf.js";import{L as v}from"./router-BAzpOxbo.js";import{C as j,a as h}from"./calculatorSEOData-BA0ImUJZ.js";import{C as g}from"./chevron-left-Ep4pqQcd.js";import{E as b}from"./eye-S2SysmVr.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-ik6vfZ65.js";import"./rocket-BEoGgNr2.js";import"./target-Dul0NbVV.js";import"./zap-C4mKju26.js";import"./book-open-EV5sJdXr.js";import"./star-BUSksJJE.js";import"./circle-help-BbvIlE64.js";import"./instagram-ClgJ7H9i.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=s("Hand",[["path",{d:"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2",key:"1fvzgz"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2",key:"1kc0my"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8",key:"10h0bg"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]]),N=()=>{const s=h.glasgow,[N,f]=a.useState({eyeOpening:null,motorResponse:null,verbalResponse:null}),k={eyeOpening:[{value:4,label:"Espontânea"},{value:3,label:"Ao chamado"},{value:2,label:"Estímulo álgico"},{value:1,label:"Não responde"}],motorResponse:[{value:6,label:"Movimentos com propósito"},{value:5,label:"Localiza dor"},{value:4,label:"Retira membros à dor"},{value:3,label:"Flexão anormal (decorticação)"},{value:2,label:"Extensão anormal (descerebração)"},{value:1,label:"Não responde"}],verbalResponse:[{value:5,label:"Lalação, sons próprios da idade"},{value:4,label:"Choro consolável"},{value:3,label:"Choro inconsolável"},{value:2,label:"Grunidos ou gemência à dor"},{value:1,label:"Não responde"}]},w=(e,a)=>{f((s=>({...s,[e]:parseInt(a,10)})))},R=Object.values(N).reduce(((e,a)=>e+(a||0)),0),S=(M=R)>=13?{text:"Trauma craniano leve",description:"Monitoramento contínuo recomendado",color:"text-green-600 dark:text-green-400"}:M>=9?{text:"Trauma craniano moderado",description:"Avaliação neurológica detalhada necessária",color:"text-yellow-600 dark:text-yellow-400"}:{text:"Trauma craniano grave",description:"Necessidade de intubação e cuidados intensivos",color:"text-red-600 dark:text-red-400"};var M;const O=Object.values(N).every((e=>null!==e));return e.jsxs("div",{className:r.gradientBackground("min-h-screen flex flex-col"),children:[e.jsx(j,{...s}),e.jsx(l,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{to:"/calculadoras",children:e.jsx(o,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:e.jsx(g,{className:"h-5 w-5"})})}),e.jsx("h1",{className:r.gradientHeading("text-3xl"),children:"Escala de Glasgow Pediátrica"})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avaliação do nível de consciência em crianças baseada em três componentes principais"}),e.jsxs(t,{className:r.card("p-6 space-y-6"),children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(b,{className:"h-5 w-5 text-primary dark:text-blue-400"}),e.jsx(i,{className:"text-base font-medium text-gray-800 dark:text-gray-200",children:"Abertura Ocular"})]}),e.jsxs(n,{value:N.eyeOpening?.toString()||"",onValueChange:e=>w("eyeOpening",e),children:[e.jsx(c,{className:r.select("w-full"),children:e.jsx(d,{placeholder:"Selecione uma opção"})}),e.jsx(m,{children:k.eyeOpening.map((a=>e.jsxs(x,{value:a.value.toString(),children:[a.label," (",a.value," pontos)"]},a.value)))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5 text-primary dark:text-blue-400"}),e.jsx(i,{className:"text-base font-medium text-gray-800 dark:text-gray-200",children:"Melhor Resposta Motora"})]}),e.jsxs(n,{value:N.motorResponse?.toString()||"",onValueChange:e=>w("motorResponse",e),children:[e.jsx(c,{className:r.select("w-full"),children:e.jsx(d,{placeholder:"Selecione uma opção"})}),e.jsx(m,{children:k.motorResponse.map((a=>e.jsxs(x,{value:a.value.toString(),children:[a.label," (",a.value," pontos)"]},a.value)))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5 text-primary dark:text-blue-400"}),e.jsx(i,{className:"text-base font-medium text-gray-800 dark:text-gray-200",children:"Melhor Resposta Verbal"})]}),e.jsxs(n,{value:N.verbalResponse?.toString()||"",onValueChange:e=>w("verbalResponse",e),children:[e.jsx(c,{className:r.select("w-full"),children:e.jsx(d,{placeholder:"Selecione uma opção"})}),e.jsx(m,{children:k.verbalResponse.map((a=>e.jsxs(x,{value:a.value.toString(),children:[a.label," (",a.value," pontos)"]},a.value)))})]})]})]}),O&&e.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{className:"text-4xl font-bold text-primary dark:text-blue-400",children:[R," pontos"]}),e.jsx("div",{className:`text-xl font-semibold ${S.color}`,children:S.text}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto",children:[S.description,e.jsx("p",{className:"mt-2 text-xs",children:"Nota: Esta avaliação deve ser considerada em conjunto com outros parâmetros clínicos para decisões terapêuticas."})]})]})})]})]})}),e.jsx(u,{})]})};export{N as default};
