import{r as e}from"./critical-DVX9Inzy.js";import{u as r,b4 as i,b5 as s,s as a}from"./index-DwBJcqzE.js";import{u as t}from"./query-vendor-B-7l6Nb3.js";const n=()=>{const{user:n}=r(),{data:o,isLoading:m,error:u}=t({queryKey:i.USER_PROFILE(n?.id||""),queryFn:async()=>{if(!n?.id)return null;const{data:e,error:r}=await a.from("profiles").select("\n          id, full_name, avatar_url, formation_area, graduation_year,\n          is_student, is_professional, is_admin, specialty, preparation_type,\n          theme_preference, premium, premium_requested, premium_requested_history,\n          professional_email, phone, registration_number, created_at, updated_at\n        ").eq("id",n.id).single();if(r)throw r;return e},enabled:!!n?.id,staleTime:9e5,gcTime:18e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,...s.USER_DATA});return e.useMemo((()=>({user:n,profile:o,isLoading:m,error:u,isAdmin:o?.is_admin||!1,isPremium:o?.premium||!1,isPremiumRequested:o?.premium_requested||!1,isPremiumRequestedHistory:o?.premium_requested_history||!1,domain:"residencia",isResidencia:!0,isReady:!m&&!!o,hasAccess:!0,hasCompletedOnboarding:!0})),[n,o,m,u])};export{n as u};
