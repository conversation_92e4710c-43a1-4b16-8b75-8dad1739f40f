import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{c as a,U as t,Y as i,_ as r,ak as n,V as l,az as c,P as d,av as o,al as m,a0 as x,aE as h,W as p,L as g,k as u,d as j,B as N,aC as f,T as v,s as b,a8 as _,ad as y,a6 as w,ar as A,as as C,at as k,au as S}from"./index-CNG-Xj2g.js";import{S as z}from"./separator-Cc_jdJJQ.js";import{B as E}from"./building-2-BEiGuev6.js";import{D as V}from"./dollar-sign-GR_XPpgY.js";import{C as I}from"./calendar-CqqFJfDW.js";import{U as D}from"./user-DPHDn0jo.js";import{A as L,b as P}from"./alert-BT_NObbd.js";import{B as M}from"./brain-circuit-DHMLD7O7.js";import{Z as B}from"./zap-C4mKju26.js";import{S as T}from"./save-D2Q92WQZ.js";import{R as U}from"./rotate-ccw-vnwXNAWh.js";import{a as $}from"./router-BAzpOxbo.js";import{D as q}from"./database-CAe0AgAU.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=a("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),F=a("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),G=({ingredient:s,relatedDrugs:a,isLoadingDrugs:j})=>{const N=e=>new Date(e).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),f=s=>{const a={ativo:{color:"bg-green-100 text-green-800",icon:p},inativo:{color:"bg-red-100 text-red-800",icon:h},pendente:{color:"bg-yellow-100 text-yellow-800",icon:m}},t=a[s]||a.ativo,i=t.icon;return e.jsxs(c,{className:`${t.color} flex items-center gap-1`,children:[e.jsx(i,{className:"h-3 w-3"}),s]})},{issues:v,suggestions:b}=(()=>{const e=[],s=[];return a.forEach((a=>{a&&a.brand_name&&(a.descricao&&""!==a.descricao.trim()||e.push(`${a.brand_name}: Descrição vazia`),a.therapeutic_classes&&0!==a.therapeutic_classes.length||e.push(`${a.brand_name}: Classe terapêutica não definida`),a.fabricante&&""!==a.fabricante.trim()||s.push(`${a.brand_name}: Adicionar fabricante`),a.pregnancy_info&&"nd"!==a.pregnancy_info&&""!==a.pregnancy_info.trim()||s.push(`${a.brand_name}: Adicionar informações sobre gravidez`),a.breastfeeding_info&&"nd"!==a.breastfeeding_info&&""!==a.breastfeeding_info.trim()||s.push(`${a.brand_name}: Adicionar informações sobre amamentação`),a.patient_instructions&&0!==a.patient_instructions.length||s.push(`${a.brand_name}: Adicionar instruções ao paciente`))})),{issues:e,suggestions:s}})();return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(n,{className:"h-5 w-5"}),"Informações Básicas"]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Nome"}),e.jsx("p",{className:"text-lg font-semibold",children:s.name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Código DCB"}),s.dcb_codes&&s.dcb_codes.length>1?e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.dcb_codes.filter(Boolean).map(((s,a)=>e.jsx(c,{variant:"outline",className:"text-xs",children:s},a)))}):e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(R,{className:"h-4 w-4"}),s.dcb_code||"Não informado"]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Número CAS"}),s.cas_numbers&&s.cas_numbers.length>1?e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.cas_numbers.filter(Boolean).map(((s,a)=>e.jsx(c,{variant:"outline",className:"text-xs",children:s},a)))}):e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(R,{className:"h-4 w-4"}),s.cas_number||"Não informado"]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Total de Medicamentos"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(d,{className:"h-4 w-4"}),s.total_drugs||0]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Total de Apresentações"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4"}),s.total_presentations||0]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Classes Terapêuticas"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(o,{className:"h-4 w-4"}),s.total_therapeutic_classes||0]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Vias de Administração"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-4 w-4"}),s.total_administration_routes||0]})]}),s.has_combinations&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Associações"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4"}),s.combination_count||0," medicamentos"]})]}),s.individual_ingredients&&s.individual_ingredients.length>1&&e.jsxs("div",{className:"col-span-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Ingredientes da Combinação"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.individual_ingredients.map(((s,a)=>e.jsx(c,{variant:"outline",className:"text-xs",children:s},a)))})]}),s.is_association_only&&e.jsx("div",{className:"col-span-2",children:e.jsx(c,{variant:"secondary",className:"bg-orange-100 text-orange-700 border-orange-300",children:"🔗 Esta é uma combinação de medicamentos"})})]}),e.jsx(z,{}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Criado em"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-4 w-4"}),N(s.created_at)]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Atualizado em"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-4 w-4"}),N(s.updated_at)]})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(o,{className:"h-5 w-5"}),"Classes Terapêuticas (",s.therapeutic_classes?.length??"?",")"]})}),e.jsx(l,{children:s.therapeutic_classes&&0!==s.therapeutic_classes.length?e.jsx("div",{className:"space-y-2",children:s.therapeutic_classes.map(((s,a)=>e.jsx(c,{variant:"outline",className:"mr-2 mb-2",children:s},a)))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"Nenhuma classe terapêutica encontrada"})})]}),e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5"}),"Vias de Administração (",s.administration_routes?.length??"?",")"]})}),e.jsx(l,{children:s.administration_routes&&0!==s.administration_routes.length?e.jsx("div",{className:"space-y-2",children:s.administration_routes.map(((s,a)=>e.jsx(c,{variant:"secondary",className:"mr-2 mb-2",children:s},a)))}):e.jsx("p",{className:"text-gray-500 text-center py-4",children:"Nenhuma via de administração encontrada"})})]})]}),e.jsxs(t,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(m,{className:"h-5 w-5"}),"Análise de Qualidade dos Dados"]}),e.jsx(x,{children:"Problemas e sugestões identificados nos medicamentos relacionados"})]}),e.jsxs(l,{className:"space-y-4",children:[v.length>0&&e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-red-700 mb-2 flex items-center gap-2",children:[e.jsx(h,{className:"h-4 w-4"}),"Problemas Críticos (",v.length,")"]}),e.jsxs("ul",{className:"space-y-1",children:[v.slice(0,5).map(((s,a)=>e.jsxs("li",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:["• ",s]},a))),v.length>5&&e.jsxs("li",{className:"text-sm text-red-500 italic",children:["... e mais ",v.length-5," problemas"]})]})]}),b.length>0&&e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-yellow-700 mb-2 flex items-center gap-2",children:[e.jsx(m,{className:"h-4 w-4"}),"Sugestões de Melhoria (",b.length,")"]}),e.jsxs("ul",{className:"space-y-1",children:[b.slice(0,5).map(((s,a)=>e.jsxs("li",{className:"text-sm text-yellow-600 bg-yellow-50 p-2 rounded",children:["• ",s]},a))),b.length>5&&e.jsxs("li",{className:"text-sm text-yellow-500 italic",children:["... e mais ",b.length-5," sugestões"]})]})]}),0===v.length&&0===b.length&&e.jsxs("div",{className:"text-center py-4",children:[e.jsx(p,{className:"h-8 w-8 mx-auto mb-2 text-green-500"}),e.jsx("p",{className:"text-green-600 font-medium",children:"Todos os dados estão em boa qualidade!"})]})]})]}),a.length>0&&e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(n,{className:"h-5 w-5"}),"Resumo dos Medicamentos"]})}),e.jsxs(l,{children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Medicamentos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:a.reduce(((e,s)=>e+(s.presentations?.length||0)),0)}),e.jsx("div",{className:"text-xs text-gray-500",children:"Apresentações"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:a.filter((e=>e.is_controlled)).length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Controlados"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a.filter((e=>e.is_high_cost)).length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Alto Custo"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-blue-500",children:a.filter((e=>e.adult_use)).length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Uso Adulto"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-pink-500",children:a.filter((e=>e.pediatric_use)).length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Uso Pediátrico"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-red-500",children:a.filter((e=>e.pregnancy_info&&"nd"!==e.pregnancy_info)).length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Info Gravidez"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-semibold text-cyan-500",children:a.filter((e=>e.breastfeeding_info&&"nd"!==e.breastfeeding_info)).length}),e.jsx("div",{className:"text-xs text-gray-500",children:"Info Amamentação"})]})]})]})]}),e.jsxs(t,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(d,{className:"h-5 w-5"}),"Medicamentos Relacionados (",a.length,")"]}),e.jsx(x,{children:"Medicamentos que contêm este princípio ativo"})]}),e.jsx(l,{children:j?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx(g,{className:"h-6 w-6 animate-spin"}),e.jsx("span",{className:"ml-2",children:"Carregando medicamentos..."})]}):0===a.length?e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(d,{className:"h-8 w-8 mx-auto mb-2"}),e.jsx("p",{children:"Nenhum medicamento encontrado"})]}):e.jsx("div",{className:"space-y-6 max-h-[80vh] overflow-y-auto",children:a.map((s=>e.jsx(t,{className:"border-l-4 border-l-blue-500",children:e.jsxs(l,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-xl mb-1",children:s.brand_name}),s.descricao&&s.descricao!==s.brand_name&&e.jsx("p",{className:"text-sm text-gray-600",children:s.descricao})]}),f(s.status)]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[s.therapeutic_classes&&s.therapeutic_classes.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Classes Terapêuticas"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:s.therapeutic_classes.map(((s,a)=>e.jsxs(c,{variant:"outline",className:"text-xs",children:[e.jsx(E,{className:"h-3 w-3 mr-1"}),s]},a)))})]}),s.fabricante&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Fabricante"}),e.jsxs("p",{className:"flex items-center gap-1 text-sm",children:[e.jsx(E,{className:"h-3 w-3"}),s.fabricante]})]}),s.tipo&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Tipo"}),e.jsx("p",{className:"text-sm capitalize",children:s.tipo.replace("-"," ")})]}),s.titularidade&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Titularidade"}),e.jsx("p",{className:"text-sm",children:s.titularidade})]})]}),s.composicao&&e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Composição"}),e.jsx("p",{className:"text-sm bg-gray-50 p-2 rounded mt-1",children:s.composicao})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[s.adult_use&&e.jsxs(c,{variant:"outline",className:"text-xs",children:[e.jsx(D,{className:"h-3 w-3 mr-1"}),"Uso Adulto"]}),s.pediatric_use&&e.jsxs(c,{variant:"outline",className:"text-xs",children:[e.jsx(u,{className:"h-3 w-3 mr-1"}),"Uso Pediátrico"]}),s.is_controlled&&e.jsxs(c,{variant:"destructive",className:"text-xs",children:[e.jsx(o,{className:"h-3 w-3 mr-1"}),"Medicamento Controlado"]}),s.is_high_cost&&e.jsxs(c,{variant:"secondary",className:"text-xs",children:[e.jsx(V,{className:"h-3 w-3 mr-1"}),"Alto Custo"]}),s.is_association&&e.jsx(c,{variant:"outline",className:"text-xs bg-orange-50 text-orange-700 border-orange-300",children:"Associação"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[s.pregnancy_info&&"nd"!==s.pregnancy_info&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Gravidez"}),e.jsx("p",{className:"text-sm bg-pink-50 p-2 rounded mt-1",children:s.pregnancy_info})]}),s.breastfeeding_info&&"nd"!==s.breastfeeding_info&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Amamentação"}),e.jsx("p",{className:"text-sm bg-blue-50 p-2 rounded mt-1",children:s.breastfeeding_info})]})]}),s.patient_instructions&&s.patient_instructions.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Instruções ao Paciente"}),e.jsx("ul",{className:"text-sm bg-green-50 p-2 rounded mt-1 list-disc list-inside",children:s.patient_instructions.map(((s,a)=>e.jsx("li",{children:s},a)))})]}),s.eans&&s.eans.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Códigos EAN"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.eans.map(((s,a)=>e.jsx(c,{variant:"outline",className:"text-xs font-mono",children:s},a)))})]}),s.presentations&&s.presentations.length>0&&e.jsxs("div",{className:"border-t pt-4",children:[e.jsxs("h5",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[e.jsx(d,{className:"h-4 w-4"}),"Apresentações (",s.presentations.length,")"]}),e.jsx("div",{className:"space-y-3",children:s.presentations.map((s=>e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg border",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{children:[e.jsxs("span",{className:"font-medium text-base",children:[s.dosage_form," - ",s.strength]}),s.is_primary&&e.jsx(c,{variant:"outline",className:"text-xs ml-2 bg-green-50 text-green-700 border-green-300",children:"Apresentação Principal"})]}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx(c,{variant:"vermelho"===s.prescription_category?"destructive":"outline",className:"text-xs",children:s.prescription_category}),e.jsx(c,{variant:"secondary",className:"text-xs",children:s.prescription_type})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[s.therapeutic_classes.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Classes Terapêuticas"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.therapeutic_classes.map(((s,a)=>e.jsx(c,{variant:"outline",className:"text-xs",children:s},a)))})]}),s.administration_routes.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Vias de Administração"}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:s.administration_routes.map(((s,a)=>e.jsx(c,{variant:"secondary",className:"text-xs",children:s},a)))})]})]})]},s.id)))})]})]})},s.id)))})})]})]})},O=({ingredient:a,relatedDrugs:d,onDataUpdated:o})=>{const{toast:u}=j(),[_,y]=s.useState(!1),[w,A]=s.useState([]),[C,k]=s.useState(null),[S,E]=s.useState(null),[V,I]=s.useState(""),D=async e=>{try{if(!C)return;const s={};"patient_instructions"===e.field?s[e.field]=S===e.field?V.split("\n").filter((e=>""!==e.trim())):e.suggestedValue.split("\n").filter((e=>""!==e.trim())):s[e.field]=S===e.field?V:e.suggestedValue;const{error:a}=await b.from("drugs").update(s).eq("id",C.id);if(a)return void u({title:"Erro",description:"Erro ao atualizar medicamento",variant:"destructive"});A((s=>s.map((s=>s.field===e.field?{...s,status:"approved"}:s)))),E(null),I(""),u({title:"Sucesso",description:"Alteração aprovada e aplicada!"}),o()}catch(s){u({title:"Erro",description:"Erro inesperado ao aplicar alteração",variant:"destructive"})}},$=()=>{E(null),I("")},q=s=>{const{color:a,label:t}={high:{color:"bg-green-100 text-green-800",label:"Alta Confiança"},medium:{color:"bg-yellow-100 text-yellow-800",label:"Média Confiança"},low:{color:"bg-red-100 text-red-800",label:"Baixa Confiança"}}[s];return e.jsx(c,{className:a,children:t})},R=s=>{switch(s){case"approved":return e.jsx(p,{className:"h-4 w-4 text-green-500"});case"rejected":return e.jsx(h,{className:"h-4 w-4 text-red-500"});default:return e.jsx(m,{className:"h-4 w-4 text-yellow-500"})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(t,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-5 w-5"}),"Análise com IA"]}),e.jsx(x,{children:"Selecione um medicamento para analisar e melhorar seus dados"})]}),e.jsx(l,{className:"space-y-4",children:0===d.length?e.jsxs(L,{children:[e.jsx(n,{className:"h-4 w-4"}),e.jsx(P,{children:"Nenhum medicamento relacionado encontrado para análise."})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:d.map((s=>e.jsx(t,{className:"cursor-pointer transition-all hover:shadow-md "+(C?.id===s.id?"ring-2 ring-blue-500 bg-blue-50":"hover:bg-gray-50"),onClick:()=>k(s),children:e.jsxs(l,{className:"p-4",children:[e.jsx("h4",{className:"font-medium mb-1",children:s.brand_name}),e.jsx("p",{className:"text-sm text-gray-600",children:s.therapeutic_classes&&s.therapeutic_classes.length>0?s.therapeutic_classes.join(", "):"Classes não definidas"}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsx(c,{variant:"outline",children:s.status}),C?.id===s.id&&e.jsxs(N,{size:"sm",onClick:e=>{e.stopPropagation(),(async e=>{try{y(!0),k(e);const s=[];e.descricao&&""!==e.descricao.trim()&&e.descricao!==e.brand_name||s.push({field:"descricao",fieldLabel:"Descrição",currentValue:e.descricao||"",suggestedValue:`${a.name} é um medicamento utilizado para [indicação terapêutica]. Pertence à classe ${e.drug_class||"[classe terapêutica]"} e é indicado para o tratamento de [condições específicas].`,reasoning:"A descrição atual está vazia ou muito básica. Uma descrição mais detalhada ajudaria profissionais de saúde a entender melhor o medicamento.",confidence:"high",status:"pending"}),e.pregnancy_info&&"nd"!==e.pregnancy_info&&""!==e.pregnancy_info.trim()||s.push({field:"pregnancy_info",fieldLabel:"Informações sobre Gravidez",currentValue:e.pregnancy_info||"",suggestedValue:"<b>Categoria de risco na gravidez: [A/B/C/D/X]</b>\n\nEste medicamento deve ser usado durante a gravidez somente quando o benefício justificar o risco potencial. Consulte sempre um médico antes de usar durante a gestação.",reasoning:"Informações sobre segurança na gravidez são essenciais para prescrição segura.",confidence:"medium",status:"pending"}),e.breastfeeding_info&&"nd"!==e.breastfeeding_info&&""!==e.breastfeeding_info.trim()||s.push({field:"breastfeeding_info",fieldLabel:"Informações sobre Amamentação",currentValue:e.breastfeeding_info||"",suggestedValue:"<b>[Verde/Amarelo/Vermelho]: [Compatível/Uso criterioso/Contraindicado] durante a amamentação</b>\n\nEste medicamento [pode ser usado com segurança/requer monitoramento/deve ser evitado] durante a amamentação. Consulte um médico para orientações específicas.",reasoning:"Informações sobre compatibilidade com amamentação são cruciais para mães lactantes.",confidence:"medium",status:"pending"}),e.patient_instructions&&0!==e.patient_instructions.length||s.push({field:"patient_instructions",fieldLabel:"Instruções ao Paciente",currentValue:e.patient_instructions?.join("\n")||"",suggestedValue:"Tome este medicamento conforme orientação médica.\nNão interrompa o tratamento sem consultar seu médico.\nEm caso de efeitos colaterais, procure orientação médica.\nMantenha o medicamento em local seco e protegido da luz.\nNão compartilhe este medicamento com outras pessoas.",reasoning:"Instruções claras ao paciente são fundamentais para o uso seguro e eficaz do medicamento.",confidence:"high",status:"pending"}),e.therapeutic_classes&&0!==e.therapeutic_classes.length||s.push({field:"therapeutic_classes",fieldLabel:"Classes Terapêuticas",currentValue:e.therapeutic_classes?.join(", ")||"",suggestedValue:`[Classes terapêuticas baseadas no princípio ativo ${a.name}]`,reasoning:"As classes terapêuticas são importantes para categorização e busca de medicamentos.",confidence:"medium",status:"pending"}),A(s),0===s.length?u({title:"Análise Concluída",description:"Nenhuma melhoria identificada. Os dados estão em boa qualidade!"}):u({title:"Análise Concluída",description:`${s.length} sugestões de melhoria identificadas.`})}catch(s){u({title:"Erro",description:"Erro ao analisar com IA",variant:"destructive"})}finally{y(!1)}})(s)},disabled:_,className:"flex items-center gap-2",children:[_?e.jsx(g,{className:"h-4 w-4 animate-spin"}):e.jsx(f,{className:"h-4 w-4"}),_?"Analisando...":"Analisar com IA"]})]})]})},s.id)))})})]}),w.length>0&&e.jsxs(t,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5"}),"Sugestões de Melhoria (",w.length,")"]}),e.jsx(x,{children:"Revise e aprove as sugestões da IA para melhorar os dados"})]}),e.jsx(l,{className:"space-y-6",children:w.map(((s,a)=>e.jsxs("div",{className:"border rounded-lg p-4 space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[R(s.status),e.jsx("h4",{className:"font-medium",children:s.fieldLabel}),q(s.confidence)]})}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Valor Atual:"}),e.jsx("div",{className:"bg-gray-50 p-3 rounded text-sm",children:s.currentValue||e.jsx("em",{className:"text-gray-400",children:"Vazio"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Sugestão da IA:"}),S===s.field?e.jsx(v,{value:V,onChange:e=>I(e.target.value),className:"min-h-24",placeholder:"Edite a sugestão..."}):e.jsx("div",{className:"bg-blue-50 p-3 rounded text-sm border-l-4 border-blue-500",children:s.suggestedValue})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Justificativa:"}),e.jsx("p",{className:"text-sm text-gray-700 bg-yellow-50 p-3 rounded",children:s.reasoning})]})]}),"pending"===s.status&&e.jsx("div",{className:"flex gap-2 pt-2",children:S===s.field?e.jsxs(e.Fragment,{children:[e.jsxs(N,{size:"sm",onClick:()=>D(s),className:"flex items-center gap-2",children:[e.jsx(T,{className:"h-4 w-4"}),"Salvar e Aprovar"]}),e.jsxs(N,{size:"sm",variant:"outline",onClick:$,className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-4 w-4"}),"Cancelar"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs(N,{size:"sm",onClick:()=>D(s),className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-4 w-4"}),"Aprovar"]}),e.jsxs(N,{size:"sm",variant:"outline",onClick:()=>(e=>{E(e.field),I(e.suggestedValue)})(s),className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-4 w-4"}),"Editar"]}),e.jsxs(N,{size:"sm",variant:"destructive",onClick:()=>(e=>{A((s=>s.map((s=>s.field===e.field?{...s,status:"rejected"}:s)))),E(null),I(""),u({title:"Rejeitado",description:"Sugestão rejeitada"})})(s),className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-4 w-4"}),"Rejeitar"]})]})}),"approved"===s.status&&e.jsxs(L,{className:"bg-green-50 border-green-200",children:[e.jsx(p,{className:"h-4 w-4 text-green-600"}),e.jsx(P,{className:"text-green-700",children:"Sugestão aprovada e aplicada com sucesso!"})]}),"rejected"===s.status&&e.jsxs(L,{className:"bg-red-50 border-red-200",children:[e.jsx(h,{className:"h-4 w-4 text-red-600"}),e.jsx(P,{className:"text-red-700",children:"Sugestão rejeitada."})]}),a<w.length-1&&e.jsx(z,{})]},s.field)))})]})]})},H=()=>{const a=$(),{toast:m}=j(),[h,p]=s.useState([]),[u,v]=s.useState(null),[z,E]=s.useState([]),[V,I]=s.useState(""),[D,L]=s.useState(!0),[P,T]=s.useState(!1),[U,R]=s.useState(!1);s.useEffect((()=>{F()}),[]);const F=async()=>{try{L(!0);const{data:e,error:s}=await b.rpc("get_active_ingredient_combinations");if(s)return void m({title:"Erro",description:"Erro ao carregar princípios ativos",variant:"destructive"});const a=(e||[]).map((e=>({id:e.combination_id,name:e.combination_name,dcb_code:1===e.dcb_codes?.length?e.dcb_codes[0]:"Múltiplos",cas_number:1===e.cas_numbers?.length?e.cas_numbers[0]:"Múltiplos",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),total_drugs:e.total_drugs,total_presentations:e.total_presentations,total_therapeutic_classes:e.therapeutic_classes?.length||0,total_administration_routes:e.administration_routes?.length||0,therapeutic_classes:e.therapeutic_classes||[],administration_routes:e.administration_routes||[],has_combinations:!1,combination_count:0,is_association_only:e.is_association||!1,individual_ingredients:e.individual_ingredients||[],dcb_codes:e.dcb_codes||[],cas_numbers:e.cas_numbers||[]})));p(a)}catch(e){m({title:"Erro",description:"Erro inesperado ao carregar dados",variant:"destructive"})}finally{L(!1)}},H=async e=>{try{T(!0);const{data:s,error:a}=await b.rpc("get_active_ingredient_combinations");if(a)return void m({title:"Erro",description:"Erro ao carregar dados da combinação",variant:"destructive"});const t=s?.find((s=>s.combination_id===e));if(!t||!t.sample_drugs)return void E([]);const i=t.sample_drugs.map((e=>({id:e.drug_id,brand_name:e.brand_name,descricao:e.descricao,fabricante:e.fabricante,composicao:e.composicao,pregnancy_info:e.pregnancy_info,breastfeeding_info:e.breastfeeding_info,patient_instructions:e.patient_instructions||[],adult_use:e.adult_use,pediatric_use:e.pediatric_use,is_controlled:e.is_controlled,is_high_cost:e.is_high_cost,status:e.status,tipo:e.tipo,titularidade:e.titularidade,eans:e.eans||[],is_association:e.is_association,therapeutic_classes:t.therapeutic_classes||[],presentations:(e.presentations||[]).map((e=>({id:e.id,strength:e.strength||"",dosage_form:e.dosage_form||"",prescription_category:e.prescription_category||"",prescription_type:e.prescription_type||"",therapeutic_classes:e.therapeutic_classes||[],administration_routes:e.administration_routes||[],is_primary:e.is_primary||!1})))})));E(i)}catch(s){m({title:"Erro",description:"Erro inesperado ao carregar medicamentos",variant:"destructive"})}finally{T(!1)}},J=h.filter((e=>e.name?.toLowerCase().includes(V.toLowerCase())||e.dcb_code?.toLowerCase().includes(V.toLowerCase())||e.cas_number?.toLowerCase().includes(V.toLowerCase())));return e.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 rounded-2xl p-8 text-white shadow-xl",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(N,{variant:"ghost",size:"sm",onClick:()=>a("/admin/dashboard"),className:"flex items-center gap-2 hover:bg-white/20 text-white border-white/20",children:[e.jsx(_,{className:"h-4 w-4"}),"Voltar"]}),e.jsxs("div",{children:[e.jsxs("h1",{className:"text-4xl font-bold mb-2 flex items-center gap-3",children:[e.jsx(M,{className:"h-10 w-10"}),"🧬 Formatação de Princípios Ativos"]}),e.jsx("p",{className:"text-emerald-100 text-lg",children:"Use IA para analisar e melhorar dados de princípios ativos"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-2xl font-bold",children:h.length}),e.jsx("div",{className:"text-emerald-100",children:"Princípios Ativos"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsxs(t,{children:[e.jsxs(i,{children:[e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-5 w-5"}),"Princípios Ativos"]}),e.jsx(x,{children:"Selecione um princípio ativo para analisar"})]}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(y,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(w,{type:"search",placeholder:"Buscar princípios ativos...",value:V,onChange:e=>I(e.target.value),className:"pl-10"})]}),e.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:D?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx(g,{className:"h-6 w-6 animate-spin"})}):0===J.length?e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(n,{className:"h-8 w-8 mx-auto mb-2"}),e.jsx("p",{children:"Nenhum princípio ativo encontrado"})]}):J.map((s=>e.jsx(t,{className:"cursor-pointer transition-all hover:shadow-md "+(u?.id===s.id?"ring-2 ring-emerald-500 bg-emerald-50":"hover:bg-gray-50"),onClick:()=>(async e=>{v(e),await H(e.id)})(s),children:e.jsx(l,{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-medium text-sm",children:s.name}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.dcb_code&&e.jsxs(c,{variant:"outline",className:"text-xs",children:["DCB: ",s.dcb_code]}),s.cas_number&&e.jsxs(c,{variant:"outline",className:"text-xs",children:["CAS: ",s.cas_number]}),s.is_association_only&&e.jsx(c,{variant:"secondary",className:"text-xs bg-orange-100 text-orange-700 border-orange-300",children:"Associação"}),s.individual_ingredients&&s.individual_ingredients.length>1&&e.jsxs(c,{variant:"secondary",className:"text-xs bg-blue-100 text-blue-700 border-blue-300",children:[s.individual_ingredients.length," Ingredientes"]}),s.therapeutic_classes&&s.therapeutic_classes.length>0&&e.jsxs(c,{variant:"secondary",className:"text-xs bg-green-100 text-green-700 border-green-300",children:[s.therapeutic_classes.length," Classes"]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-1 text-xs text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(d,{className:"h-3 w-3"}),s.total_drugs||0," medicamentos"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(q,{className:"h-3 w-3"}),s.total_presentations||0," apresentações"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(o,{className:"h-3 w-3"}),s.total_therapeutic_classes||0," classes"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(B,{className:"h-3 w-3"}),s.total_administration_routes||0," vias"]})]})]})})},s.id)))})]})]})}),e.jsx("div",{className:"lg:col-span-2",children:u?e.jsxs(A,{defaultValue:"details",className:"space-y-4",children:[e.jsxs(C,{className:"grid w-full grid-cols-2",children:[e.jsxs(k,{value:"details",className:"flex items-center gap-2",children:[e.jsx(d,{className:"h-4 w-4"}),"Detalhes"]}),e.jsxs(k,{value:"analysis",className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-4 w-4"}),"Análise IA"]})]}),e.jsx(S,{value:"details",children:e.jsx(G,{ingredient:u,relatedDrugs:z,isLoadingDrugs:P})}),e.jsx(S,{value:"analysis",children:e.jsx(O,{ingredient:u,relatedDrugs:z,onDataUpdated:()=>{H(u.id)}})})]}):e.jsx(t,{className:"h-96 flex items-center justify-center",children:e.jsxs(l,{className:"text-center",children:[e.jsx(M,{className:"h-16 w-16 mx-auto mb-4 text-gray-300"}),e.jsx("h3",{className:"text-lg font-medium text-gray-600 mb-2",children:"Selecione um Princípio Ativo"}),e.jsx("p",{className:"text-gray-500",children:"Escolha um princípio ativo da lista para começar a análise com IA"})]})})})]})]})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */export{H as default};
