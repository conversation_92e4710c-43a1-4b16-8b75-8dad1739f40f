import{j as a}from"./radix-core-6kBL75b5.js";import{r as e}from"./critical-DVX9Inzy.js";import{aM as l,ab as s,B as r,U as o,ao as t,ae as i,af as c,ag as m,ah as d,aj as n}from"./index-D89HBjcn.js";import u from"./Footer-CliqTtAT.js";import{L as p}from"./router-BAzpOxbo.js";import{C as x,a as v}from"./calculatorSEOData-DV731ymA.js";import{C as j}from"./chevron-left-CUbzR6vp.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CnktnpVq.js";import"./rocket-BrzzCnNA.js";import"./target-jx5HoNTw.js";import"./zap-3JoB1_vc.js";import"./book-open-JvVCwLVv.js";import"./star-DPJhgPy1.js";import"./circle-help-Bt3O9hrS.js";import"./instagram-BUkbqcNN.js";const b=()=>{const b=v["capurro-neuro"],[g,h]=e.useState({pregas:null,pele:null,orelha:null,glandula:null,xale:null,cabecaPesco:null}),f=(()=>{const a=Object.values(g).reduce(((a,e)=>a+(e||0)),0)+200;return{total:a,weeks:Math.floor(a/7),days:a%7}})(),y=Object.values(g).every((a=>null!==a));return a.jsxs("div",{className:l.gradientBackground("min-h-screen flex flex-col"),children:[a.jsx(x,{...b}),a.jsx(s,{}),a.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:a.jsxs("div",{className:"max-w-3xl mx-auto space-y-8",children:[a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx(p,{to:"/calculadoras",children:a.jsx(r,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:a.jsx(j,{className:"h-5 w-5"})})}),a.jsx("h1",{className:l.gradientHeading("text-3xl"),children:"Capurro Neurológico"})]}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avaliação da idade gestacional com base em critérios neurológicos e somáticos"}),a.jsxs(o,{className:l.card("p-6 space-y-6"),children:[Object.entries({pregas:[{value:0,label:"Sem pregas"},{value:5,label:"Discretas anteriores"},{value:10,label:"Anteriores mais definidas"},{value:15,label:"Sulcos anteriores"},{value:20,label:"Sulcos na metade posterior"}],pele:[{value:0,label:"Fina e gelatinosa"},{value:10,label:"Mais grossa com descamação fina"},{value:15,label:"Grossa com sulcos, descamação de mãos e pés"},{value:20,label:"Grossa e apergaminhada, sulcos profundos"}],orelha:[{value:0,label:"Chata e disforme"},{value:8,label:"Pouco encurvada"},{value:16,label:"Encurvada em toda parte superior"},{value:24,label:"Toda encurvada"}],glandula:[{value:0,label:"Não palpável"},{value:5,label:"< 5 mm"},{value:10,label:"5 a 10 mm"},{value:15,label:"> 10 mm"}],xale:[{value:0,label:"Cotovelo na axila oposta"},{value:6,label:"Cotovelo ultrapassa linha média"},{value:12,label:"Cotovelo na linha média"},{value:16,label:"Cotovelo não atinge a linha média"}],cabecaPesco:[{value:0,label:"Deflexionada a 270°"},{value:4,label:"180° a 270°"},{value:8,label:"180°"},{value:12,label:"< 180°"}]}).map((([e,s])=>a.jsxs("div",{className:"space-y-2",children:[a.jsx(t,{className:"text-base font-medium text-gray-800 dark:text-gray-200",children:e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1").trim()}),a.jsxs(i,{value:g[e]?.toString()||"",onValueChange:a=>((a,e)=>{h((l=>({...l,[a]:parseInt(e,10)})))})(e,a),children:[a.jsx(c,{className:l.select("w-full"),children:a.jsx(m,{placeholder:"Selecione uma opção"})}),a.jsx(d,{children:s.map((e=>a.jsx(n,{value:e.value.toString(),children:e.label},e.value)))})]})]},e))),y&&a.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:a.jsxs("div",{className:"text-center space-y-4",children:[a.jsxs("div",{className:"text-4xl font-bold text-primary dark:text-blue-400",children:[f.weeks," semanas e ",f.days," dias"]}),a.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Total de pontos: ",f.total]}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto",children:"Nota: Este método pode apresentar variações em comparação com outros métodos de avaliação da idade gestacional."})]})})]})]})}),a.jsx(u,{})]})};export{b as default};
