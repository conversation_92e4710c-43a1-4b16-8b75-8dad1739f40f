import{j as e}from"./radix-core-6kBL75b5.js";import{r as a,b as s}from"./critical-DVX9Inzy.js";import{c as r,D as t,e as l,f as i,g as n,B as c,ay as o,m as d,R as m,aB as x,j as h,aI as g,ac as p,a5 as u,X as j,C as b,b6 as f,aa as y}from"./index-CrSshpOb.js";import v from"./Footer-ClHMSbsi.js";import{S as N}from"./skeleton-Cg77x26y.js";import{u as w,a as k,b as C}from"./useNewsletters-CbzT9a9B.js";import{S as z}from"./separator-4vXkjTSb.js";import{C as M}from"./calendar-zjm19wJF.js";import{T as P}from"./tag-DtTlijo6.js";import{E as L}from"./external-link-BmPF7vzZ.js";import{a as S,f as _}from"./date-vendor-BOcTQe0E.js";import{p as R}from"./pt-BR-a_BmBHfW.js";import{C as B}from"./clock-CsU5Tz0c.js";import{L as E}from"./list-_n1PON_B.js";import{F}from"./filter-nqiR8KC6.js";import{C as T}from"./chevron-left-CuYzwBha.js";import{a as A}from"./router-BAzpOxbo.js";import{N as H}from"./newspaper-xBgfKLc-.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=r("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),q=r("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),O=r("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),I=r("Rss",[["path",{d:"M4 11a9 9 0 0 1 9 9",key:"pv89mb"}],["path",{d:"M4 4a16 16 0 0 1 16 16",key:"k0647b"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]]),V=({news:a,open:s,onOpenChange:r})=>a?e.jsx(t,{open:s,onOpenChange:r,children:e.jsx(l,{className:"w-[95vw] max-w-4xl max-h-[80vh] sm:max-h-[90vh] overflow-hidden p-0",children:e.jsxs("div",{className:"overflow-y-auto p-4 sm:p-6 max-h-[75vh] sm:max-h-[85vh]",children:[e.jsxs(i,{className:"space-y-3 mb-4",children:[e.jsx("div",{className:"flex items-start justify-between",children:e.jsx(n,{className:"text-xl sm:text-2xl font-bold leading-tight pr-8",children:a.title})}),e.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400",children:[a.source&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(q,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:a.source})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(M,{className:"w-4 h-4"}),e.jsx("span",{children:(e=>{if(!e)return"";try{const a=new Date(e);return S(a,"EEEE, d 'de' MMMM 'de' yyyy 'às' HH:mm",{locale:R})}catch(a){return""}})(a.pub_date)})]})]}),a.category&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:[e.jsx(P,{className:"w-4 h-4"}),e.jsx("span",{children:"Categorias:"})]}),(()=>{if(!a.category)return null;const s=a.category.split(",").map((e=>e.trim()));return e.jsx("div",{className:"flex flex-wrap gap-2",children:s.map(((a,s)=>e.jsx(o,{variant:"secondary",className:"bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300",children:a},s)))})})()]})]}),e.jsx(z,{className:"my-4"}),a.image_url&&e.jsx("div",{className:"mb-4",children:e.jsx("img",{src:a.image_url,alt:a.title,className:"w-full h-48 sm:h-64 object-cover rounded-lg shadow-sm"})}),a.summary&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Resumo"}),e.jsx("div",{className:"prose prose-sm sm:prose prose-gray dark:prose-invert max-w-none",children:e.jsx("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed text-sm sm:text-base",children:a.summary})})]}),a.content&&e.jsxs("div",{className:"space-y-2 mt-4",children:[e.jsx("h3",{className:"text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Conteúdo Completo"}),e.jsx("div",{className:"prose prose-sm sm:prose prose-gray dark:prose-invert max-w-none",children:e.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed text-sm sm:text-base",dangerouslySetInnerHTML:{__html:a.content}})})]}),e.jsx("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3",children:[e.jsx(c,{variant:"outline",onClick:()=>r(!1),className:"flex-1 sm:flex-none order-2 sm:order-1 h-12 sm:h-10 text-base sm:text-sm",children:"Fechar"}),a.link&&e.jsxs(c,{onClick:()=>{a.link&&window.open(a.link,"_blank","noopener,noreferrer")},className:"bg-blue-500 hover:bg-blue-600 text-white flex-1 sm:flex-none order-1 sm:order-2 h-12 sm:h-10 text-base sm:text-sm",children:[e.jsx(L,{className:"w-5 h-5 sm:w-4 sm:h-4 mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:"Ler Artigo Completo"}),e.jsx("span",{className:"sm:hidden",children:"Ler Artigo"})]})]})})]})})}):null,G=({news:s,index:r,variant:t="standard"})=>{const[l,i]=a.useState(!1),[n,g]=a.useState(!1),p=a.useRef(null),u=e=>{if(!e)return"";try{const a=new Date(e);return _(a,{locale:R,addSuffix:!0})}catch(a){return""}},j=()=>{s.link&&window.open(s.link,"_blank","noopener,noreferrer")},b=()=>{g(!0)},f=()=>{if(!s.category)return null;const a=s.category.split(",").map((e=>e.trim())).slice(0,2);return e.jsx("div",{className:"flex flex-wrap gap-1",children:a.map(((a,s)=>e.jsx(o,{variant:"secondary",className:h("text-[10px] px-2 py-0.5 font-medium transition-all duration-200","featured"===t?"bg-white/90 text-gray-700 hover:bg-white":"bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300"),children:a},s)))})};return"featured"===t?e.jsxs(e.Fragment,{children:[e.jsx(d.div,{ref:p,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*r},className:"col-span-full md:col-span-2 lg:col-span-3",onHoverStart:()=>i(!0),onHoverEnd:()=>i(!1),children:e.jsxs(m,{className:"relative overflow-hidden h-[400px] group cursor-pointer",onClick:b,children:[s.image_url&&e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{src:s.image_url,alt:s.title,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/30"})]}),e.jsx("div",{className:"relative h-full flex flex-col justify-end p-6 text-white",children:e.jsxs(d.div,{animate:{y:l?-10:0},transition:{duration:.3},children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2 bg-yellow-500/20 backdrop-blur-sm px-3 py-1 rounded-full border border-yellow-400/30",children:[e.jsx(x,{className:"w-4 h-4 text-yellow-300"}),e.jsx("span",{className:"text-sm font-medium text-yellow-300",children:"Destaque"})]}),f()]}),e.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-3 leading-tight text-white drop-shadow-lg",children:s.title}),e.jsx("p",{className:"text-gray-100 mb-4 line-clamp-2 text-lg drop-shadow-md",children:s.summary}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-200 drop-shadow-sm",children:[s.source&&e.jsx("span",{className:"font-medium bg-black/30 px-2 py-1 rounded",children:s.source}),e.jsxs("div",{className:"flex items-center gap-1 bg-black/30 px-2 py-1 rounded",children:[e.jsx(B,{className:"w-3 h-3"}),e.jsx("span",{children:u(s.pub_date)})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(c,{variant:"ghost",size:"sm",className:"text-white hover:bg-white/20 backdrop-blur-sm border border-white/20",onClick:e=>{e.stopPropagation(),b()},children:e.jsx(D,{className:"w-4 h-4"})}),e.jsxs(c,{className:"bg-white/90 text-black hover:bg-white backdrop-blur-sm shadow-lg",onClick:e=>{e.stopPropagation(),j()},children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),"Ler Artigo"]})]})]})]})})]})}),e.jsx(V,{news:s,open:n,onOpenChange:g})]}):e.jsxs(e.Fragment,{children:[e.jsx(d.div,{ref:p,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.05*r},onHoverStart:()=>i(!0),onHoverEnd:()=>i(!1),className:"col-span-1",children:e.jsxs(m,{className:h("overflow-hidden group cursor-pointer transition-all duration-300",(()=>{switch(t){case"featured":return"h-[400px]";case"compact":return"h-[200px]";default:return"h-[320px]"}})(),l&&"shadow-xl shadow-blue-500/10 -translate-y-1"),onClick:b,children:[s.image_url&&e.jsxs("div",{className:h("relative overflow-hidden",(()=>{switch(t){case"featured":return"h-48";case"compact":return"h-24";default:return"h-40"}})()),children:[e.jsx("img",{src:s.image_url,alt:s.title,className:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"}),e.jsx("div",{className:"absolute top-2 right-2",children:f()}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),e.jsxs("div",{className:"p-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"flex items-center justify-between mb-2 text-xs text-gray-500 dark:text-gray-400",children:e.jsxs("div",{className:"flex items-center gap-2",children:[s.source&&e.jsx("span",{className:"font-medium",children:s.source}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(B,{className:"w-3 h-3"}),e.jsx("span",{children:u(s.pub_date)})]})]})}),e.jsx("h3",{className:h("font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2 leading-tight","compact"===t?"text-sm":"text-base"),children:s.title}),"compact"!==t&&e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-3",children:s.summary}),"compact"!==t&&!s.image_url&&f()]}),e.jsxs("div",{className:"flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-800",children:[e.jsxs(c,{variant:"ghost",size:"sm",className:"text-xs",onClick:e=>{e.stopPropagation(),b()},children:[e.jsx(D,{className:"w-4 h-4 mr-1"}),"Ver Detalhes"]}),e.jsxs(c,{size:"sm",className:"bg-blue-500 hover:bg-blue-600 text-white",onClick:e=>{e.stopPropagation(),j()},children:[e.jsx(L,{className:"w-3 h-3 mr-1"}),"Ler"]})]})]})]})}),e.jsx(V,{news:s,open:n,onOpenChange:g})]})},$=({onFilterChange:s,onLayoutChange:r,currentLayout:t})=>{const[l,i]=a.useState({search:"",category:"",sortBy:"recent",timeRange:"all"}),[n,m]=a.useState(!1),x=g(l.search,300);a.useEffect((()=>{if(x!==l.search){const e={...l,search:x};s(e)}}),[x]);const{data:b}=w(),f=b?.map((e=>e.name))||[],y=e=>{const a={...l,...e};i(a),s(a)},v=l.search||l.category||"recent"!==l.sortBy||"all"!==l.timeRange;return e.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 sm:p-4 mb-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center justify-center gap-3 sm:gap-4 max-w-4xl mx-auto",children:[e.jsxs("div",{className:"relative flex-1 sm:w-80 sm:flex-none",children:[e.jsx(p,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),e.jsx(u,{placeholder:"Buscar notícias...",value:l.search,onChange:e=>y({search:e.target.value}),className:"pl-10 h-10 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"})]}),e.jsxs("div",{className:"flex items-center justify-between sm:justify-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[v&&e.jsxs(o,{variant:"secondary",className:"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 text-xs",children:[[l.search,l.category,"all"!==l.timeRange?"Período":""].filter(Boolean).length," filtro(s)"]}),v&&e.jsxs(c,{variant:"ghost",size:"sm",onClick:()=>{const e={search:"",category:"",sortBy:"recent",timeRange:"all"};i(e),s(e)},className:"h-9 px-2 sm:px-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:[e.jsx(j,{className:"w-4 h-4 sm:mr-1"}),e.jsx("span",{className:"hidden sm:inline",children:"Limpar"})]})]}),e.jsxs("div",{className:"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:[e.jsx(c,{variant:"grid"===t?"default":"ghost",size:"sm",onClick:()=>r("grid"),className:"h-8 w-8 p-0",children:e.jsx(O,{className:"w-4 h-4"})}),e.jsx(c,{variant:"list"===t?"default":"ghost",size:"sm",onClick:()=>r("list"),className:"h-8 w-8 p-0",children:e.jsx(E,{className:"w-4 h-4"})})]}),e.jsx(c,{variant:"ghost",size:"sm",onClick:()=>m(!n),className:"h-9 w-9 p-0",children:e.jsx(F,{className:h("w-4 h-4 transition-transform",n&&"rotate-180")})})]})]}),e.jsx(d.div,{initial:!1,animate:{height:n?"auto":0,opacity:n?1:0},transition:{duration:.3},className:"overflow-hidden",children:e.jsxs("div",{className:"space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e.jsx(M,{className:"w-4 h-4 inline mr-1"}),"Período"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:[{value:"today",label:"Hoje"},{value:"week",label:"Esta Semana"},{value:"month",label:"Este Mês"},{value:"all",label:"Todos"}].map((a=>e.jsx(c,{variant:l.timeRange===a.value?"default":"outline",size:"sm",onClick:()=>y({timeRange:a.value}),className:"text-xs",children:a.label},a.value)))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e.jsx(P,{className:"w-4 h-4 inline mr-1"}),"Categorias"]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(c,{variant:l.category?"outline":"default",size:"sm",onClick:()=>y({category:""}),className:"text-xs",children:"Todas"}),f.map((a=>e.jsx(c,{variant:l.category===a?"default":"outline",size:"sm",onClick:()=>y({category:l.category===a?"":a}),className:"text-xs",children:a},a)))]})]})]})})]})},U=({currentPage:a,totalPages:r,totalItems:t,itemsPerPage:l,onPageChange:i})=>{const n=(a-1)*l+1,o=Math.min(a*l,t);return r<=1?null:e.jsxs(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"flex flex-col sm:flex-row items-center justify-between gap-4 mt-8 p-4 bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Mostrando ",e.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:n})," a"," ",e.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:o})," de"," ",e.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:t})," notícias"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(c,{variant:"outline",size:"sm",onClick:()=>i(a-1),disabled:1===a,className:"h-9 w-9 p-0",children:e.jsx(T,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center gap-1",children:(()=>{const e=[],s=[];for(let t=Math.max(2,a-2);t<=Math.min(r-1,a+2);t++)e.push(t);return a-2>2?s.push(1,"..."):s.push(1),s.push(...e),a+2<r-1?s.push("...",r):r>1&&s.push(r),s})().map(((r,t)=>e.jsx(s.Fragment,{children:"..."===r?e.jsx("div",{className:"flex items-center justify-center h-9 w-9",children:e.jsx(D,{className:"h-4 w-4 text-gray-400"})}):e.jsx(c,{variant:a===r?"default":"outline",size:"sm",onClick:()=>i(r),className:h("h-9 w-9 p-0",a===r&&"bg-blue-500 hover:bg-blue-600 text-white"),children:r})},t)))}),e.jsx(c,{variant:"outline",size:"sm",onClick:()=>i(a+1),disabled:a===r,className:"h-9 w-9 p-0",children:e.jsx(b,{className:"h-4 w-4"})})]})]})},Z=()=>{const s=A(),[r,t]=a.useState({search:"",category:"",sortBy:"recent",timeRange:"all"}),[l,i]=a.useState("grid"),[n,o]=a.useState(1),m="list"===l||1===n?10:12,h=1===n?0:10+12*(n-2),{data:g,isLoading:p,isError:u,error:j}=k({limit:m,offset:h,category:r.category||void 0,searchTerm:r.search||void 0}),{data:b,isLoading:w}=C({category:r.category||void 0,searchTerm:r.search||void 0}),z=(()=>{if(!b)return 0;if("list"===l)return Math.ceil(b/10);if(b<=10)return 1;const e=b-10;return 1+Math.ceil(e/12)})(),M=e=>{t(e),o(1)};return e.jsxs("div",{className:"flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 dark:from-slate-900 dark:via-slate-800/50 dark:to-slate-900",children:[e.jsxs(f,{children:[e.jsx("title",{children:"Notícias Diárias | PedBook"}),e.jsx("meta",{name:"description",content:"Descubra as últimas atualizações e novidades do mundo da medicina pediátrica com nossa experiência revolucionária de notícias."})]}),e.jsx(y,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx(d.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-6 mt-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(c,{variant:"ghost",onClick:()=>s("/"),className:"flex items-center gap-2 h-9 px-3 hover:bg-blue-50/60 dark:hover:bg-slate-800/60 transition-all duration-300 hover:scale-105 rounded-lg","aria-label":"Voltar ao menu inicial",children:[e.jsx(T,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline text-sm font-medium",children:"Voltar"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-blue-500 rounded-full blur-lg opacity-30 animate-pulse"}),e.jsx(H,{className:"relative h-6 w-6 text-blue-500"})]}),e.jsx("h1",{className:"text-xl md:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-400",children:"Notícias Médicas"})]}),e.jsxs("div",{className:"flex items-center gap-2 bg-yellow-50 dark:bg-yellow-900/20 px-3 py-1.5 rounded-full",children:[e.jsx(x,{className:"w-3 h-3 text-yellow-500"}),e.jsx("span",{className:"hidden sm:inline text-xs font-medium text-gray-600 dark:text-gray-400",children:"Atualizado às 07:00"}),e.jsx("span",{className:"sm:hidden text-xs font-medium text-gray-600 dark:text-gray-400",children:"Atualizado 07:00"})]})]})}),e.jsx("div",{children:e.jsx($,{onFilterChange:M,onLayoutChange:e=>{i(e),o(1)},currentLayout:l})}),e.jsx("div",{children:p?e.jsx("div",{className:"grid"===l?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:[...Array(6)].map(((a,s)=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden",children:[e.jsx(N,{className:"h-40"}),e.jsxs("div",{className:"p-4 space-y-3",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{className:"h-5 w-16 rounded-full"}),e.jsx(N,{className:"h-5 w-20 rounded-full"})]}),e.jsx(N,{className:"h-6 w-full"}),e.jsx(N,{className:"h-4 w-full"}),e.jsx(N,{className:"h-4 w-3/4"}),e.jsxs("div",{className:"flex justify-between items-center pt-2",children:[e.jsx(N,{className:"h-8 w-20"}),e.jsx(N,{className:"h-8 w-8 rounded-full"})]})]})]},s)))}):u?e.jsx("div",{className:"text-center py-16",children:e.jsxs("div",{className:"bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-8 rounded-2xl border border-red-200 dark:border-red-800/50 max-w-md mx-auto",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(I,{className:"w-8 h-8 text-red-500"})}),e.jsx("h3",{className:"text-xl font-bold text-red-800 dark:text-red-300 mb-3",children:"Ops! Algo deu errado"}),e.jsx("p",{className:"text-red-600 dark:text-red-400 mb-4",children:"Não conseguimos carregar as notícias no momento. Nossa equipe já foi notificada."}),e.jsxs(c,{variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30",onClick:()=>window.location.reload(),children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Tentar Novamente"]})]})}):g&&g.length>0?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid"===l?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:[g.length>0&&"grid"===l&&1===n&&e.jsx(G,{news:g[0],index:0,variant:"featured"},`featured-${g[0].id}`),g.slice("grid"===l&&1===n?1:0).map(((a,s)=>e.jsx(G,{news:a,index:"grid"===l&&1===n?s+1:s,variant:"list"===l?"compact":"standard"},a.id)))]}),b&&z>1&&e.jsx(U,{currentPage:n,totalPages:z,totalItems:b,itemsPerPage:"grid"===l?1===n?10:12:10,onPageChange:e=>{o(e),window.scrollTo({top:0,behavior:"smooth"})}})]}):e.jsx("div",{className:"text-center py-16",children:e.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl border border-blue-200 dark:border-blue-800/50 max-w-md mx-auto",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(x,{className:"w-8 h-8 text-blue-500"})}),e.jsx("h3",{className:"text-xl font-bold text-blue-800 dark:text-blue-300 mb-3",children:"Nenhuma notícia encontrada"}),e.jsx("p",{className:"text-blue-600 dark:text-blue-400 mb-4",children:"Tente ajustar os filtros ou volte mais tarde para novas atualizações."}),e.jsxs(c,{variant:"outline",className:"border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30",onClick:()=>M({search:"",category:"",sortBy:"recent",timeRange:"all"}),children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Limpar Filtros"]})]})})})]})}),e.jsx(v,{})]})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */export{Z as default};
