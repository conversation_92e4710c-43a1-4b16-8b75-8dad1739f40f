import{r as e,b as r}from"./critical-DVX9Inzy.js";import{j as t,u as o,S as n,P as c}from"./radix-core-6kBL75b5.js";function s(...r){const t=r[0];if(1===r.length)return t;const o=()=>{const o=r.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(r){const n=o.reduce(((e,{useScope:t,scopeName:o})=>({...e,...t(r)[`__scope${o}`]})),{});return e.useMemo((()=>({[`__scope${t.scopeName}`]:n})),[n])}};return o.scopeName=t.scopeName,o}function i(c){const i=c+"CollectionProvider",[a,l]=function(r,o=[]){let n=[];const c=()=>{const t=n.map((r=>e.createContext(r)));return function(o){const n=o?.[r]||t;return e.useMemo((()=>({[`__scope${r}`]:{...o,[r]:n}})),[o,n])}};return c.scopeName=r,[function(o,c){const s=e.createContext(c),i=n.length;function a(o){const{scope:n,children:c,...a}=o,l=n?.[r][i]||s,u=e.useMemo((()=>a),Object.values(a));return t.jsx(l.Provider,{value:u,children:c})}return n=[...n,c],a.displayName=o+"Provider",[a,function(t,n){const a=n?.[r][i]||s,l=e.useContext(a);if(l)return l;if(void 0!==c)return c;throw new Error(`\`${t}\` must be used within \`${o}\``)}]},s(c,...o)]}(i),[u,f]=a(i,{collectionRef:{current:null},itemMap:new Map}),p=e=>{const{scope:o,children:n}=e,c=r.useRef(null),s=r.useRef(new Map).current;return t.jsx(u,{scope:o,itemMap:s,collectionRef:c,children:n})};p.displayName=i;const d=c+"CollectionSlot",m=r.forwardRef(((e,r)=>{const{scope:c,children:s}=e,i=f(d,c),a=o(r,i.collectionRef);return t.jsx(n,{ref:a,children:s})}));m.displayName=d;const h=c+"CollectionItemSlot",w="data-radix-collection-item",x=r.forwardRef(((e,c)=>{const{scope:s,children:i,...a}=e,l=r.useRef(null),u=o(c,l),p=f(h,s);return r.useEffect((()=>(p.itemMap.set(l,{ref:l,...a}),()=>{p.itemMap.delete(l)}))),t.jsx(n,{[w]:"",ref:u,children:i})}));return x.displayName=h,[{Provider:p,Slot:m,ItemSlot:x},function(e){const t=f(c+"CollectionConsumer",e);return r.useCallback((()=>{const e=t.collectionRef.current;if(!e)return[];const r=Array.from(e.querySelectorAll(`[${w}]`));return Array.from(t.itemMap.values()).sort(((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current)))}),[t.collectionRef,t.itemMap])},l]}var a=e.forwardRef(((e,r)=>t.jsx(c.span,{...e,ref:r,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));a.displayName="VisuallyHidden";var l=a;export{l as R,a as V,i as c};
