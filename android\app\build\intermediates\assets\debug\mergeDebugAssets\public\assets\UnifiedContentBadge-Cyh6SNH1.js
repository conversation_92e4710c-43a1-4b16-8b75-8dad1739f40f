import{j as e}from"./radix-core-6kBL75b5.js";import{r}from"./critical-DVX9Inzy.js";import{u as t}from"./query-vendor-B-7l6Nb3.js";import{s as a,m as i,j as o}from"./index-DV3Span9.js";import{a as s}from"./useNewsletters-BsfaylSD.js";import{a as d}from"./router-BAzpOxbo.js";import{N as n}from"./newspaper-xIdoo9t3.js";import{D as l}from"./droplets-BXkKjGxJ.js";import{I as p}from"./instagram-BdLPZF9i.js";import{E as c}from"./external-link-C9J9ACvb.js";import{f as m}from"./date-vendor-BOcTQe0E.js";import{p as u}from"./pt-BR-a_BmBHfW.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const b=e=>{try{return m(e,{locale:u,addSuffix:!0}).replace("cerca de ","").replace(" atrás","").replace("há ","")}catch{return"agora"}},x=(e,r=35)=>e.length<=r?e:e.substring(0,r)+"...",k=({type:t,title:a,fullTitle:i,date:s,link:d,isStatic:m=!1,postId:u,onNavigate:x,onInstagramClick:k})=>{const f=r.useCallback((async e=>{if(e&&(e.preventDefault(),e.stopPropagation()),"news"===t)x("/newsletters");else if("pedidrop"===t);else if("instagram"===t&&d){if(u&&k)try{await k(u)}catch(r){}window.open(d,"_blank")}}),[t,a,d,u,x,k]);return e.jsxs("div",{onClick:f,className:o("flex items-center gap-1.5 px-2.5 py-1 rounded-full cursor-pointer group","backdrop-blur-sm border shadow-sm","transition-all duration-200 ease-out","flex-1 min-w-0 max-w-fit","hover:scale-105 active:scale-95","news"===t?"bg-blue-50/80 dark:bg-blue-950/40 border-blue-200/50 dark:border-blue-800/30 hover:bg-blue-100/90 dark:hover:bg-blue-900/50 hover:border-blue-300/60 dark:hover:border-blue-700/40 hover:shadow-md":"pedidrop"===t?"bg-gradient-to-r from-blue-50/80 via-purple-50/80 to-indigo-50/80 dark:from-blue-950/40 dark:via-purple-950/40 dark:to-indigo-950/40 border-blue-200/50 dark:border-blue-800/30 hover:from-blue-100/90 hover:via-purple-100/90 hover:to-indigo-100/90 dark:hover:from-blue-900/50 dark:hover:via-purple-900/50 dark:hover:to-indigo-900/50 hover:border-blue-300/60 dark:hover:border-blue-700/40 hover:shadow-md":"bg-pink-50/80 dark:bg-pink-950/40 border-pink-200/50 dark:border-pink-800/30 hover:bg-pink-100/90 dark:hover:bg-pink-900/50 hover:border-pink-300/60 dark:hover:border-pink-700/40 hover:shadow-md"),children:[e.jsx("div",{className:"flex-shrink-0",children:"news"===t?e.jsx("div",{className:"p-0.5 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600",children:e.jsx(n,{className:"h-2.5 w-2.5 text-white"})}):"pedidrop"===t?e.jsx("div",{className:"p-0.5 rounded-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600",children:e.jsx(l,{className:"h-2.5 w-2.5 text-white"})}):e.jsx("div",{className:"p-0.5 rounded-full bg-gradient-to-br from-pink-500 to-purple-600",children:e.jsx(p,{className:"h-2.5 w-2.5 text-white"})})}),e.jsx("span",{className:o("text-[11px] font-medium truncate transition-colors duration-200","news"===t||"pedidrop"===t?"text-blue-700 dark:text-blue-300 group-hover:text-blue-800 dark:group-hover:text-blue-200":"text-pink-700 dark:text-pink-300 group-hover:text-pink-800 dark:group-hover:text-pink-200"),children:a}),!m&&s&&e.jsxs("div",{className:"flex items-center gap-0.5 flex-shrink-0",children:[e.jsx("div",{className:o("w-1 h-1 rounded-full","news"===t||"pedidrop"===t?"bg-blue-400":"bg-pink-400")}),e.jsx("span",{className:"text-[8px] text-gray-500 dark:text-gray-400",children:b(s)})]}),"instagram"===t&&e.jsx(c,{className:"h-2.5 w-2.5 text-pink-400 dark:text-pink-300 group-hover:scale-110 transition-transform duration-200 flex-shrink-0"})]})},f=()=>{const o=d(),{data:n}=s({limit:1}),{data:l}=t({queryKey:["latest-instagram-post"],queryFn:async()=>{try{const{data:e,error:r}=await a.from("pedbook_site_instagram_posts").select("id, title, link, created_at, post_date, click_count").order("created_at",{ascending:!1}).limit(1).single();if(r){if("PGRST116"===r.code)return null;throw r}const t=e.post_date||e.created_at;return{...e,effective_date:t}}catch(e){return null}},retry:1,staleTime:3e5}),p=r.useCallback((e=>{o(e)}),[o]),c=r.useCallback((async e=>{try{const{data:r,error:t}=await a.rpc("increment_instagram_click",{post_id:e})}catch(r){}}),[]);return r.useMemo((()=>"Notícias"),[]),n?.[0]||l?e.jsx(i.div,{initial:{opacity:0,y:4},animate:{opacity:1,y:0},transition:{duration:.3,ease:"easeOut"},className:"flex justify-center",children:e.jsx("div",{className:"flex items-center justify-center gap-2 max-w-[90%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%]",children:l&&e.jsx(k,{type:"instagram",title:x(l.title,25),fullTitle:l.title,date:new Date(l.effective_date),link:l.link,postId:l.id,onNavigate:p,onInstagramClick:c})})}):null};export{f as UnifiedContentBadge};
