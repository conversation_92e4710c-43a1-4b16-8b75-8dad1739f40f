import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{aM as s,aa as r,B as l,R as t,aT as o,aU as i,an as n}from"./index-Dq2DDcRF.js";import{L as c}from"./router-BAzpOxbo.js";import{C as d,a as m}from"./calculatorSEOData-BNjt_MvV.js";import{C as x}from"./chevron-left-BRVekuj0.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const u=()=>{const u=m.apgar,[v,p]=a.useState({heartRate:"",breathing:"",muscleTone:"",skinColor:"",reflexes:""}),j=Object.values(v).every((e=>""!==e)),h=Object.values(v).reduce(((e,a)=>e+(parseInt(a)||0)),0),b=(g=h)>=7?{text:"Normal",color:"text-green-600"}:g>=4?{text:"Alterado (Baixo)",color:"text-yellow-600"}:{text:"Crítico",color:"text-red-600"};var g;return e.jsxs("div",{className:s.gradientBackground("min-h-screen flex flex-col"),children:[e.jsx(d,{...u}),e.jsx(r,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(c,{to:"/calculadoras",children:e.jsx(l,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:e.jsx(x,{className:"h-5 w-5"})})}),e.jsx("h1",{className:s.gradientHeading("text-3xl"),children:"Calculadora de APGAR"})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avaliação da vitalidade de recém-nascidos baseada em cinco parâmetros clínicos"}),e.jsxs(t,{className:s.card("p-6 space-y-8"),children:[[{name:"heartRate",title:"Frequência Cardíaca",options:[{value:"0",label:"Ausente"},{value:"1",label:"Menor que 100/minuto"},{value:"2",label:"Maior que 100/minuto"}]},{name:"breathing",title:"Respiração",options:[{value:"0",label:"Ausente"},{value:"1",label:"Fraca/Irregular"},{value:"2",label:"Forte/Choro"}]},{name:"muscleTone",title:"Tônus Muscular",options:[{value:"0",label:"Flácido"},{value:"1",label:"Flexão de pernas e braços"},{value:"2",label:"Movimento ativo/Boa flexão"}]},{name:"skinColor",title:"Cor da Pele",options:[{value:"0",label:"Cianótico/Pálido"},{value:"1",label:"Cianose de extremidades"},{value:"2",label:"Rosado"}]},{name:"reflexes",title:"Irritabilidade Reflexa",options:[{value:"0",label:"Ausente"},{value:"1",label:"Algum movimento"},{value:"2",label:"Espirros/Choro"}]}].map((a=>e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-800 dark:text-gray-100",children:a.title}),e.jsx(o,{value:v[a.name],onValueChange:e=>p((s=>({...s,[a.name]:e}))),className:"space-y-2",children:a.options.map((s=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(i,{value:s.value,id:`${a.name}-${s.value}`}),e.jsxs(n,{htmlFor:`${a.name}-${s.value}`,className:"text-gray-700 dark:text-gray-300",children:[s.label," (",s.value," ","1"===s.value?"ponto":"pontos",")"]})]},s.value)))})]},a.name))),j&&e.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{className:"text-4xl font-bold text-primary dark:text-blue-400",children:[h," pontos"]}),e.jsx("div",{className:`text-xl font-semibold ${b.color}`,children:b.text}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:h>=7?"Considerado adequado e sem necessidade de intervenção.":h>=4?"Necessita acompanhamento próximo e possível intervenção médica.":"Necessita intervenção de urgência."})]})})]})]})})]})};export{u as default};
