import{r as e,b as t,a as n}from"./critical-DVX9Inzy.js";const r="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function o(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function i(e){return"nodeType"in e}function a(e){var t,n;return e?o(e)?e:i(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function l(e){const{Document:t}=a(e);return e instanceof t}function s(e){return!o(e)&&e instanceof a(e).HTMLElement}function c(e){return e instanceof a(e).SVGElement}function u(e){return e?o(e)?e.document:i(e)?l(e)?e:s(e)||c(e)?e.ownerDocument:document:document:document}const d=r?e.useLayoutEffect:e.useEffect;function f(t){const n=e.useRef(t);return d((()=>{n.current=t})),e.useCallback((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}function h(t,n){void 0===n&&(n=[t]);const r=e.useRef(t);return d((()=>{r.current!==t&&(r.current=t)}),n),r}function v(t,n){const r=e.useRef();return e.useMemo((()=>{const e=t(r.current);return r.current=e,e}),[...n])}function g(t){const n=f(t),r=e.useRef(null),o=e.useCallback((e=>{e!==r.current&&(null==n||n(e,r.current)),r.current=e}),[]);return[r,o]}function p(t){const n=e.useRef();return e.useEffect((()=>{n.current=t}),[t]),n.current}let b={};function m(t,n){return e.useMemo((()=>{if(n)return n;const e=null==b[t]?0:b[t]+1;return b[t]=e,t+"-"+e}),[t,n])}function y(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[o,i]of r){const n=t[o];null!=n&&(t[o]=n+e*i)}return t}),{...t})}}const w=y(1),x=y(-1);function D(e){if(!e)return!1;const{KeyboardEvent:t}=a(e.target);return t&&e instanceof t}function C(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=a(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const R=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[R.Translate.toString(e),R.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),E="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function S(e){return e.matches(E)?e:e.querySelector(E)}const M={display:"none"};function I(e){let{id:n,value:r}=e;return t.createElement("div",{id:n,style:M},r)}function N(e){let{id:n,announcement:r,ariaLiveType:o="assertive"}=e;return t.createElement("div",{id:n,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":o,"aria-atomic":!0},r)}const T=e.createContext(null),k={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},A={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function O(r){let{announcements:o=A,container:i,hiddenTextDescribedById:a,screenReaderInstructions:l=k}=r;const{announce:s,announcement:c}=function(){const[t,n]=e.useState("");return{announce:e.useCallback((e=>{null!=e&&n(e)}),[]),announcement:t}}(),u=m("DndLiveRegion"),[d,f]=e.useState(!1);if(e.useEffect((()=>{f(!0)}),[]),function(t){const n=e.useContext(T);e.useEffect((()=>{if(!n)throw new Error("useDndMonitor must be used within a children of <DndContext>");return n(t)}),[t,n])}(e.useMemo((()=>({onDragStart(e){let{active:t}=e;s(o.onDragStart({active:t}))},onDragMove(e){let{active:t,over:n}=e;o.onDragMove&&s(o.onDragMove({active:t,over:n}))},onDragOver(e){let{active:t,over:n}=e;s(o.onDragOver({active:t,over:n}))},onDragEnd(e){let{active:t,over:n}=e;s(o.onDragEnd({active:t,over:n}))},onDragCancel(e){let{active:t,over:n}=e;s(o.onDragCancel({active:t,over:n}))}})),[s,o])),!d)return null;const h=t.createElement(t.Fragment,null,t.createElement(I,{id:a,value:l.draggable}),t.createElement(N,{id:u,announcement:c}));return i?n.createPortal(h,i):h}var L,B;function P(){}function z(t,n){return e.useMemo((()=>({sensor:t,options:null!=n?n:{}})),[t,n])}function F(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.useMemo((()=>[...n].filter((e=>null!=e))),[...n])}(B=L||(L={})).DragStart="dragStart",B.DragMove="dragMove",B.DragEnd="dragEnd",B.DragCancel="dragCancel",B.DragOver="dragOver",B.RegisterDroppable="registerDroppable",B.SetDroppableDisabled="setDroppableDisabled",B.UnregisterDroppable="unregisterDroppable";const j=Object.freeze({x:0,y:0});function U(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function X(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function Y(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function K(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function W(e,t){if(!e||0===e.length)return null;const[n]=e;return n[t]}function H(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}const q=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=H(t,t.left,t.top),i=[];for(const a of r){const{id:e}=a,t=n.get(e);if(t){const n=U(H(t),o);i.push({id:e,data:{droppableContainer:a,value:n}})}}return i.sort(X)};function J(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,l=i-n;if(r<o&&n<i){const n=t.width*t.height,r=e.width*e.height,o=a*l;return Number((o/(n+r-o)).toFixed(4))}return 0}const V=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:e}=i,r=n.get(e);if(r){const n=J(r,t);n>0&&o.push({id:e,data:{droppableContainer:i,value:n}})}}return o.sort(Y)};function _(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:j}function G(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const Q=G(1),Z={ignoreTransform:!1};function $(e,t){void 0===t&&(t=Z);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=a(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=function(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:l}=r,s=e.left-a-(1-o)*parseFloat(n),c=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:u,height:d,top:c,right:s+u,bottom:c+d,left:s}}(n,t,r))}const{top:r,left:o,width:i,height:l,bottom:s,right:c}=n;return{top:r,left:o,width:i,height:l,bottom:s,right:c}}function ee(e){return $(e,{ignoreTransform:!0})}function te(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if(l(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!s(o)||c(o))return n;if(n.includes(o))return n;const i=a(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=a(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"==typeof r&&n.test(r)}))}(o,i)&&n.push(o),function(e,t){return void 0===t&&(t=a(e).getComputedStyle(e)),"fixed"===t.position}(o,i)?n:r(o.parentNode)}(e):n}function ne(e){const[t]=te(e,1);return null!=t?t:null}function re(e){return r&&e?o(e)?e:i(e)?l(e)||e===u(e).scrollingElement?window:s(e)?e:null:null:null}function oe(e){return o(e)?e.scrollX:e.scrollLeft}function ie(e){return o(e)?e.scrollY:e.scrollTop}function ae(e){return{x:oe(e),y:ie(e)}}var le,se;function ce(e){return!(!r||!e)&&e===document.scrollingElement}function ue(e){const t={x:0,y:0},n=ce(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}(se=le||(le={}))[se.Forward=1]="Forward",se[se.Backward=-1]="Backward";const de={x:.2,y:.2};function fe(e,t,n,r,o){let{top:i,left:a,right:l,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=de);const{isTop:c,isBottom:u,isLeft:d,isRight:f}=ue(e),h={x:0,y:0},v={x:0,y:0},g=t.height*o.y,p=t.width*o.x;return!c&&i<=t.top+g?(h.y=le.Backward,v.y=r*Math.abs((t.top+g-i)/g)):!u&&s>=t.bottom-g&&(h.y=le.Forward,v.y=r*Math.abs((t.bottom-g-s)/g)),!f&&l>=t.right-p?(h.x=le.Forward,v.x=r*Math.abs((t.right-p-l)/p)):!d&&a<=t.left+p&&(h.x=le.Backward,v.x=r*Math.abs((t.left+p-a)/p)),{direction:h,speed:v}}function he(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function ve(e){return e.reduce(((e,t)=>w(e,ae(t))),j)}const ge=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+oe(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+ie(t)),0)}]];class pe{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=te(t),r=ve(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[o,i,a]of ge)for(const e of i)Object.defineProperty(this,e,{get:()=>{const t=a(n),i=r[o]-t;return this.rect[e]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class be{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function me(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var ye,we,xe,De;function Ce(e){e.preventDefault()}function Re(e){e.stopPropagation()}(we=ye||(ye={})).Click="click",we.DragStart="dragstart",we.Keydown="keydown",we.ContextMenu="contextmenu",we.Resize="resize",we.SelectionChange="selectionchange",we.VisibilityChange="visibilitychange",(De=xe||(xe={})).Space="Space",De.Down="ArrowDown",De.Right="ArrowRight",De.Left="ArrowLeft",De.Up="ArrowUp",De.Esc="Escape",De.Enter="Enter",De.Tab="Tab";const Ee={start:[xe.Space,xe.Enter],cancel:[xe.Esc],end:[xe.Space,xe.Enter,xe.Tab]},Se=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case xe.Right:return{...n,x:n.x+25};case xe.Left:return{...n,x:n.x-25};case xe.Down:return{...n,y:n.y+25};case xe.Up:return{...n,y:n.y-25}}};class Me{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new be(u(t)),this.windowListeners=new be(a(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(ye.Resize,this.handleCancel),this.windowListeners.add(ye.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(ye.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=$),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);ne(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(j)}handleKeyDown(e){if(D(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=Ee,coordinateGetter:i=Se,scrollBehavior:a="smooth"}=r,{code:l}=e;if(o.end.includes(l))return void this.handleEnd(e);if(o.cancel.includes(l))return void this.handleCancel(e);const{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:j;this.referenceCoordinates||(this.referenceCoordinates=c);const u=i(e,{active:t,context:n.current,currentCoordinates:c});if(u){const t=x(u,c),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:i,isRight:l,isLeft:s,isBottom:c,maxScroll:d,minScroll:f}=ue(n),h=he(n),v={x:Math.min(o===xe.Right?h.right-h.width/2:h.right,Math.max(o===xe.Right?h.left:h.left+h.width/2,u.x)),y:Math.min(o===xe.Down?h.bottom-h.height/2:h.bottom,Math.max(o===xe.Down?h.top:h.top+h.height/2,u.y))},g=o===xe.Right&&!l||o===xe.Left&&!s,p=o===xe.Down&&!c||o===xe.Up&&!i;if(g&&v.x!==u.x){const e=n.scrollLeft+t.x,i=o===xe.Right&&e<=d.x||o===xe.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});r.x=i?n.scrollLeft-e:o===xe.Right?n.scrollLeft-d.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(p&&v.y!==u.y){const e=n.scrollTop+t.y,i=o===xe.Down&&e<=d.y||o===xe.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});r.y=i?n.scrollTop-e:o===xe.Down?n.scrollTop-d.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,w(x(u,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function Ie(e){return Boolean(e&&"distance"in e)}function Ne(e){return Boolean(e&&"delay"in e)}Me.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Ee,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const t=i.activatorNode.current;return!(t&&e.target!==t||(e.preventDefault(),null==o||o({event:e.nativeEvent}),0))}return!1}}];class Te{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=a(e);return e instanceof t?e:u(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:i}=o;this.props=e,this.events=t,this.document=u(i),this.documentListeners=new be(this.document),this.listeners=new be(n),this.windowListeners=new be(a(i)),this.initialCoordinates=null!=(r=C(o))?r:j,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(ye.Resize,this.handleCancel),this.windowListeners.add(ye.DragStart,Ce),this.windowListeners.add(ye.VisibilityChange,this.handleCancel),this.windowListeners.add(ye.ContextMenu,Ce),this.documentListeners.add(ye.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Ne(t))return this.timeoutId=setTimeout(this.handleStart,t.delay),void this.handlePending(t);if(Ie(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){const{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(ye.Click,Re,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(ye.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:i,options:{activationConstraint:a}}=o;if(!r)return;const l=null!=(t=C(e))?t:j,s=x(r,l);if(!n&&a){if(Ie(a)){if(null!=a.tolerance&&me(s,a.tolerance))return this.handleCancel();if(me(s,a.distance))return this.handleStart()}return Ne(a)&&me(s,a.tolerance)?this.handleCancel():void this.handlePending(a,s)}e.cancelable&&e.preventDefault(),i(l)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===xe.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const ke={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Ae extends Te{constructor(e){const{event:t}=e,n=u(t.target);super(e,ke,n)}}Ae.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button||(null==r||r({event:n}),0))}}];const Oe={move:{name:"mousemove"},end:{name:"mouseup"}};var Le,Be;(Be=Le||(Le={}))[Be.RightClick=2]="RightClick",class extends Te{constructor(e){super(e,Oe,u(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==Le.RightClick&&(null==r||r({event:n}),!0)}}];const Pe={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};var ze,Fe,je,Ue;(class extends Te{constructor(e){super(e,Pe)}static setup(){return window.addEventListener(Pe.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Pe.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1||(null==r||r({event:n}),0))}}],(Fe=ze||(ze={}))[Fe.Pointer=0]="Pointer",Fe[Fe.DraggableRect=1]="DraggableRect",(Ue=je||(je={}))[Ue.TreeOrder=0]="TreeOrder",Ue[Ue.ReversedTreeOrder=1]="ReversedTreeOrder";const Xe={x:{[le.Backward]:!1,[le.Forward]:!1},y:{[le.Backward]:!1,[le.Forward]:!1}};var Ye,Ke,We;(Ke=Ye||(Ye={}))[Ke.Always=0]="Always",Ke[Ke.BeforeDragging=1]="BeforeDragging",Ke[Ke.WhileDragging=2]="WhileDragging",(We||(We={})).Optimized="optimized";const He=new Map;function qe(e,t){return v((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function Je(t){let{callback:n,disabled:r}=t;const o=f(n),i=e.useMemo((()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)}),[r]);return e.useEffect((()=>()=>null==i?void 0:i.disconnect()),[i]),i}function Ve(e){return new pe($(e),e)}function _e(t,n,r){void 0===n&&(n=Ve);const[o,i]=e.useState(null);function a(){i((e=>{if(!t)return null;var o;if(!1===t.isConnected)return null!=(o=null!=e?e:r)?o:null;const i=n(t);return JSON.stringify(e)===JSON.stringify(i)?e:i}))}const l=function(t){let{callback:n,disabled:r}=t;const o=f(n),i=e.useMemo((()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)}),[o,r]);return e.useEffect((()=>()=>null==i?void 0:i.disconnect()),[i]),i}({callback(e){if(t)for(const n of e){const{type:e,target:r}=n;if("childList"===e&&r instanceof HTMLElement&&r.contains(t)){a();break}}}}),s=Je({callback:a});return d((()=>{a(),t?(null==s||s.observe(t),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())}),[t]),o}const Ge=[];function Qe(t,n){void 0===n&&(n=[]);const r=e.useRef(null);return e.useEffect((()=>{r.current=null}),n),e.useEffect((()=>{const e=t!==j;e&&!r.current&&(r.current=t),!e&&r.current&&(r.current=null)}),[t]),r.current?x(t,r.current):j}function Ze(t){return e.useMemo((()=>t?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(t):null),[t])}const $e=[];const et=[{sensor:Ae,options:{}},{sensor:Me,options:{}}],tt={current:{}},nt={draggable:{measure:ee},droppable:{measure:ee,strategy:Ye.WhileDragging,frequency:We.Optimized},dragOverlay:{measure:$}};class rt extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const ot={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new rt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:P},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:nt,measureDroppableContainers:P,windowRect:null,measuringScheduled:!1},it={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:P,draggableNodes:new Map,over:null,measureDroppableContainers:P},at=e.createContext(it),lt=e.createContext(ot);function st(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new rt}}}function ct(e,t){switch(t.type){case L.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case L.DragMove:return null==e.draggable.active?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case L.DragEnd:case L.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case L.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new rt(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case L.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new rt(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case L.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new rt(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function ut(t){let{disabled:n}=t;const{active:r,activatorEvent:o,draggableNodes:i}=e.useContext(at),a=p(o),l=p(null==r?void 0:r.id);return e.useEffect((()=>{if(!n&&!o&&a&&null!=l){if(!D(a))return;if(document.activeElement===a.target)return;const e=i.get(l);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=S(e);if(t){t.focus();break}}}))}}),[o,n,i,l,a]),null}const dt=e.createContext({...j,scaleX:1,scaleY:1});var ft,ht;(ht=ft||(ft={}))[ht.Uninitialized=0]="Uninitialized",ht[ht.Initializing=1]="Initializing",ht[ht.Initialized=2]="Initialized";const vt=e.memo((function(o){var i,l,c,u;let{id:f,accessibility:b,autoScroll:y=!0,children:x,sensors:D=et,collisionDetection:R=V,measuring:E,modifiers:S,...M}=o;const I=e.useReducer(ct,void 0,st),[N,k]=I,[A,B]=function(){const[t]=e.useState((()=>new Set)),n=e.useCallback((e=>(t.add(e),()=>t.delete(e))),[t]);return[e.useCallback((e=>{let{type:n,event:r}=e;t.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[t]),n]}(),[P,z]=e.useState(ft.Uninitialized),F=P===ft.Initialized,{draggable:{active:U,nodes:X,translate:Y},droppable:{containers:K}}=N,H=null!=U?X.get(U):null,q=e.useRef({initial:null,translated:null}),J=e.useMemo((()=>{var e;return null!=U?{id:U,data:null!=(e=null==H?void 0:H.data)?e:tt,rect:q}:null}),[U,H]),G=e.useRef(null),[Z,ee]=e.useState(null),[oe,ie]=e.useState(null),se=h(M,Object.values(M)),ue=m("DndDescribedBy",f),de=e.useMemo((()=>K.getEnabled()),[K]),he=(ge=E,e.useMemo((()=>({draggable:{...nt.draggable,...null==ge?void 0:ge.draggable},droppable:{...nt.droppable,...null==ge?void 0:ge.droppable},dragOverlay:{...nt.dragOverlay,...null==ge?void 0:ge.dragOverlay}})),[null==ge?void 0:ge.draggable,null==ge?void 0:ge.droppable,null==ge?void 0:ge.dragOverlay]));var ge;const{droppableRects:be,measureDroppableContainers:me,measuringScheduled:ye}=function(t,n){let{dragging:r,dependencies:o,config:i}=n;const[a,l]=e.useState(null),{frequency:s,measure:c,strategy:u}=i,d=e.useRef(t),f=function(){switch(u){case Ye.Always:return!1;case Ye.BeforeDragging:return r;default:return!r}}(),g=h(f),p=e.useCallback((function(e){void 0===e&&(e=[]),g.current||l((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[g]),b=e.useRef(null),m=v((e=>{if(f&&!r)return He;if(!e||e===He||d.current!==t||null!=a){const e=new Map;for(let n of t){if(!n)continue;if(a&&a.length>0&&!a.includes(n.id)&&n.rect.current){e.set(n.id,n.rect.current);continue}const t=n.node.current,r=t?new pe(c(t),t):null;n.rect.current=r,r&&e.set(n.id,r)}return e}return e}),[t,a,r,f,c]);return e.useEffect((()=>{d.current=t}),[t]),e.useEffect((()=>{f||p()}),[r,f]),e.useEffect((()=>{a&&a.length>0&&l(null)}),[JSON.stringify(a)]),e.useEffect((()=>{f||"number"!=typeof s||null!==b.current||(b.current=setTimeout((()=>{p(),b.current=null}),s))}),[s,f,p,...o]),{droppableRects:m,measureDroppableContainers:p,measuringScheduled:null!=a}}(de,{dragging:F,dependencies:[Y.x,Y.y],config:he.droppable}),we=function(e,t){const n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return v((e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(X,U),xe=e.useMemo((()=>oe?C(oe):null),[oe]),De=function(){const e=!1===(null==Z?void 0:Z.autoScrollEnabled),t="object"==typeof y?!1===y.enabled:!1===y,n=F&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),Ce=function(e,t){return qe(e,t)}(we,he.draggable.measure);!function(t){let{activeNode:n,measure:r,initialRect:o,config:i=!0}=t;const a=e.useRef(!1),{x:l,y:s}="boolean"==typeof i?{x:i,y:i}:i;d((()=>{if(!l&&!s||!n)return void(a.current=!1);if(a.current||!o)return;const e=null==n?void 0:n.node.current;if(!e||!1===e.isConnected)return;const t=_(r(e),o);if(l||(t.x=0),s||(t.y=0),a.current=!0,Math.abs(t.x)>0||Math.abs(t.y)>0){const n=ne(e);n&&n.scrollBy({top:t.y,left:t.x})}}),[n,l,s,o,r])}({activeNode:null!=U?X.get(U):null,config:De.layoutShiftCompensation,initialRect:Ce,measure:he.draggable.measure});const Re=_e(we,he.draggable.measure,Ce),Ee=_e(we?we.parentElement:null),Se=e.useRef({activatorEvent:null,active:null,activeNode:we,collisionRect:null,collisions:null,droppableRects:be,draggableNodes:X,draggingNode:null,draggingNodeRect:null,droppableContainers:K,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),Me=K.getNodeFor(null==(i=Se.current.over)?void 0:i.id),Ie=function(t){let{measure:n}=t;const[r,o]=e.useState(null),i=Je({callback:e.useCallback((e=>{for(const{target:t}of e)if(s(t)){o((e=>{const r=n(t);return e?{...e,width:r.width,height:r.height}:r}));break}}),[n])}),a=e.useCallback((e=>{const t=function(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return s(t)?t:e}(e);null==i||i.disconnect(),t&&(null==i||i.observe(t)),o(t?n(t):null)}),[n,i]),[l,c]=g(a);return e.useMemo((()=>({nodeRef:l,rect:r,setRef:c})),[r,l,c])}({measure:he.dragOverlay.measure}),Ne=null!=(l=Ie.nodeRef.current)?l:we,Te=F?null!=(c=Ie.rect)?c:Re:null,ke=Boolean(Ie.nodeRef.current&&Ie.rect),Ae=_(Oe=ke?null:Re,qe(Oe));var Oe;const Le=Ze(Ne?a(Ne):null),Be=function(t){const n=e.useRef(t),r=v((e=>t?e&&e!==Ge&&t&&n.current&&t.parentNode===n.current.parentNode?e:te(t):Ge),[t]);return e.useEffect((()=>{n.current=t}),[t]),r}(F?null!=Me?Me:we:null),Pe=function(t,n){void 0===n&&(n=$);const[r]=t,o=Ze(r?a(r):null),[i,l]=e.useState($e);function s(){l((()=>t.length?t.map((e=>ce(e)?o:new pe(n(e),e))):$e))}const c=Je({callback:s});return d((()=>{null==c||c.disconnect(),s(),t.forEach((e=>null==c?void 0:c.observe(e)))}),[t]),i}(Be),Fe=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}(S,{transform:{x:Y.x-Ae.x,y:Y.y-Ae.y,scaleX:1,scaleY:1},activatorEvent:oe,active:J,activeNodeRect:Re,containerNodeRect:Ee,draggingNodeRect:Te,over:Se.current.over,overlayNodeRect:Ie.rect,scrollableAncestors:Be,scrollableAncestorRects:Pe,windowRect:Le}),Ue=xe?w(xe,Y):null,Ke=function(t){const[n,r]=e.useState(null),o=e.useRef(t),i=e.useCallback((e=>{const t=re(e.target);t&&r((e=>e?(e.set(t,ae(t)),new Map(e)):null))}),[]);return e.useEffect((()=>{const e=o.current;if(t!==e){n(e);const a=t.map((e=>{const t=re(e);return t?(t.addEventListener("scroll",i,{passive:!0}),[t,ae(t)]):null})).filter((e=>null!=e));r(a.length?new Map(a):null),o.current=t}return()=>{n(t),n(e)};function n(e){e.forEach((e=>{const t=re(e);null==t||t.removeEventListener("scroll",i)}))}}),[i,t]),e.useMemo((()=>t.length?n?Array.from(n.values()).reduce(((e,t)=>w(e,t)),j):ve(t):j),[t,n])}(Be),We=Qe(Ke),Ve=Qe(Ke,[Re]),rt=w(Fe,We),ot=Te?Q(Te,Fe):null,it=J&&ot?R({active:J,collisionRect:ot,droppableRects:be,droppableContainers:de,pointerCoordinates:Ue}):null,ht=W(it,"id"),[vt,gt]=e.useState(null),pt=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(ke?Fe:w(Fe,Ve),null!=(u=null==vt?void 0:vt.rect)?u:null,Re),bt=e.useRef(null),mt=e.useCallback(((e,t)=>{let{sensor:r,options:o}=t;if(null==G.current)return;const i=X.get(G.current);if(!i)return;const a=e.nativeEvent,l=new r({active:G.current,activeNode:i,event:a,options:o,context:Se,onAbort(e){if(!X.get(e))return;const{onDragAbort:t}=se.current,n={id:e};null==t||t(n),A({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!X.get(e))return;const{onDragPending:o}=se.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==o||o(i),A({type:"onDragPending",event:i})},onStart(e){const t=G.current;if(null==t)return;const r=X.get(t);if(!r)return;const{onDragStart:o}=se.current,i={activatorEvent:a,active:{id:t,data:r.data,rect:q}};n.unstable_batchedUpdates((()=>{null==o||o(i),z(ft.Initializing),k({type:L.DragStart,initialCoordinates:e,active:t}),A({type:"onDragStart",event:i}),ee(bt.current),ie(a)}))},onMove(e){k({type:L.DragMove,coordinates:e})},onEnd:s(L.DragEnd),onCancel:s(L.DragCancel)});function s(e){return async function(){const{active:t,collisions:r,over:o,scrollAdjustedTranslate:i}=Se.current;let l=null;if(t&&i){const{cancelDrop:n}=se.current;l={activatorEvent:a,active:t,collisions:r,delta:i,over:o},e===L.DragEnd&&"function"==typeof n&&await Promise.resolve(n(l))&&(e=L.DragCancel)}G.current=null,n.unstable_batchedUpdates((()=>{k({type:e}),z(ft.Uninitialized),gt(null),ee(null),ie(null),bt.current=null;const t=e===L.DragEnd?"onDragEnd":"onDragCancel";if(l){const e=se.current[t];null==e||e(l),A({type:t,event:l})}}))}}bt.current=l}),[X]),yt=function(t,n){return e.useMemo((()=>t.reduce(((e,t)=>{const{sensor:r}=t;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:n(e.handler,t)})))]}),[])),[t,n])}(D,e.useCallback(((e,t)=>(n,r)=>{const o=n.nativeEvent,i=X.get(r);if(null!==G.current||!i||o.dndKit||o.defaultPrevented)return;const a={active:i};!0===e(n,t.options,a)&&(o.dndKit={capturedBy:t.sensor},G.current=r,mt(n,t))}),[X,mt]));!function(t){e.useEffect((()=>{if(!r)return;const e=t.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const t of e)null==t||t()}}),t.map((e=>{let{sensor:t}=e;return t})))}(D),d((()=>{Re&&P===ft.Initializing&&z(ft.Initialized)}),[Re,P]),e.useEffect((()=>{const{onDragMove:e}=se.current,{active:t,activatorEvent:r,collisions:o,over:i}=Se.current;if(!t||!r)return;const a={active:t,activatorEvent:r,collisions:o,delta:{x:rt.x,y:rt.y},over:i};n.unstable_batchedUpdates((()=>{null==e||e(a),A({type:"onDragMove",event:a})}))}),[rt.x,rt.y]),e.useEffect((()=>{const{active:e,activatorEvent:t,collisions:r,droppableContainers:o,scrollAdjustedTranslate:i}=Se.current;if(!e||null==G.current||!t||!i)return;const{onDragOver:a}=se.current,l=o.get(ht),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,c={active:e,activatorEvent:t,collisions:r,delta:{x:i.x,y:i.y},over:s};n.unstable_batchedUpdates((()=>{gt(s),null==a||a(c),A({type:"onDragOver",event:c})}))}),[ht]),d((()=>{Se.current={activatorEvent:oe,active:J,activeNode:we,collisionRect:ot,collisions:it,droppableRects:be,draggableNodes:X,draggingNode:Ne,draggingNodeRect:Te,droppableContainers:K,over:vt,scrollableAncestors:Be,scrollAdjustedTranslate:rt},q.current={initial:Te,translated:ot}}),[J,we,it,ot,X,Ne,Te,be,K,vt,Be,rt]),function(t){let{acceleration:n,activator:r=ze.Pointer,canScroll:o,draggingRect:i,enabled:a,interval:l=5,order:s=je.TreeOrder,pointerCoordinates:c,scrollableAncestors:u,scrollableAncestorRects:d,delta:f,threshold:h}=t;const g=function(e){let{delta:t,disabled:n}=e;const r=p(t);return v((e=>{if(n||!r||!e)return Xe;const o=Math.sign(t.x-r.x),i=Math.sign(t.y-r.y);return{x:{[le.Backward]:e.x[le.Backward]||-1===o,[le.Forward]:e.x[le.Forward]||1===o},y:{[le.Backward]:e.y[le.Backward]||-1===i,[le.Forward]:e.y[le.Forward]||1===i}}}),[n,t,r])}({delta:f,disabled:!a}),[b,m]=function(){const t=e.useRef(null);return[e.useCallback(((e,n)=>{t.current=setInterval(e,n)}),[]),e.useCallback((()=>{null!==t.current&&(clearInterval(t.current),t.current=null)}),[])]}(),y=e.useRef({x:0,y:0}),w=e.useRef({x:0,y:0}),x=e.useMemo((()=>{switch(r){case ze.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case ze.DraggableRect:return i}}),[r,i,c]),D=e.useRef(null),C=e.useCallback((()=>{const e=D.current;if(!e)return;const t=y.current.x*w.current.x,n=y.current.y*w.current.y;e.scrollBy(t,n)}),[]),R=e.useMemo((()=>s===je.TreeOrder?[...u].reverse():u),[s,u]);e.useEffect((()=>{if(a&&u.length&&x){for(const e of R){if(!1===(null==o?void 0:o(e)))continue;const t=u.indexOf(e),r=d[t];if(!r)continue;const{direction:i,speed:a}=fe(e,r,x,n,h);for(const e of["x","y"])g[e][i[e]]||(a[e]=0,i[e]=0);if(a.x>0||a.y>0)return m(),D.current=e,b(C,l),y.current=a,void(w.current=i)}y.current={x:0,y:0},w.current={x:0,y:0},m()}else m()}),[n,C,o,m,a,l,JSON.stringify(x),JSON.stringify(g),b,u,R,d,JSON.stringify(h)])}({...De,delta:Y,draggingRect:ot,pointerCoordinates:Ue,scrollableAncestors:Be,scrollableAncestorRects:Pe});const wt=e.useMemo((()=>({active:J,activeNode:we,activeNodeRect:Re,activatorEvent:oe,collisions:it,containerNodeRect:Ee,dragOverlay:Ie,draggableNodes:X,droppableContainers:K,droppableRects:be,over:vt,measureDroppableContainers:me,scrollableAncestors:Be,scrollableAncestorRects:Pe,measuringConfiguration:he,measuringScheduled:ye,windowRect:Le})),[J,we,Re,oe,it,Ee,Ie,X,K,be,vt,me,Be,Pe,he,ye,Le]),xt=e.useMemo((()=>({activatorEvent:oe,activators:yt,active:J,activeNodeRect:Re,ariaDescribedById:{draggable:ue},dispatch:k,draggableNodes:X,over:vt,measureDroppableContainers:me})),[oe,yt,J,Re,k,ue,X,vt,me]);return t.createElement(T.Provider,{value:B},t.createElement(at.Provider,{value:xt},t.createElement(lt.Provider,{value:wt},t.createElement(dt.Provider,{value:pt},x)),t.createElement(ut,{disabled:!1===(null==b?void 0:b.restoreFocus)})),t.createElement(O,{...b,hiddenTextDescribedById:ue}))})),gt=e.createContext(null),pt="button";const bt={timeout:25};function mt(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function yt(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function wt(e){return null!==e&&e>=0}const xt=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=mt(t,r,n),a=t[o],l=i[o];return l&&a?{x:l.left-a.left,y:l.top-a.top,scaleX:l.width/a.width,scaleY:l.height/a.height}:null},Dt={scaleX:1,scaleY:1},Ct=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:a}=e;const l=null!=(t=i[n])?t:r;if(!l)return null;if(o===n){const e=i[a];return e?{x:0,y:n<a?e.top+e.height-(l.top+l.height):e.top-l.top,...Dt}:null}const s=function(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];return r?n<t?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}(i,o,n);return o>n&&o<=a?{x:0,y:-l.height-s,...Dt}:o<n&&o>=a?{x:0,y:l.height+s,...Dt}:{x:0,y:0,...Dt}},Rt="Sortable",Et=t.createContext({activeIndex:-1,containerId:Rt,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:xt,disabled:{draggable:!1,droppable:!1}});function St(n){let{children:r,id:o,items:i,strategy:a=xt,disabled:l=!1}=n;const{active:s,dragOverlay:c,droppableRects:u,over:f,measureDroppableContainers:h}=e.useContext(lt),v=m(Rt,o),g=Boolean(null!==c.rect),p=e.useMemo((()=>i.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[i]),b=null!=s,y=s?p.indexOf(s.id):-1,w=f?p.indexOf(f.id):-1,x=e.useRef(p),D=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(p,x.current),C=-1!==w&&-1===y||D,R=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(l);d((()=>{D&&b&&h(p)}),[D,p,b,h]),e.useEffect((()=>{x.current=p}),[p]);const E=e.useMemo((()=>({activeIndex:y,containerId:v,disabled:R,disableTransforms:C,items:p,overIndex:w,useDragOverlay:g,sortedRects:yt(p,u),strategy:a})),[y,v,R.draggable,R.droppable,C,p,w,u,g,a]);return t.createElement(Et.Provider,{value:E},r)}const Mt=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return mt(n,r,o).indexOf(t)},It=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:l,previousContainerId:s,transition:c}=e;return!(!c||!r||l!==i&&o===a||!n&&(a===o||t!==s))},Nt={duration:200,easing:"ease"},Tt="transform",kt=R.Transition.toString({property:Tt,duration:0,easing:"linear"}),At={roleDescription:"sortable"};function Ot(t){let{animateLayoutChanges:n=It,attributes:r,disabled:o,data:i,getNewIndex:a=Mt,id:l,strategy:s,resizeObserverConfig:c,transition:u=Nt}=t;const{items:f,containerId:v,activeIndex:p,disabled:b,disableTransforms:y,sortedRects:w,overIndex:x,useDragOverlay:C,strategy:E}=e.useContext(Et),S=function(e,t){var n,r;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(o,b),M=f.indexOf(l),I=e.useMemo((()=>({sortable:{containerId:v,index:M,items:f},...i})),[v,i,M,f]),N=e.useMemo((()=>f.slice(f.indexOf(l))),[f,l]),{rect:T,node:k,isOver:A,setNodeRef:O}=function(t){let{data:n,disabled:r=!1,id:o,resizeObserverConfig:i}=t;const a=m("Droppable"),{active:l,dispatch:s,over:c,measureDroppableContainers:u}=e.useContext(at),d=e.useRef({disabled:r}),f=e.useRef(!1),v=e.useRef(null),p=e.useRef(null),{disabled:b,updateMeasurementsFor:y,timeout:w}={...bt,...i},x=h(null!=y?y:o),D=Je({callback:e.useCallback((()=>{f.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{u(Array.isArray(x.current)?x.current:[x.current]),p.current=null}),w)):f.current=!0}),[w]),disabled:b||!l}),C=e.useCallback(((e,t)=>{D&&(t&&(D.unobserve(t),f.current=!1),e&&D.observe(e))}),[D]),[R,E]=g(C),S=h(n);return e.useEffect((()=>{D&&R.current&&(D.disconnect(),f.current=!1,D.observe(R.current))}),[R,D]),e.useEffect((()=>(s({type:L.RegisterDroppable,element:{id:o,key:a,disabled:r,node:R,rect:v,data:S}}),()=>s({type:L.UnregisterDroppable,key:a,id:o}))),[o]),e.useEffect((()=>{r!==d.current.disabled&&(s({type:L.SetDroppableDisabled,id:o,key:a,disabled:r}),d.current.disabled=r)}),[o,a,r,s]),{active:l,rect:v,isOver:(null==c?void 0:c.id)===o,node:R,over:c,setNodeRef:E}}({id:l,data:I,disabled:S.droppable,resizeObserverConfig:{updateMeasurementsFor:N,...c}}),{active:B,activatorEvent:P,activeNodeRect:z,attributes:F,setNodeRef:j,listeners:U,isDragging:X,over:Y,setActivatorNodeRef:K,transform:W}=function(t){let{id:n,data:r,disabled:o=!1,attributes:i}=t;const a=m("Draggable"),{activators:l,activatorEvent:s,active:c,activeNodeRect:u,ariaDescribedById:f,draggableNodes:v,over:p}=e.useContext(at),{role:b=pt,roleDescription:y="draggable",tabIndex:w=0}=null!=i?i:{},x=(null==c?void 0:c.id)===n,D=e.useContext(x?dt:gt),[C,R]=g(),[E,S]=g(),M=function(t,n){return e.useMemo((()=>t.reduce(((e,t)=>{let{eventName:r,handler:o}=t;return e[r]=e=>{o(e,n)},e}),{})),[t,n])}(l,n),I=h(r);return d((()=>(v.set(n,{id:n,key:a,node:C,activatorNode:E,data:I}),()=>{const e=v.get(n);e&&e.key===a&&v.delete(n)})),[v,n]),{active:c,activatorEvent:s,activeNodeRect:u,attributes:e.useMemo((()=>({role:b,tabIndex:w,"aria-disabled":o,"aria-pressed":!(!x||b!==pt)||void 0,"aria-roledescription":y,"aria-describedby":f.draggable})),[o,b,w,x,y,f.draggable]),isDragging:x,listeners:o?void 0:M,node:C,over:p,setNodeRef:R,setActivatorNodeRef:S,transform:D}}({id:l,data:I,attributes:{...At,...r},disabled:S.draggable}),H=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.useMemo((()=>e=>{n.forEach((t=>t(e)))}),n)}(O,j),q=Boolean(B),J=q&&!y&&wt(p)&&wt(x),V=!C&&X,_=V&&J?W:null,G=J?null!=_?_:(null!=s?s:E)({rects:w,activeNodeRect:z,activeIndex:p,overIndex:x,index:M}):null,Q=wt(p)&&wt(x)?a({id:l,items:f,activeIndex:p,overIndex:x}):M,Z=null==B?void 0:B.id,ee=e.useRef({activeId:Z,items:f,newIndex:Q,containerId:v}),te=f!==ee.current.items,ne=n({active:B,containerId:v,isDragging:X,isSorting:q,id:l,index:M,items:f,newIndex:ee.current.newIndex,previousItems:ee.current.items,previousContainerId:ee.current.containerId,transition:u,wasDragging:null!=ee.current.activeId}),re=function(t){let{disabled:n,index:r,node:o,rect:i}=t;const[a,l]=e.useState(null),s=e.useRef(r);return d((()=>{if(!n&&r!==s.current&&o.current){const e=i.current;if(e){const t=$(o.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&l(n)}}r!==s.current&&(s.current=r)}),[n,r,o,i]),e.useEffect((()=>{a&&l(null)}),[a]),a}({disabled:!ne,index:M,node:k,rect:T});return e.useEffect((()=>{q&&ee.current.newIndex!==Q&&(ee.current.newIndex=Q),v!==ee.current.containerId&&(ee.current.containerId=v),f!==ee.current.items&&(ee.current.items=f)}),[q,Q,v,f]),e.useEffect((()=>{if(Z===ee.current.activeId)return;if(null!=Z&&null==ee.current.activeId)return void(ee.current.activeId=Z);const e=setTimeout((()=>{ee.current.activeId=Z}),50);return()=>clearTimeout(e)}),[Z]),{active:B,activeIndex:p,attributes:F,data:I,rect:T,index:M,newIndex:Q,items:f,isOver:A,isSorting:q,isDragging:X,listeners:U,node:k,overIndex:x,over:Y,setNodeRef:H,setActivatorNodeRef:K,setDroppableNodeRef:O,setDraggableNodeRef:j,transform:null!=re?re:G,transition:re||te&&ee.current.newIndex===M?kt:V&&!D(P)||!u?void 0:q||ne?R.Transition.toString({...u,property:Tt}):void 0}}function Lt(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const Bt=[xe.Down,xe.Right,xe.Up,xe.Left],Pt=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:i,over:a,scrollableAncestors:l}}=t;if(Bt.includes(e.code)){if(e.preventDefault(),!n||!r)return;const t=[];i.getEnabled().forEach((n=>{if(!n||null!=n&&n.disabled)return;const i=o.get(n.id);if(i)switch(e.code){case xe.Down:r.top<i.top&&t.push(n);break;case xe.Up:r.top>i.top&&t.push(n);break;case xe.Left:r.left>i.left&&t.push(n);break;case xe.Right:r.left<i.left&&t.push(n)}}));const u=(e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=K(t),i=[];for(const a of r){const{id:e}=a,t=n.get(e);if(t){const n=K(t),r=o.reduce(((e,t,r)=>e+U(n[r],t)),0),l=Number((r/4).toFixed(4));i.push({id:e,data:{droppableContainer:a,value:l}})}}return i.sort(X)})({collisionRect:r,droppableRects:o,droppableContainers:t});let d=W(u,"id");if(d===(null==a?void 0:a.id)&&u.length>1&&(d=u[1].id),null!=d){const e=i.get(n.id),t=i.get(d),a=t?o.get(t.id):null,u=null==t?void 0:t.node.current;if(u&&a&&e&&t){const n=te(u).some(((e,t)=>l[t]!==e)),o=zt(e,t),i=(c=t,!(!Lt(s=e)||!Lt(c))&&!!zt(s,c)&&s.data.current.sortable.index<c.data.current.sortable.index),d=n||!o?{x:0,y:0}:{x:i?r.width-a.width:0,y:i?r.height-a.height:0},f={x:a.left,y:a.top};return d.x&&d.y?f:x(f,d)}}}var s,c};function zt(e,t){return!(!Lt(e)||!Lt(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}export{R as C,vt as D,Me as K,Ae as P,St as S,F as a,z as b,q as c,mt as d,Pt as s,Ot as u,Ct as v};
