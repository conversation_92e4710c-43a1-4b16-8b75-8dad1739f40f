import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-DnaPB9-4.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-CNG-Xj2g.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-BgCSiPkf.js";import"./FeedbackTrigger-ik6vfZ65.js";import"./rocket-BEoGgNr2.js";import"./target-Dul0NbVV.js";import"./zap-C4mKju26.js";import"./book-open-EV5sJdXr.js";import"./star-BUSksJJE.js";import"./circle-help-BbvIlE64.js";import"./instagram-ClgJ7H9i.js";import"./collapsible-B6HfSnGs.js";import"./accordion-ktS3I2K8.js";import"./PatientInfoSection-BWWQwWDh.js";import"./scale-BytuqEvR.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-CqqFJfDW.js";import"./user-DPHDn0jo.js";import"./DosageDisplay-DzxS2sxe.js";import"./alert-BT_NObbd.js";import"./plus-5xdDX9-5.js";import"./lightbulb-DnxfUKrr.js";import"./external-link-DtRA0qiZ.js";import"./stethoscope-CWY4pChw.js";import"./syringe-xvAK2K4s.js";import"./wind-DGF9C2YG.js";import"./bug-BVNgNMVk.js";import"./chevron-left-Ep4pqQcd.js";import"./house-CQtCJU99.js";function t(){return r.jsx(o,{})}export{t as default};
