const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/CinematicWelcome-BEYqwypy.js","assets/radix-core-6kBL75b5.js","assets/critical-DVX9Inzy.js","assets/index-BGVWLj2Q.js","assets/query-vendor-B-7l6Nb3.js","assets/supabase-vendor-qi_Ptfv-.js","assets/router-BAzpOxbo.js","assets/form-vendor-rYZw_ur7.js","assets/radix-forms-DX-owj97.js","assets/radix-interactive-DJo-0Sg_.js","assets/radix-toast-1_gbKn9f.js","assets/radix-feedback-dpGNY8wJ.js","assets/radix-popover-DQqTw7_-.js","assets/radix-layout-CC8mXA4O.js","assets/index-CobVw-zb.js","assets/target-B7qg_LDj.js","assets/book-open-ClUpo2Lw.js","assets/play-Cgco6ohu.js","assets/FeedbackTrigger-S5r6pr1T.js","assets/rocket-BJgtWoQ_.js","assets/zap-CJm1mYQd.js","assets/star-Bpzr1rUs.js","assets/circle-help-CBQwJU4Z.js"])))=>i.map(i=>d[i]);
import{d as e,_ as s}from"./supabase-vendor-qi_Ptfv-.js";import{j as a}from"./radix-core-6kBL75b5.js";import{r,b as t}from"./critical-DVX9Inzy.js";import{m as i,R as d,aJ as l,aB as o,B as n,X as c,ay as m,U as x,C as p,u,d as b,s as g,a8 as h,aa as j}from"./index-BGVWLj2Q.js";import f from"./Footer-D6qSLzC8.js";import{A as v}from"./index-CobVw-zb.js";import{T as y}from"./target-B7qg_LDj.js";import{B as N}from"./building-2-CJvqAM4u.js";import{C as w}from"./calendar-CkqAMcCO.js";import{F as k}from"./file-question-Crp6Q8Sp.js";import{A as E,H as C,T as P}from"./FeedbackTrigger-S5r6pr1T.js";import{P as S}from"./play-Cgco6ohu.js";import{a as A}from"./router-BAzpOxbo.js";import{S as _}from"./stethoscope-CoAh0LEU.js";import{B as F}from"./book-open-ClUpo2Lw.js";import{T as I}from"./trending-up-DQ7UXh5Z.js";import{C as q}from"./clock-Ctd21uX9.js";import{S as M}from"./star-Bpzr1rUs.js";import"./query-vendor-B-7l6Nb3.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-D_9FoHz5.js";import"./rocket-BJgtWoQ_.js";import"./zap-CJm1mYQd.js";import"./circle-help-CBQwJU4Z.js";const V=({isOpen:e,onClose:s,onStartStudy:t})=>{const[u,b]=r.useState(0),g=[{title:"🎯 Bem-vindo ao PedBook!",description:"Vamos te mostrar como usar os filtros inteligentes para estudar pediatria de forma eficiente. Este tutorial rápido vai te ensinar tudo que você precisa saber!",icon:y,color:"from-blue-500 to-indigo-600",badge:"INÍCIO"},{title:"🧠 Especialidades → Temas → Focos",description:"A hierarquia mais importante! Navegue por ESPECIALIDADE → TEMA → FOCO. No PedBook, você terá acesso apenas à pediatria, mas com toda a profundidade de temas e focos específicos.",icon:l,color:"from-yellow-500 to-orange-600",badge:"HIERÁRQUICO",demo:a.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[a.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(p,{className:"h-4 w-4 text-blue-600"}),a.jsx("span",{className:"font-medium text-blue-600",children:"Pediatria"})]}),a.jsxs("div",{className:"ml-6 space-y-1",children:[a.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[a.jsx(p,{className:"h-3 w-3 text-purple-600"}),a.jsx("span",{className:"text-purple-600",children:"Cardiologia Pediátrica"})]}),a.jsxs("div",{className:"ml-4 text-xs text-gray-600",children:["• Cardiopatias Congênitas",a.jsx("br",{}),"• Arritmias Pediátricas"]})]})]})},{title:"🏥 Instituições",description:"Filtre por instituições específicas como UNIFESP, USP, UFMG e outras. Ideal para focar em bancas que você mais estuda ou que caem na sua prova de residência.",icon:N,color:"from-blue-500 to-cyan-600",demo:a.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:a.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[a.jsx(m,{variant:"outline",className:"justify-center",children:"UNIFESP"}),a.jsx(m,{variant:"outline",className:"justify-center",children:"USP"}),a.jsx(m,{variant:"outline",className:"justify-center",children:"UFMG"}),a.jsx(m,{variant:"outline",className:"justify-center",children:"UFRJ"})]})})},{title:"📅 Anos das Provas",description:"Selecione anos específicos das provas. Questões mais recentes refletem tendências atuais, enquanto questões antigas testam conceitos consolidados da pediatria.",icon:w,color:"from-green-500 to-emerald-600",demo:a.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:a.jsx("div",{className:"flex flex-wrap gap-2",children:[2024,2023,2022,2021].map((e=>a.jsx(m,{variant:"outline",className:"text-xs",children:e},e)))})})},{title:"🎯 Formato de Questão",description:"Filtre por formato: questões objetivas, discursivas, com imagens, casos clínicos. Personalize conforme seu estilo de estudo em pediatria.",icon:k,color:"from-pink-500 to-rose-600",demo:a.jsx("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:a.jsxs("div",{className:"text-xs space-y-1",children:[a.jsx("div",{children:"✓ Questões Objetivas"}),a.jsx("div",{children:"✓ Casos Clínicos"}),a.jsx("div",{children:"✓ Com Imagens"})]})})},{title:"🎲 Opções de Estudo",description:"Após filtrar, escolha: estudar questões FILTRADAS (seguindo seus critérios) ou um MIX ALEATÓRIO (variado para testar conhecimentos gerais de pediatria).",icon:o,color:"from-indigo-500 to-purple-600",badge:"FINAL",demo:a.jsxs("div",{className:"space-y-2",children:[a.jsx(n,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700",children:"📋 Estudar Filtradas"}),a.jsx(n,{size:"sm",variant:"outline",className:"w-full",children:"🎲 Mix Aleatório"})]})}];if(!e)return null;const h=g[u];return a.jsx(v,{children:a.jsx(i.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4",children:a.jsx(i.div,{initial:{scale:.9,opacity:0,y:20},animate:{scale:1,opacity:1,y:0},exit:{scale:.9,opacity:0,y:20},transition:{type:"spring",stiffness:300,damping:30},className:"w-full max-w-lg",children:a.jsxs(d,{className:"overflow-hidden border-2 border-gray-100 shadow-2xl",children:[a.jsxs("div",{className:`bg-gradient-to-r ${h.color} p-6 text-white relative`,children:[a.jsx(n,{variant:"ghost",size:"sm",onClick:s,className:"absolute top-4 right-4 text-white hover:bg-white/20 h-8 w-8 p-0",children:a.jsx(c,{className:"h-4 w-4"})}),a.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[a.jsx("div",{className:"bg-white/20 p-2 rounded-lg",children:a.jsx(h.icon,{className:"h-5 w-5"})}),h.badge&&a.jsx(m,{variant:"secondary",className:"bg-white/20 text-white border-white/30",children:h.badge})]}),a.jsx("h3",{className:"text-xl font-bold",children:h.title}),a.jsxs("div",{className:"text-sm text-white/80 mt-1",children:["Passo ",u+1," de ",g.length]})]}),a.jsxs(x,{className:"p-6 space-y-4",children:[a.jsx("p",{className:"text-gray-700 leading-relaxed",children:h.description}),h.demo&&a.jsxs("div",{className:"border rounded-lg p-4 bg-gray-50",children:[a.jsx("div",{className:"text-xs text-gray-500 mb-2 font-medium",children:"Exemplo:"}),h.demo]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx(i.div,{className:`bg-gradient-to-r ${h.color} h-2 rounded-full`,initial:{width:0},animate:{width:(u+1)/g.length*100+"%"},transition:{duration:.5}})}),a.jsxs("div",{className:"flex justify-between items-center pt-2",children:[a.jsx(n,{variant:"outline",onClick:()=>{u>0&&b(u-1)},disabled:0===u,className:"px-4",children:"Anterior"}),a.jsx("div",{className:"text-xs text-gray-500",children:0===u?"Vamos começar!":u===g.length-1?"Pronto para estudar!":"Continue explorando"}),a.jsx(n,{onClick:()=>{u<g.length-1?b(u+1):(s(),t())},className:`bg-gradient-to-r ${h.color} hover:opacity-90 text-white font-medium px-6 flex items-center gap-2`,children:u<g.length-1?a.jsxs(a.Fragment,{children:["Próximo",a.jsx(E,{className:"h-4 w-4"})]}):a.jsxs(a.Fragment,{children:["Começar!",a.jsx(S,{className:"h-4 w-4"})]})})]})]})]})})})})},z=t.lazy((()=>s((()=>import("./CinematicWelcome-BEYqwypy.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22])).then((e=>({default:e.CinematicWelcome}))))),T=()=>{const s=A();b();const{user:t}=u(),[d,c]=r.useState(!1),[m,x]=r.useState(!1),{cinematicViewed:p,loading:v,markCinematicAsViewed:N}=(()=>{const s=e.useUser(),{user:a}=u(),{toast:t}=b(),[i,d]=r.useState({cinematicViewed:null,loading:!0,error:null}),l=async()=>{const e=s||a;if(e?.id)try{d((e=>({...e,loading:!0,error:null})));const{data:s,error:a}=await g.from("user_preferences").select("cinematic_viewed").eq("user_id",e.id).single();if(a){if("PGRST116"!==a.code)throw a;{const{error:s}=await g.from("user_preferences").insert({user_id:e.id,cinematic_viewed:!1});if(s)throw s;d({cinematicViewed:!1,loading:!1,error:null})}}else{const e=s.cinematic_viewed??!1;d({cinematicViewed:e,loading:!1,error:null})}}catch(r){d({cinematicViewed:null,loading:!1,error:"Erro ao carregar preferências"})}else{const e=localStorage.getItem("cinematic_viewed");d({cinematicViewed:"true"===e,loading:!1,error:null})}};return r.useEffect((()=>{l()}),[s?.id,a?.id]),{...i,markCinematicAsViewed:async()=>{const e=s||a;if(!e?.id)return localStorage.setItem("cinematic_viewed","true"),d((e=>({...e,cinematicViewed:!0}))),!0;try{const{error:s}=await g.from("user_preferences").update({cinematic_viewed:!0}).eq("user_id",e.id);if(s)throw s;return d((e=>({...e,cinematicViewed:!0}))),!0}catch(r){return t({title:"Erro",description:"Não foi possível salvar a preferência",variant:"destructive"}),!1}},resetCinematicViewed:async()=>{const e=s||a;if(!e?.id)return localStorage.setItem("cinematic_viewed","false"),d((e=>({...e,cinematicViewed:!1}))),t({title:"Sucesso",description:"Experiência cinematográfica será mostrada novamente na próxima visita"}),!0;try{const{error:s}=await g.from("user_preferences").update({cinematic_viewed:!1}).eq("user_id",e.id);if(s)throw s;return d((e=>({...e,cinematicViewed:!1}))),t({title:"Sucesso",description:"Experiência cinematográfica será mostrada novamente na próxima visita"}),!0}catch(r){return t({title:"Erro",description:"Não foi possível resetar a preferência",variant:"destructive"}),!1}},refetch:l}})();r.useEffect((()=>{v||!1!==p?(v||!0!==p)&&(v||null!==p)||x(!1):x(!0)}),[p,v]);const w=async()=>{m?await N()&&(x(!1),s("/estudos/filtros")):s("/estudos/filtros")};return m?a.jsx(r.Suspense,{fallback:a.jsx("div",{className:"fixed inset-0 z-50 bg-black flex items-center justify-center",children:a.jsxs("div",{className:"text-center text-white",children:[a.jsx(i.div,{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),a.jsx("p",{className:"text-lg font-medium",children:"Carregando experiência cinematográfica..."})]})}),children:a.jsx(z,{onComplete:async()=>{await N()&&x(!1)},onStartStudy:w})}):a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden",children:[a.jsxs(h,{children:[a.jsx("title",{children:"PedBook - Estudos de Pediatria"}),a.jsx("meta",{name:"description",content:"Estude pediatria para residência médica através do PedBook. Questões do MedEvo focadas em pediatria."})]}),a.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[a.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-blue-200/20 rounded-full blur-3xl"}),a.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200/20 rounded-full blur-3xl"})]}),a.jsx(j,{}),a.jsx("main",{className:"relative z-10 px-4 py-8",children:a.jsx("div",{className:"max-w-7xl mx-auto",children:a.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg rounded-3xl border-2 border-white/50 dark:border-gray-700/50 shadow-2xl overflow-hidden",children:[a.jsx("div",{className:"px-8 py-12 border-b border-gray-200/50 dark:border-gray-700/50",children:a.jsxs("div",{className:"text-center max-w-5xl mx-auto",children:[a.jsxs(i.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"mb-8",children:[a.jsxs("div",{className:"inline-flex items-center gap-3 mb-6",children:[a.jsx("div",{className:"p-3 bg-pink-100 dark:bg-pink-900/30 rounded-2xl border border-pink-300 dark:border-pink-800/30",children:a.jsx(C,{className:"h-8 w-8 text-pink-600 dark:text-pink-400"})}),a.jsx("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-2xl border border-blue-300 dark:border-blue-800/30",children:a.jsx(_,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})})]}),a.jsxs(i.h1,{className:"text-5xl md:text-7xl font-bold mb-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},children:[a.jsx("span",{className:"text-gray-800 dark:text-gray-200",children:"Ped"}),a.jsx("span",{className:"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-transparent bg-clip-text",children:"Book"})]}),a.jsxs(i.p,{className:"text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-6 leading-relaxed font-medium",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},children:["Estude ",a.jsx("span",{className:"font-bold text-blue-600 dark:text-blue-400",children:"pediatria"})," para residência médica com questões do MedEvo"]}),a.jsxs(i.div,{className:"flex flex-col sm:flex-row justify-center gap-2 sm:gap-3 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.7},children:[a.jsx("span",{className:"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 px-4 py-2 rounded-full text-xs sm:text-sm font-semibold border-2 border-blue-300 dark:border-blue-700 shadow-md text-center",children:"🎯 Prévia do MedEvo"}),a.jsx("span",{className:"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-100 px-4 py-2 rounded-full text-xs sm:text-sm font-semibold border-2 border-purple-300 dark:border-purple-700 shadow-md text-center",children:"🔍 Foco em Pediatria"}),a.jsx("span",{className:"bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-100 px-4 py-2 rounded-full text-xs sm:text-sm font-semibold border-2 border-pink-300 dark:border-pink-700 shadow-md text-center",children:"🧠 Acesso Limitado"})]})]}),a.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[a.jsxs(n,{onClick:()=>s("/estudos/filtros"),size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg rounded-xl font-semibold flex items-center justify-center gap-2 min-h-[3rem]",children:[a.jsx(S,{className:"h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"}),a.jsx("span",{className:"truncate",children:"Começar Estudos"}),a.jsx(E,{className:"h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"})]}),a.jsxs(n,{variant:"outline",onClick:()=>window.open("https://medevo.com.br","_blank"),size:"lg",className:"border-2 border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500 px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg rounded-xl font-semibold flex items-center justify-center gap-2 min-h-[3rem]",children:[a.jsx(F,{className:"h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"}),a.jsx("span",{className:"truncate",children:"MedEvo Completo"})]})]})]})}),a.jsx("div",{className:"px-8 py-12 border-b border-gray-200/50 dark:border-gray-700/50",children:a.jsxs("div",{className:"max-w-6xl mx-auto",children:[a.jsxs("div",{className:"text-center mb-10",children:[a.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 px-6 py-3 rounded-full mb-6 border border-orange-200 dark:border-orange-800/30",children:[a.jsx(P,{className:"h-5 w-5 text-orange-600"}),a.jsx("span",{className:"text-sm font-bold text-orange-800 dark:text-orange-300",children:"Ferramenta Premium Gratuita"})]}),a.jsxs("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:["Como ",a.jsx("span",{className:"text-blue-600 dark:text-blue-400",children:"funciona?"})]}),a.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Acesso gratuito e limitado às questões de pediatria. Para experiência completa com todas as especialidades, acesse o MedEvo."})]}),a.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[a.jsxs("div",{className:"text-center p-4 sm:p-6 rounded-2xl bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800/30 hover:border-blue-300 dark:hover:border-blue-700/40 transition-colors",children:[a.jsx("div",{className:"bg-blue-600 dark:bg-blue-500 w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4",children:a.jsx(y,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),a.jsx("h3",{className:"text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 sm:mb-3",children:"Prévia Gratuita"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed",children:"Teste como é estudar no MedEvo com questões de pediatria."})]}),a.jsxs("div",{className:"text-center p-4 sm:p-6 rounded-2xl bg-purple-50 dark:bg-purple-950/30 border border-purple-200 dark:border-purple-800/30 hover:border-purple-300 dark:hover:border-purple-700/40 transition-colors",children:[a.jsx("div",{className:"bg-purple-600 dark:bg-purple-500 w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4",children:a.jsx(l,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),a.jsx("h3",{className:"text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 sm:mb-3",children:"Acesso Limitado"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed",children:"Apenas pediatria. Para todas as especialidades, use o MedEvo."})]}),a.jsxs("div",{className:"text-center p-4 sm:p-6 rounded-2xl bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800/30 hover:border-green-300 dark:hover:border-green-700/40 transition-colors sm:col-span-2 lg:col-span-1",children:[a.jsx("div",{className:"bg-green-600 dark:bg-green-500 w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4",children:a.jsx(I,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),a.jsx("h3",{className:"text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 sm:mb-3",children:"Experiência Completa"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed",children:"Acesse o MedEvo para recursos completos e todas as áreas."})]})]})]})}),a.jsx("div",{className:"px-8 py-12 border-b border-gray-200/50",children:a.jsxs("div",{className:"max-w-5xl mx-auto",children:[a.jsxs("div",{className:"text-center mb-10",children:[a.jsxs("div",{className:"inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 px-6 py-3 rounded-full mb-6 border border-blue-200 dark:border-blue-800/30",children:[a.jsx(o,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),a.jsx("span",{className:"text-sm font-bold text-gray-700 dark:text-gray-300",children:"Recursos Premium do MedEvo"})]}),a.jsxs("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:["Experimente os ",a.jsx("span",{className:"text-blue-600 dark:text-blue-400",children:"recursos premium"})]}),a.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed",children:"Uma prévia das ferramentas avançadas que aceleram sua aprovação na residência médica"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6",children:[a.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 rounded-2xl p-4 sm:p-6 shadow-lg border border-blue-100 dark:border-blue-800/30 hover:border-blue-200 dark:hover:border-blue-700/40 transition-colors",children:a.jsxs("div",{className:"flex items-start gap-3 sm:gap-4",children:[a.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/50 p-2 sm:p-3 rounded-xl flex-shrink-0",children:a.jsx(q,{className:"h-5 w-5 sm:h-6 sm:w-6 text-blue-600 dark:text-blue-400"})}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-1 sm:mb-2",children:"Filtros Inteligentes"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed",children:"Sistema avançado de filtros por especialidade, tema, foco, instituição e ano."})]})]})}),a.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 rounded-2xl p-4 sm:p-6 shadow-lg border border-purple-100 dark:border-purple-800/30 hover:border-purple-200 dark:hover:border-purple-700/40 transition-colors",children:a.jsxs("div",{className:"flex items-start gap-3 sm:gap-4",children:[a.jsx("div",{className:"bg-purple-100 dark:bg-purple-900/50 p-2 sm:p-3 rounded-xl flex-shrink-0",children:a.jsx(M,{className:"h-5 w-5 sm:h-6 sm:w-6 text-purple-600 dark:text-purple-400"})}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:"text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-1 sm:mb-2",children:"Mix Personalizado"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed",children:"Algoritmo inteligente que cria sessões de estudo personalizadas."})]})]})})]})]})}),a.jsx("div",{className:"px-8 py-12",children:a.jsx("div",{className:"max-w-4xl mx-auto text-center",children:a.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700",children:[a.jsxs("div",{className:"inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/30 px-4 py-2 rounded-full mb-6 border border-green-200 dark:border-green-800/30",children:[a.jsx(o,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),a.jsx("span",{className:"text-sm font-bold text-green-800 dark:text-green-300",children:"100% Gratuito"})]}),a.jsx("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4",children:"Escolha sua experiência"}),a.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed",children:"Prévia gratuita em pediatria ou experiência completa no MedEvo com todas as especialidades."}),a.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[a.jsxs(n,{onClick:()=>s("/estudos/filtros"),size:"lg",className:"w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-8 py-3 sm:py-4 text-sm sm:text-lg rounded-xl font-semibold min-h-[3rem] flex items-center justify-center gap-2",children:[a.jsx(S,{className:"h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"}),a.jsx("span",{className:"truncate",children:"Prévia Gratuita - Pediatria"}),a.jsx(E,{className:"h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"})]}),a.jsxs(n,{variant:"outline",onClick:()=>window.open("https://medevo.com.br","_blank"),className:"w-full sm:w-auto border-2 border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500 px-3 sm:px-6 py-3 text-xs sm:text-base rounded-xl min-h-[3rem] flex items-center justify-center gap-2",children:[a.jsx(F,{className:"h-4 w-4 flex-shrink-0"}),a.jsx("span",{className:"truncate",children:"Experiência Completa - MedEvo"})]})]})]})})})]})})}),a.jsx(f,{}),a.jsx(V,{isOpen:d,onClose:()=>{c(!1)},onStartStudy:w})]})};export{T as default};
