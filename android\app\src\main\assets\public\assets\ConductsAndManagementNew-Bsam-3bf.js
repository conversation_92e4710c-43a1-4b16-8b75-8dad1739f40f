import{j as e}from"./radix-core-6kBL75b5.js";import{b as t,r as s}from"./critical-DVX9Inzy.js";import{u as a}from"./query-vendor-B-7l6Nb3.js";import{m as r,j as i,C as o,b8 as l,aa as c,B as d,ac as n,a5 as m,L as x,s as u}from"./index-DV3Span9.js";import p from"./Footer-DaWfQ4Sj.js";import{a as g}from"./router-BAzpOxbo.js";import{Z as h}from"./zap-xHziMQfW.js";import{C as b}from"./clock-whXX1eHh.js";import{C as f}from"./chevron-left-2OWC_ZeG.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-OUR0fGD_.js";import"./rocket-DWbYpez3.js";import"./target-qP-p0xEy.js";import"./book-open-Ca6sFj94.js";import"./star-CdRVV6QC.js";import"./circle-help-psGgvKcv.js";import"./instagram-BdLPZF9i.js";const j=({category:t,index:s,searchTerm:a})=>{const l=g(),c=t=>{if(!a||""===a.trim())return t;const s=t.split(new RegExp(`(${a})`,"gi"));return e.jsx(e.Fragment,{children:s.map(((t,s)=>t.toLowerCase()===a.toLowerCase()?e.jsx("span",{className:"bg-yellow-200 dark:bg-yellow-900 font-semibold",children:t},s):t))})};return e.jsx(r.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"h-full",children:e.jsxs("div",{onClick:()=>{t.coming_soon||l(`/condutas-e-manejos/${t.slug}`)},className:i("relative h-full flex flex-col rounded-xl overflow-hidden","bg-white dark:bg-slate-800/90 backdrop-blur-sm shadow-md","border border-blue-100 dark:border-slate-700","transition-all duration-300",t.coming_soon?"opacity-80 cursor-not-allowed":"cursor-pointer hover:shadow-lg hover:border-blue-200 dark:hover:border-blue-800/50"),children:[e.jsx("div",{className:"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary to-blue-400"}),e.jsxs("div",{className:"p-5 flex items-start gap-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-16 h-16 rounded-lg overflow-hidden bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center",children:t.image_url?e.jsx("img",{src:t.image_url,alt:t.name,className:"w-full h-full object-cover"}):e.jsx("div",{className:"w-full h-full bg-blue-200 dark:bg-blue-800 flex items-center justify-center text-blue-600 dark:text-blue-300 text-2xl font-bold",children:t.name.charAt(0)})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-800 dark:text-white",children:c(t.name)}),t.isFuzzyMatch&&e.jsxs("span",{className:"bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800/50 flex items-center gap-1 text-xs px-2 py-0.5 whitespace-nowrap rounded-full",title:"Você quis dizer este resultado?",children:[e.jsx(h,{className:"h-3 w-3"}),e.jsx("span",{children:"Você quis dizer?"})]}),t.coming_soon&&e.jsxs("span",{className:"bg-amber-50 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800/50 flex items-center gap-1 text-xs px-2 py-0.5 whitespace-nowrap rounded-full",children:[e.jsx(b,{className:"h-3 w-3"}),e.jsx("span",{children:"Em breve"})]})]}),t.description&&e.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2",children:c(t.description)}),void 0!==t.summary_count&&t.summary_count>0&&e.jsxs("div",{className:"mt-2 text-xs text-blue-600 dark:text-blue-400 font-medium",children:[t.summary_count," ",1===t.summary_count?"tópico":"tópicos"]}),a&&t.topics&&t.topics.length>0&&e.jsxs("div",{className:"mt-2 space-y-1",children:[t.topics.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).slice(0,3).map((t=>e.jsx("div",{className:"text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 py-1 px-2 rounded inline-block mr-1 mb-1",children:c(t.name)},t.id))),t.topics.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).length>3&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",t.topics.filter((e=>e.name.toLowerCase().includes(a.toLowerCase()))).length-3," mais"]})]})]}),!t.coming_soon&&e.jsx("div",{className:"flex-shrink-0 self-center",children:e.jsx("div",{className:"p-2 rounded-full bg-blue-50 dark:bg-blue-900/20",children:e.jsx(o,{className:"h-5 w-5 text-primary dark:text-blue-400"})})})]})]})})},y=t.memo((()=>{const[i,o]=s.useState(""),b=g(),{data:y=[],isLoading:w}=a({queryKey:["conducts-categories-public"],queryFn:async()=>{const{data:e,error:t}=await u.from("pedbook_conducts_categories").select("*").order("display_order",{ascending:!0});if(t)throw t;return e},staleTime:3e5}),{data:N=[],isLoading:v}=a({queryKey:["conducts-topics-public"],queryFn:async()=>{const{data:e,error:t}=await u.from("pedbook_conducts_topics").select("id, name, slug, category_id");if(t)throw t;return e},staleTime:3e5}),{data:k=[],isLoading:_}=a({queryKey:["conducts-summaries-public"],queryFn:async()=>{const{data:e,error:t}=await u.from("pedbook_conducts_summaries").select("topic_id").eq("published",!0);if(t)throw t;return e},staleTime:3e5}),{data:C=[],isLoading:z}=a({queryKey:["conducts-optimized-public"],queryFn:async()=>{const{data:e,error:t}=await u.from("pedbook_conducts_optimized").select("topic_id").eq("published",!0);if(t)throw t;return e},staleTime:3e5}),L=w||v||_||z,q=t.useMemo((()=>{if(L||!y.length)return[];const e={};k.forEach((t=>{t.topic_id&&(e[t.topic_id]=(e[t.topic_id]||0)+1)})),C.forEach((t=>{t.topic_id&&(e[t.topic_id]=(e[t.topic_id]||0)+1)}));const t={};return N.forEach((s=>{const a=e[s.id]||0;a>0&&(t[s.category_id]=(t[s.category_id]||0)+a)})),y.map((e=>({...e,summary_count:t[e.id]||0,topics:N.filter((t=>t.category_id===e.id))}))).sort(((e,t)=>e.coming_soon&&!t.coming_soon?1:!e.coming_soon&&t.coming_soon?-1:(t.summary_count||0)-(e.summary_count||0)))}),[y,N,k,C,L]),{filteredCategories:F,fuzzyResults:T}=t.useMemo((()=>{if(!i.trim())return{filteredCategories:q,fuzzyResults:[]};const e=q.filter((e=>{const t=e.name.toLowerCase().includes(i.toLowerCase()),s=e.description?.toLowerCase().includes(i.toLowerCase()),a=e.topics?.some((e=>e.name.toLowerCase().includes(i.toLowerCase())));return t||s||a}));let t=[];return e.length<3&&i.length>=3&&(t=l(q,i,["name","description"],5,.5).filter((t=>!e.some((e=>e.id===t.item.id)))).map((e=>({...e.item,isFuzzyMatch:!0})))),{filteredCategories:e,fuzzyResults:t}}),[i,q]);return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col",children:[e.jsx(c,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-6 md:py-8",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2 md:gap-4",children:[e.jsxs(d,{variant:"ghost",size:"sm",onClick:()=>b("/"),className:"flex items-center gap-1 md:gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(f,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm md:text-base",children:"Início"})]}),e.jsx("h1",{className:"text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white",children:"Condutas e Manejos"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{className:"w-5 h-5 text-yellow-500"}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[q.length," categorias"]})]})]}),e.jsxs("div",{className:"relative mb-6",children:[e.jsx(n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 md:w-5 md:h-5"}),e.jsx(m,{type:"text",placeholder:"Buscar categorias ou tópicos...",value:i,onChange:e=>o(e.target.value),className:"pl-10 md:pl-12 text-sm md:text-base"})]}),L&&e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx(x,{className:"w-8 h-8 animate-spin text-blue-500"})}),!L&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6",children:[F.map((t=>e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(j,{category:t,index:0})},t.id))),T.map((t=>e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(j,{category:t,index:0})},`fuzzy-${t.id}`)))]}),!L&&0===F.length&&0===T.length&&e.jsxs("div",{className:"text-center py-8 md:py-12",children:[e.jsx("h3",{className:"text-lg md:text-xl lg:text-2xl font-medium text-gray-600 dark:text-gray-300",children:"Nenhuma categoria encontrada"}),e.jsx("p",{className:"text-sm md:text-base lg:text-lg text-gray-500 dark:text-gray-400 mt-2",children:"Tente ajustar sua pesquisa ou explore outras opções."})]})]})}),e.jsx(p,{})]})}));y.displayName="ConductsAndManagementNew";export{y as default};
