import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{a,u as i}from"./query-vendor-B-7l6Nb3.js";import{ao as r,a6 as n,ae as t,af as c,ag as o,ah as d,aj as l,T as m,aA as u,d as h,ar as x,as as j,at as p,au as g,B as f,s as v,D as y,e as b,f as N,g as _,z as w,ax as C,aM as k,ad as q}from"./index-D89HBjcn.js";import{s as D,A as I}from"./slugify-t5oqNdxM.js";import{A as E,a as F,b as O,c as S,d as M,e as z,f as A,g as L}from"./alert-dialog-DUTJqGTa.js";import{F as K,D as P,a as T}from"./DosageList-Cysh0LTu.js";import{A as $}from"./arrow-down-ByQAWSep.js";import{T as B}from"./trash-2-DldO7Ekc.js";import{P as R}from"./plus-7ON-wNau.js";import{P as Q}from"./pencil-B9pAnueM.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./switch-Bs48sy8W.js";function V({formData:s,categories:a,onChange:i}){const h=(e,a)=>{const r=new Set(s.required_measures);a?r.add(e):r.delete(e),i("required_measures",Array.from(r))};return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"name",children:"Nome do Medicamento"}),e.jsx(n,{id:"name",value:s.name,onChange:e=>i("name",e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"category",children:"Categoria"}),e.jsxs(t,{value:s.categoryId,onValueChange:e=>i("categoryId",e),children:[e.jsx(c,{children:e.jsx(o,{placeholder:"Selecione uma categoria"})}),e.jsx(d,{children:a?.map((s=>e.jsx(l,{value:s.id,children:s.name},s.id)))})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"description",children:"Descrição"}),e.jsx(m,{id:"description",value:s.description,onChange:e=>i("description",e.target.value)})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"brands",children:"Marcas Comerciais"}),e.jsx(n,{id:"brands",value:s.brands,onChange:e=>i("brands",e.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Medidas Requeridas"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{id:"weight",checked:s.required_measures.includes("weight"),onCheckedChange:e=>h("weight",e)}),e.jsx(r,{htmlFor:"weight",className:"cursor-pointer",children:"Peso"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{id:"age",checked:s.required_measures.includes("age"),onCheckedChange:e=>h("age",e)}),e.jsx(r,{htmlFor:"age",className:"cursor-pointer",children:"Idade"})]})]})]})]})}function W({formData:s,onChange:a}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"contraindications",children:"Contraindicações"}),e.jsx(m,{id:"contraindications",value:s.contraindications||"",onChange:e=>a("contraindications",e.target.value),className:"min-h-[150px]"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"guidelines",children:"Orientações"}),e.jsx(m,{id:"guidelines",value:s.guidelines||"",onChange:e=>a("guidelines",e.target.value),className:"min-h-[150px]"})]})]})}function G({formData:s,onChange:a}){return e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(r,{htmlFor:"scientific_references",children:"Referências Científicas"}),e.jsx(m,{id:"scientific_references",value:s.scientific_references||"",onChange:e=>a("scientific_references",e.target.value),className:"min-h-[200px]"})]})})}function H({medication:i,categories:r,onClose:n}){const[t,c]=s.useState({name:i?.name||"",categoryId:i?.category_id||"",description:i?.description||"",brands:i?.brands||"",contraindications:i?.contraindications||"",guidelines:i?.guidelines||"",scientific_references:i?.scientific_references||"",required_measures:i?.required_measures||["weight","age"]}),{toast:o}=h(),d=a();s.useEffect((()=>{i&&c({name:i.name||"",categoryId:i.category_id||"",description:i.description||"",brands:i.brands||"",contraindications:i.contraindications||"",guidelines:i.guidelines||"",scientific_references:i.scientific_references||"",required_measures:i.required_measures||["weight","age"]})}),[i]);const l=(e,s)=>{c((a=>({...a,[e]:s})))};return e.jsxs("form",{id:"medication-form",onSubmit:async e=>{e.preventDefault();try{const e=D(t.name),s={name:t.name,category_id:t.categoryId,description:t.description,brands:t.brands,contraindications:t.contraindications,guidelines:t.guidelines,scientific_references:t.scientific_references,required_measures:t.required_measures,slug:e};if(i){const{error:e}=await v.from("pedbook_medications").update(s).eq("id",i.id);if(e)throw e;o({title:"Medicamento atualizado com sucesso!",description:`O medicamento ${t.name} foi atualizado.`})}else{const{error:e}=await v.from("pedbook_medications").insert([s]);if(e)throw e;o({title:"Medicamento criado com sucesso!",description:`O medicamento ${t.name} foi adicionado.`})}await d.invalidateQueries({queryKey:["medications"]}),n()}catch(s){o({variant:"destructive",title:"Erro ao salvar medicamento",description:s.message||"Ocorreu um erro ao salvar o medicamento."})}},className:"space-y-4",children:[e.jsxs(x,{defaultValue:"basic",className:"w-full",children:[e.jsxs(j,{className:"grid w-full grid-cols-3",children:[e.jsx(p,{value:"basic",children:"Informações Básicas"}),e.jsx(p,{value:"details",children:"Detalhes"}),e.jsx(p,{value:"references",children:"Referências"})]}),e.jsx(g,{value:"basic",children:e.jsx(V,{formData:t,categories:r,onChange:l})}),e.jsx(g,{value:"details",children:e.jsx(W,{formData:t,onChange:l})}),e.jsx(g,{value:"references",children:e.jsx(G,{formData:t,onChange:l})})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(f,{type:"button",variant:"outline",onClick:n,children:"Cancelar"}),e.jsx(f,{type:"submit",children:"Salvar"})]})]})}function J({medication:s,isOpen:i,onClose:r}){const{toast:n}=h(),t=a();return e.jsx(E,{open:i,onOpenChange:r,children:e.jsxs(F,{children:[e.jsxs(O,{children:[e.jsx(S,{children:"Confirmar exclusão"}),e.jsxs(M,{children:['Tem certeza que deseja excluir o medicamento "',s?.name,'"? Esta ação não pode ser desfeita.']})]}),e.jsxs(z,{children:[e.jsx(A,{children:"Cancelar"}),e.jsx(L,{onClick:async()=>{try{const{error:e}=await v.from("pedbook_medications").delete().eq("id",s?.id);if(e)throw e;n({title:"Medicamento excluído com sucesso!",description:`O medicamento ${s?.name} foi excluído.`}),t.invalidateQueries({queryKey:["medications"]}),r()}catch(e){n({variant:"destructive",title:"Erro ao excluir medicamento",description:e.message||"Ocorreu um erro ao excluir o medicamento."})}},className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Excluir"})]})]})})}function U({medicationId:r}){const[t,c]=s.useState({name:"",description:""}),[o,d]=s.useState(null),{toast:l}=h();a();const{data:u,refetch:x}=i({queryKey:["medication-use-cases",r],queryFn:async()=>{const{data:e,error:s}=await v.from("pedbook_medication_use_cases").select("*").eq("medication_id",r).order("display_order",{ascending:!0});if(s)throw s;return e}}),j=async(e,s)=>{if(!u)return;const a=u.findIndex((s=>s.id===e));if(-1===a)return;const i="up"===s?a-1:a+1;if(i<0||i>=u.length)return;const r=u[a],n=u[i];try{const{error:e}=await v.from("pedbook_medication_use_cases").update({display_order:n.display_order}).eq("id",r.id),{error:s}=await v.from("pedbook_medication_use_cases").update({display_order:r.display_order}).eq("id",n.id);if(e||s)throw e||s;x(),l({title:"Ordem atualizada",description:"A ordem das indicações foi atualizada com sucesso."})}catch(t){l({variant:"destructive",title:"Erro ao reordenar indicações",description:t.message})}};return u?.length?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-4",children:u.map(((s,a)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 space-y-2 flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.name}),s.description&&e.jsx("p",{className:"text-sm text-gray-600",children:s.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(f,{variant:"ghost",size:"icon",onClick:()=>j(s.id,"up"),disabled:0===a,className:"hover:bg-primary/10",children:e.jsx(I,{className:"h-4 w-4"})}),e.jsx(f,{variant:"ghost",size:"icon",onClick:()=>j(s.id,"down"),disabled:a===u.length-1,className:"hover:bg-primary/10",children:e.jsx($,{className:"h-4 w-4"})}),e.jsx(f,{variant:"ghost",size:"icon",onClick:()=>d(s.id),className:"hover:bg-destructive/10",children:e.jsx(B,{className:"h-4 w-4"})})]})]},s.id)))}),e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsx("h4",{className:"font-medium",children:"Adicionar Nova Indicação"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{placeholder:"Nome da indicação",value:t.name,onChange:e=>c({...t,name:e.target.value})}),e.jsx(m,{placeholder:"Descrição (opcional)",value:t.description,onChange:e=>c({...t,description:e.target.value})}),e.jsxs(f,{onClick:async()=>{try{const{data:e,error:s}=await v.from("pedbook_medication_use_cases").select("display_order").eq("medication_id",r).order("display_order",{ascending:!1}).limit(1);if(s)throw s;const a=e&&e[0]?e[0].display_order+1:1,{error:i}=await v.from("pedbook_medication_use_cases").insert({medication_id:r,name:t.name,description:t.description,display_order:a});if(i)throw i;l({title:"Indicação adicionada com sucesso!",description:"A nova indicação de uso foi criada."}),c({name:"",description:""}),x()}catch(e){l({variant:"destructive",title:"Erro ao adicionar indicação",description:e.message})}},disabled:!t.name,className:"w-full",children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Adicionar Indicação"]})]})]}),e.jsx(E,{open:!!o,onOpenChange:()=>d(null),children:e.jsxs(F,{children:[e.jsxs(O,{children:[e.jsx(S,{children:"Confirmar exclusão"}),e.jsx(M,{children:"Tem certeza que deseja excluir esta indicação de uso? Esta ação não pode ser desfeita."})]}),e.jsxs(z,{children:[e.jsx(A,{children:"Cancelar"}),e.jsx(L,{onClick:async()=>{if(o)try{const{error:e}=await v.from("pedbook_medication_use_cases").delete().eq("id",o);if(e)throw e;l({title:"Indicação excluída com sucesso!",description:"A indicação de uso foi removida."}),d(null),x()}catch(e){l({variant:"destructive",title:"Erro ao excluir indicação",description:e.message})}},children:"Confirmar"})]})]})})]}):e.jsx("div",{className:"text-center text-gray-500 py-8",children:"Nenhuma indicação de uso cadastrada para este medicamento"})}function X({medication:a,categories:i,isOpen:r,onClose:n}){const[t,c]=s.useState(!1),[o,d]=s.useState(!1),[l,m]=s.useState(null);return e.jsxs(e.Fragment,{children:[e.jsx(y,{open:r,onOpenChange:n,children:e.jsxs(b,{className:"max-w-2xl h-[85vh]",children:[e.jsxs(N,{className:"px-6 pt-6 pb-4",children:[e.jsx(_,{children:a?"Editar Medicamento":"Novo Medicamento"}),e.jsx(w,{children:a?"Edite as informações do medicamento":"Preencha as informações do novo medicamento"})]}),e.jsx(C,{className:"h-[calc(85vh-120px)] px-6",children:e.jsxs("div",{className:"space-y-6 pb-6",children:[e.jsx(K,{formId:"medication-form",storageKey:`medication_${a?.id||"new"}`,debug:!0,children:e.jsx(H,{medication:a,categories:i,onClose:n})}),a&&e.jsxs("div",{className:"space-y-6",children:[e.jsx(U,{medicationId:a.id}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Dosagens"}),e.jsxs(f,{type:"button",onClick:()=>d(!0),children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Nova Dosagem"]})]}),e.jsx(P,{selectedMedicationId:a.id,onEdit:e=>{m(e),d(!0)}}),e.jsxs(f,{type:"button",variant:"destructive",onClick:()=>c(!0),className:"w-full",children:[e.jsx(B,{className:"h-4 w-4 mr-2"}),"Excluir Medicamento"]})]})]})})]})}),e.jsx(J,{medication:a,isOpen:t,onClose:()=>c(!1)}),a&&e.jsx(y,{open:o,onOpenChange:e=>{d(e),e||m(null)},children:e.jsxs(b,{className:"max-w-2xl",children:[e.jsxs(N,{children:[e.jsx(_,{children:l?"Editar Dosagem":"Nova Dosagem"}),e.jsx(w,{children:l?"Edite as informações da dosagem":"Preencha as informações da nova dosagem"})]}),e.jsx(T,{medicationId:a.id,dosage:l,onSuccess:()=>{d(!1),m(null)},onCancel:()=>{d(!1),m(null)}})]})})]})}function Y(){const[a,r]=s.useState(""),[t,c]=s.useState(null),[o,d]=s.useState(!1),{data:l}=i({queryKey:["medication-categories"],queryFn:async()=>{const{data:e,error:s}=await v.from("pedbook_medication_categories").select("*").order("name");if(s)throw s;return e}}),{data:m}=i({queryKey:["medications"],queryFn:async()=>{const{data:e,error:s}=await v.from("pedbook_medications").select("\n          *,\n          pedbook_medication_categories (\n            name\n          )\n        ").order("name");if(s)throw s;return e}}),u=m?.filter((e=>e.name.toLowerCase().includes(a.toLowerCase())||e.description?.toLowerCase().includes(a.toLowerCase())||e.brands?.toLowerCase().includes(a.toLowerCase())||e.pedbook_medication_categories?.name.toLowerCase().includes(a.toLowerCase())));return e.jsxs("div",{className:k.pageBackground("container mx-auto py-8"),children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold dark:text-gray-100",children:"Medicamentos"}),e.jsxs(f,{onClick:()=>{c(null),d(!0)},children:[e.jsx(R,{className:"mr-2 h-4 w-4"}),"Novo Medicamento"]})]}),e.jsxs("div",{className:"relative mb-6",children:[e.jsx(q,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(n,{type:"search",placeholder:"Pesquisar medicamentos...",value:a,onChange:e=>r(e.target.value),className:"pl-10 dark:bg-slate-800 dark:border-slate-700 dark:text-gray-100"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u?.map((s=>e.jsx("div",{className:k.card("p-6 space-y-2"),children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold dark:text-gray-100",children:s.name}),e.jsx("span",{className:"inline-block px-2 py-1 rounded-full text-xs bg-primary/10 text-primary dark:bg-blue-900/30 dark:text-blue-400 mt-2",children:s.pedbook_medication_categories?.name||"Sem categoria"}),s.description&&e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:s.description}),s.brands&&e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:s.brands})]}),e.jsx(f,{variant:"ghost",size:"icon",onClick:()=>{c(s),d(!0)},className:"dark:text-gray-300 dark:hover:text-white",children:e.jsx(Q,{className:"h-4 w-4"})})]})},s.id)))}),e.jsx(X,{medication:t,categories:l||[],isOpen:o,onClose:()=>{d(!1),c(null)}})]})}export{Y as default};
