import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{d as a,ay as i,B as t,a8 as n,U as r,Y as c,_ as l,a0 as d,V as o,ak as m,ar as h,as as x,at as u,au as j,L as g,az as p,ad as f,a6 as v,aA as N,C as _,aB as b,aC as y,aq as w,D as k,e as C,f as A,g as S,z as q,X as z,aD as F,s as E}from"./index-CNG-Xj2g.js";import{A as M,a as I,b as P}from"./alert-BT_NObbd.js";import{T,a as U,b as $,c as K,d as O,e as D}from"./table-Dku9nJtf.js";import{a as L,u as R,c as V}from"./query-vendor-B-7l6Nb3.js";import{M as B}from"./milk-sZCreeF9.js";import{R as Q}from"./refresh-cw-D5Pk4unN.js";import{D as Y}from"./database-CAe0AgAU.js";import{C as G}from"./chevron-left-Ep4pqQcd.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const H=()=>{const{toast:H}=a(),J=L(),[W,X]=s.useState(""),[Z,ee]=s.useState([]),[se,ae]=s.useState([]),[ie,te]=s.useState(!1),[ne,re]=s.useState(!1),[ce,le]=s.useState(null),[de,oe]=s.useState("select"),[me,he]=s.useState(null),[xe,ue]=s.useState(1),je=10,[ge,pe]=s.useState(!1);i(),s.useEffect((()=>()=>{}),[]);const{data:fe,isLoading:ve,refetch:Ne}=R({queryKey:["breastfeeding-sections-with-incomplete-medications"],queryFn:async()=>{const{data:e,error:s}=await E.from("pedbook_breastfeeding_sections").select("id, name").order("name");if(s)throw s;const a=[];for(const i of e){const{count:e,error:s}=await E.from("pedbook_breastfeeding_medications").select("id",{count:"exact",head:!0}).eq("section_id",i.id).is("efeitos_no_lactente",null);if(s)throw s;e&&e>0&&a.push({id:i.id,name:i.name,count:e})}return a},staleTime:0,gcTime:0}),{data:_e,isLoading:be,error:ye,refetch:we}=R({queryKey:["breastfeeding-medications-by-section",me,W,xe],queryFn:async()=>{if(!me)return[];let e=E.from("pedbook_breastfeeding_medications").select("\n          id,\n          name,\n          compatibility_level,\n          usage_description,\n          additional_info,\n          efeitos_no_lactente,\n          alternativas_seguras,\n          orientacoes_uso,\n          section_id,\n          subsection_id,\n          section:pedbook_breastfeeding_sections(id, name),\n          subsection:pedbook_breastfeeding_subsections(name)\n        ").eq("section_id",me).is("efeitos_no_lactente",null).order("name").range((xe-1)*je,xe*je-1);W&&(e=e.ilike("name",`%${W}%`));const{data:s,error:a}=await e;if(a)throw a;return s},enabled:!!me}),{data:ke}=R({queryKey:["breastfeeding-medications-count",me,W],queryFn:async()=>{if(!me)return 0;let e=E.from("pedbook_breastfeeding_medications").select("id",{count:"exact",head:!0}).eq("section_id",me).is("efeitos_no_lactente",null);W&&(e=e.ilike("name",`%${W}%`));const{count:s,error:a}=await e;if(a)throw a;return s||0},enabled:!!me}),Ce=V({mutationFn:async e=>{const{error:s}=await E.from("pedbook_breastfeeding_medications").update({usage_description:e.usage_description,efeitos_no_lactente:e.efeitos_no_lactente,alternativas_seguras:e.alternativas_seguras,orientacoes_uso:e.orientacoes_uso,updated_at:(new Date).toISOString()}).eq("id",e.id);if(s)throw s;return e.id},onSuccess:()=>{J.invalidateQueries({queryKey:["breastfeeding-medications-by-section"]}),J.invalidateQueries({queryKey:["breastfeeding-sections-with-incomplete-medications"]}),setTimeout((()=>{Ne()}),500)}}),[Ae,Se]=s.useState(!1),[qe,ze]=s.useState(null),Fe=async e=>{const s=se.find((s=>s.id===e));if(!s)return;const a=Ae&&qe?{id:s.id,usage_description:qe.uso_amamentacao,efeitos_no_lactente:qe.efeitos_no_lactente,alternativas_seguras:qe.alternativas_seguras,orientacoes_uso:qe.orientacoes_uso}:{id:s.id,usage_description:s.enhanced.uso_amamentacao,efeitos_no_lactente:s.enhanced.efeitos_no_lactente,alternativas_seguras:s.enhanced.alternativas_seguras,orientacoes_uso:s.enhanced.orientacoes_uso};try{await Ce.mutateAsync(a),H({title:"Alterações aplicadas",description:`As informações de "${s.name}" foram atualizadas com sucesso.`}),ae((s=>s.filter((s=>s.id!==e)))),re(!1)}catch(i){H({title:"Erro ao aplicar alterações",description:i.message,variant:"destructive"})}},Ee=s=>{switch(s.toLowerCase()){case"verde":return e.jsx(p,{className:"bg-green-100 text-green-800",children:"Compatível"});case"amarelo":return e.jsx(p,{className:"bg-yellow-100 text-yellow-800",children:"Cautela"});case"vermelho":return e.jsx(p,{className:"bg-red-100 text-red-800",children:"Incompatível"});default:return e.jsx(p,{className:"bg-gray-100 text-gray-800",children:"Desconhecido"})}};return e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-6",children:[e.jsxs(t,{variant:"ghost",size:"sm",className:"gap-1",onClick:()=>window.history.back(),children:[e.jsx(n,{className:"h-4 w-4"}),"Voltar"]}),e.jsx("h1",{className:"text-3xl font-bold",children:"Aprimoramento de Medicamentos na Amamentação"})]}),e.jsxs(r,{className:"mb-6 border-l-4 border-l-pink-400",children:[e.jsxs(c,{children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(B,{className:"h-6 w-6 text-pink-500"}),e.jsx(l,{children:"Aprimoramento de Medicamentos na Amamentação"})]})}),e.jsx(d,{children:"Utilize inteligência artificial para aprimorar informações sobre medicamentos durante a amamentação, incluindo efeitos no lactente, alternativas seguras e orientações de uso."})]}),e.jsx(o,{children:e.jsxs(M,{className:"bg-blue-50 border-blue-200 text-blue-800",children:[e.jsx(m,{className:"h-4 w-4"}),e.jsx(I,{children:"Como funciona"}),e.jsxs(P,{children:["1. Selecione os medicamentos que deseja aprimorar",e.jsx("br",{}),'2. Clique em "Aprimorar com IA" para gerar sugestões',e.jsx("br",{}),"3. Revise as sugestões e aplique as que considerar adequadas"]})]})})]}),e.jsxs(h,{value:de,onValueChange:oe,children:[e.jsxs(x,{className:"mb-4",children:[e.jsx(u,{value:"select",children:"Selecionar Medicamentos"}),e.jsxs(u,{value:"review",disabled:0===se.length,children:["Revisar Sugestões ",se.length>0&&`(${se.length})`]})]}),e.jsx(j,{value:"select",children:e.jsxs(r,{children:[e.jsxs(c,{children:[e.jsx(l,{children:"Selecione os Medicamentos"}),e.jsx(d,{children:"Escolha os medicamentos que deseja aprimorar com IA"})]}),e.jsx(o,{children:ve?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(g,{className:"h-8 w-8 animate-spin text-primary"})}):0===fe?.length?e.jsxs(M,{children:[e.jsx(I,{children:"Nenhuma seção disponível"}),e.jsx(P,{children:"Não há medicamentos incompletos para aprimorar."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Selecione uma classe de medicamentos:"}),e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>{Ne()},className:"flex items-center gap-1",children:[e.jsx(Q,{className:"h-3 w-3"}),"Atualizar"]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:fe?.map((s=>e.jsxs(t,{variant:me===s.id?"default":"outline",className:"justify-start gap-2",onClick:()=>{he(s.id),ue(1),ee([])},children:[e.jsx(Y,{className:"h-4 w-4"}),e.jsx("span",{className:"truncate",children:s.name}),e.jsx(p,{className:"ml-auto",children:s.count})]},s.id)))})]}),me&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(f,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"}),e.jsx(v,{placeholder:"Buscar medicamento...",className:"pl-9",value:W,onChange:e=>{X(e.target.value),ue(1)}})]}),e.jsx(t,{variant:"outline",size:"icon",onClick:()=>we(),children:e.jsx(Q,{className:"h-4 w-4"})})]}),be?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(g,{className:"h-8 w-8 animate-spin text-primary"})}):ye?e.jsxs(M,{variant:"destructive",children:[e.jsx(I,{children:"Erro ao carregar medicamentos"}),e.jsx(P,{children:"Ocorreu um erro ao buscar os medicamentos. Tente novamente mais tarde."})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border rounded-md overflow-hidden",children:e.jsxs(T,{children:[e.jsx(U,{children:e.jsxs($,{children:[e.jsx(K,{className:"w-12",children:e.jsx(N,{checked:_e?.length>0&&Z.length===_e.length,onCheckedChange:e=>{ee(e&&_e?.map((e=>e.id))||[])}})}),e.jsx(K,{children:"Nome"}),e.jsx(K,{children:"Compatibilidade"}),e.jsx(K,{className:"hidden lg:table-cell",children:"Subclasse"}),e.jsx(K,{className:"hidden md:table-cell",children:"Uso Atual"})]})}),e.jsx(O,{children:0===_e?.length?e.jsx($,{children:e.jsx(D,{colSpan:5,className:"text-center py-4 text-gray-500",children:"Nenhum medicamento encontrado"})}):_e?.map((s=>e.jsxs($,{children:[e.jsx(D,{children:e.jsx(N,{checked:Z.includes(s.id),onCheckedChange:e=>{ee(e?e=>[...e,s.id]:e=>e.filter((e=>e!==s.id)))}})}),e.jsx(D,{className:"font-medium",children:s.name}),e.jsx(D,{children:Ee(s.compatibility_level)}),e.jsx(D,{className:"hidden lg:table-cell",children:s.subsection?.name||"-"}),e.jsx(D,{className:"hidden md:table-cell max-w-xs truncate",children:s.usage_description})]},s.id)))})]})}),ke&&ke>je&&e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:["Mostrando ",(xe-1)*je+1," a ",Math.min(xe*je,ke)," de ",ke," medicamentos"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{variant:"outline",size:"sm",onClick:()=>ue((e=>Math.max(1,e-1))),disabled:1===xe,children:e.jsx(G,{className:"h-4 w-4"})}),e.jsxs("div",{className:"text-sm",children:["Página ",xe," de ",Math.ceil(ke/je)]}),e.jsx(t,{variant:"outline",size:"sm",onClick:()=>ue((e=>Math.min(Math.ceil(ke/je),e+1))),disabled:xe>=Math.ceil(ke/je),children:e.jsx(_,{className:"h-4 w-4"})})]})]})]})]})]})}),e.jsxs(b,{className:"flex justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[Z.length," medicamentos selecionados"]}),e.jsx(t,{onClick:async()=>{if(0!==Z.length){te(!0);try{const e=await E.functions.invoke("enhance-breastfeeding-medications",{body:{medicationIds:Z}});if(e.error)throw new Error(e.error.message);ae(e.data.results),oe("review"),H({title:"Aprimoramento concluído",description:`${e.data.results.length} medicamentos foram aprimorados com sucesso.`})}catch(e){H({title:"Erro ao aprimorar medicamentos",description:e.message,variant:"destructive"})}finally{te(!1)}}else H({title:"Nenhum medicamento selecionado",description:"Selecione pelo menos um medicamento para aprimorar.",variant:"destructive"})},disabled:0===Z.length||ie,className:"gap-2",children:ie?e.jsxs(e.Fragment,{children:[e.jsx(g,{className:"h-4 w-4 animate-spin"}),"Processando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(y,{className:"h-4 w-4"}),"Aprimorar com IA"]})})]})]})}),e.jsx(j,{value:"review",children:e.jsxs(r,{children:[e.jsxs(c,{className:"flex flex-row items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(l,{children:"Revisar Sugestões da IA"}),e.jsx(d,{children:"Revise e aplique as sugestões geradas pela IA para cada medicamento"})]}),se.length>0&&e.jsx(t,{onClick:async()=>{if(0!==se.length){pe(!0);try{const s=[...se];let a=0;for(const i of s){const s={id:i.id,usage_description:i.enhanced.uso_amamentacao,efeitos_no_lactente:i.enhanced.efeitos_no_lactente,alternativas_seguras:i.enhanced.alternativas_seguras,orientacoes_uso:i.enhanced.orientacoes_uso};try{await Ce.mutateAsync(s),a++,ae((e=>e.filter((e=>e.id!==i.id))))}catch(e){}}J.invalidateQueries({queryKey:["breastfeeding-sections-with-incomplete-medications"]}),setTimeout((()=>{Ne()}),1e3),H({title:"Processamento em massa concluído",description:`${a} de ${s.length} medicamentos foram atualizados com sucesso.`})}catch(e){H({title:"Erro no processamento em massa",description:e.message,variant:"destructive"})}finally{pe(!1)}}},disabled:ge,className:"gap-2",children:ge?e.jsxs(e.Fragment,{children:[e.jsx(g,{className:"h-4 w-4 animate-spin"}),"Aplicando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-4 w-4"}),"Aprovar Todos (",se.length,")"]})})]}),e.jsx(o,{children:0===se.length?e.jsxs("div",{className:"space-y-4 text-center py-6",children:[e.jsxs(M,{children:[e.jsx(I,{children:"Nenhuma sugestão disponível"}),e.jsx(P,{children:"Todas as sugestões foram processadas."})]}),e.jsxs(t,{onClick:()=>oe("select"),className:"mt-4",children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Voltar para Seleção de Medicamentos"]})]}):e.jsx("div",{className:"border rounded-md overflow-hidden",children:e.jsxs(T,{children:[e.jsx(U,{children:e.jsxs($,{children:[e.jsx(K,{children:"Nome"}),e.jsx(K,{children:"Compatibilidade"}),e.jsx(K,{className:"hidden md:table-cell",children:"Uso Original"}),e.jsx(K,{className:"w-32 text-right",children:"Ações"})]})}),e.jsx(O,{children:se.map((s=>e.jsxs($,{children:[e.jsx(D,{className:"font-medium",children:s.name}),e.jsx(D,{children:Ee(s.original.compatibility_level)}),e.jsx(D,{className:"hidden md:table-cell max-w-xs truncate",children:s.original.usage_description}),e.jsx(D,{className:"text-right",children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(t,{variant:"outline",size:"sm",onClick:()=>(e=>{le(e),ze({uso_amamentacao:e.enhanced.uso_amamentacao,efeitos_no_lactente:e.enhanced.efeitos_no_lactente,alternativas_seguras:Array.isArray(e.enhanced.alternativas_seguras)?e.enhanced.alternativas_seguras:[e.enhanced.alternativas_seguras],orientacoes_uso:e.enhanced.orientacoes_uso}),Se(!1),re(!0)})(s),children:"Visualizar"}),e.jsx(t,{variant:"default",size:"sm",onClick:()=>Fe(s.id),disabled:Ce.isPending,children:Ce.isPending?e.jsx(g,{className:"h-4 w-4 animate-spin"}):e.jsx(w,{className:"h-4 w-4"})})]})})]},s.id)))})]})})})]})})]}),e.jsx(k,{open:ne,onOpenChange:re,children:e.jsxs(C,{className:"max-w-3xl max-h-[90vh] overflow-y-auto",children:[e.jsxs(A,{children:[e.jsxs(S,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5 text-pink-500"}),ce?.name,ce&&Ee(ce.original.compatibility_level)]}),e.jsx(q,{children:Ae?"Edite as informações conforme necessário":"Comparação entre as informações originais e as sugestões da IA"})]}),ce&&qe&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs(r,{children:[e.jsx(c,{className:"pb-2",children:e.jsx(l,{className:"text-sm flex items-center",children:e.jsx("span",{className:"mr-2",children:"Informações Originais"})})}),e.jsx(o,{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-1",children:"Uso na Amamentação"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:ce.original.usage_description})]})})]}),e.jsxs(r,{children:[e.jsx(c,{className:"pb-2",children:e.jsxs(l,{className:"text-sm flex items-center",children:[e.jsx("span",{className:"mr-2",children:Ae?"Edição de Informações":"Sugestões da IA"}),!Ae&&e.jsx(y,{className:"h-4 w-4 text-yellow-500"})]})}),e.jsx(o,{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-1",children:"Uso na Amamentação"}),Ae?e.jsx(v,{value:qe.uso_amamentacao,onChange:e=>ze({...qe,uso_amamentacao:e.target.value}),className:"text-sm mt-1"}):e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:ce.enhanced.uso_amamentacao})]})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-medium",children:Ae?"Editar Campos Adicionais":"Novos Campos Sugeridos"}),e.jsx(t,{variant:"outline",size:"sm",onClick:()=>Se(!Ae),children:Ae?"Cancelar Edição":"Editar Campos"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-1",children:"Efeitos no Lactente"}),Ae?e.jsx(v,{value:qe.efeitos_no_lactente,onChange:e=>ze({...qe,efeitos_no_lactente:e.target.value}),className:"text-sm mt-1"}):e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:ce.enhanced.efeitos_no_lactente})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-1",children:"Alternativas Seguras"}),Ae?e.jsxs("div",{className:"space-y-2",children:[qe.alternativas_seguras.map(((s,a)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{value:s,onChange:e=>{const s=[...qe.alternativas_seguras];s[a]=e.target.value,ze({...qe,alternativas_seguras:s})},className:"text-sm flex-1"}),e.jsx(t,{variant:"outline",size:"icon",type:"button",onClick:()=>{const e=qe.alternativas_seguras.filter(((e,s)=>s!==a));ze({...qe,alternativas_seguras:e})},children:e.jsx(z,{className:"h-4 w-4"})})]},a))),e.jsx(t,{variant:"outline",type:"button",size:"sm",className:"mt-2",onClick:()=>{ze({...qe,alternativas_seguras:[...qe.alternativas_seguras,""]})},children:"Adicionar Alternativa"})]}):e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:Array.isArray(ce.enhanced.alternativas_seguras)?e.jsx("ul",{className:"list-disc pl-5 space-y-1",children:ce.enhanced.alternativas_seguras.map(((s,a)=>e.jsx("li",{children:s},a)))}):ce.enhanced.alternativas_seguras})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-1",children:"Orientações de Uso"}),Ae?e.jsx(v,{value:qe.orientacoes_uso,onChange:e=>ze({...qe,orientacoes_uso:e.target.value}),className:"text-sm mt-1"}):e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:ce.enhanced.orientacoes_uso})]})]})]}),e.jsxs(F,{className:"flex justify-between items-center",children:[e.jsx(t,{variant:"outline",onClick:()=>re(!1),children:"Fechar"}),e.jsx(t,{onClick:()=>{ce&&Fe(ce.id)},disabled:Ce.isPending||!ce,children:Ce.isPending?e.jsxs(e.Fragment,{children:[e.jsx(g,{className:"h-4 w-4 animate-spin mr-2"}),"Aplicando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),"Aplicar Alterações"]})})]})]})})]})};export{H as default};
