import{j as e}from"./radix-core-6kBL75b5.js";import{r as i}from"./critical-DVX9Inzy.js";import{d as a,s,R as r,W as t,Z as o,$ as n,U as c,T as m,B as d,L as l,Y as p,aw as x}from"./index-DwBJcqzE.js";import{T as h,a as j,b as f,c as v,d as u,e as _}from"./table-CA3aI-dx.js";import{A as g,a as N,b as w}from"./alert-XCiMnFRg.js";import{C as y}from"./copy--jc_nvAW.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const b=()=>{const[b,C]=i.useState(""),[k,A]=i.useState(!1),[I,T]=i.useState([]),[z,E]=i.useState(!0),{toast:S}=a();i.useEffect((()=>{M()}),[]);const M=async()=>{E(!0);try{const{data:e,error:i}=await s.rpc("admin_get_all_medications");if(i)throw i;T(e||[])}catch(e){S({variant:"destructive",title:"Erro ao carregar medicamentos",description:e.message||"Não foi possível carregar os medicamentos. Tente novamente."})}finally{E(!1)}},P=e=>{const i=e.split("|").map((e=>e.trim()));return 2!==i.length?null:{active_ingredient:i[0],commercial_names:i[1]}},q=e=>{navigator.clipboard.writeText(e).then((()=>{S({title:"Copiado!",description:"Informação copiada para a área de transferência."})}),(e=>{S({variant:"destructive",title:"Erro ao copiar",description:"Não foi possível copiar o texto."})}))};return e.jsx("div",{className:"container mx-auto py-8",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs(r,{children:[e.jsxs(t,{children:[e.jsx(o,{children:"Importar Medicamentos para Interações"}),e.jsx(n,{children:'Cole a lista de medicamentos no formato "Princípio ativo | Nome Comercial®, Nome Comercial®, ..."'})]}),e.jsx(c,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(m,{className:"min-h-[200px] font-mono text-sm",placeholder:"Azitromicina Di-hidratada | Astro®, Astro IV®, Azi®...",value:b,onChange:e=>C(e.target.value),disabled:k}),e.jsx(d,{onClick:async()=>{if(b.trim()){A(!0);try{const e=b.split("\n").filter((e=>e.trim())),i=[],a=[],r={};for(let s=0;s<e.length;s++){const i=e[s],t=P(i);if(!t){a.push(`Linha ${s+1}: Formato inválido`);continue}const{active_ingredient:o,commercial_names:n}=t;if(o in r){if(n){const e=r[o],i=n||"";i&&e?r[o]=e+", "+i:i&&(r[o]=i)}}else r[o]=n||""}for(const[s,t]of Object.entries(r))i.push({active_ingredient:s,commercial_names:t});if(a.length>0)return void S({variant:"destructive",title:`${a.length} erros encontrados`,description:a.join(", ")});for(const t of i){const{data:e,error:i}=await s.from("pedbook_drug_interaction_medications").select("*").eq("active_ingredient",t.active_ingredient).maybeSingle();if(i)throw i;if(e){let i=t.commercial_names;if(e.commercial_names&&t.commercial_names){const a=e.commercial_names.split(", "),s=t.commercial_names.split(", ");i=[...new Set([...a,...s])].filter((e=>e.trim())).join(", ")}else e.commercial_names&&(i=e.commercial_names);const{error:a}=await s.from("pedbook_drug_interaction_medications").update({commercial_names:i}).eq("id",e.id);if(a)throw a}else{const{error:e}=await s.from("pedbook_drug_interaction_medications").insert([t]);if(e)throw e}}S({title:"Importação concluída",description:`${i.length} medicamentos processados com sucesso.`}),C(""),M()}catch(e){S({variant:"destructive",title:"Erro na importação",description:e.message||"Não foi possível importar os medicamentos. Tente novamente."})}finally{A(!1)}}else S({variant:"destructive",title:"Texto vazio",description:"Por favor, insira algum conteúdo para importar."})},disabled:k||!b.trim(),children:k?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"mr-2 h-4 w-4 animate-spin"}),"Processando..."]}):"Importar Medicamentos"}),e.jsxs(g,{children:[e.jsx(p,{className:"h-4 w-4"}),e.jsx(N,{children:"Formato esperado"}),e.jsx(w,{children:'Cada linha deve estar no formato: "Princípio ativo | Nome1®, Nome2®, Nome3®"'})]})]})})]}),e.jsxs(r,{children:[e.jsxs(t,{children:[e.jsx(o,{children:"Medicamentos para Interações"}),e.jsx(n,{children:"Lista de medicamentos cadastrados para interações medicamentosas"})]}),e.jsx(c,{children:z?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(l,{className:"h-8 w-8 animate-spin text-primary"})}):0===I.length?e.jsx("div",{className:"text-center py-8 text-muted-foreground",children:"Nenhum medicamento cadastrado."}):e.jsx(x,{className:"h-[500px]",children:e.jsxs(h,{children:[e.jsx(j,{children:e.jsxs(f,{children:[e.jsx(v,{children:"Princípio Ativo"}),e.jsx(v,{children:"Nomes Comerciais"}),e.jsx(v,{className:"w-24",children:"Ações"})]})}),e.jsx(u,{children:I.map((i=>e.jsxs(f,{children:[e.jsx(_,{className:"font-medium",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:i.active_ingredient}),e.jsx(d,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>q(i.active_ingredient),children:e.jsx(y,{className:"h-3 w-3"})})]})}),e.jsx(_,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"truncate max-w-[400px]",children:i.commercial_names||"—"}),i.commercial_names&&e.jsx(d,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>q(i.commercial_names),children:e.jsx(y,{className:"h-3 w-3"})})]})}),e.jsx(_,{children:e.jsx(d,{variant:"destructive",size:"sm",onClick:()=>(async e=>{try{const{error:i}=await s.from("pedbook_drug_interaction_medications").delete().eq("id",e);if(i)throw i;S({title:"Medicamento excluído",description:"Medicamento removido com sucesso."}),T(I.filter((i=>i.id!==e)))}catch(i){S({variant:"destructive",title:"Erro ao excluir",description:i.message||"Não foi possível excluir o medicamento. Tente novamente."})}})(i.id),children:"Excluir"})})]},i.id)))})]})})})]})]})})};export{b as default};
