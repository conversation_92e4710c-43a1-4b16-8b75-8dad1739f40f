import{j as e}from"./radix-core-6kBL75b5.js";import{r}from"./critical-DVX9Inzy.js";import{a as s,u as a}from"./query-vendor-B-7l6Nb3.js";import{d as i,D as t,e as n,f as o,g as l,an as c,a5 as d,T as h,ad as m,ae as u,af as j,ag as x,ai as p,B as v,s as g,L as f}from"./index-CR7o3nEo.js";import{T as y,a as w,b as C,c as b,d as S,e as N}from"./table-YFGStdcU.js";import{P as T}from"./plus-C7-Pt48y.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";function _({open:a,onOpenChange:f}){const[y,w]=r.useState(""),[C,b]=r.useState(""),[S,N]=r.useState(""),[T,_]=r.useState(""),[q,F]=r.useState(""),[k,D]=r.useState(null),[P,G]=r.useState(!1),{toast:M}=i(),U=s();return e.jsx(t,{open:a,onOpenChange:f,children:e.jsxs(n,{className:"sm:max-w-[500px]",children:[e.jsx(o,{children:e.jsx(l,{children:"Nova Curva de Crescimento"})}),e.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),k){G(!0);try{const e=k.name.split(".").pop(),r=`${crypto.randomUUID()}.${e}`,{error:s}=await g.storage.from("growth-curves").upload(r,k);if(s)throw s;const{data:{publicUrl:a}}=g.storage.from("growth-curves").getPublicUrl(r),{error:i}=await g.from("pedbook_growth_curves").insert({title:y,description:C,gender:S,gestational_age:T,growth_type:q,image_url:a});if(i)throw i;M({title:"Sucesso",description:"Curva de crescimento criada com sucesso!"}),U.invalidateQueries({queryKey:["growth-curves"]}),f(!1),w(""),b(""),N(""),_(""),F(""),D(null)}catch(r){M({variant:"destructive",title:"Erro ao criar curva de crescimento",description:r.message})}finally{G(!1)}}else M({variant:"destructive",title:"Erro",description:"Por favor, selecione uma imagem para a curva de crescimento."})},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(c,{htmlFor:"title",children:"Título"}),e.jsx(d,{id:"title",value:y,onChange:e=>w(e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"description",children:"Descrição"}),e.jsx(h,{id:"description",value:C,onChange:e=>b(e.target.value),className:"min-h-[100px]"})]}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"gender",children:"Gênero"}),e.jsxs(m,{value:S,onValueChange:N,required:!0,children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Selecione o gênero"})}),e.jsxs(x,{children:[e.jsx(p,{value:"male",children:"Menino"}),e.jsx(p,{value:"female",children:"Menina"})]})]})]}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"gestationalAge",children:"Tempo Gestacional"}),e.jsxs(m,{value:T,onValueChange:_,required:!0,children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Selecione o tempo gestacional"})}),e.jsxs(x,{children:[e.jsx(p,{value:"term",children:"A Termo"}),e.jsx(p,{value:"preterm",children:"Pré-termo"})]})]})]}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"growthType",children:"Tipo de Crescimento"}),e.jsxs(m,{value:q,onValueChange:F,required:!0,children:[e.jsx(u,{children:e.jsx(j,{placeholder:"Selecione o tipo de crescimento"})}),e.jsxs(x,{children:[e.jsx(p,{value:"healthy",children:"Saudável"}),e.jsx(p,{value:"disorder",children:"Transtorno"})]})]})]}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"image",children:"Imagem"}),e.jsx(d,{id:"image",type:"file",accept:"image/*",onChange:e=>D(e.target.files?.[0]||null),required:!0})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(v,{type:"button",variant:"outline",onClick:()=>f(!1),disabled:P,children:"Cancelar"}),e.jsx(v,{type:"submit",disabled:P,children:P?"Salvando...":"Salvar"})]})]})]})})}function q({curves:r}){return e.jsx("div",{className:"border rounded-lg",children:e.jsxs(y,{children:[e.jsx(w,{children:e.jsxs(C,{children:[e.jsx(b,{children:"Título"}),e.jsx(b,{children:"Gênero"}),e.jsx(b,{children:"Tempo Gestacional"}),e.jsx(b,{children:"Tipo"}),e.jsx(b,{children:"Descrição"})]})}),e.jsx(S,{children:r.map((r=>{return e.jsxs(C,{children:[e.jsx(N,{children:r.title}),e.jsx(N,{children:(i=r.gender,"male"===i?"Menino":"Menina")}),e.jsx(N,{children:(a=r.gestational_age,"term"===a?"A Termo":"Pré-termo")}),e.jsx(N,{children:(s=r.growth_type,"healthy"===s?"Saudável":"Transtorno")}),e.jsx(N,{children:r.description})]},r.id);var s,a,i}))})]})})}function F(){const[s,i]=r.useState(!1),{data:t,isLoading:n}=a({queryKey:["growth-curves"],queryFn:async()=>{const{data:e,error:r}=await g.from("pedbook_growth_curves").select("*").order("created_at",{ascending:!1});if(r)throw r;return e}});return e.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Curvas de Crescimento"}),e.jsxs(v,{onClick:()=>i(!0),children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Nova Curva"]})]}),n?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx(f,{className:"h-8 w-8 animate-spin text-primary"})}):e.jsx(q,{curves:t||[]}),e.jsx(_,{open:s,onOpenChange:i})]})}export{F as default};
