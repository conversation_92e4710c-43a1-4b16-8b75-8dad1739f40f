var e=Object.defineProperty,t=(t,r,n)=>((t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n)(t,"symbol"!=typeof r?r+"":r,n);import{j as r}from"./radix-core-6kBL75b5.js";import{r as n,c as i,d as a,b as o}from"./critical-DVX9Inzy.js";import{L as s}from"./router-BAzpOxbo.js";import{c,an as l,a5 as u,aT as f,aU as d,k as p,aJ as h,R as m,ak as y,aL as g,aH as v,az as b,B as x,ad as w,ae as j,af as O,ag as k,ai as S,a_ as P,aq as A,ar as N,as as E,at as M,s as _,d as C,Y as T,o as D,p as I,v as B,a$ as $,w as R,P as L,l as z,ay as F,ab as U,n as q,j as W,a7 as V,au as G,aj as Y,aa as H}from"./index-CFFY2EZF.js";import X from"./Footer-Cy0ETRb8.js";import{C as K}from"./calendar-BzRFPDjE.js";import{U as Z}from"./user-DTxhJiBv.js";import{S as J}from"./scale-BQZyeNkz.js";import{R as Q}from"./ruler-BPc4Sqml.js";import{S as ee}from"./switch-DfZoFaNV.js";import{C as te}from"./chart-line-CYYwFsR2.js";import{u as re}from"./query-vendor-B-7l6Nb3.js";import{A as ne,a as ie,b as ae}from"./alert-VzAM23dx.js";import{T as oe}from"./trending-up-QfN0HaR0.js";import{F as se}from"./filter-DwlRWDIX.js";import{C as ce}from"./circle-check-BcCajg2-.js";import{A as le}from"./FeedbackTrigger-D3WjakYT.js";import{c as ue,S as fe}from"./supplementationCalculator-Cuf9VslC.js";import{E as de}from"./pdf-vendor-C6iMwFa1.js";import{D as pe}from"./download-CpeVlzju.js";import{C as he}from"./clock-AzOEB54f.js";import{T as me}from"./target-BR2vVj-t.js";import{S as ye}from"./syringe-DAzry6Ak.js";import{P as ge}from"./pill-bottle-Cj9NYVo1.js";import{u as ve}from"./carousel-vendor-BSJaAqXc.js";import{C as be}from"./chart-column-CwUImhYI.js";import{C as xe,a as we}from"./childcareSEOData-Dlzjjv9N.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-CMzOelh-.js";import"./rocket-CXfOzAv0.js";import"./zap-DiDsx3f0.js";import"./book-open-DfMTUXW0.js";import"./star-D6ndiBmz.js";import"./circle-help-8_s38d5r.js";import"./accordion-Cl5OLBW2.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=c("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]),Oe=c("Milestone",[["path",{d:"M12 13v8",key:"1l5pq0"}],["path",{d:"M12 3v3",key:"1n5kay"}],["path",{d:"M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z",key:"1btarq"}]]),ke=c("Weight",[["circle",{cx:"12",cy:"5",r:"3",key:"rqqgnr"}],["path",{d:"M6.5 8a2 2 0 0 0-1.905 1.46L2.1 18.5A2 2 0 0 0 4 21h16a2 2 0 0 0 1.925-2.54L19.4 9.5A2 2 0 0 0 17.48 8Z",key:"56o5sh"}]]);
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function Se({defaultAgeMonths:e=0,defaultAgeDays:t=0,defaultGestationalAge:i=0}){const[a,o]=n.useState(e),[s,c]=n.useState(t),[h,m]=n.useState(Math.floor(i||0)),[y,g]=n.useState(Math.round((i||0)%1*7)),v=h+y/7,b=(e,t)=>e+t/30.44;return n.useEffect((()=>{const e=b(a,s),t=document.querySelector('input[name="age"]');t&&(t.value=e.toString())}),[a,s]),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(K,{className:"h-4 w-4 text-blue-600"}),"Idade"]}),r.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"ageMonths",type:"number",min:0,max:240,value:a,onChange:e=>o(Number(e.target.value)||0),className:"bg-white dark:bg-slate-800 h-10 text-sm pr-8 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500",placeholder:"0"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium",children:"m"})]}),r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"ageDays",type:"number",min:0,max:30,value:s,onChange:e=>c(Number(e.target.value)||0),className:"bg-white dark:bg-slate-800 h-10 text-sm pr-8 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500",placeholder:"0"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium",children:"d"})]})]}),r.jsx("input",{type:"hidden",name:"age",value:b(a,s)})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(Z,{className:"h-4 w-4 text-blue-600"}),"Gênero"]}),r.jsxs(f,{name:"gender",className:"flex gap-3 pt-1",defaultValue:"male",children:[r.jsxs("div",{className:"flex items-center space-x-2 bg-white dark:bg-slate-800 px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-colors",children:[r.jsx(d,{value:"male",id:"male",className:"h-4 w-4"}),r.jsx(l,{htmlFor:"male",className:"text-sm font-medium cursor-pointer",children:"♂ Masculino"})]}),r.jsxs("div",{className:"flex items-center space-x-2 bg-white dark:bg-slate-800 px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-colors",children:[r.jsx(d,{value:"female",id:"female",className:"h-4 w-4"}),r.jsx(l,{htmlFor:"female",className:"text-sm font-medium cursor-pointer",children:"♀ Feminino"})]})]})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(p,{className:"h-4 w-4 text-blue-600"}),"Idade Gestacional"]}),r.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"gestationalWeeks",type:"number",min:22,max:42,value:h||"",onChange:e=>m(Number(e.target.value)||0),className:"bg-white dark:bg-slate-800 h-10 text-sm pr-12 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500",placeholder:"0"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium",children:"sem"})]}),r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"gestationalDays",type:"number",min:0,max:6,value:y||"",onChange:e=>g(Number(e.target.value)||0),className:"bg-white dark:bg-slate-800 h-10 text-sm pr-8 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500",placeholder:"0"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium",children:"d"})]})]}),v>0&&r.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400 font-medium bg-gray-50 dark:bg-gray-800 px-2 py-1 rounded",children:[h>0&&y>0?`${h} semanas e ${y} dias`:h>0&&0===y?`${h} semanas`:0===h&&y>0?`${y} dias`:`${h} semanas`," • ",(x=v,x<28?"🔴 Extremamente prematuro":x>=28&&x<32?"🟠 Muito prematuro":x>=32&&x<34?"🟡 Prematuro moderado":x>=34&&x<37?"🟡 Prematuro tardio":x>=37&&x<39?"🟢 Pré-termo limítrofe":x>=39&&x<41?"🟢 Termo completo":x>=41&&x<42?"🟢 Termo tardio":x>=42?"🔵 Pós-termo":"A termo")]}),r.jsx("input",{type:"hidden",name:"gestationalAge",value:v})]}),r.jsx("input",{type:"hidden",name:"maturity",value:v>=37?"Term":"Pre-term"})]});var x}function Pe({defaultValues:e}){return r.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"birthWeight",className:"flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(p,{className:"h-3 w-3 text-blue-600"}),"Peso Nasc."]}),r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"birthWeight",name:"birthWeight",type:"number",step:"1",min:500,max:6e3,defaultValue:e?.birthWeight,required:!0,placeholder:"3200",className:"bg-white dark:bg-slate-800 h-9 text-sm pr-8"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500",children:"g"})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"weight",className:"flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(J,{className:"h-3 w-3 text-green-600"}),"Peso Atual"]}),r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"weight",name:"weight",type:"number",step:"1",min:500,max:1e5,defaultValue:e?.weight?1e3*e.weight:void 0,required:!0,placeholder:"5200",className:"bg-white dark:bg-slate-800 h-9 text-sm pr-8"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500",children:"g"})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"height",className:"flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(Q,{className:"h-3 w-3 text-purple-600"}),"Altura"]}),r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"height",name:"height",type:"number",step:"0.1",min:20,max:250,defaultValue:e?.height,required:!0,placeholder:"65.5",className:"bg-white dark:bg-slate-800 h-9 text-sm pr-10"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500",children:"cm"})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"headCircumference",className:"flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300",children:[r.jsx(h,{className:"h-3 w-3 text-orange-600"}),"PC"]}),r.jsxs("div",{className:"relative",children:[r.jsx(u,{id:"headCircumference",name:"headCircumference",type:"number",step:"0.1",min:20,max:80,defaultValue:e?.headCircumference,required:!0,placeholder:"42.0",className:"bg-white dark:bg-slate-800 h-9 text-sm pr-10"}),r.jsx("span",{className:"absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500",children:"cm"})]})]})]})}function Ae({exclusiveBreastfeeding:e,riskFactors:t,onBreastfeedingChange:i,onRiskFactorsChange:a}){const[o,s]=n.useState(!1);return r.jsx(m,{className:"p-4 bg-white/80 dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700",children:r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs(l,{htmlFor:"exclusiveBreastfeeding",className:"flex items-center gap-2 cursor-pointer",children:[r.jsx(p,{className:"h-4 w-4 text-purple-500 dark:text-purple-400"}),r.jsx("span",{className:"text-gray-800 dark:text-gray-200",children:"Aleitamento Materno Exclusivo"})]}),r.jsx(ee,{id:"exclusiveBreastfeeding",checked:e,onCheckedChange:i,className:"data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"})]}),r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>s(!o),children:[r.jsxs(l,{className:"flex items-center gap-2 cursor-pointer",children:[r.jsx(y,{className:"h-4 w-4 text-yellow-500 dark:text-yellow-400"}),r.jsxs("span",{className:"text-gray-800 dark:text-gray-200",children:["Fatores de Risco ",t.length>0&&`(${t.length} selecionados)`]})]}),o?r.jsx(g,{className:"h-4 w-4 text-gray-500"}):r.jsx(v,{className:"h-4 w-4 text-gray-500"})]}),o&&r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg",children:[{id:"prematurity",label:"Prematuridade",description:"Nascimento antes de 37 semanas"},{id:"low_birth_weight",label:"Baixo peso ao nascer",description:"Peso ao nascer < 2500g"},{id:"poor_iron_diet",label:"Alimentação pobre em ferro",description:"Introdução alimentar inadequada"},{id:"exclusive_breastfeeding_gt_6m_without_supplement",label:"AME prolongado sem suplementação",description:"Além de 6 meses sem ferro"},{id:"multiple_pregnancy",label:"Gestação múltipla",description:"Gêmeos, trigêmeos, etc."},{id:"maternal_anemia",label:"Anemia materna",description:"Durante gestação ou lactação"},{id:"frequent_infections",label:"Infecções frequentes",description:"Processos infecciosos recorrentes"}].map((e=>r.jsxs("div",{className:"flex items-start space-x-2",children:[r.jsx(b,{id:e.id,checked:t.includes(e.id),onCheckedChange:r=>((e,r)=>{a(r?[...t,e]:t.filter((t=>t!==e)))})(e.id,r),className:"mt-1"}),r.jsxs("div",{className:"grid gap-1.5 leading-none",children:[r.jsx(l,{htmlFor:e.id,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer",children:e.label}),e.description&&r.jsx("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.id)))})]})]})})}function Ne({onSubmit:e,defaultValues:t}){const[i,a]=n.useState(t?.exclusiveBreastfeeding||!1),[o,s]=n.useState(t?.riskFactors||[]),c=t?.age?(e=>{const t=Math.floor(e);return{months:t,days:Math.round(30.44*(e-t))}})(t.age):{months:0,days:0};return r.jsxs("form",{onSubmit:t=>{t.preventDefault();const r=new FormData(t.currentTarget),n={age:Number(r.get("age")),weight:Number(r.get("weight"))/1e3,birthWeight:Number(r.get("birthWeight")),height:Number(r.get("height")),gender:r.get("gender"),headCircumference:Number(r.get("headCircumference")),exclusiveBreastfeeding:i,hasRiskFactors:o.length>0,riskFactors:o,maturity:r.get("maturity"),gestationalAge:Number(r.get("gestationalAge"))};e(n)},className:"space-y-4",children:[r.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/30 rounded-xl p-5 border border-blue-200 dark:border-blue-800 shadow-sm",children:[r.jsxs("h3",{className:"text-base font-semibold text-blue-800 dark:text-blue-300 mb-4 flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full shadow-sm"}),"Dados Básicos"]}),r.jsx(Se,{defaultAgeMonths:c.months,defaultAgeDays:c.days,defaultGestationalAge:t?.gestationalAge})]}),r.jsxs("div",{className:"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/30 rounded-xl p-5 border border-green-200 dark:border-green-800 shadow-sm",children:[r.jsxs("h3",{className:"text-base font-semibold text-green-800 dark:text-green-300 mb-4 flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full shadow-sm"}),"Medidas Antropométricas"]}),r.jsx(Pe,{defaultValues:{weight:t?.weight,birthWeight:t?.birthWeight,height:t?.height,headCircumference:t?.headCircumference}})]}),r.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-900/20 dark:to-violet-900/30 rounded-xl p-5 border border-purple-200 dark:border-purple-800 shadow-sm",children:[r.jsxs("h3",{className:"text-base font-semibold text-purple-800 dark:text-purple-300 mb-4 flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-purple-500 rounded-full shadow-sm"}),"Informações Clínicas"]}),r.jsx(Ae,{exclusiveBreastfeeding:i,riskFactors:o,onBreastfeedingChange:a,onRiskFactorsChange:s})]}),r.jsx("div",{className:"pt-2",children:r.jsx(x,{type:"submit",className:"w-full h-14 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] border-0",children:r.jsxs("div",{className:"flex items-center justify-center gap-3",children:[r.jsx("div",{className:"w-6 h-6 rounded-full bg-white/20 flex items-center justify-center",children:r.jsx("div",{className:"w-3 h-3 bg-white rounded-full animate-pulse"})}),r.jsx("span",{className:"text-lg",children:"Analisar Paciente"})]})})})]})}function Ee({data:e,onChange:t}){return r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"age",className:"flex items-center gap-2",children:[r.jsx(p,{className:"h-4 w-4"}),"Idade (meses) *"]}),r.jsx(u,{id:"age",type:"number",min:0,max:240,value:e.age,onChange:r=>t({...e,age:Number(r.target.value)}),required:!0})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(l,{htmlFor:"gender",className:"flex items-center gap-2",children:"Gênero *"}),r.jsxs(w,{value:e.gender,onValueChange:r=>t({...e,gender:r}),children:[r.jsx(j,{children:r.jsx(O,{placeholder:"Selecione o gênero"})}),r.jsxs(k,{children:[r.jsx(S,{value:"male",children:"Masculino"}),r.jsx(S,{value:"female",children:"Feminino"})]})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"maturity",className:"flex items-center gap-2",children:[r.jsx(p,{className:"h-4 w-4"}),"Maturidade *"]}),r.jsxs(w,{value:e.maturity,onValueChange:r=>t({...e,maturity:r}),children:[r.jsx(j,{children:r.jsx(O,{placeholder:"Selecione a maturidade"})}),r.jsxs(k,{children:[r.jsx(S,{value:"Term",children:"A termo"}),r.jsx(S,{value:"Pre-term",children:"Pré-termo"})]})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"birthWeight",className:"flex items-center gap-2",children:[r.jsx(J,{className:"h-4 w-4"}),"Peso ao Nascer (g)"]}),r.jsx(u,{id:"birthWeight",type:"number",step:"1",min:0,value:e.birthWeight||"",onChange:r=>t({...e,birthWeight:Number(r.target.value)})})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"weight",className:"flex items-center gap-2",children:[r.jsx(J,{className:"h-4 w-4"}),"Peso Atual (kg)"]}),r.jsx(u,{id:"weight",type:"number",step:"0.1",min:0,value:e.weight||"",onChange:r=>t({...e,weight:Number(r.target.value)})})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"height",className:"flex items-center gap-2",children:[r.jsx(Q,{className:"h-4 w-4"}),"Altura (cm)"]}),r.jsx(u,{id:"height",type:"number",step:"0.1",min:0,value:e.height||"",onChange:r=>t({...e,height:Number(r.target.value)})})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(l,{htmlFor:"headCircumference",className:"flex items-center gap-2",children:[r.jsx(te,{className:"h-4 w-4"}),"Perímetro Cefálico (cm)"]}),r.jsx(u,{id:"headCircumference",type:"number",step:"0.1",min:0,value:e.headCircumference||"",onChange:r=>t({...e,headCircumference:Number(r.target.value)})})]})]})}const Me={weight:"Peso (kg)",height:"Altura (cm)",bmi:"IMC (kg/m²)","head-circumference":"Perímetro Cefálico (cm)"},_e={p3:"#ff7300",p15:"#ffd95d",p50:"#387908",p85:"#ffd95d",p97:"#ff7300",patient:"#8884d8"},Ce={p3:"P3",p15:"P15",p50:"P50",p85:"P85",p97:"P97",patient:"Paciente"};function Te({payload:e,measurementType:t}){return e?r.jsxs("div",{className:"flex flex-col items-center gap-2 text-xs",children:[r.jsx("div",{className:"flex flex-wrap justify-center items-center gap-4",children:e.filter((e=>"age"!==e.value&&e.value!==t)).map(((e,t)=>r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsx("div",{className:"w-4 h-0.5",style:{backgroundColor:e.color,borderStyle:"p3"===e.value||"p15"===e.value||"p85"===e.value||"p97"===e.value?"dashed":"solid"}}),r.jsx("span",{children:Ce[e.value]})]},t)))}),r.jsxs("div",{className:"flex flex-col items-center text-gray-600",children:[r.jsx("span",{children:"Eixo X: Idade (meses/anos)"}),r.jsxs("span",{children:["Eixo Y: ",Me[t]]})]})]}):null}const De=e=>{if(e<1){const t=Math.round(30.44*e);return`${t} ${1===t?"dia":"dias"}`}const t=e,r=Math.floor(t/12),n=t-12*r,i=Math.floor(n),a=n-i,o=Math.round(30.44*a);return 0===r?0===o?`${i} ${1===i?"mês":"meses"}`:`${i} ${1===i?"mês":"meses"} e ${o} ${1===o?"dia":"dias"}`:0===i&&0===o?`${r} ${1===r?"ano":"anos"}`:0===o?`${r} ${1===r?"ano":"anos"} e ${i} ${1===i?"mês":"meses"}`:0===i?`${r} ${1===r?"ano":"anos"} e ${o} ${1===o?"dia":"dias"}`:`${r} ${1===r?"ano":"anos"}, ${i} ${1===i?"mês":"meses"} e ${o} ${1===o?"dia":"dias"}`},Ie=(e,t=12)=>{let r=Math.max(0,e-t),n=Math.min(60,e+t);if(n-r<18){const e=(r+n)/2,t=9;r=Math.max(0,e-t),n=Math.min(60,e+t),n-r<18&&(0===r?n=Math.min(60,18):60===n&&(r=Math.max(0,42)))}r=Math.floor(r),n=Math.ceil(n);const i=e=>{if(0===e)return"0m";if(e<12)return`${e}m`;const t=Math.floor(e/12),r=e%12;return 0===r?`${t}a`:`${t}a${r}m`};return{minAge:r,maxAge:n,totalMonths:n-r,description:`${i(r)} - ${i(n)}`}};var Be=Array.isArray,$e="object"==typeof i&&i&&i.Object===Object&&i,Re=$e,Le="object"==typeof self&&self&&self.Object===Object&&self,ze=Re||Le||Function("return this")(),Fe=ze.Symbol,Ue=Fe,qe=Object.prototype,We=qe.hasOwnProperty,Ve=qe.toString,Ge=Ue?Ue.toStringTag:void 0,Ye=Object.prototype.toString,He=function(e){var t=We.call(e,Ge),r=e[Ge];try{e[Ge]=void 0;var n=!0}catch(a){}var i=Ve.call(e);return n&&(t?e[Ge]=r:delete e[Ge]),i},Xe=Fe?Fe.toStringTag:void 0,Ke=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Xe&&Xe in Object(e)?He(e):function(e){return Ye.call(e)}(e)},Ze=function(e){return null!=e&&"object"==typeof e},Je=Ke,Qe=Ze,et=function(e){return"symbol"==typeof e||Qe(e)&&"[object Symbol]"==Je(e)},tt=Be,rt=et,nt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,it=/^\w*$/,at=function(e,t){if(tt(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!rt(e))||it.test(e)||!nt.test(e)||null!=t&&e in Object(t)},ot=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const st=a(ot);var ct=Ke,lt=ot,ut=function(e){if(!lt(e))return!1;var t=ct(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};const ft=a(ut);var dt,pt=ze["__core-js_shared__"],ht=(dt=/[^.]+$/.exec(pt&&pt.keys&&pt.keys.IE_PROTO||""))?"Symbol(src)_1."+dt:"",mt=Function.prototype.toString,yt=function(e){if(null!=e){try{return mt.call(e)}catch(t){}try{return e+""}catch(t){}}return""},gt=ut,vt=function(e){return!!ht&&ht in e},bt=ot,xt=yt,wt=/^\[object .+?Constructor\]$/,jt=Function.prototype,Ot=Object.prototype,kt=jt.toString,St=Ot.hasOwnProperty,Pt=RegExp("^"+kt.call(St).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),At=function(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return function(e){return!(!bt(e)||vt(e))&&(gt(e)?Pt:wt).test(xt(e))}(r)?r:void 0},Nt=At(Object,"create"),Et=Nt,Mt=Nt,_t=Object.prototype.hasOwnProperty,Ct=Nt,Tt=Object.prototype.hasOwnProperty,Dt=Nt,It=function(){this.__data__=Et?Et(null):{},this.size=0},Bt=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},$t=function(e){var t=this.__data__;if(Mt){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return _t.call(t,e)?t[e]:void 0};function Rt(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Rt.prototype.clear=It,Rt.prototype.delete=Bt,Rt.prototype.get=$t,Rt.prototype.has=function(e){var t=this.__data__;return Ct?void 0!==t[e]:Tt.call(t,e)},Rt.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Dt&&void 0===t?"__lodash_hash_undefined__":t,this};var Lt=Rt,zt=function(e,t){return e===t||e!=e&&t!=t},Ft=zt,Ut=function(e,t){for(var r=e.length;r--;)if(Ft(e[r][0],t))return r;return-1},qt=Ut,Wt=Array.prototype.splice,Vt=Ut,Gt=Ut,Yt=Ut,Ht=function(){this.__data__=[],this.size=0};function Xt(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Xt.prototype.clear=Ht,Xt.prototype.delete=function(e){var t=this.__data__,r=qt(t,e);return!(r<0||(r==t.length-1?t.pop():Wt.call(t,r,1),--this.size,0))},Xt.prototype.get=function(e){var t=this.__data__,r=Vt(t,e);return r<0?void 0:t[r][1]},Xt.prototype.has=function(e){return Gt(this.__data__,e)>-1},Xt.prototype.set=function(e,t){var r=this.__data__,n=Yt(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};var Kt=Xt,Zt=At(ze,"Map"),Jt=Lt,Qt=Kt,er=Zt,tr=function(e,t){var r,n,i=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?i["string"==typeof t?"string":"hash"]:i.map},rr=tr,nr=tr,ir=tr,ar=tr;function or(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}or.prototype.clear=function(){this.size=0,this.__data__={hash:new Jt,map:new(er||Qt),string:new Jt}},or.prototype.delete=function(e){var t=rr(this,e).delete(e);return this.size-=t?1:0,t},or.prototype.get=function(e){return nr(this,e).get(e)},or.prototype.has=function(e){return ir(this,e).has(e)},or.prototype.set=function(e,t){var r=ar(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};var sr=or,cr=sr;function lr(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(lr.Cache||cr),r}lr.Cache=cr;var ur=lr;const fr=a(ur);var dr,pr,hr=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,mr=/\\(\\)?/g,yr=(dr=ur((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(hr,(function(e,r,n,i){t.push(n?i.replace(mr,"$1"):r||e)})),t}),(function(e){return 500===pr.size&&pr.clear(),e})),pr=dr.cache,dr),gr=function(e,t){for(var r=-1,n=null==e?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i},vr=gr,br=Be,xr=et,wr=Fe?Fe.prototype:void 0,jr=wr?wr.toString:void 0,Or=function e(t){if("string"==typeof t)return t;if(br(t))return vr(t,e)+"";if(xr(t))return jr?jr.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r},kr=function(e){return null==e?"":Or(e)},Sr=Be,Pr=at,Ar=yr,Nr=kr,Er=function(e,t){return Sr(e)?e:Pr(e,t)?[e]:Ar(Nr(e))},Mr=et,_r=function(e){if("string"==typeof e||Mr(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t},Cr=Er,Tr=_r,Dr=function(e,t){for(var r=0,n=(t=Cr(t,e)).length;null!=e&&r<n;)e=e[Tr(t[r++])];return r&&r==n?e:void 0},Ir=Dr,Br=function(e,t,r){var n=null==e?void 0:Ir(e,t);return void 0===n?r:n};const $r=a(Br),Rr=a((function(e){return null==e}));var Lr=Ke,zr=Be,Fr=Ze;const Ur=a((function(e){return"string"==typeof e||!zr(e)&&Fr(e)&&"[object String]"==Lr(e)}));var qr,Wr={exports:{}},Vr={},Gr=Symbol.for("react.element"),Yr=Symbol.for("react.portal"),Hr=Symbol.for("react.fragment"),Xr=Symbol.for("react.strict_mode"),Kr=Symbol.for("react.profiler"),Zr=Symbol.for("react.provider"),Jr=Symbol.for("react.context"),Qr=Symbol.for("react.server_context"),en=Symbol.for("react.forward_ref"),tn=Symbol.for("react.suspense"),rn=Symbol.for("react.suspense_list"),nn=Symbol.for("react.memo"),an=Symbol.for("react.lazy"),on=Symbol.for("react.offscreen");function sn(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case Gr:switch(e=e.type){case Hr:case Kr:case Xr:case tn:case rn:return e;default:switch(e=e&&e.$$typeof){case Qr:case Jr:case en:case an:case nn:case Zr:return e;default:return t}}case Yr:return t}}}qr=Symbol.for("react.module.reference"),Vr.ContextConsumer=Jr,Vr.ContextProvider=Zr,Vr.Element=Gr,Vr.ForwardRef=en,Vr.Fragment=Hr,Vr.Lazy=an,Vr.Memo=nn,Vr.Portal=Yr,Vr.Profiler=Kr,Vr.StrictMode=Xr,Vr.Suspense=tn,Vr.SuspenseList=rn,Vr.isAsyncMode=function(){return!1},Vr.isConcurrentMode=function(){return!1},Vr.isContextConsumer=function(e){return sn(e)===Jr},Vr.isContextProvider=function(e){return sn(e)===Zr},Vr.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===Gr},Vr.isForwardRef=function(e){return sn(e)===en},Vr.isFragment=function(e){return sn(e)===Hr},Vr.isLazy=function(e){return sn(e)===an},Vr.isMemo=function(e){return sn(e)===nn},Vr.isPortal=function(e){return sn(e)===Yr},Vr.isProfiler=function(e){return sn(e)===Kr},Vr.isStrictMode=function(e){return sn(e)===Xr},Vr.isSuspense=function(e){return sn(e)===tn},Vr.isSuspenseList=function(e){return sn(e)===rn},Vr.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===Hr||e===Kr||e===Xr||e===tn||e===rn||e===on||"object"==typeof e&&null!==e&&(e.$$typeof===an||e.$$typeof===nn||e.$$typeof===Zr||e.$$typeof===Jr||e.$$typeof===en||e.$$typeof===qr||void 0!==e.getModuleId)},Vr.typeOf=sn,Wr.exports=Vr;var cn=Wr.exports,ln=Ke,un=Ze,fn=function(e){return"number"==typeof e||un(e)&&"[object Number]"==ln(e)};const dn=a(fn);var pn=fn;const hn=a((function(e){return pn(e)&&e!=+e}));var mn=function(e){return 0===e?0:e>0?1:-1},yn=function(e){return Ur(e)&&e.indexOf("%")===e.length-1},gn=function(e){return dn(e)&&!hn(e)},vn=function(e){return gn(e)||Ur(e)},bn=0,xn=function(e){var t=++bn;return"".concat(e||"").concat(t)},wn=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!gn(e)&&!Ur(e))return n;if(yn(e)){var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return hn(r)&&(r=n),i&&r>t&&(r=t),r},jn=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},On=function(e,t){return gn(e)&&gn(t)?function(r){return e+r*(t-e)}:function(){return t}};function kn(e,t,r){return e&&e.length?e.find((function(e){return e&&("function"==typeof t?t(e):$r(e,t))===r})):null}var Sn=function(e,t){return gn(e)&&gn(t)?e-t:Ur(e)&&Ur(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))};function Pn(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function An(e){return(An="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Nn=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],En=["points","pathLength"],Mn={svg:["viewBox","children"],polygon:En,polyline:En},_n=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Cn=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if(n.isValidElement(e)&&(r=e.props),!st(r))return null;var i={};return Object.keys(r).forEach((function(e){_n.includes(e)&&(i[e]=t||function(t){return r[e](r,t)})})),i},Tn=function(e,t,r){if(!st(e)||"object"!==An(e))return null;var n=null;return Object.keys(e).forEach((function(i){var a=e[i];_n.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=function(e,t,r){return function(n){return e(t,r,n),null}}(a,t,r))})),n},Dn=["children"],In=["children"];function Bn(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function $n(e){return($n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Rn={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Ln=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},zn=null,Fn=null,Un=function e(t){if(t===zn&&Array.isArray(Fn))return Fn;var r=[];return n.Children.forEach(t,(function(t){Rr(t)||(cn.isFragment(t)?r=r.concat(e(t.props.children)):r.push(t))})),Fn=r,zn=t,r};function qn(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map((function(e){return Ln(e)})):[Ln(t)],Un(e).forEach((function(e){var t=$r(e,"type.displayName")||$r(e,"type.name");-1!==n.indexOf(t)&&r.push(e)})),r}function Wn(e,t){var r=qn(e,t);return r&&r[0]}var Vn=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!(!gn(r)||r<=0||!gn(n)||n<=0)},Gn=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Yn=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var i=e;if(n.isValidElement(e)&&(i=e.props),!st(i))return null;var a={};return Object.keys(i).forEach((function(e){var n;(function(e,t,r,n){var i,a=null!==(i=null==Mn?void 0:Mn[n])&&void 0!==i?i:[];return t.startsWith("data-")||!ft(e)&&(n&&a.includes(t)||Nn.includes(t))||r&&_n.includes(t)})(null===(n=i)||void 0===n?void 0:n[e],e,t,r)&&(a[e]=i[e])})),a},Hn=function e(t,r){if(t===r)return!0;var i=n.Children.count(t);if(i!==n.Children.count(r))return!1;if(0===i)return!0;if(1===i)return Xn(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var a=0;a<i;a++){var o=t[a],s=r[a];if(Array.isArray(o)||Array.isArray(s)){if(!e(o,s))return!1}else if(!Xn(o,s))return!1}return!0},Xn=function(e,t){if(Rr(e)&&Rr(t))return!0;if(!Rr(e)&&!Rr(t)){var r=e.props||{},n=r.children,i=Bn(r,Dn),a=t.props||{},o=a.children,s=Bn(a,In);return n&&o?Pn(i,s)&&Hn(n,o):!n&&!o&&Pn(i,s)}return!1},Kn=function(e,t){var r=[],n={};return Un(e).forEach((function(e,i){if(function(e){return e&&e.type&&Ur(e.type)&&Gn.indexOf(e.type)>=0}(e))r.push(e);else if(e){var a=Ln(e.type),o=t[a]||{},s=o.handler,c=o.once;if(s&&(!c||!n[a])){var l=s(e,a,i);r.push(l),n[a]=!0}}})),r},Zn=["children","width","height","viewBox","className","style","title","desc"];function Jn(){return Jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jn.apply(this,arguments)}function Qn(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,s=e.style,c=e.title,l=e.desc,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Zn),f=i||{width:r,height:n,x:0,y:0},d=P("recharts-surface",a);return o.createElement("svg",Jn({},Yn(u,!0,"svg"),{className:d,width:r,height:n,style:s,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),o.createElement("title",null,c),o.createElement("desc",null,l),t)}var ei=["children","className"];function ti(){return ti=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ti.apply(this,arguments)}var ri=o.forwardRef((function(e,t){var r=e.children,n=e.className,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ei),a=P("recharts-layer",n);return o.createElement("g",ti({className:a},Yn(i,!0),{ref:t}),r)})),ni=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},ii=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]"),ai=function(e){return ii.test(e)},oi="\\ud800-\\udfff",si="["+oi+"]",ci="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",li="\\ud83c[\\udffb-\\udfff]",ui="[^"+oi+"]",fi="(?:\\ud83c[\\udde6-\\uddff]){2}",di="[\\ud800-\\udbff][\\udc00-\\udfff]",pi="(?:"+ci+"|"+li+")?",hi="[\\ufe0e\\ufe0f]?",mi=hi+pi+"(?:\\u200d(?:"+[ui,fi,di].join("|")+")"+hi+pi+")*",yi="(?:"+[ui+ci+"?",ci,fi,di,si].join("|")+")",gi=RegExp(li+"(?="+li+")|"+yi+mi,"g"),vi=function(e){return e.split("")},bi=ai,xi=function(e,t,r){var n=e.length;return r=void 0===r?n:r,!t&&r>=n?e:function(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}(e,t,r)},wi=ai,ji=function(e){return bi(e)?function(e){return e.match(gi)||[]}(e):vi(e)},Oi=kr;const ki=a((function(e){e=Oi(e);var t=wi(e)?ji(e):void 0,r=t?t[0]:e.charAt(0),n=t?xi(t,1).join(""):e.slice(1);return r.toUpperCase()+n}));function Si(e){return function(){return e}}const Pi=Math.cos,Ai=Math.sin,Ni=Math.sqrt,Ei=Math.PI,Mi=2*Ei,_i=Math.PI,Ci=2*_i,Ti=1e-6,Di=Ci-Ti;function Ii(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class Bi{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?Ii:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Ii;const r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e=+e,t=+t,r=+r,n=+n,(i=+i)<0)throw new Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,s=r-e,c=n-t,l=a-e,u=o-t,f=l*l+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>Ti)if(Math.abs(u*s-c*l)>Ti&&i){let d=r-a,p=n-o,h=s*s+c*c,m=d*d+p*p,y=Math.sqrt(h),g=Math.sqrt(f),v=i*Math.tan((_i-Math.acos((h+f-m)/(2*y*g)))/2),b=v/g,x=v/y;Math.abs(b-1)>Ti&&this._append`L${e+b*l},${t+b*u}`,this._append`A${i},${i},0,0,${+(u*d>l*p)},${this._x1=e+x*s},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,i,a){if(e=+e,t=+t,a=!!a,(r=+r)<0)throw new Error(`negative radius: ${r}`);let o=r*Math.cos(n),s=r*Math.sin(n),c=e+o,l=t+s,u=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${l}`:(Math.abs(this._x1-c)>Ti||Math.abs(this._y1-l)>Ti)&&this._append`L${c},${l}`,r&&(f<0&&(f=f%Ci+Ci),f>Di?this._append`A${r},${r},0,1,${u},${e-o},${t-s}A${r},${r},0,1,${u},${this._x1=c},${this._y1=l}`:f>Ti&&this._append`A${r},${r},0,${+(f>=_i)},${u},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function $i(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{const e=Math.floor(r);if(!(e>=0))throw new RangeError(`invalid digits: ${r}`);t=e}return e},()=>new Bi(t)}function Ri(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function Li(e){this._context=e}function zi(e){return new Li(e)}function Fi(e){return e[0]}function Ui(e){return e[1]}function qi(e,t){var r=Si(!0),n=null,i=zi,a=null,o=$i(s);function s(s){var c,l,u,f=(s=Ri(s)).length,d=!1;for(null==n&&(a=i(u=o())),c=0;c<=f;++c)!(c<f&&r(l=s[c],c,s))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(l,c,s),+t(l,c,s));if(u)return a=null,u+""||null}return e="function"==typeof e?e:void 0===e?Fi:Si(e),t="function"==typeof t?t:void 0===t?Ui:Si(t),s.x=function(t){return arguments.length?(e="function"==typeof t?t:Si(+t),s):e},s.y=function(e){return arguments.length?(t="function"==typeof e?e:Si(+e),s):t},s.defined=function(e){return arguments.length?(r="function"==typeof e?e:Si(!!e),s):r},s.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),s):i},s.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),s):n},s}function Wi(e,t,r){var n=null,i=Si(!0),a=null,o=zi,s=null,c=$i(l);function l(l){var u,f,d,p,h,m=(l=Ri(l)).length,y=!1,g=new Array(m),v=new Array(m);for(null==a&&(s=o(h=c())),u=0;u<=m;++u){if(!(u<m&&i(p=l[u],u,l))===y)if(y=!y)f=u,s.areaStart(),s.lineStart();else{for(s.lineEnd(),s.lineStart(),d=u-1;d>=f;--d)s.point(g[d],v[d]);s.lineEnd(),s.areaEnd()}y&&(g[u]=+e(p,u,l),v[u]=+t(p,u,l),s.point(n?+n(p,u,l):g[u],r?+r(p,u,l):v[u]))}if(h)return s=null,h+""||null}function u(){return qi().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?Fi:Si(+e),t="function"==typeof t?t:Si(void 0===t?0:+t),r="function"==typeof r?r:void 0===r?Ui:Si(+r),l.x=function(t){return arguments.length?(e="function"==typeof t?t:Si(+t),n=null,l):e},l.x0=function(t){return arguments.length?(e="function"==typeof t?t:Si(+t),l):e},l.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:Si(+e),l):n},l.y=function(e){return arguments.length?(t="function"==typeof e?e:Si(+e),r=null,l):t},l.y0=function(e){return arguments.length?(t="function"==typeof e?e:Si(+e),l):t},l.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:Si(+e),l):r},l.lineX0=l.lineY0=function(){return u().x(e).y(t)},l.lineY1=function(){return u().x(e).y(r)},l.lineX1=function(){return u().x(n).y(t)},l.defined=function(e){return arguments.length?(i="function"==typeof e?e:Si(!!e),l):i},l.curve=function(e){return arguments.length?(o=e,null!=a&&(s=o(a)),l):o},l.context=function(e){return arguments.length?(null==e?a=s=null:s=o(a=e),l):a},l}Li.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}};class Vi{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}const Gi={draw(e,t){const r=Ni(t/Ei);e.moveTo(r,0),e.arc(0,0,r,0,Mi)}},Yi={draw(e,t){const r=Ni(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Hi=Ni(1/3),Xi=2*Hi,Ki={draw(e,t){const r=Ni(t/Xi),n=r*Hi;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},Zi={draw(e,t){const r=Ni(t),n=-r/2;e.rect(n,n,r,r)}},Ji=Ai(Ei/10)/Ai(7*Ei/10),Qi=Ai(Mi/10)*Ji,ea=-Pi(Mi/10)*Ji,ta={draw(e,t){const r=Ni(.8908130915292852*t),n=Qi*r,i=ea*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const t=Mi*a/5,o=Pi(t),s=Ai(t);e.lineTo(s*r,-o*r),e.lineTo(o*n-s*i,s*n+o*i)}e.closePath()}},ra=Ni(3),na={draw(e,t){const r=-Ni(t/(3*ra));e.moveTo(0,2*r),e.lineTo(-ra*r,-r),e.lineTo(ra*r,-r),e.closePath()}},ia=-.5,aa=Ni(3)/2,oa=1/Ni(12),sa=3*(oa/2+1),ca={draw(e,t){const r=Ni(t/sa),n=r/2,i=r*oa,a=n,o=r*oa+r,s=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(s,c),e.lineTo(ia*n-aa*i,aa*n+ia*i),e.lineTo(ia*a-aa*o,aa*a+ia*o),e.lineTo(ia*s-aa*c,aa*s+ia*c),e.lineTo(ia*n+aa*i,ia*i-aa*n),e.lineTo(ia*a+aa*o,ia*o-aa*a),e.lineTo(ia*s+aa*c,ia*c-aa*s),e.closePath()}};function la(){}function ua(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function fa(e){this._context=e}function da(e){this._context=e}function pa(e){this._context=e}function ha(e){this._context=e}function ma(e){return e<0?-1:1}function ya(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),s=(a*i+o*n)/(n+i);return(ma(a)+ma(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(s))||0}function ga(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function va(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,s=(a-n)/3;e._context.bezierCurveTo(n+s,i+s*t,a-s,o-s*r,a,o)}function ba(e){this._context=e}function xa(e){this._context=new wa(e)}function wa(e){this._context=e}function ja(e){this._context=e}function Oa(e){var t,r,n=e.length-1,i=new Array(n),a=new Array(n),o=new Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[n-1]=(e[n]+i[n-1])/2,t=0;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function ka(e,t){this._context=e,this._t=t}function Sa(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],s=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<s;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function Pa(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Aa(e,t){return e[t]}function Na(e){const t=[];return t.key=e,t}function Ea(e){return(Ea="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}fa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:ua(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:ua(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},da.prototype={areaStart:la,areaEnd:la,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:ua(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},pa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:ua(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},ha.prototype={areaStart:la,areaEnd:la,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},ba.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:va(this,this._t0,ga(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,va(this,ga(this,r=ya(this,e,t)),r);break;default:va(this,this._t0,r=ya(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(xa.prototype=Object.create(ba.prototype)).point=function(e,t){ba.prototype.point.call(this,t,e)},wa.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},ja.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=Oa(e),i=Oa(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},ka.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var Ma=["type","size","sizeType"];function _a(){return _a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_a.apply(this,arguments)}function Ca(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ta(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ca(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Ea(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Ea(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Ea(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ca(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Da={symbolCircle:Gi,symbolCross:Yi,symbolDiamond:Ki,symbolSquare:Zi,symbolStar:ta,symbolTriangle:na,symbolWye:ca},Ia=Math.PI/180,Ba=function(e){var t,r,n=e.type,i=void 0===n?"circle":n,a=e.size,s=void 0===a?64:a,c=e.sizeType,l=void 0===c?"area":c,u=Ta(Ta({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ma)),{},{type:i,size:s,sizeType:l}),f=u.className,d=u.cx,p=u.cy,h=Yn(u,!0);return d===+d&&p===+p&&s===+s?o.createElement("path",_a({},h,{className:P("recharts-symbols",f),transform:"translate(".concat(d,", ").concat(p,")"),d:(t=function(e){var t="symbol".concat(ki(e));return Da[t]||Gi}(i),r=function(e,t){let r=null,n=$i(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:Si(e||Gi),t="function"==typeof t?t:Si(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:Si(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:Si(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i}().type(t).size(function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return.5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*Ia;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}}(s,l,i)),r())})):null};function $a(e){return($a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ra(){return Ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ra.apply(this,arguments)}function La(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function za(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(za=function(){return!!e})()}function Fa(e){return(Fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ua(e,t){return(Ua=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function qa(e,t,r){return(t=Wa(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wa(e){var t=function(e){if("object"!=$a(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=$a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==$a(t)?t:t+""}Ba.registerSymbol=function(e,t){Da["symbol".concat(ki(e))]=t};var Va=32,Ga=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=Fa(r=e),function(e,t){if(t&&("object"===$a(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,za()?Reflect.construct(r,n||[],Fa(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ua(e,t)}(e,n.PureComponent),t=e,r=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=16,n=Va/6,i=Va/3,a=e.inactive?t:e.color;if("plainline"===e.type)return o.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:r,x2:Va,y2:r,className:"recharts-legend-icon"});if("line"===e.type)return o.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(i,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(2*i,",").concat(r,"\n            H").concat(Va,"M").concat(2*i,",").concat(r,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(i,",").concat(r),className:"recharts-legend-icon"});if("rect"===e.type)return o.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(Va,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(o.isValidElement(e.legendIcon)){var s=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?La(Object(r),!0).forEach((function(t){qa(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):La(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e);return delete s.legendIcon,o.cloneElement(e.legendIcon,s)}return o.createElement(Ba,{fill:a,cx:r,cy:r,size:Va,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,i=t.layout,a=t.formatter,s=t.inactiveColor,c={x:0,y:0,width:Va,height:Va},l={display:"horizontal"===i?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map((function(t,r){var i=t.formatter||a,f=P(qa(qa({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var d=ft(t.value)?null:t.value;ni(!ft(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var p=t.inactive?s:t.color;return o.createElement("li",Ra({className:f,style:l,key:"legend-item-".concat(r)},Tn(e.props,t,r)),o.createElement(Qn,{width:n,height:n,viewBox:c,style:u},e.renderIcon(t)),o.createElement("span",{className:"recharts-legend-item-text",style:{color:p}},i?i(d,t,r):d))}))}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;if(!t||!t.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?n:"left"};return o.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}],r&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Wa(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();qa(Ga,"displayName","Legend"),qa(Ga,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Ya=Kt,Ha=Kt,Xa=Zt,Ka=sr,Za=Kt;function Ja(e){var t=this.__data__=new Za(e);this.size=t.size}Ja.prototype.clear=function(){this.__data__=new Ya,this.size=0},Ja.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},Ja.prototype.get=function(e){return this.__data__.get(e)},Ja.prototype.has=function(e){return this.__data__.has(e)},Ja.prototype.set=function(e,t){var r=this.__data__;if(r instanceof Ha){var n=r.__data__;if(!Xa||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Ka(n)}return r.set(e,t),this.size=r.size,this};var Qa=Ja,eo=sr;function to(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new eo;++t<r;)this.add(e[t])}to.prototype.add=to.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},to.prototype.has=function(e){return this.__data__.has(e)};var ro,no,io,ao,oo,so,co,lo,uo=to,fo=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1},po=function(e,t){return e.has(t)},ho=uo,mo=fo,yo=po,go=function(e,t,r,n,i,a){var o=1&r,s=e.length,c=t.length;if(s!=c&&!(o&&c>s))return!1;var l=a.get(e),u=a.get(t);if(l&&u)return l==t&&u==e;var f=-1,d=!0,p=2&r?new ho:void 0;for(a.set(e,t),a.set(t,e);++f<s;){var h=e[f],m=t[f];if(n)var y=o?n(m,h,f,t,e,a):n(h,m,f,e,t,a);if(void 0!==y){if(y)continue;d=!1;break}if(p){if(!mo(t,(function(e,t){if(!yo(p,t)&&(h===e||i(h,e,r,n,a)))return p.push(t)}))){d=!1;break}}else if(h!==m&&!i(h,m,r,n,a)){d=!1;break}}return a.delete(e),a.delete(t),d},vo=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r},bo=ze.Uint8Array,xo=zt,wo=go,jo=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r},Oo=vo,ko=Fe?Fe.prototype:void 0,So=ko?ko.valueOf:void 0,Po=function(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e},Ao=Po,No=Be,Eo=Object.prototype.propertyIsEnumerable,Mo=Object.getOwnPropertySymbols,_o=Mo?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var r=-1,n=null==e?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}(Mo(e),(function(t){return Eo.call(e,t)})))}:function(){return[]},Co=Ke,To=Ze,Do=function(e){return To(e)&&"[object Arguments]"==Co(e)},Io=Ze,Bo=Object.prototype,$o=Bo.hasOwnProperty,Ro=Bo.propertyIsEnumerable,Lo=Do(function(){return arguments}())?Do:function(e){return Io(e)&&$o.call(e,"callee")&&!Ro.call(e,"callee")},zo={exports:{}};ro=zo,io=ze,ao=function(){return!1},lo=((co=(so=(oo=(no=zo.exports)&&!no.nodeType&&no)&&ro&&!ro.nodeType&&ro)&&so.exports===oo?io.Buffer:void 0)?co.isBuffer:void 0)||ao,ro.exports=lo;var Fo=zo.exports,Uo=/^(?:0|[1-9]\d*)$/,qo=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&Uo.test(e))&&e>-1&&e%1==0&&e<t},Wo=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},Vo=Ke,Go=Wo,Yo=Ze,Ho={};Ho["[object Float32Array]"]=Ho["[object Float64Array]"]=Ho["[object Int8Array]"]=Ho["[object Int16Array]"]=Ho["[object Int32Array]"]=Ho["[object Uint8Array]"]=Ho["[object Uint8ClampedArray]"]=Ho["[object Uint16Array]"]=Ho["[object Uint32Array]"]=!0,Ho["[object Arguments]"]=Ho["[object Array]"]=Ho["[object ArrayBuffer]"]=Ho["[object Boolean]"]=Ho["[object DataView]"]=Ho["[object Date]"]=Ho["[object Error]"]=Ho["[object Function]"]=Ho["[object Map]"]=Ho["[object Number]"]=Ho["[object Object]"]=Ho["[object RegExp]"]=Ho["[object Set]"]=Ho["[object String]"]=Ho["[object WeakMap]"]=!1;var Xo=function(e){return function(t){return e(t)}},Ko={exports:{}};!function(e,t){var r=$e,n=t&&!t.nodeType&&t,i=n&&e&&!e.nodeType&&e,a=i&&i.exports===n&&r.process,o=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=o}(Ko,Ko.exports);var Zo=Ko.exports,Jo=function(e){return Yo(e)&&Go(e.length)&&!!Ho[Vo(e)]},Qo=Xo,es=Zo&&Zo.isTypedArray,ts=es?Qo(es):Jo,rs=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n},ns=Lo,is=Be,as=Fo,os=qo,ss=ts,cs=Object.prototype.hasOwnProperty,ls=Object.prototype,us=function(e,t){return function(r){return e(t(r))}},fs=us(Object.keys,Object),ds=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||ls)},ps=fs,hs=Object.prototype.hasOwnProperty,ms=ut,ys=Wo,gs=function(e){return null!=e&&ys(e.length)&&!ms(e)},vs=function(e,t){var r=is(e),n=!r&&ns(e),i=!r&&!n&&as(e),a=!r&&!n&&!i&&ss(e),o=r||n||i||a,s=o?rs(e.length,String):[],c=s.length;for(var l in e)!t&&!cs.call(e,l)||o&&("length"==l||i&&("offset"==l||"parent"==l)||a&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||os(l,c))||s.push(l);return s},bs=gs,xs=function(e){return bs(e)?vs(e):function(e){if(!ds(e))return ps(e);var t=[];for(var r in Object(e))hs.call(e,r)&&"constructor"!=r&&t.push(r);return t}(e)},ws=function(e,t,r){var n=t(e);return No(e)?n:Ao(n,r(e))},js=_o,Os=xs,ks=function(e){return ws(e,Os,js)},Ss=Object.prototype.hasOwnProperty,Ps=At(ze,"DataView"),As=At(ze,"Promise"),Ns=At(ze,"Set"),Es=Ps,Ms=Zt,_s=As,Cs=Ns,Ts=At(ze,"WeakMap"),Ds=Ke,Is=yt,Bs="[object Map]",$s="[object Promise]",Rs="[object Set]",Ls="[object WeakMap]",zs="[object DataView]",Fs=Is(Es),Us=Is(Ms),qs=Is(_s),Ws=Is(Cs),Vs=Is(Ts),Gs=Ds;(Es&&Gs(new Es(new ArrayBuffer(1)))!=zs||Ms&&Gs(new Ms)!=Bs||_s&&Gs(_s.resolve())!=$s||Cs&&Gs(new Cs)!=Rs||Ts&&Gs(new Ts)!=Ls)&&(Gs=function(e){var t=Ds(e),r="[object Object]"==t?e.constructor:void 0,n=r?Is(r):"";if(n)switch(n){case Fs:return zs;case Us:return Bs;case qs:return $s;case Ws:return Rs;case Vs:return Ls}return t});var Ys=Qa,Hs=go,Xs=function(e,t,r,n,i,a,o){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!a(new bo(e),new bo(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return xo(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=jo;case"[object Set]":var c=1&n;if(s||(s=Oo),e.size!=t.size&&!c)return!1;var l=o.get(e);if(l)return l==t;n|=2,o.set(e,t);var u=wo(s(e),s(t),n,i,a,o);return o.delete(e),u;case"[object Symbol]":if(So)return So.call(e)==So.call(t)}return!1},Ks=function(e,t,r,n,i,a){var o=1&r,s=ks(e),c=s.length;if(c!=ks(t).length&&!o)return!1;for(var l=c;l--;){var u=s[l];if(!(o?u in t:Ss.call(t,u)))return!1}var f=a.get(e),d=a.get(t);if(f&&d)return f==t&&d==e;var p=!0;a.set(e,t),a.set(t,e);for(var h=o;++l<c;){var m=e[u=s[l]],y=t[u];if(n)var g=o?n(y,m,u,t,e,a):n(m,y,u,e,t,a);if(!(void 0===g?m===y||i(m,y,r,n,a):g)){p=!1;break}h||(h="constructor"==u)}if(p&&!h){var v=e.constructor,b=t.constructor;v==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof v&&v instanceof v&&"function"==typeof b&&b instanceof b||(p=!1)}return a.delete(e),a.delete(t),p},Zs=Gs,Js=Be,Qs=Fo,ec=ts,tc="[object Arguments]",rc="[object Array]",nc="[object Object]",ic=Object.prototype.hasOwnProperty,ac=Ze,oc=function e(t,r,n,i,a){return t===r||(null==t||null==r||!ac(t)&&!ac(r)?t!=t&&r!=r:function(e,t,r,n,i,a){var o=Js(e),s=Js(t),c=o?rc:Zs(e),l=s?rc:Zs(t),u=(c=c==tc?nc:c)==nc,f=(l=l==tc?nc:l)==nc,d=c==l;if(d&&Qs(e)){if(!Qs(t))return!1;o=!0,u=!1}if(d&&!u)return a||(a=new Ys),o||ec(e)?Hs(e,t,r,n,i,a):Xs(e,t,c,r,n,i,a);if(!(1&r)){var p=u&&ic.call(e,"__wrapped__"),h=f&&ic.call(t,"__wrapped__");if(p||h){var m=p?e.value():e,y=h?t.value():t;return a||(a=new Ys),i(m,y,r,n,a)}}return!!d&&(a||(a=new Ys),Ks(e,t,r,n,i,a))}(t,r,n,i,e,a))},sc=Qa,cc=oc,lc=ot,uc=function(e){return e==e&&!lc(e)},fc=uc,dc=xs,pc=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}},hc=pc,mc=Er,yc=Lo,gc=Be,vc=qo,bc=Wo,xc=_r,wc=function(e,t){return null!=e&&t in Object(e)},jc=oc,Oc=Br,kc=function(e,t){return null!=e&&function(e,t,r){for(var n=-1,i=(t=mc(t,e)).length,a=!1;++n<i;){var o=xc(t[n]);if(!(a=null!=e&&r(e,o)))break;e=e[o]}return a||++n!=i?a:!!(i=null==e?0:e.length)&&bc(i)&&vc(o,i)&&(gc(e)||yc(e))}(e,t,wc)},Sc=at,Pc=uc,Ac=pc,Nc=_r,Ec=function(e){return e},Mc=Dr,_c=at,Cc=_r,Tc=function(e){var t=function(e){for(var t=dc(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,fc(i)]}return t}(e);return 1==t.length&&t[0][2]?hc(t[0][0],t[0][1]):function(r){return r===e||function(e,t,r,n){var i=r.length,a=i,o=!n;if(null==e)return!a;for(e=Object(e);i--;){var s=r[i];if(o&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<a;){var c=(s=r[i])[0],l=e[c],u=s[1];if(o&&s[2]){if(void 0===l&&!(c in e))return!1}else{var f=new sc;if(n)var d=n(l,u,c,e,t,f);if(!(void 0===d?cc(u,l,3,n,f):d))return!1}}return!0}(r,e,t)}},Dc=Ec,Ic=Be,Bc=function(e){return _c(e)?(t=Cc(e),function(e){return null==e?void 0:e[t]}):function(e){return function(t){return Mc(t,e)}}(e);var t},$c=function(e){return"function"==typeof e?e:null==e?Dc:"object"==typeof e?Ic(e)?(t=e[0],r=e[1],Sc(t)&&Pc(r)?Ac(Nc(t),r):function(e){var n=Oc(e,t);return void 0===n&&n===r?kc(e,t):jc(r,n,3)}):Tc(e):Bc(e);var t,r},Rc=function(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1},Lc=Rc,zc=function(e){return e!=e},Fc=Ns,Uc=Fc&&1/vo(new Fc([,-0]))[1]==1/0?function(e){return new Fc(e)}:function(){},qc=uo,Wc=function(e,t){return!(null==e||!e.length)&&function(e,t,r){return t==t?function(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}(e,t,r):Lc(e,zc,r)}(e,t,0)>-1},Vc=function(e,t,r){for(var n=-1,i=null==e?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1},Gc=po,Yc=Uc,Hc=vo,Xc=$c;const Kc=a((function(e,t){return e&&e.length?function(e,t,r){var n=-1,i=Wc,a=e.length,o=!0,s=[],c=s;if(r)o=!1,i=Vc;else if(a>=200){var l=t?null:Yc(e);if(l)return Hc(l);o=!1,i=Gc,c=new qc}else c=t?[]:s;e:for(;++n<a;){var u=e[n],f=t?t(u):u;if(u=r||0!==u?u:0,o&&f==f){for(var d=c.length;d--;)if(c[d]===f)continue e;t&&c.push(f),s.push(u)}else i(c,f,r)||(c!==s&&c.push(f),s.push(u))}return s}(e,Xc(t)):[]}));function Zc(e,t,r){return!0===t?Kc(e,r):ft(t)?Kc(e,t):e}function Jc(e){return(Jc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Qc=["ref"];function el(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function tl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?el(Object(r),!0).forEach((function(t){ol(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):el(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function rl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sl(n.key),n)}}function nl(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(nl=function(){return!!e})()}function il(e){return(il=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function al(e,t){return(al=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ol(e,t,r){return(t=sl(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sl(e){var t=function(e){if("object"!=Jc(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Jc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Jc(t)?t:t+""}function cl(e){return e.value}var ll=function(){function e(){var t,r,n,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];return ol((r=this,n=e,i=[].concat(o),n=il(n),t=function(e,t){if(t&&("object"===Jc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(r,nl()?Reflect.construct(n,i||[],il(r).constructor):n.apply(r,i))),"lastBoundingBox",{width:-1,height:-1}),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&al(e,t)}(e,n.PureComponent),t=e,i=[{key:"getWithHeight",value:function(e,t){var r=tl(tl({},this.defaultProps),e.props).layout;return"vertical"===r&&gn(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],(r=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?tl({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,i=n.layout,a=n.align,o=n.verticalAlign,s=n.margin,c=n.chartWidth,l=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===a&&"vertical"===i?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===a?{right:s&&s.right||0}:{left:s&&s.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===o?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===o?{bottom:s&&s.bottom||0}:{top:s&&s.top||0}),tl(tl({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,i=t.height,a=t.wrapperStyle,s=t.payloadUniqBy,c=t.payload,l=tl(tl({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return o.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(t){e.wrapperNode=t}},function(e,t){if(o.isValidElement(e))return o.cloneElement(e,t);if("function"==typeof e)return o.createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(t,Qc);return o.createElement(Ga,r)}(r,tl(tl({},this.props),{},{payload:Zc(c,s,cl)})))}}])&&rl(t.prototype,r),i&&rl(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,i}();ol(ll,"displayName","Legend"),ol(ll,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ul,fl,dl,pl,hl=Lo,ml=Be,yl=Fe?Fe.isConcatSpreadable:void 0,gl=Po,vl=function(e){return ml(e)||hl(e)||!!(yl&&e&&e[yl])},bl=function e(t,r,n,i,a){var o=-1,s=t.length;for(n||(n=vl),a||(a=[]);++o<s;){var c=t[o];r>0&&n(c)?r>1?e(c,r-1,n,i,a):gl(a,c):i||(a[a.length]=c)}return a},xl=function(e,t,r){for(var n=-1,i=Object(e),a=r(e),o=a.length;o--;){var s=a[++n];if(!1===t(i[s],s,i))break}return e},wl=xs,jl=function(e,t){return e&&xl(e,t,wl)},Ol=gs,kl=(ul=jl,function(e,t){if(null==e)return e;if(!Ol(e))return ul(e,t);for(var r=e.length,n=-1,i=Object(e);++n<r&&!1!==t(i[n],n,i););return e}),Sl=kl,Pl=gs,Al=function(e,t){var r=-1,n=Pl(e)?Array(e.length):[];return Sl(e,(function(e,i,a){n[++r]=t(e,i,a)})),n},Nl=et,El=function(e,t){if(e!==t){var r=void 0!==e,n=null===e,i=e==e,a=Nl(e),o=void 0!==t,s=null===t,c=t==t,l=Nl(t);if(!s&&!l&&!a&&e>t||a&&o&&c&&!s&&!l||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!l&&e<t||l&&r&&i&&!n&&!a||s&&r&&i||!o&&i||!c)return-1}return 0},Ml=gr,_l=Dr,Cl=$c,Tl=Al,Dl=Xo,Il=Ec,Bl=Be,$l=Math.max,Rl=At,Ll=function(){try{var e=Rl(Object,"defineProperty");return e({},"",{}),e}catch(t){}}(),zl=function(e){return function(){return e}},Fl=Ll,Ul=Fl?function(e,t){return Fl(e,"toString",{configurable:!0,enumerable:!1,value:zl(t),writable:!0})}:Ec,ql=Date.now,Wl=(fl=Ul,dl=0,pl=0,function(){var e=ql(),t=16-(e-pl);if(pl=e,t>0){if(++dl>=800)return arguments[0]}else dl=0;return fl.apply(void 0,arguments)}),Vl=Ec,Gl=function(e,t,r){return t=$l(void 0===t?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=$l(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var s=Array(t+1);++i<t;)s[i]=n[i];return s[t]=r(o),function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(e,this,s)}},Yl=Wl,Hl=zt,Xl=gs,Kl=qo,Zl=ot,Jl=function(e,t,r){if(!Zl(r))return!1;var n=typeof t;return!!("number"==n?Xl(r)&&Kl(t,r.length):"string"==n&&t in r)&&Hl(r[t],e)},Ql=bl,eu=function(e,t,r){t=t.length?Ml(t,(function(e){return Bl(e)?function(t){return _l(t,1===e.length?e[0]:e)}:e})):[Il];var n=-1;return t=Ml(t,Dl(Cl)),function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}(Tl(e,(function(e,r,i){return{criteria:Ml(t,(function(t){return t(e)})),index:++n,value:e}})),(function(e,t){return function(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,s=r.length;++n<o;){var c=El(i[n],a[n]);if(c)return n>=s?c:c*("desc"==r[n]?-1:1)}return e.index-t.index}(e,t,r)}))},tu=Jl;const ru=a(function(e){return Yl(Gl(e,void 0,Vl),e+"")}((function(e,t){if(null==e)return[];var r=t.length;return r>1&&tu(e,t[0],t[1])?t=[]:r>2&&tu(t[0],t[1],t[2])&&(t=[t[0]]),eu(e,Ql(t,1),[])})));function nu(e){return(nu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iu(){return iu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},iu.apply(this,arguments)}function au(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ou(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function su(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ou(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=nu(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=nu(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==nu(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ou(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function cu(e){return Array.isArray(e)&&vn(e[0])&&vn(e[1])?e.join(" ~ "):e}var lu=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,i=void 0===n?{}:n,a=e.itemStyle,s=void 0===a?{}:a,c=e.labelStyle,l=void 0===c?{}:c,u=e.payload,f=e.formatter,d=e.itemSorter,p=e.wrapperClassName,h=e.labelClassName,m=e.label,y=e.labelFormatter,g=e.accessibilityLayer,v=void 0!==g&&g,b=su({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),x=su({margin:0},l),w=!Rr(m),j=w?m:"",O=P("recharts-default-tooltip",p),k=P("recharts-tooltip-label",h);w&&y&&null!=u&&(j=y(m,u));var S=v?{role:"status","aria-live":"assertive"}:{};return o.createElement("div",iu({className:O,style:b},S),o.createElement("p",{className:k,style:x},o.isValidElement(j)?j:"".concat(j)),function(){if(u&&u.length){var e=(d?ru(u,d):u).map((function(e,t){if("none"===e.type)return null;var n=su({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},s),i=e.formatter||f||cu,a=e.value,c=e.name,l=a,d=c;if(i&&null!=l&&null!=d){var p=i(a,c,e,t,u);if(Array.isArray(p)){var h=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return au(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?au(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(p,2);l=h[0],d=h[1]}else l=p}return o.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},vn(d)?o.createElement("span",{className:"recharts-tooltip-item-name"},d):null,vn(d)?o.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,o.createElement("span",{className:"recharts-tooltip-item-value"},l),o.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))}));return o.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function uu(e){return(uu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fu(e,t,r){var n;return n=function(e,t){if("object"!=uu(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=uu(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t,"string"),(t="symbol"==uu(n)?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var du="recharts-tooltip-wrapper",pu={visibility:"hidden"};function hu(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return P(du,fu(fu(fu(fu({},"".concat(du,"-right"),gn(r)&&t&&gn(t.x)&&r>=t.x),"".concat(du,"-left"),gn(r)&&t&&gn(t.x)&&r<t.x),"".concat(du,"-bottom"),gn(n)&&t&&gn(t.y)&&n>=t.y),"".concat(du,"-top"),gn(n)&&t&&gn(t.y)&&n<t.y))}function mu(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,s=e.tooltipDimension,c=e.viewBox,l=e.viewBoxDimension;if(a&&gn(a[n]))return a[n];var u=r[n]-s-i,f=r[n]+i;return t[n]?o[n]?u:f:o[n]?u<c[n]?Math.max(f,c[n]):Math.max(u,c[n]):f+s>c[n]+l?Math.max(u,c[n]):Math.max(f,c[n])}function yu(e){return(yu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function gu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function vu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gu(Object(r),!0).forEach((function(t){ju(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gu(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function bu(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(bu=function(){return!!e})()}function xu(e){return(xu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function wu(e,t){return(wu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ju(e,t,r){return(t=Ou(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ou(e){var t=function(e){if("object"!=yu(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=yu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==yu(t)?t:t+""}var ku=function(){function e(){var t,r,n,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];return ju((r=this,n=e,i=[].concat(o),n=xu(n),t=function(e,t){if(t&&("object"===yu(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(r,bu()?Reflect.construct(n,i||[],xu(r).constructor):n.apply(r,i))),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ju(t,"handleKeyDown",(function(e){var r,n,i,a;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(i=null===(a=t.props.coordinate)||void 0===a?void 0:a.y)&&void 0!==i?i:0}})})),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wu(e,t)}(e,n.PureComponent),t=e,(r=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var e=this,t=this.props,r=t.active,n=t.allowEscapeViewBox,i=t.animationDuration,a=t.animationEasing,s=t.children,c=t.coordinate,l=t.hasPayload,u=t.isAnimationActive,f=t.offset,d=t.position,p=t.reverseDirection,h=t.useTranslate3d,m=t.viewBox,y=t.wrapperStyle,g=function(e){var t,r,n=e.allowEscapeViewBox,i=e.coordinate,a=e.offsetTopLeft,o=e.position,s=e.reverseDirection,c=e.tooltipBox,l=e.useTranslate3d,u=e.viewBox;return{cssProperties:c.height>0&&c.width>0&&i?function(e){var t=e.translateX,r=e.translateY;return{transform:e.useTranslate3d?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:t=mu({allowEscapeViewBox:n,coordinate:i,key:"x",offsetTopLeft:a,position:o,reverseDirection:s,tooltipDimension:c.width,viewBox:u,viewBoxDimension:u.width}),translateY:r=mu({allowEscapeViewBox:n,coordinate:i,key:"y",offsetTopLeft:a,position:o,reverseDirection:s,tooltipDimension:c.height,viewBox:u,viewBoxDimension:u.height}),useTranslate3d:l}):pu,cssClasses:hu({translateX:t,translateY:r,coordinate:i})}}({allowEscapeViewBox:n,coordinate:c,offsetTopLeft:f,position:d,reverseDirection:p,tooltipBox:this.state.lastBoundingBox,useTranslate3d:h,viewBox:m}),v=g.cssClasses,b=g.cssProperties,x=vu(vu({transition:u&&r?"transform ".concat(i,"ms ").concat(a):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},y);return o.createElement("div",{tabIndex:-1,className:v,style:x,ref:function(t){e.wrapperNode=t}},s)}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ou(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}(),Su=!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout);function Pu(e){return(Pu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Au(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Nu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Au(Object(r),!0).forEach((function(t){Cu(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Au(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Eu(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Eu=function(){return!!e})()}function Mu(e){return(Mu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _u(e,t){return(_u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Cu(e,t,r){return(t=Tu(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tu(e){var t=function(e){if("object"!=Pu(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Pu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Pu(t)?t:t+""}function Du(e){return e.dataKey}var Iu=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=Mu(r=e),function(e,t){if(t&&("object"===Pu(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,Eu()?Reflect.construct(r,n||[],Mu(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_u(e,t)}(e,n.PureComponent),t=e,(r=[{key:"render",value:function(){var e=this,t=this.props,r=t.active,n=t.allowEscapeViewBox,i=t.animationDuration,a=t.animationEasing,s=t.content,c=t.coordinate,l=t.filterNull,u=t.isAnimationActive,f=t.offset,d=t.payload,p=t.payloadUniqBy,h=t.position,m=t.reverseDirection,y=t.useTranslate3d,g=t.viewBox,v=t.wrapperStyle,b=null!=d?d:[];l&&b.length&&(b=Zc(d.filter((function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)})),p,Du));var x=b.length>0;return o.createElement(ku,{allowEscapeViewBox:n,animationDuration:i,animationEasing:a,isAnimationActive:u,active:r,coordinate:c,hasPayload:x,offset:f,position:h,reverseDirection:m,useTranslate3d:y,viewBox:g,wrapperStyle:v},function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):"function"==typeof e?o.createElement(e,t):o.createElement(lu,t)}(s,Nu(Nu({},this.props),{},{payload:b})))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tu(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();Cu(Iu,"displayName","Tooltip"),Cu(Iu,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Su,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Bu=ze,$u=/\s/,Ru=/^\s+/,Lu=function(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&$u.test(e.charAt(t)););return t}(e)+1).replace(Ru,""):e},zu=ot,Fu=et,Uu=/^[-+]0x[0-9a-f]+$/i,qu=/^0b[01]+$/i,Wu=/^0o[0-7]+$/i,Vu=parseInt,Gu=function(e){if("number"==typeof e)return e;if(Fu(e))return NaN;if(zu(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=zu(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Lu(e);var r=qu.test(e);return r||Wu.test(e)?Vu(e.slice(2),r?2:8):Uu.test(e)?NaN:+e},Yu=ot,Hu=function(){return Bu.Date.now()},Xu=Gu,Ku=Math.max,Zu=Math.min,Ju=ot;const Qu=a((function(e,t,r){var n=!0,i=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return Ju(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),function(e,t,r){var n,i,a,o,s,c,l=0,u=!1,f=!1,d=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var r=n,a=i;return n=i=void 0,l=t,o=e.apply(a,r)}function h(e){var r=e-c;return void 0===c||r>=t||r<0||f&&e-l>=a}function m(){var e=Hu();if(h(e))return y(e);s=setTimeout(m,function(e){var r=t-(e-c);return f?Zu(r,a-(e-l)):r}(e))}function y(e){return s=void 0,d&&n?p(e):(n=i=void 0,o)}function g(){var e=Hu(),r=h(e);if(n=arguments,i=this,c=e,r){if(void 0===s)return function(e){return l=e,s=setTimeout(m,t),u?p(e):o}(c);if(f)return clearTimeout(s),s=setTimeout(m,t),p(c)}return void 0===s&&(s=setTimeout(m,t)),o}return t=Xu(t)||0,Yu(r)&&(u=!!r.leading,a=(f="maxWait"in r)?Ku(Xu(r.maxWait)||0,t):a,d="trailing"in r?!!r.trailing:d),g.cancel=function(){void 0!==s&&clearTimeout(s),l=0,n=c=i=s=void 0},g.flush=function(){return void 0===s?o:y(Hu())},g}(e,t,{leading:n,maxWait:t,trailing:i})}));function ef(e){return(ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function rf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tf(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=ef(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=ef(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==ef(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tf(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function nf(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var af=n.forwardRef((function(e,t){var r=e.aspect,i=e.initialDimension,a=void 0===i?{width:-1,height:-1}:i,s=e.width,c=void 0===s?"100%":s,l=e.height,u=void 0===l?"100%":l,f=e.minWidth,d=void 0===f?0:f,p=e.minHeight,h=e.maxHeight,m=e.children,y=e.debounce,g=void 0===y?0:y,v=e.id,b=e.className,x=e.onResize,w=e.style,j=void 0===w?{}:w,O=n.useRef(null),k=n.useRef();k.current=x,n.useImperativeHandle(t,(function(){return Object.defineProperty(O.current,"current",{get:function(){return O.current},configurable:!0})}));var S,A,N=(S=n.useState({containerWidth:a.width,containerHeight:a.height}),A=2,function(e){if(Array.isArray(e))return e}(S)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(S,A)||function(e,t){if(e){if("string"==typeof e)return nf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?nf(e,t):void 0}}(S,A)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),E=N[0],M=N[1],_=n.useCallback((function(e,t){M((function(r){var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}}))}),[]);n.useEffect((function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,i=r.height;_(n,i),null===(t=k.current)||void 0===t||t.call(k,n,i)};g>0&&(e=Qu(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=O.current.getBoundingClientRect(),n=r.width,i=r.height;return _(n,i),t.observe(O.current),function(){t.disconnect()}}),[_,g]);var C=n.useMemo((function(){var e=E.containerWidth,t=E.containerHeight;if(e<0||t<0)return null;ni(yn(c)||yn(u),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,u),ni(!r||r>0,"The aspect(%s) must be greater than zero.",r);var i=yn(c)?e:c,a=yn(u)?t:u;r&&r>0&&(i?a=i/r:a&&(i=a*r),h&&a>h&&(a=h)),ni(i>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",i,a,c,u,d,p,r);var s=!Array.isArray(m)&&Ln(m.type).endsWith("Chart");return o.Children.map(m,(function(e){return o.isValidElement(e)?n.cloneElement(e,rf({width:i,height:a},s?{style:rf({height:"100%",width:"100%",maxHeight:a,maxWidth:i},e.props.style)}:{})):e}))}),[r,m,u,h,p,d,E,c]);return o.createElement("div",{id:v?"".concat(v):void 0,className:P("recharts-responsive-container",b),style:rf(rf({},j),{},{width:c,height:u,minWidth:d,minHeight:p,maxHeight:h}),ref:O},C)})),of=function(e){return null};function sf(e){return(sf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function cf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function lf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?cf(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=sf(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=sf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==sf(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cf(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}of.displayName="Cell";var uf={widthCache:{},cacheCount:0},ff={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},df="recharts_measurement_span",pf=function(e){if(null==e||Su)return{width:0,height:0};var t,r=(t=lf({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),Object.keys(t).forEach((function(e){t[e]||delete t[e]})),t),n=JSON.stringify({text:e,copyStyle:r});if(uf.widthCache[n])return uf.widthCache[n];try{var i=document.getElementById(df);i||((i=document.createElement("span")).setAttribute("id",df),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=lf(lf({},ff),r);Object.assign(i.style,a),i.textContent="".concat(e);var o=i.getBoundingClientRect(),s={width:o.width,height:o.height};return uf.widthCache[n]=s,++uf.cacheCount>2e3&&(uf.cacheCount=0,uf.widthCache={}),s}catch(c){return{width:0,height:0}}};function hf(e){return(hf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mf(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return yf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?yf(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yf(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function gf(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vf(n.key),n)}}function vf(e){var t=function(e){if("object"!=hf(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=hf(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==hf(t)?t:t+""}var bf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,xf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,wf=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,jf=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Of={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},kf=Object.keys(Of),Sf="NaN",Pf=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),""===r||wf.test(r)||(this.num=NaN,this.unit=""),kf.includes(r)&&(this.num=function(e,t){return e*Of[t]}(t,r),this.unit="px")}return t=e,n=[{key:"parse",value:function(t){var r,n=mf(null!==(r=jf.exec(t))&&void 0!==r?r:[],3),i=n[1],a=n[2];return new e(parseFloat(i),null!=a?a:"")}}],(r=[{key:"add",value:function(t){return this.unit!==t.unit?new e(NaN,""):new e(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new e(NaN,""):new e(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new e(NaN,""):new e(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new e(NaN,""):new e(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&gf(t.prototype,r),n&&gf(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,n}();function Af(e){if(e.includes(Sf))return Sf;for(var t=e;t.includes("*")||t.includes("/");){var r,n=mf(null!==(r=bf.exec(t))&&void 0!==r?r:[],4),i=n[1],a=n[2],o=n[3],s=Pf.parse(null!=i?i:""),c=Pf.parse(null!=o?o:""),l="*"===a?s.multiply(c):s.divide(c);if(l.isNaN())return Sf;t=t.replace(bf,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=mf(null!==(u=xf.exec(t))&&void 0!==u?u:[],4),d=f[1],p=f[2],h=f[3],m=Pf.parse(null!=d?d:""),y=Pf.parse(null!=h?h:""),g="+"===p?m.add(y):m.subtract(y);if(g.isNaN())return Sf;t=t.replace(xf,g.toString())}return t}var Nf=/\(([^()]*)\)/;function Ef(e){var t=function(e){try{return function(e){var t=e.replace(/\s+/g,"");return t=function(e){for(var t=e;t.includes("(");){var r=mf(Nf.exec(t),2)[1];t=t.replace(Nf,Af(r))}return t}(t),Af(t)}(e)}catch(t){return Sf}}(e.slice(5,-1));return t===Sf?"":t}var Mf=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],_f=["dx","dy","angle","className","breakAll"];function Cf(){return Cf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cf.apply(this,arguments)}function Tf(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Df(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return If(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?If(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function If(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Bf=/[ \f\n\r\t\v\u2028\u2029]+/,$f=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var i=[];return Rr(t)||(i=r?t.toString().split(""):t.toString().split(Bf)),{wordsWithComputedWidth:i.map((function(e){return{word:e,width:pf(e,n).width}})),spaceWidth:r?0:pf(" ",n).width}}catch(a){return null}},Rf=function(e){return[{words:Rr(e)?[]:e.toString().split(Bf)}]},Lf="#808080",zf=function(e){var t=e.x,r=void 0===t?0:t,i=e.y,a=void 0===i?0:i,s=e.lineHeight,c=void 0===s?"1em":s,l=e.capHeight,u=void 0===l?"0.71em":l,f=e.scaleToFit,d=void 0!==f&&f,p=e.textAnchor,h=void 0===p?"start":p,m=e.verticalAnchor,y=void 0===m?"end":m,g=e.fill,v=void 0===g?Lf:g,b=Tf(e,Mf),x=n.useMemo((function(){return function(e){var t=e.width,r=e.scaleToFit,n=e.children,i=e.style,a=e.breakAll,o=e.maxLines;if((t||r)&&!Su){var s=$f({breakAll:a,children:n,style:i});return s?function(e,t,r,n,i){var a=e.maxLines,o=e.children,s=e.style,c=e.breakAll,l=gn(a),u=o,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(e,t){var a=t.word,o=t.width,s=e[e.length-1];if(s&&(null==n||i||s.width+o+r<Number(n)))s.words.push(a),s.width+=o+r;else{var c={words:[a],width:o};e.push(c)}return e}),[])},d=f(t);if(!l)return d;for(var p,h=function(e){var t=u.slice(0,e),r=$f({breakAll:c,style:s,children:t+"…"}).wordsWithComputedWidth,i=f(r),o=i.length>a||function(e){return e.reduce((function(e,t){return e.width>t.width?e:t}))}(i).width>Number(n);return[o,i]},m=0,y=u.length-1,g=0;m<=y&&g<=u.length-1;){var v=Math.floor((m+y)/2),b=Df(h(v-1),2),x=b[0],w=b[1],j=Df(h(v),1)[0];if(x||j||(m=v+1),x&&j&&(y=v-1),!x&&j){p=w;break}g++}return p||d}({breakAll:a,children:n,maxLines:o,style:i},s.wordsWithComputedWidth,s.spaceWidth,t,r):Rf(n)}return Rf(n)}({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})}),[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),w=b.dx,j=b.dy,O=b.angle,k=b.className,S=b.breakAll,A=Tf(b,_f);if(!vn(r)||!vn(a))return null;var N,E=r+(gn(w)?w:0),M=a+(gn(j)?j:0);switch(y){case"start":N=Ef("calc(".concat(u,")"));break;case"middle":N=Ef("calc(".concat((x.length-1)/2," * -").concat(c," + (").concat(u," / 2))"));break;default:N=Ef("calc(".concat(x.length-1," * -").concat(c,")"))}var _=[];if(d){var C=x[0].width,T=b.width;_.push("scale(".concat((gn(T)?T/C:1)/C,")"))}return O&&_.push("rotate(".concat(O,", ").concat(E,", ").concat(M,")")),_.length&&(A.transform=_.join(" ")),o.createElement("text",Cf({},Yn(A,!0),{x:E,y:M,className:P("recharts-text",k),textAnchor:h,fill:v.includes("url")?Lf:v}),x.map((function(e,t){var r=e.words.join(S?"":" ");return o.createElement("tspan",{x:E,dy:0===t?N:c,key:"".concat(r,"-").concat(t)},r)})))};function Ff(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Uf(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function qf(e){let t,r,n;function i(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<0?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=Ff,r=(t,r)=>Ff(e(t),r),n=(t,r)=>e(t)-r):(t=e===Ff||e===Uf?e:Wf,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){const o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<=0?i=t+1:a=t}while(i<a)}return i}}}function Wf(){return 0}function Vf(e){return null===e?NaN:+e}const Gf=qf(Ff).right;qf(Vf).center;class Yf extends Map{constructor(e,t=Xf){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[r,n]of e)this.set(r,n)}get(e){return super.get(Hf(this,e))}has(e){return super.has(Hf(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function Hf({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function Xf(e){return null!==e&&"object"==typeof e?e.valueOf():e}function Kf(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}const Zf=Math.sqrt(50),Jf=Math.sqrt(10),Qf=Math.sqrt(2);function ed(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=Zf?10:a>=Jf?5:a>=Qf?2:1;let s,c,l;return i<0?(l=Math.pow(10,-i)/o,s=Math.round(e*l),c=Math.round(t*l),s/l<e&&++s,c/l>t&&--c,l=-l):(l=Math.pow(10,i)*o,s=Math.round(e/l),c=Math.round(t/l),s*l<e&&++s,c*l>t&&--c),c<s&&.5<=r&&r<2?ed(e,t,2*r):[s,c,l]}function td(e,t,r){if(!((r=+r)>0))return[];if((e=+e)===(t=+t))return[e];const n=t<e,[i,a,o]=n?ed(t,e,r):ed(e,t,r);if(!(a>=i))return[];const s=a-i+1,c=new Array(s);if(n)if(o<0)for(let l=0;l<s;++l)c[l]=(a-l)/-o;else for(let l=0;l<s;++l)c[l]=(a-l)*o;else if(o<0)for(let l=0;l<s;++l)c[l]=(i+l)/-o;else for(let l=0;l<s;++l)c[l]=(i+l)*o;return c}function rd(e,t,r){return ed(e=+e,t=+t,r=+r)[2]}function nd(e,t,r){r=+r;const n=(t=+t)<(e=+e),i=n?rd(t,e,r):rd(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function id(e,t){let r;for(const n of e)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);return r}function ad(e,t){let r;for(const n of e)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);return r}function od(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=void 0===i?Kf:function(e=Ff){if(e===Ff)return Kf;if("function"!=typeof e)throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);n>r;){if(n-r>600){const a=n-r+1,o=t-r+1,s=Math.log(a),c=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*c*(a-c)/a)*(o-a/2<0?-1:1);od(e,t,Math.max(r,Math.floor(t-o*c/a+l)),Math.min(n,Math.floor(t+(a-o)*c/a+l)),i)}const a=e[t];let o=r,s=n;for(sd(e,r,t),i(e[n],a)>0&&sd(e,r,n);o<s;){for(sd(e,o,s),++o,--s;i(e[o],a)<0;)++o;for(;i(e[s],a)>0;)--s}0===i(e[r],a)?sd(e,r,s):(++s,sd(e,s,n)),s<=t&&(r=s+1),t<=s&&(n=s-1)}return e}function sd(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function cd(e,t,r=Vf){if((n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}function ld(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function ud(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}const fd=Symbol("implicit");function dd(){var e=new Yf,t=[],r=[],n=fd;function i(i){let a=e.get(i);if(void 0===a){if(n!==fd)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();t=[],e=new Yf;for(const n of r)e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return dd(t,r).unknown(n)},ld.apply(i,arguments),i}function pd(){var e,t,r=dd().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,s=!1,c=0,l=0,u=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-c+2*l),s&&(e=Math.floor(e)),d+=(p-d-e*(r-c))*u,t=e*(1-c),s&&(d=Math.round(d),t=Math.round(t));var h=function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=new Array(i);++n<i;)a[n]=e+n*r;return a}(r).map((function(t){return d+e*t}));return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,s=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(s=!!e,f()):s},r.padding=function(e){return arguments.length?(c=Math.min(1,l=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(l=+e,f()):l},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return pd(n(),[a,o]).round(s).paddingInner(c).paddingOuter(l).align(u)},ld.apply(f(),arguments)}function hd(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return hd(t())},e}function md(){return hd(pd.apply(null,arguments).paddingInner(1))}function yd(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function gd(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function vd(){}var bd=.7,xd=1/bd,wd="\\s*([+-]?\\d+)\\s*",jd="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Od="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",kd=/^#([0-9a-f]{3,8})$/,Sd=new RegExp(`^rgb\\(${wd},${wd},${wd}\\)$`),Pd=new RegExp(`^rgb\\(${Od},${Od},${Od}\\)$`),Ad=new RegExp(`^rgba\\(${wd},${wd},${wd},${jd}\\)$`),Nd=new RegExp(`^rgba\\(${Od},${Od},${Od},${jd}\\)$`),Ed=new RegExp(`^hsl\\(${jd},${Od},${Od}\\)$`),Md=new RegExp(`^hsla\\(${jd},${Od},${Od},${jd}\\)$`),_d={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Cd(){return this.rgb().formatHex()}function Td(){return this.rgb().formatRgb()}function Dd(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=kd.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?Id(t):3===r?new Rd(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?Bd(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?Bd(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Sd.exec(e))?new Rd(t[1],t[2],t[3],1):(t=Pd.exec(e))?new Rd(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Ad.exec(e))?Bd(t[1],t[2],t[3],t[4]):(t=Nd.exec(e))?Bd(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Ed.exec(e))?Wd(t[1],t[2]/100,t[3]/100,1):(t=Md.exec(e))?Wd(t[1],t[2]/100,t[3]/100,t[4]):_d.hasOwnProperty(e)?Id(_d[e]):"transparent"===e?new Rd(NaN,NaN,NaN,0):null}function Id(e){return new Rd(e>>16&255,e>>8&255,255&e,1)}function Bd(e,t,r,n){return n<=0&&(e=t=r=NaN),new Rd(e,t,r,n)}function $d(e,t,r,n){return 1===arguments.length?((i=e)instanceof vd||(i=Dd(i)),i?new Rd((i=i.rgb()).r,i.g,i.b,i.opacity):new Rd):new Rd(e,t,r,null==n?1:n);var i}function Rd(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function Ld(){return`#${qd(this.r)}${qd(this.g)}${qd(this.b)}`}function zd(){const e=Fd(this.opacity);return`${1===e?"rgb(":"rgba("}${Ud(this.r)}, ${Ud(this.g)}, ${Ud(this.b)}${1===e?")":`, ${e})`}`}function Fd(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Ud(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function qd(e){return((e=Ud(e))<16?"0":"")+e.toString(16)}function Wd(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Gd(e,t,r,n)}function Vd(e){if(e instanceof Gd)return new Gd(e.h,e.s,e.l,e.opacity);if(e instanceof vd||(e=Dd(e)),!e)return new Gd;if(e instanceof Gd)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,s=a-i,c=(a+i)/2;return s?(o=t===a?(r-n)/s+6*(r<n):r===a?(n-t)/s+2:(t-r)/s+4,s/=c<.5?a+i:2-a-i,o*=60):s=c>0&&c<1?0:o,new Gd(o,s,c,e.opacity)}function Gd(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function Yd(e){return(e=(e||0)%360)<0?e+360:e}function Hd(e){return Math.max(0,Math.min(1,e||0))}function Xd(e,t,r){return 255*(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)}yd(vd,Dd,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Cd,formatHex:Cd,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Vd(this).formatHsl()},formatRgb:Td,toString:Td}),yd(Rd,$d,gd(vd,{brighter(e){return e=null==e?xd:Math.pow(xd,e),new Rd(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?bd:Math.pow(bd,e),new Rd(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Rd(Ud(this.r),Ud(this.g),Ud(this.b),Fd(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ld,formatHex:Ld,formatHex8:function(){return`#${qd(this.r)}${qd(this.g)}${qd(this.b)}${qd(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:zd,toString:zd})),yd(Gd,(function(e,t,r,n){return 1===arguments.length?Vd(e):new Gd(e,t,r,null==n?1:n)}),gd(vd,{brighter(e){return e=null==e?xd:Math.pow(xd,e),new Gd(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?bd:Math.pow(bd,e),new Gd(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Rd(Xd(e>=240?e-240:e+120,i,n),Xd(e,i,n),Xd(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Gd(Yd(this.h),Hd(this.s),Hd(this.l),Fd(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Fd(this.opacity);return`${1===e?"hsl(":"hsla("}${Yd(this.h)}, ${100*Hd(this.s)}%, ${100*Hd(this.l)}%${1===e?")":`, ${e})`}`}}));const Kd=e=>()=>e;function Zd(e,t){var r=t-e;return r?function(e,t){return function(r){return e+r*t}}(e,r):Kd(isNaN(e)?t:e)}const Jd=function e(t){var r=function(e){return 1===(e=+e)?Zd:function(t,r){return r-t?function(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}(t,r,e):Kd(isNaN(t)?r:t)}}(t);function n(e,t){var n=r((e=$d(e)).r,(t=$d(t)).r),i=r(e.g,t.g),a=r(e.b,t.b),o=Zd(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return n.gamma=e,n}(1);function Qd(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}}function ep(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=new Array(i),o=new Array(n);for(r=0;r<i;++r)a[r]=sp(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}function tp(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function rp(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function np(e,t){var r,n={},i={};for(r in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)r in e?n[r]=sp(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}var ip=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ap=new RegExp(ip.source,"g");function op(e,t){var r,n,i,a=ip.lastIndex=ap.lastIndex=0,o=-1,s=[],c=[];for(e+="",t+="";(r=ip.exec(e))&&(n=ap.exec(t));)(i=n.index)>a&&(i=t.slice(a,i),s[o]?s[o]+=i:s[++o]=i),(r=r[0])===(n=n[0])?s[o]?s[o]+=n:s[++o]=n:(s[++o]=null,c.push({i:o,x:rp(r,n)})),a=ap.lastIndex;return a<t.length&&(i=t.slice(a),s[o]?s[o]+=i:s[++o]=i),s.length<2?c[0]?function(e){return function(t){return e(t)+""}}(c[0].x):function(e){return function(){return e}}(t):(t=c.length,function(e){for(var r,n=0;n<t;++n)s[(r=c[n]).i]=r.x(e);return s.join("")})}function sp(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?Kd(t):("number"===i?rp:"string"===i?(r=Dd(t))?(t=r,Jd):op:t instanceof Dd?Jd:t instanceof Date?tp:(n=t,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(t)?ep:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?np:rp:Qd))(e,t)}function cp(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function lp(e){return+e}var up=[0,1];function fp(e){return e}function dp(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r});var r}function pp(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=dp(i,n),a=r(o,a)):(n=dp(n,i),a=r(a,o)),function(e){return a(n(e))}}function hp(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=dp(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=Gf(e,t,1,n)-1;return a[r](i[r](t))}}function mp(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function yp(){var e,t,r,n,i,a,o=up,s=up,c=sp,l=fp;function u(){var e,t,r,c=Math.min(o.length,s.length);return l!==fp&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),l=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?hp:pp,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),s,c)))(e(l(t)))}return f.invert=function(r){return l(t((a||(a=n(s,o.map(e),rp)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,lp),u()):o.slice()},f.range=function(e){return arguments.length?(s=Array.from(e),u()):s.slice()},f.rangeRound=function(e){return s=Array.from(e),c=cp,u()},f.clamp=function(e){return arguments.length?(l=!!e||fp,u()):l!==fp},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function gp(){return yp()(fp,fp)}function vp(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function bp(e){return(e=vp(Math.abs(e)))?e[1]:NaN}var xp,wp=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function jp(e){if(!(t=wp.exec(e)))throw new Error("invalid format: "+e);var t;return new Op({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function Op(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function kp(e,t){var r=vp(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}jp.prototype=Op.prototype,Op.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Sp={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>kp(100*e,t),r:kp,s:function(e,t){var r=vp(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(xp=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+vp(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Pp(e){return e}var Ap,Np,Ep,Mp=Array.prototype.map,_p=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Cp(e,t,r,n){var i,a=nd(e,t,r);switch((n=jp(null==n?",f":n)).type){case"s":var o=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(bp(t)/3)))-bp(Math.abs(e)))}(a,o))||(n.precision=i),Ep(n,o);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,bp(t)-bp(e))+1}(a,Math.max(Math.abs(e),Math.abs(t))))||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=function(e){return Math.max(0,-bp(Math.abs(e)))}(a))||(n.precision=i-2*("%"===n.type))}return Np(n)}function Tp(e){var t=e.domain;return e.ticks=function(e){var r=t();return td(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return Cp(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,s=a.length-1,c=a[o],l=a[s],u=10;for(l<c&&(i=c,c=l,l=i,i=o,o=s,s=i);u-- >0;){if((i=rd(c,l,r))===n)return a[o]=c,a[s]=l,t(a);if(i>0)c=Math.floor(c/i)*i,l=Math.ceil(l/i)*i;else{if(!(i<0))break;c=Math.ceil(c*i)/i,l=Math.floor(l*i)/i}n=i}return e},e}function Dp(){var e=gp();return e.copy=function(){return mp(e,Dp())},ld.apply(e,arguments),Tp(e)}function Ip(e,t){var r,n=0,i=(e=e.slice()).length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function Bp(e){return Math.log(e)}function $p(e){return Math.exp(e)}function Rp(e){return-Math.log(-e)}function Lp(e){return-Math.exp(-e)}function zp(e){return isFinite(e)?+("1e"+e):e<0?0:e}function Fp(e){return(t,r)=>-e(-t,r)}function Up(e){const t=e(Bp,$p),r=t.domain;let n,i,a=10;function o(){return n=function(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}(a),i=function(e){return 10===e?zp:e===Math.E?Math.exp:t=>Math.pow(e,t)}(a),r()[0]<0?(n=Fp(n),i=Fp(i),e(Rp,Lp)):e(Bp,$p),t}return t.base=function(e){return arguments.length?(a=+e,o()):a},t.domain=function(e){return arguments.length?(r(e),o()):r()},t.ticks=e=>{const t=r();let o=t[0],s=t[t.length-1];const c=s<o;c&&([o,s]=[s,o]);let l,u,f=n(o),d=n(s);const p=null==e?10:+e;let h=[];if(!(a%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),o>0){for(;f<=d;++f)for(l=1;l<a;++l)if(u=f<0?l/i(-f):l*i(f),!(u<o)){if(u>s)break;h.push(u)}}else for(;f<=d;++f)for(l=a-1;l>=1;--l)if(u=f>0?l/i(-f):l*i(f),!(u<o)){if(u>s)break;h.push(u)}2*h.length<p&&(h=td(o,s,p))}else h=td(f,d,Math.min(d-f,p)).map(i);return c?h.reverse():h},t.tickFormat=(e,r)=>{if(null==e&&(e=10),null==r&&(r=10===a?"s":","),"function"!=typeof r&&(a%1||null!=(r=jp(r)).precision||(r.trim=!0),r=Np(r)),e===1/0)return r;const o=Math.max(1,a*e/t.ticks().length);return e=>{let t=e/i(Math.round(n(e)));return t*a<a-.5&&(t*=a),t<=o?r(e):""}},t.nice=()=>r(Ip(r(),{floor:e=>i(Math.floor(n(e))),ceil:e=>i(Math.ceil(n(e)))})),t}function qp(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Wp(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Vp(e){var t=1,r=e(qp(t),Wp(t));return r.constant=function(r){return arguments.length?e(qp(t=+r),Wp(t)):t},Tp(r)}function Gp(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function Yp(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function Hp(e){return e<0?-e*e:e*e}function Xp(e){var t=e(fp,fp),r=1;return t.exponent=function(t){return arguments.length?1===(r=+t)?e(fp,fp):.5===r?e(Yp,Hp):e(Gp(r),Gp(1/r)):r},Tp(t)}function Kp(){var e=Xp(yp());return e.copy=function(){return mp(e,Kp()).exponent(e.exponent())},ld.apply(e,arguments),e}function Zp(e){return Math.sign(e)*e*e}Ap=function(e){var t,r,n=void 0===e.grouping||void 0===e.thousands?Pp:(t=Mp.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,s=t[0],c=0;i>0&&s>0&&(c+s+1>n&&(s=Math.max(1,n-c)),a.push(e.substring(i-=s,i+s)),!((c+=s+1)>n));)s=t[o=(o+1)%t.length];return a.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",o=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?Pp:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(Mp.call(e.numerals,String)),c=void 0===e.percent?"%":e.percent+"",l=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=jp(e)).fill,r=e.align,f=e.sign,d=e.symbol,p=e.zero,h=e.width,m=e.comma,y=e.precision,g=e.trim,v=e.type;"n"===v?(m=!0,v="g"):Sp[v]||(void 0===y&&(y=12),g=!0,v="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(v)?"0"+v.toLowerCase():"",x="$"===d?a:/[%p]/.test(v)?c:"",w=Sp[v],j=/[defgprs%]/.test(v);function O(e){var i,a,c,d=b,O=x;if("c"===v)O=w(e)+O,e="";else{var k=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),y),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),k&&0===+e&&"+"!==f&&(k=!1),d=(k?"("===f?f:l:"-"===f||"("===f?"":f)+d,O=("s"===v?_p[8+xp/3]:"")+O+(k&&"("===f?")":""),j)for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){O=(46===c?o+e.slice(i+1):e.slice(i))+O,e=e.slice(0,i);break}}m&&!p&&(e=n(e,1/0));var S=d.length+e.length+O.length,P=S<h?new Array(h-S+1).join(t):"";switch(m&&p&&(e=n(P+e,P.length?h-O.length:1/0),P=""),r){case"<":e=d+e+O+P;break;case"=":e=d+P+e+O;break;case"^":e=P.slice(0,S=P.length>>1)+d+e+O+P.slice(S);break;default:e=P+d+e+O}return s(e)}return y=void 0===y?6:/[gprs]/.test(v)?Math.max(1,Math.min(21,y)):Math.max(0,Math.min(20,y)),O.toString=function(){return e+""},O}return{format:f,formatPrefix:function(e,t){var r=f(((e=jp(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(bp(t)/3))),i=Math.pow(10,-n),a=_p[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]}),Np=Ap.format,Ep=Ap.formatPrefix;const Jp=new Date,Qp=new Date;function eh(e,t,r,n){function i(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{const t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{const o=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n&&a>0))return o;let s;do{o.push(s=new Date(+r)),t(r,a),e(r)}while(s<r&&r<n);return o},i.filter=r=>eh((t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)}),((e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););})),r&&(i.count=(t,n)=>(Jp.setTime(+t),Qp.setTime(+n),e(Jp),e(Qp),Math.floor(r(Jp,Qp))),i.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?i.filter(n?t=>n(t)%e===0:t=>i.count(0,t)%e===0):i:null)),i}const th=eh((()=>{}),((e,t)=>{e.setTime(+e+t)}),((e,t)=>t-e));th.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?eh((t=>{t.setTime(Math.floor(t/e)*e)}),((t,r)=>{t.setTime(+t+r*e)}),((t,r)=>(r-t)/e)):th:null),th.range;const rh=1e3,nh=6e4,ih=36e5,ah=864e5,oh=6048e5,sh=31536e6,ch=eh((e=>{e.setTime(e-e.getMilliseconds())}),((e,t)=>{e.setTime(+e+t*rh)}),((e,t)=>(t-e)/rh),(e=>e.getUTCSeconds()));ch.range;const lh=eh((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*rh)}),((e,t)=>{e.setTime(+e+t*nh)}),((e,t)=>(t-e)/nh),(e=>e.getMinutes()));lh.range;const uh=eh((e=>{e.setUTCSeconds(0,0)}),((e,t)=>{e.setTime(+e+t*nh)}),((e,t)=>(t-e)/nh),(e=>e.getUTCMinutes()));uh.range;const fh=eh((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*rh-e.getMinutes()*nh)}),((e,t)=>{e.setTime(+e+t*ih)}),((e,t)=>(t-e)/ih),(e=>e.getHours()));fh.range;const dh=eh((e=>{e.setUTCMinutes(0,0,0)}),((e,t)=>{e.setTime(+e+t*ih)}),((e,t)=>(t-e)/ih),(e=>e.getUTCHours()));dh.range;const ph=eh((e=>e.setHours(0,0,0,0)),((e,t)=>e.setDate(e.getDate()+t)),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*nh)/ah),(e=>e.getDate()-1));ph.range;const hh=eh((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/ah),(e=>e.getUTCDate()-1));hh.range;const mh=eh((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/ah),(e=>Math.floor(e/ah)));function yh(e){return eh((t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)}),((e,t)=>{e.setDate(e.getDate()+7*t)}),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*nh)/oh))}mh.range;const gh=yh(0),vh=yh(1),bh=yh(2),xh=yh(3),wh=yh(4),jh=yh(5),Oh=yh(6);function kh(e){return eh((t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)}),((e,t)=>(t-e)/oh))}gh.range,vh.range,bh.range,xh.range,wh.range,jh.range,Oh.range;const Sh=kh(0),Ph=kh(1),Ah=kh(2),Nh=kh(3),Eh=kh(4),Mh=kh(5),_h=kh(6);Sh.range,Ph.range,Ah.range,Nh.range,Eh.range,Mh.range,_h.range;const Ch=eh((e=>{e.setDate(1),e.setHours(0,0,0,0)}),((e,t)=>{e.setMonth(e.getMonth()+t)}),((e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear())),(e=>e.getMonth()));Ch.range;const Th=eh((e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)}),((e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear())),(e=>e.getUTCMonth()));Th.range;const Dh=eh((e=>{e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,t)=>{e.setFullYear(e.getFullYear()+t)}),((e,t)=>t.getFullYear()-e.getFullYear()),(e=>e.getFullYear()));Dh.every=e=>isFinite(e=Math.floor(e))&&e>0?eh((t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,r)=>{t.setFullYear(t.getFullYear()+r*e)})):null,Dh.range;const Ih=eh((e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)}),((e,t)=>t.getUTCFullYear()-e.getUTCFullYear()),(e=>e.getUTCFullYear()));function Bh(e,t,r,n,i,a){const o=[[ch,1,rh],[ch,5,5e3],[ch,15,15e3],[ch,30,3e4],[a,1,nh],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,ih],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,ah],[n,2,1728e5],[r,1,oh],[t,1,2592e6],[t,3,7776e6],[e,1,sh]];function s(t,r,n){const i=Math.abs(r-t)/n,a=qf((([,,e])=>e)).right(o,i);if(a===o.length)return e.every(nd(t/sh,r/sh,n));if(0===a)return th.every(Math.max(nd(t,r,n),1));const[s,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return s.every(c)}return[function(e,t,r){const n=t<e;n&&([e,t]=[t,e]);const i=r&&"function"==typeof r.range?r:s(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},s]}Ih.every=e=>isFinite(e=Math.floor(e))&&e>0?eh((t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)})):null,Ih.range;const[$h,Rh]=Bh(Ih,Th,Sh,mh,dh,uh),[Lh,zh]=Bh(Dh,Ch,gh,ph,fh,lh);function Fh(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Uh(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function qh(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var Wh,Vh,Gh,Yh={"-":"",_:" ",0:"0"},Hh=/^\s*\d+/,Xh=/^%/,Kh=/[\\^$*+?|[\]().{}]/g;function Zh(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Jh(e){return e.replace(Kh,"\\$&")}function Qh(e){return new RegExp("^(?:"+e.map(Jh).join("|")+")","i")}function em(e){return new Map(e.map(((e,t)=>[e.toLowerCase(),t])))}function tm(e,t,r){var n=Hh.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function rm(e,t,r){var n=Hh.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function nm(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function im(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function am(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function om(e,t,r){var n=Hh.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function sm(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function cm(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function lm(e,t,r){var n=Hh.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function um(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function fm(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function dm(e,t,r){var n=Hh.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function pm(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function hm(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function mm(e,t,r){var n=Hh.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function ym(e,t,r){var n=Hh.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function gm(e,t,r){var n=Hh.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function vm(e,t,r){var n=Xh.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function bm(e,t,r){var n=Hh.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function xm(e,t,r){var n=Hh.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function wm(e,t){return Zh(e.getDate(),t,2)}function jm(e,t){return Zh(e.getHours(),t,2)}function Om(e,t){return Zh(e.getHours()%12||12,t,2)}function km(e,t){return Zh(1+ph.count(Dh(e),e),t,3)}function Sm(e,t){return Zh(e.getMilliseconds(),t,3)}function Pm(e,t){return Sm(e,t)+"000"}function Am(e,t){return Zh(e.getMonth()+1,t,2)}function Nm(e,t){return Zh(e.getMinutes(),t,2)}function Em(e,t){return Zh(e.getSeconds(),t,2)}function Mm(e){var t=e.getDay();return 0===t?7:t}function _m(e,t){return Zh(gh.count(Dh(e)-1,e),t,2)}function Cm(e){var t=e.getDay();return t>=4||0===t?wh(e):wh.ceil(e)}function Tm(e,t){return e=Cm(e),Zh(wh.count(Dh(e),e)+(4===Dh(e).getDay()),t,2)}function Dm(e){return e.getDay()}function Im(e,t){return Zh(vh.count(Dh(e)-1,e),t,2)}function Bm(e,t){return Zh(e.getFullYear()%100,t,2)}function $m(e,t){return Zh((e=Cm(e)).getFullYear()%100,t,2)}function Rm(e,t){return Zh(e.getFullYear()%1e4,t,4)}function Lm(e,t){var r=e.getDay();return Zh((e=r>=4||0===r?wh(e):wh.ceil(e)).getFullYear()%1e4,t,4)}function zm(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Zh(t/60|0,"0",2)+Zh(t%60,"0",2)}function Fm(e,t){return Zh(e.getUTCDate(),t,2)}function Um(e,t){return Zh(e.getUTCHours(),t,2)}function qm(e,t){return Zh(e.getUTCHours()%12||12,t,2)}function Wm(e,t){return Zh(1+hh.count(Ih(e),e),t,3)}function Vm(e,t){return Zh(e.getUTCMilliseconds(),t,3)}function Gm(e,t){return Vm(e,t)+"000"}function Ym(e,t){return Zh(e.getUTCMonth()+1,t,2)}function Hm(e,t){return Zh(e.getUTCMinutes(),t,2)}function Xm(e,t){return Zh(e.getUTCSeconds(),t,2)}function Km(e){var t=e.getUTCDay();return 0===t?7:t}function Zm(e,t){return Zh(Sh.count(Ih(e)-1,e),t,2)}function Jm(e){var t=e.getUTCDay();return t>=4||0===t?Eh(e):Eh.ceil(e)}function Qm(e,t){return e=Jm(e),Zh(Eh.count(Ih(e),e)+(4===Ih(e).getUTCDay()),t,2)}function ey(e){return e.getUTCDay()}function ty(e,t){return Zh(Ph.count(Ih(e)-1,e),t,2)}function ry(e,t){return Zh(e.getUTCFullYear()%100,t,2)}function ny(e,t){return Zh((e=Jm(e)).getUTCFullYear()%100,t,2)}function iy(e,t){return Zh(e.getUTCFullYear()%1e4,t,4)}function ay(e,t){var r=e.getUTCDay();return Zh((e=r>=4||0===r?Eh(e):Eh.ceil(e)).getUTCFullYear()%1e4,t,4)}function oy(){return"+0000"}function sy(){return"%"}function cy(e){return+e}function ly(e){return Math.floor(+e/1e3)}function uy(e){return new Date(e)}function fy(e){return e instanceof Date?+e:+new Date(+e)}function dy(e,t,r,n,i,a,o,s,c,l){var u=gp(),f=u.invert,d=u.domain,p=l(".%L"),h=l(":%S"),m=l("%I:%M"),y=l("%I %p"),g=l("%a %d"),v=l("%b %d"),b=l("%B"),x=l("%Y");function w(e){return(c(e)<e?p:s(e)<e?h:o(e)<e?m:a(e)<e?y:n(e)<e?i(e)<e?g:v:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,fy)):d().map(uy)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:l(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(Ip(r,e)):u},u.copy=function(){return mp(u,dy(e,t,r,n,i,a,o,s,c,l))},u}function py(){var e,t,r,n,i,a=0,o=1,s=fp,c=!1;function l(t){return null==t||isNaN(t=+t)?i:s(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,s=e(r,n),l):[s(0),s(1)]}}return l.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),l):[a,o]},l.clamp=function(e){return arguments.length?(c=!!e,l):c},l.interpolator=function(e){return arguments.length?(s=e,l):s},l.range=u(sp),l.rangeRound=u(cp),l.unknown=function(e){return arguments.length?(i=e,l):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),l}}function hy(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function my(){var e=Xp(py());return e.copy=function(){return hy(e,my()).exponent(e.exponent())},ud.apply(e,arguments)}function yy(){var e,t,r,n,i,a,o,s=0,c=.5,l=1,u=1,f=fp,d=!1;function p(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(u*e<u*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=sp);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([s,c,l]=o,e=a(s=+s),t=a(c=+c),r=a(l=+l),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,p):[s,c,l]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(sp),p.rangeRound=h(cp),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(s),t=o(c),r=o(l),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,p}}function gy(){var e=Xp(yy());return e.copy=function(){return hy(e,gy()).exponent(e.exponent())},ud.apply(e,arguments)}Wh=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,s=e.months,c=e.shortMonths,l=Qh(i),u=em(i),f=Qh(a),d=em(a),p=Qh(o),h=em(o),m=Qh(s),y=em(s),g=Qh(c),v=em(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return s[e.getMonth()]},c:null,d:wm,e:wm,f:Pm,g:$m,G:Lm,H:jm,I:Om,j:km,L:Sm,m:Am,M:Nm,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:cy,s:ly,S:Em,u:Mm,U:_m,V:Tm,w:Dm,W:Im,x:null,X:null,y:Bm,Y:Rm,Z:zm,"%":sy},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return s[e.getUTCMonth()]},c:null,d:Fm,e:Fm,f:Gm,g:ny,G:ay,H:Um,I:qm,j:Wm,L:Vm,m:Ym,M:Hm,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:cy,s:ly,S:Xm,u:Km,U:Zm,V:Qm,w:ey,W:ty,x:null,X:null,y:ry,Y:iy,Z:oy,"%":sy},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=y.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return k(e,t,r,n)},d:fm,e:fm,f:gm,g:sm,G:om,H:pm,I:pm,j:dm,L:ym,m:um,M:hm,p:function(e,t,r){var n=l.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:lm,Q:bm,s:xm,S:mm,u:rm,U:nm,V:im,w:tm,W:am,x:function(e,t,n){return k(e,r,t,n)},X:function(e,t,r){return k(e,n,t,r)},y:sm,Y:om,Z:cm,"%":vm};function j(e,t){return function(r){var n,i,a,o=[],s=-1,c=0,l=e.length;for(r instanceof Date||(r=new Date(+r));++s<l;)37===e.charCodeAt(s)&&(o.push(e.slice(c,s)),null!=(i=Yh[n=e.charAt(++s)])?n=e.charAt(++s):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=s+1);return o.push(e.slice(c,s)),o.join("")}}function O(e,t){return function(r){var n,i,a=qh(1900,void 0,1);if(k(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(t&&!("Z"in a)&&(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(i=(n=Uh(qh(a.y,0,1))).getUTCDay(),n=i>4||0===i?Ph.ceil(n):Ph(n),n=hh.offset(n,7*(a.V-1)),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(i=(n=Fh(qh(a.y,0,1))).getDay(),n=i>4||0===i?vh.ceil(n):vh(n),n=ph.offset(n,7*(a.V-1)),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?Uh(qh(a.y,0,1)).getUTCDay():Fh(qh(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,Uh(a)):Fh(a)}}function k(e,t,r,n){for(var i,a,o=0,s=t.length,c=r.length;o<s;){if(n>=c)return-1;if(37===(i=t.charCodeAt(o++))){if(i=t.charAt(o++),!(a=w[i in Yh?t.charAt(o++):i])||(n=a(e,r,n))<0)return-1}else if(i!=r.charCodeAt(n++))return-1}return n}return b.x=j(r,b),b.X=j(n,b),b.c=j(t,b),x.x=j(r,x),x.X=j(n,x),x.c=j(t,x),{format:function(e){var t=j(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=O(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=j(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=O(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),Vh=Wh.format,Wh.parse,Gh=Wh.utcFormat,Wh.utcParse;const vy=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:pd,scaleDiverging:function e(){var t=Tp(yy()(fp));return t.copy=function(){return hy(t,e())},ud.apply(t,arguments)},scaleDivergingLog:function e(){var t=Up(yy()).domain([.1,1,10]);return t.copy=function(){return hy(t,e()).base(t.base())},ud.apply(t,arguments)},scaleDivergingPow:gy,scaleDivergingSqrt:function(){return gy.apply(null,arguments).exponent(.5)},scaleDivergingSymlog:function e(){var t=Vp(yy());return t.copy=function(){return hy(t,e()).constant(t.constant())},ud.apply(t,arguments)},scaleIdentity:function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,lp),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,lp):[0,1],Tp(n)},scaleImplicit:fd,scaleLinear:Dp,scaleLog:function e(){const t=Up(yp()).domain([1,10]);return t.copy=()=>mp(t,e()).base(t.base()),ld.apply(t,arguments),t},scaleOrdinal:dd,scalePoint:md,scalePow:Kp,scaleQuantile:function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=new Array(t-1);++e<t;)i[e-1]=cd(r,e/t);return o}function o(e){return null==e||isNaN(e=+e)?t:n[Gf(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();r=[];for(let t of e)null==t||isNaN(t=+t)||r.push(t);return r.sort(Ff),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},ld.apply(o,arguments)},scaleQuantize:function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function s(e){return null!=e&&e<=e?o[Gf(a,e,0,i)]:t}function c(){var e=-1;for(a=new Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return s}return s.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,c()):[r,n]},s.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},s.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},s.unknown=function(e){return arguments.length?(t=e,s):s},s.thresholds=function(){return a.slice()},s.copy=function(){return e().domain([r,n]).range(o).unknown(t)},ld.apply(Tp(s),arguments)},scaleRadial:function e(){var t,r=gp(),n=[0,1],i=!1;function a(e){var n=function(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}(r(e));return isNaN(n)?t:i?Math.round(n):n}return a.invert=function(e){return r.invert(Zp(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,lp)).map(Zp)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},ld.apply(a,arguments),Tp(a)},scaleSequential:function e(){var t=Tp(py()(fp));return t.copy=function(){return hy(t,e())},ud.apply(t,arguments)},scaleSequentialLog:function e(){var t=Up(py()).domain([1,10]);return t.copy=function(){return hy(t,e()).base(t.base())},ud.apply(t,arguments)},scaleSequentialPow:my,scaleSequentialQuantile:function e(){var t=[],r=fp;function n(e){if(null!=e&&!isNaN(e=+e))return r((Gf(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(Ff),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map(((e,n)=>r(n/(t.length-1))))},n.quantiles=function(e){return Array.from({length:e+1},((r,n)=>function(e,t){if((r=(e=Float64Array.from(function*(e){for(let t of e)null!=t&&(t=+t)>=t&&(yield t)}(e))).length)&&!isNaN(t=+t)){if(t<=0||r<2)return ad(e);if(t>=1)return id(e);var r,n=(r-1)*t,i=Math.floor(n),a=id(od(e,i).subarray(0,i+1));return a+(ad(e.subarray(i+1))-a)*(n-i)}}(t,n/e)))},n.copy=function(){return e(r).domain(t)},ud.apply(n,arguments)},scaleSequentialSqrt:function(){return my.apply(null,arguments).exponent(.5)},scaleSequentialSymlog:function e(){var t=Vp(py());return t.copy=function(){return hy(t,e()).constant(t.constant())},ud.apply(t,arguments)},scaleSqrt:function(){return Kp.apply(null,arguments).exponent(.5)},scaleSymlog:function e(){var t=Vp(yp());return t.copy=function(){return mp(t,e()).constant(t.constant())},ld.apply(t,arguments)},scaleThreshold:function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[Gf(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(r=Array.from(e),i=Math.min(r.length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},ld.apply(a,arguments)},scaleTime:function(){return ld.apply(dy(Lh,zh,Dh,Ch,gh,ph,fh,lh,ch,Vh).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},scaleUtc:function(){return ld.apply(dy($h,Rh,Ih,Th,Sh,hh,dh,uh,ch,Gh).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},tickFormat:Cp},Symbol.toStringTag,{value:"Module"}));var by=et,xy=function(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(null!=o&&(void 0===s?o==o&&!by(o):r(o,s)))var s=o,c=a}return c},wy=xy,jy=function(e,t){return e>t},Oy=Ec;const ky=a((function(e){return e&&e.length?wy(e,Oy,jy):void 0}));var Sy=xy,Py=function(e,t){return e<t},Ay=Ec;const Ny=a((function(e){return e&&e.length?Sy(e,Ay,Py):void 0}));var Ey=gr,My=$c,_y=Al,Cy=Be,Ty=bl;const Dy=a((function(e,t){return Ty(function(e,t){return(Cy(e)?Ey:_y)(e,My(t))}(e,t),1)}));var Iy=oc;const By=a((function(e,t){return Iy(e,t)}));var $y,Ry=1e9,Ly=!0,zy="[DecimalError] ",Fy=zy+"Invalid argument: ",Uy=zy+"Exponent out of range: ",qy=Math.floor,Wy=Math.pow,Vy=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Gy=1e7,Yy=qy(1286742750677284.5),Hy={};function Xy(e,t){var r,n,i,a,o,s,c,l,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),Ly?ag(t,f):t;if(c=e.d,l=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,s=l.length):(n=l,i=o,s=c.length),a>(s=(o=Math.ceil(f/7))>s?o+1:s+1)&&(a=s,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((s=c.length)-(a=l.length)<0&&(a=s,n=l,l=c,c=n),r=0;a;)r=(c[--a]=c[a]+l[a]+r)/Gy|0,c[a]%=Gy;for(r&&(c.unshift(r),++i),s=c.length;0==c[--s];)c.pop();return t.d=c,t.e=i,Ly?ag(t,f):t}function Ky(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Fy+e)}function Zy(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=rg(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=rg(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}Hy.absoluteValue=Hy.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},Hy.comparedTo=Hy.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(t=0,r=(n=a.d.length)<(i=e.d.length)?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1},Hy.decimalPlaces=Hy.dp=function(){var e=this,t=e.d.length-1,r=7*(t-e.e);if(t=e.d[t])for(;t%10==0;t/=10)r--;return r<0?0:r},Hy.dividedBy=Hy.div=function(e){return Jy(this,new this.constructor(e))},Hy.dividedToIntegerBy=Hy.idiv=function(e){var t=this.constructor;return ag(Jy(this,new t(e),0,1),t.precision)},Hy.equals=Hy.eq=function(e){return!this.cmp(e)},Hy.exponent=function(){return eg(this)},Hy.greaterThan=Hy.gt=function(e){return this.cmp(e)>0},Hy.greaterThanOrEqualTo=Hy.gte=function(e){return this.cmp(e)>=0},Hy.isInteger=Hy.isint=function(){return this.e>this.d.length-2},Hy.isNegative=Hy.isneg=function(){return this.s<0},Hy.isPositive=Hy.ispos=function(){return this.s>0},Hy.isZero=function(){return 0===this.s},Hy.lessThan=Hy.lt=function(e){return this.cmp(e)<0},Hy.lessThanOrEqualTo=Hy.lte=function(e){return this.cmp(e)<1},Hy.logarithm=Hy.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(void 0===e)e=new n(10);else if((e=new n(e)).s<1||e.eq($y))throw Error(zy+"NaN");if(r.s<1)throw Error(zy+(r.s?"NaN":"-Infinity"));return r.eq($y)?new n(0):(Ly=!1,t=Jy(ng(r,a),ng(e,a),a),Ly=!0,ag(t,i))},Hy.minus=Hy.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?og(t,e):Xy(t,(e.s=-e.s,e))},Hy.modulo=Hy.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(!(e=new n(e)).s)throw Error(zy+"NaN");return r.s?(Ly=!1,t=Jy(r,e,0,1).times(e),Ly=!0,r.minus(t)):ag(new n(r),i)},Hy.naturalExponential=Hy.exp=function(){return Qy(this)},Hy.naturalLogarithm=Hy.ln=function(){return ng(this)},Hy.negated=Hy.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},Hy.plus=Hy.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Xy(t,e):og(t,(e.s=-e.s,e))},Hy.precision=Hy.sd=function(e){var t,r,n,i=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(Fy+e);if(t=eg(i)+1,r=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},Hy.squareRoot=Hy.sqrt=function(){var e,t,r,n,i,a,o,s=this,c=s.constructor;if(s.s<1){if(!s.s)return new c(0);throw Error(zy+"NaN")}for(e=eg(s),Ly=!1,0==(i=Math.sqrt(+s))||i==1/0?(((t=Zy(s.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=qy((e+1)/2)-(e<0||e%2),n=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(Jy(s,a,o+2)).times(.5),Zy(a.d).slice(0,o)===(t=Zy(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(ag(a,r+1,0),a.times(a).eq(s)){n=a;break}}else if("9999"!=t)break;o+=4}return Ly=!0,ag(n,r)},Hy.times=Hy.mul=function(e){var t,r,n,i,a,o,s,c,l,u=this,f=u.constructor,d=u.d,p=(e=new f(e)).d;if(!u.s||!e.s)return new f(0);for(e.s*=u.s,r=u.e+e.e,(c=d.length)<(l=p.length)&&(a=d,d=p,p=a,o=c,c=l,l=o),a=[],n=o=c+l;n--;)a.push(0);for(n=l;--n>=0;){for(t=0,i=c+n;i>n;)s=a[i]+p[n]*d[i-n-1]+t,a[i--]=s%Gy|0,t=s/Gy|0;a[i]=(a[i]+t)%Gy|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,Ly?ag(e,f.precision):e},Hy.toDecimalPlaces=Hy.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(Ky(e,0,Ry),void 0===t?t=n.rounding:Ky(t,0,8),ag(r,e+eg(r)+1,t))},Hy.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=sg(n,!0):(Ky(e,0,Ry),void 0===t?t=i.rounding:Ky(t,0,8),r=sg(n=ag(new i(n),e+1,t),!0,e+1)),r},Hy.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?sg(i):(Ky(e,0,Ry),void 0===t?t=a.rounding:Ky(t,0,8),r=sg((n=ag(new a(i),e+eg(i)+1,t)).abs(),!1,e+eg(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)},Hy.toInteger=Hy.toint=function(){var e=this,t=e.constructor;return ag(new t(e),eg(e)+1,t.rounding)},Hy.toNumber=function(){return+this},Hy.toPower=Hy.pow=function(e){var t,r,n,i,a,o,s=this,c=s.constructor,l=+(e=new c(e));if(!e.s)return new c($y);if(!(s=new c(s)).s){if(e.s<1)throw Error(zy+"Infinity");return s}if(s.eq($y))return s;if(n=c.precision,e.eq($y))return ag(s,n);if(o=(t=e.e)>=(r=e.d.length-1),a=s.s,o){if((r=l<0?-l:l)<=9007199254740991){for(i=new c($y),t=Math.ceil(n/7+4),Ly=!1;r%2&&cg((i=i.times(s)).d,t),0!==(r=qy(r/2));)cg((s=s.times(s)).d,t);return Ly=!0,e.s<0?new c($y).div(i):ag(i,n)}}else if(a<0)throw Error(zy+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,Ly=!1,i=e.times(ng(s,n+12)),Ly=!0,(i=Qy(i)).s=a,i},Hy.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?n=sg(i,(r=eg(i))<=a.toExpNeg||r>=a.toExpPos):(Ky(e,1,Ry),void 0===t?t=a.rounding:Ky(t,0,8),n=sg(i=ag(new a(i),e,t),e<=(r=eg(i))||r<=a.toExpNeg,e)),n},Hy.toSignificantDigits=Hy.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(Ky(e,1,Ry),void 0===t?t=r.rounding:Ky(t,0,8)),ag(new r(this),e,t)},Hy.toString=Hy.valueOf=Hy.val=Hy.toJSON=Hy[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=eg(e),r=e.constructor;return sg(e,t<=r.toExpNeg||t>=r.toExpPos)};var Jy=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%Gy|0,n=r/Gy|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=n*Gy+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var s,c,l,u,f,d,p,h,m,y,g,v,b,x,w,j,O,k,S=n.constructor,P=n.s==i.s?1:-1,A=n.d,N=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(zy+"Division by zero");for(c=n.e-i.e,O=N.length,w=A.length,h=(p=new S(P)).d=[],l=0;N[l]==(A[l]||0);)++l;if(N[l]>(A[l]||0)&&--c,(v=null==a?a=S.precision:o?a+(eg(n)-eg(i))+1:a)<0)return new S(0);if(v=v/7+2|0,l=0,1==O)for(u=0,N=N[0],v++;(l<w||u)&&v--;l++)b=u*Gy+(A[l]||0),h[l]=b/N|0,u=b%N|0;else{for((u=Gy/(N[0]+1)|0)>1&&(N=e(N,u),A=e(A,u),O=N.length,w=A.length),x=O,y=(m=A.slice(0,O)).length;y<O;)m[y++]=0;(k=N.slice()).unshift(0),j=N[0],N[1]>=Gy/2&&++j;do{u=0,(s=t(N,m,O,y))<0?(g=m[0],O!=y&&(g=g*Gy+(m[1]||0)),(u=g/j|0)>1?(u>=Gy&&(u=Gy-1),1==(s=t(f=e(N,u),m,d=f.length,y=m.length))&&(u--,r(f,O<d?k:N,d))):(0==u&&(s=u=1),f=N.slice()),(d=f.length)<y&&f.unshift(0),r(m,f,y),-1==s&&(s=t(N,m,O,y=m.length))<1&&(u++,r(m,O<y?k:N,y)),y=m.length):0===s&&(u++,m=[0]),h[l++]=u,s&&m[0]?m[y++]=A[x]||0:(m=[A[x]],y=1)}while((x++<w||void 0!==m[0])&&v--)}return h[0]||h.shift(),p.e=c,ag(p,o?a+eg(p)+1:a)}}();function Qy(e,t){var r,n,i,a,o,s=0,c=0,l=e.constructor,u=l.precision;if(eg(e)>16)throw Error(Uy+eg(e));if(!e.s)return new l($y);for(Ly=!1,o=u,a=new l(.03125);e.abs().gte(.1);)e=e.times(a),c+=5;for(o+=Math.log(Wy(2,c))/Math.LN10*2+5|0,r=n=i=new l($y),l.precision=o;;){if(n=ag(n.times(e),o),r=r.times(++s),Zy((a=i.plus(Jy(n,r,o))).d).slice(0,o)===Zy(i.d).slice(0,o)){for(;c--;)i=ag(i.times(i),o);return l.precision=u,null==t?(Ly=!0,ag(i,u)):i}i=a}}function eg(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function tg(e,t,r){if(t>e.LN10.sd())throw Ly=!0,r&&(e.precision=r),Error(zy+"LN10 precision limit exceeded");return ag(new e(e.LN10),t)}function rg(e){for(var t="";e--;)t+="0";return t}function ng(e,t){var r,n,i,a,o,s,c,l,u,f=1,d=e,p=d.d,h=d.constructor,m=h.precision;if(d.s<1)throw Error(zy+(d.s?"NaN":"-Infinity"));if(d.eq($y))return new h(0);if(null==t?(Ly=!1,l=m):l=t,d.eq(10))return null==t&&(Ly=!0),tg(h,l);if(l+=10,h.precision=l,n=(r=Zy(p)).charAt(0),a=eg(d),!(Math.abs(a)<15e14))return c=tg(h,l+2,m).times(a+""),d=ng(new h(n+"."+r.slice(1)),l-10).plus(c),h.precision=m,null==t?(Ly=!0,ag(d,m)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=Zy((d=d.times(e)).d)).charAt(0),f++;for(a=eg(d),n>1?(d=new h("0."+r),a++):d=new h(n+"."+r.slice(1)),s=o=d=Jy(d.minus($y),d.plus($y),l),u=ag(d.times(d),l),i=3;;){if(o=ag(o.times(u),l),Zy((c=s.plus(Jy(o,new h(i),l))).d).slice(0,l)===Zy(s.d).slice(0,l))return s=s.times(2),0!==a&&(s=s.plus(tg(h,l+2,m).times(a+""))),s=Jy(s,new h(f),l),h.precision=m,null==t?(Ly=!0,ag(s,m)):s;s=c,i+=2}}function ig(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=qy(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),Ly&&(e.e>Yy||e.e<-Yy))throw Error(Uy+r)}else e.s=0,e.e=0,e.d=[0];return e}function ag(e,t,r){var n,i,a,o,s,c,l,u,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,l=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(a=f.length))return e;for(l=a=f[u],o=1;a>=10;a/=10)o++;i=(n%=7)-7+o}if(void 0!==r&&(s=l/(a=Wy(10,o-i-1))%10|0,c=t<0||void 0!==f[u+1]||l%a,c=r<4?(s||c)&&(0==r||r==(e.s<0?3:2)):s>5||5==s&&(4==r||c||6==r&&(n>0?i>0?l/Wy(10,o-i):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(a=eg(e),f.length=1,t=t-a-1,f[0]=Wy(10,(7-t%7)%7),e.e=qy(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,a=1,u--):(f.length=u+1,a=Wy(10,7-n),f[u]=i>0?(l/Wy(10,o-i)%Wy(10,i)|0)*a:0),c)for(;;){if(0==u){(f[0]+=a)==Gy&&(f[0]=1,++e.e);break}if(f[u]+=a,f[u]!=Gy)break;f[u--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(Ly&&(e.e>Yy||e.e<-Yy))throw Error(Uy+eg(e));return e}function og(e,t){var r,n,i,a,o,s,c,l,u,f,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),Ly?ag(t,p):t;if(c=e.d,f=t.d,n=t.e,l=e.e,c=c.slice(),o=l-n){for((u=o<0)?(r=c,o=-o,s=f.length):(r=f,n=l,s=c.length),o>(i=Math.max(Math.ceil(p/7),s)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((u=(i=c.length)<(s=f.length))&&(s=i),i=0;i<s;i++)if(c[i]!=f[i]){u=c[i]<f[i];break}o=0}for(u&&(r=c,c=f,f=r,t.s=-t.s),s=c.length,i=f.length-s;i>0;--i)c[s++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&0===c[--a];)c[a]=Gy-1;--c[a],c[i]+=Gy}c[i]-=f[i]}for(;0===c[--s];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,Ly?ag(t,p):t):new d(0)}function sg(e,t,r){var n,i=eg(e),a=Zy(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+rg(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+rg(-i-1)+a,r&&(n=r-o)>0&&(a+=rg(n))):i>=o?(a+=rg(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+rg(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=rg(n))),e.s<0?"-"+a:a}function cg(e,t){if(e.length>t)return e.length=t,!0}function lg(e){if(!e||"object"!=typeof e)throw Error(zy+"Object expected");var t,r,n,i=["precision",1,Ry,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(!(qy(n)===n&&n>=i[t+1]&&n<=i[t+2]))throw Error(Fy+r+": "+n);this[r]=n}if(void 0!==(n=e[r="LN10"])){if(n!=Math.LN10)throw Error(Fy+r+": "+n);this[r]=new this(n)}return this}var ug=function e(t){var r,n,i;function a(e){var t=this;if(!(t instanceof a))return new a(e);if(t.constructor=a,e instanceof a)return t.s=e.s,t.e=e.e,void(t.d=(e=e.d)?e.slice():e);if("number"==typeof e){if(0*e!=0)throw Error(Fy+e);if(e>0)t.s=1;else{if(!(e<0))return t.s=0,t.e=0,void(t.d=[0]);e=-e,t.s=-1}return e===~~e&&e<1e7?(t.e=0,void(t.d=[e])):ig(t,e.toString())}if("string"!=typeof e)throw Error(Fy+e);if(45===e.charCodeAt(0)?(e=e.slice(1),t.s=-1):t.s=1,!Vy.test(e))throw Error(Fy+e);ig(t,e)}if(a.prototype=Hy,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=lg,void 0===t&&(t={}),t)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});$y=new ug(1);const fg=ug;function dg(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var pg=function(e){return e},hg={},mg=function(e){return e===hg},yg=function(e){return function t(){return 0===arguments.length||1===arguments.length&&mg(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},gg=function e(t,r){return 1===t?r:yg((function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter((function(e){return e!==hg})).length;return o>=t?r.apply(void 0,i):e(t-o,yg((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a,o=i.map((function(e){return mg(e)?t.shift():e}));return r.apply(void 0,(a=o,function(e){if(Array.isArray(e))return dg(e)}(a)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(a)||function(e,t){if(e){if("string"==typeof e)return dg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?dg(e,t):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))})))}))},vg=function(e){return gg(e.length,e)},bg=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},xg=vg((function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map((function(e){return t[e]})).map(e)})),wg=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},jg=function(e){var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((function(e,r){return e===t[r]}))?r:(t=i,r=e.apply(void 0,i))}};const Og=function(e,t,r){for(var n=new fg(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a},kg=function(e){return 0===e?1:Math.floor(new fg(e).abs().log(10).toNumber())+1};vg((function(e,t,r){var n=+e;return n+r*(+t-n)})),vg((function(e,t,r){var n=t-+e;return(r-e)/(n||1/0)})),vg((function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))}));function Sg(e){return function(e){if(Array.isArray(e))return Ng(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||Ag(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pg(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(c){i=!0,a=c}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}}(e,t)||Ag(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ag(e,t){if(e){if("string"==typeof e)return Ng(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ng(e,t):void 0}}function Ng(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Eg(e){var t=Pg(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Mg(e,t,r){if(e.lte(0))return new fg(0);var n=kg(e.toNumber()),i=new fg(10).pow(n),a=e.div(i),o=1!==n?.05:.1,s=new fg(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return t?s:new fg(Math.ceil(s))}function _g(e,t,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new fg(0),tickMin:new fg(0),tickMax:new fg(0)};var a,o=Mg(new fg(t).sub(e).div(r-1),n,i);a=e<=0&&t>=0?new fg(0):(a=new fg(e).add(t).div(2)).sub(new fg(a).mod(o));var s=Math.ceil(a.sub(e).div(o).toNumber()),c=Math.ceil(new fg(t).sub(a).div(o).toNumber()),l=s+c+1;return l>r?_g(e,t,r,n,i+1):(l<r&&(c=t>0?c+(r-l):c,s=t>0?s:s+(r-l)),{step:o,tickMin:a.sub(new fg(s).mul(o)),tickMax:a.add(new fg(c).mul(o))})}var Cg=jg((function(e){var t=Pg(e,2),r=t[0],n=t[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=Math.max(i,2),s=Pg(Eg([r,n]),2),c=s[0],l=s[1];if(c===-1/0||l===1/0){var u=l===1/0?[c].concat(Sg(bg(0,i-1).map((function(){return 1/0})))):[].concat(Sg(bg(0,i-1).map((function(){return-1/0}))),[l]);return r>n?wg(u):u}if(c===l)return function(e,t,r){var n=1,i=new fg(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new fg(10).pow(kg(e)-1),i=new fg(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new fg(Math.floor(e)))}else 0===e?i=new fg(Math.floor((t-1)/2)):r||(i=new fg(Math.floor(e)));var o=Math.floor((t-1)/2),s=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return pg;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((function(e,t){return t(e)}),i.apply(void 0,arguments))}}(xg((function(e){return i.add(new fg(e-o).mul(n)).toNumber()})),bg);return s(0,t)}(c,i,a);var f=_g(c,l,o,a),d=f.step,p=f.tickMin,h=f.tickMax,m=Og(p,h.add(new fg(.1).mul(d)),d);return r>n?wg(m):m})),Tg=jg((function(e,t){var r=Pg(e,2),n=r[0],i=r[1],a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=Pg(Eg([n,i]),2),s=o[0],c=o[1];if(s===-1/0||c===1/0)return[n,i];if(s===c)return[s];var l=Math.max(t,2),u=Mg(new fg(c).sub(s).div(l-1),a,0),f=[].concat(Sg(Og(new fg(s),new fg(c).sub(new fg(.99).mul(u)),u)),[c]);return n>i?wg(f):f}));function Dg(e,t){throw new Error("Invariant failed")}var Ig=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Bg(e){return(Bg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $g(){return $g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$g.apply(this,arguments)}function Rg(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Lg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Lg=function(){return!!e})()}function zg(e){return(zg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Fg(e,t){return(Fg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Ug(e,t,r){return(t=qg(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qg(e){var t=function(e){if("object"!=Bg(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Bg(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Bg(t)?t:t+""}var Wg=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=zg(r=e),function(e,t){if(t&&("object"===Bg(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,Lg()?Reflect.construct(r,n||[],zg(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Fg(e,t)}(e,o.Component),t=e,(r=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,i=e.dataKey,a=e.data,s=e.dataPointFormatter,c=e.xAxis,l=e.yAxis,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ig),f=Yn(u,!1);"x"===this.props.direction&&"number"!==c.type&&Dg();var d=a.map((function(e){var a=s(e,i),u=a.x,d=a.y,p=a.value,h=a.errorVal;if(!h)return null;var m,y,g,v,b=[];if(Array.isArray(h)){var x=(v=2,function(e){if(Array.isArray(e))return e}(g=h)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(g,v)||function(e,t){if(e){if("string"==typeof e)return Rg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Rg(e,t):void 0}}(g,v)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());m=x[0],y=x[1]}else m=y=h;if("vertical"===r){var w=c.scale,j=d+t,O=j+n,k=j-n,S=w(p-m),P=w(p+y);b.push({x1:P,y1:O,x2:P,y2:k}),b.push({x1:S,y1:j,x2:P,y2:j}),b.push({x1:S,y1:O,x2:S,y2:k})}else if("horizontal"===r){var A=l.scale,N=u+t,E=N-n,M=N+n,_=A(p-m),C=A(p+y);b.push({x1:E,y1:C,x2:M,y2:C}),b.push({x1:N,y1:_,x2:N,y2:C}),b.push({x1:E,y1:_,x2:M,y2:_})}return o.createElement(ri,$g({className:"recharts-errorBar",key:"bar-".concat(b.map((function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)})))},f),b.map((function(e){return o.createElement("line",$g({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))})))}));return o.createElement(ri,{className:"recharts-errorBars"},d)}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,qg(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function Vg(e){return(Vg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Gg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Yg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Gg(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Vg(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Vg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Vg(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gg(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}Ug(Wg,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),Ug(Wg,"displayName","ErrorBar");var Hg=function(e){var t=e.children,r=e.formattedGraphicalItems,n=e.legendWidth,i=e.legendContent,a=Wn(t,ll);if(!a)return null;var o,s=ll.defaultProps,c=void 0!==s?Yg(Yg({},s),a.props):{};return o=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(r||[]).reduce((function(e,t){var r=t.item,n=t.props,i=n.sectors||n.data||[];return e.concat(i.map((function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}})))}),[]):(r||[]).map((function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?Yg(Yg({},r),t.props):{},i=n.dataKey,a=n.name,o=n.legendType;return{inactive:n.hide,dataKey:i,type:c.iconType||o||"square",color:nv(t),value:a||i,payload:n}})),Yg(Yg(Yg({},c),ll.getWithHeight(a,n)),{},{payload:o,item:a})};function Xg(e){return(Xg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Kg(e){return function(e){if(Array.isArray(e))return Zg(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Zg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Zg(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zg(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Jg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Jg(Object(r),!0).forEach((function(t){ev(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jg(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ev(e,t,r){var n;return n=function(e,t){if("object"!=Xg(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Xg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t,"string"),(t="symbol"==Xg(n)?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tv(e,t,r){return Rr(e)||Rr(t)?r:vn(t)?$r(e,t,r):ft(t)?t(e):r}function rv(e,t,r,n){var i=Dy(e,(function(e){return tv(e,t)}));if("number"===r){var a=i.filter((function(e){return gn(e)||parseFloat(e)}));return a.length?[Ny(a),ky(a)]:[1/0,-1/0]}return(n?i.filter((function(e){return!Rr(e)})):i).map((function(e){return vn(e)||e instanceof Date?e:""}))}var nv=function(e){var t,r,n=e.type.displayName,i=null!==(t=e.type)&&void 0!==t&&t.defaultProps?Qg(Qg({},e.type.defaultProps),e.props):e.props,a=i.stroke,o=i.fill;switch(n){case"Line":r=a;break;case"Area":case"Radar":r=a&&"none"!==a?a:o;break;default:r=o}return r},iv=function(e,t,r,n,i){var a=qn(t.props.children,Wg).filter((function(e){return function(e,t,r){return!!Rr(t)||("horizontal"===e?"yAxis"===t:"vertical"===e||"x"===r?"xAxis"===t:"y"!==r||"yAxis"===t)}(n,i,e.props.direction)}));if(a&&a.length){var o=a.map((function(e){return e.props.dataKey}));return e.reduce((function(e,t){var n=tv(t,r);if(Rr(n))return e;var i=Array.isArray(n)?[Ny(n),ky(n)]:[n,n],a=o.reduce((function(e,r){var n=tv(t,r,0),a=i[0]-Math.abs(Array.isArray(n)?n[0]:n),o=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(a,e[0]),Math.max(o,e[1])]}),[1/0,-1/0]);return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]}),[1/0,-1/0])}return null},av=function(e,t,r,n,i){var a=t.map((function(t){var a=t.props.dataKey;return"number"===r&&a&&iv(e,t,a,n)||rv(e,a,r,i)}));if("number"===r)return a.reduce((function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}),[1/0,-1/0]);var o={};return a.reduce((function(e,t){for(var r=0,n=t.length;r<n;r++)o[t[r]]||(o[t[r]]=!0,e.push(t[r]));return e}),[])},ov=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},sv=function(e,t,r,n){if(n)return e.map((function(e){return e.coordinate}));var i,a,o=e.map((function(e){return e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate}));return i||o.push(t),a||o.push(r),o},cv=function(e,t,r){if(!e)return null;var n=e.scale,i=e.duplicateDomain,a=e.type,o=e.range,s="scaleBand"===e.realScaleType?n.bandwidth()/2:2,c=(t||r)&&"category"===a&&n.bandwidth?n.bandwidth()/s:0;return c="angleAxis"===e.axisType&&(null==o?void 0:o.length)>=2?2*mn(o[0]-o[1])*c:c,t&&(e.ticks||e.niceTicks)?(e.ticks||e.niceTicks).map((function(e){var t=i?i.indexOf(e):e;return{coordinate:n(t)+c,value:e,offset:c}})).filter((function(e){return!hn(e.coordinate)})):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map((function(e,t){return{coordinate:n(e)+c,value:e,index:t,offset:c}})):n.ticks&&!r?n.ticks(e.tickCount).map((function(e){return{coordinate:n(e)+c,value:e,offset:c}})):n.domain().map((function(e,t){return{coordinate:n(e)+c,value:i?i[e]:e,index:t,offset:c}}))},lv=new WeakMap,uv=function(e,t){if("function"!=typeof t)return e;lv.has(e)||lv.set(e,new WeakMap);var r=lv.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},fv=1e-4,dv={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var s=hn(e[o][r][1])?e[o][r][0]:e[o][r][1];s>=0?(e[o][r][0]=i,e[o][r][1]=i+s,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+s,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}Sa(e,t)}},none:Sa,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,s=0;o<r;++o)s+=e[o][n][1]||0;i[n][1]+=i[n][0]=-s/2}Sa(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var s=0,c=0,l=0;s<i;++s){for(var u=e[t[s]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,p=0;p<s;++p){var h=e[t[p]];d+=(h[o][1]||0)-(h[o-1][1]||0)}c+=f,l+=d*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=l/c)}r[o-1][1]+=r[o-1][0]=a,Sa(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=hn(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},pv=function(e,t,r){var n=t.map((function(e){return e.props.dataKey})),i=dv[r],a=function(){var e=Si([]),t=Pa,r=Sa,n=Aa;function i(i){var a,o,s=Array.from(e.apply(this,arguments),Na),c=s.length,l=-1;for(const e of i)for(a=0,++l;a<c;++a)(s[a][l]=[0,+n(e,s[a].key,l,i)]).data=e;for(a=0,o=Ri(t(s));a<c;++a)s[o[a]].index=a;return r(s,o),s}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:Si(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:Si(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?Pa:"function"==typeof e?e:Si(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?Sa:e,i):r},i}().keys(n).value((function(e,t){return+tv(e,t,0)})).order(Pa).offset(i);return a(e)};function hv(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!Rr(i[t.dataKey])){var s=kn(r,"value",i[t.dataKey]);if(s)return s.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=tv(i,Rr(o)?t.dataKey:o);return Rr(c)?null:t.scale(c)}var mv=function(e){var t=e.axis,r=e.ticks,n=e.offset,i=e.bandSize,a=e.entry,o=e.index;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var s=tv(a,t.dataKey,t.domain[o]);return Rr(s)?null:t.scale(s)-i/2+n},yv=function(e,t,r){return Object.keys(e).reduce((function(n,i){var a=e[i].stackedData.reduce((function(e,n){var i=n.slice(t,r+1).reduce((function(e,t){return[Ny(t.concat([e[0]]).filter(gn)),ky(t.concat([e[1]]).filter(gn))]}),[1/0,-1/0]);return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]}),[1/0,-1/0]);return[Math.min(a[0],n[0]),Math.max(a[1],n[1])]}),[1/0,-1/0]).map((function(e){return e===1/0||e===-1/0?0:e}))},gv=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,vv=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,bv=function(e,t,r){if(ft(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(gn(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(gv.test(e[0])){var i=+gv.exec(e[0])[1];n[0]=t[0]-i}else ft(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(gn(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(vv.test(e[1])){var a=+vv.exec(e[1])[1];n[1]=t[1]+a}else ft(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},xv=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=ru(t,(function(e){return e.coordinate})),a=1/0,o=1,s=i.length;o<s;o++){var c=i[o],l=i[o-1];a=Math.min((c.coordinate||0)-(l.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0},wv=function(e,t,r){return e&&e.length?By(e,$r(r,"type.defaultProps.domain"))?t:e:t},jv=function(e,t){var r=e.type.defaultProps?Qg(Qg({},e.type.defaultProps),e.props):e.props,n=r.dataKey,i=r.name,a=r.unit,o=r.formatter,s=r.tooltipType,c=r.chartType,l=r.hide;return Qg(Qg({},Yn(e,!1)),{},{dataKey:n,unit:a,formatter:o,name:i||n,color:nv(e),value:tv(t,n),type:s,payload:t,chartType:c,hide:l})};function Ov(e){return(Ov="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function kv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Sv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?kv(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Ov(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Ov(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Ov(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Pv=Math.PI/180,Av=function(e){return 180*e/Math.PI},Nv=function(e,t,r,n){return{x:e+Math.cos(-Pv*n)*r,y:t+Math.sin(-Pv*n)*r}},Ev=function(e,t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360);return e+360*Math.min(i,a)},Mv=function(e,t){var r=function(e,t){var r=e.x,n=e.y,i=t.cx,a=t.cy,o=function(e,t){var r=e.x,n=e.y,i=t.x,a=t.y;return Math.sqrt(Math.pow(r-i,2)+Math.pow(n-a,2))}({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o};var s=(r-i)/o,c=Math.acos(s);return n>a&&(c=2*Math.PI-c),{radius:o,angle:Av(c),angleInRadian:c}}({x:e.x,y:e.y},t),n=r.radius,i=r.angle,a=t.innerRadius,o=t.outerRadius;if(n<a||n>o)return!1;if(0===n)return!0;var s,c=function(e){var t=e.startAngle,r=e.endAngle,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-360*a,endAngle:r-360*a}}(t),l=c.startAngle,u=c.endAngle,f=i;if(l<=u){for(;f>u;)f-=360;for(;f<l;)f+=360;s=f>=l&&f<=u}else{for(;f>l;)f-=360;for(;f<u;)f+=360;s=f>=u&&f<=l}return s?Sv(Sv({},t),{},{radius:n,angle:Ev(f,t)}):null};function _v(e){return(_v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Cv=["offset"];function Tv(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Dv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Iv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Dv(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=_v(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=_v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==_v(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Bv(){return Bv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bv.apply(this,arguments)}function $v(e){var t,r=e.offset,i=Iv({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Cv)),a=i.viewBox,s=i.position,c=i.value,l=i.children,u=i.content,f=i.className,d=void 0===f?"":f,p=i.textBreakAll;if(!a||Rr(c)&&Rr(l)&&!n.isValidElement(u)&&!ft(u))return null;if(n.isValidElement(u))return n.cloneElement(u,i);if(ft(u)){if(t=n.createElement(u,i),n.isValidElement(t))return t}else t=function(e){var t=e.value,r=e.formatter,n=Rr(e.children)?t:e.children;return ft(r)?r(n):n}(i);var h=function(e){return"cx"in e&&gn(e.cx)}(a),m=Yn(i,!0);if(h&&("insideStart"===s||"insideEnd"===s||"end"===s))return function(e,t,r){var n,i,a=e.position,s=e.viewBox,c=e.offset,l=e.className,u=s,f=u.cx,d=u.cy,p=u.innerRadius,h=u.outerRadius,m=u.startAngle,y=u.endAngle,g=u.clockWise,v=(p+h)/2,b=function(e,t){return mn(t-e)*Math.min(Math.abs(t-e),360)}(m,y),x=b>=0?1:-1;"insideStart"===a?(n=m+x*c,i=g):"insideEnd"===a?(n=y-x*c,i=!g):"end"===a&&(n=y+x*c,i=g),i=b<=0?i:!i;var w=Nv(f,d,v,n),j=Nv(f,d,v,n+359*(i?1:-1)),O="M".concat(w.x,",").concat(w.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(i?0:1,",\n    ").concat(j.x,",").concat(j.y),k=Rr(e.id)?xn("recharts-radial-line-"):e.id;return o.createElement("text",Bv({},r,{dominantBaseline:"central",className:P("recharts-radial-bar-label",l)}),o.createElement("defs",null,o.createElement("path",{id:k,d:O})),o.createElement("textPath",{xlinkHref:"#".concat(k)},t))}(i,t,m);var y=h?function(e){var t=e.viewBox,r=e.offset,n=e.position,i=t,a=i.cx,o=i.cy,s=i.innerRadius,c=i.outerRadius,l=(i.startAngle+i.endAngle)/2;if("outside"===n){var u=Nv(a,o,c+r,l),f=u.x;return{x:f,y:u.y,textAnchor:f>=a?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:a,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:a,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:a,y:o,textAnchor:"middle",verticalAnchor:"end"};var d=Nv(a,o,(s+c)/2,l);return{x:d.x,y:d.y,textAnchor:"middle",verticalAnchor:"middle"}}(i):function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,i=e.position,a=t,o=a.x,s=a.y,c=a.width,l=a.height,u=l>=0?1:-1,f=u*n,d=u>0?"end":"start",p=u>0?"start":"end",h=c>=0?1:-1,m=h*n,y=h>0?"end":"start",g=h>0?"start":"end";if("top"===i)return Iv(Iv({},{x:o+c/2,y:s-u*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(s-r.y,0),width:c}:{});if("bottom"===i)return Iv(Iv({},{x:o+c/2,y:s+l+f,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(s+l),0),width:c}:{});if("left"===i){var v={x:o-m,y:s+l/2,textAnchor:y,verticalAnchor:"middle"};return Iv(Iv({},v),r?{width:Math.max(v.x-r.x,0),height:l}:{})}if("right"===i){var b={x:o+c+m,y:s+l/2,textAnchor:g,verticalAnchor:"middle"};return Iv(Iv({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:l}:{})}var x=r?{width:c,height:l}:{};return"insideLeft"===i?Iv({x:o+m,y:s+l/2,textAnchor:g,verticalAnchor:"middle"},x):"insideRight"===i?Iv({x:o+c-m,y:s+l/2,textAnchor:y,verticalAnchor:"middle"},x):"insideTop"===i?Iv({x:o+c/2,y:s+f,textAnchor:"middle",verticalAnchor:p},x):"insideBottom"===i?Iv({x:o+c/2,y:s+l-f,textAnchor:"middle",verticalAnchor:d},x):"insideTopLeft"===i?Iv({x:o+m,y:s+f,textAnchor:g,verticalAnchor:p},x):"insideTopRight"===i?Iv({x:o+c-m,y:s+f,textAnchor:y,verticalAnchor:p},x):"insideBottomLeft"===i?Iv({x:o+m,y:s+l-f,textAnchor:g,verticalAnchor:d},x):"insideBottomRight"===i?Iv({x:o+c-m,y:s+l-f,textAnchor:y,verticalAnchor:d},x):st(i)&&(gn(i.x)||yn(i.x))&&(gn(i.y)||yn(i.y))?Iv({x:o+wn(i.x,c),y:s+wn(i.y,l),textAnchor:"end",verticalAnchor:"end"},x):Iv({x:o+c/2,y:s+l/2,textAnchor:"middle",verticalAnchor:"middle"},x)}(i);return o.createElement(zf,Bv({className:P("recharts-label",d)},m,y,{breakAll:p}),t)}$v.displayName="Label";var Rv=function(e){var t=e.cx,r=e.cy,n=e.angle,i=e.startAngle,a=e.endAngle,o=e.r,s=e.radius,c=e.innerRadius,l=e.outerRadius,u=e.x,f=e.y,d=e.top,p=e.left,h=e.width,m=e.height,y=e.clockWise,g=e.labelViewBox;if(g)return g;if(gn(h)&&gn(m)){if(gn(u)&&gn(f))return{x:u,y:f,width:h,height:m};if(gn(d)&&gn(p))return{x:d,y:p,width:h,height:m}}return gn(u)&&gn(f)?{x:u,y:f,width:0,height:0}:gn(t)&&gn(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:l||s||o||0,clockWise:y}:e.viewBox?e.viewBox:{}};$v.parseViewBox=Rv,$v.renderCallByParent=function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var i=e.children,a=Rv(e),s=qn(i,$v).map((function(e,r){return n.cloneElement(e,{viewBox:t||a,key:"label-".concat(r)})}));if(!r)return s;var c,l=function(e,t){return e?!0===e?o.createElement($v,{key:"label-implicit",viewBox:t}):vn(e)?o.createElement($v,{key:"label-implicit",viewBox:t,value:e}):n.isValidElement(e)?e.type===$v?n.cloneElement(e,{key:"label-implicit",viewBox:t}):o.createElement($v,{key:"label-implicit",content:e,viewBox:t}):ft(e)?o.createElement($v,{key:"label-implicit",content:e,viewBox:t}):st(e)?o.createElement($v,Bv({viewBox:t},e,{key:"label-implicit"})):null:null}(e.label,t||a);return[l].concat(function(e){if(Array.isArray(e))return Tv(e)}(c=s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(c)||function(e,t){if(e){if("string"==typeof e)return Tv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Tv(e,t):void 0}}(c)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};const Lv=a((function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}));function zv(e){return(zv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Fv=["valueAccessor"],Uv=["data","dataKey","clockWise","id","textBreakAll"];function qv(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Wv(){return Wv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wv.apply(this,arguments)}function Vv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Gv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Vv(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=zv(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=zv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==zv(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Yv(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Hv=function(e){return Array.isArray(e.value)?Lv(e.value):e.value};function Xv(e){var t=e.valueAccessor,r=void 0===t?Hv:t,n=Yv(e,Fv),i=n.data,a=n.dataKey,s=n.clockWise,c=n.id,l=n.textBreakAll,u=Yv(n,Uv);return i&&i.length?o.createElement(ri,{className:"recharts-label-list"},i.map((function(e,t){var n=Rr(a)?r(e,t):tv(e&&e.payload,a),i=Rr(c)?{}:{id:"".concat(c,"-").concat(t)};return o.createElement($v,Wv({},Yn(e,!0),u,i,{parentViewBox:e.parentViewBox,value:n,textBreakAll:l,viewBox:$v.parseViewBox(Rr(s)?e:Gv(Gv({},e),{},{clockWise:s})),key:"label-".concat(t),index:t}))}))):null}function Kv(e){return(Kv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Zv(){return Zv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zv.apply(this,arguments)}function Jv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Jv(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Kv(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Kv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Kv(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}Xv.displayName="LabelList",Xv.renderCallByParent=function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var i,a=qn(e.children,Xv).map((function(e,r){return n.cloneElement(e,{data:t,key:"labelList-".concat(r)})}));return r?[function(e,t){return e?!0===e?o.createElement(Xv,{key:"labelList-implicit",data:t}):o.isValidElement(e)||ft(e)?o.createElement(Xv,{key:"labelList-implicit",data:t,content:e}):st(e)?o.createElement(Xv,Wv({data:t},e,{key:"labelList-implicit"})):null:null}(e.label,t)].concat(function(e){if(Array.isArray(e))return qv(e)}(i=a)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return qv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?qv(e,t):void 0}}(i)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a};var eb=function(e){var t=e.cx,r=e.cy,n=e.radius,i=e.angle,a=e.sign,o=e.isExternal,s=e.cornerRadius,c=e.cornerIsExternal,l=s*(o?1:-1)+n,u=Math.asin(s/l)/Pv,f=c?i:i+a*u,d=c?i-a*u:i;return{center:Nv(t,r,l,f),circleTangency:Nv(t,r,n,f),lineTangency:Nv(t,r,l*Math.cos(u*Pv),d),theta:u}},tb=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,i=e.outerRadius,a=e.startAngle,o=function(e,t){return mn(t-e)*Math.min(Math.abs(t-e),359.999)}(a,e.endAngle),s=a+o,c=Nv(t,r,i,a),l=Nv(t,r,i,s),u="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(o)>180),",").concat(+(a>s),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var f=Nv(t,r,n,a),d=Nv(t,r,n,s);u+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(o)>180),",").concat(+(a<=s),",\n            ").concat(f.x,",").concat(f.y," Z")}else u+="L ".concat(t,",").concat(r," Z");return u},rb={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},nb=function(e){var t=Qv(Qv({},rb),e),r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,s=t.cornerRadius,c=t.forceCornerRadius,l=t.cornerIsExternal,u=t.startAngle,f=t.endAngle,d=t.className;if(a<i||u===f)return null;var p,h=P("recharts-sector",d),m=a-i,y=wn(s,m,0,!0);return p=y>0&&Math.abs(u-f)<360?function(e){var t=e.cx,r=e.cy,n=e.innerRadius,i=e.outerRadius,a=e.cornerRadius,o=e.forceCornerRadius,s=e.cornerIsExternal,c=e.startAngle,l=e.endAngle,u=mn(l-c),f=eb({cx:t,cy:r,radius:i,angle:c,sign:u,cornerRadius:a,cornerIsExternal:s}),d=f.circleTangency,p=f.lineTangency,h=f.theta,m=eb({cx:t,cy:r,radius:i,angle:l,sign:-u,cornerRadius:a,cornerIsExternal:s}),y=m.circleTangency,g=m.lineTangency,v=m.theta,b=s?Math.abs(c-l):Math.abs(c-l)-h-v;if(b<0)return o?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*-a,",0\n      "):tb({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:l});var x="M ".concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(b>180),",").concat(+(u<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(g.x,",").concat(g.y,"\n  ");if(n>0){var w=eb({cx:t,cy:r,radius:n,angle:c,sign:u,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),j=w.circleTangency,O=w.lineTangency,k=w.theta,S=eb({cx:t,cy:r,radius:n,angle:l,sign:-u,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),P=S.circleTangency,A=S.lineTangency,N=S.theta,E=s?Math.abs(c-l):Math.abs(c-l)-k-N;if(E<0&&0===a)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(A.x,",").concat(A.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(u>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x}({cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:Math.min(y,m/2),forceCornerRadius:c,cornerIsExternal:l,startAngle:u,endAngle:f}):tb({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:u,endAngle:f}),o.createElement("path",Zv({},Yn(t,!0),{className:h,d:p,role:"img"}))};function ib(e){return(ib="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ab(){return ab=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ab.apply(this,arguments)}function ob(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function sb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ob(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=ib(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=ib(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==ib(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ob(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var cb={curveBasisClosed:function(e){return new da(e)},curveBasisOpen:function(e){return new pa(e)},curveBasis:function(e){return new fa(e)},curveBumpX:function(e){return new Vi(e,!0)},curveBumpY:function(e){return new Vi(e,!1)},curveLinearClosed:function(e){return new ha(e)},curveLinear:zi,curveMonotoneX:function(e){return new ba(e)},curveMonotoneY:function(e){return new xa(e)},curveNatural:function(e){return new ja(e)},curveStep:function(e){return new ka(e,.5)},curveStepAfter:function(e){return new ka(e,1)},curveStepBefore:function(e){return new ka(e,0)}},lb=function(e){return e.x===+e.x&&e.y===+e.y},ub=function(e){return e.x},fb=function(e){return e.y},db=function(e){var t=e.className,r=e.points,n=e.path,i=e.pathRef;if(!(r&&r.length||n))return null;var a=r&&r.length?function(e){var t,r=e.type,n=void 0===r?"linear":r,i=e.points,a=void 0===i?[]:i,o=e.baseLine,s=e.layout,c=e.connectNulls,l=void 0!==c&&c,u=function(e,t){if(ft(e))return e;var r="curve".concat(ki(e));return"curveMonotone"!==r&&"curveBump"!==r||!t?cb[r]||zi:cb["".concat(r).concat("vertical"===t?"Y":"X")]}(n,s),f=l?a.filter((function(e){return lb(e)})):a;if(Array.isArray(o)){var d=l?o.filter((function(e){return lb(e)})):o,p=f.map((function(e,t){return sb(sb({},e),{},{base:d[t]})}));return(t="vertical"===s?Wi().y(fb).x1(ub).x0((function(e){return e.base.x})):Wi().x(ub).y1(fb).y0((function(e){return e.base.y}))).defined(lb).curve(u),t(p)}return(t="vertical"===s&&gn(o)?Wi().y(fb).x1(ub).x0(o):gn(o)?Wi().x(ub).y1(fb).y0(o):qi().x(ub).y(fb)).defined(lb).curve(u),t(f)}(e):n;return o.createElement("path",ab({},Yn(e,!1),Cn(e),{className:P("recharts-curve",t),d:a,ref:i}))},pb={exports:{}};function hb(){}function mb(){}mb.resetWarningCache=hb,pb.exports=function(){function e(e,t,r,n,i,a){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==a){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:mb,resetWarningCache:hb};return r.PropTypes=r,r}();const yb=a(pb.exports);var gb=Object.getOwnPropertyNames,vb=Object.getOwnPropertySymbols,bb=Object.prototype.hasOwnProperty;function xb(e,t){return function(r,n,i){return e(r,n,i)&&t(r,n,i)}}function wb(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var i=n.cache,a=i.get(t),o=i.get(r);if(a&&o)return a===r&&o===t;i.set(t,r),i.set(r,t);var s=e(t,r,n);return i.delete(t),i.delete(r),s}}function jb(e){return gb(e).concat(vb(e))}var Ob=Object.hasOwn||function(e,t){return bb.call(e,t)};function kb(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var Sb=Object.getOwnPropertyDescriptor,Pb=Object.keys;function Ab(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function Nb(e,t){return kb(e.getTime(),t.getTime())}function Eb(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function Mb(e,t){return e===t}function _b(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i,a,o=new Array(n),s=e.entries(),c=0;(i=s.next())&&!i.done;){for(var l=t.entries(),u=!1,f=0;(a=l.next())&&!a.done;)if(o[f])f++;else{var d=i.value,p=a.value;if(r.equals(d[0],p[0],c,f,e,t,r)&&r.equals(d[1],p[1],d[0],p[0],e,t,r)){u=o[f]=!0;break}f++}if(!u)return!1;c++}return!0}var Cb=kb;function Tb(e,t,r){var n=Pb(e),i=n.length;if(Pb(t).length!==i)return!1;for(;i-- >0;)if(!zb(e,t,r,n[i]))return!1;return!0}function Db(e,t,r){var n,i,a,o=jb(e),s=o.length;if(jb(t).length!==s)return!1;for(;s-- >0;){if(!zb(e,t,r,n=o[s]))return!1;if(i=Sb(e,n),a=Sb(t,n),(i||a)&&(!i||!a||i.configurable!==a.configurable||i.enumerable!==a.enumerable||i.writable!==a.writable))return!1}return!0}function Ib(e,t){return kb(e.valueOf(),t.valueOf())}function Bb(e,t){return e.source===t.source&&e.flags===t.flags}function $b(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i,a,o=new Array(n),s=e.values();(i=s.next())&&!i.done;){for(var c=t.values(),l=!1,u=0;(a=c.next())&&!a.done;){if(!o[u]&&r.equals(i.value,a.value,i.value,a.value,e,t,r)){l=o[u]=!0;break}u++}if(!l)return!1}return!0}function Rb(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function Lb(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function zb(e,t,r,n){return!("_owner"!==n&&"__o"!==n&&"__v"!==n||!e.$$typeof&&!t.$$typeof)||Ob(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var Fb=Array.isArray,Ub="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,qb=Object.assign,Wb=Object.prototype.toString.call.bind(Object.prototype.toString),Vb=Gb();function Gb(e){void 0===e&&(e={});var t,r=e.circular,n=void 0!==r&&r,i=e.createInternalComparator,a=e.createState,o=e.strict,s=void 0!==o&&o,c=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Db:Ab,areDatesEqual:Nb,areErrorsEqual:Eb,areFunctionsEqual:Mb,areMapsEqual:n?xb(_b,Db):_b,areNumbersEqual:Cb,areObjectsEqual:n?Db:Tb,arePrimitiveWrappersEqual:Ib,areRegExpsEqual:Bb,areSetsEqual:n?xb($b,Db):$b,areTypedArraysEqual:n?Db:Rb,areUrlsEqual:Lb};if(r&&(i=qb({},i,r(i))),t){var a=wb(i.areArraysEqual),o=wb(i.areMapsEqual),s=wb(i.areObjectsEqual),c=wb(i.areSetsEqual);i=qb({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:s,areSetsEqual:c})}return i}(e),l=function(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,s=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,l=e.areRegExpsEqual,u=e.areSetsEqual,f=e.areTypedArraysEqual,d=e.areUrlsEqual;return function(e,p,h){if(e===p)return!0;if(null==e||null==p)return!1;var m=typeof e;if(m!==typeof p)return!1;if("object"!==m)return"number"===m?o(e,p,h):"function"===m&&i(e,p,h);var y=e.constructor;if(y!==p.constructor)return!1;if(y===Object)return s(e,p,h);if(Fb(e))return t(e,p,h);if(null!=Ub&&Ub(e))return f(e,p,h);if(y===Date)return r(e,p,h);if(y===RegExp)return l(e,p,h);if(y===Map)return a(e,p,h);if(y===Set)return u(e,p,h);var g=Wb(e);return"[object Date]"===g?r(e,p,h):"[object RegExp]"===g?l(e,p,h):"[object Map]"===g?a(e,p,h):"[object Set]"===g?u(e,p,h):"[object Object]"===g?"function"!=typeof e.then&&"function"!=typeof p.then&&s(e,p,h):"[object URL]"===g?d(e,p,h):"[object Error]"===g?n(e,p,h):"[object Arguments]"===g?s(e,p,h):("[object Boolean]"===g||"[object Number]"===g||"[object String]"===g)&&c(e,p,h)}}(c);return function(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(e,o){var s=n(),c=s.cache,l=void 0===c?t?new WeakMap:void 0:c,u=s.meta;return r(e,o,{cache:l,equals:i,meta:u,strict:a})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(e,t){return r(e,t,o)}}({circular:n,comparator:l,createState:a,equals:i?i(l):(t=l,function(e,r,n,i,a,o,s){return t(e,r,s)}),strict:s})}function Yb(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame((function n(i){r<0&&(r=i),i-r>t?(e(i),r=-1):function(e){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(e)}(n)}))}function Hb(e){return(Hb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Xb(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Kb(){var e=function(){return null},t=!1,r=function r(n){if(!t){if(Array.isArray(n)){if(!n.length)return;var i=function(e){if(Array.isArray(e))return e}(s=n)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(s)||function(e,t){if(e){if("string"==typeof e)return Xb(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Xb(e,t):void 0}}(s)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],o=i.slice(1);return"number"==typeof a?void Yb(r.bind(null,o),a):(r(a),void Yb(r.bind(null,o)))}"object"===Hb(n)&&e(n),"function"==typeof n&&n()}var s};return{stop:function(){t=!0},start:function(e){t=!1,r(e)},subscribe:function(t){return e=t,function(){e=function(){return null}}}}}function Zb(e){return(Zb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Jb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Jb(Object(r),!0).forEach((function(t){ex(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jb(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ex(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Zb(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!==Zb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===Zb(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Gb({strict:!0}),Gb({circular:!0}),Gb({circular:!0,strict:!0}),Gb({createInternalComparator:function(){return kb}}),Gb({strict:!0,createInternalComparator:function(){return kb}}),Gb({circular:!0,createInternalComparator:function(){return kb}}),Gb({circular:!0,createInternalComparator:function(){return kb},strict:!0});var tx=function(e){return e},rx=function(e,t){return Object.keys(t).reduce((function(r,n){return Qb(Qb({},r),{},ex({},n,e(n,t[n])))}),{})},nx=function(e,t,r){return e.map((function(e){return"".concat((n=e,n.replace(/([A-Z])/g,(function(e){return"-".concat(e.toLowerCase())})))," ").concat(t,"ms ").concat(r);var n})).join(",")};function ix(e,t){if(e){if("string"==typeof e)return ax(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ax(e,t):void 0}}function ax(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ox=1e-4,sx=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},cx=function(e,t){return e.map((function(e,r){return e*Math.pow(t,r)})).reduce((function(e,t){return e+t}))},lx=function(e,t){return function(r){var n=sx(e,t);return cx(n,r)}},ux=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,i,a=t[0],o=t[1],s=t[2],c=t[3];if(1===t.length)switch(t[0]){case"linear":a=0,o=0,s=1,c=1;break;case"ease":a=.25,o=.1,s=.25,c=1;break;case"ease-in":a=.42,o=0,s=1,c=1;break;case"ease-out":a=.42,o=0,s=.58,c=1;break;case"ease-in-out":a=0,o=0,s=.58,c=1;break;default:var l=t[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var u=(n=l[1].split(")")[0].split(",").map((function(e){return parseFloat(e)})),i=4,function(e){if(Array.isArray(e))return e}(n)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(n,i)||ix(n,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());a=u[0],o=u[1],s=u[2],c=u[3]}}var f,d,p=lx(a,s),h=lx(o,c),m=(f=a,d=s,function(e){var t=sx(f,d),r=[].concat(function(e){return function(e){if(Array.isArray(e))return ax(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||ix(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t.map((function(e,t){return e*t})).slice(1)),[0]);return cx(r,e)}),y=function(e){for(var t,r=e>1?1:e,n=r,i=0;i<8;++i){var a=p(n)-r,o=m(n);if(Math.abs(a-r)<ox||o<ox)return h(n);n=(t=n-a/o)>1?1:t<0?0:t}return h(n)};return y.isStepper=!1,y};function fx(e){return(fx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dx(e){return function(e){if(Array.isArray(e))return gx(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||yx(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function px(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function hx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?px(Object(r),!0).forEach((function(t){mx(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):px(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function mx(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==fx(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!==fx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===fx(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yx(e,t){if(e){if("string"==typeof e)return gx(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gx(e,t):void 0}}function gx(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var vx=function(e,t,r){return e+(t-e)*r},bx=function(e){return e.from!==e.to},xx=function e(t,r,n){var i=rx((function(e,r){if(bx(r)){var n=(o=t(r.from,r.to,r.velocity),s=2,function(e){if(Array.isArray(e))return e}(o)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(o,s)||yx(o,s)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=n[0],a=n[1];return hx(hx({},r),{},{from:i,velocity:a})}var o,s;return r}),r);return n<1?rx((function(e,t){return bx(t)?hx(hx({},t),{},{velocity:vx(t.velocity,i[e].velocity,n),from:vx(t.from,i[e].from,n)}):t}),r):e(t,i,n-1)};function wx(e){return(wx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var jx=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function Ox(e){return function(e){if(Array.isArray(e))return kx(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return kx(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?kx(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kx(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Sx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Px(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sx(Object(r),!0).forEach((function(t){Ax(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sx(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ax(e,t,r){return(t=Nx(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nx(e){var t=function(e,t){if("object"!==wx(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!==wx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===wx(t)?t:String(t)}function Ex(e,t){return(Ex=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Mx(e,t){if(t&&("object"===wx(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _x(e)}function _x(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cx(e){return(Cx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var Tx=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ex(e,t)}(s,n.PureComponent);var e,t,r,i,a=(e=s,t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var r,n=Cx(e);if(t){var i=Cx(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return Mx(this,r)});function s(e,t){var r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s);var n=(r=a.call(this,e,t)).props,i=n.isActive,o=n.attributeName,c=n.from,l=n.to,u=n.steps,f=n.children,d=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(_x(r)),r.changeStyle=r.changeStyle.bind(_x(r)),!i||d<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),Mx(r);if(u&&u.length)r.state={style:u[0].style};else if(c){if("function"==typeof f)return r.state={style:c},Mx(r);r.state={style:o?Ax({},o,c):c}}else r.state={style:{}};return r}return r=s,i=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,i=t.attributeName,a=t.shouldReAnimate,o=t.to,s=t.from,c=this.state.style;if(n)if(r){if(!(Vb(e.to,o)&&e.canBegin&&e.isActive)){var l=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var u=l||a?s:e.to;if(this.state&&c){var f={style:i?Ax({},i,u):u};(i&&c[i]!==u||!i&&c!==u)&&this.setState(f)}this.runAnimation(Px(Px({},this.props),{},{from:u,begin:0}))}}else{var d={style:i?Ax({},i,o):o};this.state&&c&&(i&&c[i]!==o||!i&&c!==o)&&this.setState(d)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,i=e.duration,a=e.easing,o=e.begin,s=e.onAnimationEnd,c=e.onAnimationStart,l=function(e,t,r,n,i){var a,o,s,c,l=(a=e,o=t,[Object.keys(a),Object.keys(o)].reduce((function(e,t){return e.filter((function(e){return t.includes(e)}))}))),u=l.reduce((function(r,n){return hx(hx({},r),{},mx({},n,[e[n],t[n]]))}),{}),f=l.reduce((function(r,n){return hx(hx({},r),{},mx({},n,{from:e[n],velocity:0,to:t[n]}))}),{}),d=-1,p=function(){return null};return p=r.isStepper?function(n){s||(s=n);var a=(n-s)/r.dt;f=xx(r,f,a),i(hx(hx(hx({},e),t),rx((function(e,t){return t.from}),f))),s=n,Object.values(f).filter(bx).length&&(d=requestAnimationFrame(p))}:function(a){c||(c=a);var o=(a-c)/n,s=rx((function(e,t){return vx.apply(void 0,dx(t).concat([r(o)]))}),u);if(i(hx(hx(hx({},e),t),s)),o<1)d=requestAnimationFrame(p);else{var l=rx((function(e,t){return vx.apply(void 0,dx(t).concat([r(1)]))}),u);i(hx(hx(hx({},e),t),l))}},function(){return requestAnimationFrame(p),function(){cancelAnimationFrame(d)}}}(r,n,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return ux(n);case"spring":return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,i=void 0===n?8:n,a=e.dt,o=void 0===a?17:a,s=function(e,t,n){var a=n+(-(e-t)*r-n*i)*o/1e3,s=n*o/1e3+e;return Math.abs(s-t)<ox&&Math.abs(a)<ox?[t,0]:[s,a]};return s.isStepper=!0,s.dt=o,s}();default:if("cubic-bezier"===n.split("(")[0])return ux(n)}return"function"==typeof n?n:null}(a),i,this.changeStyle);this.manager.start([c,o,function(){t.stopJSAnimation=l()},i,s])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,i=e.onAnimationStart,a=r[0],o=a.style,s=a.duration,c=void 0===s?0:s;return this.manager.start([i].concat(Ox(r.reduce((function(e,n,i){if(0===i)return e;var a=n.duration,o=n.easing,s=void 0===o?"ease":o,c=n.style,l=n.properties,u=n.onAnimationEnd,f=i>0?r[i-1]:n,d=l||Object.keys(c);if("function"==typeof s||"spring"===s)return[].concat(Ox(e),[t.runJSAnimation.bind(t,{from:f.style,to:c,duration:a,easing:s}),a]);var p=nx(d,a,s),h=Px(Px(Px({},f.style),c),{},{transition:p});return[].concat(Ox(e),[h,a,u]).filter(tx)}),[o,Math.max(c,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){this.manager||(this.manager=Kb());var t=e.begin,r=e.duration,n=e.attributeName,i=e.to,a=e.easing,o=e.onAnimationStart,s=e.onAnimationEnd,c=e.steps,l=e.children,u=this.manager;if(this.unSubscribe=u.subscribe(this.handleStyleChange),"function"!=typeof a&&"function"!=typeof l&&"spring"!==a)if(c.length>1)this.runStepAnimation(e);else{var f=n?Ax({},n,i):i,d=nx(Object.keys(f),r,a);u.start([o,t,Px(Px({},f),{},{transition:d}),r,s])}else this.runJSAnimation(e)}},{key:"render",value:function(){var e=this.props,t=e.children;e.begin;var r=e.duration;e.attributeName,e.easing;var i=e.isActive;e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart;var a=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,jx),s=n.Children.count(t),c=this.state.style;if("function"==typeof t)return t(c);if(!i||0===s||r<=0)return t;var l=function(e){var t=e.props,r=t.style,i=void 0===r?{}:r,o=t.className;return n.cloneElement(e,Px(Px({},a),{},{style:Px(Px({},i),c),className:o}))};return 1===s?l(n.Children.only(t)):o.createElement("div",null,n.Children.map(t,(function(e){return l(e)})))}}],i&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nx(n.key),n)}}(r.prototype,i),Object.defineProperty(r,"prototype",{writable:!1}),s}();function Dx(e){return(Dx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ix(){return Ix=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ix.apply(this,arguments)}function Bx(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function $x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Rx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$x(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Dx(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Dx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Dx(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$x(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}Tx.displayName="Animate",Tx.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},Tx.propTypes={from:yb.oneOfType([yb.object,yb.string]),to:yb.oneOfType([yb.object,yb.string]),attributeName:yb.string,duration:yb.number,begin:yb.number,easing:yb.oneOfType([yb.string,yb.func]),steps:yb.arrayOf(yb.shape({duration:yb.number.isRequired,style:yb.object.isRequired,easing:yb.oneOfType([yb.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),yb.func]),properties:yb.arrayOf("string"),onAnimationEnd:yb.func})),children:yb.oneOfType([yb.node,yb.func]),isActive:yb.bool,canBegin:yb.bool,onAnimationEnd:yb.func,shouldReAnimate:yb.bool,onAnimationStart:yb.func,onAnimationReStart:yb.func};var Lx=function(e,t,r,n,i){var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),s=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+s*u[0]),u[0]>0&&(a+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(l,",").concat(e+c*u[0],",").concat(t)),a+="L ".concat(e+r-c*u[1],",").concat(t),u[1]>0&&(a+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(l,",\n        ").concat(e+r,",").concat(t+s*u[1])),a+="L ".concat(e+r,",").concat(t+n-s*u[2]),u[2]>0&&(a+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(l,",\n        ").concat(e+r-c*u[2],",").concat(t+n)),a+="L ".concat(e+c*u[3],",").concat(t+n),u[3]>0&&(a+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+n-s*u[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+s*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+r,",").concat(t+s*d,"\n            L ").concat(e+r,",").concat(t+n-s*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e,",").concat(t+n-s*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},zx=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,i=t.x,a=t.y,o=t.width,s=t.height;if(Math.abs(o)>0&&Math.abs(s)>0){var c=Math.min(i,i+o),l=Math.max(i,i+o),u=Math.min(a,a+s),f=Math.max(a,a+s);return r>=c&&r<=l&&n>=u&&n<=f}return!1},Fx={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Ux=function(e){var t,r,i=Rx(Rx({},Fx),e),a=n.useRef(),s=(t=n.useState(-1),r=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(t,r)||function(e,t){if(e){if("string"==typeof e)return Bx(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Bx(e,t):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),c=s[0],l=s[1];n.useEffect((function(){if(a.current&&a.current.getTotalLength)try{var e=a.current.getTotalLength();e&&l(e)}catch(t){}}),[]);var u=i.x,f=i.y,d=i.width,p=i.height,h=i.radius,m=i.className,y=i.animationEasing,g=i.animationDuration,v=i.animationBegin,b=i.isAnimationActive,x=i.isUpdateAnimationActive;if(u!==+u||f!==+f||d!==+d||p!==+p||0===d||0===p)return null;var w=P("recharts-rectangle",m);return x?o.createElement(Tx,{canBegin:c>0,from:{width:d,height:p,x:u,y:f},to:{width:d,height:p,x:u,y:f},duration:g,animationEasing:y,isActive:x},(function(e){var t=e.width,r=e.height,n=e.x,s=e.y;return o.createElement(Tx,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:g,isActive:b,easing:y},o.createElement("path",Ix({},Yn(i,!0),{className:w,d:Lx(n,s,t,r,h),ref:a})))})):o.createElement("path",Ix({},Yn(i,!0),{className:w,d:Lx(u,f,d,p,h)}))};function qx(){return qx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qx.apply(this,arguments)}var Wx=function(e){var t=e.cx,r=e.cy,n=e.r,i=e.className,a=P("recharts-dot",i);return t===+t&&r===+r&&n===+n?o.createElement("circle",qx({},Yn(e,!1),Cn(e),{className:a,cx:t,cy:r,r:n})):null};function Vx(e){return(Vx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Gx=["x","y","top","left","width","height","className"];function Yx(){return Yx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yx.apply(this,arguments)}function Hx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}var Xx=function(e,t,r,n,i,a){return"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r)},Kx=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,i=void 0===n?0:n,a=e.top,s=void 0===a?0:a,c=e.left,l=void 0===c?0:c,u=e.width,f=void 0===u?0:u,d=e.height,p=void 0===d?0:d,h=e.className,m=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Hx(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Vx(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Vx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Vx(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hx(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({x:r,y:i,top:s,left:l,width:f,height:p},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Gx));return gn(r)&&gn(i)&&gn(f)&&gn(p)&&gn(s)&&gn(l)?o.createElement("path",Yx({},Yn(m,!0),{className:P("recharts-cross",h),d:Xx(r,i,f,p,s,l)})):null},Zx=us(Object.getPrototypeOf,Object),Jx=Ke,Qx=Zx,ew=Ze,tw=Function.prototype,rw=Object.prototype,nw=tw.toString,iw=rw.hasOwnProperty,aw=nw.call(Object);const ow=a((function(e){if(!ew(e)||"[object Object]"!=Jx(e))return!1;var t=Qx(e);if(null===t)return!0;var r=iw.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&nw.call(r)==aw}));var sw=Ke,cw=Ze;const lw=a((function(e){return!0===e||!1===e||cw(e)&&"[object Boolean]"==sw(e)}));function uw(e){return(uw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fw(){return fw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fw.apply(this,arguments)}function dw(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function hw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pw(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=uw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=uw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==uw(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var mw=function(e,t,r,n,i){var a,o=r-n;return a="M ".concat(e,",").concat(t),a+="L ".concat(e+r,",").concat(t),a+="L ".concat(e+r-o/2,",").concat(t+i),(a+="L ".concat(e+r-o/2-n,",").concat(t+i))+"L ".concat(e,",").concat(t," Z")},yw={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},gw=function(e){var t,r,i=hw(hw({},yw),e),a=n.useRef(),s=(t=n.useState(-1),r=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(t,r)||function(e,t){if(e){if("string"==typeof e)return dw(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?dw(e,t):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),c=s[0],l=s[1];n.useEffect((function(){if(a.current&&a.current.getTotalLength)try{var e=a.current.getTotalLength();e&&l(e)}catch(t){}}),[]);var u=i.x,f=i.y,d=i.upperWidth,p=i.lowerWidth,h=i.height,m=i.className,y=i.animationEasing,g=i.animationDuration,v=i.animationBegin,b=i.isUpdateAnimationActive;if(u!==+u||f!==+f||d!==+d||p!==+p||h!==+h||0===d&&0===p||0===h)return null;var x=P("recharts-trapezoid",m);return b?o.createElement(Tx,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:u,y:f},to:{upperWidth:d,lowerWidth:p,height:h,x:u,y:f},duration:g,animationEasing:y,isActive:b},(function(e){var t=e.upperWidth,r=e.lowerWidth,n=e.height,s=e.x,l=e.y;return o.createElement(Tx,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:g,easing:y},o.createElement("path",fw({},Yn(i,!0),{className:x,d:mw(s,l,t,r,n),ref:a})))})):o.createElement("g",null,o.createElement("path",fw({},Yn(i,!0),{className:x,d:mw(u,f,d,p,h)})))},vw=["option","shapeType","propTransformer","activeClassName","isActive"];function bw(e){return(bw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function xw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ww(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xw(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=bw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=bw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==bw(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function jw(e,t){return ww(ww({},t),e)}function Ow(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return o.createElement(Ux,r);case"trapezoid":return o.createElement(gw,r);case"sector":return o.createElement(nb,r);case"symbols":if(function(e){return"symbols"===e}(t))return o.createElement(Ba,r);break;default:return null}}function kw(e){var t,r=e.option,i=e.shapeType,a=e.propTransformer,s=void 0===a?jw:a,c=e.activeClassName,l=void 0===c?"recharts-active-shape":c,u=e.isActive,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vw);if(n.isValidElement(r))t=n.cloneElement(r,ww(ww({},f),function(e){return n.isValidElement(e)?e.props:e}(r)));else if(ft(r))t=r(f);else if(ow(r)&&!lw(r)){var d=s(r,f);t=o.createElement(Ow,{shapeType:i,elementProps:d})}else{var p=f;t=o.createElement(Ow,{shapeType:i,elementProps:p})}return u?o.createElement(ri,{className:l},t):t}function Sw(e,t){return null!=t&&"trapezoids"in e.props}function Pw(e,t){return null!=t&&"sectors"in e.props}function Aw(e,t){return null!=t&&"points"in e.props}function Nw(e,t){var r,n,i=e.x===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.x)||e.x===t.x,a=e.y===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.y)||e.y===t.y;return i&&a}function Ew(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function Mw(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function _w(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=function(e,t){var r;return Sw(e,t)?r="trapezoids":Pw(e,t)?r="sectors":Aw(e,t)&&(r="points"),r}(r,t),a=function(e,t){var r,n;return Sw(e,t)?null===(r=t.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:Pw(e,t)?null===(n=t.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:Aw(e,t)?t.payload:{}}(r,t),o=n.filter((function(e,n){var o=By(a,e),s=r.props[i].filter((function(e){var n=function(e,t){var r;return Sw(e,t)?r=Nw:Pw(e,t)?r=Ew:Aw(e,t)&&(r=Mw),r}(r,t);return n(e,t)})),c=r.props[i].indexOf(s[s.length-1]);return o&&n===c}));return n.indexOf(o[o.length-1])}var Cw=Math.ceil,Tw=Math.max,Dw=Gu,Iw=1/0,Bw=function(e){return e?(e=Dw(e))===Iw||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0},$w=Jl,Rw=Bw;const Lw=a((function(e,t,r){return r&&"number"!=typeof r&&$w(e,t,r)&&(t=r=void 0),e=Rw(e),void 0===t?(t=e,e=0):t=Rw(t),function(e,t,r,n){for(var i=-1,a=Tw(Cw((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}(e,t,r=void 0===r?e<t?1:-1:Rw(r),void 0)}));function zw(e){return(zw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Fw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Uw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fw(Object(r),!0).forEach((function(t){qw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function qw(e,t,r){var n;return n=function(e,t){if("object"!=zw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=zw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t,"string"),(t="symbol"==zw(n)?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ww=["Webkit","Moz","O","ms"];function Vw(e){return(Vw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Gw(){return Gw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gw.apply(this,arguments)}function Yw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Hw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Yw(Object(r),!0).forEach((function(t){Qw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Xw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ej(n.key),n)}}function Kw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Kw=function(){return!!e})()}function Zw(e){return(Zw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Jw(e,t){return(Jw=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Qw(e,t,r){return(t=ej(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ej(e){var t=function(e){if("object"!=Vw(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Vw(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Vw(t)?t:t+""}var tj=function(e){return e.changedTouches&&!!e.changedTouches.length},rj=function(){function e(t){var r,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Qw((n=this,a=[t],i=Zw(i=e),r=function(e,t){if(t&&("object"===Vw(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(n,Kw()?Reflect.construct(i,a||[],Zw(n).constructor):i.apply(n,a))),"handleDrag",(function(e){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(e):r.state.isSlideMoving&&r.handleSlideDrag(e)})),Qw(r,"handleTouchMove",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&r.handleDrag(e.changedTouches[0])})),Qw(r,"handleDragEnd",(function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},(function(){var e=r.props,t=e.endIndex,n=e.onDragEnd,i=e.startIndex;null==n||n({endIndex:t,startIndex:i})})),r.detachDragEndListener()})),Qw(r,"handleLeaveWrapper",(function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))})),Qw(r,"handleEnterSlideOrTraveller",(function(){r.setState({isTextActive:!0})})),Qw(r,"handleLeaveSlideOrTraveller",(function(){r.setState({isTextActive:!1})})),Qw(r,"handleSlideDragStart",(function(e){var t=tj(e)?e.changedTouches[0]:e;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:t.pageX}),r.attachDragEndListener()})),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jw(e,t)}(e,n.PureComponent),t=e,i=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,i=e.height,a=e.stroke,s=Math.floor(r+i/2)-1;return o.createElement(o.Fragment,null,o.createElement("rect",{x:t,y:r,width:n,height:i,fill:a,stroke:"none"}),o.createElement("line",{x1:t+1,y1:s,x2:t+n-1,y2:s,fill:"none",stroke:"#fff"}),o.createElement("line",{x1:t+1,y1:s+2,x2:t+n-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return o.isValidElement(t)?o.cloneElement(t,r):ft(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,i=e.x,a=e.travellerWidth,o=e.updateId,s=e.startIndex,c=e.endIndex;if(r!==t.prevData||o!==t.prevUpdateId)return Hw({prevData:r,prevTravellerWidth:a,prevUpdateId:o,prevX:i,prevWidth:n},r&&r.length?function(e){var t=e.data,r=e.startIndex,n=e.endIndex,i=e.x,a=e.width,o=e.travellerWidth;if(!t||!t.length)return{};var s=t.length,c=md().domain(Lw(0,s)).range([i,i+a-o]),l=c.domain().map((function(e){return c(e)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:l}}({data:r,width:n,x:i,travellerWidth:a,startIndex:s,endIndex:c}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||i!==t.prevX||a!==t.prevTravellerWidth)){t.scale.range([i,i+n-a]);var l=t.scale.domain().map((function(e){return t.scale(e)}));return{prevData:r,prevTravellerWidth:a,prevUpdateId:o,prevX:i,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=0,n=e.length-1;n-r>1;){var i=Math.floor((r+n)/2);e[i]>t?n=i:r=i}return t>=e[n]?n:r}}],(r=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,i=this.state.scaleValues,a=this.props,o=a.gap,s=a.data.length-1,c=Math.min(r,n),l=Math.max(r,n),u=e.getIndexInRange(i,c),f=e.getIndexInRange(i,l);return{startIndex:u-u%o,endIndex:f===s?s:f-f%o}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,i=t.dataKey,a=tv(r[e],i,e);return ft(n)?n(a,e):a}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,i=t.endX,a=this.props,o=a.x,s=a.width,c=a.travellerWidth,l=a.startIndex,u=a.endIndex,f=a.onChange,d=e.pageX-r;d>0?d=Math.min(d,o+s-c-i,o+s-c-n):d<0&&(d=Math.max(d,o-n,o-i));var p=this.getIndex({startX:n+d,endX:i+d});p.startIndex===l&&p.endIndex===u||!f||f(p),this.setState({startX:n+d,endX:i+d,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=tj(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,i=t.endX,a=t.startX,o=this.state[n],s=this.props,c=s.x,l=s.width,u=s.travellerWidth,f=s.onChange,d=s.gap,p=s.data,h={startX:this.state.startX,endX:this.state.endX},m=e.pageX-r;m>0?m=Math.min(m,c+l-u-o):m<0&&(m=Math.max(m,c-o)),h[n]=o+m;var y=this.getIndex(h),g=y.startIndex,v=y.endIndex;this.setState(Qw(Qw({},n,o+m),"brushMoveStartX",e.pageX),(function(){var e;f&&(e=p.length-1,("startX"===n&&(i>a?g%d===0:v%d===0)||i<a&&v===e||"endX"===n&&(i>a?v%d===0:g%d===0)||i>a&&v===e)&&f(y))}))}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,i=n.scaleValues,a=n.startX,o=n.endX,s=this.state[t],c=i.indexOf(s);if(-1!==c){var l=c+e;if(!(-1===l||l>=i.length)){var u=i[l];"startX"===t&&u>=o||"endX"===t&&u<=a||this.setState(Qw({},t,u),(function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))}))}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,i=e.height,a=e.fill,s=e.stroke;return o.createElement("rect",{stroke:s,fill:a,x:t,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,i=e.width,a=e.height,s=e.data,c=e.children,l=e.padding,u=n.Children.only(c);return u?o.cloneElement(u,{x:t,y:r,width:i,height:a,margin:l,compact:!0,data:s}):null}},{key:"renderTravellerLayer",value:function(t,r){var n,i,a=this,s=this.props,c=s.y,l=s.travellerWidth,u=s.height,f=s.traveller,d=s.ariaLabel,p=s.data,h=s.startIndex,m=s.endIndex,y=Math.max(t,this.props.x),g=Hw(Hw({},Yn(this.props,!1)),{},{x:y,y:c,width:l,height:u}),v=d||"Min value: ".concat(null===(n=p[h])||void 0===n?void 0:n.name,", Max value: ").concat(null===(i=p[m])||void 0===i?void 0:i.name);return o.createElement(ri,{tabIndex:0,role:"slider","aria-label":v,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,r))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(f,g))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,i=r.height,a=r.stroke,s=r.travellerWidth,c=Math.min(e,t)+s,l=Math.max(Math.abs(t-e)-s,0);return o.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:n,width:l,height:i})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,i=e.height,a=e.travellerWidth,s=e.stroke,c=this.state,l=c.startX,u=c.endX,f={pointerEvents:"none",fill:s};return o.createElement(ri,{className:"recharts-brush-texts"},o.createElement(zf,Gw({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,u)-5,y:n+i/2},f),this.getTextOfTick(t)),o.createElement(zf,Gw({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,u)+a+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,i=e.x,a=e.y,s=e.width,c=e.height,l=e.alwaysShowText,u=this.state,f=u.startX,d=u.endX,p=u.isTextActive,h=u.isSlideMoving,m=u.isTravellerMoving,y=u.isTravellerFocused;if(!t||!t.length||!gn(i)||!gn(a)||!gn(s)||!gn(c)||s<=0||c<=0)return null;var g,v,b,x,w=P("recharts-brush",r),j=1===o.Children.count(n),O=(v="none",b=(g="userSelect").replace(/(\w)/,(function(e){return e.toUpperCase()})),(x=Ww.reduce((function(e,t){return Uw(Uw({},e),{},qw({},t+b,v))}),{}))[g]=v,x);return o.createElement(ri,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:O},this.renderBackground(),j&&this.renderPanorama(),this.renderSlide(f,d),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(d,"endX"),(p||h||m||y||l)&&this.renderText())}}])&&Xw(t.prototype,r),i&&Xw(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,i}();Qw(rj,"displayName","Brush"),Qw(rj,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var nj=kl,ij=fo,aj=$c,oj=function(e,t){var r;return nj(e,(function(e,n,i){return!(r=t(e,n,i))})),!!r},sj=Be,cj=Jl;const lj=a((function(e,t,r){var n=sj(e)?ij:oj;return r&&cj(e,t,r)&&(t=void 0),n(e,aj(t))}));var uj=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},fj=Ll,dj=jl,pj=$c;const hj=a((function(e,t){var r={};return t=pj(t),dj(e,(function(e,n,i){!function(e,t,r){"__proto__"==t&&fj?fj(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}(r,n,t(e,n,i))})),r}));var mj=kl,yj=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0},gj=function(e,t){var r=!0;return mj(e,(function(e,n,i){return r=!!t(e,n,i)})),r},vj=$c,bj=Be,xj=Jl;const wj=a((function(e,t,r){var n=bj(e)?yj:gj;return r&&xj(e,t,r)&&(t=void 0),n(e,vj(t))}));var jj=["x","y"];function Oj(e){return(Oj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function kj(){return kj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kj.apply(this,arguments)}function Sj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Pj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sj(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=Oj(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Oj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==Oj(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Aj(e,t){var r=e.x,n=e.y,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,jj),a="".concat(r),o=parseInt(a,10),s="".concat(n),c=parseInt(s,10),l="".concat(t.height||i.height),u=parseInt(l,10),f="".concat(t.width||i.width),d=parseInt(f,10);return Pj(Pj(Pj(Pj(Pj({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:u,width:d,name:t.name,radius:t.radius})}function Nj(e){return o.createElement(kw,kj({shapeType:"rectangle",propTransformer:Aj,activeClassName:"recharts-active-bar"},e))}var Ej,Mj=["value","background"];function _j(e){return(_j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Cj(){return Cj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cj.apply(this,arguments)}function Tj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Dj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Tj(Object(r),!0).forEach((function(t){Lj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ij(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zj(n.key),n)}}function Bj(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Bj=function(){return!!e})()}function $j(e){return($j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Rj(e,t){return(Rj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Lj(e,t,r){return(t=zj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zj(e){var t=function(e){if("object"!=_j(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=_j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==_j(t)?t:t+""}var Fj=function(){function e(){var t,r,n,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];return Lj((r=this,n=e,i=[].concat(o),n=$j(n),t=function(e,t){if(t&&("object"===_j(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(r,Bj()?Reflect.construct(n,i||[],$j(r).constructor):n.apply(r,i))),"state",{isAnimationFinished:!1}),Lj(t,"id",xn("recharts-bar-")),Lj(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()})),Lj(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()})),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rj(e,t)}(e,n.PureComponent),t=e,i=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],(r=[{key:"renderRectanglesStatically",value:function(e){var t=this,r=this.props,n=r.shape,i=r.dataKey,a=r.activeIndex,s=r.activeBar,c=Yn(this.props,!1);return e&&e.map((function(e,r){var l=r===a,u=l?s:n,f=Dj(Dj(Dj({},c),e),{},{isActive:l,option:u,index:r,dataKey:i,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return o.createElement(ri,Cj({className:"recharts-bar-rectangle"},Tn(t.props,e,r),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),o.createElement(Nj,f))}))}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,r=t.data,n=t.layout,i=t.isAnimationActive,a=t.animationBegin,s=t.animationDuration,c=t.animationEasing,l=t.animationId,u=this.state.prevData;return o.createElement(Tx,{begin:a,duration:s,isActive:i,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(t){var i=t.t,a=r.map((function(e,t){var r=u&&u[t];if(r){var a=On(r.x,e.x),o=On(r.y,e.y),s=On(r.width,e.width),c=On(r.height,e.height);return Dj(Dj({},e),{},{x:a(i),y:o(i),width:s(i),height:c(i)})}if("horizontal"===n){var l=On(0,e.height)(i);return Dj(Dj({},e),{},{y:e.y+e.height-l,height:l})}var f=On(0,e.width)(i);return Dj(Dj({},e),{},{width:f})}));return o.createElement(ri,null,e.renderRectanglesStatically(a))}))}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,r=e.isAnimationActive,n=this.state.prevData;return!(r&&t&&t.length)||n&&By(n,t)?this.renderRectanglesStatically(t):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var e=this,t=this.props,r=t.data,n=t.dataKey,i=t.activeIndex,a=Yn(this.props.background,!1);return r.map((function(t,r){t.value;var s=t.background,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(t,Mj);if(!s)return null;var l=Dj(Dj(Dj(Dj(Dj({},c),{},{fill:"#eee"},s),a),Tn(e.props,t,r)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return o.createElement(Nj,Cj({key:"background-bar-".concat(r),option:e.props.background,isActive:r===i},l))}))}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,a=r.yAxis,s=r.layout,c=qn(r.children,Wg);if(!c)return null;var l="vertical"===s?n[0].height/2:n[0].width/2,u=function(e,t){var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:tv(e,t)}},f={clipPath:e?"url(#clipPath-".concat(t,")"):null};return o.createElement(ri,f,c.map((function(e){return o.cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:n,xAxis:i,yAxis:a,layout:s,offset:l,dataPointFormatter:u})})))}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.data,n=e.className,i=e.xAxis,a=e.yAxis,s=e.left,c=e.top,l=e.width,u=e.height,f=e.isAnimationActive,d=e.background,p=e.id;if(t||!r||!r.length)return null;var h=this.state.isAnimationFinished,m=P("recharts-bar",n),y=i&&i.allowDataOverflow,g=a&&a.allowDataOverflow,v=y||g,b=Rr(p)?this.id:p;return o.createElement(ri,{className:m},y||g?o.createElement("defs",null,o.createElement("clipPath",{id:"clipPath-".concat(b)},o.createElement("rect",{x:y?s:s-l/2,y:g?c:c-u/2,width:y?l:2*l,height:g?u:2*u}))):null,o.createElement(ri,{className:"recharts-bar-rectangles",clipPath:v?"url(#clipPath-".concat(b,")"):null},d?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(v,b),(!f||h)&&Xv.renderCallByParent(this.props,r))}}])&&Ij(t.prototype,r),i&&Ij(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,i}();function Uj(e){return(Uj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function qj(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yj(n.key),n)}}function Wj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Vj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Wj(Object(r),!0).forEach((function(t){Gj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Gj(e,t,r){return(t=Yj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yj(e){var t=function(e,t){if("object"!=Uj(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=Uj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==Uj(t)?t:t+""}Ej=Fj,Lj(Fj,"displayName","Bar"),Lj(Fj,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Su,animationBegin:0,animationDuration:400,animationEasing:"ease"}),Lj(Fj,"getComposedData",(function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,s=e.xAxisTicks,c=e.yAxisTicks,l=e.stackedData,u=e.dataStartIndex,f=e.displayedData,d=e.offset,p=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].item===t)return e[r].position;return null}(n,r);if(!p)return null;var h=t.layout,m=r.type.defaultProps,y=void 0!==m?Dj(Dj({},m),r.props):r.props,g=y.dataKey,v=y.children,b=y.minPointSize,x="horizontal"===h?o:a,w=l?x.scale.domain():null,j=function(e){var t=e.numericAxis,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]}({numericAxis:x}),O=qn(v,of),k=f.map((function(e,t){var n,f,d,m,y,v;l?n=function(e,t){if(!t||2!==t.length||!gn(t[0])||!gn(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!gn(e[0])||e[0]<r)&&(i[0]=r),(!gn(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i}(l[u+t],w):(n=tv(e,g),Array.isArray(n)||(n=[j,n]));var x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof e)return e;var i="number"==typeof r;return i?e(r,n):(i||Dg(),t)}}(b,Ej.defaultProps.minPointSize)(n[1],t);if("horizontal"===h){var k,S=[o.scale(n[0]),o.scale(n[1])],P=S[0],A=S[1];f=mv({axis:a,ticks:s,bandSize:i,offset:p.offset,entry:e,index:t}),d=null!==(k=null!=A?A:P)&&void 0!==k?k:void 0,m=p.size;var N=P-A;if(y=Number.isNaN(N)?0:N,v={x:f,y:o.y,width:m,height:o.height},Math.abs(x)>0&&Math.abs(y)<Math.abs(x)){var E=mn(y||x)*(Math.abs(x)-Math.abs(y));d-=E,y+=E}}else{var M=[a.scale(n[0]),a.scale(n[1])],_=M[0],C=M[1];f=_,d=mv({axis:o,ticks:c,bandSize:i,offset:p.offset,entry:e,index:t}),m=C-_,y=p.size,v={x:a.x,y:d,width:a.width,height:y},Math.abs(x)>0&&Math.abs(m)<Math.abs(x)&&(m+=mn(m||x)*(Math.abs(x)-Math.abs(m)))}return Dj(Dj(Dj({},e),{},{x:f,y:d,width:m,height:y,value:l?n:n[1],payload:e,background:v},O&&O[t]&&O[t].props),{},{tooltipPayload:[jv(r,e)],tooltipPosition:{x:f+m/2,y:d+y/2}})}));return Dj({data:k,layout:h},d)}));var Hj=function(e,t){var r=e.x,n=e.y,i=t.x,a=t.y;return{x:Math.min(r,i),y:Math.min(n,a),width:Math.abs(i-r),height:Math.abs(a-n)}},Xj=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.scale=t}return t=e,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+i;case"end":var a=this.bandwidth?this.bandwidth():0;return this.scale(e)+a}if(r){var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],n=[{key:"create",value:function(t){return new e(t)}}],r&&qj(t.prototype,r),n&&qj(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,n}();Gj(Xj,"EPS",1e-4);var Kj=function(e){var t=Object.keys(e).reduce((function(t,r){return Vj(Vj({},t),{},Gj({},r,Xj.create(e[r])))}),{});return Vj(Vj({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return hj(e,(function(e,r){return t[r].apply(e,{bandAware:n,position:i})}))},isInRange:function(e){return wj(e,(function(e,r){return t[r].isInRange(e)}))}})},Zj=$c,Jj=gs,Qj=xs,eO=Bw,tO=Rc,rO=$c,nO=Math.max;const iO=a((aO=function(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=null==r?0:function(e){var t=eO(e),r=t%1;return t==t?r?t-r:t:0}(r);return i<0&&(i=nO(n+i,0)),tO(e,rO(t),i)},function(e,t,r){var n=Object(e);if(!Jj(e)){var i=Zj(t);e=Qj(e),t=function(e){return i(n[e],e,n)}}var a=aO(e,t,r);return a>-1?n[i?e[a]:a]:void 0}));var aO,oO=fr((function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}}),(function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")})),sO=n.createContext(void 0),cO=n.createContext(void 0),lO=n.createContext(void 0),uO=n.createContext({}),fO=n.createContext(void 0),dO=n.createContext(0),pO=n.createContext(0),hO=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,i=t.offset,a=e.clipPathId,s=e.children,c=e.width,l=e.height,u=oO(i);return o.createElement(sO.Provider,{value:r},o.createElement(cO.Provider,{value:n},o.createElement(uO.Provider,{value:i},o.createElement(lO.Provider,{value:u},o.createElement(fO.Provider,{value:a},o.createElement(dO.Provider,{value:l},o.createElement(pO.Provider,{value:c},s)))))))},mO=function(e){var t=n.useContext(sO);null==t&&Dg();var r=t[e];return null==r&&Dg(),r},yO=function(e){var t=n.useContext(cO);null==t&&Dg();var r=t[e];return null==r&&Dg(),r},gO=function(){return n.useContext(pO)},vO=function(){return n.useContext(dO)};function bO(e){return(bO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function xO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(xO=function(){return!!e})()}function wO(e){return(wO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function jO(e,t){return(jO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function OO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function kO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?OO(Object(r),!0).forEach((function(t){SO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):OO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function SO(e,t,r){return(t=PO(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PO(e){var t=function(e){if("object"!=bO(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=bO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==bO(t)?t:t+""}function AO(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function NO(){return NO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},NO.apply(this,arguments)}function EO(e){var t=e.x,r=e.y,i=e.segment,a=e.xAxisId,s=e.yAxisId,c=e.shape,l=e.className,u=e.alwaysShow,f=n.useContext(fO),d=mO(a),p=yO(s),h=n.useContext(lO);if(!f||!h)return null;ni(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=function(e,t,r,n,i,a,o,s,c){var l=i.x,u=i.y,f=i.width,d=i.height;if(r){var p=c.y,h=e.y.apply(p,{position:a});if(uj(c,"discard")&&!e.y.isInRange(h))return null;var m=[{x:l+f,y:h},{x:l,y:h}];return"left"===s?m.reverse():m}if(t){var y=c.x,g=e.x.apply(y,{position:a});if(uj(c,"discard")&&!e.x.isInRange(g))return null;var v=[{x:g,y:u+d},{x:g,y:u}];return"top"===o?v.reverse():v}if(n){var b=c.segment.map((function(t){return e.apply(t,{position:a})}));return uj(c,"discard")&&lj(b,(function(t){return!e.isInRange(t)}))?null:b}return null}(Kj({x:d.scale,y:p.scale}),vn(t),vn(r),i&&2===i.length,h,e.position,d.orientation,p.orientation,e);if(!m)return null;var y,g,v,b=(g=2,function(e){if(Array.isArray(e))return e}(y=m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(y,g)||function(e,t){if(e){if("string"==typeof e)return AO(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?AO(e,t):void 0}}(y,g)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),x=b[0],w=x.x,j=x.y,O=b[1],k=O.x,S=O.y,A=kO(kO({clipPath:uj(e,"hidden")?"url(#".concat(f,")"):void 0},Yn(e,!0)),{},{x1:w,y1:j,x2:k,y2:S});return o.createElement(ri,{className:P("recharts-reference-line",l)},function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):ft(e)?e(t):o.createElement("line",NO({},t,{className:"recharts-reference-line-line"}))}(c,A),$v.renderCallByParent(e,Hj({x:(v={x1:w,y1:j,x2:k,y2:S}).x1,y:v.y1},{x:v.x2,y:v.y2})))}var MO=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=wO(r=e),function(e,t){if(t&&("object"===bO(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,xO()?Reflect.construct(r,n||[],wO(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jO(e,t)}(e,o.Component),t=e,(r=[{key:"render",value:function(){return o.createElement(EO,this.props)}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,PO(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function _O(){return _O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_O.apply(this,arguments)}function CO(e){return(CO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function TO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function DO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?TO(Object(r),!0).forEach((function(t){RO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):TO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function IO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(IO=function(){return!!e})()}function BO(e){return(BO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function $O(e,t){return($O=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function RO(e,t,r){return(t=LO(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LO(e){var t=function(e){if("object"!=CO(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=CO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==CO(t)?t:t+""}SO(MO,"displayName","ReferenceLine"),SO(MO,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var zO=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=BO(r=e),function(e,t){if(t&&("object"===CO(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,IO()?Reflect.construct(r,n||[],BO(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$O(e,t)}(e,o.Component),t=e,r=[{key:"render",value:function(){var t=this.props,r=t.x,n=t.y,i=t.r,a=t.alwaysShow,s=t.clipPathId,c=vn(r),l=vn(n);if(ni(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var u=function(e){var t=e.x,r=e.y,n=e.xAxis,i=e.yAxis,a=Kj({x:n.scale,y:i.scale}),o=a.apply({x:t,y:r},{bandAware:!0});return uj(e,"discard")&&!a.isInRange(o)?null:o}(this.props);if(!u)return null;var f=u.x,d=u.y,p=this.props,h=p.shape,m=p.className,y=DO(DO({clipPath:uj(this.props,"hidden")?"url(#".concat(s,")"):void 0},Yn(this.props,!0)),{},{cx:f,cy:d});return o.createElement(ri,{className:P("recharts-reference-dot",m)},e.renderDot(h,y),$v.renderCallByParent(this.props,{x:f-i,y:d-i,width:2*i,height:2*i}))}}],r&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,LO(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function FO(){return FO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},FO.apply(this,arguments)}function UO(e){return(UO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function qO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function WO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?qO(Object(r),!0).forEach((function(t){HO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function VO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(VO=function(){return!!e})()}function GO(e){return(GO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function YO(e,t){return(YO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function HO(e,t,r){return(t=XO(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XO(e){var t=function(e){if("object"!=UO(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=UO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==UO(t)?t:t+""}RO(zO,"displayName","ReferenceDot"),RO(zO,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),RO(zO,"renderDot",(function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):ft(e)?e(t):o.createElement(Wx,_O({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))}));var KO=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=GO(r=e),function(e,t){if(t&&("object"===UO(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,VO()?Reflect.construct(r,n||[],GO(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&YO(e,t)}(e,o.Component),t=e,(r=[{key:"render",value:function(){var t=this.props,r=t.x1,n=t.x2,i=t.y1,a=t.y2,s=t.className,c=t.alwaysShow,l=t.clipPathId;ni(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var u=vn(r),f=vn(n),d=vn(i),p=vn(a),h=this.props.shape;if(!(u||f||d||p||h))return null;var m=function(e,t,r,n,i){var a=i.x1,o=i.x2,s=i.y1,c=i.y2,l=i.xAxis,u=i.yAxis;if(!l||!u)return null;var f=Kj({x:l.scale,y:u.scale}),d={x:e?f.x.apply(a,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(s,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(o,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!uj(i,"discard")||f.isInRange(d)&&f.isInRange(p)?Hj(d,p):null}(u,f,d,p,this.props);if(!m&&!h)return null;var y=uj(this.props,"hidden")?"url(#".concat(l,")"):void 0;return o.createElement(ri,{className:P("recharts-reference-area",s)},e.renderRect(h,WO(WO({clipPath:y},Yn(this.props,!0)),m)),$v.renderCallByParent(this.props,m))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,XO(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function ZO(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function JO(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function QO(e){return(QO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ek(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function tk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ek(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=QO(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=QO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==QO(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ek(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function rk(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,s=e.orientation,c=e.interval,l=e.tickFormatter,u=e.unit,f=e.angle;if(!i||!i.length||!n)return[];if(gn(c)||Su)return function(e,t){return ZO(e,t+1)}(i,"number"==typeof c&&gn(c)?c:0);var d=[],p="top"===s||"bottom"===s?"width":"height",h=u&&"width"===p?pf(u,{fontSize:t,letterSpacing:r}):{width:0,height:0},m=function(e,n){var i=ft(l)?l(e.value,n):e.value;return"width"===p?function(e,t,r){return function(e){var t=e.width,r=e.height,n=function(e){return(e%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=n*Math.PI/180,a=Math.atan(r/t),o=i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i);return Math.abs(o)}({width:e.width+t.width,height:e.height+t.height},r)}(pf(i,{fontSize:t,letterSpacing:r}),h,f):pf(i,{fontSize:t,letterSpacing:r})[p]},y=i.length>=2?mn(i[1].coordinate-i[0].coordinate):1,g=function(e,t,r){var n="width"===r,i=e.x,a=e.y,o=e.width,s=e.height;return 1===t?{start:n?i:a,end:n?i+o:a+s}:{start:n?i+o:a+s,end:n?i:a}}(a,y,p);return"equidistantPreserveStart"===c?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),s=t.start,c=t.end,l=0,u=1,f=s,d=function(){var t=null==n?void 0:n[l];if(void 0===t)return{v:ZO(n,u)};var a,o=l,d=function(){return void 0===a&&(a=r(t,o)),a},p=t.coordinate,h=0===l||JO(e,p,d,f,c);h||(l=0,f=s,u+=1),h&&(f=p+e*(d()/2+i),l+=u)};u<=o.length;)if(a=d())return a.v;return[]}(y,g,m,i,o):(d="preserveStart"===c||"preserveStartEnd"===c?function(e,t,r,n,i,a){var o=(n||[]).slice(),s=o.length,c=t.start,l=t.end;if(a){var u=n[s-1],f=r(u,s-1),d=e*(u.coordinate+e*f/2-l);o[s-1]=u=tk(tk({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),JO(e,u.tickCoord,(function(){return f}),c,l)&&(l=u.tickCoord-e*(f/2+i),o[s-1]=tk(tk({},u),{},{isShow:!0}))}for(var p=a?s-1:s,h=function(t){var n,a=o[t],s=function(){return void 0===n&&(n=r(a,t)),n};if(0===t){var u=e*(a.coordinate-e*s()/2-c);o[t]=a=tk(tk({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else o[t]=a=tk(tk({},a),{},{tickCoord:a.coordinate});JO(e,a.tickCoord,s,c,l)&&(c=a.tickCoord+e*(s()/2+i),o[t]=tk(tk({},a),{},{isShow:!0}))},m=0;m<p;m++)h(m);return o}(y,g,m,i,o,"preserveStartEnd"===c):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,s=t.start,c=t.end,l=function(t){var n,l=a[t],u=function(){return void 0===n&&(n=r(l,t)),n};if(t===o-1){var f=e*(l.coordinate+e*u()/2-c);a[t]=l=tk(tk({},l),{},{tickCoord:f>0?l.coordinate-f*e:l.coordinate})}else a[t]=l=tk(tk({},l),{},{tickCoord:l.coordinate});JO(e,l.tickCoord,u,s,c)&&(c=l.tickCoord-e*(u()/2+i),a[t]=tk(tk({},l),{},{isShow:!0}))},u=o-1;u>=0;u--)l(u);return a}(y,g,m,i,o),d.filter((function(e){return e.isShow})))}HO(KO,"displayName","ReferenceArea"),HO(KO,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),HO(KO,"renderRect",(function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):ft(e)?e(t):o.createElement(Ux,FO({},t,{className:"recharts-reference-area-rect"}))}));var nk=["viewBox"],ik=["viewBox"],ak=["ticks"];function ok(e){return(ok="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sk(){return sk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sk.apply(this,arguments)}function ck(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function lk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ck(Object(r),!0).forEach((function(t){mk(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ck(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function uk(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function fk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yk(n.key),n)}}function dk(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(dk=function(){return!!e})()}function pk(e){return(pk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hk(e,t){return(hk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mk(e,t,r){return(t=yk(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yk(e){var t=function(e){if("object"!=ok(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=ok(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ok(t)?t:t+""}var gk=function(){function e(t){var r,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),(n=this,i=e,a=[t],i=pk(i),r=function(e,t){if(t&&("object"===ok(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(n,dk()?Reflect.construct(i,a||[],pk(n).constructor):i.apply(n,a))).state={fontSize:"",letterSpacing:""},r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hk(e,t)}(e,n.Component),t=e,i=[{key:"renderTickItem",value:function(e,t,r){return o.isValidElement(e)?o.cloneElement(e,t):ft(e)?e(t):o.createElement(zf,sk({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(r=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=uk(e,nk),i=this.props,a=i.viewBox,o=uk(i,ik);return!Pn(r,a)||!Pn(n,o)||!Pn(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,i,a,o,s=this.props,c=s.x,l=s.y,u=s.width,f=s.height,d=s.orientation,p=s.tickSize,h=s.mirror,m=s.tickMargin,y=h?-1:1,g=e.tickSize||p,v=gn(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=l+ +!h*f)-y*g)-y*m,a=v;break;case"left":n=i=e.coordinate,a=(t=(r=c+ +!h*u)-y*g)-y*m,o=v;break;case"right":n=i=e.coordinate,a=(t=(r=c+ +h*u)+y*g)+y*m,o=v;break;default:t=r=e.coordinate,o=(n=(i=l+ +h*f)+y*g)+y*m,a=v}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,i=e.height,a=e.orientation,s=e.mirror,c=e.axisLine,l=lk(lk(lk({},Yn(this.props,!1)),Yn(c,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var u=+("top"===a&&!s||"bottom"===a&&s);l=lk(lk({},l),{},{x1:t,y1:r+u*i,x2:t+n,y2:r+u*i})}else{var f=+("left"===a&&!s||"right"===a&&s);l=lk(lk({},l),{},{x1:t+f*n,y1:r,x2:t+f*n,y2:r+i})}return o.createElement("line",sk({},l,{className:P("recharts-cartesian-axis-line",$r(c,"className"))}))}},{key:"renderTicks",value:function(t,r,n){var i=this,a=this.props,s=a.tickLine,c=a.stroke,l=a.tick,u=a.tickFormatter,f=a.unit,d=rk(lk(lk({},this.props),{},{ticks:t}),r,n),p=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),m=Yn(this.props,!1),y=Yn(l,!1),g=lk(lk({},m),{},{fill:"none"},Yn(s,!1)),v=d.map((function(t,r){var n=i.getTickLineCoord(t),a=n.line,v=n.tick,b=lk(lk(lk(lk({textAnchor:p,verticalAnchor:h},m),{},{stroke:"none",fill:c},y),v),{},{index:r,payload:t,visibleTicksCount:d.length,tickFormatter:u});return o.createElement(ri,sk({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},Tn(i.props,t,r)),s&&o.createElement("line",sk({},g,a,{className:P("recharts-cartesian-axis-tick-line",$r(s,"className"))})),l&&e.renderTickItem(l,b,"".concat(ft(u)?u(t.value,r):t.value).concat(f||"")))}));return o.createElement("g",{className:"recharts-cartesian-axis-ticks"},v)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,i=t.height,a=t.ticksGenerator,s=t.className;if(t.hide)return null;var c=this.props,l=c.ticks,u=uk(c,ak),f=l;return ft(a)&&(f=l&&l.length>0?a(this.props):a(u)),n<=0||i<=0||!f||!f.length?null:o.createElement(ri,{className:P("recharts-cartesian-axis",s),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),$v.renderCallByParent(this.props))}}])&&fk(t.prototype,r),i&&fk(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,i}();mk(gk,"displayName","CartesianAxis"),mk(gk,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var vk=["x1","y1","x2","y2","key"],bk=["offset"];function xk(e){return(xk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function wk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wk(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=xk(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=xk(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==xk(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wk(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ok(){return Ok=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ok.apply(this,arguments)}function kk(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Sk=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,i=e.y,a=e.width,s=e.height,c=e.ry;return o.createElement("rect",{x:n,y:i,ry:c,width:a,height:s,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function Pk(e,t){var r;if(o.isValidElement(e))r=o.cloneElement(e,t);else if(ft(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,s=t.y2,c=t.key,l=kk(t,vk),u=Yn(l,!1);u.offset;var f=kk(u,bk);r=o.createElement("line",Ok({},f,{x1:n,y1:i,x2:a,y2:s,fill:"none",key:c}))}return r}function Ak(e){var t=e.x,r=e.width,n=e.horizontal,i=void 0===n||n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var s=a.map((function(n,a){var o=jk(jk({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(a),index:a});return Pk(i,o)}));return o.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function Nk(e){var t=e.y,r=e.height,n=e.vertical,i=void 0===n||n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var s=a.map((function(n,a){var o=jk(jk({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(a),index:a});return Pk(i,o)}));return o.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function Ek(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,s=e.height,c=e.horizontalPoints,l=e.horizontal;if(void 0!==l&&!l||!t||!t.length)return null;var u=c.map((function(e){return Math.round(e+i-i)})).sort((function(e,t){return e-t}));i!==u[0]&&u.unshift(0);var f=u.map((function(e,c){var l=u[c+1]?u[c+1]-e:i+s-e;if(l<=0)return null;var f=c%t.length;return o.createElement("rect",{key:"react-".concat(c),y:e,x:n,height:l,width:a,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})}));return o.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function Mk(e){var t=e.vertical,r=void 0===t||t,n=e.verticalFill,i=e.fillOpacity,a=e.x,s=e.y,c=e.width,l=e.height,u=e.verticalPoints;if(!r||!n||!n.length)return null;var f=u.map((function(e){return Math.round(e+a-a)})).sort((function(e,t){return e-t}));a!==f[0]&&f.unshift(0);var d=f.map((function(e,t){var r=f[t+1]?f[t+1]-e:a+c-e;if(r<=0)return null;var u=t%n.length;return o.createElement("rect",{key:"react-".concat(t),x:e,y:s,width:r,height:l,stroke:"none",fill:n[u],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return o.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},d)}var _k=function(e,t){var r=e.xAxis,n=e.width,i=e.height,a=e.offset;return sv(rk(jk(jk(jk({},gk.defaultProps),r),{},{ticks:cv(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},Ck=function(e,t){var r=e.yAxis,n=e.width,i=e.height,a=e.offset;return sv(rk(jk(jk(jk({},gk.defaultProps),r),{},{ticks:cv(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},Tk={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Dk(e){var t,r,i,a,s,c,l,u,f=gO(),d=vO(),p=n.useContext(uO),h=jk(jk({},e),{},{stroke:null!==(t=e.stroke)&&void 0!==t?t:Tk.stroke,fill:null!==(r=e.fill)&&void 0!==r?r:Tk.fill,horizontal:null!==(i=e.horizontal)&&void 0!==i?i:Tk.horizontal,horizontalFill:null!==(a=e.horizontalFill)&&void 0!==a?a:Tk.horizontalFill,vertical:null!==(s=e.vertical)&&void 0!==s?s:Tk.vertical,verticalFill:null!==(c=e.verticalFill)&&void 0!==c?c:Tk.verticalFill,x:gn(e.x)?e.x:p.left,y:gn(e.y)?e.y:p.top,width:gn(e.width)?e.width:p.width,height:gn(e.height)?e.height:p.height}),m=h.x,y=h.y,g=h.width,v=h.height,b=h.syncWithTicks,x=h.horizontalValues,w=h.verticalValues,j=(l=n.useContext(sO),jn(l)),O=(u=n.useContext(cO),iO(u,(function(e){return wj(e.domain,Number.isFinite)}))||jn(u));if(!gn(g)||g<=0||!gn(v)||v<=0||!gn(m)||m!==+m||!gn(y)||y!==+y)return null;var k=h.verticalCoordinatesGenerator||_k,S=h.horizontalCoordinatesGenerator||Ck,P=h.horizontalPoints,A=h.verticalPoints;if((!P||!P.length)&&ft(S)){var N=x&&x.length,E=S({yAxis:O?jk(jk({},O),{},{ticks:N?x:O.ticks}):void 0,width:f,height:d,offset:p},!!N||b);ni(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(xk(E),"]")),Array.isArray(E)&&(P=E)}if((!A||!A.length)&&ft(k)){var M=w&&w.length,_=k({xAxis:j?jk(jk({},j),{},{ticks:M?w:j.ticks}):void 0,width:f,height:d,offset:p},!!M||b);ni(Array.isArray(_),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(xk(_),"]")),Array.isArray(_)&&(A=_)}return o.createElement("g",{className:"recharts-cartesian-grid"},o.createElement(Sk,{fill:h.fill,fillOpacity:h.fillOpacity,x:h.x,y:h.y,width:h.width,height:h.height,ry:h.ry}),o.createElement(Ak,Ok({},h,{offset:p,horizontalPoints:P,xAxis:j,yAxis:O})),o.createElement(Nk,Ok({},h,{offset:p,verticalPoints:A,xAxis:j,yAxis:O})),o.createElement(Ek,Ok({},h,{horizontalPoints:P})),o.createElement(Mk,Ok({},h,{verticalPoints:A})))}Dk.displayName="CartesianGrid";var Ik=["type","layout","connectNulls","ref"],Bk=["key"];function $k(e){return($k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Rk(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Lk(){return Lk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Lk.apply(this,arguments)}function zk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Fk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?zk(Object(r),!0).forEach((function(t){Hk(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zk(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Uk(e){return function(e){if(Array.isArray(e))return qk(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return qk(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?qk(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qk(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Wk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xk(n.key),n)}}function Vk(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Vk=function(){return!!e})()}function Gk(e){return(Gk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Yk(e,t){return(Yk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Hk(e,t,r){return(t=Xk(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xk(e){var t=function(e){if("object"!=$k(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=$k(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==$k(t)?t:t+""}var Kk=function(){function e(){var t,r,n,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];return Hk((r=this,n=e,i=[].concat(o),n=Gk(n),t=function(e,t){if(t&&("object"===$k(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(r,Vk()?Reflect.construct(n,i||[],Gk(r).constructor):n.apply(r,i))),"state",{isAnimationFinished:!0,totalLength:0}),Hk(t,"generateSimpleStrokeDasharray",(function(e,t){return"".concat(t,"px ").concat(e-t,"px")})),Hk(t,"getStrokeDasharray",(function(r,n,i){var a=i.reduce((function(e,t){return e+t}));if(!a)return t.generateSimpleStrokeDasharray(n,r);for(var o=Math.floor(r/a),s=r%a,c=n-r,l=[],u=0,f=0;u<i.length;f+=i[u],++u)if(f+i[u]>s){l=[].concat(Uk(i.slice(0,u)),[s-f]);break}var d=l.length%2==0?[0,c]:[c];return[].concat(Uk(e.repeat(i,o)),Uk(l),d).map((function(e){return"".concat(e,"px")})).join(", ")})),Hk(t,"id",xn("recharts-line-")),Hk(t,"pathRef",(function(e){t.mainCurve=e})),Hk(t,"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()})),Hk(t,"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()})),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yk(e,t)}(e,n.PureComponent),t=e,i=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(Uk(e),[0]):e,n=[],i=0;i<t;++i)n=[].concat(Uk(n),Uk(r));return n}},{key:"renderDotItem",value:function(e,t){var r;if(o.isValidElement(e))r=o.cloneElement(e,t);else if(ft(e))r=e(t);else{var n=t.key,i=Rk(t,Bk),a=P("recharts-line-dot","boolean"!=typeof e?e.className:"");r=o.createElement(Wx,Lk({key:n},i,{className:a}))}return r}}],(r=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,i=r.xAxis,a=r.yAxis,s=r.layout,c=qn(r.children,Wg);if(!c)return null;var l=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:tv(e.payload,t)}},u={clipPath:e?"url(#clipPath-".concat(t,")"):null};return o.createElement(ri,u,c.map((function(e){return o.cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:n,xAxis:i,yAxis:a,layout:s,dataPointFormatter:l})})))}},{key:"renderDots",value:function(t,r,n){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,s=i.points,c=i.dataKey,l=Yn(this.props,!1),u=Yn(a,!0),f=s.map((function(t,r){var n=Fk(Fk(Fk({key:"dot-".concat(r),r:3},l),u),{},{index:r,cx:t.x,cy:t.y,value:t.value,dataKey:c,payload:t.payload,points:s});return e.renderDotItem(a,n)})),d={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(n,")"):null};return o.createElement(ri,Lk({className:"recharts-line-dots",key:"dots"},d),f)}},{key:"renderCurveStatically",value:function(e,t,r,n){var i=this.props,a=i.type,s=i.layout,c=i.connectNulls;i.ref;var l=Rk(i,Ik),u=Fk(Fk(Fk({},Yn(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},n),{},{type:a,layout:s,connectNulls:c});return o.createElement(db,Lk({},u,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,n=this.props,i=n.points,a=n.strokeDasharray,s=n.isAnimationActive,c=n.animationBegin,l=n.animationDuration,u=n.animationEasing,f=n.animationId,d=n.animateNewValues,p=n.width,h=n.height,m=this.state,y=m.prevPoints,g=m.totalLength;return o.createElement(Tx,{begin:c,duration:l,isActive:s,easing:u,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(n){var o=n.t;if(y){var s=y.length/i.length,c=i.map((function(e,t){var r=Math.floor(t*s);if(y[r]){var n=y[r],i=On(n.x,e.x),a=On(n.y,e.y);return Fk(Fk({},e),{},{x:i(o),y:a(o)})}if(d){var c=On(2*p,e.x),l=On(h/2,e.y);return Fk(Fk({},e),{},{x:c(o),y:l(o)})}return Fk(Fk({},e),{},{x:e.x,y:e.y})}));return r.renderCurveStatically(c,e,t)}var l,u=On(0,g)(o);if(a){var f="".concat(a).split(/[,\s]+/gim).map((function(e){return parseFloat(e)}));l=r.getStrokeDasharray(u,g,f)}else l=r.generateSimpleStrokeDasharray(g,u);return r.renderCurveStatically(i,e,t,{strokeDasharray:l})}))}},{key:"renderCurve",value:function(e,t){var r=this.props,n=r.points,i=r.isAnimationActive,a=this.state,o=a.prevPoints,s=a.totalLength;return i&&n&&n.length&&(!o&&s>0||!By(o,n))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,n=t.dot,i=t.points,a=t.className,s=t.xAxis,c=t.yAxis,l=t.top,u=t.left,f=t.width,d=t.height,p=t.isAnimationActive,h=t.id;if(r||!i||!i.length)return null;var m=this.state.isAnimationFinished,y=1===i.length,g=P("recharts-line",a),v=s&&s.allowDataOverflow,b=c&&c.allowDataOverflow,x=v||b,w=Rr(h)?this.id:h,j=null!==(e=Yn(n,!1))&&void 0!==e?e:{r:3,strokeWidth:2},O=j.r,k=void 0===O?3:O,S=j.strokeWidth,A=void 0===S?2:S,N=(function(e){return e&&"object"===$n(e)&&"clipDot"in e}(n)?n:{}).clipDot,E=void 0===N||N,M=2*k+A;return o.createElement(ri,{className:g},v||b?o.createElement("defs",null,o.createElement("clipPath",{id:"clipPath-".concat(w)},o.createElement("rect",{x:v?u:u-f/2,y:b?l:l-d/2,width:v?f:2*f,height:b?d:2*d})),!E&&o.createElement("clipPath",{id:"clipPath-dots-".concat(w)},o.createElement("rect",{x:u-M/2,y:l-M/2,width:f+M,height:d+M}))):null,!y&&this.renderCurve(x,w),this.renderErrorBar(x,w),(y||n)&&this.renderDots(x,E,w),(!p||m)&&Xv.renderCallByParent(this.props,i))}}])&&Wk(t.prototype,r),i&&Wk(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r,i}();function Zk(e){return(Zk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Jk(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Jk=function(){return!!e})()}function Qk(e){return(Qk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eS(e,t){return(eS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tS(e,t,r){return(t=rS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rS(e){var t=function(e){if("object"!=Zk(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Zk(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Zk(t)?t:t+""}function nS(){return nS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nS.apply(this,arguments)}function iS(e){var t=e.xAxisId,r=gO(),n=vO(),i=mO(t);return null==i?null:o.createElement(gk,nS({},i,{className:P("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return cv(e,!0)}}))}Hk(Kk,"displayName","Line"),Hk(Kk,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!Su,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),Hk(Kk,"getComposedData",(function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,s=e.bandSize,c=e.displayedData,l=e.offset,u=t.layout;return Fk({points:c.map((function(e,t){var c=tv(e,o);return"horizontal"===u?{x:hv({axis:r,ticks:i,bandSize:s,entry:e,index:t}),y:Rr(c)?null:n.scale(c),value:c,payload:e}:{x:Rr(c)?null:r.scale(c),y:hv({axis:n,ticks:a,bandSize:s,entry:e,index:t}),value:c,payload:e}})),layout:u},l)}));var aS=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=Qk(r=e),function(e,t){if(t&&("object"===Zk(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,Jk()?Reflect.construct(r,n||[],Qk(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eS(e,t)}(e,o.Component),t=e,(r=[{key:"render",value:function(){return o.createElement(iS,this.props)}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,rS(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function oS(e){return(oS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sS(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(sS=function(){return!!e})()}function cS(e){return(cS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function lS(e,t){return(lS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function uS(e,t,r){return(t=fS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fS(e){var t=function(e){if("object"!=oS(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=oS(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==oS(t)?t:t+""}function dS(){return dS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dS.apply(this,arguments)}tS(aS,"displayName","XAxis"),tS(aS,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var pS=function(e){var t=e.yAxisId,r=gO(),n=vO(),i=yO(t);return null==i?null:o.createElement(gk,dS({},i,{className:P("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return cv(e,!0)}}))},hS=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,n=arguments,r=cS(r=e),function(e,t){if(t&&("object"===oS(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(t,sS()?Reflect.construct(r,n||[],cS(t).constructor):r.apply(t,n));var t,r,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lS(e,t)}(e,o.Component),t=e,(r=[{key:"render",value:function(){return o.createElement(pS,this.props)}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fS(n.key),n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();function mS(e){return function(e){if(Array.isArray(e))return yS(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return yS(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?yS(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yS(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}uS(hS,"displayName","YAxis"),uS(hS,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var gS=function(e,t,r,n,i){var a=qn(e,MO),o=qn(e,zO),s=[].concat(mS(a),mS(o)),c=qn(e,KO),l="".concat(n,"Id"),u=n[0],f=t;if(s.length&&(f=s.reduce((function(e,t){if(t.props[l]===r&&uj(t.props,"extendDomain")&&gn(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e}),f)),c.length){var d="".concat(u,"1"),p="".concat(u,"2");f=c.reduce((function(e,t){if(t.props[l]===r&&uj(t.props,"extendDomain")&&gn(t.props[d])&&gn(t.props[p])){var n=t.props[d],i=t.props[p];return[Math.min(e[0],n,i),Math.max(e[1],n,i)]}return e}),f)}return i&&i.length&&(f=i.reduce((function(e,t){return gn(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e}),f)),f},vS={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw new TypeError("The listener must be a function");var s=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0===--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=new Array(a);i<a;i++)o[i]=n[i].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,a,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(l=1,c=new Array(f-1);l<f;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var d,p=u.length;for(l=0;l<p;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),f){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,i);break;default:if(!c)for(d=1,c=new Array(f-1);d<f;d++)c[d-1]=arguments[d];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||o(this,a);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||i&&!s[c].once||n&&s[c].context!==n)&&l.push(s[c]);l.length?this._events[a]=1===l.length?l[0]:l:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s}(vS);var bS=new(a(vS.exports)),xS="recharts.syncMouseEvents";function wS(e){return(wS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function jS(e,t,r){return(t=OS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function OS(e){var t=function(e){if("object"!=wS(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=wS(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==wS(t)?t:t+""}var kS=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),jS(this,"activeIndex",0),jS(this,"coordinateList",[]),jS(this,"layout","horizontal")},(t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,i=e.container,a=void 0===i?null:i,o=e.layout,s=void 0===o?null:o,c=e.offset,l=void 0===c?null:c,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!==(t=null!=n?n:this.coordinateList)&&void 0!==t?t:[],this.container=null!=a?a:this.container,this.layout=null!=s?s:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){var e,t;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,i=r.y,a=r.height,o=this.coordinateList[this.activeIndex].coordinate,s=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,c=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,l=n+o+s,u=i+this.offset.top+a/2+c;this.mouseHandlerCallback({pageX:l,pageY:u})}}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,OS(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();function SS(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle;return{points:[Nv(t,r,n,i),Nv(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function PS(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return SS(t);var s=t.cx,c=t.cy,l=t.innerRadius,u=t.outerRadius,f=t.angle,d=Nv(s,c,l,f),p=Nv(s,c,u,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}function AS(e){return(AS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function NS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ES(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?NS(Object(r),!0).forEach((function(t){var n,i,a,o;n=e,i=t,a=r[t],o=function(e,t){if("object"!=AS(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=AS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i,"string"),(i="symbol"==AS(o)?o:o+"")in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):NS(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function MS(e){var t,r,i,a=e.element,o=e.tooltipEventType,s=e.isActive,c=e.activeCoordinate,l=e.activePayload,u=e.offset,f=e.activeTooltipIndex,d=e.tooltipAxisBandSize,p=e.layout,h=e.chartName,m=null!==(t=a.props.cursor)&&void 0!==t?t:null===(r=a.type.defaultProps)||void 0===r?void 0:r.cursor;if(!a||!m||!s||!c||"ScatterChart"!==h&&"axis"!==o)return null;var y=db;if("ScatterChart"===h)i=c,y=Kx;else if("BarChart"===h)i=function(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:r.left+.5,y:"horizontal"===e?r.top+.5:t.y-i,width:"horizontal"===e?n:r.width-1,height:"horizontal"===e?r.height-1:n}}(p,c,u,d),y=Ux;else if("radial"===p){var g=SS(c),v=g.cx,b=g.cy,x=g.radius;i={cx:v,cy:b,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:x,outerRadius:x},y=nb}else i={points:PS(p,c,u)},y=db;var w=ES(ES(ES(ES({stroke:"#ccc",pointerEvents:"none"},u),i),Yn(m,!1)),{},{payload:l,payloadIndex:f,className:P("recharts-tooltip-cursor",m.className)});return n.isValidElement(m)?n.cloneElement(m,w):n.createElement(y,w)}var _S=["item"],CS=["children","className","width","height","style","compact","title","desc"];function TS(e){return(TS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function DS(){return DS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},DS.apply(this,arguments)}function IS(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t);else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(e,t)||FS(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function BS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function $S(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return($S=function(){return!!e})()}function RS(e){return(RS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function LS(e,t){return(LS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function zS(e){return function(e){if(Array.isArray(e))return US(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||FS(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function FS(e,t){if(e){if("string"==typeof e)return US(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?US(e,t):void 0}}function US(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function qS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function WS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?qS(Object(r),!0).forEach((function(t){VS(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qS(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function VS(e,t,r){return(t=GS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GS(e){var t=function(e,t){if("object"!=TS(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=TS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==TS(t)?t:t+""}var YS={xAxis:["bottom","top"],yAxis:["left","right"]},HS={width:"100%",height:"100%"},XS={x:0,y:0};function KS(e){return e}var ZS=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,i=t.dataEndIndex,a=(null!=r?r:[]).reduce((function(e,t){var r=t.props.data;return r&&r.length?[].concat(zS(e),zS(r)):e}),[]);return a.length>0?a:e&&e.length&&gn(n)&&gn(i)?e.slice(n,i+1):[]};function JS(e){return"number"===e?[0,"auto"]:void 0}var QS=function(e,t,r,n){var i=e.graphicalItems,a=e.tooltipAxis,o=ZS(t,e);return r<0||!i||!i.length||r>=o.length?null:i.reduce((function(i,s){var c,l,u=null!==(c=s.props.data)&&void 0!==c?c:t;return u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),(l=a.dataKey&&!a.allowDuplicatedCategory?kn(void 0===u?o:u,a.dataKey,n):u&&u[r]||o[r])?[].concat(zS(i),[jv(s,l)]):i}),[])},eP=function(e,t,r,n){var i=n||{x:e.chartX,y:e.chartY},a=function(e,t){return"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius}(i,r),o=e.orderedTooltipTicks,s=e.tooltipAxis,c=e.tooltipTicks,l=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,a=-1,o=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;if(o<=1)return 0;if(i&&"angleAxis"===i.axisType&&Math.abs(Math.abs(i.range[1]-i.range[0])-360)<=1e-6)for(var s=i.range,c=0;c<o;c++){var l=c>0?n[c-1].coordinate:n[o-1].coordinate,u=n[c].coordinate,f=c>=o-1?n[0].coordinate:n[c+1].coordinate,d=void 0;if(mn(u-l)!==mn(f-u)){var p=[];if(mn(f-u)===mn(s[1]-s[0])){d=f;var h=u+s[1]-s[0];p[0]=Math.min(h,(h+l)/2),p[1]=Math.max(h,(h+l)/2)}else{d=l;var m=f+s[1]-s[0];p[0]=Math.min(u,(m+u)/2),p[1]=Math.max(u,(m+u)/2)}var y=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>y[0]&&e<=y[1]||e>=p[0]&&e<=p[1]){a=n[c].index;break}}else{var g=Math.min(l,f),v=Math.max(l,f);if(e>(g+u)/2&&e<=(v+u)/2){a=n[c].index;break}}}else for(var b=0;b<o;b++)if(0===b&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b>0&&b<o-1&&e>(r[b].coordinate+r[b-1].coordinate)/2&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b===o-1&&e>(r[b].coordinate+r[b-1].coordinate)/2){a=r[b].index;break}return a}(a,o,c,s);if(l>=0&&c){var u=c[l]&&c[l].value,f=QS(e,t,l,u),d=function(e,t,r,n){var i=t.find((function(e){return e&&e.index===r}));if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,o=n.radius;return WS(WS(WS({},n),Nv(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var s=i.coordinate,c=n.angle;return WS(WS(WS({},n),Nv(n.cx,n.cy,s,c)),{},{angle:c,radius:s})}return XS}(r,o,l,i);return{activeTooltipIndex:l,activeLabel:u,activePayload:f,activeCoordinate:d}}return null},tP=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,i=t.AxisComp,a=t.graphicalItems,o=t.stackGroups,s=t.dataStartIndex,c=t.dataEndIndex,l=e.children,u="".concat(n,"Id"),f=qn(l,i),d={};return f&&f.length?d=function(e,t){var r=t.axes,n=t.graphicalItems,i=t.axisType,a=t.axisIdKey,o=t.stackGroups,s=t.dataStartIndex,c=t.dataEndIndex,l=e.layout,u=e.children,f=e.stackOffset,d=ov(l,i);return r.reduce((function(t,r){var p,h=void 0!==r.type.defaultProps?WS(WS({},r.type.defaultProps),r.props):r.props,m=h.type,y=h.dataKey,g=h.allowDataOverflow,v=h.allowDuplicatedCategory,b=h.scale,x=h.ticks,w=h.includeHidden,j=h[a];if(t[j])return t;var O,k,S,P=ZS(e.data,{graphicalItems:n.filter((function(e){var t;return(a in e.props?e.props[a]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[a])===j})),dataStartIndex:s,dataEndIndex:c}),A=P.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],i=null==e?void 0:e[1];if(n&&i&&gn(n)&&gn(i))return!0}return!1})(h.domain,g,m)&&(O=bv(h.domain,null,g),!d||"number"!==m&&"auto"===b||(S=rv(P,y,"category")));var N=JS(m);if(!O||0===O.length){var E,M=null!==(E=h.domain)&&void 0!==E?E:N;if(y){if(O=rv(P,y,m),"category"===m&&d){var _=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1}(O);v&&_?(k=O,O=Lw(0,A)):v||(O=wv(M,O,r).reduce((function(e,t){return e.indexOf(t)>=0?e:[].concat(zS(e),[t])}),[]))}else if("category"===m)O=v?O.filter((function(e){return""!==e&&!Rr(e)})):wv(M,O,r).reduce((function(e,t){return e.indexOf(t)>=0||""===t||Rr(t)?e:[].concat(zS(e),[t])}),[]);else if("number"===m){var C=function(e,t,r,n,i){var a=t.map((function(t){return iv(e,t,r,i,n)})).filter((function(e){return!Rr(e)}));return a&&a.length?a.reduce((function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}),[1/0,-1/0]):null}(P,n.filter((function(e){var t,r,n=a in e.props?e.props[a]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[a],i="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===j&&(w||!i)})),y,i,l);C&&(O=C)}!d||"number"!==m&&"auto"===b||(S=rv(P,y,"category"))}else O=d?Lw(0,A):o&&o[j]&&o[j].hasStack&&"number"===m?"expand"===f?[0,1]:yv(o[j].stackGroups,s,c):av(P,n.filter((function(e){var t=a in e.props?e.props[a]:e.type.defaultProps[a],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===j&&(w||!r)})),m,l,!0);if("number"===m)O=gS(u,O,j,i,x),M&&(O=bv(M,O,g));else if("category"===m&&M){var T=M;O.every((function(e){return T.indexOf(e)>=0}))&&(O=T)}}return WS(WS({},t),{},VS({},j,WS(WS({},h),{},{axisType:i,domain:O,categoricalDomain:S,duplicateDomain:k,originalDomain:null!==(p=h.domain)&&void 0!==p?p:N,isCategorical:d,layout:l})))}),{})}(e,{axes:f,graphicalItems:a,axisType:n,axisIdKey:u,stackGroups:o,dataStartIndex:s,dataEndIndex:c}):a&&a.length&&(d=function(e,t){var r=t.graphicalItems,n=t.Axis,i=t.axisType,a=t.axisIdKey,o=t.stackGroups,s=t.dataStartIndex,c=t.dataEndIndex,l=e.layout,u=e.children,f=ZS(e.data,{graphicalItems:r,dataStartIndex:s,dataEndIndex:c}),d=f.length,p=ov(l,i),h=-1;return r.reduce((function(e,t){var m,y=(void 0!==t.type.defaultProps?WS(WS({},t.type.defaultProps),t.props):t.props)[a],g=JS("number");return e[y]?e:(h++,p?m=Lw(0,d):o&&o[y]&&o[y].hasStack?(m=yv(o[y].stackGroups,s,c),m=gS(u,m,y,i)):(m=bv(g,av(f,r.filter((function(e){var t,r,n=a in e.props?e.props[a]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[a],i="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===y&&!i})),"number",l),n.defaultProps.allowDataOverflow),m=gS(u,m,y,i)),WS(WS({},e),{},VS({},y,WS(WS({axisType:i},n.defaultProps),{},{hide:!0,orientation:$r(YS,"".concat(i,".").concat(h%2),null),domain:m,originalDomain:g,isCategorical:p,layout:l}))))}),{})}(e,{Axis:i,graphicalItems:a,axisType:n,axisIdKey:u,stackGroups:o,dataStartIndex:s,dataEndIndex:c})),d},rP=function(e){var t=e.children,r=e.defaultShowTooltip,n=Wn(t,rj),i=0,a=0;return e.data&&0!==e.data.length&&(a=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(a=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:a,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},nP=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},iP=function(e,t){return"xAxis"===t?e[t].width:"yAxis"===t?e[t].height:void 0},aP=function(e){var t=e.chartName,r=e.GraphicalChild,i=e.defaultTooltipEventType,a=void 0===i?"axis":i,s=e.validateTooltipEventTypes,c=void 0===s?["axis"]:s,l=e.axisComponents,u=e.legendContent,f=e.formatAxisMap,d=e.defaultProps,p=function(e,n){var i=e.props,a=e.dataStartIndex,o=e.dataEndIndex,s=e.updateId;if(!Vn({props:i}))return null;var c=i.children,u=i.layout,d=i.stackOffset,p=i.data,h=i.reverseStackOrder,m=nP(u),y=m.numericAxisName,g=m.cateAxisName,v=qn(c,r),b=function(e,t,r,n,i,a){if(!e)return null;var o=(a?t.reverse():t).reduce((function(e,t){var i,a=null!==(i=t.type)&&void 0!==i&&i.defaultProps?Qg(Qg({},t.type.defaultProps),t.props):t.props,o=a.stackId;if(a.hide)return e;var s=a[r],c=e[s]||{hasStack:!1,stackGroups:{}};if(vn(o)){var l=c.stackGroups[o]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(t),c.hasStack=!0,c.stackGroups[o]=l}else c.stackGroups[xn("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return Qg(Qg({},e),{},ev({},s,c))}),{});return Object.keys(o).reduce((function(t,a){var s=o[a];return s.hasStack&&(s.stackGroups=Object.keys(s.stackGroups).reduce((function(t,a){var o=s.stackGroups[a];return Qg(Qg({},t),{},ev({},a,{numericAxisId:r,cateAxisId:n,items:o.items,stackedData:pv(e,o.items,i)}))}),{})),Qg(Qg({},t),{},ev({},a,s))}),{})}(p,v,"".concat(y,"Id"),"".concat(g,"Id"),d,h),x=l.reduce((function(e,t){var r="".concat(t.axisType,"Map");return WS(WS({},e),{},VS({},r,tP(i,WS(WS({},t),{},{graphicalItems:v,stackGroups:t.axisType===y&&b,dataStartIndex:a,dataEndIndex:o}))))}),{}),w=function(e,t){var r=e.props,n=(e.graphicalItems,e.xAxisMap),i=void 0===n?{}:n,a=e.yAxisMap,o=void 0===a?{}:a,s=r.width,c=r.height,l=r.children,u=r.margin||{},f=Wn(l,rj),d=Wn(l,ll),p=Object.keys(o).reduce((function(e,t){var r=o[t],n=r.orientation;return r.mirror||r.hide?e:WS(WS({},e),{},VS({},n,e[n]+r.width))}),{left:u.left||0,right:u.right||0}),h=Object.keys(i).reduce((function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:WS(WS({},e),{},VS({},n,$r(e,"".concat(n))+r.height))}),{top:u.top||0,bottom:u.bottom||0}),m=WS(WS({},h),p),y=m.bottom;f&&(m.bottom+=f.props.height||rj.defaultProps.height),d&&t&&(m=function(e,t,r,n){var i=r.children,a=r.width,o=r.margin,s=a-(o.left||0)-(o.right||0),c=Hg({children:i,legendWidth:s});if(c){var l=n||{},u=l.width,f=l.height,d=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==d&&gn(e[d]))return Qg(Qg({},e),{},ev({},d,e[d]+(u||0)));if(("horizontal"===h||"vertical"===h&&"center"===d)&&"middle"!==p&&gn(e[p]))return Qg(Qg({},e),{},ev({},p,e[p]+(f||0)))}return e}(m,0,r,t));var g=s-m.left-m.right,v=c-m.top-m.bottom;return WS(WS({brushBottom:y},m),{},{width:Math.max(g,0),height:Math.max(v,0)})}(WS(WS({},x),{},{props:i,graphicalItems:v}),null==n?void 0:n.legendBBox);Object.keys(x).forEach((function(e){x[e]=f(i,x[e],w,e.replace("Map",""),t)}));var j,O,k=x["".concat(g,"Map")],S=(j=jn(k),{tooltipTicks:O=cv(j,!1,!0),orderedTooltipTicks:ru(O,(function(e){return e.coordinate})),tooltipAxis:j,tooltipAxisBandSize:xv(j,O)}),P=function(e,t){var r=t.graphicalItems,n=t.stackGroups,i=t.offset,a=t.updateId,o=t.dataStartIndex,s=t.dataEndIndex,c=e.barSize,u=e.layout,f=e.barGap,d=e.barCategoryGap,p=e.maxBarSize,h=nP(u),m=h.numericAxisName,y=h.cateAxisName,g=function(e){return!(!e||!e.length)&&e.some((function(e){var t=Ln(e&&e.type);return t&&t.indexOf("Bar")>=0}))}(r),v=[];return r.forEach((function(r,h){var b=ZS(e.data,{graphicalItems:[r],dataStartIndex:o,dataEndIndex:s}),x=void 0!==r.type.defaultProps?WS(WS({},r.type.defaultProps),r.props):r.props,w=x.dataKey,j=x.maxBarSize,O=x["".concat(m,"Id")],k=x["".concat(y,"Id")],S=l.reduce((function(e,r){var n=t["".concat(r.axisType,"Map")],i=x["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||Dg();var a=n[i];return WS(WS({},e),{},VS(VS({},r.axisType,a),"".concat(r.axisType,"Ticks"),cv(a)))}),{}),P=S[y],A=S["".concat(y,"Ticks")],N=n&&n[O]&&n[O].hasStack&&function(e,t){var r,n=(null!==(r=e.type)&&void 0!==r&&r.defaultProps?Qg(Qg({},e.type.defaultProps),e.props):e.props).stackId;if(vn(n)){var i=t[n];if(i){var a=i.items.indexOf(e);return a>=0?i.stackedData[a]:null}}return null}(r,n[O].stackGroups),E=Ln(r.type).indexOf("Bar")>=0,M=xv(P,A),_=[],C=g&&function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var a={},o=Object.keys(i),s=0,c=o.length;s<c;s++)for(var l=i[o[s]].stackGroups,u=Object.keys(l),f=0,d=u.length;f<d;f++){var p=l[u[f]],h=p.items,m=p.cateAxisId,y=h.filter((function(e){return Ln(e.type).indexOf("Bar")>=0}));if(y&&y.length){var g=y[0].type.defaultProps,v=void 0!==g?Qg(Qg({},g),y[0].props):y[0].props,b=v.barSize,x=v[m];a[x]||(a[x]=[]);var w=Rr(b)?t:b;a[x].push({item:y[0],stackList:y.slice(1),barSize:Rr(w)?void 0:wn(w,r,0)})}}return a}({barSize:c,stackGroups:n,totalSize:iP(S,y)});if(E){var T,D,I=Rr(j)?p:j,B=null!==(T=null!==(D=xv(P,A,!0))&&void 0!==D?D:I)&&void 0!==T?T:0;_=function(e){var t=e.barGap,r=e.barCategoryGap,n=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,o=e.maxBarSize,s=a.length;if(s<1)return null;var c,l=wn(t,n,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,d=n/s,p=a.reduce((function(e,t){return e+t.barSize||0}),0);(p+=(s-1)*l)>=n&&(p-=(s-1)*l,l=0),p>=n&&d>0&&(f=!0,p=s*(d*=.9));var h={offset:((n-p)/2|0)-l,size:0};c=a.reduce((function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+l,size:f?d:t.barSize}},n=[].concat(Kg(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach((function(e){n.push({item:e,position:h})})),n}),u)}else{var m=wn(r,n,0,!0);n-2*m-(s-1)*l<=0&&(l=0);var y=(n-2*m-(s-1)*l)/s;y>1&&(y>>=0);var g=o===+o?Math.min(y,o):y;c=a.reduce((function(e,t,r){var n=[].concat(Kg(e),[{item:t.item,position:{offset:m+(y+l)*r+(y-g)/2,size:g}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach((function(e){n.push({item:e,position:n[n.length-1].position})})),n}),u)}return c}({barGap:f,barCategoryGap:d,bandSize:B!==M?B:M,sizeList:C[k],maxBarSize:I}),B!==M&&(_=_.map((function(e){return WS(WS({},e),{},{position:WS(WS({},e.position),{},{offset:e.position.offset-B/2})})})))}var $,R,L=r&&r.type&&r.type.getComposedData;L&&v.push({props:WS(WS({},L(WS(WS({},S),{},{displayedData:b,props:e,dataKey:w,item:r,bandSize:M,barPosition:_,offset:i,stackedData:N,layout:u,dataStartIndex:o,dataEndIndex:s}))),{},VS(VS(VS({key:r.key||"item-".concat(h)},m,S[m]),y,S[y]),"animationId",a)),childIndex:($=r,R=e.children,Un(R).indexOf($)),item:r})})),v}(i,WS(WS({},x),{},{dataStartIndex:a,dataEndIndex:o,updateId:s,graphicalItems:v,stackGroups:b,offset:w}));return WS(WS({formattedGraphicalItems:P,graphicalItems:v,offset:w,stackGroups:b},S),x)},h=function(){function e(r){var i,a,s,c,l,f;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),VS((c=this,f=[r],l=RS(l=e),s=function(e,t){if(t&&("object"===TS(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(c,$S()?Reflect.construct(l,f||[],RS(c).constructor):l.apply(c,f))),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),VS(s,"accessibilityManager",new kS),VS(s,"handleLegendBBoxUpdate",(function(e){if(e){var t=s.state,r=t.dataStartIndex,n=t.dataEndIndex,i=t.updateId;s.setState(WS({legendBBox:e},p({props:s.props,dataStartIndex:r,dataEndIndex:n,updateId:i},WS(WS({},s.state),{},{legendBBox:e}))))}})),VS(s,"handleReceiveSyncEvent",(function(e,t,r){if(s.props.syncId===e){if(r===s.eventEmitterSymbol&&"function"!=typeof s.props.syncMethod)return;s.applySyncEvent(t)}})),VS(s,"handleBrushChange",(function(e){var t=e.startIndex,r=e.endIndex;if(t!==s.state.dataStartIndex||r!==s.state.dataEndIndex){var n=s.state.updateId;s.setState((function(){return WS({dataStartIndex:t,dataEndIndex:r},p({props:s.props,dataStartIndex:t,dataEndIndex:r,updateId:n},s.state))})),s.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}})),VS(s,"handleMouseEnter",(function(e){var t=s.getMouseInfo(e);if(t){var r=WS(WS({},t),{},{isTooltipActive:!0});s.setState(r),s.triggerSyncEvent(r);var n=s.props.onMouseEnter;ft(n)&&n(r,e)}})),VS(s,"triggeredAfterMouseMove",(function(e){var t=s.getMouseInfo(e),r=t?WS(WS({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};s.setState(r),s.triggerSyncEvent(r);var n=s.props.onMouseMove;ft(n)&&n(r,e)})),VS(s,"handleItemMouseEnter",(function(e){s.setState((function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}}))})),VS(s,"handleItemMouseLeave",(function(){s.setState((function(){return{isTooltipActive:!1}}))})),VS(s,"handleMouseMove",(function(e){e.persist(),s.throttleTriggeredAfterMouseMove(e)})),VS(s,"handleMouseLeave",(function(e){s.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};s.setState(t),s.triggerSyncEvent(t);var r=s.props.onMouseLeave;ft(r)&&r(t,e)})),VS(s,"handleOuterEvent",(function(e){var t,r=function(e){var t=e&&e.type;return t&&Rn[t]?Rn[t]:null}(e),n=$r(s.props,"".concat(r));r&&ft(n)&&n(null!==(t=/.*touch.*/i.test(r)?s.getMouseInfo(e.changedTouches[0]):s.getMouseInfo(e))&&void 0!==t?t:{},e)})),VS(s,"handleClick",(function(e){var t=s.getMouseInfo(e);if(t){var r=WS(WS({},t),{},{isTooltipActive:!0});s.setState(r),s.triggerSyncEvent(r);var n=s.props.onClick;ft(n)&&n(r,e)}})),VS(s,"handleMouseDown",(function(e){var t=s.props.onMouseDown;ft(t)&&t(s.getMouseInfo(e),e)})),VS(s,"handleMouseUp",(function(e){var t=s.props.onMouseUp;ft(t)&&t(s.getMouseInfo(e),e)})),VS(s,"handleTouchMove",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&s.throttleTriggeredAfterMouseMove(e.changedTouches[0])})),VS(s,"handleTouchStart",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&s.handleMouseDown(e.changedTouches[0])})),VS(s,"handleTouchEnd",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&s.handleMouseUp(e.changedTouches[0])})),VS(s,"handleDoubleClick",(function(e){var t=s.props.onDoubleClick;ft(t)&&t(s.getMouseInfo(e),e)})),VS(s,"handleContextMenu",(function(e){var t=s.props.onContextMenu;ft(t)&&t(s.getMouseInfo(e),e)})),VS(s,"triggerSyncEvent",(function(e){void 0!==s.props.syncId&&bS.emit(xS,s.props.syncId,e,s.eventEmitterSymbol)})),VS(s,"applySyncEvent",(function(e){var t=s.props,r=t.layout,n=t.syncMethod,i=s.state.updateId,a=e.dataStartIndex,o=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)s.setState(WS({dataStartIndex:a,dataEndIndex:o},p({props:s.props,dataStartIndex:a,dataEndIndex:o,updateId:i},s.state)));else if(void 0!==e.activeTooltipIndex){var c=e.chartX,l=e.chartY,u=e.activeTooltipIndex,f=s.state,d=f.offset,h=f.tooltipTicks;if(!d)return;if("function"==typeof n)u=n(h,e);else if("value"===n){u=-1;for(var m=0;m<h.length;m++)if(h[m].value===e.activeLabel){u=m;break}}var y=WS(WS({},d),{},{x:d.left,y:d.top}),g=Math.min(c,y.x+y.width),v=Math.min(l,y.y+y.height),b=h[u]&&h[u].value,x=QS(s.state,s.props.data,u),w=h[u]?{x:"horizontal"===r?h[u].coordinate:g,y:"horizontal"===r?v:h[u].coordinate}:XS;s.setState(WS(WS({},e),{},{activeLabel:b,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else s.setState(e)})),VS(s,"renderCursor",(function(e){var r,n=s.state,i=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,l=n.offset,u=n.activeTooltipIndex,f=n.tooltipAxisBandSize,d=s.getTooltipEventType(),p=null!==(r=e.props.active)&&void 0!==r?r:i,h=s.props.layout,m=e.key||"_recharts-cursor";return o.createElement(MS,{key:m,activeCoordinate:a,activePayload:c,activeTooltipIndex:u,chartName:t,element:e,isActive:p,layout:h,offset:l,tooltipAxisBandSize:f,tooltipEventType:d})})),VS(s,"renderPolarAxis",(function(e,t,r){var i=$r(e,"type.axisType"),a=$r(s.state,"".concat(i,"Map")),o=e.type.defaultProps,c=void 0!==o?WS(WS({},o),e.props):e.props,l=a&&a[c["".concat(i,"Id")]];return n.cloneElement(e,WS(WS({},l),{},{className:P(i,l.className),key:e.key||"".concat(t,"-").concat(r),ticks:cv(l,!0)}))})),VS(s,"renderPolarGrid",(function(e){var t=e.props,r=t.radialLines,i=t.polarAngles,a=t.polarRadius,o=s.state,c=o.radiusAxisMap,l=o.angleAxisMap,u=jn(c),f=jn(l),d=f.cx,p=f.cy,h=f.innerRadius,m=f.outerRadius;return n.cloneElement(e,{polarAngles:Array.isArray(i)?i:cv(f,!0).map((function(e){return e.coordinate})),polarRadius:Array.isArray(a)?a:cv(u,!0).map((function(e){return e.coordinate})),cx:d,cy:p,innerRadius:h,outerRadius:m,key:e.key||"polar-grid",radialLines:r})})),VS(s,"renderLegend",(function(){var e=s.state.formattedGraphicalItems,t=s.props,r=t.children,i=t.width,a=t.height,o=s.props.margin||{},c=i-(o.left||0)-(o.right||0),l=Hg({children:r,formattedGraphicalItems:e,legendWidth:c,legendContent:u});if(!l)return null;var f=l.item,d=BS(l,_S);return n.cloneElement(f,WS(WS({},d),{},{chartWidth:i,chartHeight:a,margin:o,onBBoxUpdate:s.handleLegendBBoxUpdate}))})),VS(s,"renderTooltip",(function(){var e,t=s.props,r=t.children,i=t.accessibilityLayer,a=Wn(r,Iu);if(!a)return null;var o=s.state,c=o.isTooltipActive,l=o.activeCoordinate,u=o.activePayload,f=o.activeLabel,d=o.offset,p=null!==(e=a.props.active)&&void 0!==e?e:c;return n.cloneElement(a,{viewBox:WS(WS({},d),{},{x:d.left,y:d.top}),active:p,label:f,payload:p?u:[],coordinate:l,accessibilityLayer:i})})),VS(s,"renderBrush",(function(e){var t=s.props,r=t.margin,i=t.data,a=s.state,o=a.offset,c=a.dataStartIndex,l=a.dataEndIndex,u=a.updateId;return n.cloneElement(e,{key:e.key||"_recharts-brush",onChange:uv(s.handleBrushChange,e.props.onChange),data:i,x:gn(e.props.x)?e.props.x:o.left,y:gn(e.props.y)?e.props.y:o.top+o.height+o.brushBottom-(r.bottom||0),width:gn(e.props.width)?e.props.width:o.width,startIndex:c,endIndex:l,updateId:"brush-".concat(u)})})),VS(s,"renderReferenceElement",(function(e,t,r){if(!e)return null;var i=s.clipPathId,a=s.state,o=a.xAxisMap,c=a.yAxisMap,l=a.offset,u=e.type.defaultProps||{},f=e.props,d=f.xAxisId,p=void 0===d?u.xAxisId:d,h=f.yAxisId,m=void 0===h?u.yAxisId:h;return n.cloneElement(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:o[p],yAxis:c[m],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:i})})),VS(s,"renderActivePoints",(function(t){var r=t.item,n=t.activePoint,i=t.basePoint,a=t.childIndex,o=t.isRange,s=[],c=r.props.key,l=void 0!==r.item.type.defaultProps?WS(WS({},r.item.type.defaultProps),r.item.props):r.item.props,u=l.activeDot,f=WS(WS({index:a,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:nv(r.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},Yn(u,!1)),Cn(u));return s.push(e.renderActiveDot(u,f,"".concat(c,"-activePoint-").concat(a))),i?s.push(e.renderActiveDot(u,WS(WS({},f),{},{cx:i.x,cy:i.y}),"".concat(c,"-basePoint-").concat(a))):o&&s.push(null),s})),VS(s,"renderGraphicChild",(function(e,t,r){var i=s.filterFormatItem(e,t,r);if(!i)return null;var a=s.getTooltipEventType(),o=s.state,c=o.isTooltipActive,l=o.tooltipAxis,u=o.activeTooltipIndex,f=o.activeLabel,d=Wn(s.props.children,Iu),p=i.props,h=p.points,m=p.isRange,y=p.baseLine,g=void 0!==i.item.type.defaultProps?WS(WS({},i.item.type.defaultProps),i.item.props):i.item.props,v=g.activeDot,b=g.hide,x=g.activeBar,w=g.activeShape,j=Boolean(!b&&c&&d&&(v||x||w)),O={};"axis"!==a&&d&&"click"===d.props.trigger?O={onClick:uv(s.handleItemMouseEnter,e.props.onClick)}:"axis"!==a&&(O={onMouseLeave:uv(s.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:uv(s.handleItemMouseEnter,e.props.onMouseEnter)});var k=n.cloneElement(e,WS(WS({},i.props),O));if(j){if(!(u>=0)){var S,P=(null!==(S=s.getItemByXY(s.state.activeCoordinate))&&void 0!==S?S:{graphicalItem:k}).graphicalItem,A=P.item,N=void 0===A?e:A,E=P.childIndex,M=WS(WS(WS({},i.props),O),{},{activeIndex:E});return[n.cloneElement(N,M),null,null]}var _,C;if(l.dataKey&&!l.allowDuplicatedCategory){var T="function"==typeof l.dataKey?function(e){return"function"==typeof l.dataKey?l.dataKey(e.payload):null}:"payload.".concat(l.dataKey.toString());_=kn(h,T,f),C=m&&y&&kn(y,T,f)}else _=null==h?void 0:h[u],C=m&&y&&y[u];if(w||x){var D=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[n.cloneElement(e,WS(WS(WS({},i.props),O),{},{activeIndex:D})),null,null]}if(!Rr(_))return[k].concat(zS(s.renderActivePoints({item:i,activePoint:_,basePoint:C,childIndex:u,isRange:m})))}return m?[k,null,null]:[k,null]})),VS(s,"renderCustomized",(function(e,t,r){return n.cloneElement(e,WS(WS({key:"recharts-customized-".concat(r)},s.props),s.state))})),VS(s,"renderMap",{CartesianGrid:{handler:KS,once:!0},ReferenceArea:{handler:s.renderReferenceElement},ReferenceLine:{handler:KS},ReferenceDot:{handler:s.renderReferenceElement},XAxis:{handler:KS},YAxis:{handler:KS},Brush:{handler:s.renderBrush,once:!0},Bar:{handler:s.renderGraphicChild},Line:{handler:s.renderGraphicChild},Area:{handler:s.renderGraphicChild},Radar:{handler:s.renderGraphicChild},RadialBar:{handler:s.renderGraphicChild},Scatter:{handler:s.renderGraphicChild},Pie:{handler:s.renderGraphicChild},Funnel:{handler:s.renderGraphicChild},Tooltip:{handler:s.renderCursor,once:!0},PolarGrid:{handler:s.renderPolarGrid,once:!0},PolarAngleAxis:{handler:s.renderPolarAxis},PolarRadiusAxis:{handler:s.renderPolarAxis},Customized:{handler:s.renderCustomized}}),s.clipPathId="".concat(null!==(i=r.id)&&void 0!==i?i:xn("recharts"),"-clip"),s.throttleTriggeredAfterMouseMove=Qu(s.triggeredAfterMouseMove,null!==(a=r.throttleDelay)&&void 0!==a?a:1e3/60),s.state={},s}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&LS(e,t)}(e,n.Component),r=e,i=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,i=e.layout,a=Wn(t,Iu);if(a){var o=a.props.defaultIndex;if(!("number"!=typeof o||o<0||o>this.state.tooltipTicks.length-1)){var s=this.state.tooltipTicks[o]&&this.state.tooltipTicks[o].value,c=QS(this.state,r,o,s),l=this.state.tooltipTicks[o].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:u}:{y:l,x:u},d=this.state.formattedGraphicalItems.find((function(e){return"Scatter"===e.item.type.name}));d&&(f=WS(WS({},f),d.props.points[o].tooltipPosition),c=d.props.points[o].tooltipPayload);var p={activeTooltipIndex:o,isTooltipActive:!0,activeLabel:s,activePayload:c,activeCoordinate:f};this.setState(p),this.renderCursor(a),this.accessibilityManager.setIndex(o)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){return this.props.accessibilityLayer?(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(e){Hn([Wn(e.children,Iu)],[Wn(this.props.children,Iu)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=Wn(this.props.children,Iu);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return c.indexOf(t)>=0?t:a}return a}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t,r=this.container,n=r.getBoundingClientRect(),i=(t=n).top+window.scrollY-document.documentElement.clientTop,a=t.left+window.scrollX-document.documentElement.clientLeft,o={chartX:Math.round(e.pageX-a),chartY:Math.round(e.pageY-i)},s=n.width/r.offsetWidth||1,c=this.inRange(o.chartX,o.chartY,s);if(!c)return null;var l=this.state,u=l.xAxisMap,f=l.yAxisMap,d=this.getTooltipEventType(),p=eP(this.state,this.props.data,this.props.layout,c);if("axis"!==d&&u&&f){var h=jn(u).scale,m=jn(f).scale,y=h&&h.invert?h.invert(o.chartX):null,g=m&&m.invert?m.invert(o.chartY):null;return WS(WS({},o),{},{xValue:y,yValue:g},p)}return p?WS(WS({},o),p):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=e/r,a=t/r;if("horizontal"===n||"vertical"===n){var o=this.state.offset;return i>=o.left&&i<=o.left+o.width&&a>=o.top&&a<=o.top+o.height?{x:i,y:a}:null}var s=this.state,c=s.angleAxisMap,l=s.radiusAxisMap;if(c&&l){var u=jn(c);return Mv({x:i,y:a},u)}return null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=Wn(e,Iu),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),WS(WS({},Cn(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){bS.on(xS,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){bS.removeListener(xS,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,i=0,a=n.length;i<a;i++){var o=n[i];if(o.item===e||o.props.key===e.key||t===Ln(o.item.type)&&r===o.childIndex)return o}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,i=t.height,a=t.width;return o.createElement("defs",null,o.createElement("clipPath",{id:e},o.createElement("rect",{x:r,y:n,height:i,width:a})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce((function(e,t){var r=IS(t,2),n=r[0],i=r[1];return WS(WS({},e),{},VS({},n,i.scale))}),{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce((function(e,t){var r=IS(t,2),n=r[0],i=r[1];return WS(WS({},e),{},VS({},n,i.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var i=0,a=r.length;i<a;i++){var o=r[i],s=o.props,c=o.item,l=void 0!==c.type.defaultProps?WS(WS({},c.type.defaultProps),c.props):c.props,u=Ln(c.type);if("Bar"===u){var f=(s.data||[]).find((function(t){return zx(e,t)}));if(f)return{graphicalItem:o,payload:f}}else if("RadialBar"===u){var d=(s.data||[]).find((function(t){return Mv(e,t)}));if(d)return{graphicalItem:o,payload:d}}else if(Sw(o,n)||Pw(o,n)||Aw(o,n)){var p=_w({graphicalItem:o,activeTooltipItem:n,itemData:l.data}),h=void 0===l.activeIndex?p:l.activeIndex;return{graphicalItem:WS(WS({},o),{},{childIndex:h}),payload:Aw(o,n)?l.data[p]:o.props.data[p]}}}return null}},{key:"render",value:function(){var e=this;if(!Vn(this))return null;var t,r,n=this.props,i=n.children,a=n.className,s=n.width,c=n.height,l=n.style,u=n.compact,f=n.title,d=n.desc,p=BS(n,CS),h=Yn(p,!1);if(u)return o.createElement(hO,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o.createElement(Qn,DS({},h,{width:s,height:c,title:f,desc:d}),this.renderClipPath(),Kn(i,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,h.role=null!==(r=this.props.role)&&void 0!==r?r:"application",h.onKeyDown=function(t){e.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){e.accessibilityManager.focus()});var m=this.parseEventsOfWrapper();return o.createElement(hO,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o.createElement("div",DS({className:P("recharts-wrapper",a),style:WS({position:"relative",cursor:"default",width:s,height:c},l)},m,{ref:function(t){e.container=t}}),o.createElement(Qn,DS({},h,{width:s,height:c,title:f,desc:d,style:HS}),this.renderClipPath(),Kn(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],i&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,GS(n.key),n)}}(r.prototype,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i}();VS(h,"displayName",t),VS(h,"defaultProps",WS({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),VS(h,"getDerivedStateFromProps",(function(e,t){var r=e.dataKey,n=e.data,i=e.children,a=e.width,o=e.height,s=e.layout,c=e.stackOffset,l=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var d=rP(e);return WS(WS(WS({},d),{},{updateId:0},p(WS(WS({props:e},d),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:o,prevLayout:s,prevStackOffset:c,prevMargin:l,prevChildren:i})}if(r!==t.prevDataKey||n!==t.prevData||a!==t.prevWidth||o!==t.prevHeight||s!==t.prevLayout||c!==t.prevStackOffset||!Pn(l,t.prevMargin)){var h=rP(e),m={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},y=WS(WS({},eP(t,n,s)),{},{updateId:t.updateId+1}),g=WS(WS(WS({},h),m),y);return WS(WS(WS({},g),p(WS({props:e},g),t)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:o,prevLayout:s,prevStackOffset:c,prevMargin:l,prevChildren:i})}if(!Hn(i,t.prevChildren)){var v,b,x,w,j=Wn(i,rj),O=j&&null!==(v=null===(b=j.props)||void 0===b?void 0:b.startIndex)&&void 0!==v?v:u,k=j&&null!==(x=null===(w=j.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,S=O!==u||k!==f,P=Rr(n)||S?t.updateId+1:t.updateId;return WS(WS({updateId:P},p(WS(WS({props:e},t),{},{updateId:P,dataStartIndex:O,dataEndIndex:k}),t)),{},{prevChildren:i,dataStartIndex:O,dataEndIndex:k})}return null})),VS(h,"renderActiveDot",(function(e,t,r){var i;return i=n.isValidElement(e)?n.cloneElement(e,t):ft(e)?e(t):o.createElement(Wx,t),o.createElement(ri,{className:"recharts-active-dot",key:r},i)}));var m=n.forwardRef((function(e,t){return o.createElement(h,DS({},e,{ref:t}))}));return m.displayName=h.displayName,m}({chartName:"LineChart",GraphicalChild:Kk,axisComponents:[{axisType:"xAxis",AxisComp:aS},{axisType:"yAxis",AxisComp:hS}],formatAxisMap:function(e,t,r,n,i){var a=e.width,o=e.height,s=e.layout,c=e.children,l=Object.keys(t),u={left:r.left,leftMirror:r.left,right:a-r.right,rightMirror:a-r.right,top:r.top,topMirror:r.top,bottom:o-r.bottom,bottomMirror:o-r.bottom},f=!!Wn(c,Fj);return l.reduce((function(a,o){var c,l,d,p,h,m=t[o],y=m.orientation,g=m.domain,v=m.padding,b=void 0===v?{}:v,x=m.mirror,w=m.reversed,j="".concat(y).concat(x?"Mirror":"");if("number"===m.type&&("gap"===m.padding||"no-gap"===m.padding)){var O=g[1]-g[0],k=1/0,S=m.categoricalDomain.sort(Sn);if(S.forEach((function(e,t){t>0&&(k=Math.min((e||0)-(S[t-1]||0),k))})),Number.isFinite(k)){var P=k/O,A="vertical"===m.layout?r.height:r.width;if("gap"===m.padding&&(c=P*A/2),"no-gap"===m.padding){var N=wn(e.barCategoryGap,P*A),E=P*A/2;c=E-N-(E-N)/A*N}}}l="xAxis"===n?[r.left+(b.left||0)+(c||0),r.left+r.width-(b.right||0)-(c||0)]:"yAxis"===n?"horizontal"===s?[r.top+r.height-(b.bottom||0),r.top+(b.top||0)]:[r.top+(b.top||0)+(c||0),r.top+r.height-(b.bottom||0)-(c||0)]:m.range,w&&(l=[l[1],l[0]]);var M=function(e,t,r){var n=e.scale,i=e.type,a=e.layout,o=e.axisType;if("auto"===n)return"radial"===a&&"radiusAxis"===o?{scale:pd(),realScaleType:"band"}:"radial"===a&&"angleAxis"===o?{scale:Dp(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:md(),realScaleType:"point"}:"category"===i?{scale:pd(),realScaleType:"band"}:{scale:Dp(),realScaleType:"linear"};if(Ur(n)){var s="scale".concat(ki(n));return{scale:(vy[s]||md)(),realScaleType:vy[s]?s:"point"}}return ft(n)?{scale:n}:{scale:md(),realScaleType:"point"}}(m,i,f),_=M.scale,C=M.realScaleType;_.domain(g).range(l),function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-fv,a=Math.max(n[0],n[1])+fv,o=e(t[0]),s=e(t[r-1]);(o<i||o>a||s<i||s>a)&&e.domain([t[0],t[r-1]])}}(_);var T=function(e,t){var r=t.realScaleType,n=t.type,i=t.tickCount,a=t.originalDomain,o=t.allowDecimals,s=r||t.scale;if("auto"!==s&&"linear"!==s)return null;if(i&&"number"===n&&a&&("auto"===a[0]||"auto"===a[1])){var c=e.domain();if(!c.length)return null;var l=Cg(c,i,o);return e.domain([Ny(l),ky(l)]),{niceTicks:l}}if(i&&"number"===n){var u=e.domain();return{niceTicks:Tg(u,i,o)}}return null}(_,Vj(Vj({},m),{},{realScaleType:C}));"xAxis"===n?(h="top"===y&&!x||"bottom"===y&&x,d=r.left,p=u[j]-h*m.height):"yAxis"===n&&(h="left"===y&&!x||"right"===y&&x,d=u[j]-h*m.width,p=r.top);var D=Vj(Vj(Vj({},m),T),{},{realScaleType:C,x:d,y:p,scale:_,width:"xAxis"===n?r.width:m.width,height:"yAxis"===n?r.height:m.height});return D.bandSize=xv(D,T),m.hide||"xAxis"!==n?m.hide||(u[j]+=(h?-1:1)*D.width):u[j]+=(h?-1:1)*D.height,Vj(Vj({},a),{},Gj({},o,D))}),{})}});const oP=e=>0===e?"0":(e=>{if(e<1)return`${Math.round(30.44*e)}d`;const t=Math.floor(e/12),r=Math.floor(e%12),n=Math.round(e%1*30.44);return 0===t?0===n?`${r}m`:`${r}m${n}d`:0===r&&0===n?`${t}a`:0===n?`${t}a${r}m`:`${t}a${r}m${n}d`})(e),sP=(e,t)=>{const r="weight"===t?"kg":"height"===t?"cm":"bmi"===t?"kg/m²":"cm";return`${e.toFixed(1)} ${r}`},cP=e=>De(e);function lP({data:e,measurementType:t}){e?.filter((e=>void 0!==e.patient));const i=(()=>{if(!e||0===e.length)return{interval:2};const t=e.map((e=>e.age)),r=Math.min(...t),n=Math.max(...t);var i;return{interval:(i=n-r)<=12?0:i<=24?1:i<=36?2:3,domain:[r,n]}})(),a=n.useRef(null),[o,s]=n.useState({width:0,height:0});return n.useEffect((()=>{if(a.current){const e=new ResizeObserver((e=>{for(let t of e){const{width:e,height:r}=t.contentRect;s({width:e,height:r})}}));return e.observe(a.current),()=>{e.disconnect()}}}),[]),r.jsxs("div",{ref:a,className:"w-full h-[500px] min-h-[500px]",children:[o.width>0&&o.height>0&&r.jsx(af,{width:"100%",height:"100%",children:r.jsxs(aP,{data:e,margin:{top:5,right:0,left:-20,bottom:5},children:[r.jsx(Dk,{strokeDasharray:"3 3"}),r.jsx(aS,{dataKey:"age",tick:{fontSize:10},tickFormatter:oP,interval:i.interval,domain:i.domain||["dataMin","dataMax"]}),r.jsx(hS,{tick:{fontSize:10},domain:["auto","auto"]}),r.jsx(Iu,{formatter:(e,r)=>[sP(e,t),"patient"===r?"Paciente":r],labelFormatter:cP,contentStyle:{backgroundColor:"white",border:"1px solid #ccc",borderRadius:"4px",padding:"8px",fontSize:"12px"}}),r.jsx(ll,{content:e=>r.jsx(Te,{...e,measurementType:t})}),Object.entries(_e).map((([e,t])=>"patient"!==e&&r.jsx(Kk,{type:"monotone",dataKey:e,stroke:t,dot:!1,strokeDasharray:["p3","p15","p85","p97"].includes(e)?"5 5":void 0,strokeWidth:"p50"===e?2:1},e))),r.jsx(Kk,{type:"monotone",dataKey:"patient",stroke:_e.patient,strokeWidth:2,dot:{r:6,fill:_e.patient},connectNulls:!1})]})}),r.jsx("div",{className:"text-center text-[8px] text-gray-500 mt-2",children:"WHO Child Growth Standards"})]})}function uP({patientData:e,chartData:t}){const n=r=>{const n="weight"===r?t.weightData:"height"===r?t.heightData:"bmi"===r?t.bmiData:t.headCircumferenceData;if(!n?.data||!Array.isArray(n.data))return[];const i=Ie(e.age),a=((e,t)=>e&&Array.isArray(e)?e.filter((e=>{const r=e.age_months||e.age||0;return r>=t.minAge&&r<=t.maxAge})):[])(n.data,i),o=e.weight&&e.height?e.weight/Math.pow(e.height/100,2):void 0,s=(e=>{const t=[...n.data].sort(((e,t)=>e.age_months-t.age_months));let r=t[0],i=t[t.length-1];for(let n=0;n<t.length-1;n++)if(e>=t[n].age_months&&e<=t[n+1].age_months){r=t[n],i=t[n+1];break}if(r.age_months===e)return r;if(i.age_months===e)return i;const a=(e-r.age_months)/(i.age_months-r.age_months);return{age_months:e,percentiles:{"3rd":r.percentiles["3rd"]+(i.percentiles["3rd"]-r.percentiles["3rd"])*a,"15th":r.percentiles["15th"]+(i.percentiles["15th"]-r.percentiles["15th"])*a,"50th":r.percentiles["50th"]+(i.percentiles["50th"]-r.percentiles["50th"])*a,"85th":r.percentiles["85th"]+(i.percentiles["85th"]-r.percentiles["85th"])*a,"97th":r.percentiles["97th"]+(i.percentiles["97th"]-r.percentiles["97th"])*a}}})(e.age),c=[...a.map((e=>({age:e.age_months,p3:e.percentiles["3rd"],p15:e.percentiles["15th"],p50:e.percentiles["50th"],p85:e.percentiles["85th"],p97:e.percentiles["97th"],patient:void 0})))],l="weight"===r?e.weight:"height"===r?e.height:"bmi"===r?o:e.headCircumference;return void 0!==l&&c.push({age:s.age_months,p3:s.percentiles["3rd"],p15:s.percentiles["15th"],p50:s.percentiles["50th"],p85:s.percentiles["85th"],p97:s.percentiles["97th"],patient:l}),c.sort(((e,t)=>e.age-t.age)),c.filter((e=>void 0!==e.patient)),c},i=r.jsxs(A,{defaultValue:"weight",className:"w-full",children:[r.jsxs("div",{className:"space-y-2 mb-6",children:[r.jsx("h3",{className:"text-sm text-center text-gray-500 font-light",children:"Escolha uma curva de crescimento"}),r.jsxs(N,{className:"grid w-full grid-cols-4 gap-1 bg-white/50 rounded-lg p-1 border border-primary/10",children:[r.jsxs(E,{value:"weight",className:"data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm",children:[r.jsx(J,{className:"w-4 h-4 mr-1.5"}),"Peso"]}),r.jsxs(E,{value:"height",className:"data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm",children:[r.jsx(Q,{className:"w-4 h-4 mr-1.5"}),"Altura"]}),r.jsxs(E,{value:"bmi",className:"data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm",children:[r.jsx(te,{className:"w-4 h-4 mr-1.5"}),"IMC"]}),r.jsxs(E,{value:"head",className:"data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm",children:[r.jsx(h,{className:"w-4 h-4 mr-1.5"}),"PC"]})]})]}),r.jsx(M,{value:"weight",className:"mt-0",children:t.weightData&&e.weight&&r.jsxs("div",{className:"w-full",children:[r.jsxs("div",{className:"text-center mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Curva de Peso"}),r.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["📊 Visualizando: ",Ie(e.age).description]})]}),r.jsx(lP,{data:n("weight"),measurementType:"weight"})]})}),r.jsx(M,{value:"height",className:"mt-0",children:t.heightData&&e.height&&r.jsxs("div",{className:"w-full",children:[r.jsxs("div",{className:"text-center mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Curva de Altura"}),r.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["📊 Visualizando: ",Ie(e.age).description]})]}),r.jsx(lP,{data:n("height"),measurementType:"height"})]})}),r.jsx(M,{value:"bmi",className:"mt-0",children:t.bmiData&&e.weight&&e.height&&r.jsxs("div",{className:"w-full",children:[r.jsxs("div",{className:"text-center mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Curva de IMC"}),r.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["📊 Visualizando: ",Ie(e.age).description]})]}),r.jsx(lP,{data:n("bmi"),measurementType:"bmi"})]})}),r.jsx(M,{value:"head",className:"mt-0",children:t.headCircumferenceData&&e.headCircumference&&r.jsxs("div",{className:"w-full",children:[r.jsxs("div",{className:"text-center mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Curva de Perímetro Cefálico"}),r.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["📊 Visualizando: ",Ie(e.age).description]})]}),r.jsx(lP,{data:n("head-circumference"),measurementType:"head-circumference"})]})})]});return r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"md:hidden",children:i}),r.jsx("div",{className:"hidden md:block",children:r.jsx(m,{className:"p-6 glass-card animate-fade-in-up",children:i})})]})}function fP({initialData:e,readOnly:t=!1,useCorrectedAge:i=!1}){const[a,o]=n.useState(e??{age:0,gender:"male"}),[s,c]=n.useState(!!e),{toast:l}=C();n.useEffect((()=>{e&&(o(e),c(!0))}),[e]);const{weightData:u,heightData:f,bmiData:d,headCircumferenceData:p}=function({gender:e,enabled:t}){const{data:r}=re({queryKey:["growth-curve-metadata",e,"weight"],queryFn:async()=>{const{data:t,error:r}=await _.from("pedbook_growth_curve_metadata").select("*").eq("type","weight").eq("gender",e);if(r)throw r;return t?.[0]??null},enabled:!!e&&t,staleTime:864e5,gcTime:1728e5}),{data:n}=re({queryKey:["growth-curve-metadata",e,"height"],queryFn:async()=>{const{data:t,error:r}=await _.from("pedbook_growth_curve_metadata").select("*").eq("type","height").eq("gender",e);if(r)throw r;return t?.[0]??null},enabled:!!e&&t,staleTime:864e5,gcTime:1728e5}),{data:i}=re({queryKey:["growth-curve-metadata",e,"bmi"],queryFn:async()=>{const{data:t,error:r}=await _.from("pedbook_growth_curve_metadata").select("*").eq("type","bmi").eq("gender",e);if(r)throw r;return t?.[0]??null},enabled:!!e&&t,staleTime:864e5,gcTime:1728e5}),{data:a}=re({queryKey:["growth-curve-metadata",e,"head-circumference"],queryFn:async()=>{const{data:t,error:r}=await _.from("pedbook_growth_curve_metadata").select("*").eq("type","head-circumference").eq("gender",e);if(r)throw r;return t?.[0]??null},enabled:!!e&&t,staleTime:864e5,gcTime:1728e5});return{weightData:r,heightData:n,bmiData:i,headCircumferenceData:a}}({gender:a.gender,enabled:s});return r.jsxs("div",{className:"space-y-8",children:[!t&&r.jsxs(m,{className:"p-6 glass-card animate-fade-in-up",children:[r.jsx(Ee,{data:a,onChange:o}),r.jsx("div",{className:"mt-6 flex justify-end",children:r.jsxs(x,{onClick:()=>{a.age&&a.gender?c(!0):l({title:"Dados incompletos",description:"Idade e gênero são obrigatórios para a análise.",variant:"destructive"})},className:"bg-gradient-to-r from-primary to-primary/70 hover:opacity-90 transition-all",children:[r.jsx(te,{className:"mr-2 h-5 w-5"}),"Analisar"]})})]}),s&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs(ne,{className:"bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800",children:[r.jsx(oe,{className:"h-4 w-4"}),r.jsx(ie,{children:"Curvas de Crescimento"}),r.jsxs(ae,{className:"text-blue-700 dark:text-blue-300",children:["Com base na idade do paciente (",De(a.age),")",i&&"Pre-term"===a.maturity&&r.jsx("span",{className:"font-medium",children:" - usando idade corrigida"}),!i&&"Pre-term"===a.maturity&&r.jsx("span",{className:"font-medium",children:" - usando idade cronológica"})]})]}),r.jsx(uP,{patientData:a,chartData:{weightData:u,heightData:f,bmiData:d,headCircumferenceData:p}})]})]})}function dP({ageInMonths:e}){const[t,i]=n.useState(!1),[a,o]=n.useState(!1),[s,c]=n.useState(!0),[l,u]=n.useState(!0),{data:f,error:d}=re({queryKey:["vaccine-doses"],queryFn:async()=>{const{data:e,error:t}=await _.from("pedbook_vaccine_doses").select("\n          id,\n          dose_number,\n          age_recommendation,\n          type,\n          dose_type,\n          vaccine:pedbook_vaccines (\n            id,\n            name,\n            description\n          )\n        ").order("age_recommendation");if(t)throw t;return e}});if(d)return r.jsxs(ne,{variant:"destructive",children:[r.jsx(T,{className:"h-4 w-4"}),r.jsx(ie,{children:"Erro"}),r.jsx(ae,{children:"Não foi possível carregar as informações das vacinas."})]});if(!f)return null;const p=e=>e.filter((e=>{const t="SUS"===e.type;return s&&t||l&&!t})),h=f.reduce(((e,t)=>{const r="0"===t.age_recommendation?"Ao nascer":`${t.age_recommendation} meses`;return e[r]||(e[r]=[]),e[r].push(t),e}),{}),y=(t=>{const r=Object.keys(t).map((e=>"Ao nascer"===e?0:parseInt(e))).filter((t=>t>e)).sort(((e,t)=>e-t));return 0===r.length?null:0===r[0]?"Ao nascer":`${r[0]} meses`})(h),g={},v={};return Object.entries(h).forEach((([t,r])=>{("Ao nascer"===t?0:parseInt(t))<=e?g[t]=p(r):y&&t===y&&(v[t]=p(r))})),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs(ne,{className:"bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800/50 flex-1 mr-4",children:[r.jsx(T,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400"}),r.jsx(ie,{className:"text-yellow-800 dark:text-yellow-300",children:"Vacinas Recomendadas"}),r.jsxs(ae,{className:"text-yellow-700 dark:text-yellow-200",children:["Com base na idade do paciente (",De(e),")"]})]}),r.jsxs(D,{children:[r.jsx(I,{asChild:!0,children:r.jsxs(x,{variant:"outline",className:"gap-2",children:[r.jsx(se,{className:"h-4 w-4"}),"Filtros"]})}),r.jsxs(B,{className:"w-56 p-4",children:[r.jsx($,{children:"Visualização"}),r.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[r.jsx(b,{id:"current",checked:t,onCheckedChange:e=>{i(!0===e),e&&o(!1)}}),r.jsx("label",{htmlFor:"current",className:"text-sm",children:"Apenas vacinas atuais"})]}),r.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[r.jsx(b,{id:"upcoming",checked:a,onCheckedChange:e=>{o(!0===e),e&&i(!1)}}),r.jsx("label",{htmlFor:"upcoming",className:"text-sm",children:"Apenas próximas vacinas"})]}),r.jsx(R,{className:"my-2"}),r.jsx($,{children:"Tipo de Vacina"}),r.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[r.jsx(b,{id:"sus",checked:s,onCheckedChange:e=>c(!0===e)}),r.jsx("label",{htmlFor:"sus",className:"text-sm",children:"SUS"})]}),r.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[r.jsx(b,{id:"private",checked:l,onCheckedChange:e=>u(!0===e)}),r.jsx("label",{htmlFor:"private",className:"text-sm",children:"Particular"})]})]})]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[!a&&r.jsxs(m,{className:"p-4",children:[r.jsxs("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2 text-green-700 dark:text-green-400",children:[r.jsx(ce,{className:"h-5 w-5"}),"Vacinas que já deveriam ter sido aplicadas"]}),r.jsx("div",{className:"space-y-4 max-h-[500px] overflow-y-auto pr-2",children:Object.entries(g).map((([e,t])=>r.jsxs("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium text-green-800 dark:text-green-300 border-b border-green-200 dark:border-green-700 pb-1",children:e}),t.map((e=>r.jsxs("div",{className:"p-2 bg-green-50 dark:bg-green-900/20 rounded",children:[r.jsxs("div",{className:"font-medium text-green-800 dark:text-green-300",children:["💉 ",e.vaccine.name,"reforço"===e.dose_type?" (Reforço)":` (${e.dose_number}ª dose)`]}),r.jsx("div",{className:"text-sm text-green-600 dark:text-green-400",children:e.type})]},e.id)))]},e)))})]}),!t&&r.jsxs(m,{className:"p-4",children:[r.jsxs("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2 text-blue-700 dark:text-blue-400",children:[r.jsx(je,{className:"h-5 w-5"}),"Próximas vacinas"]}),r.jsxs("div",{className:"space-y-4 max-h-[500px] overflow-y-auto pr-2",children:[Object.entries(v).map((([e,t])=>r.jsxs("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium text-blue-800 dark:text-blue-300 border-b border-blue-200 dark:border-blue-700 pb-1",children:e}),t.map((e=>r.jsxs("div",{className:"p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:[r.jsxs("div",{className:"font-medium text-blue-800 dark:text-blue-300",children:["💉 ",e.vaccine.name,"reforço"===e.dose_type?" (Reforço)":` (${e.dose_number}ª dose)`]}),r.jsx("div",{className:"text-sm text-blue-600 dark:text-blue-400",children:e.type})]},e.id)))]},e))),0===Object.keys(v).length&&r.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-center py-4",children:"Não há próximas vacinas previstas"})]})]})]})]})}function pP({ageInMonths:e}){const{data:t,isLoading:n}=re({queryKey:["dnpm-milestones"],queryFn:async()=>{const{data:e,error:t}=await _.from("pedbook_dnpm_milestones").select("*").order("age_months");if(t)throw t;return e}});if(n||!t)return r.jsx(m,{className:"p-6",children:r.jsxs("div",{className:"animate-pulse space-y-4",children:[r.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),r.jsx("div",{className:"h-20 bg-gray-200 rounded"})]})});const i=t.filter((t=>t.age_months<=e)).slice(-1)[0],a=t.find((t=>t.age_months>e));if(!i&&a)return r.jsxs("div",{className:"space-y-4",children:[r.jsxs(ne,{children:[r.jsx(h,{className:"h-4 w-4"}),r.jsx(ie,{children:"DNPM"}),r.jsxs(ae,{className:"text-yellow-700",children:["Com base na idade do paciente (",De(e),")"]})]}),r.jsxs(m,{className:"p-4 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800/50",children:[r.jsxs("h3",{className:"font-semibold text-blue-700 dark:text-blue-300 mb-2",children:["Próximo Marco (",a.age_months," ",1===a.age_months?"mês":"meses",")"]}),r.jsxs("div",{className:"space-y-3",children:[a.social_emotional&&r.jsxs("div",{className:"flex items-start gap-2",children:[r.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-xs font-medium text-blue-600 dark:text-blue-400",children:"Social e Emocional"}),r.jsx("p",{className:"text-blue-600 dark:text-blue-200/90 text-sm",children:a.social_emotional})]})]}),a.language_communication&&r.jsxs("div",{className:"flex items-start gap-2",children:[r.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-xs font-medium text-purple-600 dark:text-purple-400",children:"Linguagem e Comunicação"}),r.jsx("p",{className:"text-purple-600 dark:text-purple-200/90 text-sm",children:a.language_communication})]})]}),a.cognition&&r.jsxs("div",{className:"flex items-start gap-2",children:[r.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-xs font-medium text-green-600 dark:text-green-400",children:"Cognição"}),r.jsx("p",{className:"text-green-600 dark:text-green-200/90 text-sm",children:a.cognition})]})]}),a.motor_physical&&r.jsxs("div",{className:"flex items-start gap-2",children:[r.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-xs font-medium text-orange-600 dark:text-orange-400",children:"Motora/Física"}),r.jsx("p",{className:"text-orange-600 dark:text-orange-200/90 text-sm",children:a.motor_physical})]})]})]})]})]});if(!i&&!a)return r.jsxs(ne,{children:[r.jsx(h,{className:"h-4 w-4"}),r.jsx(ie,{children:"DNPM"}),r.jsxs(ae,{children:["Não há marcos de desenvolvimento disponíveis para esta idade (",De(e),")."]})]});const o=({title:e,content:t,bgColor:n,textColor:i})=>t?r.jsxs("div",{className:`${n} rounded-lg p-4 shadow-sm`,children:[r.jsxs("h4",{className:`text-sm font-medium ${i} mb-2 flex items-center gap-2`,children:[r.jsx("span",{className:"w-2 h-2 rounded-full bg-current"}),e]}),r.jsx("p",{className:`text-sm ${i} break-words whitespace-pre-wrap`,children:t})]}):null,s=(e,t=!1)=>{const n=[{title:"Social e Emocional",content:e.social_emotional,bgColor:t?"bg-blue-50 dark:bg-blue-900/20":"bg-blue-100 dark:bg-blue-900/30",textColor:t?"text-blue-700 dark:text-blue-300":"text-blue-800 dark:text-blue-200"},{title:"Linguagem e Comunicação",content:e.language_communication,bgColor:t?"bg-purple-50 dark:bg-purple-900/20":"bg-purple-100 dark:bg-purple-900/30",textColor:t?"text-purple-700 dark:text-purple-300":"text-purple-800 dark:text-purple-200"},{title:"Cognição",content:e.cognition,bgColor:t?"bg-green-50 dark:bg-green-900/20":"bg-green-100 dark:bg-green-900/30",textColor:t?"text-green-700 dark:text-green-300":"text-green-800 dark:text-green-200"},{title:"Motora/Física",content:e.motor_physical,bgColor:t?"bg-orange-50 dark:bg-orange-900/20":"bg-orange-100 dark:bg-orange-900/30",textColor:t?"text-orange-700 dark:text-orange-300":"text-orange-800 dark:text-orange-200"}];return r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:n.map(((e,t)=>r.jsx(o,{title:e.title,content:e.content,bgColor:e.bgColor,textColor:e.textColor},t)))})};return r.jsxs("div",{className:"space-y-6",children:[r.jsxs(ne,{className:"bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800/50",children:[r.jsx(h,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400"}),r.jsx(ie,{className:"text-yellow-800 dark:text-yellow-300",children:"Marcos do Desenvolvimento"}),r.jsxs(ae,{className:"text-yellow-700 dark:text-yellow-200",children:["Com base na idade do paciente (",De(e),")"]})]}),r.jsxs("div",{className:"space-y-8",children:[r.jsxs("div",{className:"w-full",children:[r.jsxs("div",{className:"flex items-center gap-2 text-gray-700 dark:text-gray-300 mb-6",children:[r.jsx(Oe,{className:"h-5 w-5"}),r.jsxs("h3",{className:"text-xl font-semibold",children:["Marco Atual (",i.age_months," meses)"]})]}),s(i)]}),a&&r.jsxs("div",{className:"w-full",children:[r.jsxs("div",{className:"flex items-center gap-2 text-gray-700 dark:text-gray-300 mb-6",children:[r.jsx(le,{className:"h-5 w-5"}),r.jsxs("h3",{className:"text-xl font-semibold",children:["Próximo Marco (",a.age_months," meses)"]})]}),s(a,!0)]})]}),r.jsx("p",{className:"text-center text-sm text-gray-500 dark:text-gray-400 mt-6",children:"Referência: Cartilha de Desenvolvimento 2m-5anos, CDC/SBP"})]})}const hP=[{name:"Sulfato ferroso gotas",concentration:1.25,note:"Em geral disponível no Posto de Saúde"},{name:"Combiron gotas",concentration:2.5,note:"glicinato"},{name:"Noripurum gotas",concentration:2.5,note:"polimaltosado"},{name:"Neutrofer gotas",concentration:2.5,note:"glicinato"},{name:"Endofer gotas",concentration:2.5,note:"polimaltosado"}],mP=({data:e,useCorrectedAge:t=!1})=>{const[i,a]=n.useState(!1),[o,s]=n.useState(hP[0]),[c,l]=n.useState(null),u="Pre-term"===e.maturity,f={ageInDays:30*e.age,currentWeight:e.weight?1e3*e.weight:0,birthWeight:e.birthWeight||0,maturity:e.maturity,exclusiveBreastfeeding:e.exclusiveBreastfeeding,riskFactors:e.riskFactors||[]},d=ue(f),p=(e=>{const t=e.match(/(\d+(?:\.\d+)?)\s*mg.*?ferro.*?dia/i);return t?parseFloat(t[1]):0})(d.iron),h=e=>0===p?0:Math.round(p/e.concentration*10)/10;return r.jsxs("div",{className:"space-y-6",children:[r.jsxs(ne,{className:"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800/50",children:[r.jsx(L,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),r.jsx(ie,{className:"text-green-800 dark:text-green-300",children:"Suplementação Vitamínica"}),r.jsxs(ae,{className:"text-green-700 dark:text-green-200",children:["Com base na idade do paciente (",De(e.age),")",t&&u&&r.jsx("span",{className:"font-medium",children:" - usando idade corrigida"}),!t&&u&&r.jsx("span",{className:"font-medium",children:" - usando idade cronológica"})]})]}),r.jsxs("div",{className:"grid gap-4",children:[r.jsx(m,{className:"overflow-hidden border-l-4 border-l-purple-400",children:r.jsxs("div",{className:"p-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[r.jsx("span",{className:"text-2xl",children:"☀️"}),r.jsx("h3",{className:"text-lg font-semibold text-purple-700 dark:text-purple-300",children:"Vitamina D"})]}),r.jsx("p",{className:"text-purple-600 dark:text-purple-200/90 leading-relaxed",children:d.vitaminD})]})}),r.jsx(m,{className:"overflow-hidden border-l-4 border-l-red-400",children:r.jsxs("div",{className:"p-6 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🩸"}),r.jsx("h3",{className:"text-lg font-semibold text-red-700 dark:text-red-300",children:"Ferro"})]}),p>0&&r.jsxs(x,{variant:"outline",size:"sm",onClick:()=>a(!i),className:"text-red-600 border-red-200 hover:bg-red-100 dark:text-red-300 dark:border-red-700 dark:hover:bg-red-900/20",children:[r.jsx(z,{className:"h-4 w-4 mr-2"}),"Ver Prescrição",i?r.jsx(g,{className:"h-4 w-4 ml-2"}):r.jsx(v,{className:"h-4 w-4 ml-2"})]})]}),r.jsx("p",{className:"text-red-600 dark:text-red-200/90 leading-relaxed mb-4",children:d.iron}),i&&p>0&&r.jsxs("div",{className:"mt-4 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-red-200 dark:border-red-700",children:[r.jsxs("h4",{className:"font-semibold text-red-700 dark:text-red-300 mb-3 flex items-center gap-2",children:[r.jsx(z,{className:"h-4 w-4"}),"Cálculo de Prescrição (",p," mg/dia)"]}),r.jsxs("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-sm font-medium text-red-600 dark:text-red-300 mb-2",children:"Selecione o medicamento:"}),r.jsx("div",{className:"grid gap-2",children:hP.map(((e,t)=>r.jsx("button",{onClick:()=>s(e),className:"p-3 text-left rounded-lg border transition-all "+(o.name===e.name?"border-red-400 bg-red-100 dark:bg-red-900/30 dark:border-red-600":"border-gray-200 hover:border-red-300 hover:bg-red-50 dark:border-gray-600 dark:hover:border-red-700 dark:hover:bg-red-900/20"),children:r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{children:[r.jsx("p",{className:"font-medium text-red-700 dark:text-red-300",children:e.name}),r.jsxs("p",{className:"text-sm text-red-600 dark:text-red-400",children:[e.concentration," mg/gota (",e.note,")"]})]}),r.jsxs(F,{variant:o.name===e.name?"default":"secondary",children:[h(e)," gotas/dia"]})]})},t)))})]}),r.jsxs("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg border border-red-300 dark:border-red-600",children:[r.jsx("h5",{className:"font-semibold text-red-800 dark:text-red-200 mb-2",children:"📋 Prescrição:"}),r.jsxs("p",{className:"text-red-700 dark:text-red-300",children:[r.jsx("strong",{children:o.name}),r.jsx("br",{}),r.jsxs("strong",{children:[h(o)," gotas por dia"]}),r.jsx("br",{}),r.jsxs("span",{className:"text-sm",children:["(",o.concentration," mg/gota × ",h(o)," gotas = ",p," mg ferro elementar/dia)"]})]})]})]})]})}),e.age<6?r.jsx(m,{className:"overflow-hidden border-l-4 border-l-amber-400",children:r.jsxs("div",{className:"p-6 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[r.jsx("span",{className:"text-2xl",children:"🥕"}),r.jsx("h3",{className:"text-lg font-semibold text-amber-700 dark:text-amber-300",children:"Vitamina A"})]}),r.jsx("p",{className:"text-amber-600 dark:text-amber-200/90 leading-relaxed",children:d.vitaminA})]})}):r.jsx(m,{className:"overflow-hidden border-l-4 border-l-amber-400",children:r.jsxs("div",{className:"p-6 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🥕"}),r.jsx("h3",{className:"text-lg font-semibold text-amber-700 dark:text-amber-300",children:"Vitamina A"})]}),null===c&&r.jsxs("div",{className:"flex items-center gap-2 text-amber-600 dark:text-amber-400",children:[r.jsx("span",{className:"text-sm font-medium",children:"Avaliação necessária"}),r.jsx("span",{className:"animate-pulse",children:"⚠️"})]})]}),null===c&&r.jsxs("div",{className:"mb-4 p-4 bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/40 dark:to-orange-900/40 rounded-lg border border-amber-300 dark:border-amber-600",children:[r.jsxs("div",{className:"flex items-start gap-3 mb-3",children:[r.jsx("span",{className:"text-xl",children:"📍"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-amber-800 dark:text-amber-200 font-semibold mb-2",children:"Avaliação de Área Endêmica"}),r.jsx("p",{className:"text-amber-700 dark:text-amber-300 text-sm mb-3",children:"A criança vive em área endêmica para deficiência de Vitamina A ou apresenta fatores de risco?"}),r.jsx("div",{className:"bg-amber-50 dark:bg-amber-900/20 p-3 rounded border-l-4 border-amber-400 mb-3",children:r.jsxs("p",{className:"text-xs text-amber-700 dark:text-amber-300",children:[r.jsx("strong",{children:"Considere:"})," Região com alta prevalência de desnutrição, diarreia crônica, fibrose cística, ou outras condições que comprometem a absorção intestinal."]})})]})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[r.jsxs(x,{size:"sm",onClick:()=>l(!0),className:"bg-amber-600 hover:bg-amber-700 text-white flex-1 sm:flex-none",children:[r.jsx("span",{className:"mr-2",children:"✅"}),"Sim, área endêmica/risco"]}),r.jsxs(x,{size:"sm",variant:"outline",onClick:()=>l(!1),className:"border-amber-600 text-amber-600 hover:bg-amber-50 dark:border-amber-400 dark:text-amber-400 dark:hover:bg-amber-900/20 flex-1 sm:flex-none",children:[r.jsx("span",{className:"mr-2",children:"❌"}),"Não, área não endêmica"]})]})]}),null!==c&&r.jsx("div",{className:"p-4 rounded-lg border-l-4 "+(c?"bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600":"bg-blue-50 dark:bg-blue-900/20 border-blue-400 dark:border-blue-600"),children:r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsx("span",{className:"text-xl",children:c?"💊":"ℹ️"}),r.jsxs("div",{children:[r.jsx("h4",{className:"font-semibold mb-2 "+(c?"text-green-800 dark:text-green-200":"text-blue-800 dark:text-blue-200"),children:c?"Suplementação Recomendada":"Suplementação Não Rotineira"}),r.jsx("p",{className:"text-sm leading-relaxed "+(c?"text-green-700 dark:text-green-300":"text-blue-700 dark:text-blue-300"),children:c?r.jsxs(r.Fragment,{children:[r.jsx("strong",{children:"Protocolo de suplementação:"}),r.jsx("br",{}),"• ",r.jsx("strong",{children:"6 meses:"})," 100.000 UI (dose única)",r.jsx("br",{}),"• ",r.jsx("strong",{children:"12-59 meses:"})," 200.000 UI a cada 6 meses",r.jsx("br",{}),"• Indicado para áreas endêmicas, desnutrição, diarreia crônica, fibrose cística ou outras condições que comprometem a absorção intestinal."]}):r.jsx(r.Fragment,{children:"A suplementação de vitamina A não é rotineiramente recomendada para esta criança. Considere apenas em casos específicos como desnutrição, diarreia crônica, ou outras condições que comprometem a absorção intestinal."})})]})]})}),null!==c&&r.jsx("div",{className:"mt-4 flex justify-end",children:r.jsx(x,{size:"sm",variant:"ghost",onClick:()=>l(null),className:"text-amber-600 hover:bg-amber-100 dark:text-amber-400 dark:hover:bg-amber-900/20",children:"🔄 Reavaliar"})})]})})]}),r.jsx("div",{className:"mt-8",children:r.jsx(fe,{})})]})};class yP{static cleanText(e){return e.replace(/[áàâãä]/g,"a").replace(/[éèêë]/g,"e").replace(/[íìîï]/g,"i").replace(/[óòôõö]/g,"o").replace(/[úùûü]/g,"u").replace(/[ç]/g,"c").replace(/[ÁÀÂÃÄ]/g,"A").replace(/[ÉÈÊË]/g,"E").replace(/[ÍÌÎÏ]/g,"I").replace(/[ÓÒÔÕÖ]/g,"O").replace(/[ÚÙÛÜ]/g,"U").replace(/[Ç]/g,"C")}static formatAge(e,t){return((e,t=!1)=>`${De(e)} (${t?"idade corrigida":"idade cronológica"})`)(e,t)}static formatWeight(e){return`${(1e3*e).toFixed(0)}g`}static formatHeight(e){return`${e} cm`}static formatGestationalAge(e){return`${Math.floor(e)} semanas e ${Math.round(e%1*7)} dias`}static getMaturidade(e){return e>=37?"A termo":"Pre-termo"}static generateFileName(e){return`laudo_pediatrico_${e?this.cleanText(e).replace(/\s+/g,"_"):"paciente"}_${(new Date).toISOString().split("T")[0]}.pdf`}static formatDateTime(){const e=new Date;return`${e.toLocaleDateString("pt-BR")} | ${e.toLocaleTimeString("pt-BR")}`}static async loadLogo(){try{const e=await fetch("/faviconx.webp"),t=await e.blob(),r=new Image;return r.crossOrigin="anonymous",new Promise((e=>{r.onload=()=>{const t=document.createElement("canvas"),n=t.getContext("2d");t.width=r.width,t.height=r.height,n?.clearRect(0,0,t.width,t.height),n?.drawImage(r,0,0),e(t.toDataURL("image/png"))},r.onerror=()=>{const r=new FileReader;r.onload=()=>e(r.result),r.readAsDataURL(t)},r.src=URL.createObjectURL(t)}))}catch(e){return""}}}const gP={page:{width:210,height:297,margin:20,lineHeight:6},fonts:{title:18,section:14,normal:10,small:9,tiny:8},colors:{primary:[30,64,175],text:[0,0,0],gray:[100,100,100],success:[240,253,244],warning:[255,251,235],error:[254,242,242],info:[239,246,255],purple:[250,245,255]},logo:{size:120,opacity:.08,offsetY:-10},texts:{title:"LAUDO PEDIATRICO COMPLETO",subtitle:"PedBook - Analise Antropometrica, Nutricional, Vacinal e de Desenvolvimento",footer:"Este laudo foi gerado automaticamente pelo PedBook e deve ser interpretado por profissional de saude qualificado.",sections:{patient:"DADOS DO PACIENTE",perinatal:"DADOS PERINATAIS",analysis:"ANALISE ANTROPOMETRICA",supplementation:"RECOMENDACOES DE SUPLEMENTACAO",vaccines:"CALENDARIO VACINAL (SUS)"},icons:{patient:"[PACIENTE]",perinatal:"[NASCIMENTO]",analysis:"[MEDIDAS]",supplementation:"[VITAMINAS]",vaccines:"[VACINAS]"}}};class vP{constructor(e,r=""){t(this,"pdf"),t(this,"currentY"),t(this,"logoBase64"),this.pdf=e,this.currentY=gP.page.margin,this.logoBase64=r}checkPageBreak(e){return this.currentY+e>gP.page.height-gP.page.margin&&(this.pdf.addPage(),this.addWatermark(),this.currentY=gP.page.margin,!0)}addWatermark(){if(this.logoBase64){this.pdf.saveGraphicsState(),this.pdf.setGState(new this.pdf.GState({opacity:gP.logo.opacity}));const r=(gP.page.width-gP.logo.size)/2,n=(gP.page.height-gP.logo.size)/2+gP.logo.offsetY;try{this.pdf.addImage(this.logoBase64,"PNG",r,n,gP.logo.size,gP.logo.size,void 0,"NONE")}catch(e){try{this.pdf.addImage(this.logoBase64,"PNG",r,n,gP.logo.size,gP.logo.size)}catch(t){}}this.pdf.restoreGraphicsState()}}addSectionTitle(e,t=""){this.checkPageBreak(15),this.pdf.setFontSize(gP.fonts.section),this.pdf.setFont("helvetica","bold"),this.pdf.setTextColor(...gP.colors.primary);const r=yP.cleanText(e),n=yP.cleanText(t),i=n?`${n} `:"";this.pdf.text(`${i}${r}`,gP.page.margin,this.currentY),this.currentY+=8,this.pdf.setDrawColor(...gP.colors.primary),this.pdf.setLineWidth(.5),this.pdf.line(gP.page.margin,this.currentY,gP.page.margin+gP.page.width-2*gP.page.margin,this.currentY),this.currentY+=8}addText(e,t,r=gP.page.margin){this.checkPageBreak(gP.page.lineHeight),this.pdf.setFontSize(gP.fonts.normal),this.pdf.setFont("helvetica","bold"),this.pdf.setTextColor(...gP.colors.text);const n=yP.cleanText(e),i=yP.cleanText(t);this.pdf.text(`${n}: `,r,this.currentY),this.pdf.setFont("helvetica","normal");const a=this.pdf.getTextWidth(`${n}: `);this.pdf.text(i,r+a,this.currentY),this.currentY+=gP.page.lineHeight}addColoredCard(e,t,r){this.checkPageBreak(20);const n=6+t.length*gP.page.lineHeight,i=gP.page.width-2*gP.page.margin;this.pdf.setFillColor(...r),this.pdf.rect(gP.page.margin,this.currentY-2,i,n,"F"),this.pdf.setFontSize(11),this.pdf.setFont("helvetica","bold"),this.pdf.setTextColor(...gP.colors.text);const a=yP.cleanText(e);this.pdf.text(a,gP.page.margin+3,this.currentY+3),this.currentY+=8,this.pdf.setFontSize(gP.fonts.small),this.pdf.setFont("helvetica","normal"),t.forEach((e=>{const t=yP.cleanText(e);this.pdf.splitTextToSize(t,i-6).forEach((e=>{this.pdf.text(e,gP.page.margin+3,this.currentY),this.currentY+=gP.page.lineHeight-1}))})),this.currentY+=4}renderHeader(){this.addWatermark(),this.pdf.setFontSize(gP.fonts.title),this.pdf.setFont("helvetica","bold"),this.pdf.setTextColor(...gP.colors.primary);const e=yP.cleanText(gP.texts.title),t=this.pdf.getTextWidth(e);this.pdf.text(e,(gP.page.width-t)/2,this.currentY),this.currentY+=8,this.pdf.setFontSize(11),this.pdf.setFont("helvetica","normal"),this.pdf.setTextColor(...gP.colors.gray);const r=yP.cleanText(gP.texts.subtitle),n=this.pdf.getTextWidth(r);this.pdf.text(r,(gP.page.width-n)/2,this.currentY),this.currentY+=6;const i=yP.formatDateTime(),a=this.pdf.getTextWidth(i);this.pdf.text(i,(gP.page.width-a)/2,this.currentY),this.currentY+=15,this.pdf.setDrawColor(...gP.colors.primary),this.pdf.setLineWidth(1),this.pdf.line(gP.page.margin,this.currentY,gP.page.margin+gP.page.width-2*gP.page.margin,this.currentY),this.currentY+=15}renderPatientData(e,t){this.addSectionTitle(gP.texts.sections.patient,gP.texts.icons.patient);const r=gP.page.margin,n=gP.page.margin+(gP.page.width-2*gP.page.margin)/2,i=this.currentY;this.currentY=i,this.addText("Nome",e.name||"Nao informado",r),this.addText("Idade",yP.formatAge(e.age,t),r),this.addText("Genero","male"===e.gender?"Masculino":"Feminino",r),this.addText("Tipo de Idade",t?"Idade Corrigida":"Idade Cronologica",r);const a=i;this.currentY=a,this.addText("Peso Atual",yP.formatWeight(e.weight),n),this.addText("Altura",yP.formatHeight(e.height),n),this.addText("PC",yP.formatHeight(e.headCircumference),n),this.addText("Maturidade",yP.getMaturidade(e.gestationalAge),n),this.currentY=Math.max(this.currentY,i+4*gP.page.lineHeight)+10}renderPerinatalData(e,t){this.addSectionTitle(gP.texts.sections.perinatal,gP.texts.icons.perinatal);const r=gP.page.margin,n=gP.page.margin+(gP.page.width-2*gP.page.margin)/2,i=this.currentY;this.currentY=i,this.addText("Peso ao Nascer",`${e.birthWeight}g`,r),this.addText("Idade Gestacional",yP.formatGestationalAge(e.gestationalAge),r),this.currentY=i,this.addText("AME",e.exclusiveBreastfeeding?"Sim":"Nao",n),this.addText("Classificacao",`${t.weightAnalysis.classification} (P${t.weightAnalysis.percentile})`,n),this.currentY=Math.max(this.currentY,i+2*gP.page.lineHeight)+10}renderAnalysis(e,t){this.addSectionTitle(gP.texts.sections.analysis,gP.texts.icons.analysis);const r="normal"===e.weightAnalysis.status?gP.colors.success:gP.colors.error;this.addColoredCard(`PESO: ${yP.formatWeight(t.weight)}`,[`Percentil: P${e.weightAnalysis.percentile}`,`Status: ${e.weightAnalysis.interpretation}`,`Classificacao: ${e.weightAnalysis.classification}`],r);const n="normal"===e.heightAnalysis.status?gP.colors.success:gP.colors.error;this.addColoredCard(`ALTURA: ${yP.formatHeight(t.height)}`,[`Percentil: P${e.heightAnalysis.percentile}`,`Status: ${e.heightAnalysis.interpretation}`],n);const i="normal"===e.headCircumferenceAnalysis.status?gP.colors.success:gP.colors.error;this.addColoredCard(`PERIMETRO CEFALICO: ${yP.formatHeight(t.headCircumference)}`,[`Percentil: P${e.headCircumferenceAnalysis.percentile}`,`Status: ${e.headCircumferenceAnalysis.interpretation}`],i)}renderSupplementation(e){0!==e.recommendations.length&&(this.addSectionTitle(gP.texts.sections.supplementation,gP.texts.icons.supplementation),e.recommendations.forEach((e=>{const t="D"===e.vitamin?gP.colors.info:"Ferro"===e.vitamin?gP.colors.error:gP.colors.purple,r=[];e.dosage&&"Conforme orientacao medica"!==e.dosage&&r.push(`Dosagem: ${e.dosage}`),e.duration&&"Conforme orientacao medica"!==e.duration&&r.push(`Duracao: ${e.duration}`),e.notes&&"Administrar conforme orientacao"!==e.notes&&r.push(`Observacoes: ${e.notes}`),0===r.length&&r.push("Conforme orientacao medica"),this.addColoredCard("D"===e.vitamin?"Vitamina D":"A"===e.vitamin?"Vitamina A":"Ferro",r,t)})))}renderVaccines(e){this.checkPageBreak(30),this.addSectionTitle(gP.texts.sections.vaccines,gP.texts.icons.vaccines),e.applied.length>0&&this.addColoredCard("VACINAS APLICADAS",e.applied.map((e=>`- ${e.name} (${e.age})`)),gP.colors.success),e.upcoming.length>0&&this.addColoredCard("PROXIMAS VACINAS",e.upcoming.map((e=>`- ${e.name} (${e.age})`)),gP.colors.warning)}renderFooter(){this.checkPageBreak(20),this.currentY=gP.page.height-30,this.pdf.setFontSize(gP.fonts.tiny),this.pdf.setFont("helvetica","italic"),this.pdf.setTextColor(...gP.colors.gray);const e=yP.cleanText(gP.texts.footer),t=gP.page.width-2*gP.page.margin,r=this.pdf.splitTextToSize(e,t);this.pdf.text(r,gP.page.margin,this.currentY)}}const bP={24:[515,670,870],25:[580,750,980],26:[650,840,1100],27:[730,940,1230],28:[820,1050,1370],29:[920,1170,1530],30:[1030,1300,1700],31:[1150,1440,1880],32:[1280,1590,2070],33:[1430,1750,2270],34:[1590,1920,2480],35:[1760,2100,2690],36:[1940,2280,2900],37:[2130,2460,3110],38:[2320,2640,3320],39:[2510,2820,3530],40:[2700,3e3,3730],41:[2880,3170,3920],42:[3050,3330,4100]};function xP(e,t){if(!e||e<300||e>6e3)throw new Error(`Peso ao nascer inválido: ${e}g. Deve estar entre 300g e 6000g.`);if(!t||t<22||t>44)throw new Error(`Idade gestacional inválida: ${t} semanas. Deve estar entre 22 e 44 semanas.`);const[r,n,i]=function(e){const t=Math.floor(e),r=Math.ceil(e);if(t===r){const e=Math.max(24,Math.min(42,t));return bP[e]||[2700,3e3,3730]}const n=Math.max(24,Math.min(42,t)),i=Math.max(24,Math.min(42,r)),a=bP[n],o=bP[i];if(!a||!o)return[2700,3e3,3730];const s=e-t;return[a[0]+(o[0]-a[0])*s,a[1]+(o[1]-a[1])*s,a[2]+(o[2]-a[2])*s]}(t);let a,o,s,c,l;if(e<r){const t=e/r;a=t<.5?Math.max(.1,2*t):Math.max(1,10*t),o="PIG",s="Pequeno para idade gestacional",c="red",l="Risco aumentado de hipoglicemia, hipotermia e RCIU. Avaliação médica necessária."}else if(e>i){const t=(e-i)/(.2*i);a=Math.min(99.9,90+9*t),o="GIG",s="Grande para idade gestacional",c="orange",l="Associado a diabetes materno, risco de parto traumático e hipoglicemia neonatal."}else a=e<=n?10+(e-r)/(n-r)*40:50+(e-n)/(i-n)*40,o="AIG",s="Adequado para idade gestacional",c="green",l="Peso adequado para a idade gestacional. Crescimento normal.";return{classification:o,percentile:Math.round(a),description:s,color:c,clinicalNote:l}}function wP(e){switch(e){case"PIG":return"🔴";case"AIG":return"🟢";case"GIG":return"🟠";default:return"⚪"}}function jP(e){switch(e){case"PIG":return"text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400";case"AIG":return"text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400";case"GIG":return"text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400";default:return"text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400"}}class OP{static async generateAnalysisData(e){try{const{data:t}=await _.from("pedbook_growth_curve_metadata").select("data").eq("gender",e.gender).eq("type","weight").single(),{data:r}=await _.from("pedbook_growth_curve_metadata").select("data").eq("gender",e.gender).eq("type","height").single(),{data:n}=await _.from("pedbook_growth_curve_metadata").select("data").eq("gender",e.gender).eq("type","head-circumference").single(),i=(e,t,r)=>{if(!t||0===t.length)return{percentile:50,status:"normal",interpretation:"Normal"};const n=t.sort(((e,t)=>e.age_months-t.age_months));let i=n[0],a=n[n.length-1];for(let m=0;m<n.length-1;m++)if(r>=n[m].age_months&&r<=n[m+1].age_months){i=n[m],a=n[m+1];break}const o=(r-i.age_months)/(a.age_months-i.age_months),s=i.percentiles["3rd"]+(a.percentiles["3rd"]-i.percentiles["3rd"])*o,c=i.percentiles["15th"]+(a.percentiles["15th"]-i.percentiles["15th"])*o,l=i.percentiles["50th"]+(a.percentiles["50th"]-i.percentiles["50th"])*o,u=i.percentiles["85th"]+(a.percentiles["85th"]-i.percentiles["85th"])*o,f=i.percentiles["97th"]+(a.percentiles["97th"]-i.percentiles["97th"])*o;let d=50,p="normal",h="Normal";return e<=s?(d=3,p="attention",h="Muito baixo"):e<=c?(d=Math.round(3+(e-s)/(c-s)*12),p="attention",h="Baixo"):e<=l?(d=Math.round(15+(e-c)/(l-c)*35),p="normal",h="Normal"):e<=u?(d=Math.round(50+(e-l)/(u-l)*35),p="normal",h="Normal"):e<=f?(d=Math.round(85+(e-u)/(f-u)*12),p="attention",h="Alto"):(d=97,p="attention",h="Muito alto"),{percentile:d,status:p,interpretation:h}},a=t?.data?i(e.weight,t.data,e.age):{percentile:50,status:"normal",interpretation:"Normal"},o=r?.data?i(e.height,r.data,e.age):{percentile:50,status:"normal",interpretation:"Normal"},s=n?.data?i(e.headCircumference,n.data,e.age):{percentile:50,status:"normal",interpretation:"Normal"},c=xP(e.birthWeight,e.gestationalAge,e.gender);return{weightAnalysis:{percentile:Math.round(a.percentile),status:a.status,interpretation:a.interpretation,classification:c.classification},heightAnalysis:{percentile:Math.round(o.percentile),status:o.status,interpretation:o.interpretation},headCircumferenceAnalysis:{percentile:Math.round(s.percentile),status:s.status,interpretation:s.interpretation}}}catch(t){return{weightAnalysis:{percentile:50,status:"normal",interpretation:"Normal",classification:"AIG"},heightAnalysis:{percentile:50,status:"normal",interpretation:"Normal"},headCircumferenceAnalysis:{percentile:50,status:"normal",interpretation:"Normal"}}}}static generateSupplementationData(e){const t=[],r={ageInDays:Math.round(30*e.age),currentWeight:1e3*e.weight,birthWeight:e.birthWeight,maturity:e.maturity||(e.gestationalAge>=37?"Term":"Pre-term"),exclusiveBreastfeeding:e.exclusiveBreastfeeding,riskFactors:e.riskFactors||[]},n=ue(r);return n.vitaminD&&!n.vitaminD.includes("não está mais indicada")&&t.push({vitamin:"D",dosage:this.extractDosage(n.vitaminD),duration:this.extractDuration(n.vitaminD),notes:this.extractNotes(n.vitaminD)}),n.iron&&!n.iron.includes("não está mais indicada")&&t.push({vitamin:"Ferro",dosage:this.extractDosage(n.iron),duration:this.extractDuration(n.iron),notes:this.extractNotes(n.iron)}),n.vitaminA&&!n.vitaminA.includes("não está mais indicada")&&t.push({vitamin:"A",dosage:this.extractDosage(n.vitaminA),duration:this.extractDuration(n.vitaminA),notes:this.extractNotes(n.vitaminA)}),{recommendations:t}}static extractDosage(e){const t=[/(\d+(?:\.\d+)?\s*gotas\s*\(\d+\s*UI\))/i,/(\d+(?:\.\d+)?\s*mg\s*de\s*ferro\s*elementar\/dia)/i,/(\d+(?:\.\d+)?\s*UI\s*de\s*vitamina\s*A)/i,/(\d+(?:\.\d+)?\s*mg\/kg\/dia)/i,/(\d+(?:\.\d+)?\s*(?:mg|UI|gotas))/i];for(const r of t){const t=e.match(r);if(t)return yP.cleanText(t[1])}return"Conforme orientacao medica"}static extractDuration(e){const t=[/(ate \d+ anos de idade)/i,/(ate \d+ meses)/i,/(a cada \d+ meses)/i,/(todos os dias)/i,/(diariamente)/i];for(const r of t){const t=e.match(r);if(t)return yP.cleanText(t[1])}return"Conforme orientacao medica"}static extractNotes(e){const t=[/Fatores de risco:\s*([^.]+\.)/i,/(pre-termo[^.]*)/i,/(areas endemicas[^.]*)/i,/(criancas desnutridas[^.]*)/i],r=[];for(const n of t){const t=e.match(n);t&&r.push(yP.cleanText(t[1]))}return 0===r.length?e.includes("pre-termo")?"Indicado para pre-termo":e.includes("areas endemicas")?"Para areas endemicas ou risco aumentado":"Administrar conforme orientacao":r.join(". ")}static generateVaccineData(e){const t=e.age,r=[],n=[];return t>=0&&r.push({name:"BCG",age:"Ao nascer"},{name:"Hepatite B",age:"Ao nascer"}),t>=2&&r.push({name:"Pentavalente",age:"2 meses"},{name:"VIP",age:"2 meses"},{name:"Pneumococica",age:"2 meses"},{name:"Rotavirus",age:"2 meses"}),t>=4&&r.push({name:"Pentavalente (2a dose)",age:"4 meses"},{name:"VIP (2a dose)",age:"4 meses"},{name:"Pneumococica (2a dose)",age:"4 meses"},{name:"Rotavirus (2a dose)",age:"4 meses"}),t>=6&&r.push({name:"Pentavalente (3a dose)",age:"6 meses"},{name:"VIP (3a dose)",age:"6 meses"}),t>=12&&r.push({name:"Triplice viral",age:"12 meses"},{name:"Pneumococica (reforco)",age:"12 meses"}),t<4?n.push({name:"Pentavalente (2a dose)",age:"4 meses"},{name:"VIP (2a dose)",age:"4 meses"},{name:"Pneumococica (2a dose)",age:"4 meses"},{name:"Rotavirus (2a dose)",age:"4 meses"}):t<6?n.push({name:"Pentavalente (3a dose)",age:"6 meses"},{name:"VIP (3a dose)",age:"6 meses"}):t<12?n.push({name:"Triplice viral",age:"12 meses"},{name:"Pneumococica (reforco)",age:"12 meses"}):t<15&&n.push({name:"DTP (reforco)",age:"15 meses"},{name:"VOP (reforco)",age:"15 meses"}),{applied:r,upcoming:n}}}function kP({patientData:e,analysisData:t,supplementationData:n,vaccineData:i,useCorrectedAge:a}){return r.jsx("div",{className:"flex gap-4 justify-center",children:r.jsxs(x,{onClick:async()=>{try{const r={name:e.name,age:e.age,gender:e.gender,weight:e.weight,height:e.height,headCircumference:e.headCircumference,birthWeight:e.birthWeight,gestationalAge:e.gestationalAge,maturity:e.maturity,exclusiveBreastfeeding:e.exclusiveBreastfeeding,riskFactors:e.riskFactors},o=t||await OP.generateAnalysisData(r),s=n||OP.generateSupplementationData(r),c=i||OP.generateVaccineData(r),l=await yP.loadLogo(),u=new de({orientation:"portrait",unit:"mm",format:"a4",compress:!0,putOnlyUsedFonts:!0,floatPrecision:16});u.setFont("helvetica");const f=new vP(u,l);f.renderHeader(),f.renderPatientData(r,a),f.renderPerinatalData(r,o),f.renderAnalysis(o,r),f.renderSupplementation(s),f.renderVaccines(c),f.renderFooter();const d=yP.generateFileName(e.name);u.save(d)}catch(r){alert(`Erro ao gerar PDF: ${r.message||"Erro desconhecido"}. Tente novamente.`)}},className:"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group",children:[r.jsx(U,{className:"mr-2 h-5 w-5 group-hover:scale-110 transition-transform"}),r.jsx("span",{className:"font-medium",children:"📄 Gerar Laudo PDF"}),r.jsx(pe,{className:"ml-2 h-4 w-4 group-hover:translate-y-[1px] transition-transform"})]})})}const SP=e=>{if(!e||e<=0)return"Não informado";const t=Math.floor(e),r=Math.round(7*(e-t));return 7===r?`${t+1} semanas`:0===t?`${r} ${1===r?"dia":"dias"}`:0===r?`${t} ${1===t?"semana":"semanas"}`:`${t} ${1===t?"semana":"semanas"} e ${r} ${1===r?"dia":"dias"}`};function PP({patientData:e,onBack:t,onPatientDataUpdate:i,useCorrectedAge:a=!1,onCorrectedAgeChange:o}){const[s,c]=n.useState({}),l="Pre-term"===e.maturity,u=e=>{o&&o(e)};n.useEffect((()=>{(async()=>{try{const{data:t}=await _.from("pedbook_growth_curve_metadata").select("data").eq("gender",e.gender).eq("type","weight").single(),{data:r}=await _.from("pedbook_growth_curve_metadata").select("data").eq("gender",e.gender).eq("type","height").single(),{data:n}=await _.from("pedbook_growth_curve_metadata").select("data").eq("gender",e.gender).eq("type","head-circumference").single(),i={},a=(e,t,r)=>{if(!t||0===t.length)return null;const n=t.sort(((e,t)=>e.age_months-t.age_months));let i=n[0],a=n[n.length-1];for(let m=0;m<n.length-1;m++)if(r>=n[m].age_months&&r<=n[m+1].age_months){i=n[m],a=n[m+1];break}const o=(r-i.age_months)/(a.age_months-i.age_months),s=i.percentiles["3rd"]+(a.percentiles["3rd"]-i.percentiles["3rd"])*o,c=i.percentiles["15th"]+(a.percentiles["15th"]-i.percentiles["15th"])*o,l=i.percentiles["50th"]+(a.percentiles["50th"]-i.percentiles["50th"])*o,u=i.percentiles["85th"]+(a.percentiles["85th"]-i.percentiles["85th"])*o,f=i.percentiles["97th"]+(a.percentiles["97th"]-i.percentiles["97th"])*o;let d=50,p="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",h="normal";return e<=s?(d=3,p="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",h="muito_baixo"):e<=c?(d=Math.round(3+(e-s)/(c-s)*12),p="bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",h="baixo"):e<=l?(d=Math.round(15+(e-c)/(l-c)*35),p="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",h="normal"):e<=u?(d=Math.round(50+(e-l)/(u-l)*35),p="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",h="normal"):e<=f?(d=Math.round(85+(e-u)/(f-u)*12),p="bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",h="alto"):(d=97,p="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",h="muito_alto"),{percentile:d,color:p,interpretation:h}};if(e.weight&&t?.data){const r=a(e.weight,t.data,e.age);r&&(i.weight=r)}if(e.height&&r?.data){const t=a(e.height,r.data,e.age);t&&(i.height=t)}if(e.headCircumference&&n?.data){const t=a(e.headCircumference,n.data,e.age);t&&(i.head=t)}c(i)}catch(t){}})()}),[e.weight,e.height,e.headCircumference,e.age,e.gender]);const f=()=>"male"===e.gender?"bg-blue-100 text-blue-600 dark:bg-blue-900/40 dark:text-blue-400":"bg-pink-100 text-pink-600 dark:bg-pink-900/40 dark:text-pink-400",d=e=>{const t=Math.floor(e),r=Math.round(30.44*(e-t));if(0===t)return`${r} ${1===r?"dia":"dias"}`;if(t>=12){const e=Math.floor(t/12),n=t%12;return 0===n&&0===r?`${e} ${1===e?"ano":"anos"}`:0===n?`${e} ${1===e?"ano":"anos"} e ${r} ${1===r?"dia":"dias"}`:0===r?`${e} ${1===e?"ano":"anos"} e ${n} ${1===n?"mês":"meses"}`:`${e} ${1===e?"ano":"anos"}, ${n} ${1===n?"mês":"meses"} e ${r} ${1===r?"dia":"dias"}`}return 0===r?`${t} ${1===t?"mês":"meses"}`:`${t} ${1===t?"mês":"meses"} e ${r} ${1===r?"dia":"dias"}`};return r.jsx("div",{className:"space-y-4",children:r.jsxs("div",{className:"space-y-4",children:[r.jsxs(m,{className:"p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800",children:[r.jsxs("div",{className:"block sm:hidden space-y-3",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm flex-shrink-0",children:r.jsx(Z,{className:"h-4 w-4 text-white"})}),r.jsx("div",{className:"min-w-0 flex-1",children:r.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:e.name||"Paciente"})})]}),r.jsxs("div",{className:"flex flex-wrap items-center gap-1.5",children:[r.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs font-medium ${f()}`,children:"male"===e.gender?"♂ Masc":"♀ Fem"}),r.jsx("span",{className:"px-2 py-0.5 rounded-full text-xs font-medium "+("Pre-term"===e.maturity?"text-yellow-700 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400":"text-green-700 bg-green-100 dark:bg-green-900/20 dark:text-green-400"),children:"Pre-term"===e.maturity?"Pré-termo":"A termo"})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("span",{className:"flex items-center gap-1 text-gray-600 dark:text-gray-400 text-sm",children:[r.jsx(K,{className:"h-3 w-3 flex-shrink-0"}),r.jsxs("span",{className:"truncate",children:[a?"Corrigida:":"Cronológica:"," ",d(e.age)]})]}),l&&r.jsxs("div",{className:"flex items-center gap-1.5 p-1.5 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200 dark:border-blue-700 flex-shrink-0",children:[r.jsx(he,{className:"h-3 w-3 text-blue-600 dark:text-blue-400"}),r.jsx(ee,{checked:a,onCheckedChange:u,className:"data-[state=checked]:bg-blue-600 scale-75"})]})]})]}),r.jsxs("div",{className:"hidden sm:flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-3 min-w-0 flex-1",children:[r.jsx("div",{className:"p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm flex-shrink-0",children:r.jsx(Z,{className:"h-5 w-5 text-white"})}),r.jsxs("div",{className:"min-w-0 flex-1",children:[r.jsx("div",{className:"mb-1",children:r.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:e.name||"Paciente"})}),r.jsxs("div",{className:"flex items-center gap-2 text-sm flex-wrap",children:[r.jsx("span",{className:`px-2 py-0.5 rounded-full text-xs font-medium ${f()}`,children:"male"===e.gender?"♂ Masculino":"♀ Feminino"}),r.jsx("span",{className:"px-2 py-0.5 rounded-full text-xs font-medium "+("Pre-term"===e.maturity?"text-yellow-700 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400":"text-green-700 bg-green-100 dark:bg-green-900/20 dark:text-green-400"),children:"Pre-term"===e.maturity?"Pré-termo":"A termo"}),r.jsxs("span",{className:"flex items-center gap-1 text-gray-600 dark:text-gray-400",children:[r.jsx(K,{className:"h-3 w-3"}),a?"Corrigida:":"Cronológica:"," ",d(e.age)]})]})]})]}),l&&r.jsxs("div",{className:"flex items-center gap-2 p-2 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200 dark:border-blue-700 flex-shrink-0",children:[r.jsx(he,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),r.jsx("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:a?"Corrigida":"Cronológica"}),r.jsx(ee,{checked:a,onCheckedChange:u,className:"data-[state=checked]:bg-blue-600 scale-75"})]})]})]}),r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4",children:[r.jsxs(m,{className:"p-3 sm:p-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 min-w-0",children:[r.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2",children:[r.jsx(Q,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),"Medidas & Percentis OMS"]}),e.birthWeight&&e.gestationalAge&&r.jsx("div",{className:"mb-3 p-2 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg border border-purple-200 dark:border-purple-800",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(me,{className:"h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0"}),r.jsxs("div",{children:[r.jsxs("span",{className:"font-medium text-gray-900 dark:text-white",children:[e.birthWeight,"g"]}),r.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Peso ao nascer (",SP(e.gestationalAge),")"]})]})]}),(()=>{const t=xP(e.birthWeight,e.gestationalAge);return r.jsxs("div",{className:"text-right",children:[r.jsxs("span",{className:`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${jP(t.classification)}`,children:[wP(t.classification)," ",t.classification," (P",t.percentile,")"]}),r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"PIG"===t.classification?"Risco de hipoglicemia":"GIG"===t.classification?"Risco de parto traumático":"Crescimento adequado"})]})})()]})}),r.jsxs("div",{className:"space-y-2",children:[e.gestationalAge&&r.jsxs("div",{className:"flex items-center justify-between p-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg min-w-0",children:[r.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(K,{className:"h-4 w-4 text-indigo-600 dark:text-indigo-400 flex-shrink-0"}),r.jsxs("div",{children:[r.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"IG"}),r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:(y=e.gestationalAge,y<28?"🔴 Extremamente prematuro":y>=28&&y<32?"🟠 Muito prematuro":y>=32&&y<34?"🟡 Prematuro moderado":y>=34&&y<37?"🟡 Prematuro tardio":y>=37&&y<39?"🟢 Pré-termo limítrofe":y>=39&&y<41?"🟢 Termo completo":y>=41&&y<42?"🟢 Termo tardio":y>=42?"🔵 Pós-termo":"A termo")})]})]}),r.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:SP(e.gestationalAge)})]}),e.weight&&r.jsxs("div",{className:"flex items-center justify-between p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg min-w-0",children:[r.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(ke,{className:"h-4 w-4 text-orange-600 dark:text-orange-400 flex-shrink-0"}),r.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300 truncate",children:"Peso"})]}),r.jsxs("div",{className:"flex items-center gap-1.5 sm:gap-2 flex-shrink-0",children:[r.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:(e=>{const t=Math.round(1e3*e);if(e<1)return`${t}g`;const r=Math.floor(e),n=t-1e3*r;return 0===n?`${r}kg`:`${r}kg ${n}g`})(e.weight)}),s.weight?r.jsxs("div",{className:`text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full ${s.weight.color} whitespace-nowrap`,children:["P",s.weight.percentile]}):r.jsx("div",{className:"text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400 whitespace-nowrap",children:"P..."})]})]}),e.height&&r.jsxs("div",{className:"flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded-lg min-w-0",children:[r.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(Q,{className:"h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0"}),r.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300 truncate",children:"Altura"})]}),r.jsxs("div",{className:"flex items-center gap-1.5 sm:gap-2 flex-shrink-0",children:[r.jsxs("span",{className:"font-medium text-gray-900 dark:text-white",children:[e.height," cm"]}),s.height?r.jsxs("div",{className:`text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full ${s.height.color} whitespace-nowrap`,children:["P",s.height.percentile]}):r.jsx("div",{className:"text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400 whitespace-nowrap",children:"P..."})]})]}),e.headCircumference&&r.jsxs("div",{className:"flex items-center justify-between p-2 bg-teal-50 dark:bg-teal-900/20 rounded-lg min-w-0",children:[r.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(h,{className:"h-4 w-4 text-teal-600 dark:text-teal-400 flex-shrink-0"}),r.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300 truncate",children:"PC"})]}),r.jsxs("div",{className:"flex items-center gap-1.5 sm:gap-2 flex-shrink-0",children:[r.jsxs("span",{className:"font-medium text-gray-900 dark:text-white",children:[e.headCircumference," cm"]}),s.head?r.jsxs("div",{className:`text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full ${s.head.color} whitespace-nowrap`,children:["P",s.head.percentile]}):r.jsx("div",{className:"text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400 whitespace-nowrap",children:"P..."})]})]})]})]}),r.jsxs(m,{className:"p-3 sm:p-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 min-w-0",children:[r.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2",children:[r.jsx(q,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),"Dados Clínicos"]}),r.jsxs("div",{className:"space-y-2",children:[void 0!==e.exclusiveBreastfeeding&&r.jsxs("div",{className:"flex items-center justify-between p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg",children:[r.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(p,{className:"h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0"}),r.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Amamentação"})]}),r.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:e.exclusiveBreastfeeding?"Exclusiva":"Não exclusiva"})]}),e.riskFactors&&e.riskFactors.length>0&&r.jsxs("div",{className:"p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg",children:[r.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[r.jsx(q,{className:"h-4 w-4 text-amber-600 dark:text-amber-400 flex-shrink-0"}),r.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Fatores de Risco"})]}),r.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 leading-relaxed",children:((e=[])=>{const t={prematurity:"Prematuridade",low_birth_weight:"Baixo peso ao nascer",poor_iron_diet:"Alimentação pobre em ferro",exclusive_breastfeeding_gt_6m_without_supplement:"AME > 6m sem ferro",multiple_pregnancy:"Gestação múltipla",maternal_anemia:"Anemia materna",frequent_infections:"Infecções frequentes",multiple_gestation:"Gestação múltipla",early_cow_milk_exposure:"Leite de vaca precoce",low_socioeconomic_status:"Baixo nível socioeconômico",vegetarian_diet_without_supplement:"Dieta vegetariana sem suplemento"};return e.map((e=>t[e]||e)).join(", ")})(e.riskFactors)})]})]})]})]})]})});var y}function AP({data:e,onBack:t,onPatientDataUpdate:i}){const[a,o]=n.useState("growth"),[s,c]=n.useState(!1),l=e=>{o(e)},u=s?((e,t)=>{if(!t||t>=37)return e;const r=7*(40-t),n=(Math.round(30*e)-r)/30;return Math.max(0,n)})(e.age,e.gestationalAge):e.age;return r.jsxs("div",{className:"space-y-8 animate-fade-in-up",children:[r.jsx(PP,{patientData:{...e,age:u},onBack:t,onPatientDataUpdate:i,useCorrectedAge:s,onCorrectedAgeChange:c}),r.jsx(m,{className:"overflow-hidden border-primary/10 bg-gradient-to-br from-white via-primary/5 to-white dark:from-slate-800 dark:via-slate-800/70 dark:to-slate-800 shadow-lg",children:r.jsxs(A,{value:a,onValueChange:l,className:"w-full",children:[r.jsx("div",{className:"md:hidden w-full p-1 bg-white dark:bg-slate-800 border-b border-primary/10",children:r.jsxs(w,{value:a,onValueChange:l,children:[r.jsx(j,{className:"w-full bg-white dark:bg-slate-800 border-primary/20 hover:border-primary/40 transition-colors",children:r.jsx(O,{placeholder:r.jsxs("div",{className:"flex items-center gap-2 text-primary/70",children:[r.jsx(te,{className:"h-4 w-4"}),r.jsx("span",{children:"Selecione uma análise"})]})})}),r.jsxs(k,{children:[r.jsx(S,{value:"growth",className:"cursor-pointer hover:bg-primary/5 transition-colors",children:r.jsxs("div",{className:"flex items-center gap-2 py-1",children:[r.jsx(te,{className:"h-4 w-4 text-orange-500"}),r.jsx("span",{className:"font-medium text-orange-700 dark:text-orange-400",children:"Crescimento"})]})}),r.jsx(S,{value:"vaccines",className:"cursor-pointer hover:bg-primary/5 transition-colors",children:r.jsxs("div",{className:"flex items-center gap-2 py-1",children:[r.jsx(ye,{className:"h-4 w-4 text-purple-500"}),r.jsx("span",{className:"font-medium text-purple-700 dark:text-purple-400",children:"Vacinas"})]})}),r.jsx(S,{value:"development",className:"cursor-pointer hover:bg-primary/5 transition-colors",children:r.jsxs("div",{className:"flex items-center gap-2 py-1",children:[r.jsx(h,{className:"h-4 w-4 text-blue-500"}),r.jsx("span",{className:"font-medium text-blue-700 dark:text-blue-400",children:"DNPM"})]})}),r.jsx(S,{value:"supplementation",className:"cursor-pointer hover:bg-primary/5 transition-colors",children:r.jsxs("div",{className:"flex items-center gap-2 py-1",children:[r.jsx(ge,{className:"h-4 w-4 text-green-500"}),r.jsx("span",{className:"font-medium text-green-700 dark:text-green-400",children:"Suplementação"})]})})]})]})}),r.jsx(N,{className:"hidden md:flex w-full p-1 bg-white dark:bg-slate-800 border-b border-primary/10",children:r.jsxs("div",{className:"flex flex-wrap justify-center gap-2 py-2 w-full px-4",children:[r.jsx(E,{value:"growth",className:"data-[state=active]:bg-orange-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-orange-100 dark:hover:bg-orange-900/20 group text-sm",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(te,{className:"h-5 w-5 group-data-[state=active]:animate-pulse"}),r.jsx("span",{className:"font-medium",children:"Crescimento"})]})}),r.jsx(E,{value:"vaccines",className:"data-[state=active]:bg-purple-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-purple-100 dark:hover:bg-purple-900/20 group text-sm",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(ye,{className:"h-5 w-5 group-data-[state=active]:animate-pulse"}),r.jsx("span",{className:"font-medium",children:"Vacinas"})]})}),r.jsx(E,{value:"development",className:"data-[state=active]:bg-blue-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-blue-100 dark:hover:bg-blue-900/20 group text-sm",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(h,{className:"h-5 w-5 group-data-[state=active]:animate-pulse"}),r.jsx("span",{className:"font-medium",children:"DNPM"})]})}),r.jsx(E,{value:"supplementation",className:"data-[state=active]:bg-green-500 data-[state=active]:text-white px-3 py-2 rounded-lg transition-all duration-300 hover:bg-green-100 dark:hover:bg-green-900/20 group text-sm",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(ge,{className:"h-5 w-5 group-data-[state=active]:animate-pulse"}),r.jsx("span",{className:"font-medium",children:"Suplementação"})]})})]})}),r.jsx(M,{value:"growth",className:"animate-fade-in-up mt-6",children:r.jsx(fP,{initialData:{age:u,gender:e.gender,weight:e.weight,height:e.height,headCircumference:e.headCircumference,maturity:e.maturity},readOnly:!0,useCorrectedAge:s})}),r.jsx(M,{value:"vaccines",className:"animate-fade-in-up mt-6",children:r.jsx(dP,{ageInMonths:u})}),r.jsx(M,{value:"development",className:"animate-fade-in-up mt-6",children:r.jsx(m,{className:"p-6 bg-white dark:bg-slate-800 backdrop-blur-sm border-primary/10 shadow-lg",children:r.jsx(pP,{ageInMonths:u})})}),r.jsx(M,{value:"supplementation",className:"animate-fade-in-up mt-6 px-4",children:r.jsx(mP,{data:{...e,age:u},useCorrectedAge:s})})]})}),r.jsx(m,{className:"p-6 bg-white dark:bg-slate-800 backdrop-blur-sm border-primary/10 shadow-lg",children:r.jsx(kP,{patientData:{name:e.name,age:u,gender:e.gender,weight:e.weight,height:e.height,headCircumference:e.headCircumference,birthWeight:e.birthWeight,gestationalAge:e.gestationalAge,maturity:e.maturity,exclusiveBreastfeeding:e.exclusiveBreastfeeding,riskFactors:e.riskFactors},useCorrectedAge:s})})]})}const NP=n.createContext(null);function EP(){const e=n.useContext(NP);if(!e)throw new Error("useCarousel must be used within a <Carousel />");return e}const MP=n.forwardRef((({orientation:e="horizontal",opts:t,setApi:i,plugins:a,className:o,children:s,...c},l)=>{const[u,f]=ve({...t,axis:"horizontal"===e?"x":"y"},a),[d,p]=n.useState(!1),[h,m]=n.useState(!1),y=n.useCallback((e=>{e&&(p(e.canScrollPrev()),m(e.canScrollNext()))}),[]),g=n.useCallback((()=>{f?.scrollPrev()}),[f]),v=n.useCallback((()=>{f?.scrollNext()}),[f]),b=n.useCallback((e=>{"ArrowLeft"===e.key?(e.preventDefault(),g()):"ArrowRight"===e.key&&(e.preventDefault(),v())}),[g,v]);return n.useEffect((()=>{f&&i&&i(f)}),[f,i]),n.useEffect((()=>{if(f)return y(f),f.on("reInit",y),f.on("select",y),()=>{f?.off("select",y)}}),[f,y]),r.jsx(NP.Provider,{value:{carouselRef:u,api:f,opts:t,orientation:e||("y"===t?.axis?"vertical":"horizontal"),scrollPrev:g,scrollNext:v,canScrollPrev:d,canScrollNext:h},children:r.jsx("div",{ref:l,onKeyDownCapture:b,className:W("relative",o),role:"region","aria-roledescription":"carousel",...c,children:s})})}));MP.displayName="Carousel";const _P=n.forwardRef((({className:e,...t},n)=>{const{carouselRef:i,orientation:a}=EP();return r.jsx("div",{ref:i,className:"overflow-hidden no-swipe","data-draggable":"true",children:r.jsx("div",{ref:n,className:W("flex no-swipe","horizontal"===a?"-ml-4":"-mt-4 flex-col",e),"data-draggable":"true",...t})})}));_P.displayName="CarouselContent";const CP=n.forwardRef((({className:e,...t},n)=>{const{orientation:i}=EP();return r.jsx("div",{ref:n,role:"group","aria-roledescription":"slide",className:W("min-w-0 shrink-0 grow-0 basis-full no-swipe","horizontal"===i?"pl-4":"pt-4",e),"data-draggable":"true",...t})}));function TP(){const e=[{icon:be,title:"Crescimento",description:"Avaliação das curvas de crescimento com percentis e status nutricional",color:"text-orange-500 dark:text-orange-400",bg:"bg-orange-100 dark:bg-orange-900/30"},{icon:G,title:"Vacinas",description:"Verificação do status vacinal com calendário e recomendações",color:"text-purple-500 dark:text-purple-400",bg:"bg-purple-100 dark:bg-purple-900/30"},{icon:h,title:"DNPM",description:"Avaliação dos marcos do desenvolvimento neuropsicomotor",color:"text-blue-500 dark:text-blue-400",bg:"bg-blue-100 dark:bg-blue-900/30"},{icon:ge,title:"Suplementação",description:"Recomendações personalizadas de suplementação vitamínica",color:"text-green-500 dark:text-green-400",bg:"bg-green-100 dark:bg-green-900/30"}];return r.jsxs("div",{className:"mb-6",children:[r.jsxs("div",{className:"text-center mb-4",children:[r.jsx("h1",{className:"text-2xl md:text-4xl font-bold mb-2 text-primary dark:text-blue-400",children:"Visão Geral do Paciente"}),r.jsx("p",{className:"text-sm md:text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Análise completa e personalizada do desenvolvimento infantil"})]}),r.jsx("div",{className:"md:hidden mx-auto max-w-md",children:r.jsxs(MP,{opts:{align:"start",loop:!0},className:"w-full",children:[r.jsx(_P,{className:"-ml-2",children:e.map(((e,t)=>r.jsx(CP,{className:"pl-2 basis-4/5",children:r.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-xl p-3 shadow-md border border-primary/10 transition-all hover:shadow-lg h-full",children:r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:`${e.bg} w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0`,children:r.jsx(e.icon,{className:`h-5 w-5 ${e.color}`})}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-800 dark:text-white text-sm",children:e.title}),r.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2",children:e.description})]})]})})},t)))}),r.jsx("div",{className:"flex justify-center mt-4",children:r.jsx("div",{className:"flex gap-1",children:e.map(((e,t)=>r.jsx("div",{className:"w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600"},t)))})})]})}),r.jsx("div",{className:"hidden md:grid md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 max-w-5xl mx-auto",children:e.map(((e,t)=>r.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md border border-primary/10 transition-all hover:shadow-lg",children:r.jsxs("div",{className:"flex flex-col",children:[r.jsx("div",{className:`${e.bg} w-12 h-12 rounded-full flex items-center justify-center mb-3`,children:r.jsx(e.icon,{className:`h-6 w-6 ${e.color}`})}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-800 dark:text-white text-base",children:e.title}),r.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:e.description})]})]})},t)))}),r.jsx("div",{className:"mt-4 mb-2 md:mt-6 bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 p-3 md:p-4 rounded-lg border border-primary/20 max-w-5xl mx-auto",children:r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:"bg-primary/10 dark:bg-primary/20 p-1.5 md:p-2 rounded-full flex-shrink-0 mt-0.5",children:r.jsx(Y,{className:"h-4 w-4 md:h-5 md:w-5 text-primary dark:text-blue-400"})}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-sm md:text-base font-medium text-primary dark:text-blue-400 mb-1",children:"Como funciona"}),r.jsx("p",{className:"text-xs md:text-sm text-gray-700 dark:text-gray-300",children:"Preencha os dados do paciente abaixo para receber uma análise personalizada"})]})]})})]})}function DP(){const e=we["patient-overview"],[t,i]=n.useState(!1),[a,o]=n.useState(null);return r.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[r.jsx(xe,{...e}),r.jsx(H,{}),r.jsxs("main",{className:"flex-1 container mx-auto px-4 py-8 md:py-12",children:[r.jsxs("div",{className:"flex items-center justify-between mb-8",children:[r.jsxs(s,{to:"/puericultura",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors dark:text-blue-400 dark:hover:text-blue-300",children:[r.jsx(V,{className:"h-5 w-5"}),r.jsx("span",{children:"Voltar para Puericultura"})]}),t&&r.jsxs("button",{onClick:()=>i(!1),className:"inline-flex items-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 group",children:[r.jsx("span",{className:"hidden sm:inline",children:"✏️"}),r.jsxs("span",{className:"font-medium",children:[r.jsx("span",{className:"hidden sm:inline",children:"Editar Dados"}),r.jsx("span",{className:"sm:hidden",children:"Editar"})]}),r.jsx(V,{className:"h-4 w-4 rotate-180 group-hover:translate-x-[2px] transition-transform"})]})]}),t?r.jsx(AP,{data:a,onBack:()=>i(!1),onPatientDataUpdate:e=>{o((t=>t?{...t,...e}:null))}}):r.jsxs(r.Fragment,{children:[r.jsx(TP,{}),r.jsxs(m,{className:"max-w-2xl mx-auto p-6 mt-8 border border-primary/10 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg",children:[r.jsx("h2",{className:"text-xl font-bold mb-4 text-primary dark:text-blue-400",children:a?"Editar dados do paciente":"Preencha os dados do paciente"}),r.jsx(Ne,{onSubmit:e=>{o(e),i(!0)},defaultValues:a})]})]})]}),r.jsx(X,{})]})}CP.displayName="CarouselItem",n.forwardRef((({className:e,variant:t="outline",size:n="icon",...i},a)=>{const{orientation:o,scrollPrev:s,canScrollPrev:c}=EP();return r.jsxs(x,{ref:a,variant:t,size:n,className:W("absolute  h-8 w-8 rounded-full","horizontal"===o?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:s,...i,children:[r.jsx(V,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Previous slide"})]})})).displayName="CarouselPrevious",n.forwardRef((({className:e,variant:t="outline",size:n="icon",...i},a)=>{const{orientation:o,scrollNext:s,canScrollNext:c}=EP();return r.jsxs(x,{ref:a,variant:t,size:n,className:W("absolute h-8 w-8 rounded-full","horizontal"===o?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:s,...i,children:[r.jsx(le,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Next slide"})]})})).displayName="CarouselNext";export{DP as default};
