import{j as r}from"./radix-core-6kBL75b5.js";import{L as e}from"./router-BAzpOxbo.js";import{a8 as o,aa as a,a7 as t}from"./index-CrSshpOb.js";import i from"./Footer-ClHMSbsi.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";const s=()=>r.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[r.jsxs(o,{children:[r.jsx("title",{children:"PedBook | Animais Peçonhentos"}),r.jsx("meta",{name:"description",content:"Fluxogramas para manejo de acidentes com animais peçonhentos"})]}),r.jsx(a,{}),r.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:r.jsxs("div",{className:"max-w-5xl mx-auto space-y-8",children:[r.jsx("div",{className:"flex items-center gap-4",children:r.jsxs(e,{to:"/flowcharts",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors",children:[r.jsx(t,{className:"h-5 w-5"}),r.jsx("span",{children:"Voltar para Fluxogramas"})]})}),r.jsxs("div",{className:"text-center space-y-4",children:[r.jsx("h1",{className:"text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400",children:"Animais Peçonhentos"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Fluxogramas para manejo de acidentes com animais peçonhentos em pediatria"})]}),r.jsx("div",{className:"grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6",children:[{id:"scorpion",title:"Acidente Escorpiônico",description:"Fluxograma para manejo de acidentes escorpiônicos em pediatria",path:"/flowcharts/venomous/scorpion",color:"bg-yellow-50 dark:bg-yellow-900/30",borderColor:"border-yellow-200/50 dark:border-yellow-700/50",hoverColor:"hover:bg-yellow-100/70 dark:hover:bg-yellow-800/40",textColor:"text-gray-800 dark:text-gray-100",descriptionColor:"text-gray-600 dark:text-gray-300",iconBg:"bg-white/90 dark:bg-slate-800/90",icon:"🦂"},{id:"bothropic",title:"Acidente Botrópico",description:"Fluxograma para manejo de acidentes botrópicos em pediatria",path:"/flowcharts/venomous/bothropic",color:"bg-green-50 dark:bg-green-900/30",borderColor:"border-green-200/50 dark:border-green-700/50",hoverColor:"hover:bg-green-100/70 dark:hover:bg-green-800/40",textColor:"text-gray-800 dark:text-gray-100",descriptionColor:"text-gray-600 dark:text-gray-300",iconBg:"bg-white/90 dark:bg-slate-800/90",icon:"🐍"},{id:"crotalic",title:"Acidente Crotálico",description:"Fluxograma para manejo de acidentes crotálicos em pediatria",path:"/flowcharts/venomous/crotalic",color:"bg-purple-50 dark:bg-purple-900/30",borderColor:"border-purple-200/50 dark:border-purple-700/50",hoverColor:"hover:bg-purple-100/70 dark:hover:bg-purple-800/40",textColor:"text-gray-800 dark:text-gray-100",descriptionColor:"text-gray-600 dark:text-gray-300",iconBg:"bg-white/90 dark:bg-slate-800/90",icon:"🐍"},{id:"elapidic",title:"Acidente Elapídico",description:"Fluxograma para manejo de acidentes elapídicos (coral verdadeira) em pediatria",path:"/flowcharts/venomous/elapidic",color:"bg-red-50 dark:bg-red-900/30",borderColor:"border-red-200/50 dark:border-red-700/50",hoverColor:"hover:bg-red-100/70 dark:hover:bg-red-800/40",textColor:"text-gray-800 dark:text-gray-100",descriptionColor:"text-gray-600 dark:text-gray-300",iconBg:"bg-white/90 dark:bg-slate-800/90",icon:"🐍"},{id:"phoneutria",title:"Acidente Fonêutrico",description:"Fluxograma para manejo de acidentes com aranha armadeira em pediatria",path:"/flowcharts/venomous/phoneutria",color:"bg-orange-50 dark:bg-orange-900/30",borderColor:"border-orange-200/50 dark:border-orange-700/50",hoverColor:"hover:bg-orange-100/70 dark:hover:bg-orange-800/40",textColor:"text-gray-800 dark:text-gray-100",descriptionColor:"text-gray-600 dark:text-gray-300",iconBg:"bg-white/90 dark:bg-slate-800/90",icon:"🕷️"},{id:"loxoscelic",title:"Acidente Loxoscélico",description:"Fluxograma para manejo de acidentes com aranha marrom em pediatria",path:"/flowcharts/venomous/loxoscelic",color:"bg-amber-50 dark:bg-amber-900/30",borderColor:"border-amber-200/50 dark:border-amber-700/50",hoverColor:"hover:bg-amber-100/70 dark:hover:bg-amber-800/40",textColor:"text-gray-800 dark:text-gray-100",descriptionColor:"text-gray-600 dark:text-gray-300",iconBg:"bg-white/90 dark:bg-slate-800/90",icon:"🕷️"}].map((o=>r.jsx(e,{to:o.path,className:"block group transform transition-all duration-500 hover:scale-[1.02]",children:r.jsx("div",{className:`h-full p-6 rounded-2xl ${o.color} border ${o.borderColor} transition-all duration-300 ${o.hoverColor} hover:shadow-lg`,children:r.jsxs("div",{className:"flex flex-col items-center text-center h-full",children:[r.jsx("div",{className:`${o.iconBg} w-16 h-16 rounded-xl flex items-center justify-center mb-4 shadow-sm transition-transform group-hover:scale-110`,children:r.jsx("span",{className:"text-2xl",children:o.icon})}),r.jsxs("div",{className:"space-y-3 flex-1",children:[r.jsx("h3",{className:`text-xl font-semibold ${o.textColor}`,children:o.title}),r.jsx("p",{className:`text-sm ${o.descriptionColor} mt-2`,children:o.description})]})]})})},o.id)))})]})}),r.jsx(i,{})]});export{s as default};
