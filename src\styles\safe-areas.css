/**
 * Safe Areas CSS - Específico para Capacitor Mobile
 * 
 * Este arquivo contém estilos que respeitam as safe areas do dispositivo
 * sem afetar o layout web. Usa CSS custom properties definidas pelo JavaScript.
 */

/* ===== VARIÁVEIS SAFE AREAS ===== */
:root {
  /* Fallback values - serão sobrescritas pelo JavaScript */
  --safe-area-inset-top: 0px;
  --safe-area-inset-bottom: 0px;
  --safe-area-inset-left: 0px;
  --safe-area-inset-right: 0px;
  --safe-area-height: 100vh;
  --safe-area-width: 100vw;
}

/* ===== BODY E HTML BASE ===== */
/* Apenas para Capacitor - não afeta web */
.capacitor body {
  /* Garantir que o body respeite as safe areas */
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
  padding-right: var(--safe-area-inset-right);
  
  /* Ajustar altura para compensar os paddings */
  min-height: var(--safe-area-height);
  box-sizing: border-box;
}

/* ===== CONTAINERS PRINCIPAIS ===== */
/* Container principal do app */
.capacitor .app-container {
  min-height: var(--safe-area-height);
  width: var(--safe-area-width);
}

/* Main content area */
.capacitor main {
  min-height: var(--safe-area-height);
}

/* ===== NAVEGAÇÃO MOBILE ===== */
/* Bottom navigation - ajustar para gesture bar */
.capacitor .nav-mobile {
  /* Remover padding bottom do body para este elemento */
  margin-bottom: calc(-1 * var(--safe-area-inset-bottom));
  padding-bottom: var(--safe-area-inset-bottom);
}

/* ===== BOTÕES FLUTUANTES ===== */
/* Dr. Will floating button - acima do menu mobile */
.capacitor .floating-chat-button {
  /* Ajustar posição para ficar acima do menu mobile e gesture bar */
  bottom: calc(6rem + var(--safe-area-inset-bottom));
}

/* Theme toggle button - acima do menu mobile */
.capacitor .mobile-theme-toggle {
  /* Ajustar posição para ficar acima do menu mobile e gesture bar */
  bottom: calc(6rem + var(--safe-area-inset-bottom));
}

/* ===== MODAIS E OVERLAYS ===== */
/* Modal containers */
.capacitor .modal-container,
.capacitor .dialog-container {
  /* Garantir que modais respeitem safe areas */
  top: var(--safe-area-inset-top);
  bottom: var(--safe-area-inset-bottom);
  left: var(--safe-area-inset-left);
  right: var(--safe-area-inset-right);
  height: var(--safe-area-height);
  width: var(--safe-area-width);
}

/* ===== PÁGINAS ESPECÍFICAS ===== */
/* Páginas fullscreen */
.capacitor .page-fullscreen {
  height: var(--safe-area-height);
  padding-top: 0;
  padding-bottom: 0;
}

/* Header fixo */
.capacitor .header-fixed {
  top: var(--safe-area-inset-top);
}

/* Footer fixo */
.capacitor .footer-fixed {
  bottom: var(--safe-area-inset-bottom);
}

/* ===== AJUSTES PARA TECLADO ===== */
/* Quando o teclado está aberto, ajustar viewport */
.capacitor.keyboard-open {
  /* O JavaScript pode adicionar esta classe quando detectar teclado */
}

.capacitor.keyboard-open .nav-mobile {
  /* Esconder navegação quando teclado aberto */
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.capacitor.keyboard-open .floating-chat-button,
.capacitor.keyboard-open .mobile-theme-toggle {
  /* Esconder botões flutuantes quando teclado aberto */
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* ===== ORIENTAÇÃO ===== */
/* Landscape adjustments */
@media screen and (orientation: landscape) {
  .capacitor body {
    /* Em landscape, safe areas laterais podem ser maiores */
    padding-left: var(--safe-area-inset-left);
    padding-right: var(--safe-area-inset-right);
  }
}

/* ===== CLASSES UTILITÁRIAS ===== */
/* Para aplicar safe areas manualmente */
.safe-area-top {
  padding-top: var(--safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: var(--safe-area-inset-left);
}

.safe-area-right {
  padding-right: var(--safe-area-inset-right);
}

.safe-area-all {
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
  padding-right: var(--safe-area-inset-right);
}

/* Margin variants */
.safe-margin-top {
  margin-top: var(--safe-area-inset-top);
}

.safe-margin-bottom {
  margin-bottom: var(--safe-area-inset-bottom);
}

.safe-margin-left {
  margin-left: var(--safe-area-inset-left);
}

.safe-margin-right {
  margin-right: var(--safe-area-inset-right);
}

/* Height/Width utilities */
.safe-height {
  height: var(--safe-area-height);
}

.safe-width {
  width: var(--safe-area-width);
}

.safe-min-height {
  min-height: var(--safe-area-height);
}

/* ===== FIXES PARA LAYOUT MOBILE ===== */
/* Prevenir problemas de overflow em botões dentro de containers fixed */
.android-safe-area button,
.android-safe-area .btn {
  position: relative;
  z-index: 10;
  flex-shrink: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

/* Garantir que cards não quebrem o layout */
.android-safe-area .card,
.android-safe-area [class*="card"] {
  position: relative;
  contain: layout;
}

/* Fix específico para botões escapando dos cards na calculadora de hidratação */
.android-safe-area .group {
  overflow: hidden !important;
  contain: layout style !important;
}

.android-safe-area .group > div {
  position: relative !important;
  overflow: hidden !important;
  height: auto !important;
  min-height: fit-content !important;
}

/* Garantir que botões fiquem dentro dos containers */
.android-safe-area .group button {
  position: relative !important;
  z-index: 1 !important;
  margin-top: auto !important;
  flex-shrink: 0 !important;
}

/* ===== DEBUGGING ===== */
/* Para visualizar safe areas durante desenvolvimento */
.debug-safe-areas::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--safe-area-inset-top);
  background: rgba(255, 0, 0, 0.3);
  z-index: 9999;
  pointer-events: none;
}

.debug-safe-areas::after {
  content: '';
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--safe-area-inset-bottom);
  background: rgba(0, 255, 0, 0.3);
  z-index: 9999;
  pointer-events: none;
}

/* ===== COMPATIBILIDADE ===== */
/* Fallback para dispositivos que não suportam CSS custom properties */
@supports not (padding: var(--safe-area-inset-top)) {
  .capacitor body {
    padding-top: 24px; /* Status bar típica */
    padding-bottom: 48px; /* Gesture bar típica */
  }
  
  .capacitor .nav-mobile {
    margin-bottom: -48px;
    padding-bottom: 48px;
  }
  
  .capacitor .floating-chat-button,
  .capacitor .mobile-theme-toggle {
    bottom: calc(5rem + 48px);
  }
}
