import{j as r}from"./radix-core-6kBL75b5.js";import{aX as a,j as o,aZ as e,bc as t}from"./index-Dq2DDcRF.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const s=()=>{const{theme:s,toggleTheme:i}=a();return r.jsx("div",{className:"mobile-theme-toggle fixed left-3 bottom-24 z-[85] sm:hidden",children:r.jsx("button",{onClick:i,className:o("w-9 h-9 rounded-full flex items-center justify-center transition-all duration-300","focus:outline-none focus:ring-2 focus:ring-primary/20 dark:focus:ring-blue-500/30","dark"===s?"text-yellow-300 bg-slate-800/90 hover:bg-slate-700/90 shadow-md backdrop-blur-sm":"text-primary bg-white/90 hover:bg-gray-100/90 shadow-md backdrop-blur-sm"),"aria-label":"dark"===s?"Alternar para modo claro":"Alternar para modo escuro",title:"dark"===s?"Alternar para modo claro":"Alternar para modo escuro",children:"dark"===s?r.jsx(e,{className:"h-4 w-4"}):r.jsx(t,{className:"h-4 w-4"})})})};export{s as MobileThemeToggle,s as default};
