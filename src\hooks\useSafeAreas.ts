import { useEffect, useState } from 'react';
import { getSafeAreas, clearSafeAreasCache, type SafeAreas } from '@/utils/safeAreas';
import { isCapacitor } from '@/utils/capacitorUtils';

/**
 * Hook para gerenciar safe areas no React
 */
export const useSafeAreas = () => {
  const [safeAreas, setSafeAreas] = useState<SafeAreas>({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isCapacitor()) {
      setIsLoading(false);
      return;
    }

    const loadSafeAreas = async () => {
      try {
        const areas = await getSafeAreas();
        setSafeAreas(areas);
      } catch (error) {
        console.error('Erro ao carregar safe areas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSafeAreas();

    // Listener para mudanças de orientação
    const handleOrientationChange = () => {
      clearSafeAreasCache();
      setTimeout(loadSafeAreas, 100); // Delay para aguardar mudança completa
    };

    // Listener para mudanças de viewport (teclado, etc.)
    const handleResize = () => {
      clearSafeAreasCache();
      loadSafeAreas();
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return {
    safeAreas,
    isLoading,
    refresh: async () => {
      clearSafeAreasCache();
      const areas = await getSafeAreas();
      setSafeAreas(areas);
    }
  };
};

/**
 * Hook para aplicar safe areas a um elemento específico
 */
export const useSafeAreaElement = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    top?: boolean;
    bottom?: boolean;
    left?: boolean;
    right?: boolean;
  } = {}
) => {
  const { safeAreas, isLoading } = useSafeAreas();

  useEffect(() => {
    if (isLoading || !elementRef.current || !isCapacitor()) return;

    const element = elementRef.current;
    const { top = true, bottom = true, left = true, right = true } = options;

    // Aplicar safe areas como padding
    if (top && safeAreas.top > 0) {
      element.style.paddingTop = `${safeAreas.top}px`;
    }
    if (bottom && safeAreas.bottom > 0) {
      element.style.paddingBottom = `${safeAreas.bottom}px`;
    }
    if (left && safeAreas.left > 0) {
      element.style.paddingLeft = `${safeAreas.left}px`;
    }
    if (right && safeAreas.right > 0) {
      element.style.paddingRight = `${safeAreas.right}px`;
    }
  }, [safeAreas, isLoading, elementRef, options]);

  return { safeAreas, isLoading };
};

/**
 * Hook para obter CSS custom properties das safe areas
 */
export const useSafeAreaCSS = () => {
  const { safeAreas, isLoading } = useSafeAreas();

  const cssVars = {
    '--safe-area-inset-top': `${safeAreas.top}px`,
    '--safe-area-inset-bottom': `${safeAreas.bottom}px`,
    '--safe-area-inset-left': `${safeAreas.left}px`,
    '--safe-area-inset-right': `${safeAreas.right}px`,
    '--safe-area-height': `calc(100vh - ${safeAreas.top}px - ${safeAreas.bottom}px)`,
    '--safe-area-width': `calc(100vw - ${safeAreas.left}px - ${safeAreas.right}px)`,
  };

  return {
    cssVars,
    isLoading,
    safeAreas
  };
};
