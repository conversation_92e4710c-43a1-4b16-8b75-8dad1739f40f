import{bd as t,be as r,bf as i}from"./index-D89HBjcn.js";import"./radix-core-6kBL75b5.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";class e extends t{constructor(){super(...arguments),this.selectionStarted=!1}async impact(t){const r=this.patternForImpact(null==t?void 0:t.style);this.vibrateWithPattern(r)}async notification(t){const r=this.patternForNotification(null==t?void 0:t.type);this.vibrateWithPattern(r)}async vibrate(t){const r=(null==t?void 0:t.duration)||300;this.vibrateWithPattern([r])}async selectionStart(){this.selectionStarted=!0}async selectionChanged(){this.selectionStarted&&this.vibrateWithPattern([70])}async selectionEnd(){this.selectionStarted=!1}patternForImpact(t=r.Heavy){return t===r.Medium?[43]:t===r.Light?[20]:[61]}patternForNotification(t=i.Success){return t===i.Warning?[30,40,30,50,60]:t===i.Error?[27,45,50]:[35,65,21]}vibrateWithPattern(t){if(!navigator.vibrate)throw this.unavailable("Browser does not support the vibrate API");navigator.vibrate(t)}}export{e as HapticsWeb};
