import{j as e}from"./radix-core-6kBL75b5.js";import{j as r,P as a,ab as t,B as o,a8 as s,b3 as d}from"./index-DQuOk0R3.js";import i from"./Footer-BmzagX2Z.js";import{P as l,a as n}from"./poisoningSEOData-BJvP4yn5.js";import{a as m}from"./router-BAzpOxbo.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-Bwp4oswe.js";import"./rocket-Czd-at64.js";import"./target-CuA2iBUH.js";import"./zap-B06nQ-rd.js";import"./book-open-gHhE7Hhk.js";import"./star-CaqDe8as.js";import"./circle-help-CTIYt4iy.js";import"./instagram-DOFGyRt3.js";const x=({name:t,color:o,onClick:s})=>{const d=o.split("-")[1];return e.jsxs("button",{className:r("relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer","bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg","border border-gray-100 dark:border-gray-700/50","hover:-translate-y-1 text-center"),onClick:s,children:[e.jsx("div",{className:r("absolute top-0 left-0 right-0 h-1.5 rounded-t-xl","blue"===d?"bg-blue-500":"purple"===d?"bg-purple-500":"emerald"===d?"bg-emerald-500":"red"===d?"bg-red-500":"orange"===d?"bg-orange-500":"indigo"===d?"bg-indigo-500":"yellow"===d?"bg-yellow-500":"pink"===d?"bg-pink-500":"rose"===d?"bg-rose-500":"bg-primary")}),e.jsxs("div",{className:"flex flex-col items-center justify-center h-full pt-2",children:[e.jsx("div",{className:r("w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center mb-2 shadow-sm","blue"===d?"bg-blue-50 dark:bg-blue-900/30":"purple"===d?"bg-purple-50 dark:bg-purple-900/30":"emerald"===d?"bg-emerald-50 dark:bg-emerald-900/30":"red"===d?"bg-red-50 dark:bg-red-900/30":"orange"===d?"bg-orange-50 dark:bg-orange-900/30":"indigo"===d?"bg-indigo-50 dark:bg-indigo-900/30":"yellow"===d?"bg-yellow-50 dark:bg-yellow-900/30":"pink"===d?"bg-pink-50 dark:bg-pink-900/30":"rose"===d?"bg-rose-50 dark:bg-rose-900/30":"bg-gray-50 dark:bg-slate-800"),children:e.jsx(a,{className:r("w-5 h-5 sm:w-6 sm:h-6","blue"===d?"text-blue-700 dark:text-blue-300":"purple"===d?"text-purple-700 dark:text-purple-300":"emerald"===d?"text-emerald-700 dark:text-emerald-300":"red"===d?"text-red-700 dark:text-red-300":"orange"===d?"text-orange-700 dark:text-orange-300":"indigo"===d?"text-indigo-700 dark:text-indigo-300":"yellow"===d?"text-yellow-700 dark:text-yellow-300":"pink"===d?"text-pink-700 dark:text-pink-300":"rose"===d?"text-rose-700 dark:text-rose-300":"text-primary dark:text-blue-400")})}),e.jsx("h3",{className:"font-bold text-sm sm:text-base text-gray-800 dark:text-gray-200",children:t})]})]})},g=()=>{const r=m(),a=n.main;return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-red-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[e.jsx(l,{...a}),e.jsx(t,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-12",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(o,{variant:"ghost",className:"gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100",onClick:()=>r("/"),children:[e.jsx(s,{className:"h-4 w-4"}),"Voltar"]})}),e.jsxs("div",{className:"text-center space-y-4 mb-12",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-red-400 dark:from-red-500 dark:to-red-300",children:"Gerenciador de Intoxicações"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Encontre rapidamente informações sobre toxíndromes, agentes tóxicos, antídotos e doses ajustadas automaticamente com base no peso ou idade do paciente."})]}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:gap-4",children:d.map((a=>e.jsx(x,{name:a.name,color:a.color,onClick:()=>r(`/poisonings/${a.id}`)},a.id)))})]}),e.jsx(i,{})]})};export{g as default};
