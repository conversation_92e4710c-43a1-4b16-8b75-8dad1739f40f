import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{aM as l,ab as s,B as r,U as o,ao as t,ae as i,af as n,ag as u,ah as c,aj as m}from"./index-CNG-Xj2g.js";import d from"./Footer-BgCSiPkf.js";import{L as v}from"./router-BAzpOxbo.js";import{C as b,a as x}from"./calculatorSEOData-BA0ImUJZ.js";import{C as p}from"./chevron-left-Ep4pqQcd.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-ik6vfZ65.js";import"./rocket-BEoGgNr2.js";import"./target-Dul0NbVV.js";import"./zap-C4mKju26.js";import"./book-open-EV5sJdXr.js";import"./star-BUSksJJE.js";import"./circle-help-BbvIlE64.js";import"./instagram-ClgJ7H9i.js";const j=()=>{const j=x.finnegan,[h,g]=a.useState({choro:null,sono:null,moro:null,tremores:null,tonus:null,bocejos:null,escoriacoes:null,convulsoes:null,sudorese:null,febre:null,cutis:null,espirros:null,prurido:null,batimentos:null,respiracao:null,succao:null,alimentacao:null,regurgitacao:null,vomitos:null,fezes:null}),f=Object.values(h).reduce(((e,a)=>e+(a||0)),0),S=(y=f)>=12?{text:"Indica necessidade de tratamento",color:"text-red-600 dark:text-red-400"}:y>=8?{text:"Monitoramento frequente, considerar tratamento se persistente",color:"text-yellow-600 dark:text-yellow-400"}:{text:"Monitoramento contínuo",color:"text-green-600 dark:text-green-400"};var y;const N=Object.values(h).every((e=>null!==e));return e.jsxs("div",{className:l.gradientBackground("min-h-screen flex flex-col"),children:[e.jsx(b,{...j}),e.jsx(s,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-3xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{to:"/calculadoras",children:e.jsx(r,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:e.jsx(p,{className:"h-5 w-5"})})}),e.jsx("h1",{className:l.gradientHeading("text-3xl"),children:"Escala de Finnegan"})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avaliação da síndrome de abstinência neonatal (SAN) em recém-nascidos"}),e.jsxs(o,{className:l.card("p-6 space-y-6"),children:[Object.entries({choro:[{value:3,label:"Contínuo"},{value:2,label:"Excessivo"},{value:0,label:"Nenhum dos acima"}],sono:[{value:3,label:"< 1 hora"},{value:2,label:"< 2 horas"},{value:1,label:"< 3 horas"},{value:0,label:"Nenhum dos acima"}],moro:[{value:3,label:"Hiperatividade marcante"},{value:2,label:"Hiperatividade"},{value:0,label:"Sem hiperatividade"}],tremores:[{value:4,label:"Grave"},{value:3,label:"Moderado a grave"},{value:2,label:"Leve"},{value:1,label:"Sem tremor"}],tonus:[{value:2,label:"Aumento do tônus"},{value:0,label:"Sem aumento"}],bocejos:[{value:1,label:"Frequentes"},{value:0,label:"Sem bocejos frequentes"}],escoriacoes:[{value:1,label:"Escoriação"},{value:0,label:"Sem escoriação"}],convulsoes:[{value:5,label:"Convulsões"},{value:0,label:"Sem convulsões"}],sudorese:[{value:1,label:"Suor"},{value:0,label:"Sem suor"}],febre:[{value:2,label:"≥ 38,3°C"},{value:1,label:"37,8 - 38,3°C"},{value:0,label:"< 37,8ºC"}],cutis:[{value:1,label:"Cutis marmorata"},{value:0,label:"Sem cutis marmorata"}],espirros:[{value:1,label:"Espirros frequentes"},{value:0,label:"Sem espirros frequentes"}],prurido:[{value:1,label:"Prurido nasal"},{value:0,label:"Sem prurido nasal"}],batimentos:[{value:2,label:"Com batimentos"},{value:0,label:"Sem batimentos"}],respiracao:[{value:2,label:"60 irpm + retração intercostal"},{value:1,label:"60 irpm"},{value:0,label:"≤ 60 irpm"}],succao:[{value:1,label:"Sucção excessiva"},{value:0,label:"Sem sucção excessiva"}],alimentacao:[{value:2,label:"Pouca alimentação"},{value:0,label:"Alimentação normal"}],regurgitacao:[{value:2,label:"Com regurgitação"},{value:0,label:"Sem regurgitação"}],vomitos:[{value:3,label:"Vômitos em jato"},{value:0,label:"Sem vômitos em jato"}],fezes:[{value:3,label:"Líquidas"},{value:2,label:"Semipastosas"},{value:0,label:"Pastosas"}]}).map((([a,s])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{className:"text-base font-medium text-gray-800 dark:text-gray-200",children:a.charAt(0).toUpperCase()+a.slice(1).replace(/([A-Z])/g," $1").trim()}),e.jsxs(i,{value:h[a]?.toString()||"",onValueChange:e=>((e,a)=>{g((l=>({...l,[e]:parseInt(a,10)})))})(a,e),children:[e.jsx(n,{className:l.select("w-full"),children:e.jsx(u,{placeholder:"Selecione uma opção"})}),e.jsx(c,{children:s.map((a=>e.jsx(m,{value:a.value.toString(),children:a.label},a.value)))})]})]},a))),N&&e.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"text-center space-y-4",children:[e.jsxs("div",{className:"text-4xl font-bold text-primary dark:text-blue-400",children:[f," pontos"]}),e.jsx("div",{className:`text-xl font-semibold ${S.color}`,children:S.text}),e.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto",children:"Nota: Escores ≥ 8 por 3 vezes consecutivas ou ≥ 12 por 2 vezes consecutivas indicam necessidade de tratamento."})]})})]})]})}),e.jsx(d,{})]})};export{j as default};
