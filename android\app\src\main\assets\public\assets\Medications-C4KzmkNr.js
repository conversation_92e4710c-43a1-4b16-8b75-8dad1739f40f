import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-B3w9PzNx.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-DwBJcqzE.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-DXia2SVU.js";import"./FeedbackTrigger-BC_TYgH3.js";import"./rocket-7eidEF9E.js";import"./target-D2iN3abh.js";import"./zap-BpuRFf_b.js";import"./book-open-C1lgvyyJ.js";import"./star-BwJL-OtG.js";import"./circle-help-OcogOqeH.js";import"./instagram-Dc9Ip0W1.js";import"./collapsible-B6HfSnGs.js";import"./accordion-M6CVpPsM.js";import"./PatientInfoSection-BJyrYluY.js";import"./scale-BGcHTc2j.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-CYN_N3Cw.js";import"./user-CvSIj0YF.js";import"./DosageDisplay-FiD0x-7k.js";import"./alert-XCiMnFRg.js";import"./plus-DseG_YPL.js";import"./lightbulb-BzllhExY.js";import"./external-link-Cs_oj-pB.js";import"./stethoscope-DOFN7fZH.js";import"./syringe-BX3eTIwh.js";import"./wind-DfhUE4Ns.js";import"./bug-DnLcBfSm.js";import"./chevron-left-B7itoz_r.js";import"./house-XoBBGZd5.js";function t(){return r.jsx(o,{})}export{t as default};
