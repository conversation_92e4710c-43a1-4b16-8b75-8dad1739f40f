import{b as t}from"./critical-DVX9Inzy.js";const e=t=>{let e;const n=new Set,s=(t,s)=>{const c="function"==typeof t?t(e):t;if(!Object.is(c,e)){const t=e;e=(null!=s?s:"object"!=typeof c||null===c)?c:Object.assign({},e,c),n.forEach((n=>n(e,t)))}},c=()=>e,a={setState:s,getState:c,getInitialState:()=>o,subscribe:t=>(n.add(t),()=>n.delete(t))},o=e=t(s,c,a);return a},n=t=>t,s=s=>{const c=(t=>t?e(t):e)(s),a=e=>function(e,s=n){const c=t.useSyncExternalStore(e.subscribe,(()=>s(e.getState())),(()=>s(e.getInitialState())));return t.useDebugValue(c),c}(c,e);return Object.assign(a,c),a},c=t=>t?s(t):s;export{c};
