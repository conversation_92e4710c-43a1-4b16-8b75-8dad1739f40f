import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{O as o}from"./index-CrSshpOb.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const i=()=>{const[i,a]=t.useState(!1),[n,s]=t.useState(null),[d,r]=t.useState({}),l=()=>{const e=document.getElementById("root"),t=window.getComputedStyle(document.body),i=window.getComputedStyle(document.documentElement),a={timestamp:(new Date).toISOString(),platform:o.getPlatform(),isNative:o.isNativePlatform(),userAgent:navigator.userAgent,viewport:{width:window.innerWidth,height:window.innerHeight},calculations:{statusBarHeight:30,navigationBarHeight:48,calculatedHeight:window.innerHeight-30-48,formula:`${window.innerHeight} - 30 - 48 = ${window.innerHeight-30-48}`},cssVariables:{top:document.documentElement.style.getPropertyValue("--safe-area-top")||getComputedStyle(document.documentElement).getPropertyValue("--safe-area-top"),bottom:document.documentElement.style.getPropertyValue("--safe-area-bottom")||getComputedStyle(document.documentElement).getPropertyValue("--safe-area-bottom"),height:document.documentElement.style.getPropertyValue("--safe-area-height")||getComputedStyle(document.documentElement).getPropertyValue("--safe-area-height")},bodyClasses:document.body.className,rootElement:e?{classes:e.className,computedHeight:window.getComputedStyle(e).height,computedPosition:window.getComputedStyle(e).position,computedTop:window.getComputedStyle(e).top}:null},n={currentTime:(new Date).toLocaleString(),screenInfo:{availHeight:screen.availHeight,availWidth:screen.availWidth,height:screen.height,width:screen.width,orientation:screen.orientation?.type||"unknown"},windowInfo:{innerHeight:window.innerHeight,innerWidth:window.innerWidth,outerHeight:window.outerHeight,outerWidth:window.outerWidth,devicePixelRatio:window.devicePixelRatio},documentInfo:{documentElementClientHeight:document.documentElement.clientHeight,documentElementScrollHeight:document.documentElement.scrollHeight,bodyClientHeight:document.body.clientHeight,bodyScrollHeight:document.body.scrollHeight},computedStyles:{body:{height:t.height,minHeight:t.minHeight,maxHeight:t.maxHeight,position:t.position,top:t.top,paddingTop:t.paddingTop,paddingBottom:t.paddingBottom},html:{height:i.height,minHeight:i.minHeight,maxHeight:i.maxHeight},root:e?{height:window.getComputedStyle(e).height,minHeight:window.getComputedStyle(e).minHeight,maxHeight:window.getComputedStyle(e).maxHeight,position:window.getComputedStyle(e).position,top:window.getComputedStyle(e).top,left:window.getComputedStyle(e).left,right:window.getComputedStyle(e).right,width:window.getComputedStyle(e).width,overflowY:window.getComputedStyle(e).overflowY,overflowX:window.getComputedStyle(e).overflowX}:null},safeAreaFromWindow:window.safeAreaDebugData||null};s(a),r(n)};return t.useEffect((()=>{l();const e=()=>{l()};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),i?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",style:{zIndex:9999},children:e.jsxs("div",{className:"bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold",children:"🐛 Safe Area Debugger"}),e.jsx("button",{onClick:()=>a(!1),className:"text-gray-500 hover:text-gray-700 text-xl",children:"✕"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:l,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:"🔄 Atualizar Dados"}),e.jsx("button",{onClick:()=>{const e={basicData:n,realTimeData:d,logs:"Verifique o console para logs detalhados"},t=JSON.stringify(e,null,2);navigator.clipboard.writeText(t).then((()=>{alert("Dados copiados para o clipboard!")})).catch((()=>{const e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),alert("Dados copiados para o clipboard!")}))},className:"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600",children:"📋 Copiar Dados"})]}),n&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-100 p-4 rounded",children:[e.jsx("h3",{className:"font-bold mb-2",children:"📱 Informações Básicas"}),e.jsx("pre",{className:"text-xs overflow-auto",children:JSON.stringify(n,null,2)})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded",children:[e.jsx("h3",{className:"font-bold mb-2",children:"📊 Dados em Tempo Real"}),e.jsx("pre",{className:"text-xs overflow-auto",children:JSON.stringify(d,null,2)})]}),e.jsxs("div",{className:"bg-yellow-50 p-4 rounded",children:[e.jsx("h3",{className:"font-bold mb-2",children:"🎯 Status das Safe Areas"}),e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Plataforma Nativa:"})," ",n.isNative?"✅ Sim":"❌ Não"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Classe Android:"})," ",n.bodyClasses?.includes("android-safe-area")?"✅ Aplicada":"❌ Não aplicada"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Variável --safe-area-top:"})," ",n.cssVariables?.top||"❌ Não definida"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Variável --safe-area-bottom:"})," ",n.cssVariables?.bottom||"❌ Não definida"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Variável --safe-area-height:"})," ",n.cssVariables?.height||"❌ Não definida"]})]})]})]})]})]})}):null};export{i as SafeAreaDebugger};
