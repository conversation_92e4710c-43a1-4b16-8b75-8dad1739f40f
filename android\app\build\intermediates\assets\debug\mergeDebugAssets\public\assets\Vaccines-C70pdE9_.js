import{j as e}from"./radix-core-6kBL75b5.js";import{r as a,b as s}from"./critical-DVX9Inzy.js";import{u as r}from"./query-vendor-B-7l6Nb3.js";import{R as t,m as i,ay as d,au as n,B as o,aL as l,aj as c,aa as m,a7 as x,s as h}from"./index-Dq2DDcRF.js";import p from"./Footer-DN7aP5VN.js";import{M as j}from"./MarkdownRenderer-BqxKrNWM.js";import{S as b}from"./syringe-BoQxY2VE.js";import{A as u}from"./index-BBVJGnSw.js";import{C as v}from"./calendar-DkU4Wz_i.js";import{L as g}from"./router-BAzpOxbo.js";import{C as f,a as y}from"./childcareSEOData-CjGCwidC.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-BWleNIUB.js";import"./rocket-CQ12FwVc.js";import"./target-CjP44kbC.js";import"./zap-BS-YR30Y.js";import"./book-open-CQJyFt3x.js";import"./star-VMrI2CfW.js";import"./circle-help-m-47zVGS.js";import"./instagram-BrswTqbB.js";import"./index-Bsdbnpgm.js";import"./markdown-vendor-C57yw7YK.js";import"./index-Bf1cTgQT.js";function N({dose:s}){const[r,m]=a.useState(!1);return e.jsx(t,{className:"overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 dark:from-slate-800 dark:via-slate-800/90 dark:to-slate-700/50",children:e.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"relative",children:[e.jsx("div",{className:"p-4 sm:p-6 pb-3 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-emerald-400 via-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg",children:e.jsx(b,{className:"w-7 h-7 text-white"})}),e.jsx("div",{className:"absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white text-xs font-bold",children:s.dose_number})})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-2",children:s.vaccine.name}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs(d,{className:`${(e=>{switch(e.toLowerCase()){case"sus":return"bg-gradient-to-r from-green-500 to-emerald-600 text-white";case"privada":return"bg-gradient-to-r from-blue-500 to-indigo-600 text-white";default:return"bg-gradient-to-r from-gray-500 to-gray-600 text-white"}})(s.type)} shadow-md`,children:[e.jsx(n,{className:"w-3 h-3 mr-1"}),s.type]}),e.jsx(d,{variant:"outline",className:"bg-white/80 dark:bg-slate-700/80 border-emerald-200 dark:border-emerald-700",children:(x=s.dose_number,h=s.dose_type,`${x}º ${"reforço"===h?"reforço":"dose"}`)})]})]})]}),e.jsx(o,{variant:"outline",size:"sm",onClick:()=>m(!r),className:"hover:bg-emerald-50 dark:hover:bg-slate-700 border-emerald-200 dark:border-emerald-700",children:r?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"h-4 w-4 mr-2"}),"Ocultar"]}):e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"h-4 w-4 mr-2"}),"Ver Detalhes"]})})]})}),e.jsx(u,{children:r&&e.jsx(i.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:e.jsxs("div",{className:"px-4 sm:px-6 pb-4 sm:pb-6 pt-2 border-t border-gray-100 dark:border-slate-600 bg-gradient-to-r from-gray-50/50 to-blue-50/50 dark:from-slate-700/50 dark:to-slate-600/50",children:[s.vaccine.description&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center",children:[e.jsx(v,{className:"w-4 h-4 mr-2"}),"Descrição"]}),e.jsx("div",{className:"bg-white/80 dark:bg-slate-800/80 rounded-lg p-3 border border-gray-200 dark:border-slate-600",children:e.jsx(j,{content:s.vaccine.description,className:"compact text-sm"})})]}),s.related_vaccines&&s.related_vaccines.length>0&&e.jsxs("div",{children:[e.jsxs("h4",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center",children:[e.jsx(n,{className:"w-4 h-4 mr-2"}),"Vacinas Incluídas (",s.related_vaccines.length,")"]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:s.related_vaccines.map((a=>e.jsx(i.div,{whileHover:{scale:1.02},className:"bg-white/90 dark:bg-slate-800/90 rounded-lg p-3 border border-gray-200 dark:border-slate-600",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-500 rounded-full"}),e.jsx("span",{className:"text-sm font-medium text-gray-800 dark:text-gray-200",children:a.name})]})},a.id)))})]})]})})})]})});var x,h}function w({age:a,doses:s,index:r}){return e.jsxs(i.div,{className:"mb-8 sm:mb-12 relative",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.15*r,duration:.6,ease:"easeOut"},children:[e.jsx("div",{className:"hidden sm:block absolute left-8 -translate-x-1/2 w-1 h-full bg-gradient-to-b from-emerald-200 via-emerald-300 to-emerald-400 dark:from-emerald-700 dark:via-emerald-600 dark:to-emerald-500 rounded-full opacity-30"}),e.jsxs("div",{className:"relative mb-6",children:[e.jsx("div",{className:"hidden sm:block absolute left-8 -translate-x-1/2 w-6 h-6 bg-gradient-to-br from-emerald-400 via-emerald-500 to-emerald-600 rounded-full shadow-lg border-4 border-white dark:border-slate-900 z-10"}),e.jsx("div",{className:"ml-0 sm:ml-20",children:e.jsxs(i.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.15*r+.2},className:"inline-flex items-center bg-gradient-to-r from-emerald-500 via-emerald-600 to-teal-600 text-white px-6 py-3 rounded-2xl font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105",children:[e.jsx(v,{className:"w-5 h-5 mr-3"}),e.jsx("span",{className:"text-lg",children:a}),e.jsx("div",{className:"ml-3 bg-white/20 rounded-full px-2 py-1",children:e.jsxs("span",{className:"text-sm font-medium",children:[s.length," vacina",1!==s.length?"s":""]})})]})})]}),e.jsx("div",{className:"ml-0 sm:ml-20 space-y-4 sm:space-y-6",children:s.map(((a,s)=>e.jsx(i.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.15*r+.1*s+.3,duration:.5,ease:"easeOut"},children:e.jsx(N,{dose:a})},a.id)))})]})}const k=e=>{if("0"===e||"ao nascer"===e.toLowerCase())return 0;const a=parseInt(e.replace(/[^0-9]/g,""));return isNaN(a)?999999:a},_=()=>{const a=y["calendario-vacinal"],{data:t=[],isLoading:d}=r({queryKey:["vaccine-doses"],queryFn:async()=>{const{data:e,error:a}=await h.from("pedbook_vaccine_doses").select("\n          id,\n          dose_number,\n          age_recommendation,\n          description,\n          type,\n          dose_type,\n          vaccine_id,\n          vaccine:pedbook_vaccines(\n            id,\n            name,\n            description\n          )\n        ").order("age_recommendation");if(a)throw a;const{data:s,error:r}=await h.from("pedbook_vaccine_relationships").select("\n          parent_vaccine_id,\n          child_vaccine:pedbook_vaccines!child_vaccine_id (\n            id,\n            name\n          )\n        ");if(r)throw r;const t={};return s.forEach((e=>{t[e.parent_vaccine_id]||(t[e.parent_vaccine_id]=[]),t[e.parent_vaccine_id].push(e.child_vaccine)})),e.map((e=>({...e,related_vaccines:t[e.vaccine_id]||[]}))).sort(((e,a)=>k(e.age_recommendation)-k(a.age_recommendation)))}}),n=s.useMemo((()=>{const e={};return t?t.reduce(((e,a)=>{const s=(e=>{if("0"===e||"ao nascer"===e.toLowerCase())return"Ao nascer";const a=parseInt(e.replace(/[^0-9]/g,""));if(isNaN(a))return e;if(a<12)return`${a} ${1===a?"mês":"meses"}`;{const e=Math.floor(a/12),s=a%12;return 0===s?`${e} ${1===e?"ano":"anos"}`:`${e} ${1===e?"ano":"anos"} e ${s} ${1===s?"mês":"meses"}`}})(a.age_recommendation);return e[s]||(e[s]=[]),e[s].push(a),e}),e):e}),[t]);return d?e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(m,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"animate-pulse text-gray-500 dark:text-gray-400",children:"Carregando vacinas..."})})}),e.jsx(p,{})]}):e.jsxs("div",{className:"min-h-screen flex flex-col dark:bg-slate-900",children:[e.jsx(f,{...a}),e.jsx(m,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-2 sm:px-4 py-8",children:[e.jsxs(g,{to:"/puericultura",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(x,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Puericultura"})]}),e.jsxs(i.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-12 text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Calendário Vacinal"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Acompanhe todas as vacinas recomendadas para cada fase"})]}),e.jsx("div",{className:"max-w-3xl mx-auto",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"hidden sm:block absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-emerald-400 to-emerald-600 dark:from-emerald-600 dark:to-emerald-800 rounded-full"}),Object.entries(n).map((([a,s],r)=>e.jsx(w,{age:a,doses:s,index:r},a)))]})})]}),e.jsx(p,{})]})};export{_ as default};
