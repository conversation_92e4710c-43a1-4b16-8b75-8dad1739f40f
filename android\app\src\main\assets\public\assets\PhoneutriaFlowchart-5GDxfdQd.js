import{j as e}from"./radix-core-6kBL75b5.js";import{L as a}from"./router-BAzpOxbo.js";import{R as r,aj as s,B as t,Y as o,ak as i,d,aa as n,a7 as l}from"./index-Dq2DDcRF.js";import c from"./Footer-DN7aP5VN.js";import{F as m,a as x}from"./flowchartSEOData-snZ6HOGG.js";import{c as g}from"./state-vendor-DwPaWbBF.js";import{A as u}from"./FeedbackTrigger-BWleNIUB.js";import{u as h}from"./useAge-C_36_Zbj.js";import{O as b}from"./octagon-alert-q9QH5JcX.js";import{R as p}from"./refresh-cw-D4Gt5rMG.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-BrswTqbB.js";import"./rocket-CQ12FwVc.js";import"./target-CjP44kbC.js";import"./zap-BS-YR30Y.js";import"./book-open-CQJyFt3x.js";import"./star-VMrI2CfW.js";import"./circle-help-m-47zVGS.js";const j=g((e=>({currentStage:"initial",setStage:a=>e({currentStage:a}),resetFlow:()=>e({currentStage:"initial"})}))),v=({onAnswer:a,stage:o})=>{if("initial"===o)return e.jsxs(r,{className:"p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800/90 dark:border-gray-700",children:[e.jsxs("h2",{className:"text-2xl font-bold mb-6 flex items-center gap-2 text-gray-800 dark:text-gray-100",children:[e.jsx(s,{className:"h-6 w-6 text-blue-500 dark:text-blue-400"}),"Início"]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-8",children:"Suspeita de acidente com aranha armadeira (Phoneutria sp.)"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-medium text-gray-700 dark:text-gray-200",children:"A aranha foi identificada?"}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs(t,{variant:"outline",onClick:()=>a("unidentifiedSpider"),className:"w-full flex justify-between items-center bg-[#D3E4FD] hover:bg-[#BED6F6] text-gray-700 border-2 border-[#BED6F6] dark:bg-blue-900/30 dark:hover:bg-blue-900/50 dark:border-blue-800/50 dark:text-gray-100",children:[e.jsx("span",{children:"Aranha NÃO identificada"}),e.jsx(u,{className:"h-4 w-4 ml-2 flex-shrink-0"})]}),e.jsxs(t,{variant:"outline",onClick:()=>a("identifiedSpider"),className:"w-full flex justify-between items-center bg-[#F2FCE2] hover:bg-[#E8F7D4] text-gray-700 border-2 border-[#E8F7D4] dark:bg-green-900/30 dark:hover:bg-green-900/50 dark:border-green-800/50 dark:text-gray-100",children:[e.jsx("span",{children:"Aranha identificada"}),e.jsx(u,{className:"h-4 w-4 ml-2 flex-shrink-0"})]})]})]})]});const i="unidentifiedSpider"===o?"Aranha NÃO Identificada":"Aranha Identificada",d="unidentifiedSpider"===o?"Paciente com suspeita de acidente por Phoneutria sp., mas sem confirmação visual do animal.":"Paciente com confirmação visual de acidente por Phoneutria sp.";return e.jsxs(r,{className:"p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800/90 dark:border-gray-700",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100",children:i}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-8",children:d}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-medium text-gray-700 dark:text-gray-200",children:"O paciente apresenta sinais/sintomas clínicos?"}),e.jsxs("div",{className:"grid gap-4",children:[e.jsxs(t,{onClick:()=>a("clinicalPictures"),className:"w-full justify-between bg-[#FEF7CD] hover:bg-[#FDF2B8] text-gray-700 border-2 border-[#FDF2B8] dark:bg-yellow-900/30 dark:hover:bg-yellow-900/50 dark:border-yellow-800/50 dark:text-gray-100",children:["Sim",e.jsx(u,{className:"ml-2 h-4 w-4"})]}),e.jsxs(t,{variant:"outline",onClick:()=>a("discharge"),className:"w-full justify-between bg-[#F1F0FB] hover:bg-gray-100 text-gray-700 border-2 border-gray-200 dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-gray-700 dark:text-gray-200",children:["Não",e.jsx(u,{className:"ml-2 h-4 w-4"})]})]})]})]})},y=({group:a,color:s,instructions:d,nextQuestion:n,onContinue:l})=>{h();const c=a.includes("Leve")?e.jsx(o,{className:"h-6 w-6 text-emerald-600"}):a.includes("Moderado")?e.jsx(i,{className:"h-6 w-6 text-amber-600"}):e.jsx(b,{className:"h-6 w-6 text-rose-600"});return e.jsx(r,{className:`p-8 max-w-2xl mx-auto ${s} shadow-md border-2`,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[c,e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:a})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("ul",{className:"list-disc pl-6 space-y-2",children:d.map(((a,r)=>e.jsx("li",{className:"text-gray-700",children:a},r)))}),n&&l&&e.jsxs("div",{className:"pt-4",children:[e.jsx("p",{className:"font-medium text-gray-700 mb-4",children:n}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(t,{variant:"outline",onClick:()=>l("under7"),className:"w-full justify-between bg-white hover:bg-gray-50 text-gray-700 border-2",children:"Menor que 7 anos"}),e.jsx(t,{variant:"outline",onClick:()=>l("over7"),className:"w-full justify-between bg-white hover:bg-gray-50 text-gray-700 border-2",children:"7 anos ou mais"})]})]})]})]})})},f=({onSelectPicture:a})=>{const s=[{title:"Quadro Leve",description:"Predomínio de manifestações locais: Dor, edema, eritema, irradiação, sudorese, parestesia, taquicardia e agitação secundárias à dor.",severity:"mild",color:"bg-[#F2FCE2]",borderColor:"border-[#E8F7D4]",icon:e.jsx(o,{className:"h-6 w-6 text-emerald-600"})},{title:"Quadro Moderado",description:"Manifestações locais associadas à sudorese, taquicardia, vômitos ocasionais, agitação, aumento da PA.",severity:"moderate",color:"bg-[#FEF7CD]",borderColor:"border-[#FDF2B8]",icon:e.jsx(i,{className:"h-6 w-6 text-amber-600"})},{title:"Quadro Grave",description:"Prostação, sudorese profusa, aumento da PA, priapismo, diarreia, diminuição da FC, arritmias cardíacas, convulsões, cianose, edema pulmonar, choque.",severity:"severe",color:"bg-[#FFDEE2]",borderColor:"border-[#FFD4D9]",icon:e.jsx(b,{className:"h-6 w-6 text-rose-600"})}];return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto p-4",children:s.map((s=>e.jsxs(r,{className:`p-6 transition-all duration-300 hover:scale-105 border-2 ${s.color} ${s.borderColor}`,children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.icon,e.jsx("h3",{className:"text-xl font-semibold text-gray-800",children:s.title})]}),e.jsx("p",{className:"text-gray-600 mb-6 min-h-[100px]",children:s.description}),e.jsx(t,{variant:"outline",className:"w-full justify-between border-2 hover:bg-white/50 text-gray-700",onClick:()=>a(s.severity),children:"Ver tratamento"})]},s.severity)))})},k=["Alívio da dor com anestesia local e/ou analgesia","Observação por 6-12 horas","Orientar retorno se piora"],N=["SAAR IV: 3 ampolas","Anestesia local e/ou analgesia"],w=["Alívio da dor com anestesia local e/ou analgesia","Observação por 6 horas"],F=["SAAR IV: 6 ampolas","Prover terapia de suporte","Cuidados intensivos","Anestesia local e/ou analgesia","Monitoramento contínuo"],A=()=>{const{currentStage:s,setStage:o,resetFlow:i}=j(),{toast:g}=d(),u=x.phoneutria,h=e=>{switch(e){case"mild":o("mildTreatment");break;case"moderate":o("moderateQuestion");break;case"severe":o("severeTreatment")}};return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-orange-50 via-white to-orange-50 dark:from-orange-900/20 dark:via-slate-900 dark:to-orange-900/10",children:[e.jsx(m,{...u}),e.jsx(n,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-5xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs(a,{to:"/flowcharts/venomous",className:"hidden sm:inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors",children:[e.jsx(l,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Animais Peçonhentos"})]}),e.jsxs(t,{onClick:i,variant:"outline",className:"flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 border-orange-200 dark:border-orange-700/50 hover:bg-orange-50 dark:hover:bg-orange-900/20",children:[e.jsx(p,{className:"h-4 w-4"}),e.jsx("span",{children:"Reiniciar"})]})]}),e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("h1",{className:"text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-orange-600 to-amber-600 dark:from-orange-400 dark:to-amber-400",children:"Acidente Fonêutrico (Aranha Armadeira)"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Fluxograma para manejo de acidentes com aranha armadeira em pediatria"})]}),(()=>{switch(s){case"initial":case"unidentifiedSpider":case"identifiedSpider":return e.jsx(v,{stage:s,onAnswer:e=>{"discharge"===e&&g({title:"Alta com orientações",description:"Orientar retorno imediato em caso de sintomas"}),o(e)}});case"clinicalPictures":return e.jsx(f,{onSelectPicture:h});case"mildTreatment":return e.jsx(y,{group:"Quadro Leve",color:"bg-[#F2FCE2] dark:bg-green-900/30",instructions:k});case"moderateQuestion":return e.jsx(y,{group:"Quadro Moderado",color:"bg-[#FEF7CD] dark:bg-yellow-900/30",instructions:[],nextQuestion:"Qual a idade do paciente?",onContinue:e=>{o("under7"===e?"moderateTreatmentUnder7":"moderateTreatmentOver7")}});case"moderateTreatmentUnder7":return e.jsx(y,{group:"Quadro Moderado",color:"bg-[#FEF7CD] dark:bg-yellow-900/30",instructions:N});case"moderateTreatmentOver7":return e.jsx(y,{group:"Quadro Moderado",color:"bg-[#FEF7CD] dark:bg-yellow-900/30",instructions:w});case"severeTreatment":return e.jsx(y,{group:"Quadro Grave",color:"bg-[#FFDEE2] dark:bg-red-900/30",instructions:F});case"discharge":return e.jsxs(r,{className:"p-8 max-w-2xl mx-auto bg-white dark:bg-slate-800 shadow-md border-2 border-gray-100 dark:border-gray-700",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100",children:"Alta do Paciente"}),e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-[#F2FCE2] dark:bg-green-900/30 p-6 rounded-lg border-2 border-[#E8F7D4] dark:border-green-800/50",children:e.jsx("p",{className:"text-gray-700 dark:text-gray-200",children:"Paciente estável. Orientar retorno imediato caso surjam novos sintomas."})})})]});default:return null}})(),e.jsxs("div",{className:"mt-8 p-6 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2",children:"Referência"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm",children:"BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024."})]})]})}),e.jsx(c,{})]})};export{A as default};
