import{j as e}from"./radix-core-6kBL75b5.js";import{u as s}from"./query-vendor-B-7l6Nb3.js";import{c as a,R as r,U as i,ak as t,B as n,a7 as c,b6 as d,W as l,Z as o,P as x,aj as m,au as h,s as j}from"./index-BGVWLj2Q.js";import{c as p,a as u}from"./router-BAzpOxbo.js";import{S as N}from"./scroll-text-CgDYC5zA.js";import{C as g}from"./clock-Ctd21uX9.js";import"./critical-DVX9Inzy.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=a("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),v=()=>{const{medicationId:a}=p(),v=u(),{data:w,isLoading:y,error:b}=s({queryKey:["medication-leaflet",a],queryFn:async()=>{if(!a)throw new Error("ID do medicamento não fornecido");const{data:e,error:s}=await j.from("pedbook_medication_leaflets").select("\n          *,\n          pedbook_medications!inner(name)\n        ").eq("medication_id",a).single();if(s)throw s;return{...e,medication_name:e.pedbook_medications.name}},enabled:!!a,staleTime:18e5,retry:1});return y?e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"})]})]})}):b||!w?e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsx(r,{children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx(t,{className:"h-12 w-12 text-amber-500 mx-auto mb-4"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Bula não encontrada"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:"Não foi possível encontrar a bula para este medicamento."}),e.jsxs(n,{onClick:()=>v(-1),children:[e.jsx(c,{className:"h-4 w-4 mr-2"}),"Voltar"]})]})})}):e.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[e.jsxs(d,{children:[e.jsxs("title",{children:["Bula - ",w.medication_name," | PedBook"]}),e.jsx("meta",{name:"description",content:`Bula completa do medicamento ${w.medication_name} - ${w.active_ingredient}`})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(n,{variant:"ghost",onClick:()=>v(-1),children:[e.jsx(c,{className:"h-4 w-4 mr-2"}),"Voltar"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-6 w-6 text-orange-500"}),e.jsx("h1",{className:"text-2xl font-bold",children:"Bula do Medicamento"})]})]}),e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-5 w-5 text-primary"}),w.title]})}),e.jsxs(i,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm text-muted-foreground",children:"Princípio Ativo"}),e.jsx("p",{children:w.active_ingredient})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm text-muted-foreground",children:"Classe Terapêutica"}),e.jsx("p",{children:w.therapeutic_class})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm text-muted-foreground",children:"Fabricante"}),e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-4 w-4"}),w.manufacturer]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm text-muted-foreground",children:"Registro ANVISA"}),e.jsx("p",{children:w.registration_number})]})]}),w.presentation&&e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm text-muted-foreground",children:"Apresentação"}),e.jsx("p",{children:w.presentation})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[w.indications&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs(o,{className:"flex items-center gap-2 text-green-600",children:[e.jsx(m,{className:"h-5 w-5"}),"Indicações"]})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.indications})})]}),w.contraindications&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs(o,{className:"flex items-center gap-2 text-red-600",children:[e.jsx(t,{className:"h-5 w-5"}),"Contraindicações"]})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.contraindications})})]}),w.posology&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs(o,{className:"flex items-center gap-2 text-blue-600",children:[e.jsx(g,{className:"h-5 w-5"}),"Posologia"]})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.posology})})]}),w.warnings&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs(o,{className:"flex items-center gap-2 text-amber-600",children:[e.jsx(h,{className:"h-5 w-5"}),"Advertências e Precauções"]})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.warnings})})]}),w.adverse_reactions&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(o,{className:"text-orange-600",children:"Reações Adversas"})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.adverse_reactions})})]}),w.interactions&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(o,{className:"text-purple-600",children:"Interações Medicamentosas"})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.interactions})})]}),w.overdose&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(o,{className:"text-red-600",children:"Superdosagem"})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.overdose})})]}),w.storage&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(o,{className:"text-gray-600",children:"Armazenamento"})}),e.jsx(i,{children:e.jsx("p",{className:"whitespace-pre-wrap",children:w.storage})})]})]}),(w.pdf_url||w.anvisa_url)&&e.jsxs(r,{children:[e.jsx(l,{children:e.jsx(o,{children:"Links Externos"})}),e.jsxs(i,{className:"space-y-2",children:[w.pdf_url&&e.jsx(n,{variant:"outline",asChild:!0,children:e.jsx("a",{href:w.pdf_url,target:"_blank",rel:"noopener noreferrer",children:"Ver PDF Original"})}),w.anvisa_url&&e.jsx(n,{variant:"outline",asChild:!0,children:e.jsx("a",{href:w.anvisa_url,target:"_blank",rel:"noopener noreferrer",children:"Ver na ANVISA"})})]})]}),e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["Última atualização: ",new Date(w.last_updated).toLocaleDateString("pt-BR")]})]})};export{v as default};
