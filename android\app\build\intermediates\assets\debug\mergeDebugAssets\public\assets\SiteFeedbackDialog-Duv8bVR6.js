import{j as e}from"./radix-core-6kBL75b5.js";import{d as a,s as t,T as r,a5 as s,D as o,e as i,f as n,g as d,z as l,B as c}from"./index-CrSshpOb.js";import{r as m}from"./critical-DVX9Inzy.js";import{S as p}from"./star-DsgxKBIV.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const h=({rating:a,setRating:t,comment:o,setComment:i,whatsapp:n,setWhatsapp:d})=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-semibold tracking-tight dark:text-gray-100",children:"Sua opinião é importante!"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Ajude-nos a melhorar sua experiência compartilhando seu feedback."})]}),e.jsx("div",{className:"flex justify-center gap-2 sm:gap-3 py-2",children:[1,2,3,4,5].map((r=>e.jsx("button",{onClick:()=>t(r),className:"transition-all duration-200 hover:scale-110 p-1.5 "+(a>=r?"text-yellow-400 dark:text-yellow-300":"text-gray-300 dark:text-gray-600"),type:"button","aria-label":`Avaliação ${r} estrelas`,children:e.jsx(p,{className:"w-7 h-7 sm:w-8 sm:h-8",fill:a>=r?"currentColor":"none",strokeWidth:1.5})},r)))}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium dark:text-gray-300",children:a<=4?e.jsx("span",{className:"text-red-500 dark:text-red-400",children:"Por favor, nos ajude a entender o que podemos melhorar *"}):"Suas sugestões para melhorarmos o PedBook"}),e.jsx(r,{value:o,onChange:e=>i(e.target.value),placeholder:a<=4?"Conte-nos o que não atendeu suas expectativas para que possamos melhorar...":"Compartilhe suas ideias e sugestões para nos ajudar a melhorar cada vez mais!",className:"min-h-[120px] resize-none rounded-xl dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200",required:a<=4})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium dark:text-gray-300",children:a<=4?e.jsx("span",{className:"text-red-500 dark:text-red-400",children:"WhatsApp para contato *"}):"WhatsApp para contato (opcional)"}),e.jsx(s,{type:"tel",placeholder:"Digite seu WhatsApp",value:n,onChange:e=>d(e.target.value),className:"bg-white dark:bg-slate-700 rounded-xl dark:border-slate-600 dark:text-gray-200",required:a<=4})]})]});function x(){const{open:r,setOpen:s,rating:p,setRating:x,comment:u,setComment:g,whatsapp:b,setWhatsapp:v,isSubmitting:f,handleSubmit:j,handleClose:k}=(()=>{const[e,r]=m.useState(!1),[s,o]=m.useState(5),[i,n]=m.useState(""),[d,l]=m.useState(""),[c,p]=m.useState(!1),{toast:h}=a();return m.useEffect((()=>{localStorage.removeItem("feedback_submitted"),localStorage.removeItem("feedback_postponed"),localStorage.removeItem("visitor_feedback_id")}),[]),{open:e,setOpen:r,rating:s,setRating:o,comment:i,setComment:n,whatsapp:d,setWhatsapp:l,isSubmitting:c,handleSubmit:async()=>{if(0===s)return void h({title:"Avaliação necessária",description:"Por favor, selecione uma classificação antes de enviar.",variant:"destructive"});if(s<=4&&!d.trim())return void h({title:"WhatsApp necessário",description:"Por favor, forneça seu WhatsApp para que possamos entender melhor como melhorar.",variant:"destructive"});p(!0);const e=localStorage.getItem("visitor_feedback_id");try{const{error:a}=await t.from("site_visitor_feedbacks").insert([{rating:s,comment:i,whatsapp:d||null,visitor_id:e}]);if(a)throw a;localStorage.setItem("feedback_submitted","true"),h({title:"Feedback enviado!",description:"Obrigado por compartilhar sua opinião conosco."}),r(!1),o(5),n(""),l("")}catch(a){h({title:"Erro ao enviar feedback",description:"Por favor, tente novamente mais tarde.",variant:"destructive"})}finally{p(!1)}},handleClose:()=>{localStorage.setItem("feedback_postponed",Date.now().toString()),r(!1),o(5),n(""),l("")}}})();return e.jsx(o,{open:r,onOpenChange:e=>{e?s(e):k()},children:e.jsxs(i,{className:"fixed left-[50%] -translate-x-1/2 bg-white dark:bg-slate-800 p-0 overflow-hidden flex flex-col rounded-2xl shadow-xl border dark:border-slate-700",hideCloseButton:!1,style:{top:window.innerWidth<=640?"20%":"50%",transform:window.innerWidth<=640?"translate(-50%, -20%)":"translate(-50%, -50%)",maxHeight:window.innerWidth<=640?"75dvh":"80vh",width:window.innerWidth<=640?window.innerHeight>window.innerWidth?"98%":"75%":"calc(100%-2rem)",maxWidth:window.innerWidth<=640?window.innerHeight>window.innerWidth?"520px":"350px":"500px"},children:[e.jsxs(n,{className:"sr-only",children:[e.jsx(d,{children:"Feedback do Site"}),e.jsx(l,{children:"Compartilhe sua opinião sobre o site para nos ajudar a melhorar"})]}),e.jsx("div",{className:"overflow-y-auto flex-1 p-5 sm:p-6",children:e.jsx(h,{rating:p,setRating:x,comment:u,setComment:g,whatsapp:b,setWhatsapp:v})}),e.jsxs("div",{className:"flex gap-2 sm:gap-3 justify-end p-4 border-t border-gray-200 dark:border-slate-700 bg-gray-50/80 dark:bg-slate-800/90 backdrop-blur supports-[backdrop-filter]:bg-gray-50/60 dark:supports-[backdrop-filter]:bg-slate-800/80",children:[e.jsx(c,{variant:"outline",onClick:k,className:"hover:bg-gray-100 dark:hover:bg-slate-700 dark:bg-slate-800 text-sm sm:text-base rounded-xl dark:text-gray-300 dark:border-slate-600",children:"Deixar para depois"}),e.jsx(c,{onClick:j,disabled:f||p<=4&&(!u.trim()||!b.trim()),className:"bg-primary hover:bg-primary/90 text-sm sm:text-base rounded-xl text-white",children:f?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"h-4 w-4 block rounded-full border-2 border-white border-t-transparent animate-spin"}),e.jsx("span",{children:"Enviando..."})]}):"Enviar feedback"})]})]})})}export{x as SiteFeedbackDialog};
