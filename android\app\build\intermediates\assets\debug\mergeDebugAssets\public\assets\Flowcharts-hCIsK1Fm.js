import{j as e}from"./radix-core-6kBL75b5.js";import{L as a}from"./router-BAzpOxbo.js";import{aM as r,a8 as o,aa as i,B as s,a7 as t,j as l}from"./index-CrSshpOb.js";import d from"./Footer-ClHMSbsi.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";const n=()=>{const n=e=>{switch(e){case"blue":return"border-blue-500 dark:border-blue-600";case"orange":return"border-orange-500 dark:border-orange-600";case"purple":return"border-purple-500 dark:border-purple-600";case"red":return"border-red-500 dark:border-red-600";case"yellow":return"border-yellow-500 dark:border-yellow-600";case"indigo":return"border-indigo-500 dark:border-indigo-600";case"green":return"border-green-500 dark:border-green-600";default:return"border-gray-300 dark:border-gray-600"}},c=e=>{switch(e){case"blue":return"bg-blue-50 dark:bg-blue-900/30";case"orange":return"bg-orange-50 dark:bg-orange-900/30";case"purple":return"bg-purple-50 dark:bg-purple-900/30";case"red":return"bg-red-50 dark:bg-red-900/30";case"yellow":return"bg-yellow-50 dark:bg-yellow-900/30";case"indigo":return"bg-indigo-50 dark:bg-indigo-900/30";case"green":return"bg-green-50 dark:bg-green-900/30";default:return"bg-gray-50 dark:bg-gray-800/30"}};return e.jsxs("div",{className:r.gradientBackground("min-h-screen flex flex-col from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800"),children:[e.jsxs(o,{children:[e.jsx("title",{children:"PedBook | Fluxogramas"}),e.jsx("meta",{name:"description",content:"Fluxogramas interativos para manejo de urgências e emergências pediátricas"})]}),e.jsx(i,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-5xl mx-auto space-y-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(a,{to:"/",children:e.jsx(s,{variant:"ghost",size:"icon",className:"hidden sm:inline-flex hover:bg-primary/10 dark:hover:bg-primary/20",children:e.jsx(t,{className:"h-5 w-5 text-primary dark:text-blue-400"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:r.gradientHeading("text-3xl text-center"),children:"Fluxogramas de Urgência"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-center max-w-2xl mx-auto",children:"Guias práticos e interativos para manejo de urgências e emergências pediátricas"})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:gap-4",children:[{id:"hydration",title:"Hidratação Venosa",description:"Fluxograma para cálculo da hidratação venosa de manutenção (Holliday-Segar)",path:"/flowcharts/hidratacao",colorName:"blue",icon:"💧"},{id:"dengue",title:"Dengue",description:"Fluxograma para manejo de casos suspeitos de dengue",path:"/flowcharts/dengue",colorName:"orange",icon:"🦟"},{id:"dka",title:"Cetoacidose Diabética",description:"Fluxograma para manejo de cetoacidose diabética em pediatria",path:"/flowcharts/dka",colorName:"purple",icon:"💉"},{id:"anaphylaxis",title:"Anafilaxia",description:"Fluxograma para manejo de anafilaxia em pediatria",path:"/flowcharts/anaphylaxis",colorName:"red",icon:"💊"},{id:"asthma",title:"Crise Asmática",description:"Fluxograma para manejo de crise asmática em pediatria",path:"/flowcharts/asthma",colorName:"blue",icon:"💨"},{id:"seizure",title:"Crise Convulsiva",description:"Fluxograma para manejo de crise convulsiva em pediatria",path:"/flowcharts/seizure",colorName:"yellow",icon:"🧠"},{id:"pecarn",title:"PECARN - Trauma Craniano",description:"Fluxograma para avaliação de trauma craniano em pediatria",path:"/flowcharts/pecarn",colorName:"indigo",icon:"🤕"},{id:"venomous",title:"Animais Peçonhentos",description:"Fluxograma para manejo de acidentes com animais peçonhentos",path:"/flowcharts/venomous",colorName:"green",icon:"🐍"}].map(((r,o)=>e.jsx(a,{to:r.path,className:"block group transform transition-all duration-500 hover:scale-[1.02]",style:{animationDelay:100*o+"ms",animation:"fade-in-up 0.5s ease-out forwards",opacity:0},children:e.jsxs("div",{className:l("relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 hover:shadow-lg hover:-translate-y-1","bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md","border border-gray-100 dark:border-gray-700/50"),children:[e.jsx("div",{className:l("absolute top-0 left-0 right-0 h-1.5 rounded-t-xl","blue"===r.colorName?"bg-blue-500":"orange"===r.colorName?"bg-orange-500":"purple"===r.colorName?"bg-purple-500":"red"===r.colorName?"bg-red-500":"yellow"===r.colorName?"bg-yellow-500":"indigo"===r.colorName?"bg-indigo-500":"green"===r.colorName?"bg-green-500":"bg-primary")}),e.jsxs("div",{className:"flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:l("w-10 h-10 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center mb-2 sm:mb-3 shadow-sm transition-transform group-hover:scale-110","border-2",n(r.colorName),c(r.colorName)),children:e.jsx("span",{className:"text-xl sm:text-2xl",children:r.icon})}),e.jsxs("div",{className:"space-y-2 flex-1",children:[e.jsx("h3",{className:"text-sm sm:text-base font-semibold text-gray-800 dark:text-gray-100 line-clamp-2",children:r.title}),e.jsx("p",{className:"text-[10px] sm:text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2",children:r.description})]})]})]})},r.id)))})]})}),e.jsx(d,{})]})};export{n as default};
