import{j as e}from"./radix-core-6kBL75b5.js";import{j as a,ay as r,aa as t,a7 as i,aW as s,aJ as l,k as o}from"./index-DwBJcqzE.js";import n from"./Footer-DXia2SVU.js";import{L as d}from"./router-BAzpOxbo.js";import{C as c,a as m}from"./childcareSEOData-BGDJGA8f.js";import{C as p}from"./chart-line-uzYAfoO7.js";import{S as u}from"./syringe-BX3eTIwh.js";import{P as x}from"./pill-bottle-eRK7Hfkf.js";import"./critical-DVX9Inzy.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-BC_TYgH3.js";import"./rocket-7eidEF9E.js";import"./target-D2iN3abh.js";import"./zap-BpuRFf_b.js";import"./book-open-C1lgvyyJ.js";import"./star-BwJL-OtG.js";import"./circle-help-OcogOqeH.js";import"./instagram-Dc9Ip0W1.js";const b=({title:t,description:i,icon:s,color:l,path:o,badge:n})=>(l.includes("-")&&l.split("-")[1],e.jsx(d,{to:o,className:"block h-full",children:e.jsxs("div",{className:a("relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer","bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg","border border-gray-100 dark:border-gray-700/50","hover:-translate-y-1"),children:[e.jsx("div",{className:a("absolute top-0 left-0 right-0 h-1.5 rounded-t-xl",l.includes("yellow")?"bg-yellow-500":l.includes("purple")?"bg-purple-500":l.includes("blue")?"bg-blue-500":l.includes("pink")?"bg-pink-500":l.includes("green")?"bg-green-500":l.includes("amber")?"bg-amber-500":l.includes("red")?"bg-red-500":l.includes("cyan")?"bg-cyan-500":l.includes("indigo")?"bg-indigo-500":l.includes("rose")?"bg-rose-500":"bg-primary")}),e.jsxs("div",{className:"flex flex-col items-center text-center h-full justify-between pt-2",children:[e.jsx("div",{className:a("w-10 h-10 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mb-2 sm:mb-3 shadow-sm",l.includes("yellow")?"bg-yellow-50 dark:bg-yellow-900/30":l.includes("purple")?"bg-purple-50 dark:bg-purple-900/30":l.includes("blue")?"bg-blue-50 dark:bg-blue-900/30":l.includes("pink")?"bg-pink-50 dark:bg-pink-900/30":l.includes("green")?"bg-green-50 dark:bg-green-900/30":l.includes("amber")?"bg-amber-50 dark:bg-amber-900/30":l.includes("red")?"bg-red-50 dark:bg-red-900/30":l.includes("cyan")?"bg-cyan-50 dark:bg-cyan-900/30":l.includes("indigo")?"bg-indigo-50 dark:bg-indigo-900/30":l.includes("rose")?"bg-rose-50 dark:bg-rose-900/30":"bg-gray-50 dark:bg-slate-800"),children:e.jsx(s,{className:a("w-5 h-5 sm:w-7 sm:h-7",l.includes("yellow")?"text-yellow-700 dark:text-yellow-300":l.includes("purple")?"text-purple-700 dark:text-purple-300":l.includes("blue")?"text-blue-700 dark:text-blue-300":l.includes("pink")?"text-pink-700 dark:text-pink-300":l.includes("green")?"text-green-700 dark:text-green-300":l.includes("amber")?"text-amber-700 dark:text-amber-300":l.includes("red")?"text-red-700 dark:text-red-300":l.includes("cyan")?"text-cyan-700 dark:text-cyan-300":l.includes("indigo")?"text-indigo-700 dark:text-indigo-300":l.includes("rose")?"text-rose-700 dark:text-rose-300":"text-primary dark:text-blue-400")})}),e.jsx("h3",{className:"font-bold text-sm sm:text-base text-gray-800 dark:text-gray-200 line-clamp-2",children:t}),i&&e.jsx("p",{className:"text-[10px] sm:text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2",children:i}),n&&e.jsx("div",{className:"mt-2",children:e.jsx(r,{variant:"outline",className:"bg-gray-100/80 dark:bg-slate-700/80 border-none text-gray-700 dark:text-gray-300 text-[10px] font-medium px-2 py-0.5 h-auto",children:n})})]})]})})),g=()=>{const a=m.main,r=[{title:"Visão Geral do Paciente",description:"Análise completa e personalizada do desenvolvimento infantil com recomendações específicas para cada paciente.",icon:s,color:"bg-blue",path:"/puericultura/patient-overview",badge:"Mais utilizado",isHighlighted:!0},{title:"Curvas de Crescimento",description:"Veja as principais curvas de crescimento.",icon:p,color:"bg-yellow",path:"/puericultura/curva-de-crescimento"},{title:"Vacinas",description:"Calendário vacinal completo e atualizado.",icon:u,color:"bg-pink",path:"/puericultura/calendario-vacinal"},{title:"Marcos DNPM",description:"Veja os os marcos do desenvolvimento neuropsicomotor.",icon:l,color:"bg-blue",path:"/dnpm"},{title:"Fórmulas Infantis",description:"Guia sobre fórmulas e suplementação infantil.",icon:o,color:"bg-purple",path:"/puericultura/formulas"},{title:"Suplementação",description:"Encontre orientações automáticas para suplementação de vitaminas conforme as necessidades da criança.",icon:x,color:"bg-green",path:"/puericultura/suplementacao-infantil",badge:"Automático"}];return e.jsxs("div",{className:"min-h-screen flex flex-col dark:bg-slate-900",children:[e.jsx(c,{...a}),e.jsx(t,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-12",children:[e.jsxs(d,{to:"/",className:"hidden sm:inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(i,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para o Menu Inicial"})]}),e.jsxs("div",{className:"text-center space-y-4 mb-12",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold dark:text-white",children:"Puericultura"}),e.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto dark:text-gray-300",children:"Ferramentas essenciais para acompanhamento do crescimento e desenvolvimento infantil"})]}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:gap-4 max-w-7xl mx-auto",children:r.map(((a,r)=>e.jsx(b,{...a},r)))})]}),e.jsx(n,{})]})};export{g as default};
