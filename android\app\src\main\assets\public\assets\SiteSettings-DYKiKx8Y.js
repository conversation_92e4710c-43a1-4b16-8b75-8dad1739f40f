import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{u as s}from"./query-vendor-B-7l6Nb3.js";import{c as i,d as t,R as o,W as r,Z as l,U as c,B as n,a5 as m,s as d}from"./index-CFFY2EZF.js";import{c as x}from"./imageOptimization-Di_XtiVZ.js";import{Z as p}from"./zap-DiDsx3f0.js";import{R as h}from"./refresh-cw-Oq-z6v3G.js";import{U as u}from"./upload-CLfvGhlZ.js";import{D as g}from"./download-CpeVlzju.js";import{S as j}from"./separator-CmN5pc9-.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=i("FileImage",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]),f=async e=>{const a=e.size,s=await x(e,{maxWidth:48,maxHeight:48,quality:.9,format:"webp",maintainAspectRatio:!0}),i=s.size;return{originalSize:a,optimizedSize:i,compressionRatio:Math.round((a-i)/a*100),optimizedBlob:s}},b=async e=>{const a=e.size,s=await x(e,{maxWidth:32,maxHeight:32,quality:.95,format:"webp",maintainAspectRatio:!0}),i=s.size;return{originalSize:a,optimizedSize:i,compressionRatio:Math.round((a-i)/a*100),optimizedBlob:s}},y=async e=>{const a=e.size,s=await x(e,{maxWidth:200,maxHeight:200,quality:.85,format:"webp",maintainAspectRatio:!0}),i=s.size;return{originalSize:a,optimizedSize:i,compressionRatio:Math.round((a-i)/a*100),optimizedBlob:s}},N=()=>{const[s,i]=a.useState(!1),[x,j]=a.useState(null),{toast:N}=t(),z=(e,a)=>{((e,a)=>{const s=URL.createObjectURL(e),i=document.createElement("a");i.href=s,i.download=a,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(s)})(e,a)},w=e=>(e/1024).toFixed(2)+" KB",k=async()=>{try{const{error:e}=await d.from("pedbook_site_settings").update({value:""}).eq("key","logo_url");if(e)throw e;N({title:"Logo local ativado!",description:"O site agora está usando o logo otimizado local (/faviconx.webp)."}),setTimeout((()=>{window.location.reload()}),1e3)}catch(e){N({variant:"destructive",title:"Erro",description:"Não foi possível ativar o logo local."})}};return e.jsx("div",{className:"space-y-6",children:e.jsxs(o,{children:[e.jsx(r,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5"}),"Otimizador de Logo"]})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg mb-4",children:[e.jsx("h4",{className:"font-semibold text-green-800 dark:text-green-200 mb-2",children:"🚀 Ação Rápida: Ativar Logo Otimizado"}),e.jsx("p",{className:"text-sm text-green-700 dark:text-green-300 mb-3",children:"O logo local já foi otimizado (7,6KB - 200x200px). Clique para ativá-lo imediatamente:"}),e.jsxs(n,{onClick:k,className:"w-full",variant:"default",children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Usar Logo Local Otimizado (7,6KB)"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(m,{type:"file",accept:"image/*",onChange:async e=>{const a=e.target.files?.[0];if(a)if(a.type.startsWith("image/")){i(!0);try{const e=await(async e=>{const a={favicon:await b(e),header:await f(e),general:await y(e)},s=3*e.size,i=s-(a.favicon.optimizedSize+a.header.optimizedSize+a.general.optimizedSize),t=Math.round(i/s*100);return{...a,totalSavings:i,totalCompressionRatio:t,summary:{originalSize:e.size,totalSavings:i,compressionRatio:t}}})(a);j(e),N({title:"Otimização concluída!",description:`Economia total: ${e.summary.compressionRatio}% (${(e.summary.totalSavings/1024).toFixed(2)} KB)`})}catch(s){N({variant:"destructive",title:"Erro na otimização",description:"Não foi possível otimizar a imagem."})}finally{i(!1)}}else N({variant:"destructive",title:"Arquivo inválido",description:"Por favor, selecione uma imagem."})},disabled:s,className:"flex-1"}),e.jsxs(n,{variant:"outline",disabled:s,className:"flex items-center gap-2",children:[e.jsx(u,{className:"h-4 w-4"}),s?"Otimizando...":"Upload"]})]}),s&&e.jsxs("div",{className:"text-center py-4",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Otimizando imagem..."})]}),x&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:[e.jsx("h3",{className:"font-semibold text-green-800 dark:text-green-200 mb-2",children:"Resumo da Otimização"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Tamanho Original:"}),e.jsx("span",{className:"ml-2 font-medium",children:w(x.summary.originalSize)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Economia Total:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-600",children:[w(x.summary.totalSavings)," (",x.summary.compressionRatio,"%)"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(o,{children:[e.jsx(r,{className:"pb-2",children:e.jsx(l,{className:"text-sm",children:"Favicon (32x32)"})}),e.jsxs(c,{className:"space-y-2",children:[e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e.jsxs("div",{children:["Tamanho: ",w(x.favicon.optimizedSize)]}),e.jsxs("div",{children:["Economia: ",x.favicon.compressionRatio,"%"]})]}),e.jsxs(n,{size:"sm",variant:"outline",onClick:()=>z(x.favicon.optimizedBlob,"favicon-32x32.webp"),className:"w-full",children:[e.jsx(g,{className:"h-3 w-3 mr-1"}),"Download"]})]})]}),e.jsxs(o,{children:[e.jsx(r,{className:"pb-2",children:e.jsx(l,{className:"text-sm",children:"Header (48x48)"})}),e.jsxs(c,{className:"space-y-2",children:[e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e.jsxs("div",{children:["Tamanho: ",w(x.header.optimizedSize)]}),e.jsxs("div",{children:["Economia: ",x.header.compressionRatio,"%"]})]}),e.jsxs(n,{size:"sm",variant:"outline",onClick:()=>z(x.header.optimizedBlob,"logo-48x48.webp"),className:"w-full",children:[e.jsx(g,{className:"h-3 w-3 mr-1"}),"Download"]})]})]}),e.jsxs(o,{children:[e.jsx(r,{className:"pb-2",children:e.jsx(l,{className:"text-sm",children:"Geral (200x200)"})}),e.jsxs(c,{className:"space-y-2",children:[e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e.jsxs("div",{children:["Tamanho: ",w(x.general.optimizedSize)]}),e.jsxs("div",{children:["Economia: ",x.general.compressionRatio,"%"]})]}),e.jsxs(n,{size:"sm",variant:"outline",onClick:()=>z(x.general.optimizedBlob,"logo-optimized.webp"),className:"w-full",children:[e.jsx(g,{className:"h-3 w-3 mr-1"}),"Download"]})]})]})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[e.jsxs("h4",{className:"font-semibold text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2",children:[e.jsx(v,{className:"h-4 w-4"}),"Próximos Passos"]}),e.jsxs("ol",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside mb-4",children:[e.jsxs("li",{children:['Baixe a versão "Header (48x48)" e substitua o arquivo ',e.jsx("code",{children:"public/faviconx.webp"})]}),e.jsx("li",{children:'Baixe a versão "Favicon (32x32)" para usar como favicon'}),e.jsx("li",{children:"Clique no botão abaixo para ativar o logo local"}),e.jsx("li",{children:"Execute um novo teste no PageSpeed Insights"})]}),e.jsxs(n,{onClick:k,className:"w-full",variant:"default",children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Usar Logo Local Otimizado"]})]})]})]})]})})},z=()=>{const[i,o]=a.useState(""),[r,l]=a.useState(""),{toast:c}=t(),{data:x,refetch:p}=s({queryKey:["site-settings"],queryFn:async()=>{const{data:e,error:a}=await d.from("pedbook_site_settings").select("*");if(a)throw a;const s=e.reduce(((e,a)=>(e[a.key]=a.value,e)),{});return o(s.logo_url||""),l(s.favicon_url||""),s}});return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Configurações do Site"}),e.jsx("div",{className:"mb-8",children:e.jsx(N,{})}),e.jsx(j,{className:"my-8"}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{const{error:e}=await d.from("pedbook_site_settings").update({value:i}).eq("key","logo_url");if(e)throw e;const{error:a}=await d.from("pedbook_site_settings").update({value:r}).eq("key","favicon_url");if(a)throw a;c({title:"Configurações atualizadas",description:"As configurações do site foram atualizadas com sucesso."});const s=document.querySelector("link[rel='icon']");s&&(s.href=r),p()}catch(a){c({variant:"destructive",title:"Erro ao atualizar configurações",description:a.message})}},className:"space-y-8 max-w-2xl",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"logo",className:"block text-lg font-medium mb-2",children:"URL do Logo"}),e.jsx(m,{id:"logo",value:i,onChange:e=>o(e.target.value),className:"w-full"}),i&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Preview:"}),e.jsx("img",{src:i,alt:"Logo Preview",className:"h-8 w-auto"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"favicon",className:"block text-lg font-medium mb-2",children:"URL do Favicon"}),e.jsx(m,{id:"favicon",value:r,onChange:e=>l(e.target.value),className:"w-full"}),r&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Preview:"}),e.jsx("img",{src:r,alt:"Favicon Preview",className:"h-8 w-auto"})]})]})]}),e.jsx(n,{type:"submit",className:"w-full",children:"Salvar Configurações"})]})]})};export{z as default};
