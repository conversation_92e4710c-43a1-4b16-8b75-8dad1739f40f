import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,d as r,B as i,ab as t,s as n,R as o,T as l,aH as c,ao as m,an as d,a5 as p,ac as u,ad as x,ae as h,af as g,ag as b,ai as y,ay as f,aw as j,b0 as v,L as N,D as w,e as D,f as S,g as C,z as k,aC as P,aa as A}from"./index-DwykrzWu.js";import F from"./Footer-CEErUVD6.js";import{C as I}from"./copy-ftty0-1L.js";import{S as T}from"./slider-DJZ9XRYr.js";import{a as E}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-Cce_pfRl.js";import"./rocket-BVKdk9NC.js";import"./target-Bq7BybE4.js";import"./zap-sktuObTW.js";import"./book-open-vsXyzIQN.js";import"./star-CVUfjIpQ.js";import"./circle-help-DkV0sebI.js";import"./instagram-BgC8q_0n.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=s("FileSearch",[["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M4.268 21a2 2 0 0 0 1.727 1H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3",key:"ms7g94"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}],["circle",{cx:"5",cy:"14",r:"3",key:"ufru5t"}]]);function R({selectedDiagnosis:a,patientData:s,onPrescriptionGenerated:o,onDetailedAnalysis:l,setIsLoadingPrescription:c,setIsLoadingAnalysis:m}){const{toast:d}=r();return e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 mt-6",children:[e.jsxs(i,{onClick:async()=>{if(a){c(!0);try{const{data:e,error:r}=await n.functions.invoke("generate-prescription",{body:{diagnosis:a,patientData:s}});if(r)throw r;o(e),d({title:"Prescrição gerada com sucesso!",description:"A prescrição foi gerada baseada no diagnóstico selecionado."})}catch(e){d({title:"Erro ao gerar prescrição",description:"Ocorreu um erro ao tentar gerar a prescrição. Tente novamente.",variant:"destructive"}),c(!1)}}else d({title:"Selecione um diagnóstico",description:"Por favor, selecione um diagnóstico para gerar a prescrição.",variant:"destructive"})},className:"flex-1 gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70",children:[e.jsx(t,{className:"h-4 w-4"}),"Gerar Prescrição"]}),e.jsxs(i,{onClick:async()=>{if(a){m(!0);try{const{data:e,error:r}=await n.functions.invoke("generate-detailed-analysis",{body:{diagnosis:a,patientData:s}});if(r)throw r;l(e.analysis),d({title:"Análise detalhada gerada!",description:"A análise detalhada do caso foi gerada com sucesso."})}catch(e){d({title:"Erro ao gerar análise",description:"Ocorreu um erro ao tentar gerar a análise detalhada. Tente novamente.",variant:"destructive"}),m(!1)}}else d({title:"Selecione um diagnóstico",description:"Por favor, selecione um diagnóstico para gerar a análise detalhada.",variant:"destructive"})},variant:"outline",className:"flex-1 gap-2 border-primary/20 hover:bg-primary/5",children:[e.jsx(q,{className:"h-4 w-4"}),"Análise Detalhada"]})]})}function z({formData:a}){return e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text",children:"Resumo da Anamnese"}),e.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Idade:"})," ",a.age," anos"]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Peso:"})," ",a.weight," kg"]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Gênero:"})," ","male"===a.gender?"Masculino":"Feminino"]}),"female"===a.gender&&void 0!==a.isPregnant&&e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Gestante:"})," ",a.isPregnant?"Sim":"Não"]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Paciente Pediátrico:"})," ",a.isPediatric?"Sim":"Não"]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Sintomas:"})," ",a.symptoms.join(", ")]}),a.manualSymptoms&&e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Sintomas adicionais:"})," ",a.manualSymptoms]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Intensidade:"})," ",a.symptomsIntensity,"/10"]}),a.hasChronicDiseases&&a.chronicDiseases&&e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Doenças crônicas:"})," ",a.chronicDiseases]}),a.hasRecentExams&&a.examDetails&&e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Exames recentes:"})," ",a.examDetails]})]})]})}function M({prescription:a,editablePrescription:s,onPrescriptionChange:t}){const{toast:n}=r();return e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text",children:"Prescrição Médica"}),e.jsxs(i,{variant:"outline",size:"sm",className:"flex items-center gap-2",onClick:async()=>{try{await navigator.clipboard.writeText(s),n({title:"Copiado!",description:"Prescrição copiada para a área de transferência"})}catch(e){n({title:"Erro ao copiar",description:"Não foi possível copiar a prescrição",variant:"destructive"})}},children:[e.jsx(I,{className:"h-4 w-4"}),"Copiar"]})]}),e.jsx(l,{value:s,onChange:e=>t(e.target.value),className:"font-mono text-sm min-h-[200px] mb-4"})]})}function L({content:a}){return e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text",children:"Observações e Instruções"}),e.jsx("div",{className:"prose prose-blue max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0",dangerouslySetInnerHTML:{__html:(s=a,s.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\n/g,"<br>").replace(/- /g,"• "))}})]});var s}function G({diagnoses:s,summary:r,formData:i}){const[t,n]=a.useState(null),[l,d]=a.useState(null),[p,u]=a.useState(""),[x,h]=a.useState(null),[g,b]=a.useState(!1),[y,f]=a.useState(!1);return e.jsxs("div",{className:"space-y-8",children:[e.jsx(z,{formData:i}),!t&&e.jsxs("div",{className:"text-center space-y-2 mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-primary",children:"Selecione um diagnóstico abaixo"}),e.jsx(c,{className:"w-6 h-6 text-primary mx-auto animate-bounce"})]}),e.jsx(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in",children:e.jsx("div",{className:"space-y-3",children:s.map(((a,s)=>e.jsxs("div",{onClick:()=>n(a),className:"p-4 rounded-lg transition-all cursor-pointer group "+(t?.condition===a.condition?"bg-primary/10 border-2 border-primary":"bg-white/50 border border-primary/20 hover:bg-primary/5"),children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("h4",{className:"font-medium text-primary group-hover:text-primary/80 transition-colors",children:a.condition}),e.jsxs("span",{className:"text-sm font-medium text-primary/80",children:[a.probability,"%"]})]}),e.jsx(m,{value:a.probability,className:"h-2 bg-primary/10"})]},s)))})}),t&&e.jsx(R,{selectedDiagnosis:t,patientData:i,onPrescriptionGenerated:e=>{const a=e.prescription.split("**Medicamentos:**")[1]?.split("**Observações:**")[0]?.trim()||"",s=e.prescription.split("**Observações:**")[1]?.trim()||"";d(e.prescription),u(a),h(s),b(!1)},onDetailedAnalysis:e=>{h(e),d(null),f(!1)},setIsLoadingPrescription:b,setIsLoadingAnalysis:f}),g&&e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-pulse",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.3s]"}),e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.15s]"}),e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce"})]}),e.jsx("p",{className:"text-center text-primary/60 mt-4",children:"Gerando prescrição..."})]}),y&&e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-pulse",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.3s]"}),e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.15s]"}),e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce"})]}),e.jsx("p",{className:"text-center text-primary/60 mt-4",children:"Gerando análise detalhada..."})]}),l&&e.jsxs(e.Fragment,{children:[e.jsx(M,{prescription:l,editablePrescription:p,onPrescriptionChange:u}),x&&e.jsx(L,{content:x})]}),!l&&x&&e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text",children:"Análise Detalhada"}),e.jsx("div",{className:"prose prose-blue max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0",dangerouslySetInnerHTML:{__html:x.replace(/\n/g,"<br>").replace(/- /g,"• ")}})]}),e.jsxs(o,{className:"p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text",children:"Resumo da Análise"}),e.jsx("p",{className:"text-gray-600 text-base leading-relaxed whitespace-pre-line",children:r})]})]})}function O({formData:a,setFormData:s}){return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(d,{htmlFor:"age",className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Idade"}),e.jsx(p,{id:"age",type:"number",min:0,max:120,value:a.age,onChange:e=>(e=>{if(""===e)return void s({...a,age:"",isPediatric:!1});const r=parseInt(e);if(!isNaN(r)){const e=r<18;s({...a,age:r,isPediatric:e})}})(e.target.value),required:!0,className:"h-12 text-lg transition-all duration-200 focus:ring-2 focus:ring-primary/50 bg-white/50 backdrop-blur-sm"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(d,{htmlFor:"weight",className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Peso (kg)"}),e.jsx(p,{id:"weight",type:"number",min:0,max:200,step:.1,value:a.weight,onChange:e=>(e=>{if(""===e)return void s({...a,weight:""});const r=parseFloat(e);isNaN(r)||s({...a,weight:r})})(e.target.value),required:!0,className:"h-12 text-lg transition-all duration-200 focus:ring-2 focus:ring-primary/50 bg-white/50 backdrop-blur-sm"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Gênero"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(i,{type:"button",variant:"male"===a.gender?"default":"outline",onClick:()=>s({...a,gender:"male"}),className:"flex-1 h-12",children:"Masculino"}),e.jsx(i,{type:"button",variant:"female"===a.gender?"default":"outline",onClick:()=>s({...a,gender:"female"}),className:"flex-1 h-12",children:"Feminino"})]}),"female"===a.gender&&e.jsxs("div",{className:"mt-4 space-y-3 animate-fade-in",children:[e.jsx(d,{className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Está grávida?"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(i,{type:"button",variant:a.isPregnant?"default":"outline",onClick:()=>s({...a,isPregnant:!0}),className:"flex-1 h-12",children:"Sim"}),e.jsx(i,{type:"button",variant:a.isPregnant?"outline":"default",onClick:()=>s({...a,isPregnant:!1}),className:"flex-1 h-12",children:"Não"})]})]})]})]})}const _=[{name:"Gerais",symptoms:["Febre","Cansaço/extrema fadiga","Perda de peso inexplicada","Suor excessivo","Tremores","Calafrios","Mal-estar geral","Apatia","Perda de apetite"]},{name:"Cardiovasculares",symptoms:["Dor no peito","Falta de ar (dispneia)","Palpitações","Edema (inchaço) nos pés ou tornozelos","Tontura ou desmaio","Hipotensão (pressão baixa)","Hipertensão (pressão alta)","Batimento cardíaco irregular","Cianose (coloração azulada)"]},{name:"Respiratórios",symptoms:["Tosse persistente","Dificuldade para respirar (dispneia)","Chiado no peito","Catarro (escarro) com sangue","Dor ao respirar","Respiração acelerada","Sensação de aperto no peito"]},{name:"Digestivos",symptoms:["Náusea","Vômito","Diarreia","Prisão de ventre (constipação)","Dor abdominal","Distensão abdominal","Indigestão (azia)","Sangramento gastrointestinal (hemorragia)","Fezes escuras (indicação de sangue)","Perda de apetite"]},{name:"Musculoesqueléticos",symptoms:["Dor nas articulações (artralgia)","Rigidez nas articulações","Dor nos músculos (mialgia)","Fraqueza muscular","Limitação de movimento","Edema nas articulações"]},{name:"Neurológicos",symptoms:["Dor de cabeça (cefaleia)","Tontura","Confusão mental","Perda de equilíbrio","Dormência ou formigamento (parestesias)","Visão embaçada","Dificuldade de concentração","Convulsões","Alterações no nível de consciência","Perda de memória"]},{name:"Urológicos",symptoms:["Dor ao urinar (disúria)","Urina escura ou sanguinolenta","Aumento da frequência urinária","Incontinência urinária","Dor nas costas ou flanco"]},{name:"Endócrinos",symptoms:["Alterações no apetite","Suor excessivo","Tolerância ao calor ou ao frio alterada","Alterações no ciclo menstrual","Mudanças de peso inexplicadas","Tremores","Irritabilidade","Boca seca","Aumento da sede (polidipsia)"]},{name:"Psiquiátricos",symptoms:["Ansiedade","Depressão","Mudanças de humor","Irritabilidade","Insônia","Alucinações","Delírios","Pensamentos suicidas"]},{name:"Dermatológicos",symptoms:["Erupções cutâneas","Coceira (prurido)","Alterações na cor da pele","Manchas na pele","Pele seca ou oleosa excessiva","Perda de cabelo","Unhas frágeis ou quebradiças"]},{name:"Imunológicos",symptoms:["Febre baixa crônica","Sensibilidade a infecções recorrentes","Inflamação persistente","Reações alérgicas","Gânglios linfáticos inchados"]},{name:"Hematológicos",symptoms:["Sangramentos ou hematomas fáceis","Palidez","Dificuldade para cicatrização","Dores no peito relacionadas à coagulação","Sangramento nas gengivas ou nariz"]},{name:"Renais",symptoms:["Retenção de líquidos","Urina espumosa","Sangue na urina"]},{name:"Oftalmológicos",symptoms:["Visão turva","Olhos vermelhos","Sensibilidade à luz","Dor ocular","Visão dupla"]},{name:"Otorrinolaringológicos",symptoms:["Dor de garganta","Rouquidão","Zumbido nos ouvidos","Secreção nasal","Perda de audição"]},{name:"Ginecológicos",symptoms:["Dor pélvica","Alterações no fluxo menstrual","Sangramento entre períodos","Dificuldade para engravidar","Corrimento vaginal anormal","Seios doloridos"]},{name:"Relacionados a alergias",symptoms:["Coceira na pele","Inchaço nos lábios ou rosto","Dificuldade para respirar após exposição a alérgenos"]},{name:"De infecção",symptoms:["Calafrios","Vermelhidão ou calor local","Exudato purulento","Febre alta","Sudorese excessiva"]}];function V({categories:a,selectedSymptoms:s,onSymptomToggle:r}){return e.jsx("div",{className:"space-y-6",children:a.map((a=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:a.name}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:a.symptoms.map((a=>e.jsx("div",{onClick:()=>r(a),className:"p-3 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-md "+(s.includes(a)?"border-primary bg-primary/10 shadow":"border-gray-200 hover:border-primary/50"),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 flex-shrink-0 rounded-full border-2 border-primary flex items-center justify-center",children:s.includes(a)&&e.jsx("div",{className:"w-2 h-2 rounded-full bg-primary animate-scale-in"})}),e.jsx("span",{className:"text-sm",children:a})]})},a)))})]},a.name)))})}function H({searchTerm:a,setSearchTerm:s,selectedCategory:r,setSelectedCategory:i}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(u,{className:"absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-500"}),e.jsx(p,{placeholder:"Buscar sintomas...",value:a,onChange:e=>s(e.target.value),className:"pl-10 h-12 text-lg bg-white/50 backdrop-blur-sm w-full"})]}),e.jsx("div",{className:"md:hidden",children:e.jsxs(x,{value:r||"all",onValueChange:e=>i("all"===e?null:e),children:[e.jsx(h,{className:"w-full bg-white/50 backdrop-blur-sm border-primary/20",children:e.jsx(g,{placeholder:"Selecione uma categoria"})}),e.jsxs(b,{className:"bg-white/95 backdrop-blur-sm",children:[e.jsx(y,{value:"all",children:"Todos"}),_.map((a=>e.jsx(y,{value:a.name,children:a.name},a.name)))]})]})}),e.jsxs("div",{className:"hidden md:flex flex-wrap gap-2",children:[e.jsx(f,{variant:r?"outline":"default",className:"cursor-pointer hover:bg-primary/90 transition-colors text-sm",onClick:()=>i(null),children:"Todos"}),_.map((a=>e.jsx(f,{variant:r===a.name?"default":"outline",className:"cursor-pointer hover:bg-primary/90 transition-colors text-sm",onClick:()=>i(a.name),children:a.name},a.name)))]})]})}function B({selectedSymptoms:s,onSymptomsChange:r}){const[i,t]=a.useState(""),[n,o]=a.useState(null),l=_.filter((e=>!n||e.name===n)).map((e=>({...e,symptoms:e.symptoms.filter((e=>!i||v(e.toLowerCase(),i.toLowerCase())<=3||e.toLowerCase().includes(i.toLowerCase())))}))).filter((e=>e.symptoms.length>0));return e.jsxs("div",{className:"space-y-4",children:[e.jsx(H,{searchTerm:i,setSearchTerm:t,selectedCategory:n,setSelectedCategory:o}),e.jsx(j,{className:"h-[400px] rounded-lg border p-4 bg-white/50 backdrop-blur-sm",children:e.jsx(V,{categories:l,selectedSymptoms:s,onSymptomToggle:e=>{const a=s.includes(e)?s.filter((a=>a!==e)):[...s,e];r(a)}})}),e.jsxs("div",{className:"flex items-center justify-between px-4",children:[e.jsxs("span",{className:"text-sm text-gray-500",children:[s.length," sintoma(s) selecionado(s)"]}),s.length>0&&e.jsx("button",{onClick:()=>r([]),className:"text-sm text-red-500 hover:text-red-600 transition-colors",children:"Limpar seleção"})]})]})}function U({formData:s,setFormData:r}){const[t,n]=a.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Sinais e Sintomas"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(i,{type:"button",variant:"outline",onClick:()=>n(!t),className:"w-full py-6 text-base font-medium bg-accent-blue/50 hover:bg-accent-blue/70 border-primary/20 hover:border-primary/40 text-primary/80 hover:text-primary transition-all duration-300 whitespace-normal h-auto",children:"Clique aqui para escrever os sinais/sintomas ou caso clínico manualmente"}),t&&e.jsx(l,{placeholder:"Descreva aqui sinais/sintomas relevantes e/ou caso clínico completo.",value:s.manualSymptoms||"",onChange:e=>r({...s,manualSymptoms:e.target.value}),className:"min-h-[100px] text-base bg-white/50 backdrop-blur-sm resize-none animate-fade-in"}),e.jsx(B,{selectedSymptoms:s.symptoms,onSymptomsChange:e=>r({...s,symptoms:e})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Intensidade dos sintomas"}),e.jsx(T,{value:[s.symptomsIntensity],onValueChange:e=>r({...s,symptomsIntensity:e[0]}),max:10,min:1,step:1,className:"py-4"}),e.jsxs("div",{className:"text-center text-3xl font-semibold bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:[s.symptomsIntensity,"/10"]})]})]})}function Z({formData:a,setFormData:s}){return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(d,{className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Tem doenças crônicas?"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(i,{type:"button",variant:a.hasChronicDiseases?"default":"outline",onClick:()=>s({...a,hasChronicDiseases:!0}),className:"flex-1 h-12",children:"Sim"}),e.jsx(i,{type:"button",variant:a.hasChronicDiseases?"outline":"default",onClick:()=>s({...a,hasChronicDiseases:!1,chronicDiseases:void 0}),className:"flex-1 h-12",children:"Não"})]}),a.hasChronicDiseases&&e.jsx(p,{placeholder:"Descreva as doenças crônicas",value:a.chronicDiseases||"",onChange:e=>s({...a,chronicDiseases:e.target.value}),className:"mt-2 h-12 text-lg bg-white/50 backdrop-blur-sm"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(d,{className:"text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent",children:"Fez exames recentemente?"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(i,{type:"button",variant:a.hasRecentExams?"default":"outline",onClick:()=>s({...a,hasRecentExams:!0}),className:"flex-1 h-12",children:"Sim"}),e.jsx(i,{type:"button",variant:a.hasRecentExams?"outline":"default",onClick:()=>s({...a,hasRecentExams:!1,examDetails:void 0}),className:"flex-1 h-12",children:"Não"})]}),a.hasRecentExams&&e.jsx(p,{placeholder:"Descreva os exames e resultados",value:a.examDetails||"",onChange:e=>s({...a,examDetails:e.target.value}),className:"mt-2 h-12 text-lg bg-white/50 backdrop-blur-sm"})]})]})}function $({formData:a,setFormData:s}){return e.jsxs("div",{className:"space-y-8",children:[e.jsx(O,{formData:a,setFormData:s}),e.jsx(U,{formData:a,setFormData:s}),e.jsx(Z,{formData:a,setFormData:s})]})}function J({onDiagnosisStart:s}){const[t,l]=a.useState(!1),[c,m]=a.useState(null),[d,p]=a.useState({age:0,weight:0,gender:"male",symptoms:[],hasChronicDiseases:!1,symptomsIntensity:5,hasRecentExams:!1,isPediatric:!1}),{toast:u}=r();return c?e.jsxs("div",{className:"space-y-6 animate-fade-in",children:[e.jsx(G,{diagnoses:c.diagnoses,summary:c.summary,formData:d}),e.jsx(i,{onClick:()=>m(null),className:"w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:"Nova Análise"})]}):e.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),0!==d.symptoms.length||d.manualSymptoms&&""!==d.manualSymptoms.trim())if(d.weight<=0)u({variant:"destructive",title:"Erro no formulário",description:"Por favor, insira um peso válido."});else{l(!0);try{const{data:e,error:a}=await n.functions.invoke("diagnose",{body:d});if(a)throw a;m(e),s()}catch(a){u({variant:"destructive",title:"Erro",description:"Ocorreu um erro ao processar o diagnóstico. Tente novamente."})}finally{l(!1)}}else u({variant:"destructive",title:"Erro no formulário",description:"Por favor, selecione pelo menos um sintoma ou descreva os sintomas manualmente."})},className:"space-y-8 animate-fade-in",children:[e.jsx(o,{className:"p-8 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300",children:e.jsx($,{formData:d,setFormData:p})}),e.jsx(i,{type:"submit",className:"w-full h-14 text-lg bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300",disabled:t||d.weight<=0,children:t?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"mr-2 h-6 w-6 animate-spin"}),"Processando"]}):"Analisar Sintomas"})]})}function K({onAccept:s}){const[t,o]=a.useState(!0),{toast:l}=r();return e.jsx(w,{open:t,onOpenChange:o,children:e.jsxs(D,{className:"sm:max-w-[500px]",children:[e.jsxs(S,{children:[e.jsx(C,{children:"Termos de Uso do Assistente IA"}),e.jsx(k,{children:"Antes de prosseguir, é importante que você compreenda e aceite os seguintes termos:"})]}),e.jsxs("div",{className:"space-y-4 py-4 text-sm text-muted-foreground",children:[e.jsx("p",{children:"1. Este assistente utiliza inteligência artificial para gerar análises e prescrições médicas."}),e.jsx("p",{children:"2. As recomendações fornecidas são baseadas em padrões e dados de treinamento, mas não substituem o julgamento clínico profissional."}),e.jsx("p",{children:"3. Todas as análises e prescrições devem ser revisadas e validadas por um profissional de saúde qualificado antes de serem aplicadas."}),e.jsx("p",{children:"4. O usuário é responsável por verificar a precisão e adequação de todas as informações fornecidas."})]}),e.jsx(P,{children:e.jsx(i,{onClick:async()=>{try{const{error:e}=await n.from("ai_terms_acceptance").insert([{user_id:(await n.auth.getUser()).data.user?.id}]);if(e)throw e;o(!1),s()}catch(e){l({variant:"destructive",title:"Erro ao aceitar os termos",description:e.message})}},className:"w-full",children:"Aceito os Termos"})})]})})}const Q=()=>{const s=E(),[r,t]=a.useState(!1),[o,l]=a.useState(!0),[c,m]=a.useState(!1);a.useEffect((()=>{d()}),[]);const d=async()=>{try{const{data:e}=await n.auth.getSession();if(!e.session)return void s("/");const{data:a}=await n.from("ai_terms_acceptance").select("accepted_at").eq("user_id",e.session.user.id).maybeSingle();t(!a),l(!1)}catch(e){l(!1)}};return o?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-primary/5 to-primary/10",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.3s]"}),e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.15s]"}),e.jsx("div",{className:"w-4 h-4 bg-primary/60 rounded-full animate-bounce"})]})}):e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-primary/10",children:[e.jsx(A,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-12",children:[e.jsx(i,{onClick:()=>s("/"),variant:"outline",className:"mb-6 bg-white/50 hover:bg-white/70 backdrop-blur-sm hidden sm:inline-flex",children:"Voltar ao Menu Inicial"}),!c&&e.jsxs("div",{className:"text-center mb-8 animate-fade-in",children:[e.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text inline-block",children:"Assistente IA PedBook"}),e.jsx("p",{className:"text-gray-600 mt-2 max-w-2xl mx-auto",children:"Auxílio inteligente para diagnósticos e condutas médicas em pediatria"})]}),e.jsx("div",{className:"animate-fade-in-up",children:e.jsx(J,{onDiagnosisStart:()=>m(!0)})})]})}),e.jsx(F,{}),r&&e.jsx(K,{onAccept:()=>t(!1)})]})};export{Q as default};
