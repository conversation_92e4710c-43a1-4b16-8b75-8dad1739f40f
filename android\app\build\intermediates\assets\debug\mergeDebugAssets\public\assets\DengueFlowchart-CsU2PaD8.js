import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{L as r}from"./router-BAzpOxbo.js";import{an as o,a5 as s,aM as i,B as t,m as n,R as d,aa as l,a7 as c}from"./index-D9amGMlQ.js";import m from"./Footer-BkFd5qSK.js";import{F as u,a as p}from"./flowchartSEOData-B7-hWfhR.js";import{A as g}from"./arrow-down-CRHiKBGF.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./target-Cj27UDYs.js";import"./zap-DULtmWB8.js";import"./book-open-CzUd5kBy.js";import"./star-BlLX_9hT.js";import"./circle-help-DFUIKtE9.js";import"./instagram-CuaDlQAQ.js";const x=({weight:a,age:r,onWeightChange:n,onAgeChange:d,onSubmit:l})=>e.jsxs("form",{onSubmit:l,className:"space-y-6 max-w-md mx-auto animate-fade-in",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"weight",className:"text-gray-700 dark:text-gray-200",children:"Peso do paciente (kg)"}),e.jsx(s,{id:"weight",type:"number",step:"0.1",min:"0",required:!0,value:a,onChange:e=>n(e.target.value),className:i.input("bg-white/50 dark:bg-slate-800/50 border-orange-200 dark:border-orange-900/30")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"age",className:"text-gray-700 dark:text-gray-200",children:"Idade do paciente (anos)"}),e.jsx(s,{id:"age",type:"number",step:"0.1",min:"0",required:!0,value:r,onChange:e=>d(e.target.value),className:i.input("bg-white/50 dark:bg-slate-800/50 border-orange-200 dark:border-orange-900/30")})]}),e.jsx(t,{type:"submit",className:"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 dark:from-orange-600 dark:to-red-600 dark:hover:from-orange-700 dark:hover:to-red-700 text-white",children:"Iniciar Avaliação"})]}),h=({question:a,onAnswer:r,selectedAnswer:o})=>{const s=a.includes("sinais de gravidade"),l=a.includes("sangramento espontâneo"),c=a.includes("sinais de choque");return e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8 animate-fade-in",children:e.jsxs(d,{className:i.gradientCard("orange","p-6 relative overflow-hidden border border-orange-200 dark:border-orange-800/30"),children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 dark:from-white/5 to-transparent pointer-events-none"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6 text-center relative z-10",children:a}),s&&e.jsx("div",{className:"space-y-6 mb-6 relative z-10",children:e.jsx("div",{className:"border-2 border-orange-200 dark:border-orange-700/50 rounded-lg p-6 bg-orange-50/50 dark:bg-orange-900/30",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-orange-700 dark:text-orange-300 text-center",children:"Sinais de alarme presentes e sinais de gravidades ausentes"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-200",children:[e.jsx("li",{children:"Dor abdominal intensa (referida ou à palpação) e contínua;"}),e.jsx("li",{children:"Vômitos persistentes;"}),e.jsx("li",{children:"Extravasamento de líquidos (ascite, derrame pleural, derrame pericárdico);"}),e.jsx("li",{children:"Hipotensão postural e/ou lipotimia;"}),e.jsx("li",{children:"Hepatomegalia maior do que 2 cm abaixo do rebordo costal;"}),e.jsx("li",{children:"Sangramento de mucosa;"}),e.jsx("li",{children:"Letargia e/ou irritabilidade;"}),e.jsx("li",{children:"Aumento progressivo do hematócrito;"})]})]})})}),c&&e.jsx("div",{className:"space-y-6 mb-6 relative z-10",children:e.jsx("div",{className:"border-2 border-orange-200 dark:border-orange-700/50 rounded-lg p-6 bg-orange-50/50 dark:bg-orange-900/30",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-red-700 dark:text-red-300 text-center",children:"DENGUE - SINAIS DE CHOQUE"}),e.jsx("p",{className:"text-gray-700 dark:text-gray-200 italic",children:"• Extravasamento grave de plasma, levando ao choque evidenciado por taquicardia;"}),e.jsxs("ul",{className:"list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-200",children:[e.jsx("li",{children:"Extremidades distais frias;"}),e.jsx("li",{children:"Pulso fraco e filiforme;"}),e.jsxs("li",{children:["Enchimento capilar lento (",">","2 segundos);"]}),e.jsxs("li",{children:["Pressão arterial convergente (","<","20 mmHg);"]}),e.jsx("li",{children:"Taquipneia;"}),e.jsxs("li",{children:["Oligúria (","<","1.5 mL/kg/h);"]}),e.jsx("li",{children:"Hipotensão arterial (fase tardia do choque);"}),e.jsx("li",{children:"Cianose (fase tardia do choque);"}),e.jsx("li",{children:"Acumulação de líquidos com insuficiência respiratória;"}),e.jsx("li",{children:"Sangramento grave;"}),e.jsx("li",{children:"Comprometimento grave de órgãos;"})]})]})})}),l&&e.jsx("div",{className:"space-y-6 mb-6 relative z-10",children:e.jsx("div",{className:"border-2 border-orange-200 dark:border-orange-700/50 rounded-lg p-6 bg-orange-50/50 dark:bg-orange-900/30",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-orange-700 dark:text-orange-300 text-center",children:"Condições clínicas especiais e/ou risco social ou comorbidades:"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsxs("ul",{className:"list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-200",children:[e.jsx("li",{children:"Doenças autoimunes;"}),e.jsx("li",{children:"Hepatopatias;"}),e.jsx("li",{children:"Doença ácido péptica;"}),e.jsx("li",{children:"Doença renal crônica;"}),e.jsx("li",{children:"Doenças hematológicas crônicas;"}),e.jsx("li",{children:"Obesidade;"}),e.jsxs("li",{children:["Lactentes (","<","24 meses);"]}),e.jsx("li",{children:"Gestantes"}),e.jsx("li",{children:"Hipertensão arterial ou outras doenças cardiovasculares graves;"}),e.jsx("li",{children:"Diabetes mellitus;"}),e.jsx("li",{children:"Doença pulmonar obstrutiva crônica (DPOC);"}),e.jsx("li",{children:"Asma;"})]})}),e.jsx("h3",{className:"text-lg font-semibold text-orange-700 dark:text-orange-300 text-center",children:"Como realizar a Prova do Laço:"}),e.jsx("div",{children:e.jsxs("ol",{className:"list-decimal pl-6 space-y-2 text-gray-700 dark:text-gray-200",children:[e.jsxs("li",{children:[e.jsx("b",{children:"Aferir a Pressão Arterial:"})," Meça a pressão arterial e calcule a pressão média (PAS + PAD) / 2."]}),e.jsxs("li",{children:[e.jsx("b",{children:"Insuflar o Manguito:"})," Insufle o manguito do esfigmomanômetro até a pressão média calculada e mantenha por 5 minutos."]}),e.jsxs("li",{children:[e.jsx("b",{children:"Desinsuflar e Observar:"})," Após os 5 minutos, desinsufle o manguito e aguarde 1-2 minutos."]}),e.jsxs("li",{children:[e.jsx("b",{children:"Contar Petéquias:"})," Conte as petéquias (pequenos pontos vermelhos) dentro de um quadrado de 2,5 cm x 2,5 cm no braço ou antebraço."]}),e.jsxs("li",{children:[e.jsx("b",{children:"Interpretar o Resultado:"}),e.jsxs("ul",{className:"list-disc pl-6",children:[e.jsxs("li",{children:[e.jsx("b",{children:"Positivo:"})," 20 ou mais petéquias no quadrado."]}),e.jsxs("li",{children:[e.jsx("b",{children:"Negativo:"})," Menos de 20 petéquias."]})]})]})]})})]})]})})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 relative z-10",children:[e.jsx(t,{onClick:()=>r(!0),variant:!0===o?"default":"outline",className:"flex-1 transition-all duration-300 border "+(!0===o?"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 dark:from-orange-600 dark:to-red-600 dark:hover:from-orange-700 dark:hover:to-red-700":"hover:bg-orange-50 dark:hover:bg-orange-900/30 border-orange-200 dark:border-orange-700/50"),children:"Sim"}),e.jsx(t,{onClick:()=>r(!1),variant:!1===o?"default":"outline",className:"flex-1 transition-all duration-300 border "+(!1===o?"bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 dark:from-orange-600 dark:to-red-600 dark:hover:from-orange-700 dark:hover:to-red-700":"hover:bg-orange-50 dark:hover:bg-orange-900/30 border-orange-200 dark:border-orange-700/50"),children:"Não"})]})]})})},b=({group:a,color:r,instructions:o,nextQuestion:s,nextStep:t,onContinue:l,weight:c=0,age:m=0,showHydration:u})=>{const p=u?((e,a)=>{if(e/12>=13)return{totalVolume:Math.round(60*a),sro:Math.round(60*a/3),caseiros:Math.round(60*a*2/3),formula:"60 mL/kg/dia"};{let e;e=a<=10?130:a<=20?100:80;const r=Math.round(a*e);return{totalVolume:r,sro:Math.round(r/3),caseiros:Math.round(2*r/3),formula:`${e} mL/kg/dia`}}})(m,c):null,x=u?((e,a)=>{const r=a/12;return{paracetamol:{drops:Math.min(Math.round(1*e),r<12?35:55),maxDaily:r<12?"5 doses":"4.000 mg/dia"},dipirona:{drops:Math.min(Math.round(1*e),40),solution:Math.min(Math.round(.24*e*10)/10,15)}}})(c,m):null,h=r.split("-")[1]||"blue";return e.jsxs(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[e.jsxs(d,{className:i.gradientCard(h,"p-6 relative overflow-hidden"),children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 dark:from-white/5 to-transparent pointer-events-none"}),e.jsx("h3",{className:"text-xl font-bold text-gray-800 dark:text-gray-100 mb-4",children:a}),e.jsxs("div",{className:"space-y-3 text-gray-700 dark:text-gray-300",children:[o.map(((a,r)=>e.jsx("p",{className:"relative z-10",children:a},r))),u&&p&&e.jsxs("div",{className:"mt-6 space-y-4 bg-white/50 dark:bg-slate-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200",children:"Hidratação Oral Calculada:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:["Cálculo utilizado para a Taxa hídrica: ",p.formula]}),e.jsxs("p",{children:["Volume total em 24h: ",p.totalVolume," mL"]}),e.jsxs("p",{children:["• Soro de Reidratação Oral (1/3): ",p.sro," mL"]}),e.jsxs("p",{children:["• Líquidos caseiros (2/3): ",p.caseiros," mL"]}),e.jsx("p",{className:"text-sm italic mt-2"})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"Medicações para dor ou febre:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{children:"Paracetamol Solução oral 200 mg/mL - Gotas:"}),e.jsxs("p",{className:"ml-4",children:["• ",x?.paracetamol.drops," gotas/dose a cada 6 horas"]}),e.jsxs("p",{className:"ml-4 text-sm",children:["• Máximo: ",x?.paracetamol.maxDaily]}),e.jsx("p",{children:"ou"}),e.jsx("p",{className:"mt-2",children:"Dipirona Solução oral 500 mg/mL - Gotas:"}),e.jsxs("p",{className:"ml-4",children:["• ",x?.dipirona.drops," gotas/dose a cada 6 horas"]}),e.jsx("p",{className:"ml-4 text-sm",children:"• Máximo: 40 gotas por dose"}),e.jsx("p",{children:"ou"}),e.jsx("p",{className:"mt-2",children:"Dipirona Solução oral 50 mg/mL - Xarope:"}),e.jsxs("p",{className:"ml-4",children:["• ",x?.dipirona.solution," mL/dose a cada 6 horas"]}),e.jsx("p",{className:"ml-4 text-sm",children:"• Máximo: 15 mL/dose"})]})]})]})]}),(s||t)&&e.jsxs(n.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-6 flex flex-col items-center",children:[e.jsx(g,{className:"w-8 h-8 text-primary dark:text-blue-400 animate-bounce"}),e.jsx("p",{className:"text-sm text-primary dark:text-blue-400 mt-2",children:"Continue o manejo abaixo"})]})]}),s&&e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex justify-center",children:e.jsx(d,{onClick:()=>l?.(s),className:i.gradientCard("blue","p-4 hover:from-primary/20 hover:to-primary/10 dark:hover:from-primary/30 dark:hover:to-primary/20 transition-all cursor-pointer backdrop-blur-sm"),children:e.jsx("p",{className:"text-primary dark:text-blue-400 font-medium text-center",children:"Próxima Avaliação"})})}),t&&e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex justify-center",children:e.jsx(d,{onClick:()=>l?.(t),className:i.gradientCard("green","p-4 hover:from-green-500/20 hover:to-green-500/10 dark:hover:from-green-500/30 dark:hover:to-green-500/20 transition-all cursor-pointer backdrop-blur-sm"),children:e.jsx("p",{className:"text-green-600 dark:text-green-400 font-medium text-center",children:"Próxima Etapa"})})})]})},j=()=>{const[o,s]=a.useState(""),[t,n]=a.useState(""),[d,g]=a.useState(!0),j=(e=>{const[r,o]=a.useState("start"),[s,i]=a.useState({});return{currentStep:r,getQuestion:()=>({start:"O paciente tem sinais de gravidade ou sinais de alarme?",gravidade:"Paciente apresenta sinais de choque?",sangramento:"Pesquisar sangramento espontâneo de pele ou induzido (Prova do laço, condição clínica especial, risco social ou comorbidades).",grupo_d_melhora:"Teve melhora clínica e de hematócrito?",grupo_d_hemoconcentracao:"O hematócrito está em elevação?",grupo_d_choque:"Persistência do choque?",grupo_d_icc:"O paciente melhorou do choque, mas apresenta sinais de insuficiência cardíaca congestiva?",grupo_c_melhora:"Após expsnão teve melhora clínica e laboratorial. Sinais vitais e PA estável, diurese normal e queda do hematócrito?",grupo_b_hemoconcentracao:"Hemoconcentração ou sinais de alarme presentes?"}[r]||""),getResult:()=>{const a=(e=>({grupo_d:{group:"Grupo D - Dengue Grave",color:"bg-red-50",instructions:["Conduta:",`Realizar expansão volêmica imediata: ${(20*e).toFixed(1)} mL de solução salina em 20 minutos em qualquer nível de complexidade, inclusive durante eventual transferência para uma unidade de referência, mesmo na ausência de exames complementares.`,"Acompanhamento Em leito de UTI até estabilização – mínimo de 48h.","Exames complementares:","• Obrigatórios: hemograma completo, dosagem de albumina sérica e transaminases;","• Recomendados: raio X de tórax (PA, perfil e incidência de Laurell) e USG de abdome;","• Outros exames conforme necessidade: glicemia, ureia, creatinina, eletrólitos, gasometria, Tpae e ecocardiograma;","• Exames específicos para confirmação de dengue são obrigatórios, mas não são essenciais para conduta clínica.","Reavaliação:","Reavaliação clínica a cada 15-30 minutos e de hematócrito em 2 horas.",""],nextQuestion:"grupo_d_melhora"},grupo_d_albumina:{group:"Grupo D - Administração de Albumina",color:"bg-red-50",instructions:["Utilizar expansores plasmáticos (0,5-1 g/kg):","Como preparar a solução: Para cada 100 mL desta solução, usar 25 mL de albumina a 20% e 75 mL de SF a 0,9%",`Calculando para o peso atual: ${(10*e).toFixed(1)} mL de soro albuminado a 5%`,"",`Para preparar ${(10*e).toFixed(1)} mL de soro albuminado a 5%, usar:`,`• ${(10*e*.25).toFixed(1)} mL de albumina a 20%`,`• ${(10*e*.75).toFixed(1)} mL de solução salina (SF 0,9%)`,"","Observação: na falta desta, usar coloides sintéticos, a 10 mL/kg/hora."],nextStep:"grupo_c"},grupo_d_transfusao:{group:"Grupo D - Investigar hemorragia e coagulopatia de consumo",color:"bg-red-50",instructions:["Se hemorragia, transfundir concentrado de hemácias","Se coagulopatia, avaliar a necessidade de uso de plasma fresco (10 mL/kg). Vitamina K endovenosa e criopecipitado (1 U para cada 5-10 kg)","Transfusão de plaquetas apenas nas seguintes condições: sangramento persistente não controlado, depois de corrigidos os fatores de coagulação e do choque, e com trombocitopenia e INR > que 1,5 vez o valor normal"],nextStep:"grupo_c"},grupo_d_icc:{group:"Grupo D - Com resolução do choque, ausência de sangramento, mas com surgimento de outros sinais de gravidade, observar",color:"bg-red-50",instructions:["Sinais de desconforto respiratório, sinais de ICC e investigar hiperhidratação","Tratar com diminuição importante da infusão de líquido, uso de diuréticos e drogas inotrópicas, quando necessário"],nextStep:"grupo_c"},grupo_c:{group:"Grupo C - Sinais de alarme presentes e sinais de gravidade ausentes",color:"bg-orange-50",instructions:[`Realizar expansão volêmica: ${(10*e).toFixed(1)} mL de solução salina na primeira hora em qualquer ponto de atenção, independente do nível e complexidade, mesmo na ausência de exames complementares.`,"Acompanhamento Em leito de internação até estabilização – mínimo de 48h.","Exames complementares:","• Obrigatórios: hemograma completo, dosagem de albumina sérica e transaminases;","• Recomendados: raio X de tórax (PA, perfil e incidência de Laurell) e USG de abdome;","• Outros exames conforme necessidade: glicemia, ureia, creatinina, eletrólitos, gasometria, Tpae e ecocardiograma;","• Exames específicos para confirmação de dengue são obrigatórios, mas não são essenciais para conduta clínica.","Monitoramento:","• Reavaliação clínica após 1 hora (sinais vitais, PA, avaliar diurese – desejável 1 mL/kg/h)","• Na segunda hora: manter SF 10 mL/kg/h e rever hematócrito"],nextQuestion:"grupo_c_melhora"},grupo_c_manutencao:{group:"Grupo C - Melhora clínica e laboratorial após a(s) fase(s) de expansão",color:"bg-orange-50",instructions:["Iniciar a fase de manutenção com soro fisiológico",`Primeira fase: ${(25*e).toFixed(1)} mL de solução salina em 6 horas`,"Se houver melhora, iniciar segunda fase:",`Segunda fase: ${(25*e).toFixed(1)} mL de solução salina em 8 horas`,"","Critérios para alta hospitalar:","PACIENTE PRECISA PREENCHER TODOS OS SEIS CRITÉRIOS A SEGUIR","• Estabilização hemodinâmica durante 48 horas;","• Ausência de febre por 24 horas;","• Melhora visível do quadro clínico;","• Hematócrito normal e estável por 24 horas;","• Plaquetas em elevação;","","Após alta:","• Marcar o retorno para reavaliação clínica e laboratorial segue orientação, conforme grupo B","• Preencher e entregar cartão de acompanhamento.","• Orientar sobre sinais de alarme/gravidade"]},grupo_c_repetir:{group:"Grupo C - Sem melhora do hematócrito ou dos sinais de hemodinâmicos",color:"bg-orange-50",instructions:["Repetir fase de expansão até três vezes;","Manter reavaliação clínica (sinais vitais, PA, avaliar diurese) após 1 hora e de hematócrito em 2 horas (após conclusão de cada etapa);","Sem melhora clínica e laboratorial, conduzir como grupo D;"]},grupo_b:{group:"Grupo B - Dengue sem sinais de alarme, com condição especial, ou com risco social e com comorbidades.",color:"bg-yellow-50",instructions:["Acompanhamento Em leito de observação até resultado de exames e reavaliação clínica","Exames complementares: Hemograma completo: obrigatório.","Conduta Hidratação oral (conforme Grupo A) até o resultado dos exames."],nextQuestion:"grupo_b_hemoconcentracao",showHydration:!0},grupo_b_final:{group:"Grupo B -  Alta",color:"bg-yellow-50",instructions:["1) Alta com retorno diário para reavaliação clínica e laboratorial (até 48 horas após a remissão da febre)","2) Manter hidratação oral conforme orientação","","IMPORTANTE:","• Os sinais de alarme e agravamento do quadro costumam ocorrer na fase de remissão da febre","• Retorno imediato na presença de sinais de alarme ou no dia da melhora da febre (possível início da fase crítica)","• Caso não haja defervescência, retornar no 5º dia da doença","• Entregar cartão de acompanhamento de dengue"],showHydration:!0},grupo_a:{group:"Grupo A - Dengue sem sinais de alarme, sem condição especial, sem risco social e sem comorbidades.",color:"bg-green-50",instructions:["Iniciar hidratação oral;","Acompanhamento Ambulatorial;","Exames complementares A critério médico.","Retorno imediato se sinais de alarme"],showHydration:!0}}))(e);return r in a?a[r]:null},handleAnswer:e=>{const a={...s,[r]:e};switch(i(a),r){case"start":o(e?"gravidade":"sangramento");break;case"gravidade":o(e?"grupo_d":"grupo_c");break;case"sangramento":o(e?"grupo_b":"grupo_a");break;case"grupo_d_melhora":o(e?"grupo_c":"grupo_d_hemoconcentracao");break;case"grupo_d_hemoconcentracao":o(e?"grupo_d_albumina":"grupo_d_choque");break;case"grupo_d_choque":o(e?"grupo_d_transfusao":"grupo_d_icc");break;case"grupo_d_icc":o("grupo_c");break;case"grupo_c_melhora":o(e?"grupo_c_manutencao":"grupo_c_repetir");break;case"grupo_b_hemoconcentracao":o(e?"grupo_c":"grupo_b_final")}},resetFlow:()=>{o("start"),i({})},answers:s,setCurrentStep:o}})(parseFloat(o)),v=p.dengue;return e.jsxs("div",{className:i.pageBackground("min-h-screen flex flex-col from-orange-50 via-white to-orange-50 dark:from-orange-950 dark:via-slate-900 dark:to-slate-800"),children:[e.jsx(u,{...v}),e.jsx(l,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-8",children:[e.jsxs(r,{to:"/flowcharts",className:"inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors mb-8",children:[e.jsx(c,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Fluxogramas"})]}),e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("span",{className:"text-4xl",children:"🦟"}),e.jsx("h1",{className:"text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-orange-600 to-red-600 dark:from-orange-400 dark:to-red-400",children:"Fluxograma de Dengue"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Guia interativo para manejo de casos suspeitos de dengue em pediatria"})]}),d&&e.jsxs("div",{className:"bg-red-50 border-2 border-red-200 rounded-lg p-6 space-y-4 mb-8 dark:bg-red-900/20 dark:border-red-700/50",children:[e.jsx("h2",{className:"text-xl font-bold text-red-700 dark:text-red-300 text-center uppercase",children:"Suspeita de Dengue"}),e.jsx("p",{className:"text-gray-700 dark:text-gray-200 leading-relaxed",children:"Relato de febre, usualmente entre dois e sete dias de duração, e duas ou mais das seguintes manifestações: náusea, vômitos; exantema; mialgia, artralgia; cefaleia, dor retro-orbital; petéquias; prova do laço positiva e leucopenia. Também pode ser considerado caso suspeito toda criança com quadro febril agudo, usualmente entre dois e sete dias de duração, e sem foco de infecção aparente."}),e.jsx("p",{className:"text-red-600 dark:text-red-300 font-semibold text-center mt-4",children:"NOTIFICAR TODO CASO SUSPEITO DE DENGUE"})]}),d?e.jsx(x,{weight:o,age:t,onWeightChange:s,onAgeChange:n,onSubmit:e=>{e.preventDefault(),o&&t&&g(!1)}}):e.jsxs(e.Fragment,{children:[j.getQuestion()&&e.jsx(h,{question:j.getQuestion(),onAnswer:j.handleAnswer,selectedAnswer:j.answers[j.currentStep]}),j.getResult()&&e.jsx(b,{...j.getResult(),onReset:j.resetFlow,onContinue:e=>{j.setCurrentStep(e)},weight:parseFloat(o),age:parseFloat(t)})]})]})]}),e.jsx(m,{})]})};export{j as default};
