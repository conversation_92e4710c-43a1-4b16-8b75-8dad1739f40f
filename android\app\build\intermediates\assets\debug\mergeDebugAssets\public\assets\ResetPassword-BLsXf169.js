import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{i as r,Q as a,R as t,U as o,V as i,B as n,W as c,Y as d,Z as l,_ as m,$ as u,a0 as x,a1 as h,a2 as p,a3 as g,a4 as f,a5 as v,a6 as j,s as y,a7 as _}from"./index-CFnD44mG.js";import{c as w,b as S,s as b}from"./form-vendor-rYZw_ur7.js";import{A as N,a as k,b as I}from"./alert-DRNmYOQS.js";import{a as z,u as P}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const T=S({password:b().min(6,"A senha deve ter pelo menos 6 caracteres"),confirmPassword:b()}).refine((e=>e.password===e.confirmPassword),{message:"As senhas não coincidem",path:["confirmPassword"]}),C=()=>{const[S,b]=s.useState(!1),[C,E]=s.useState(!1),[V,A]=s.useState(null),[B,L]=s.useState(!1),U=z(),R=P(),{showNotification:O}=r(),D=w({resolver:a(T),defaultValues:{password:"",confirmPassword:""}});s.useEffect((()=>{setTimeout((async()=>{if(V)return;const e=new URLSearchParams(R.search),s=e.get("error"),r=e.get("error_code");if(e.get("error_description"),"access_denied"===s&&"otp_expired"===r)return E(!0),void O({title:"Link expirado",description:"Este link de recuperação expirou ou já foi utilizado. Solicite um novo link de recuperação.",type:"error",buttonText:"Solicitar novo link",onButtonClick:()=>U("/")});try{const{data:{session:e},error:s}=await y.auth.getSession();if(e?.user){if(new URLSearchParams(R.search).get("code"))return sessionStorage.setItem("reset_user_id",e.user.id),sessionStorage.setItem("reset_token",e.access_token),sessionStorage.setItem("recovery_method","session_with_code"),void A("session_with_code_recovery");const s=e.access_token;try{const r=s.split(".");if((JSON.parse(atob(r[1])).amr||[]).some((e=>"recovery"===e.method)))return sessionStorage.setItem("reset_user_id",e.user.id),sessionStorage.setItem("reset_token",s),sessionStorage.setItem("recovery_method","session_active"),void A("recovery_session_active")}catch(a){}}const r=new URLSearchParams(R.search),o=r.get("token"),i=r.get("type"),n=r.get("code");if(o&&"recovery"===i)try{const{data:e,error:s}=await y.auth.verifyOtp({token_hash:o,type:"recovery"});if(s){const{data:e,error:s}=await y.auth.getUser(o);if(s)return void E(!0);if(e?.user)return sessionStorage.setItem("reset_user_id",e.user.id),sessionStorage.setItem("reset_token",o),void A("url_recovery")}if(e?.user)return sessionStorage.setItem("reset_user_id",e.user.id),sessionStorage.setItem("reset_token",e.access_token||o),void A("token_recovery")}catch(t){return void E(!0)}else if(n){await new Promise((e=>setTimeout(e,2e3)));const{data:{session:e}}=await y.auth.getSession();if(e?.user)return sessionStorage.setItem("reset_user_id",e.user.id),sessionStorage.setItem("reset_token",e.access_token),void A("code_fallback_recovery")}const c=sessionStorage.getItem("recovery_login_detected"),d=sessionStorage.getItem("recovery_user_id");if(sessionStorage.getItem("pending_recovery_code")){const{data:{session:e}}=await y.auth.getSession();if(e?.user)return sessionStorage.setItem("reset_user_id",e.user.id),sessionStorage.setItem("reset_token",e.access_token),sessionStorage.removeItem("pending_recovery_code"),sessionStorage.removeItem("recovery_page_reloaded"),void A("pending_code_recovery")}if(c&&d)return sessionStorage.removeItem("recovery_login_detected"),sessionStorage.setItem("reset_user_id",d),void A("stored_recovery");E(!0)}catch(t){E(!0)}}),100)}),[R.search,V]);const $=()=>e.jsx("header",{className:"w-full bg-white border-b",children:e.jsx("div",{className:"container mx-auto px-4 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(n,{variant:"ghost",onClick:()=>U("/"),className:"flex items-center gap-2 text-primary hover:text-primary/80",children:[e.jsx(_,{className:"h-4 w-4"}),"Voltar"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(m,{className:"h-6 w-6 text-primary mr-2"}),e.jsx("span",{className:"font-bold text-lg",children:"PedBook"})]})]})})}),q=()=>e.jsx("footer",{className:"w-full bg-white border-t mt-auto",children:e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsx("p",{className:"text-center text-sm text-gray-500",children:"© 2025 PedBook. Todos os direitos reservados."})})});return B?e.jsxs("div",{className:"min-h-screen flex flex-col bg-gray-50",children:[e.jsx($,{}),e.jsx("div",{className:"container max-w-md mx-auto px-4 py-12 flex-grow flex items-center justify-center",children:e.jsx(t,{className:"w-full shadow-lg border-primary/10",children:e.jsxs(o,{className:"pt-6 pb-8 px-6 flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6",children:e.jsx(i,{className:"h-8 w-8 text-green-600"})}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Senha Atualizada!"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Sua senha foi alterada com sucesso. Você será redirecionado para a página de login em instantes."}),e.jsx(n,{variant:"duolingo",className:"w-full",onClick:()=>U("/"),children:"Ir para o login"})]})})}),e.jsx(q,{})]}):C?e.jsxs("div",{className:"min-h-screen flex flex-col bg-gray-50",children:[e.jsx($,{}),e.jsx("div",{className:"container max-w-md mx-auto px-4 py-12 flex-grow flex items-center",children:e.jsxs(t,{className:"w-full shadow-lg border-red-200",children:[e.jsxs(c,{className:"pb-4",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-12 h-12 rounded-full bg-red-100 flex items-center justify-center",children:e.jsx(d,{className:"h-6 w-6 text-red-600"})})}),e.jsx(l,{className:"text-center text-xl",children:"Link já utilizado ou expirado"})]}),e.jsxs(o,{className:"pt-2",children:[e.jsxs(N,{className:"mb-4 border-amber-200 bg-amber-50",children:[e.jsx(d,{className:"h-4 w-4 text-amber-600"}),e.jsx(k,{className:"text-amber-800",children:"Links de recuperação são de uso único"}),e.jsx(I,{className:"text-amber-700",children:"Por segurança, cada link só pode ser usado uma vez. Se você acessou este link em outro dispositivo, ele não funcionará mais."})]}),e.jsx("p",{className:"text-center text-gray-600 mb-6",children:"Solicite um novo link de recuperação para redefinir sua senha."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(n,{variant:"duolingo",onClick:()=>U("/?reset=true"),className:"w-full",children:"Solicitar novo link"}),e.jsx(n,{variant:"outline",onClick:()=>U("/"),className:"w-full",children:"Voltar para o início"})]})]})]})}),e.jsx(q,{})]}):e.jsxs("div",{className:"min-h-screen flex flex-col bg-gray-50",children:[e.jsx($,{}),e.jsx("div",{className:"container max-w-md mx-auto px-4 py-12 flex-grow flex items-center",children:e.jsxs(t,{className:"w-full shadow-lg border-primary/10",children:[e.jsxs(c,{className:"space-y-1",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(m,{className:"h-6 w-6 text-primary"})})}),e.jsx(l,{className:"text-center text-xl",children:"Redefinir Senha"}),e.jsx(u,{className:"text-center",children:"Crie uma nova senha segura para sua conta"})]}),e.jsx(o,{children:e.jsx(x,{...D,children:e.jsxs("form",{onSubmit:D.handleSubmit((async e=>{if(V)try{b(!0);const s=sessionStorage.getItem("reset_user_id");if(sessionStorage.getItem("reset_token"),!s)throw new Error("ID do usuário não encontrado");const{data:{session:r}}=await y.auth.getSession();if(r?.user?.id!==s)throw new Error("Sessão de recuperação não encontrada. Solicite um novo link de recuperação.");{const{error:s}=await y.auth.updateUser({password:e.password});if(s)throw new Error(`Erro ao atualizar senha: ${s.message}`)}sessionStorage.removeItem("reset_user_id"),sessionStorage.removeItem("reset_token"),sessionStorage.removeItem("recovery_token"),sessionStorage.removeItem("recovery_type"),sessionStorage.removeItem("recovery_intercepted"),sessionStorage.removeItem("recovery_login_detected"),sessionStorage.removeItem("recovery_user_id"),sessionStorage.removeItem("recovery_method");const{data:{session:a}}=await y.auth.getSession();O(a?.user?{title:"Senha atualizada!",description:"Sua senha foi atualizada com sucesso. Você já está logado!",type:"success",buttonText:"Ir para o início",onButtonClick:()=>U("/")}:{title:"Senha atualizada!",description:"Sua senha foi atualizada com sucesso. Faça login com a nova senha.",type:"success",buttonText:"Ir para o login",onButtonClick:()=>U("/")}),L(!0)}catch(s){O({title:"Erro ao atualizar senha",description:s.message||"Ocorreu um erro ao atualizar sua senha. Tente novamente.",type:"error",buttonText:"Tentar novamente"})}finally{b(!1)}else O({title:"Erro ao atualizar senha",description:"Token de redefinição inválido ou expirado.",type:"error",buttonText:"Voltar"})})),className:"space-y-6",children:[e.jsx(h,{control:D.control,name:"password",render:({field:s})=>e.jsxs(p,{children:[e.jsx(g,{children:"Nova Senha"}),e.jsx(f,{children:e.jsx(v,{type:"password",placeholder:"Digite sua nova senha",className:"bg-gray-50 border-gray-200 focus:border-primary",...s})}),e.jsx(j,{})]})}),e.jsx(h,{control:D.control,name:"confirmPassword",render:({field:s})=>e.jsxs(p,{children:[e.jsx(g,{children:"Confirmar Nova Senha"}),e.jsx(f,{children:e.jsx(v,{type:"password",placeholder:"Confirme sua nova senha",className:"bg-gray-50 border-gray-200 focus:border-primary",...s})}),e.jsx(j,{})]})}),e.jsx(n,{type:"submit",variant:"duolingo",className:"w-full",disabled:S,children:S?"Atualizando...":"Atualizar Senha"})]})})})]})}),e.jsx(q,{})]})};export{C as default};
