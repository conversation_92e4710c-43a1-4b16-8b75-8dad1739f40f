import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as s,u as r}from"./query-vendor-B-7l6Nb3.js";import{d as i,B as t,s as o,X as l,ao as n,a6 as c,ae as d,af as m,ag as u,ah as h,aj as p,T as x,D as g,e as j,f,g as v,U as y,ad as N,aD as b}from"./index-CNG-Xj2g.js";import{U as C}from"./upload-DYkAbgkr.js";import{A as w,a as _,b as F,c as k,d as S,e as q,f as E,g as U}from"./alert-dialog-2iES_sbM.js";import{P as I}from"./pencil-Csl4ZKWF.js";import{T as z}from"./trash-2-2OmjJUoy.js";import{P as R}from"./plus-5xdDX9-5.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";function A({onImageUploaded:s}){const[r,l]=a.useState(!1),{toast:n}=i();return e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs(t,{type:"button",variant:"outline",disabled:r,className:"relative",children:[e.jsx("input",{type:"file",className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer",accept:"image/*",onChange:async e=>{try{if(l(!0),!e.target.files||0===e.target.files.length)throw new Error("Você precisa selecionar uma imagem para fazer upload.");const a=e.target.files[0],r=a.name.split(".").pop(),i=`${Math.random()}.${r}`,{error:t,data:c}=await o.storage.from("formulas").upload(i,a);if(t)throw t;const{data:{publicUrl:d}}=o.storage.from("formulas").getPublicUrl(i);s(d),n({title:"Imagem enviada com sucesso!",description:"A imagem foi salva e vinculada à fórmula."})}catch(a){n({variant:"destructive",title:"Erro ao fazer upload da imagem",description:a.message})}finally{l(!1)}},disabled:r}),e.jsx(C,{className:"h-4 w-4 mr-2"}),r?"Enviando...":"Upload de Imagem"]})})}function D({imageUrl:a,onRemove:s}){return e.jsxs("div",{className:"relative inline-block",children:[e.jsx("img",{src:a,alt:"Preview",className:"w-32 h-32 object-cover rounded-lg"}),e.jsx(t,{type:"button",variant:"destructive",size:"icon",className:"absolute -top-2 -right-2 h-6 w-6",onClick:s,children:e.jsx(l,{className:"h-4 w-4"})})]})}function P({name:a,setName:s,brand:r,setBrand:i,categoryId:t,setCategoryId:o,description:l,setDescription:g,ageRange:j,setAgeRange:f,price:v,setPrice:y,imageUrl:N,setImageUrl:b,categories:C}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"name",children:"Nome"}),e.jsx(c,{id:"name",value:a,onChange:e=>s(e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"brand",children:"Marca"}),e.jsx(c,{id:"brand",value:r,onChange:e=>i(e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"category",children:"Categoria"}),e.jsxs(d,{value:t,onValueChange:o,children:[e.jsx(m,{children:e.jsx(u,{placeholder:"Selecione uma categoria"})}),e.jsx(h,{children:C?.map((a=>e.jsx(p,{value:a.id,children:a.name},a.id)))})]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"ageRange",children:"Faixa Etária"}),e.jsx(c,{id:"ageRange",value:j,onChange:e=>f(e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"price",children:"Preço"}),e.jsx(c,{id:"price",type:"number",step:"0.01",value:v,onChange:e=>y(e.target.value)})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"description",children:"Descrição"}),e.jsx(x,{id:"description",value:l,onChange:e=>g(e.target.value),className:"min-h-[100px]"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{children:"Imagem"}),e.jsxs("div",{className:"flex flex-col gap-4",children:[N&&e.jsx(D,{imageUrl:N,onRemove:()=>b("")}),e.jsx(A,{onImageUploaded:b})]})]})]})}function L({formula:l,open:n,onOpenChange:c}){const[d,m]=a.useState(""),[u,h]=a.useState(""),[p,x]=a.useState(""),[y,N]=a.useState(""),[b,C]=a.useState(""),[w,_]=a.useState(""),[F,k]=a.useState(""),{toast:S}=i(),q=s();a.useEffect((()=>{l?(m(l.name),h(l.brand),x(l.category_id||""),N(l.description||""),C(l.age_range),_(l.price?.toString()||""),k(l.image_url||"")):(m(""),h(""),x(""),N(""),C(""),_(""),k(""))}),[l]);const{data:E}=r({queryKey:["formula-categories"],queryFn:async()=>{const{data:e,error:a}=await o.from("pedbook_formula_categories").select("*").order("name");if(a)throw a;return e}});return e.jsx(g,{open:n,onOpenChange:c,children:e.jsxs(j,{children:[e.jsx(f,{children:e.jsx(v,{children:l?"Editar Fórmula":"Nova Fórmula"})}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{const e={name:d,brand:u,category_id:p||null,description:y,age_range:b,price:w?parseFloat(w):null,image_url:F};if(l){const{error:a}=await o.from("pedbook_formulas").update(e).eq("id",l.id);if(a)throw a;S({title:"Fórmula atualizada com sucesso!",description:`A fórmula ${d} foi atualizada.`})}else{const{error:a}=await o.from("pedbook_formulas").insert([e]);if(a)throw a;S({title:"Fórmula criada com sucesso!",description:`A fórmula ${d} foi adicionada.`})}q.invalidateQueries({queryKey:["formulas"]}),c(!1)}catch(a){S({variant:"destructive",title:"Erro ao salvar fórmula",description:a.message||"Ocorreu um erro ao salvar a fórmula."})}},className:"space-y-4",children:[e.jsx(P,{name:d,setName:m,brand:u,setBrand:h,categoryId:p,setCategoryId:x,description:y,setDescription:N,ageRange:b,setAgeRange:C,price:w,setPrice:_,imageUrl:F,setImageUrl:k,categories:E||[]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(t,{type:"button",variant:"outline",onClick:()=>c(!1),children:"Cancelar"}),e.jsx(t,{type:"submit",children:l?"Atualizar":"Criar"})]})]})]})})}function K({formulas:r,onEdit:l}){const[n,c]=a.useState(null),{toast:d}=i(),m=s();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map((a=>e.jsxs(y,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[a.image_url&&e.jsx("div",{className:"relative h-48",children:e.jsx("img",{src:a.image_url,alt:a.name,className:"w-full h-full object-cover"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-lg font-semibold",children:a.name}),e.jsx("p",{className:"text-sm text-muted-foreground",children:a.brand}),a.pedbook_formula_categories&&e.jsx("span",{className:"inline-block px-2 py-1 rounded-full text-xs bg-primary/10 text-primary",children:a.pedbook_formula_categories.name})]}),a.description&&e.jsx("p",{className:"text-sm text-muted-foreground",children:a.description}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Faixa etária: ",a.age_range]}),e.jsxs("div",{className:"flex gap-2 pt-4",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>l(a),children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Editar"]}),e.jsxs(t,{variant:"destructive",size:"sm",onClick:()=>c(a),children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),"Remover"]})]})]})})]},a.id)))}),e.jsx(w,{open:!!n,onOpenChange:()=>c(null),children:e.jsxs(_,{children:[e.jsxs(F,{children:[e.jsx(k,{children:"Você tem certeza?"}),e.jsxs(S,{children:["Esta ação não pode ser desfeita. Isso removerá permanentemente a fórmula"," ",e.jsx("span",{className:"font-semibold",children:n?.name}),"."]})]}),e.jsxs(q,{children:[e.jsx(E,{children:"Cancelar"}),e.jsx(U,{onClick:async()=>{if(n)try{const{error:e}=await o.from("pedbook_formulas").delete().eq("id",n.id);if(e)throw e;d({title:"Fórmula removida com sucesso!",description:`A fórmula ${n.name} foi removida.`}),m.invalidateQueries({queryKey:["formulas"]})}catch(e){d({variant:"destructive",title:"Erro ao remover fórmula",description:e.message})}finally{c(null)}},children:"Remover"})]})]})})]})}function O(){const[l,d]=a.useState(""),[m,u]=a.useState(!1),[h,p]=a.useState(null),[y,C]=a.useState(!1),[w,_]=a.useState(""),[F,k]=a.useState(""),{toast:S}=i(),q=s(),{data:E,isLoading:U}=r({queryKey:["formulas"],queryFn:async()=>{const{data:e,error:a}=await o.from("pedbook_formulas").select("\n          *,\n          pedbook_formula_categories (\n            id,\n            name\n          )\n        ").order("name");if(a)throw a;return e}}),I=E?.filter((e=>e.name.toLowerCase().includes(l.toLowerCase())||e.brand.toLowerCase().includes(l.toLowerCase())||e.description?.toLowerCase().includes(l.toLowerCase())));return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Gerenciamento de Fórmulas"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(t,{onClick:()=>C(!0),children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Nova Categoria"]}),e.jsxs(t,{onClick:()=>{p(null),u(!0)},children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Nova Fórmula"]})]})]}),e.jsxs("div",{className:"relative mb-6",children:[e.jsx(N,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(c,{type:"search",placeholder:"Pesquisar fórmulas...",value:l,onChange:e=>d(e.target.value),className:"pl-10"})]}),U?e.jsx("div",{className:"flex justify-center",children:e.jsx("p",{children:"Carregando fórmulas..."})}):e.jsx(K,{formulas:I||[],onEdit:e=>{p(e),u(!0)}}),e.jsx(L,{formula:h,open:m,onOpenChange:u}),e.jsx(g,{open:y,onOpenChange:C,children:e.jsxs(j,{children:[e.jsx(f,{children:e.jsx(v,{children:"Nova Categoria de Fórmula"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"name",children:"Nome"}),e.jsx(c,{id:"name",value:w,onChange:e=>_(e.target.value),placeholder:"Nome da categoria"})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"description",children:"Descrição"}),e.jsx(x,{id:"description",value:F,onChange:e=>k(e.target.value),placeholder:"Descrição da categoria"})]})]}),e.jsxs(b,{children:[e.jsx(t,{variant:"outline",onClick:()=>C(!1),children:"Cancelar"}),e.jsx(t,{onClick:async()=>{try{const{error:e}=await o.from("pedbook_formula_categories").insert([{name:w,description:F}]);if(e)throw e;S({title:"Categoria criada com sucesso!",description:`A categoria ${w} foi adicionada.`}),q.invalidateQueries({queryKey:["formula-categories"]}),q.invalidateQueries({queryKey:["formulas"]}),_(""),k(""),C(!1)}catch(e){S({variant:"destructive",title:"Erro ao criar categoria",description:e.message})}},children:"Criar Categoria"})]})]})})]})}export{O as default};
