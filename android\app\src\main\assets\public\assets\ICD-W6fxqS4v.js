import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as t,u as i}from"./query-vendor-B-7l6Nb3.js";import{ad as s,a6 as r,d as o,U as c,B as n,a9 as d,ab as m,a8 as l,s as p}from"./index-CNG-Xj2g.js";import g from"./Footer-BgCSiPkf.js";import{C as x}from"./copy-B7iorcXD.js";import{a as u}from"./router-BAzpOxbo.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-ik6vfZ65.js";import"./rocket-BEoGgNr2.js";import"./target-Dul0NbVV.js";import"./zap-C4mKju26.js";import"./book-open-EV5sJdXr.js";import"./star-BUSksJJE.js";import"./circle-help-BbvIlE64.js";import"./instagram-ClgJ7H9i.js";const h=({searchTerm:a,setSearchTerm:t,setIsSearching:i,handleSearch:o})=>e.jsx("form",{onSubmit:o,className:"relative max-w-2xl mx-auto",children:e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-lg blur-lg group-hover:opacity-75 transition-opacity"}),e.jsx(s,{className:"absolute left-4 top-1/2 -translate-y-1/2 text-primary/60 group-hover:text-primary/80 transition-colors"}),e.jsx(r,{type:"text",value:a,onChange:e=>{const a=e.target.value;t(a)},placeholder:"Digite o nome da doença ou código CID (ex: diabetes, A01)...",className:"pl-7 pr-4 py-6 text-lg bg-white/80 backdrop-blur-sm border-primary/20 focus:border-primary/40 rounded-lg shadow-lg shadow-primary/5 input-search"}),a.length>0&&a.length<2&&e.jsx("div",{className:"absolute top-full left-0 right-0 mt-1 text-sm text-gray-500 text-center",children:"Digite pelo menos 2 caracteres para buscar"})]})}),j=({results:a,isLoading:t,searchTerm:i})=>{const{toast:s}=o();return e.jsx("div",{className:"space-y-4 animate-fade-in",children:t?e.jsx("div",{className:"text-center py-8",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-gray-500",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}),"Buscando códigos CID..."]})}):a&&a.length>0?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center text-sm text-gray-600 mb-4",children:[a.length," resultado",1!==a.length?"s":""," encontrado",1!==a.length?"s":"",' para "',i,'"']}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto",children:a.map(((a,t)=>e.jsx(c,{className:"p-4 hover:shadow-md transition-shadow bg-white/80 backdrop-blur-sm border-primary/10",children:e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"font-semibold text-primary",children:a.code}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-6 w-6 text-gray-400 hover:text-primary",onClick:()=>{navigator.clipboard.writeText(a.code),s({description:"Código CID copiado com sucesso!",duration:2e3})},children:e.jsx(x,{className:"h-4 w-4"})})]}),e.jsx("p",{className:"text-gray-700 line-clamp-2",children:a.name}),a.description&&e.jsx("p",{className:"text-sm text-gray-500 mt-1 line-clamp-2",children:a.description})]})})},`${a.code}-${t}`)))})]}):i&&i.length>=2?e.jsxs("div",{className:"text-center py-8",children:[e.jsxs("p",{className:"text-gray-500 mb-2",children:['Nenhum resultado encontrado para "',i,'"']}),e.jsxs("p",{className:"text-sm text-gray-400",children:["Tente buscar por:",e.jsx("br",{}),"• Nome da doença (ex: diabetes, pneumonia)",e.jsx("br",{}),"• Código CID (ex: E10, J18)"]})]}):null})},y=({title:a,description:t,slug:i,keywords:s,clinicalUse:r,features:o,benefits:c,clinicalSignificance:n,relatedTopics:m,searchType:l="main",categoryName:p,icdCode:g})=>{const x=()=>{let e=`${a}`;return"main"===l?e+=" - Busca CID-10 Pediátrica":"category"===l?e+=" - Categoria CID-10":"specific"===l&&(e+=" - Código CID-10"),e+=" | PedBook",e.substring(0,60)},u=()=>{let e=`${a}: ${t}`;return g&&(e+=` Código: ${g}.`),p&&(e+=` Categoria: ${p}.`),e+=` ${r}`,o.length>0&&(e+=` Inclui: ${o.slice(0,3).join(", ")}.`),e+=" Ferramenta essencial para codificação médica pediátrica.",e.substring(0,160)},h="https://pedb.com.br/icd",j={"@context":"https://schema.org","@type":"MedicalWebPage",name:x(),description:u(),url:h,mainContentOfPage:{"@type":"WebPageElement",cssSelector:"main"},specialty:"Codificação Médica",audience:{"@type":"MedicalAudience",audienceType:"Médicos pediatras e profissionais da saúde"},about:{"@type":"MedicalCondition",name:a,description:r,code:g?{"@type":"MedicalCode",code:g,codingSystem:"ICD-10"}:void 0},lastReviewed:(new Date).toISOString().split("T")[0],reviewedBy:{"@type":"Organization",name:"PedBook",url:"https://pedb.com.br"}},y={"@context":"https://schema.org","@type":"MedicalOrganization",name:"PedBook",url:"https://pedb.com.br",logo:{"@type":"ImageObject",url:"https://pedb.com.br/faviconx.webp"},medicalSpecialty:"Pediatria",serviceType:"Codificação CID-10",areaServed:"Brasil",availableService:{"@type":"MedicalTherapy",name:a,description:r}};return e.jsxs(d,{children:[e.jsx("title",{children:x()}),e.jsx("meta",{name:"description",content:u()}),e.jsx("meta",{name:"keywords",content:(()=>{const e=[`${a.toLowerCase()}`,`cid-10 ${a.toLowerCase()}`,`${a.toLowerCase()} pediatria`,`código ${a.toLowerCase()}`,`classificação ${a.toLowerCase()}`,`busca cid ${a.toLowerCase()}`];return g&&e.push(`${g}`,`código ${g}`,`cid ${g}`,`${g} pediatria`),p&&e.push(`${p.toLowerCase()}`,`cid ${p.toLowerCase()}`,`categoria ${p.toLowerCase()}`),"main"===l&&e.push("cid-10 pediatria","busca cid","códigos cid","classificação doenças","cid pediátrico","busca diagnóstico","códigos médicos"),"category"===l&&e.push("categorias cid","grupos cid","classificação médica","capítulos cid","organização cid"),"specific"===l&&e.push("código específico","diagnóstico cid","doença cid","condição médica","patologia cid"),e.push(...s),o.forEach((a=>{e.push(`${a.toLowerCase()}`)})),m.forEach((a=>{e.push(`${a.toLowerCase()}`)})),e.join(", ")})()}),e.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large, max-snippet:-1"}),e.jsx("meta",{name:"googlebot",content:"index, follow"}),e.jsx("meta",{name:"medical-content",content:"medical-coding"}),e.jsx("meta",{name:"target-audience",content:"healthcare-professionals"}),e.jsx("meta",{name:"content-type",content:"medical-classification"}),e.jsx("meta",{name:"clinical-specialty",content:"pediatrics"}),e.jsx("meta",{name:"coding-system",content:"ICD-10"}),e.jsx("meta",{name:"geo.region",content:"BR"}),e.jsx("meta",{name:"geo.country",content:"Brazil"}),e.jsx("meta",{name:"language",content:"Portuguese"}),e.jsx("meta",{property:"og:title",content:x()}),e.jsx("meta",{property:"og:description",content:u()}),e.jsx("meta",{property:"og:type",content:"website"}),e.jsx("meta",{property:"og:url",content:h}),e.jsx("meta",{property:"og:image",content:"https://pedb.com.br/faviconx.webp"}),e.jsx("meta",{property:"og:image:alt",content:`${a} - PedBook`}),e.jsx("meta",{property:"og:site_name",content:"PedBook"}),e.jsx("meta",{property:"og:locale",content:"pt_BR"}),e.jsx("meta",{property:"article:section",content:"Medicina"}),e.jsx("meta",{property:"article:tag",content:"CID-10"}),e.jsx("meta",{property:"article:tag",content:"Codificação Médica"}),e.jsx("meta",{property:"article:tag",content:l}),m.map(((a,t)=>e.jsx("meta",{property:"article:tag",content:a},t))),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:title",content:x()}),e.jsx("meta",{name:"twitter:description",content:u()}),e.jsx("meta",{name:"twitter:image",content:"https://pedb.com.br/faviconx.webp"}),e.jsx("meta",{name:"twitter:site",content:"@pedbook"}),e.jsx("link",{rel:"canonical",href:h}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(j)}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(y)}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify({"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:[{"@type":"ListItem",position:1,name:"PedBook",item:"https://pedb.com.br"},{"@type":"ListItem",position:2,name:"CID-10",item:"https://pedb.com.br/icd"}]})})]})},f={title:"CID-10",description:"Busca rápida e eficiente de códigos CID-10 para pediatria com classificação internacional de doenças",slug:"main",keywords:["cid-10","códigos cid","classificação doenças","busca cid","diagnóstico médico"],clinicalUse:"Ferramenta completa para busca e consulta de códigos CID-10 específicos para pediatria, facilitando a codificação médica e documentação clínica.",features:["Busca por nome da doença","Busca por código CID","Resultados instantâneos","Interface intuitiva","Base de dados completa"],benefits:["Codificação rápida","Precisão diagnóstica","Documentação adequada","Conformidade regulatória"],clinicalSignificance:"Essencial para codificação médica precisa e documentação clínica adequada em pediatria.",relatedTopics:["classificação internacional doenças","codificação médica","documentação clínica","diagnóstico pediátrico","prontuário médico"],searchType:"main"};function b(){const s=u(),[r,o]=a.useState(""),[c,d]=a.useState(!1),x=t(),b=f,[v,w]=a.useState("");a.useEffect((()=>{const e=setTimeout((()=>{w(r)}),300);return()=>clearTimeout(e)}),[r]),a.useEffect((()=>{(async()=>{await x.prefetchQuery({queryKey:["icd-search",""],queryFn:C,staleTime:432e5,gcTime:864e5})})()}),[x]);const C=async()=>{if(!v||v.length<2)return[];try{const{data:e,error:a}=await p.from("unified_cids").select("code, name, description").or(`name.ilike.%${v}%,code.ilike.%${v}%`).order("code",{ascending:!0}).limit(100);if(a)throw a;return e||[]}catch(e){throw e}},N=v.length>=2,{data:k,isLoading:$}=i({queryKey:["icd-search",v],queryFn:C,enabled:N,staleTime:432e5,gcTime:864e5});return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-white dark:from-slate-900 dark:via-slate-900 dark:to-slate-800",children:[e.jsx(y,{...b}),e.jsx(m,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-12 max-w-4xl",children:e.jsxs("div",{className:"space-y-8 animate-fade-in",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(n,{variant:"ghost",className:"text-primary hover:text-primary/80 transition-colors dark:text-primary-foreground dark:hover:text-primary-foreground/80",onClick:()=>s("/"),children:[e.jsx(l,{className:"h-4 w-4 mr-2"}),"Voltar ao Menu"]})}),e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent dark:from-blue-400 dark:to-blue-300",children:"Busca CID-10"}),e.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Digite abaixo a doença para buscar o seu código CID"})]}),e.jsx(h,{searchTerm:r,setSearchTerm:o,setIsSearching:d,handleSearch:e=>{e.preventDefault(),d(!0)}}),r.length>=2&&e.jsx(j,{results:k||[],isLoading:$||r!==v,searchTerm:r})]})}),e.jsx(g,{})]})}export{b as default};
