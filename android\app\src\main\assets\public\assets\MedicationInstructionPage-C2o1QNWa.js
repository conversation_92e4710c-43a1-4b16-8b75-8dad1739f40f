import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{u as a}from"./query-vendor-B-7l6Nb3.js";import{c as s,u as r,R as i,T as o,s as n,a8 as l,aa as c,B as m,a7 as d,P as p,W as g,Z as u,Y as h,U as b,m as x,ab as f,aj as j}from"./index-CR7o3nEo.js";import y from"./Footer-sDEBI9y7.js";import{A as v,a as $,b as w,c as N}from"./accordion-Bu1COLnt.js";import{A as C,a as k,b as S,c as _,d as I,e as A,f as L,g as P}from"./alert-dialog-DjBGocRG.js";import{H as T}from"./FeedbackTrigger-CHct9Uio.js";import{T as B}from"./thumbs-up-DasQZlP9.js";import{M as E}from"./meh-BkSrc7Ky.js";import{T as q}from"./thumbs-down-Cq9S_yOL.js";import{c as M,a as O}from"./router-BAzpOxbo.js";import{L as F}from"./list-CP8FHcAb.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./instagram-CpsvBnt_.js";import"./rocket-BWCmj4V_.js";import"./target-h-6dvqt2.js";import"./zap-BgS9hP5n.js";import"./book-open-mjVJfd0u.js";import"./star-DyEs3k-S.js";import"./circle-help-BhrzdJqz.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=s("Clipboard",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]]),D=s("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]),R=[{value:"excellent",label:"Excelente",icon:T,color:"text-blue-600",bgColor:"bg-blue-50 hover:bg-blue-100"},{value:"good",label:"Bom",icon:B,color:"text-green-500",bgColor:"bg-green-50 hover:bg-green-100"},{value:"regular",label:"Regular",icon:E,color:"text-yellow-500",bgColor:"bg-yellow-50 hover:bg-yellow-100"},{value:"poor",label:"Ruim",icon:q,color:"text-red-500",bgColor:"bg-red-50 hover:bg-red-100"}];
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function W({medicationId:a,medicationName:s}){const{user:l}=r(),[c,m]=t.useState(null),[d,p]=t.useState(!1),[g,u]=t.useState(""),[h,b]=t.useState(null),[x,f]=t.useState(null);t.useEffect((()=>{(async()=>{if(l)try{const{data:e,error:t}=await n.from("pedbook_medications_feedback").select("rating").eq("medication_id",a).eq("user_id",l.id).maybeSingle();if(t)return;e&&m(e.rating)}catch(e){}})()}),[l,a]);const j=async(e,t)=>{if(l)if(c)f("Você já avaliou esta bula anteriormente.");else try{const{error:r}=await n.from("pedbook_medications_feedback").insert({medication_id:a,medication_name:s,user_id:l.id,rating:e,comment:t});if(r){if("23505"===r.code)return void f("Você já avaliou esta bula anteriormente.");throw r}m(e),f("Feedback enviado com sucesso! Obrigado pela sua avaliação."),setTimeout((()=>f(null)),3e3)}catch(r){f(`Erro ao enviar feedback: ${r.message}`)}else f("Você precisa estar logado para enviar feedback.")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-6 border-t pt-6",children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900 dark:text-gray-100",children:c?"Sua avaliação":"Esta bula foi útil para você?"}),x&&e.jsx("div",{className:"text-sm px-4 py-2 rounded-md "+(x.includes("Erro")?"bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400":"bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400"),children:x})]}),e.jsx("div",{className:"mt-4 flex justify-center gap-3",children:R.map((t=>{const a=c===t.value;return e.jsxs(i,{className:`flex flex-col items-center justify-center p-2 cursor-${c?"default":"pointer"} transition-all \n                  ${a?`${t.bgColor} ring-2 ring-${t.color}`:"bg-gray-50 dark:bg-gray-800"}\n                  ${c?"":`${t.bgColor} hover:ring-2 hover:ring-${t.color}`}\n                  w-16 h-16`,onClick:()=>{return!c&&void("excellent"!==(e=t.value)?(b(e),p(!0)):j(e));var e},children:[e.jsx(t.icon,{className:`w-6 h-6 ${a?t.color:"text-gray-400"}`}),e.jsx("span",{className:"mt-1 text-xs font-medium "+(a?"text-gray-900 dark:text-gray-100":"text-gray-500 dark:text-gray-400"),children:t.label})]},t.value)}))})]}),e.jsx(C,{open:d,onOpenChange:p,children:e.jsxs(k,{children:[e.jsxs(S,{children:[e.jsx(_,{children:"Deixe seu comentário"}),e.jsx(I,{children:"Sua opinião é muito importante para melhorarmos nosso conteúdo. Por favor, compartilhe suas sugestões ou críticas construtivas."})]}),e.jsx("div",{className:"my-4",children:e.jsx(o,{value:g,onChange:e=>u(e.target.value),placeholder:"Digite seu comentário aqui...",className:"min-h-[100px]"})}),e.jsxs(A,{children:[e.jsx(L,{onClick:()=>{p(!1),h&&j(h)},children:"Cancelar"}),e.jsx(P,{onClick:()=>{h&&g.trim()&&(j(h,g.trim()),p(!1),u(""),b(null))},children:"Enviar comentário"})]})]})})]})}const z=({medicationName:t,medicationSlug:a,description:s,brands:r,category:i,hasInstructions:o,instructionContent:n})=>{const c=(e=>{if(!e)return{};const t={},a=[/<strong>Nome Genérico \(DCB\):<\/strong>\s*([^<\n\r]+)/i,/<strong>Nome genérico:<\/strong>\s*([^<\n\r]+)/i,/Nome genérico:\s*([^<\n\r]+)/i];for(const d of a){const a=e.match(d);if(a){t.dcb=a[1].trim();break}}const s=[/<strong>Nome Internacional \(DCI\):<\/strong>\s*([^<\n\r]+)/i,/<strong>Nome internacional:<\/strong>\s*([^<\n\r]+)/i,/Nome internacional:\s*([^<\n\r]+)/i];for(const d of s){const a=e.match(d);if(a){t.dci=a[1].trim();break}}const r=[/<strong>Classe Terapêutica:<\/strong>\s*([^<\n\r]+)/i,/<strong>Classe terapêutica:<\/strong>\s*([^<\n\r]+)/i,/Classe terapêutica:\s*([^<\n\r]+)/i,/<strong>Classificação:<\/strong>\s*([^<\n\r]+)/i];for(const d of r){const a=e.match(d);if(a){t.therapeuticClass=a[1].trim();break}}const i=[/<strong>Código ATC:<\/strong>\s*([^<\n\r]+)/i,/<strong>ATC:<\/strong>\s*([^<\n\r]+)/i,/Código ATC:\s*([^<\n\r]+)/i];for(const d of i){const a=e.match(d);if(a){t.atcCode=a[1].trim();break}}const o=[/<strong>Nomes comerciais:<\/strong>\s*([^<\n\r]+)/i,/<strong>Nome comercial:<\/strong>\s*([^<\n\r]+)/i,/Nome comercial:\s*([^<\n\r]+)/i,/Nomes comerciais:\s*([^<\n\r]+)/i];for(const d of o){const a=e.match(d);if(a){t.commercialNames=a[1].trim();break}}const n=[/Indicações:<\/strong>\s*([^<]*(?:<[^>]*>[^<]*)*)/i,/Indicações Clínicas<\/p><p>([^<]*(?:<[^>]*>[^<]*)*)/i,/Indicado para:\s*([^<]*(?:<[^>]*>[^<]*)*)/i];for(const d of n){const a=e.match(d);if(a){const e=a[1].replace(/<[^>]*>/g,"").trim();t.indications=e.substring(0,200);break}}const l=[/Forma farmacêutica:<\/strong>([\s\S]*?)(?=<\/li>|<strong>|$)/i,/Apresentações disponíveis:<\/strong>([\s\S]*?)(?=<\/li>|<strong>|$)/i,/Apresentações Comerciais<\/p>([\s\S]*?)(?=<p>##\.|$)/i];for(const d of l){const a=e.match(d);if(a){const e=a[1].replace(/<[^>]*>/g,"").trim();t.presentations=e.substring(0,150);break}}const c=[/Contraindicações:<\/strong>([\s\S]*?)(?=<\/li>|<strong>|$)/i,/Contraindicações<\/p>([\s\S]*?)(?=<p>##\.|$)/i];for(const d of c){const a=e.match(d);if(a){const e=a[1].replace(/<[^>]*>/g,"").trim();t.contraindications=e.substring(0,150);break}}const m=e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim();return t.contentSummary=m.substring(0,300),t})(n||""),m=(()=>{let e=`Bula ${t}`;return c.atcCode&&(e+=` (${c.atcCode})`),c.therapeuticClass?e+=` | ${c.therapeuticClass}`:i&&(e+=` | ${i}`),e+=" - Posologia Pediátrica",e.substring(0,60)})(),d=()=>{let e=`Bula profissional ${t}`;(c.dcb||c.dci)&&(e+=` (${c.dcb||c.dci})`),e+=" para pediatria. ",c.therapeuticClass?e+=`${c.therapeuticClass}. `:i&&(e+=`${i}. `),c.atcCode&&(e+=`ATC: ${c.atcCode}. `),c.indications?e+=`Indicado para ${c.indications.replace(/[✅⚠️]/g,"").replace(/Tratamento de/i,"").trim().substring(0,60)}... `:c.contentSummary&&(e+=`${c.contentSummary.replace(/Aba:|Geral|Pediátrico|Adulto/g,"").trim().substring(0,50)}... `),e+="Posologia, contraindicações e orientações para profissionais da saúde.";const a=c.commercialNames||r;if(a){const t=a.split(/[,;]/).slice(0,2).map((e=>e.trim().replace(/®/g,"").replace(/\([^)]*\)/g,""))).filter((e=>e.length>0));t.length>0&&(e+=` Nomes comerciais: ${t.join(", ")}.`)}return e.substring(0,160)},p=`https://pedb.com.br/bulas-profissionais/${a}`,g={"@context":"https://schema.org","@type":"Drug",name:t,description:d(),url:p,manufacturer:{"@type":"Organization",name:"Diversos fabricantes"},activeIngredient:c.dcb||c.dci||t,dosageForm:c.presentations||"Conforme prescrição médica",administrationRoute:"Conforme indicação médica",targetPopulation:"Pacientes pediátricos",prescriptionStatus:"PrescriptionOnly",availableStrength:c.presentations||"Diversas concentrações",clinicalPharmacology:c.mechanism||`Informações clínicas do ${t}`,indication:c.indications||"Uso pediátrico conforme prescrição médica",contraindication:c.contraindications||"Conforme bula profissional",dosage:c.dosage||"Conforme peso e idade do paciente",overdose:"Procurar atendimento médico imediato",pregnancyCategory:"Consultar orientações específicas",breastfeedingWarning:"Consultar orientações para amamentação",...c.atcCode&&{code:c.atcCode},...c.therapeuticClass&&{drugClass:c.therapeuticClass},...c.commercialNames&&{tradeName:c.commercialNames.split(",").slice(0,5).map((e=>e.trim()))}},u={"@context":"https://schema.org","@type":"MedicalWebPage",name:m,description:d(),url:p,mainContentOfPage:{"@type":"WebPageElement",cssSelector:"main"},specialty:"Pediatria",audience:{"@type":"MedicalAudience",audienceType:"Médicos pediatras e profissionais da saúde"},about:{"@type":"Drug",name:t},lastReviewed:(new Date).toISOString().split("T")[0],reviewedBy:{"@type":"Organization",name:"PedBook",url:"https://pedb.com.br"}},h={"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:[{"@type":"ListItem",position:1,name:"PedBook",item:"https://pedb.com.br"},{"@type":"ListItem",position:2,name:"Bulas Profissionais",item:"https://pedb.com.br/bulas-profissionais"},{"@type":"ListItem",position:3,name:t,item:p}]};return e.jsxs(l,{children:[e.jsx("title",{children:m}),e.jsx("meta",{name:"description",content:d()}),e.jsx("meta",{name:"keywords",content:(()=>{const e=[`bula ${t.toLowerCase()}`,`${t.toLowerCase()} pediátrico`,`posologia ${t.toLowerCase()}`,`${t.toLowerCase()} pediatria`,"bula profissional","medicamento pediátrico","posologia pediátrica","indicações pediátricas","contraindicações pediatria"];c.dcb&&(e.push(`dcb ${c.dcb}`),e.push(`${c.dcb} pediátrico`)),c.dci&&(e.push(`dci ${c.dci.toLowerCase()}`),e.push(`${c.dci.toLowerCase()} pediatria`)),c.atcCode&&(e.push(`atc ${c.atcCode.toLowerCase()}`),e.push(`código atc ${c.atcCode.toLowerCase()}`)),c.therapeuticClass?(e.push(`${c.therapeuticClass.toLowerCase()}`),e.push(`${c.therapeuticClass.toLowerCase()} pediátrico`)):i&&e.push(`${i.toLowerCase()} pediátrico`);const a=c.commercialNames||r;return a&&a.split(",").slice(0,8).forEach((t=>{const a=t.trim().replace(/®/g,"").toLowerCase();e.push(`bula ${a}`),e.push(`${a} pediátrico`)})),e.join(", ")})()}),e.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large, max-snippet:-1"}),e.jsx("meta",{name:"googlebot",content:"index, follow"}),e.jsx("meta",{name:"medical-content",content:"professional"}),e.jsx("meta",{name:"target-audience",content:"healthcare-professionals"}),e.jsx("meta",{name:"content-type",content:"drug-information"}),e.jsx("meta",{name:"geo.region",content:"BR"}),e.jsx("meta",{name:"geo.country",content:"Brazil"}),e.jsx("meta",{name:"language",content:"Portuguese"}),e.jsx("meta",{property:"og:title",content:m}),e.jsx("meta",{property:"og:description",content:d()}),e.jsx("meta",{property:"og:type",content:"article"}),e.jsx("meta",{property:"og:url",content:p}),e.jsx("meta",{property:"og:image",content:"https://pedb.com.br/faviconx.webp"}),e.jsx("meta",{property:"og:image:alt",content:`Bula ${t} - PedBook`}),e.jsx("meta",{property:"og:site_name",content:"PedBook"}),e.jsx("meta",{property:"og:locale",content:"pt_BR"}),e.jsx("meta",{property:"article:section",content:"Medicina"}),e.jsx("meta",{property:"article:tag",content:"Pediatria"}),e.jsx("meta",{property:"article:tag",content:"Medicamentos"}),e.jsx("meta",{property:"article:tag",content:"Bulas"}),i&&e.jsx("meta",{property:"article:tag",content:i}),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:title",content:m}),e.jsx("meta",{name:"twitter:description",content:d()}),e.jsx("meta",{name:"twitter:image",content:"https://pedb.com.br/faviconx.webp"}),e.jsx("meta",{name:"twitter:site",content:"@pedbook"}),e.jsx("link",{rel:"canonical",href:p}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(g)}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(u)}),e.jsx("script",{type:"application/ld+json",children:JSON.stringify(h)}),e.jsx("link",{rel:"preconnect",href:"https://bxedpdmgvgatjdfxgxij.supabase.co"}),e.jsx("link",{rel:"dns-prefetch",href:"https://bxedpdmgvgatjdfxgxij.supabase.co"})]})},G=()=>{const{slug:s}=M(),r=O(),{data:o,isLoading:l,error:C}=a({queryKey:["medication-instruction-details",s],queryFn:async()=>{if(!s)return null;const{data:e,error:t}=await n.from("pedbook_medications").select("\n          id,\n          name,\n          description,\n          brands,\n          slug,\n          pedbook_medication_categories(name)\n        ").eq("slug",s).maybeSingle();if(t){if("PGRST116"===t.code)return null;throw t}return e},enabled:!!s}),{data:k,isLoading:S,error:_}=a({queryKey:["medication-instructions-detail",o?.id],queryFn:async()=>{if(!o?.id)return null;const{data:e,error:t}=await n.from("pedbook_medication_instructions").select("*").eq("medication_id",o.id).eq("is_published",!0).maybeSingle();if(t){if("PGRST116"===t.code)return null;throw t}return e},enabled:!!o?.id}),I="simple"===k?.format_type?"simple":"standard";t.useEffect((()=>(window.handleImageClick=e=>{window.open(e,"_blank")},()=>{delete window.handleImageClick})),[]);const A=e=>{const t=document.createElement("textarea");return t.innerHTML=e,t.value},L=e=>e?"simple"===I?(e=>{if(!e)return"";let t=e.replace(/(<[^>]*>)*##\.\s*(.*?)(<\/[^>]*>)*/g,((e,t,a)=>`<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${a.replace(/<\/?[^>]+(>|$)/g,"")}</h2>`));return t=t.replace(/##\.\s*[^<\n\r]*(?:<br>|<\/p>|$)/g,""),t=t.replace(/(<[^>]*>)*##;\s*(.*?)(<\/[^>]*>)*/g,((e,t,a)=>`<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">${a.replace(/<\/?[^>]+(>|$)/g,"")}</h4>`)),t=t.replace(/##;\s*[^<\n\r]*(?:<br>|<\/p>|$)/g,""),Array.from(t.matchAll(/<ul>([\s\S]*?)<\/ul>/g)).forEach(((e,a)=>{const s=e[0],r=e[1];let i='<ul class="list-disc pl-5 mb-2 space-y-1">';Array.from(r.matchAll(/<li>([\s\S]*?)<\/li>/g)).forEach(((e,t)=>{let a=e[1].trim();a.startsWith("<p>")&&a.endsWith("</p>")&&(a=a.substring(3,a.length-4)),i+=`<li class="mb-1">${a}</li>`})),i+="</ul>",t=t.replace(s,i)})),t=t.replace(/<br>$/g,""),t=t.replace(/<ul><li><p>(.*?)<\/p><ul>/g,"<ul><li>$1<ul>"),t=t.replace(/<\/li><\/ul><\/li><li>/g,"</li></ul></li><li>"),t=t.replace(/<li><ul><li><p>(.*?)<\/p>/g,"<li><ul><li>$1"),t=t.replace(/<li><p>(.*?)<\/p><\/li>/g,"<li>$1</li>"),t=t.replace(/<\/li><\/ul><p>\s*##;/g,'</li></ul><p class="mt-4">##;'),t=t.replace(/<\/li><p><\/p>/g,"</li>"),t=t.replace(/<\/ul><p><\/p>/g,"</ul>"),t=t.replace(/<p>\s*<\/p>/g,""),t=t.replace(/<li>\s*<\/li>/g,""),t=t.replace(/<li class="mb-1">\s*<br>\s*/g,'<li class="mb-1">'),t=t.replace(/<li>\s*<br>\s*/g,"<li>"),t=t.replace(/<li class="mb-1"><p>\s*(.*?)\s*<\/p><\/li>/g,'<li class="mb-1">$1</li>'),t=t.replace(/<li><p>\s*(.*?)\s*<\/p><\/li>/g,"<li>$1</li>"),t=t.replace(/<\/li><li><p>Infecção secundária<\/p>/g,"</li><li>Infecção secundária"),t=t.replace(/<li class="mb-1">(.*?)<br>/g,'<li class="mb-1">$1 '),t=t.replace(/<li>(.*?)<br>/g,"<li>$1 "),t=t.replace(/<p>\s*##;/g,"<p>##;"),t=t.replace(/<li class="mb-1"><\/li>\s*([^<]+)/g,'<li class="mb-1">$1</li>'),t=t.replace(/<li><\/li>\s*([^<]+)/g,"<li>$1</li>"),t=t.replace(/<\/ul><p>\s+/g,"</ul><p>"),t=t.replace(/<li class="mb-1"><\/li>/g,""),t=t.replace(/<li><\/li>/g,""),t=t.replace(/<\/ul><p>\s*##;\s*Sistêmicas\s*\(raras\):/g,'</ul><p class="mt-4">##; Sistêmicas (raras):'),t=t.replace(/<\/li>\s*<br>\s*<li/g,"</li><li"),t=t.replace(/<li class="mb-1">\s+/g,'<li class="mb-1">'),t=t.replace(/<li>\s+/g,"<li>"),t=t.replace(/<li([^>]*)>\s+/g,"<li$1>"),t=t.replace(/<\/ul><p>\s*##;/g,'</ul><p class="mt-4">##;'),t=t.replace(/<\/ul><p>\s+/g,"</ul><p>"),t=t.replace(/<li([^>]*)>\s+/g,"<li$1>"),t=t.replace(/<li class="mb-1">\s+/g,'<li class="mb-1">'),t=t.replace(/<li>\s+/g,"<li>"),t=t.replace(/\s+<\/li>/g,"</li>"),t=t.replace(/<li([^>]*)>(.*?)<br>(.*?)<\/li>/g,"<li$1>$2 $3</li>"),t=t.replace(/<\/ul><p>\s*##;\s*Sistêmicas\s*\(raras\):/g,'</ul><p class="mt-4">##; Sistêmicas (raras):'),t=t.replace(/<p>\s*##;/g,"<p>##;"),t=t.replace(/##;\s+/g,"##; "),t=t.replace(/##;\s+([A-Za-zÀ-ÖØ-öø-ÿ])/g,"##; $1"),t=t.replace(/<ul[^>]*>\s*<li/g,"<ul><li"),t=t.replace(/<\/li>\s+<li/g,"</li><li"),t=t.replace(/<\/ul>\s+<p/g,"</ul><p"),t=t.replace(/<li([^>]*)>([\s]*)(.*?)<\/li>/g,((e,t,a,s)=>{let r=s.trim();return r.startsWith("<p>")&&r.endsWith("</p>")&&(r=r.substring(3,r.length-4)),`<li${t}>${r}</li>`})),t=t.replace(/<h4 class="[^"]*">\s+/g,'<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">'),t=t.replace(/<h4([^>]*)>([\s]*)(.*?)<\/h4>/g,((e,t,a,s)=>`<h4${t}>${s.trim()}</h4>`)),t=A(t.replace(/<strong>(.*?)<\/strong>/g,'<span class="font-semibold">$1</span>').replace(/<p>/g,'<p class="mb-2">').replace(/<li><p>/g,'<li class="mb-1">').replace(/<a([^>]*)target="_blank"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g,'<a$1href="$2"$3 target="_self">').replace(/<img([^>]*)>/g,'<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')),t})(e):(e=>e?A(e.replace(/<h2><strong>##\.\s*(.*?)<\/strong><\/h2>/g,'<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">$1</h2>').replace(/<h3><strong>##\.\s*(.*?)<\/strong><\/h3>/g,'<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-3 mb-1">$1</h3>').replace(/<strong>##;\s*(.*?)<\/strong>/g,'<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>').replace(/<h4><strong>(.*?)<\/strong><\/h4>/g,'<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>').replace(/<strong>(.*?)<\/strong>/g,'<span class="font-semibold">$1</span>').replace(/<p>/g,'<p class="mb-2">').replace(/<ul>/g,'<ul class="list-disc pl-5 mb-2 space-y-1">').replace(/<li><p>/g,'<li class="mb-1">').replace(/<a([^>]*)target="_blank"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g,"<a$1$2>").replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g,'<a$1href="$2"$3 target="_self">').replace(/<img([^>]*)>/g,'<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')):"")(e):"",P=l||S,T=C||_,B={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}},E=t=>{const a=t.toLowerCase();return a.includes("indicaç")?e.jsx(F,{className:"h-5 w-5"}):a.includes("posolog")?e.jsx(V,{className:"h-5 w-5"}):a.includes("apresenta")?e.jsx(D,{className:"h-5 w-5"}):a.includes("composiç")?e.jsx(p,{className:"h-5 w-5"}):a.includes("contra")?e.jsx(h,{className:"h-5 w-5"}):a.includes("precau")?e.jsx(j,{className:"h-5 w-5"}):e.jsx(f,{className:"h-5 w-5"})};return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800",children:[o&&e.jsx(z,{medicationName:o.name,medicationSlug:o.slug,description:o.description,brands:o.brands,category:o.pedbook_medication_categories?.name,hasInstructions:!!k?.content,instructionContent:k?.content}),e.jsx(c,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-5xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-6",children:[e.jsxs(m,{variant:"ghost",onClick:()=>r("/bulas-profissionais"),className:"text-primary hover:bg-primary/5 -ml-2 flex items-center gap-2 justify-start",children:[e.jsx(d,{className:"h-4 w-4"}),"Voltar para lista de bulas"]}),o?.slug&&e.jsxs(m,{variant:"outline",size:"sm",onClick:()=>r(`/medicamentos/${o.slug}`),className:"gap-2 border-primary/20 bg-white dark:bg-slate-700 shadow-sm hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-white flex items-center justify-center sm:justify-start",children:[e.jsx(p,{className:"h-4 w-4 text-primary"}),e.jsx("span",{className:"whitespace-nowrap",children:"Ver Medicamento"})]})]}),P?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-10 w-2/3 bg-slate-200 animate-pulse rounded-lg"}),e.jsx("div",{className:"h-6 w-1/2 bg-slate-200 animate-pulse rounded-lg"}),e.jsx("div",{className:"h-6 w-1/3 bg-slate-200 animate-pulse rounded-lg"})]}):T?e.jsxs(i,{className:"border-destructive/50 bg-destructive/5 shadow-lg",children:[e.jsx(g,{className:"pb-2",children:e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5 text-destructive"}),"Erro ao carregar bula"]})}),e.jsxs(b,{children:[e.jsx("p",{children:"Não foi possível carregar as informações desta bula. Tente novamente mais tarde."}),e.jsx(m,{variant:"outline",className:"mt-4",onClick:()=>window.location.reload(),children:"Tentar novamente"})]})]}):e.jsxs(e.Fragment,{children:[e.jsx(x.div,{className:"mb-8 text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:o?.name&&o.name.length<20?e.jsxs("div",{className:"flex justify-center items-center flex-wrap gap-2 mb-2",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100",children:o?.name}),o?.pedbook_medication_categories?.name&&e.jsxs("span",{className:"bg-primary/5 border border-primary/10 px-3 py-1 rounded-lg text-sm text-primary",children:["Categoria: ",o.pedbook_medication_categories.name]})]}):e.jsxs(e.Fragment,{children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-3",children:o?.name}),o?.pedbook_medication_categories?.name&&e.jsx("div",{className:"inline-block bg-primary/5 border border-primary/10 px-4 py-2 rounded-lg",children:e.jsxs("p",{className:"text-sm text-primary",children:["Categoria: ",o.pedbook_medication_categories.name]})})]})}),k?.content?e.jsxs(x.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.2}}},initial:"hidden",animate:"visible",className:"space-y-8",children:[e.jsx(x.div,{variants:B,className:"mt-8",children:e.jsx(v,{type:"multiple",className:"space-y-4",children:(e=>{if(!e)return[];const t=[];if("simple"!==I){const a=/(?:<h[2-4]><strong>##\.\s*(.*?)<\/strong><\/h[2-4]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>)([\s\S]*?)(?=<h[2-4]><strong>##\.|<h2>##\.|$)/g,s=Array.from(e.matchAll(a));if(0===s.length)return[{title:"Informações Gerais",content:e,subsections:[]}];s.forEach((e=>{const a=(e[1]||e[2]||e[3]||"").trim();let s=e[4]||"";const r=[],i=Array.from(s.matchAll(/<strong>##;\s*(.*?)<\/strong>([\s\S]*?)(?=<strong>##;|<h[2-4]><strong>##\.|$)/g));if(i.length>0){const e=s.indexOf(i[0][0]);-1!==e&&(s=s.substring(0,e).trim()),i.forEach((e=>{const t=e[1].trim(),a=e[2].trim();r.push({title:t,content:a})}))}t.push({title:a,content:s,subsections:r})}))}else{const a=/##\.\s*([^<\n\r]*?)(?:<br>|<\/p>|<p>|$)([\s\S]*?)(?=##\.|$)/g,s=Array.from(e.matchAll(a));if(0===s.length)return[{title:"Informações Gerais",content:e,subsections:[]}];s.forEach(((e,a)=>{const s=e[1].trim();let r=e[2]||"";const i=[],o=Array.from(r.matchAll(/##;\s*([^<\n\r]*?)(?:<br>|<\/p>|<p>|$)([\s\S]*?)(?=##;|##\.|$)/g));if(o.length>0){const e=r.indexOf(o[0][0]);-1!==e&&(r=r.substring(0,e).trim()),o.forEach((e=>{const t=e[1].trim(),a=e[2].trim();i.push({title:t,content:a})}))}t.push({title:s,content:r,subsections:i})}))}return 0===t.length?[{title:"Informações Gerais",content:e,subsections:[]}]:t})(k.content).map(((t,a)=>e.jsxs($,{value:`section-${a}`,className:"rounded-xl overflow-hidden border border-blue-100 bg-white/80 backdrop-blur-sm shadow-md transition-all hover:shadow-lg",children:[e.jsx(w,{className:"px-6 py-4 hover:bg-blue-50/50 data-[state=open]:bg-blue-50/80 transition-colors duration-300",children:e.jsxs("div",{className:"flex items-center gap-3 text-left",children:[e.jsx("span",{className:"flex items-center justify-center rounded-full bg-primary text-white p-2",children:E(t.title)}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:t.title}),t.subsections.length>0&&e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[t.subsections.length," subtópicos"]})]})]})}),e.jsxs(N,{className:"px-6 pt-3 pb-6 bg-white",children:[t.content?e.jsx("div",{className:"prose prose-blue prose-headings:text-primary prose-headings:font-semibold max-w-none mb-6",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:L(t.content)}})}):null,t.subsections.length>0&&e.jsx("div",{className:"space-y-4 mt-5",children:t.subsections.map(((t,s)=>e.jsxs("div",{className:"border border-blue-100 rounded-lg bg-blue-50/30 p-4",children:[e.jsxs("h3",{className:"text-md font-semibold text-primary mb-3 pb-2 border-b border-blue-100 flex items-center gap-2",children:[e.jsxs("span",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs",children:[a+1,".",s+1]}),t.title]}),e.jsx("div",{className:"prose prose-sm prose-blue max-w-none",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:L(t.content)}})})]},s)))})]})]},a)))})}),e.jsx(x.div,{variants:B,className:"mt-4",children:o?.id&&e.jsx(W,{medicationId:o.id,medicationName:o.name})})]}):e.jsxs("div",{className:"text-center p-10 border rounded-lg bg-white shadow-sm",children:[e.jsx(f,{className:"h-16 w-16 mx-auto mb-4 text-muted-foreground"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Bula não encontrada"}),e.jsx("p",{className:"text-muted-foreground max-w-md mx-auto mb-6",children:"Não encontramos a bula profissional para este medicamento no momento."}),e.jsx(m,{variant:"outline",onClick:()=>r("/bulas-profissionais"),children:"Voltar para lista de bulas"})]})]})]})}),e.jsx(y,{})]})};export{G as default};
