import{r as e,a as t}from"./critical-DVX9Inzy.js";import{j as n,a as r,P as o,c as i,u as s,b as a,d as c,e as l,f as u,g as d,h as f,i as p,k as m,l as h,m as g,R as w,S as v,F as y,n as x}from"./radix-core-6kBL75b5.js";import{c as b}from"./radix-toast-1_gbKn9f.js";function R(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var D=e.createContext(void 0);function C(t){const n=e.useContext(D);return t||n||"ltr"}var _="rovingFocusGroup.onEntryFocus",M={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[P,O,S]=b(E),[A,j]=function(t,r=[]){let o=[];const i=()=>{const n=o.map((t=>e.createContext(t)));return function(r){const o=r?.[t]||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return i.scopeName=t,[function(r,i){const s=e.createContext(i),a=o.length;function c(r){const{scope:o,children:i,...c}=r,l=o?.[t][a]||s,u=e.useMemo((()=>c),Object.values(c));return n.jsx(l.Provider,{value:u,children:i})}return o=[...o,i],c.displayName=r+"Provider",[c,function(n,o){const c=o?.[t][a]||s,l=e.useContext(c);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${n}\` must be used within \`${r}\``)}]},R(i,...r)]}(E,[S]),[T,k]=A(E),L=e.forwardRef(((e,t)=>n.jsx(P.Provider,{scope:e.__scopeRovingFocusGroup,children:n.jsx(P.Slot,{scope:e.__scopeRovingFocusGroup,children:n.jsx(F,{...e,ref:t})})})));L.displayName=E;var F=e.forwardRef(((t,r)=>{const{__scopeRovingFocusGroup:l,orientation:u,loop:d=!1,dir:f,currentTabStopId:p,defaultCurrentTabStopId:m,onCurrentTabStopIdChange:h,onEntryFocus:g,preventScrollOnEntryFocus:w=!1,...v}=t,y=e.useRef(null),x=s(r,y),b=C(f),[R=null,D]=a({prop:p,defaultProp:m,onChange:h}),[E,P]=e.useState(!1),S=c(g),A=O(l),j=e.useRef(!1),[k,L]=e.useState(0);return e.useEffect((()=>{const e=y.current;if(e)return e.addEventListener(_,S),()=>e.removeEventListener(_,S)}),[S]),n.jsx(T,{scope:l,orientation:u,dir:b,loop:d,currentTabStopId:R,onItemFocus:e.useCallback((e=>D(e)),[D]),onItemShiftTab:e.useCallback((()=>P(!0)),[]),onFocusableItemAdd:e.useCallback((()=>L((e=>e+1))),[]),onFocusableItemRemove:e.useCallback((()=>L((e=>e-1))),[]),children:n.jsx(o.div,{tabIndex:E||0===k?-1:0,"data-orientation":u,...v,ref:x,style:{outline:"none",...t.style},onMouseDown:i(t.onMouseDown,(()=>{j.current=!0})),onFocus:i(t.onFocus,(e=>{const t=!j.current;if(e.target===e.currentTarget&&t&&!E){const t=new CustomEvent(_,M);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=A().filter((e=>e.focusable));B([e.find((e=>e.active)),e.find((e=>e.id===R)),...e].filter(Boolean).map((e=>e.ref.current)),w)}}j.current=!1})),onBlur:i(t.onBlur,(()=>P(!1)))})})})),I="RovingFocusGroupItem",N=e.forwardRef(((t,s)=>{const{__scopeRovingFocusGroup:a,focusable:c=!0,active:l=!1,tabStopId:u,...d}=t,f=r(),p=u||f,m=k(I,a),h=m.currentTabStopId===p,g=O(a),{onFocusableItemAdd:w,onFocusableItemRemove:v}=m;return e.useEffect((()=>{if(c)return w(),()=>v()}),[c,w,v]),n.jsx(P.ItemSlot,{scope:a,id:p,focusable:c,active:l,children:n.jsx(o.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...d,ref:s,onMouseDown:i(t.onMouseDown,(e=>{c?m.onItemFocus(p):e.preventDefault()})),onFocus:i(t.onFocus,(()=>m.onItemFocus(p))),onKeyDown:i(t.onKeyDown,(e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:K[r]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter((e=>e.focusable)).map((e=>e.ref.current));if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const i=o.indexOf(e.currentTarget);o=m.loop?(r=i+1,(n=o).map(((e,t)=>n[(r+t)%n.length]))):o.slice(i+1)}setTimeout((()=>B(o)))}var n,r}))})})}));N.displayName=I;var K={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function B(e,t=!1){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus({preventScroll:t}),document.activeElement!==n)return}}var H=L,W=N;function $(t){const[n,r]=e.useState(void 0);return l((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}const z=["top","right","bottom","left"],G=Math.min,V=Math.max,U=Math.round,X=Math.floor,Y=e=>({x:e,y:e}),q={left:"right",right:"left",bottom:"top",top:"bottom"},Z={start:"end",end:"start"};function J(e,t,n){return V(e,G(t,n))}function Q(e,t){return"function"==typeof e?e(t):e}function ee(e){return e.split("-")[0]}function te(e){return e.split("-")[1]}function ne(e){return"x"===e?"y":"x"}function re(e){return"y"===e?"height":"width"}function oe(e){return["top","bottom"].includes(ee(e))?"y":"x"}function ie(e){return ne(oe(e))}function se(e){return e.replace(/start|end/g,(e=>Z[e]))}function ae(e){return e.replace(/left|right|bottom|top/g,(e=>q[e]))}function ce(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function le(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ue(e,t,n){let{reference:r,floating:o}=e;const i=oe(t),s=ie(t),a=re(s),c=ee(t),l="y"===i,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[a]/2-o[a]/2;let p;switch(c){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(te(t)){case"start":p[s]-=f*(n&&l?-1:1);break;case"end":p[s]+=f*(n&&l?-1:1)}return p}async function de(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Q(t,e),m=ce(p),h=a[f?"floating"===d?"reference":"floating":d],g=le(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:l,rootBoundary:u,strategy:c})),w="floating"===d?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,v=await(null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await(null==i.isElement?void 0:i.isElement(v))&&await(null==i.getScale?void 0:i.getScale(v))||{x:1,y:1},x=le(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:v,strategy:c}):w);return{top:(g.top-x.top+m.top)/y.y,bottom:(x.bottom-g.bottom+m.bottom)/y.y,left:(g.left-x.left+m.left)/y.x,right:(x.right-g.right+m.right)/y.x}}function fe(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function pe(e){return z.some((t=>e[t]>=0))}function me(){return"undefined"!=typeof window}function he(e){return ve(e)?(e.nodeName||"").toLowerCase():"#document"}function ge(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function we(e){var t;return null==(t=(ve(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ve(e){return!!me()&&(e instanceof Node||e instanceof ge(e).Node)}function ye(e){return!!me()&&(e instanceof Element||e instanceof ge(e).Element)}function xe(e){return!!me()&&(e instanceof HTMLElement||e instanceof ge(e).HTMLElement)}function be(e){return!(!me()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ge(e).ShadowRoot)}function Re(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Pe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function De(e){return["table","td","th"].includes(he(e))}function Ce(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function _e(e){const t=Me(),n=ye(e)?Pe(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Me(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Ee(e){return["html","body","#document"].includes(he(e))}function Pe(e){return ge(e).getComputedStyle(e)}function Oe(e){return ye(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Se(e){if("html"===he(e))return e;const t=e.assignedSlot||e.parentNode||be(e)&&e.host||we(e);return be(t)?t.host:t}function Ae(e){const t=Se(e);return Ee(t)?e.ownerDocument?e.ownerDocument.body:e.body:xe(t)&&Re(t)?t:Ae(t)}function je(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Ae(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),s=ge(o);if(i){const e=Te(s);return t.concat(s,s.visualViewport||[],Re(o)?o:[],e&&n?je(e):[])}return t.concat(o,je(o,[],n))}function Te(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ke(e){const t=Pe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=xe(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=U(n)!==i||U(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function Le(e){return ye(e)?e:e.contextElement}function Fe(e){const t=Le(e);if(!xe(t))return Y(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ke(t);let s=(i?U(n.width):n.width)/r,a=(i?U(n.height):n.height)/o;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}const Ie=Y(0);function Ne(e){const t=ge(e);return Me()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Ie}function Ke(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=Le(e);let s=Y(1);t&&(r?ye(r)&&(s=Fe(r)):s=Fe(e));const a=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ge(e))&&t}(i,n,r)?Ne(i):Y(0);let c=(o.left+a.x)/s.x,l=(o.top+a.y)/s.y,u=o.width/s.x,d=o.height/s.y;if(i){const e=ge(i),t=r&&ye(r)?ge(r):r;let n=e,o=Te(n);for(;o&&r&&t!==n;){const e=Fe(o),t=o.getBoundingClientRect(),r=Pe(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,l*=e.y,u*=e.x,d*=e.y,c+=i,l+=s,n=ge(o),o=Te(n)}}return le({width:u,height:d,x:c,y:l})}function Be(e,t){const n=Oe(e).scrollLeft;return t?t.left+n:Ke(we(e)).left+n}function He(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ge(e),r=we(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,c=0;if(o){i=o.width,s=o.height;const e=Me();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:s,x:a,y:c}}(e,n);else if("document"===t)r=function(e){const t=we(e),n=Oe(e),r=e.ownerDocument.body,o=V(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=V(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Be(e);const a=-n.scrollTop;return"rtl"===Pe(r).direction&&(s+=V(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}(we(e));else if(ye(t))r=function(e,t){const n=Ke(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=xe(e)?Fe(e):Y(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=Ne(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return le(r)}function We(e,t){const n=Se(e);return!(n===t||!ye(n)||Ee(n))&&("fixed"===Pe(n).position||We(n,t))}function $e(e,t,n){const r=xe(t),o=we(t),i="fixed"===n,s=Ke(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const c=Y(0);if(r||!r&&!i)if(("body"!==he(t)||Re(o))&&(a=Oe(t)),r){const e=Ke(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=Be(o));let l=0,u=0;if(o&&!r&&!i){const e=o.getBoundingClientRect();u=e.top+a.scrollTop,l=e.left+a.scrollLeft-Be(o,e)}return{x:s.left+a.scrollLeft-c.x-l,y:s.top+a.scrollTop-c.y-u,width:s.width,height:s.height}}function ze(e){return"static"===Pe(e).position}function Ge(e,t){if(!xe(e)||"fixed"===Pe(e).position)return null;if(t)return t(e);let n=e.offsetParent;return we(e)===n&&(n=n.ownerDocument.body),n}function Ve(e,t){const n=ge(e);if(Ce(e))return n;if(!xe(e)){let t=Se(e);for(;t&&!Ee(t);){if(ye(t)&&!ze(t))return t;t=Se(t)}return n}let r=Ge(e,t);for(;r&&De(r)&&ze(r);)r=Ge(r,t);return r&&Ee(r)&&ze(r)&&!_e(r)?n:r||function(e){let t=Se(e);for(;xe(t)&&!Ee(t);){if(_e(t))return t;if(Ce(t))return null;t=Se(t)}return null}(e)||n}const Ue={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,s=we(r),a=!!t&&Ce(t.floating);if(r===s||a&&i)return n;let c={scrollLeft:0,scrollTop:0},l=Y(1);const u=Y(0),d=xe(r);if((d||!d&&!i)&&(("body"!==he(r)||Re(s))&&(c=Oe(r)),xe(r))){const e=Ke(r);l=Fe(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x,y:n.y*l.y-c.scrollTop*l.y+u.y}},getDocumentElement:we,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?Ce(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=je(e,[],!1).filter((e=>ye(e)&&"body"!==he(e))),o=null;const i="fixed"===Pe(e).position;let s=i?Se(e):e;for(;ye(s)&&!Ee(s);){const t=Pe(s),n=_e(s);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Re(s)&&!n&&We(e,s))?r=r.filter((e=>e!==s)):o=t,s=Se(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=i[0],a=i.reduce(((e,n)=>{const r=He(t,n,o);return e.top=V(r.top,e.top),e.right=G(r.right,e.right),e.bottom=G(r.bottom,e.bottom),e.left=V(r.left,e.left),e}),He(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:Ve,getElementRects:async function(e){const t=this.getOffsetParent||Ve,n=this.getDimensions,r=await n(e.floating);return{reference:$e(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ke(e);return{width:t,height:n}},getScale:Fe,isElement:ye,isRTL:function(e){return"rtl"===Pe(e).direction}};const Xe=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,c=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),s=ee(n),a=te(n),c="y"===oe(n),l=["left","top"].includes(s)?-1:1,u=i&&c?-1:1,d=Q(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),c?{x:p*u,y:f*l}:{x:f*l,y:p*u}}(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:s}}}}},Ye=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=Q(e,t),l={x:n,y:r},u=await de(t,c),d=oe(ee(o)),f=ne(d);let p=l[f],m=l[d];if(i){const e="y"===f?"bottom":"right";p=J(p+u["y"===f?"top":"left"],p,p-u[e])}if(s){const e="y"===d?"bottom":"right";m=J(m+u["y"===d?"top":"left"],m,m-u[e])}const h=a.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:s}}}}}},qe=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:c,elements:l}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...g}=Q(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const w=ee(o),v=oe(a),y=ee(a)===a,x=await(null==c.isRTL?void 0:c.isRTL(l.floating)),b=f||(y||!h?[ae(a)]:function(e){const t=ae(e);return[se(e),t,se(t)]}(a)),R="none"!==m;!f&&R&&b.push(...function(e,t,n,r){const o=te(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}(ee(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(se)))),i}(a,h,m,x));const D=[a,...b],C=await de(t,g),_=[];let M=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&_.push(C[w]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=te(e),o=ie(e),i=re(o);let s="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=ae(s)),[s,ae(s)]}(o,s,x);_.push(C[e[0]],C[e[1]])}if(M=[...M,{placement:o,overflows:_}],!_.every((e=>e<=0))){var E,P;const e=((null==(E=i.flip)?void 0:E.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:M},reset:{placement:t}};let n=null==(P=M.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:P.placement;if(!n)switch(p){case"bestFit":{var O;const e=null==(O=M.filter((e=>{if(R){const t=oe(e.placement);return t===v||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:O[0];e&&(n=e);break}case"initialPlacement":n=a}if(o!==n)return{reset:{placement:n}}}return{}}}},Ze=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:a}=t,{apply:c=()=>{},...l}=Q(e,t),u=await de(t,l),d=ee(o),f=te(o),p="y"===oe(o),{width:m,height:h}=i.floating;let g,w;"top"===d||"bottom"===d?(g=d,w=f===(await(null==s.isRTL?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(w=d,g="end"===f?"top":"bottom");const v=h-u.top-u.bottom,y=m-u.left-u.right,x=G(h-u[g],v),b=G(m-u[w],y),R=!t.middlewareData.shift;let D=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=y),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(D=v),R&&!f){const e=V(u.left,0),t=V(u.right,0),n=V(u.top,0),r=V(u.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:V(u.left,u.right)):D=h-2*(0!==n||0!==r?n+r:V(u.top,u.bottom))}await c({...t,availableWidth:C,availableHeight:D});const _=await s.getDimensions(a.floating);return m!==_.width||h!==_.height?{reset:{rects:!0}}:{}}}},Je=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Q(e,t);switch(r){case"referenceHidden":{const e=fe(await de(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:pe(e)}}}case"escaped":{const e=fe(await de(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:pe(e)}}}default:return{}}}}},Qe=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:c}=t,{element:l,padding:u=0}=Q(e,t)||{};if(null==l)return{};const d=ce(u),f={x:n,y:r},p=ie(o),m=re(p),h=await s.getDimensions(l),g="y"===p,w=g?"top":"left",v=g?"bottom":"right",y=g?"clientHeight":"clientWidth",x=i.reference[m]+i.reference[p]-f[p]-i.floating[m],b=f[p]-i.reference[p],R=await(null==s.getOffsetParent?void 0:s.getOffsetParent(l));let D=R?R[y]:0;D&&await(null==s.isElement?void 0:s.isElement(R))||(D=a.floating[y]||i.floating[m]);const C=x/2-b/2,_=D/2-h[m]/2-1,M=G(d[w],_),E=G(d[v],_),P=M,O=D-h[m]-E,S=D/2-h[m]/2+C,A=J(P,S,O),j=!c.arrow&&null!=te(o)&&S!==A&&i.reference[m]/2-(S<P?M:E)-h[m]/2<0,T=j?S<P?S-P:S-O:0;return{[p]:f[p]+T,data:{[p]:A,centerOffset:S-A-T,...j&&{alignmentOffset:T}},reset:j}}}),et=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:c=!0,crossAxis:l=!0}=Q(e,t),u={x:n,y:r},d=oe(o),f=ne(d);let p=u[f],m=u[d];const h=Q(a,t),g="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+g.mainAxis,n=i.reference[f]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(l){var w,v;const e="y"===f?"width":"height",t=["top","left"].includes(ee(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(w=s.offset)?void 0:w[d])||0)+(t?0:g.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(v=s.offset)?void 0:v[d])||0)-(t?g.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}};var tt="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function nt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!nt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(!("_owner"===n&&e.$$typeof||nt(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function rt(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ot(e,t){const n=rt(e);return Math.round(t*n)/n}function it(t){const n=e.useRef(t);return tt((()=>{n.current=t})),n}const st=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Qe({element:n.current,padding:r}).fn(t):{}:n?Qe({element:n,padding:r}).fn(t):{};var o}}),at=(e,t)=>({...Ye(e),options:[e,t]}),ct=(e,t)=>({...et(e),options:[e,t]}),lt=(e,t)=>({...qe(e),options:[e,t]}),ut=(e,t)=>({...Ze(e),options:[e,t]}),dt=(e,t)=>({...Je(e),options:[e,t]}),ft=(e,t)=>({...st(e),options:[e,t]});var pt=e.forwardRef(((e,t)=>{const{children:r,width:i=10,height:s=5,...a}=e;return n.jsx(o.svg,{...a,ref:t,width:i,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:n.jsx("polygon",{points:"0,0 30,0 15,10"})})}));pt.displayName="Arrow";var mt=pt;function ht(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var gt="Popper",[wt,vt]=function(t,r=[]){let o=[];const i=()=>{const n=o.map((t=>e.createContext(t)));return function(r){const o=r?.[t]||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return i.scopeName=t,[function(r,i){const s=e.createContext(i),a=o.length;function c(r){const{scope:o,children:i,...c}=r,l=o?.[t][a]||s,u=e.useMemo((()=>c),Object.values(c));return n.jsx(l.Provider,{value:u,children:i})}return o=[...o,i],c.displayName=r+"Provider",[c,function(n,o){const c=o?.[t][a]||s,l=e.useContext(c);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${n}\` must be used within \`${r}\``)}]},ht(i,...r)]}(gt),[yt,xt]=wt(gt),bt=t=>{const{__scopePopper:r,children:o}=t,[i,s]=e.useState(null);return n.jsx(yt,{scope:r,anchor:i,onAnchorChange:s,children:o})};bt.displayName=gt;var Rt="PopperAnchor",Dt=e.forwardRef(((t,r)=>{const{__scopePopper:i,virtualRef:a,...c}=t,l=xt(Rt,i),u=e.useRef(null),d=s(r,u);return e.useEffect((()=>{l.onAnchorChange(a?.current||u.current)})),a?null:n.jsx(o.div,{...c,ref:d})}));Dt.displayName=Rt;var Ct="PopperContent",[_t,Mt]=wt(Ct),Et=e.forwardRef(((r,i)=>{const{__scopePopper:a,side:u="bottom",sideOffset:d=0,align:f="center",alignOffset:p=0,arrowPadding:m=0,avoidCollisions:h=!0,collisionBoundary:g=[],collisionPadding:w=0,sticky:v="partial",hideWhenDetached:y=!1,updatePositionStrategy:x="optimized",onPlaced:b,...R}=r,D=xt(Ct,a),[C,_]=e.useState(null),M=s(i,(e=>_(e))),[E,P]=e.useState(null),O=$(E),S=O?.width??0,A=O?.height??0,j=u+("center"!==f?"-"+f:""),T="number"==typeof w?w:{top:0,right:0,bottom:0,left:0,...w},k=Array.isArray(g)?g:[g],L=k.length>0,F={padding:T,boundary:k.filter(At),altBoundary:L},{refs:I,floatingStyles:N,placement:K,isPositioned:B,middlewareData:H}=function(n){void 0===n&&(n={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s,elements:{reference:a,floating:c}={},transform:l=!0,whileElementsMounted:u,open:d}=n,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[m,h]=e.useState(i);nt(m,i)||h(i);const[g,w]=e.useState(null),[v,y]=e.useState(null),x=e.useCallback((e=>{e!==C.current&&(C.current=e,w(e))}),[]),b=e.useCallback((e=>{e!==_.current&&(_.current=e,y(e))}),[]),R=a||g,D=c||v,C=e.useRef(null),_=e.useRef(null),M=e.useRef(f),E=null!=u,P=it(u),O=it(s),S=it(d),A=e.useCallback((()=>{if(!C.current||!_.current)return;const e={placement:r,strategy:o,middleware:m};O.current&&(e.platform=O.current),((e,t,n)=>{const r=new Map,o={platform:Ue,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),c=await(null==s.isRTL?void 0:s.isRTL(t));let l=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=ue(l,r,c),f=r,p={},m=0;for(let h=0;h<a.length;h++){const{name:n,fn:i}=a[h],{x:g,y:w,data:v,reset:y}=await i({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:l,platform:s,elements:{reference:e,floating:t}});u=null!=g?g:u,d=null!=w?w:d,p={...p,[n]:{...p[n],...v}},y&&m<=50&&(m++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(l=!0===y.rects?await s.getElementRects({reference:e,floating:t,strategy:o}):y.rects),({x:u,y:d}=ue(l,f,c))),h=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})})(C.current,_.current,e).then((e=>{const n={...e,isPositioned:!1!==S.current};j.current&&!nt(M.current,n)&&(M.current=n,t.flushSync((()=>{p(n)})))}))}),[m,r,o,O,S]);tt((()=>{!1===d&&M.current.isPositioned&&(M.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const j=e.useRef(!1);tt((()=>(j.current=!0,()=>{j.current=!1})),[]),tt((()=>{if(R&&(C.current=R),D&&(_.current=D),R&&D){if(P.current)return P.current(R,D,A);A()}}),[R,D,A,P,E]);const T=e.useMemo((()=>({reference:C,floating:_,setReference:x,setFloating:b})),[x,b]),k=e.useMemo((()=>({reference:R,floating:D})),[R,D]),L=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!k.floating)return e;const t=ot(k.floating,f.x),n=ot(k.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...rt(k.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,l,k.floating,f.x,f.y]);return e.useMemo((()=>({...f,update:A,refs:T,elements:k,floatingStyles:L})),[f,A,T,k,L])}({strategy:"fixed",placement:j,whileElementsMounted:(...e)=>function(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,l=Le(e),u=o||i?[...l?je(l):[],...je(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=l&&a?function(e,t){let n,r=null;const o=we(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function s(a,c){void 0===a&&(a=!1),void 0===c&&(c=1),i();const{left:l,top:u,width:d,height:f}=e.getBoundingClientRect();if(a||t(),!d||!f)return;const p={rootMargin:-X(u)+"px "+-X(o.clientWidth-(l+d))+"px "+-X(o.clientHeight-(u+f))+"px "+-X(l)+"px",threshold:V(0,G(1,c))||1};let m=!0;function h(e){const t=e[0].intersectionRatio;if(t!==c){if(!m)return s();t?s(!1,t):n=setTimeout((()=>{s(!1,1e-7)}),1e3)}m=!1}try{r=new IntersectionObserver(h,{...p,root:o.ownerDocument})}catch(g){r=new IntersectionObserver(h,p)}r.observe(e)}(!0),i}(l,n):null;let f,p=-1,m=null;s&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===l&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),l&&!c&&m.observe(l),m.observe(t));let h=c?Ke(e):null;return c&&function t(){const r=Ke(e);!h||r.x===h.x&&r.y===h.y&&r.width===h.width&&r.height===h.height||n(),h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(f)}}(...e,{animationFrame:"always"===x}),elements:{reference:D.anchor},middleware:[(W={mainAxis:d+A,alignmentAxis:p},{...Xe(W),options:[W,undefined]}),h&&at({mainAxis:!0,crossAxis:!1,limiter:"partial"===v?ct():void 0,...F}),h&&lt({...F}),ut({...F,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${n}px`),s.setProperty("--radix-popper-available-height",`${r}px`),s.setProperty("--radix-popper-anchor-width",`${o}px`),s.setProperty("--radix-popper-anchor-height",`${i}px`)}}),E&&ft({element:E,padding:m}),jt({arrowWidth:S,arrowHeight:A}),y&&dt({strategy:"referenceHidden",...F})]});var W;const[z,U]=Tt(K),Y=c(b);l((()=>{B&&Y?.()}),[B,Y]);const q=H.arrow?.x,Z=H.arrow?.y,J=0!==H.arrow?.centerOffset,[Q,ee]=e.useState();return l((()=>{C&&ee(window.getComputedStyle(C).zIndex)}),[C]),n.jsx("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...N,transform:B?N.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Q,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:r.dir,children:n.jsx(_t,{scope:a,placedSide:z,onArrowChange:P,arrowX:q,arrowY:Z,shouldHideArrow:J,children:n.jsx(o.div,{"data-side":z,"data-align":U,...R,ref:M,style:{...R.style,animation:B?void 0:"none"}})})})}));Et.displayName=Ct;var Pt="PopperArrow",Ot={top:"bottom",right:"left",bottom:"top",left:"right"},St=e.forwardRef((function(e,t){const{__scopePopper:r,...o}=e,i=Mt(Pt,r),s=Ot[i.placedSide];return n.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:n.jsx(mt,{...o,ref:t,style:{...o.style,display:"block"}})})}));function At(e){return null!==e}St.displayName=Pt;var jt=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=0!==o.arrow?.centerOffset,s=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[c,l]=Tt(n),u={start:"0%",center:"50%",end:"100%"}[l],d=(o.arrow?.x??0)+s/2,f=(o.arrow?.y??0)+a/2;let p="",m="";return"bottom"===c?(p=i?u:`${d}px`,m=-a+"px"):"top"===c?(p=i?u:`${d}px`,m=`${r.floating.height+a}px`):"right"===c?(p=-a+"px",m=i?u:`${f}px`):"left"===c&&(p=`${r.floating.width+a}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function Tt(e){const[t,n="center"]=e.split("-");return[t,n]}var kt,Lt=bt,Ft=Dt,It=Et,Nt=St,Kt="dismissableLayer.update",Bt=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ht=e.forwardRef(((t,r)=>{const{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:m,...h}=t,g=e.useContext(Bt),[w,v]=e.useState(null),y=w?.ownerDocument??globalThis?.document,[,x]=e.useState({}),b=s(r,(e=>v(e))),R=Array.from(g.layers),[D]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),C=R.indexOf(D),_=w?R.indexOf(w):-1,M=g.layersWithOutsidePointerEventsDisabled.size>0,E=_>=C,P=function(t,n=globalThis?.document){const r=c(t),o=e.useRef(!1),i=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){$t("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...g.branches].some((e=>e.contains(t)));E&&!n&&(d?.(e),p?.(e),e.defaultPrevented||m?.())}),y),O=function(t,n=globalThis?.document){const r=c(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{e.target&&!o.current&&$t("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...g.branches].some((e=>e.contains(t)))||(f?.(e),p?.(e),e.defaultPrevented||m?.())}),y);return u((e=>{_===g.layers.size-1&&(l?.(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))}),y),e.useEffect((()=>{if(w)return a&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(kt=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(w)),g.layers.add(w),Wt(),()=>{a&&1===g.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=kt)}}),[w,y,a,g]),e.useEffect((()=>()=>{w&&(g.layers.delete(w),g.layersWithOutsidePointerEventsDisabled.delete(w),Wt())}),[w,g]),e.useEffect((()=>{const e=()=>x({});return document.addEventListener(Kt,e),()=>document.removeEventListener(Kt,e)}),[]),n.jsx(o.div,{...h,ref:b,style:{pointerEvents:M?E?"auto":"none":void 0,...t.style},onFocusCapture:i(t.onFocusCapture,O.onFocusCapture),onBlurCapture:i(t.onBlurCapture,O.onBlurCapture),onPointerDownCapture:i(t.onPointerDownCapture,P.onPointerDownCapture)})}));function Wt(){const e=new CustomEvent(Kt);document.dispatchEvent(e)}function $t(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?d(o,i):o.dispatchEvent(i)}Ht.displayName="DismissableLayer",e.forwardRef(((t,r)=>{const i=e.useContext(Bt),a=e.useRef(null),c=s(r,a);return e.useEffect((()=>{const e=a.current;if(e)return i.branches.add(e),()=>{i.branches.delete(e)}}),[i.branches]),n.jsx(o.div,{...t,ref:c})})).displayName="DismissableLayerBranch";var zt=["Enter"," "],Gt=["ArrowUp","PageDown","End"],Vt=["ArrowDown","PageUp","Home",...Gt],Ut={ltr:[...zt,"ArrowRight"],rtl:[...zt,"ArrowLeft"]},Xt={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Yt="Menu",[qt,Zt,Jt]=b(Yt),[Qt,en]=f(Yt,[Jt,vt,j]),tn=vt(),nn=j(),[rn,on]=Qt(Yt),[sn,an]=Qt(Yt),cn=t=>{const{__scopeMenu:r,open:o=!1,children:i,dir:s,onOpenChange:a,modal:l=!0}=t,u=tn(r),[d,f]=e.useState(null),p=e.useRef(!1),m=c(a),h=C(s);return e.useEffect((()=>{const e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}}),[]),n.jsx(Lt,{...u,children:n.jsx(rn,{scope:r,open:o,onOpenChange:m,content:d,onContentChange:f,children:n.jsx(sn,{scope:r,onClose:e.useCallback((()=>m(!1)),[m]),isUsingKeyboardRef:p,dir:h,modal:l,children:i})})})};cn.displayName=Yt;var ln=e.forwardRef(((e,t)=>{const{__scopeMenu:r,...o}=e,i=tn(r);return n.jsx(Ft,{...i,...o,ref:t})}));ln.displayName="MenuAnchor";var un="MenuPortal",[dn,fn]=Qt(un,{forceMount:void 0}),pn=e=>{const{__scopeMenu:t,forceMount:r,children:o,container:i}=e,s=on(un,t);return n.jsx(dn,{scope:t,forceMount:r,children:n.jsx(m,{present:r||s.open,children:n.jsx(h,{asChild:!0,container:i,children:o})})})};pn.displayName=un;var mn="MenuContent",[hn,gn]=Qt(mn),wn=e.forwardRef(((e,t)=>{const r=fn(mn,e.__scopeMenu),{forceMount:o=r.forceMount,...i}=e,s=on(mn,e.__scopeMenu),a=an(mn,e.__scopeMenu);return n.jsx(qt.Provider,{scope:e.__scopeMenu,children:n.jsx(m,{present:o||s.open,children:n.jsx(qt.Slot,{scope:e.__scopeMenu,children:a.modal?n.jsx(vn,{...i,ref:t}):n.jsx(yn,{...i,ref:t})})})})})),vn=e.forwardRef(((t,r)=>{const o=on(mn,t.__scopeMenu),a=e.useRef(null),c=s(r,a);return e.useEffect((()=>{const e=a.current;if(e)return x(e)}),[]),n.jsx(xn,{...t,ref:c,trapFocus:o.open,disableOutsidePointerEvents:o.open,disableOutsideScroll:!0,onFocusOutside:i(t.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1}),onDismiss:()=>o.onOpenChange(!1)})})),yn=e.forwardRef(((e,t)=>{const r=on(mn,e.__scopeMenu);return n.jsx(xn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})})),xn=e.forwardRef(((t,r)=>{const{__scopeMenu:o,loop:a=!1,trapFocus:c,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:x,onDismiss:b,disableOutsideScroll:R,...D}=t,C=on(mn,o),_=an(mn,o),M=tn(o),E=nn(o),P=Zt(o),[O,S]=e.useState(null),A=e.useRef(null),j=s(r,A,C.onContentChange),T=e.useRef(0),k=e.useRef(""),L=e.useRef(0),F=e.useRef(null),I=e.useRef("right"),N=e.useRef(0),K=R?w:e.Fragment,B=R?{as:v,allowPinchZoom:!0}:void 0;e.useEffect((()=>()=>window.clearTimeout(T.current)),[]),g();const W=e.useCallback((e=>I.current===F.current?.side&&function(e,t){if(!t)return!1;return function(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const e=t[i].x,a=t[i].y,c=t[s].x,l=t[s].y;a>r!=l>r&&n<(c-e)*(r-a)/(l-a)+e&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,F.current?.area)),[]);return n.jsx(hn,{scope:o,searchRef:k,onItemEnter:e.useCallback((e=>{W(e)&&e.preventDefault()}),[W]),onItemLeave:e.useCallback((e=>{W(e)||(A.current?.focus(),S(null))}),[W]),onTriggerLeave:e.useCallback((e=>{W(e)&&e.preventDefault()}),[W]),pointerGraceTimerRef:L,onPointerGraceIntentChange:e.useCallback((e=>{F.current=e}),[]),children:n.jsx(K,{...B,children:n.jsx(y,{asChild:!0,trapped:c,onMountAutoFocus:i(l,(e=>{e.preventDefault(),A.current?.focus({preventScroll:!0})})),onUnmountAutoFocus:u,children:n.jsx(Ht,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:p,onPointerDownOutside:m,onFocusOutside:h,onInteractOutside:x,onDismiss:b,children:n.jsx(H,{asChild:!0,...E,dir:_.dir,orientation:"vertical",loop:a,currentTabStopId:O,onCurrentTabStopIdChange:S,onEntryFocus:i(f,(e=>{_.isUsingKeyboardRef.current||e.preventDefault()})),preventScrollOnEntryFocus:!0,children:n.jsx(It,{role:"menu","aria-orientation":"vertical","data-state":Vn(C.open),"data-radix-menu-content":"",dir:_.dir,...M,...D,ref:j,style:{outline:"none",...D.style},onKeyDown:i(D.onKeyDown,(e=>{const t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{const t=k.current+e,n=P().filter((e=>!e.disabled)),r=document.activeElement,o=n.find((e=>e.ref.current===r))?.textValue,i=function(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let i=(s=e,a=Math.max(o,0),s.map(((e,t)=>s[(a+t)%s.length])));var s,a;1===r.length&&(i=i.filter((e=>e!==n)));const c=i.find((e=>e.toLowerCase().startsWith(r.toLowerCase())));return c!==n?c:void 0}(n.map((e=>e.textValue)),t,o),s=n.find((e=>e.textValue===i))?.ref.current;!function e(t){k.current=t,window.clearTimeout(T.current),""!==t&&(T.current=window.setTimeout((()=>e("")),1e3))}(t),s&&setTimeout((()=>s.focus()))})(e.key));const o=A.current;if(e.target!==o)return;if(!Vt.includes(e.key))return;e.preventDefault();const i=P().filter((e=>!e.disabled)).map((e=>e.ref.current));Gt.includes(e.key)&&i.reverse(),function(e){const t=document.activeElement;for(const n of e){if(n===t)return;if(n.focus(),document.activeElement!==t)return}}(i)})),onBlur:i(t.onBlur,(e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),k.current="")})),onPointerMove:i(t.onPointerMove,Yn((e=>{const t=e.target,n=N.current!==e.clientX;if(e.currentTarget.contains(t)&&n){const t=e.clientX>N.current?"right":"left";I.current=t,N.current=e.clientX}})))})})})})})})}));wn.displayName=mn;var bn=e.forwardRef(((e,t)=>{const{__scopeMenu:r,...i}=e;return n.jsx(o.div,{role:"group",...i,ref:t})}));bn.displayName="MenuGroup";var Rn=e.forwardRef(((e,t)=>{const{__scopeMenu:r,...i}=e;return n.jsx(o.div,{...i,ref:t})}));Rn.displayName="MenuLabel";var Dn="MenuItem",Cn="menu.itemSelect",_n=e.forwardRef(((t,r)=>{const{disabled:o=!1,onSelect:a,...c}=t,l=e.useRef(null),u=an(Dn,t.__scopeMenu),f=gn(Dn,t.__scopeMenu),p=s(r,l),m=e.useRef(!1);return n.jsx(Mn,{...c,ref:p,disabled:o,onClick:i(t.onClick,(()=>{const e=l.current;if(!o&&e){const t=new CustomEvent(Cn,{bubbles:!0,cancelable:!0});e.addEventListener(Cn,(e=>a?.(e)),{once:!0}),d(e,t),t.defaultPrevented?m.current=!1:u.onClose()}})),onPointerDown:e=>{t.onPointerDown?.(e),m.current=!0},onPointerUp:i(t.onPointerUp,(e=>{m.current||e.currentTarget?.click()})),onKeyDown:i(t.onKeyDown,(e=>{const t=""!==f.searchRef.current;o||t&&" "===e.key||zt.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())}))})}));_n.displayName=Dn;var Mn=e.forwardRef(((t,r)=>{const{__scopeMenu:a,disabled:c=!1,textValue:l,...u}=t,d=gn(Dn,a),f=nn(a),p=e.useRef(null),m=s(r,p),[h,g]=e.useState(!1),[w,v]=e.useState("");return e.useEffect((()=>{const e=p.current;e&&v((e.textContent??"").trim())}),[u.children]),n.jsx(qt.ItemSlot,{scope:a,disabled:c,textValue:l??w,children:n.jsx(W,{asChild:!0,...f,focusable:!c,children:n.jsx(o.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":c||void 0,"data-disabled":c?"":void 0,...u,ref:m,onPointerMove:i(t.onPointerMove,Yn((e=>{c?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))}))),onPointerLeave:i(t.onPointerLeave,Yn((e=>d.onItemLeave(e)))),onFocus:i(t.onFocus,(()=>g(!0))),onBlur:i(t.onBlur,(()=>g(!1)))})})})})),En=e.forwardRef(((e,t)=>{const{checked:r=!1,onCheckedChange:o,...s}=e;return n.jsx(Ln,{scope:e.__scopeMenu,checked:r,children:n.jsx(_n,{role:"menuitemcheckbox","aria-checked":Un(r)?"mixed":r,...s,ref:t,"data-state":Xn(r),onSelect:i(s.onSelect,(()=>o?.(!!Un(r)||!r)),{checkForDefaultPrevented:!1})})})}));En.displayName="MenuCheckboxItem";var Pn="MenuRadioGroup",[On,Sn]=Qt(Pn,{value:void 0,onValueChange:()=>{}}),An=e.forwardRef(((e,t)=>{const{value:r,onValueChange:o,...i}=e,s=c(o);return n.jsx(On,{scope:e.__scopeMenu,value:r,onValueChange:s,children:n.jsx(bn,{...i,ref:t})})}));An.displayName=Pn;var jn="MenuRadioItem",Tn=e.forwardRef(((e,t)=>{const{value:r,...o}=e,s=Sn(jn,e.__scopeMenu),a=r===s.value;return n.jsx(Ln,{scope:e.__scopeMenu,checked:a,children:n.jsx(_n,{role:"menuitemradio","aria-checked":a,...o,ref:t,"data-state":Xn(a),onSelect:i(o.onSelect,(()=>s.onValueChange?.(r)),{checkForDefaultPrevented:!1})})})}));Tn.displayName=jn;var kn="MenuItemIndicator",[Ln,Fn]=Qt(kn,{checked:!1}),In=e.forwardRef(((e,t)=>{const{__scopeMenu:r,forceMount:i,...s}=e,a=Fn(kn,r);return n.jsx(m,{present:i||Un(a.checked)||!0===a.checked,children:n.jsx(o.span,{...s,ref:t,"data-state":Xn(a.checked)})})}));In.displayName=kn;var Nn=e.forwardRef(((e,t)=>{const{__scopeMenu:r,...i}=e;return n.jsx(o.div,{role:"separator","aria-orientation":"horizontal",...i,ref:t})}));Nn.displayName="MenuSeparator";var Kn=e.forwardRef(((e,t)=>{const{__scopeMenu:r,...o}=e,i=tn(r);return n.jsx(Nt,{...i,...o,ref:t})}));Kn.displayName="MenuArrow";var[Bn,Hn]=Qt("MenuSub"),Wn="MenuSubTrigger",$n=e.forwardRef(((t,r)=>{const o=on(Wn,t.__scopeMenu),s=an(Wn,t.__scopeMenu),a=Hn(Wn,t.__scopeMenu),c=gn(Wn,t.__scopeMenu),l=e.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:d}=c,f={__scopeMenu:t.__scopeMenu},m=e.useCallback((()=>{l.current&&window.clearTimeout(l.current),l.current=null}),[]);return e.useEffect((()=>m),[m]),e.useEffect((()=>{const e=u.current;return()=>{window.clearTimeout(e),d(null)}}),[u,d]),n.jsx(ln,{asChild:!0,...f,children:n.jsx(Mn,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":a.contentId,"data-state":Vn(o.open),...t,ref:p(r,a.onTriggerChange),onClick:e=>{t.onClick?.(e),t.disabled||e.defaultPrevented||(e.currentTarget.focus(),o.open||o.onOpenChange(!0))},onPointerMove:i(t.onPointerMove,Yn((e=>{c.onItemEnter(e),e.defaultPrevented||t.disabled||o.open||l.current||(c.onPointerGraceIntentChange(null),l.current=window.setTimeout((()=>{o.onOpenChange(!0),m()}),100))}))),onPointerLeave:i(t.onPointerLeave,Yn((e=>{m();const t=o.content?.getBoundingClientRect();if(t){const n=o.content?.dataset.side,r="right"===n,i=r?-5:5,s=t[r?"left":"right"],a=t[r?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+i,y:e.clientY},{x:s,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:s,y:t.bottom}],side:n}),window.clearTimeout(u.current),u.current=window.setTimeout((()=>c.onPointerGraceIntentChange(null)),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}}))),onKeyDown:i(t.onKeyDown,(e=>{const n=""!==c.searchRef.current;t.disabled||n&&" "===e.key||Ut[s.dir].includes(e.key)&&(o.onOpenChange(!0),o.content?.focus(),e.preventDefault())}))})})}));$n.displayName=Wn;var zn="MenuSubContent",Gn=e.forwardRef(((t,r)=>{const o=fn(mn,t.__scopeMenu),{forceMount:a=o.forceMount,...c}=t,l=on(mn,t.__scopeMenu),u=an(mn,t.__scopeMenu),d=Hn(zn,t.__scopeMenu),f=e.useRef(null),p=s(r,f);return n.jsx(qt.Provider,{scope:t.__scopeMenu,children:n.jsx(m,{present:a||l.open,children:n.jsx(qt.Slot,{scope:t.__scopeMenu,children:n.jsx(xn,{id:d.contentId,"aria-labelledby":d.triggerId,...c,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:i(t.onFocusOutside,(e=>{e.target!==d.trigger&&l.onOpenChange(!1)})),onEscapeKeyDown:i(t.onEscapeKeyDown,(e=>{u.onClose(),e.preventDefault()})),onKeyDown:i(t.onKeyDown,(e=>{const t=e.currentTarget.contains(e.target),n=Xt[u.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())}))})})})})}));function Vn(e){return e?"open":"closed"}function Un(e){return"indeterminate"===e}function Xn(e){return Un(e)?"indeterminate":e?"checked":"unchecked"}function Yn(e){return t=>"mouse"===t.pointerType?e(t):void 0}Gn.displayName=zn;var qn=cn,Zn=ln,Jn=pn,Qn=wn,er=bn,tr=Rn,nr=_n,rr=En,or=An,ir=Tn,sr=In,ar=Nn,cr=Kn,lr=$n,ur=Gn,dr="DropdownMenu",[fr,pr]=f(dr,[en]),mr=en(),[hr,gr]=fr(dr),wr=t=>{const{__scopeDropdownMenu:o,children:i,dir:s,open:c,defaultOpen:l,onOpenChange:u,modal:d=!0}=t,f=mr(o),p=e.useRef(null),[m=!1,h]=a({prop:c,defaultProp:l,onChange:u});return n.jsx(hr,{scope:o,triggerId:r(),triggerRef:p,contentId:r(),open:m,onOpenChange:h,onOpenToggle:e.useCallback((()=>h((e=>!e))),[h]),modal:d,children:n.jsx(qn,{...f,open:m,onOpenChange:h,dir:s,modal:d,children:i})})};wr.displayName=dr;var vr="DropdownMenuTrigger",yr=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,disabled:s=!1,...a}=e,c=gr(vr,r),l=mr(r);return n.jsx(Zn,{asChild:!0,...l,children:n.jsx(o.button,{type:"button",id:c.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":c.open?c.contentId:void 0,"data-state":c.open?"open":"closed","data-disabled":s?"":void 0,disabled:s,...a,ref:p(t,c.triggerRef),onPointerDown:i(e.onPointerDown,(e=>{s||0!==e.button||!1!==e.ctrlKey||(c.onOpenToggle(),c.open||e.preventDefault())})),onKeyDown:i(e.onKeyDown,(e=>{s||(["Enter"," "].includes(e.key)&&c.onOpenToggle(),"ArrowDown"===e.key&&c.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())}))})})}));yr.displayName=vr;var xr=e=>{const{__scopeDropdownMenu:t,...r}=e,o=mr(t);return n.jsx(Jn,{...o,...r})};xr.displayName="DropdownMenuPortal";var br="DropdownMenuContent",Rr=e.forwardRef(((t,r)=>{const{__scopeDropdownMenu:o,...s}=t,a=gr(br,o),c=mr(o),l=e.useRef(!1);return n.jsx(Qn,{id:a.contentId,"aria-labelledby":a.triggerId,...c,...s,ref:r,onCloseAutoFocus:i(t.onCloseAutoFocus,(e=>{l.current||a.triggerRef.current?.focus(),l.current=!1,e.preventDefault()})),onInteractOutside:i(t.onInteractOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;a.modal&&!r||(l.current=!0)})),style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}));Rr.displayName=br,e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(er,{...i,...o,ref:t})})).displayName="DropdownMenuGroup";var Dr=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(tr,{...i,...o,ref:t})}));Dr.displayName="DropdownMenuLabel";var Cr=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(nr,{...i,...o,ref:t})}));Cr.displayName="DropdownMenuItem";var _r=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(rr,{...i,...o,ref:t})}));_r.displayName="DropdownMenuCheckboxItem",e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(or,{...i,...o,ref:t})})).displayName="DropdownMenuRadioGroup";var Mr=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(ir,{...i,...o,ref:t})}));Mr.displayName="DropdownMenuRadioItem";var Er=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(sr,{...i,...o,ref:t})}));Er.displayName="DropdownMenuItemIndicator";var Pr=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(ar,{...i,...o,ref:t})}));Pr.displayName="DropdownMenuSeparator",e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(cr,{...i,...o,ref:t})})).displayName="DropdownMenuArrow";var Or=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(lr,{...i,...o,ref:t})}));Or.displayName="DropdownMenuSubTrigger";var Sr=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:r,...o}=e,i=mr(r);return n.jsx(ur,{...i,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}));Sr.displayName="DropdownMenuSubContent";var Ar=wr,jr=yr,Tr=xr,kr=Rr,Lr=Dr,Fr=Cr,Ir=_r,Nr=Mr,Kr=Er,Br=Pr,Hr=Or,Wr=Sr;export{Ft as A,It as C,W as I,Lr as L,Tr as P,Lt as R,Hr as S,jr as T,Nt as a,j as b,vt as c,H as d,$ as e,Wr as f,kr as g,Fr as h,Ir as i,Kr as j,Nr as k,Br as l,Ar as m,C as u};
