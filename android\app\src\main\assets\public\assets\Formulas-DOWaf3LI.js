import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{u as s}from"./query-vendor-B-7l6Nb3.js";import{L as r}from"./router-BAzpOxbo.js";import{aH as i,j as o,m as t,R as n,aa as d,a7 as l,s as m}from"./index-CrSshpOb.js";import{A as c}from"./index-CDKVdlBF.js";import u from"./Footer-ClHMSbsi.js";import{S as p}from"./star-DsgxKBIV.js";import{C as x,a as g}from"./childcareSEOData-BzPfjXOt.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";const f=({items:s})=>{const[r,n]=a.useState(null);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-3xl font-bold text-center mb-8 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent",children:"Perguntas Frequentes"}),e.jsx("div",{className:"space-y-4 border rounded-xl p-6 bg-white/50 backdrop-blur-sm",children:s.map(((a,s)=>e.jsxs("div",{className:"border rounded-lg overflow-hidden bg-white",children:[e.jsxs("button",{onClick:()=>n(r===s?null:s),className:"w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors",children:[e.jsx("span",{className:"font-medium text-gray-900",children:a.question}),e.jsx(i,{className:o("w-5 h-5 text-gray-500 transition-transform duration-200",r===s&&"transform rotate-180")})]}),e.jsx(c,{children:r===s&&e.jsx(t.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},children:e.jsx("div",{className:"p-4 border-t bg-gray-50",children:e.jsx("p",{className:"text-gray-600",children:a.answer})})})})]},s)))})]})},b=a.memo((({formulas:s,selectedFormula:r,setSelectedFormula:i})=>{const d=a.useCallback((e=>{i(r?.id===e.id?null:e)}),[r?.id,i]);return e.jsx("div",{className:"flex flex-wrap gap-3 justify-center",children:s?.map((a=>e.jsx(t.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"flex-shrink-0",children:e.jsx(n,{className:o("p-4 cursor-pointer transition-all duration-300 hover:shadow-md bg-white/80 backdrop-blur-sm border-2",r?.id===a.id?"border-primary shadow-lg transform -translate-y-1":"border-transparent hover:-translate-y-1"),onClick:()=>d(a),children:e.jsxs("div",{className:"space-y-2 text-center",children:[e.jsx("h4",{className:"font-medium text-gray-900 whitespace-nowrap",children:a.brand}),e.jsx("p",{className:"text-sm text-gray-600 whitespace-nowrap",children:a.name})]})})},a.id)))})})),h=({formula:a})=>{return e.jsx(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"bg-white rounded-xl p-6 shadow-lg",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[e.jsx(c,{mode:"wait",children:a.image_url&&e.jsx(t.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},className:"w-full lg:w-1/3 flex items-center justify-center",children:e.jsx(t.img,{src:a.image_url,alt:a.name,className:"w-full max-w-[300px] h-auto rounded-lg object-contain",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},loading:"eager"},a.image_url)},a.image_url)}),e.jsxs("div",{className:"flex-1 space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(p,{className:"w-6 h-6 text-yellow-400 fill-current"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900",children:a.name})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Idade recomendada:"}),e.jsx("p",{className:"font-medium text-gray-900",children:a.age_range})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Preço médio:"}),e.jsx("p",{className:"font-medium text-gray-900",children:a.price?`R$ ${a.price.toFixed(2)}`:"Não informado"})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Marca:"}),e.jsx("p",{className:"font-medium text-gray-900",children:a.brand})]})]}),a.description&&e.jsxs("div",{className:"prose max-w-none",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Descrição:"}),e.jsx("div",{className:"text-gray-700",children:(s=a.description,s?s.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").split("\n").map(((a,s)=>e.jsx("p",{className:"mb-2",children:e.jsx("span",{dangerouslySetInnerHTML:{__html:a}})},s))):"")})]}),a.nutrients&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Nutrientes por 100ml:"}),e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4",children:Object.entries(a.nutrients).map((([a,s])=>e.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[e.jsxs("p",{className:"text-gray-600 text-sm",children:[a,":"]}),e.jsx("p",{className:"font-medium text-gray-900",children:String(s)})]},a)))})]})]})]})});var s},v=a.memo((({categories:s,formulas:r,selectedCategory:i,setSelectedCategory:d,selectedFormula:l,setSelectedFormula:m,isLoading:c=!1})=>{const u=a.useMemo((()=>r?.filter((e=>e.category_id===i))||[]),[r,i]),p=a.useCallback((e=>{i===e?(d(null),m(null)):(d(e),m(null))}),[i,d,m]);return e.jsxs("div",{className:"space-y-8",children:[e.jsx("div",{className:"flex flex-wrap gap-4 justify-center",children:s?.map((a=>e.jsx(t.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"flex-shrink-0",children:e.jsx(n,{className:o("p-4 cursor-pointer transition-all duration-300 hover:shadow-lg bg-white/80 backdrop-blur-sm border-2",i===a.id?"border-primary shadow-lg transform -translate-y-1":"border-transparent hover:-translate-y-1"),onClick:()=>p(a.id),children:e.jsx("h3",{className:"text-lg font-medium text-gray-900 whitespace-nowrap",children:a.name})})},a.id)))}),i&&e.jsx(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"pt-4",children:c?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm",children:"Carregando fórmulas..."})]}):e.jsx(b,{formulas:u,selectedFormula:l,setSelectedFormula:m})}),l&&e.jsx(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"mt-8",children:e.jsx(h,{formula:l})})]})})),j=()=>{const i=g.formulas,[o,n]=a.useState(null),[c,p]=a.useState(null),{data:b,isLoading:h}=s({queryKey:["formula-categories"],queryFn:async()=>{const{data:e,error:a}=await m.from("pedbook_formula_categories").select("id, name, description, display_order").order("display_order",{ascending:!0});if(a)throw a;return e&&e.length>0&&(void 0===e[0].display_order||null===e[0].display_order)?e.sort(((e,a)=>{const s={"Fórmulas de Partida":1,"Fórmulas de Seguimento":2,"Fórmulas de Primeira Infância":3};return(s[e.name]||999)-(s[a.name]||999)})):e},staleTime:6e5,cacheTime:18e5}),{data:j,isLoading:y}=s({queryKey:["formulas",o],queryFn:async()=>{if(!o)return[];const{data:e,error:a}=await m.from("pedbook_formulas").select("id, name, description, brand, category_id, age_range, price, image_url").eq("category_id",o).order("name");if(a)throw a;return e},staleTime:9e5,cacheTime:18e5,enabled:!!o});return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-purple-900/20",children:[e.jsx(x,{...i}),e.jsx(d,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-12",children:[e.jsxs(r,{to:"/puericultura",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(l,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Puericultura"})]}),e.jsxs(t.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"relative mb-16",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-200/30 via-purple-200/30 to-pink-200/30 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-pink-900/20 rounded-3xl blur-xl"}),e.jsxs(t.div,{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},transition:{type:"spring",duration:1,bounce:.3},className:"relative glass-effect rounded-3xl p-10 backdrop-blur-sm border border-white/20 dark:border-white/10 overflow-hidden bg-white/50 dark:bg-slate-800/50",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 to-purple-500/5 animate-pulse dark:from-primary/10 dark:to-purple-500/10"}),e.jsxs("div",{className:"relative z-10",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary via-purple-600 to-pink-500 bg-clip-text text-transparent text-center mb-4",children:"Fórmulas Infantis"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-lg text-center leading-relaxed",children:"Explore nossa seleção cuidadosamente organizada de fórmulas alimentares, classificadas por categorias e faixas etárias para o melhor desenvolvimento do seu bebê."})]})]})]}),h?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Carregando categorias..."})]}):e.jsx(v,{categories:b,formulas:j,selectedCategory:o,setSelectedCategory:n,selectedFormula:c,setSelectedFormula:p,isLoading:y}),e.jsx("div",{className:"max-w-3xl mx-auto mt-16",children:e.jsx(f,{items:[{question:"O que são fórmulas infantis?",answer:"Fórmulas infantis são substitutos do leite materno desenvolvidos para suprir as necessidades nutricionais dos bebês que não podem ser amamentados. Elas são feitas com base no leite de vaca ou de soja e são modificadas para se aproximar da composição do leite materno. Elas fornecem uma mistura balanceada de proteínas, gorduras, carboidratos, vitaminas e minerais. Embora não repliquem os benefícios imunológicos do leite materno, são essenciais para bebês com contraindicações ao aleitamento."},{question:"Quando é indicada a utilização de fórmulas infantis?",answer:"A fórmula infantil é indicada quando o aleitamento materno não é possível ou é insuficiente. As principais indicações incluem: Dificuldades no aleitamento por parte da mãe; Condições clínicas específicas do bebê, como alergia à proteína do leite de vaca (APLV) ou intolerância à lactose; Situações em que a amamentação exclusiva não é praticada. Bebês prematuros, com baixo peso ao nascer, ou portadores de condições metabólicas também podem necessitar de fórmulas especiais. O acompanhamento pediátrico é essencial para ajustar o tipo e a quantidade de fórmula de acordo com as necessidades individuais."},{question:"Quais são os principais tipos de fórmulas infantis?",answer:"Os principais tipos de fórmulas infantis são:\n\nFórmulas de Partida: Indicadas para lactentes desde o nascimento até os 6 meses de idade.\nFórmulas de Seguimento: Utilizadas a partir dos 6 meses até os 12 meses, podendo se estender até os 36 meses em alguns casos.\nFórmulas de Primeira Infância: Voltadas para crianças de 1 a 3 anos de idade.\nFórmulas Antirregurgitação (AR): Mais espessas, ajudam a reduzir o refluxo.\nFórmulas de Soja: Indicadas para bebês com alergia à proteína do leite de vaca (APLV).\nFórmulas Extensamente Hidrolisadas: Usadas em casos de alergia alimentar grave.\nFórmulas de Aminoácidos: Utilizadas em bebês com alergias alimentares severas.\n\nExistem fórmulas específicas para bebês prematuros ou com restrições alimentares severas, que possuem uma composição de nutrientes mais balanceada, facilitando o crescimento e desenvolvimento."},{question:"Como preparar corretamente uma fórmula infantil?",answer:"Para preparar a fórmula corretamente:\nÁgua: Utilize água filtrada ou fervida. Caso opte por ferver a água, deixe-a esfriar até atingir aproximadamente 70°C antes de misturá-la com o pó da fórmula.\nProporção: Siga a proporção recomendada pelo fabricante (geralmente 1 medida de pó para cada 30 mL de água).\nHigiene: Lave bem as mãos e esterilize todos os utensílios, como mamadeiras, bicos e tampas.\nMistura: Adicione a quantidade correta de pó da fórmula à água preparada e agite até dissolver completamente.\nTeste de temperatura: Teste a temperatura da fórmula no pulso antes de oferecer ao bebê. Deve estar morna, nunca quente.\n\nAs fórmulas preparadas devem ser consumidas em até 2 horas. Caso contrário, descarte a fórmula. Evite deixá-la exposta à temperatura ambiente para prevenir a proliferação de bactérias."},{question:"Quais são os volumes recomendados para oferecer fórmulas de acordo com a idade do bebê?",answer:"Nascimento até 30 dias: 60-120 mL por administração, 6-8 vezes ao dia.\n1 a 2 meses: 120-150 mL por administração, 6-8 vezes ao dia.\n2 a 4 meses: 150-180 mL por administração, 5-6 vezes ao dia.\n4 a 8 meses: 180-200 mL por administração, 5-6 vezes ao dia.\nAcima de 8 meses: 200-250 mL por administração, 4-5 vezes ao dia.\n\nEsses valores podem variar conforme o desenvolvimento e o ganho de peso da criança. O pediatra deve ajustar os volumes de acordo com o estado clínico e as necessidades nutricionais do bebê."},{question:"Quando posso oferecer suco de frutas ao meu bebê?",answer:"A Sociedade Brasileira de Pediatria recomenda que sucos de frutas não sejam oferecidos a bebês com menos de 1 ano de idade, pois eles não trazem benefícios nutricionais significativos nessa fase. A introdução de alimentos sólidos deve seguir o aleitamento materno e a recomendação de alimentos naturais. A introdução de frutas deve ser feita de forma gradual e em pedaços, a partir dos 6 meses, para que o bebê desenvolva suas habilidades mastigatórias."},{question:"Qual a diferença entre fórmulas infantis e compostos lácteos?",answer:"Fórmulas infantis são desenvolvidas para atender as necessidades nutricionais dos lactentes (bebês menores de 12 meses), enquanto compostos lácteos são direcionados para crianças acima de 1 ano de idade. Os compostos lácteos, muitas vezes chamados de 'leites de crescimento', têm uma composição menos rigorosa e podem conter ingredientes adicionais como açúcares e aditivos. Eles não são indicados para bebês menores de 1 ano. O consumo antes dos 12 meses pode levar a deficiências nutricionais e sobrecarga renal no bebê."},{question:"O leite de vaca pode substituir a fórmula infantil?",answer:"Não. O leite de vaca integral não é adequado para crianças menores de 1 ano devido ao seu alto teor de proteínas, sódio, ácidos graxos saturados e menor biodisponibilidade de nutrientes essenciais como ferro e zinco. A fórmula infantil é a melhor alternativa nutricional caso o leite materno não esteja disponível."},{question:"Quais são os possíveis riscos do uso prolongado de mamadeiras?",answer:"O uso prolongado de mamadeiras, especialmente após os 4 meses de idade, pode causar problemas no desenvolvimento da cavidade bucal e no sistema respiratório. Recomenda-se a transição para o uso de copinhos para evitar essas complicações. O uso prolongado de mamadeiras também está associado ao aumento do risco de cáries dentárias e infecções do ouvido médio. A transição para copos de transição entre 6-9 meses de vida deve ser incentivada."}]})})]}),e.jsx(u,{})]})};export{j as default};
