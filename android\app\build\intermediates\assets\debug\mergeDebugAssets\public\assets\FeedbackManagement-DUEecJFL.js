import{b as e,j as s,P as a,c as t}from"./radix-core-6kBL75b5.js";import{r}from"./critical-DVX9Inzy.js";import{j as d,aF as i,d as l,s as c,R as n,W as o,Z as m,U as x,aB as h,av as g,ay as u,aG as f,E as p,ak as b,u as j,B as N,a7 as v,V as y,aq as w,ar as k,as as _,at as C,m as S,aw as z,T as R,C as E,Y as A}from"./index-DwykrzWu.js";import{C as F}from"./circle-check-vF9bNmQ9.js";import{C as P}from"./clock-v9u4hrAh.js";import{T}from"./trending-up-BWm6pgaU.js";import{S as B}from"./star-CVUfjIpQ.js";import{U as D}from"./users-B3zBleJG.js";import{C as q}from"./calendar-hvRXSQ6q.js";import{T as I}from"./target-Bq7BybE4.js";import{L as M}from"./lightbulb-CeJxLvRY.js";import{D as L}from"./dollar-sign-B-0NbHBg.js";import{A as O,h as U,a as V,b as G,c as $,d as W,e as Y,f as Z,g as H}from"./alert-dialog-DrRzT_Wc.js";import{a as J}from"./router-BAzpOxbo.js";import{R as K}from"./refresh-cw-928r5lnJ.js";import{T as Q}from"./trash-2-DV0_UZFC.js";import{C as X}from"./chevron-left-UiG5RJWe.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";var ee=r.forwardRef(((r,d)=>{const{pressed:i,defaultPressed:l=!1,onPressedChange:c,...n}=r,[o=!1,m]=e({prop:i,onChange:c,defaultProp:l});return s.jsx(a.button,{type:"button","aria-pressed":o,"data-state":o?"on":"off","data-disabled":r.disabled?"":void 0,...n,ref:d,onClick:t(r.onClick,(()=>{r.disabled||m(!o)}))})}));ee.displayName="Toggle";var se=ee;const ae=i("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground"},size:{default:"h-10 px-3",sm:"h-9 px-2.5",lg:"h-11 px-5"}},defaultVariants:{variant:"default",size:"default"}}),te=r.forwardRef((({className:e,variant:a,size:t,...r},i)=>s.jsx(se,{ref:i,className:d(ae({variant:a,size:t,className:e})),...r})));function re({feedbackId:e,currentStatus:a,onStatusChange:t}){const{toast:r}=l(),d="resolvido"===a;return s.jsx(te,{pressed:d,onPressedChange:async()=>{const s=d?"aguardando":"resolvido";try{const{error:a}=await c.from("pedbook_feedbacks").update({status:s}).eq("id",e);if(a)throw a;t(s),r({title:"Status atualizado",description:`Feedback marcado como ${s}`})}catch(a){r({title:"Erro ao atualizar status",description:"Não foi possível atualizar o status do feedback",variant:"destructive"})}},className:"gap-2",variant:"outline",children:d?s.jsxs(s.Fragment,{children:[s.jsx(F,{className:"h-4 w-4 text-green-500"}),"Resolvido"]}):s.jsxs(s.Fragment,{children:[s.jsx(P,{className:"h-4 w-4 text-yellow-500"}),"Aguardando"]})})}function de(){const[e,a]=r.useState([]),[t,d]=r.useState(!0),[i,j]=r.useState({total:0,avgRating:0,betaInterested:0,withContact:0}),{toast:N}=l();r.useEffect((()=>{v()}),[]);const v=async()=>{d(!0);try{const{data:e,error:s}=await c.from("site_visitor_feedbacks").select("*").eq("feedback_type","platform_growth").order("created_at",{ascending:!1});if(s)return void N({title:"Erro ao carregar feedbacks",description:s.message,variant:"destructive"});if(e){a(e);const s=e.length,t=e.reduce(((e,s)=>e+s.rating),0)/s,r=e.filter((e=>e.interested_in_beta)).length,d=e.filter((e=>e.email||e.whatsapp)).length;j({total:s,avgRating:t,betaInterested:r,withContact:d})}}catch(e){N({title:"Erro ao carregar feedbacks",description:"Ocorreu um erro ao carregar os feedbacks de crescimento",variant:"destructive"})}finally{d(!1)}},y=e=>{if(!e)return null;const a={freemium:{label:"Freemium",color:"bg-blue-500"},premium_only:{label:"Premium",color:"bg-purple-500"},free_limited:{label:"Gratuito",color:"bg-green-500"}}[e];return a?s.jsx(u,{className:`${a.color} text-white`,children:a.label}):null};return t?s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"})}):s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[s.jsxs(n,{children:[s.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(m,{className:"text-sm font-medium",children:"Total"}),s.jsx(T,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(x,{children:[s.jsx("div",{className:"text-2xl font-bold",children:i.total}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"feedbacks de crescimento"})]})]}),s.jsxs(n,{children:[s.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(m,{className:"text-sm font-medium",children:"Avaliação Média"}),s.jsx(B,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(x,{children:[s.jsx("div",{className:"text-2xl font-bold",children:i.avgRating.toFixed(1)}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"de 10 estrelas"})]})]}),s.jsxs(n,{children:[s.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(m,{className:"text-sm font-medium",children:"Interessados no Beta"}),s.jsx(h,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(x,{children:[s.jsx("div",{className:"text-2xl font-bold",children:i.betaInterested}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"querem acesso antecipado"})]})]}),s.jsxs(n,{children:[s.jsxs(o,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(m,{className:"text-sm font-medium",children:"Com Contato"}),s.jsx(D,{className:"h-4 w-4 text-muted-foreground"})]}),s.jsxs(x,{children:[s.jsx("div",{className:"text-2xl font-bold",children:i.withContact}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"forneceram email/WhatsApp"})]})]})]}),s.jsx("div",{className:"space-y-4",children:0===e.length?s.jsx(n,{children:s.jsxs(x,{className:"flex flex-col items-center justify-center py-12",children:[s.jsx(g,{className:"h-12 w-12 text-gray-400 mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Nenhum feedback de crescimento"}),s.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center",children:"Ainda não há feedbacks de crescimento da plataforma."})]})}):e.map((e=>{return s.jsxs(n,{className:"overflow-hidden",children:[s.jsx(o,{className:"pb-3",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:`w-8 h-8 rounded-full ${t=e.rating,t>=9?"bg-green-500":t>=7?"bg-yellow-500":t>=5?"bg-orange-500":"bg-red-500"} flex items-center justify-center text-white font-bold text-sm`,children:e.rating}),s.jsxs("div",{children:[s.jsxs(m,{className:"text-base",children:["Feedback #",e.id.slice(-8)]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[s.jsx(q,{className:"h-3 w-3"}),(a=e.created_at,new Date(a).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}))]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[e.interested_in_beta&&s.jsxs(u,{className:"bg-purple-500 text-white",children:[s.jsx(h,{className:"h-3 w-3 mr-1"}),"Beta"]}),y(e.monetization_preference)]})]})}),s.jsxs(x,{className:"space-y-4",children:[(e.email||e.whatsapp)&&s.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.email&&s.jsxs("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[s.jsx(f,{className:"h-3 w-3"}),e.email]}),e.whatsapp&&s.jsxs("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[s.jsx(p,{className:"h-3 w-3"}),e.whatsapp]})]}),e.comment&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(g,{className:"h-4 w-4"}),"Comentário"]}),s.jsx("p",{className:"text-sm text-muted-foreground bg-gray-50 dark:bg-gray-800 p-3 rounded-lg",children:e.comment})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.growth_help&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(I,{className:"h-4 w-4 text-blue-500"}),"Como podemos ajudar"]}),s.jsx("p",{className:"text-sm text-muted-foreground bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:e.growth_help})]}),e.growth_suggestions&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(M,{className:"h-4 w-4 text-yellow-500"}),"Sugestões"]}),s.jsx("p",{className:"text-sm text-muted-foreground bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg",children:e.growth_suggestions})]}),e.current_issues&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(b,{className:"h-4 w-4 text-red-500"}),"Problemas Atuais"]}),s.jsx("p",{className:"text-sm text-muted-foreground bg-red-50 dark:bg-red-900/20 p-3 rounded-lg",children:e.current_issues})]}),e.missing_features&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(I,{className:"h-4 w-4 text-purple-500"}),"Recursos Ausentes"]}),s.jsx("p",{className:"text-sm text-muted-foreground bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg",children:e.missing_features})]})]}),(e.premium_willingness||e.ads_tolerance)&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(L,{className:"h-4 w-4 text-green-500"}),"Preferências de Monetização"]}),s.jsxs("div",{className:"space-y-2",children:[e.premium_willingness&&s.jsxs("p",{className:"text-sm text-muted-foreground",children:[s.jsx("strong",{children:"Disposição a pagar:"})," ",e.premium_willingness]}),e.ads_tolerance&&s.jsxs("p",{className:"text-sm text-muted-foreground",children:[s.jsx("strong",{children:"Tolerância a anúncios:"})," ",e.ads_tolerance]})]})]}),e.beta_expectations&&s.jsxs("div",{children:[s.jsxs("h4",{className:"font-medium text-sm mb-2 flex items-center gap-2",children:[s.jsx(h,{className:"h-4 w-4 text-purple-500"}),"Expectativas do Beta"]}),s.jsx("p",{className:"text-sm text-muted-foreground bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg",children:e.beta_expectations})]})]})]},e.id);var a,t}))})]})}function ie(){const[e,a]=r.useState([]),[t,d]=r.useState(!0),[i,h]=r.useState({}),[f,p]=r.useState({total:0,aguardando:0,resolvido:0}),[b,F]=r.useState(1),[B,q]=r.useState(1),[I,M]=r.useState("regular"),{user:L,isAdmin:ee}=j(),{toast:se}=l(),ae=J(),te=async(e=1)=>{d(!0);try{const{data:s,error:t}=await c.from("pedbook_feedbacks").select("id, feedback_category, feedback_type");if(t)throw t;const r=(s?.filter((e=>"dr_will"!==e.feedback_category&&"dr_will"!==e.feedback_type))||[]).length,d=Math.ceil(r/10);q(d);const i=10*(e-1),l=i+10-1,{data:n,error:o}=await c.from("pedbook_feedbacks").select("*").order("created_at",{ascending:!1});if(o)return void se({title:"Erro ao carregar feedbacks",description:o.message,variant:"destructive"});if(n){const e=n.filter((e=>"dr_will"!==e.feedback_category&&"dr_will"!==e.feedback_type)).slice(i,l+1),s=[...new Set(e.map((e=>e.user_id)))],{data:t}=await c.from("profiles").select("id, full_name").in("id",s),r=e.map((e=>e.id)),{data:d}=await c.from("pedbook_feedback_responses").select("\n            id,\n            feedback_id,\n            message,\n            created_at,\n            user_id\n          ").in("feedback_id",r).order("created_at",{ascending:!0}),o=[...new Set(d?.map((e=>e.user_id))||[])],{data:m}=await c.from("profiles").select("id, full_name, is_admin").in("id",o),x=e.map((e=>{const s=t?.find((s=>s.id===e.user_id))||{full_name:"Usuário",email:"<EMAIL>"},a=(d?.filter((s=>s.feedback_id===e.id))||[]).map((e=>{const s=m?.find((s=>s.id===e.user_id))||{full_name:"Usuário",is_admin:!1};return{...e,user:s}}));return{...e,user:s,responses:a}}));a(x);const h=n.filter((e=>"dr_will"!==e.feedback_category&&"dr_will"!==e.feedback_type)),g=h.length,u=h.filter((e=>"aguardando"===e.status)).length,f=h.filter((e=>"resolvido"===e.status)).length;p({total:g,aguardando:u,resolvido:f})}else a([]),p({total:0,aguardando:0,resolvido:0})}catch(s){se({title:"Erro ao carregar feedbacks",description:"Ocorreu um erro ao carregar os feedbacks",variant:"destructive"})}finally{d(!1)}};r.useEffect((()=>{te(b)}),[b]);const ie=e=>{switch(e){case"pendente":return s.jsx(P,{className:"h-4 w-4 text-yellow-500"});case"em_andamento":return s.jsx(A,{className:"h-4 w-4 text-blue-500"});case"resolvido":return s.jsx(y,{className:"h-4 w-4 text-green-500"});default:return s.jsx(P,{className:"h-4 w-4 text-gray-500"})}};return t?s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:s.jsxs("div",{className:"text-center space-y-4",children:[s.jsx("div",{className:"w-16 h-16 mx-auto border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200",children:"Carregando feedbacks..."}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Buscando todos os feedbacks do sistema"})]})]})})}):s.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[s.jsx("div",{className:"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-8 text-white shadow-xl",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs(N,{variant:"ghost",size:"sm",onClick:()=>ae("/admin/dashboard"),className:"flex items-center gap-2 hover:bg-white/20 text-white border-white/20",children:[s.jsx(v,{className:"h-4 w-4"}),"Voltar"]}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-4xl font-bold mb-2",children:"💬 Gestão de Feedbacks"}),s.jsx("p",{className:"text-blue-100 text-lg",children:"Gerencie todos os feedbacks dos usuários"})]})]}),s.jsx("div",{className:"flex items-center gap-4",children:s.jsxs(N,{variant:"ghost",size:"sm",onClick:()=>te(b),className:"text-white border-white/20 hover:bg-white/20",children:[s.jsx(K,{className:"h-4 w-4 mr-2"}),"Atualizar"]})})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsx(n,{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800",children:s.jsx(x,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-blue-600 dark:text-blue-400 text-sm font-medium",children:"Total"}),s.jsx("p",{className:"text-3xl font-bold text-blue-700 dark:text-blue-300",children:f.total})]}),s.jsx(D,{className:"h-8 w-8 text-blue-500"})]})})}),s.jsx(n,{className:"bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-800",children:s.jsx(x,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-yellow-600 dark:text-yellow-400 text-sm font-medium",children:"Aguardando"}),s.jsx("p",{className:"text-3xl font-bold text-yellow-700 dark:text-yellow-300",children:f.aguardando})]}),s.jsx(P,{className:"h-8 w-8 text-yellow-500"})]})})}),s.jsx(n,{className:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800",children:s.jsx(x,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-green-600 dark:text-green-400 text-sm font-medium",children:"Resolvidos"}),s.jsx("p",{className:"text-3xl font-bold text-green-700 dark:text-green-300",children:f.resolvido})]}),s.jsx(y,{className:"h-8 w-8 text-green-500"})]})})})]}),s.jsxs(w,{value:I,onValueChange:M,className:"space-y-6",children:[s.jsxs(k,{className:"grid w-full grid-cols-2 bg-gray-100 dark:bg-slate-800 rounded-xl p-1",children:[s.jsxs(_,{value:"regular",className:"flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 rounded-lg",children:[s.jsx(g,{className:"h-4 w-4"}),"Feedbacks Regulares"]}),s.jsxs(_,{value:"growth",className:"flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 rounded-lg",children:[s.jsx(T,{className:"h-4 w-4"}),"Feedbacks de Crescimento"]})]}),s.jsxs(C,{value:"regular",className:"space-y-6",children:[0===e.length?s.jsx(S.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center p-12 bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-slate-800 dark:via-slate-800 dark:to-slate-800 rounded-2xl border border-gray-200 dark:border-slate-700 shadow-lg",children:s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"text-6xl",children:"📝"}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-800 dark:text-gray-200",children:"Nenhum feedback encontrado"}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Não há feedbacks para exibir no momento."})]})]})}):s.jsx("div",{className:"space-y-6",children:e.map(((e,t)=>s.jsx(S.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:s.jsxs(n,{className:"overflow-hidden bg-gradient-to-br from-white via-gray-50/30 to-white dark:from-slate-800 dark:via-slate-800/90 dark:to-slate-800 border border-gray-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl",children:[s.jsx(o,{className:"pb-4",children:s.jsxs("div",{className:"flex justify-between items-start gap-4",children:[s.jsxs("div",{className:"space-y-2 flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[ie(e.status),s.jsx(m,{className:"text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 dark:from-gray-200 dark:to-gray-400 bg-clip-text text-transparent",children:e.title})]}),s.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400",children:[s.jsxs("span",{className:"flex items-center gap-1",children:["👤 ",e.user?.full_name||"Usuário anônimo"]}),s.jsxs("span",{className:"flex items-center gap-1",children:["📧 ",e.user?.email||"Email não disponível"]}),s.jsxs("span",{className:"flex items-center gap-1",children:["📅 ",new Date(e.created_at).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})]})]}),e.whatsapp&&s.jsxs("div",{className:"flex items-center gap-1 text-sm text-green-600 dark:text-green-400",children:["📱 WhatsApp: ",e.whatsapp]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(re,{feedbackId:e.id,currentStatus:e.status,onStatusChange:s=>((e,s)=>{a((a=>a.map((a=>a.id===e?{...a,status:s}:a)))),te(b)})(e.id,s)}),ee&&s.jsxs(O,{children:[s.jsx(U,{asChild:!0,children:s.jsx(N,{variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20 border-red-200 dark:border-red-800",children:s.jsx(Q,{className:"h-4 w-4"})})}),s.jsxs(V,{children:[s.jsxs(G,{children:[s.jsx($,{children:"Excluir Feedback"}),s.jsx(W,{children:"Tem certeza que deseja excluir este feedback? Esta ação não pode ser desfeita. Todas as respostas associadas também serão removidas."})]}),s.jsxs(Y,{children:[s.jsx(Z,{children:"Cancelar"}),s.jsx(H,{onClick:()=>(async e=>{try{const{error:s}=await c.from("pedbook_feedback_responses").delete().eq("feedback_id",e);if(s)throw s;const{error:a}=await c.from("pedbook_feedbacks").delete().eq("id",e);if(a)throw a;se({title:"Feedback excluído",description:"O feedback foi excluído com sucesso!",variant:"default"}),te(b)}catch(s){se({title:"Erro ao excluir feedback",description:s.message||"Ocorreu um erro ao excluir o feedback",variant:"destructive"})}})(e.id),className:"bg-red-600 hover:bg-red-700 text-white",children:"Excluir"})]})]})]})]})]})}),s.jsxs(x,{className:"space-y-6",children:[s.jsxs("div",{className:"bg-gray-50 dark:bg-slate-700/50 p-4 rounded-xl border border-gray-200 dark:border-slate-600",children:[s.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"Mensagem:"}),s.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap",children:e.message})]}),s.jsxs("div",{className:"border-t border-gray-200 dark:border-slate-700 pt-6",children:[s.jsxs("h4",{className:"font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2",children:[s.jsx(g,{className:"h-5 w-5"}),s.jsx("span",{children:"Respostas"}),e.responses.length>0&&s.jsx(u,{variant:"secondary",className:"bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",children:e.responses.length})]}),s.jsx(z,{className:"h-[200px] rounded-md border border-gray-200 dark:border-slate-600 p-4 bg-white dark:bg-slate-800/50",children:s.jsxs("div",{className:"space-y-4",children:[e.responses?.map((e=>s.jsxs("div",{className:"bg-gray-50 dark:bg-slate-700/50 p-3 rounded-lg border border-gray-200 dark:border-slate-600",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("span",{className:"font-medium text-gray-800 dark:text-gray-200",children:e.user.full_name}),e.user.is_admin&&s.jsx(u,{className:"bg-blue-500 text-white text-xs",children:"Admin"}),s.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(e.created_at).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",hour:"2-digit",minute:"2-digit"})})]}),s.jsx("div",{className:"text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap",children:e.message})]},e.id))),0===e.responses.length&&s.jsxs("div",{className:"text-center text-gray-500 dark:text-gray-400 py-4",children:[s.jsx(g,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"Nenhuma resposta ainda"})]})]})}),s.jsxs("div",{className:"mt-4 space-y-3",children:[s.jsx(R,{placeholder:"Digite sua resposta...",value:i[e.id]||"",onChange:s=>h((a=>({...a,[e.id]:s.target.value}))),className:"resize-none border-2 border-gray-200 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-700 focus:border-blue-500 dark:focus:border-blue-400",rows:3}),s.jsxs(N,{onClick:()=>(async e=>{if(!L)return;const s=i[e];if(s?.trim())try{const{error:a}=await c.from("pedbook_feedback_responses").insert({feedback_id:e,message:s.trim(),user_id:L.id});if(a)throw a;se({title:"Resposta enviada",description:"Sua resposta foi enviada com sucesso!"}),h((s=>({...s,[e]:""}))),te()}catch(a){se({title:"Erro ao enviar resposta",description:a.message,variant:"destructive"})}})(e.id),disabled:!i[e.id]?.trim(),className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl",children:[s.jsx(g,{className:"h-4 w-4 mr-2"}),"Enviar Resposta"]})]})]})]})]})},e.id)))}),B>1&&s.jsxs("div",{className:"flex items-center justify-center gap-4 mt-8",children:[s.jsxs(N,{variant:"outline",size:"sm",onClick:()=>F((e=>Math.max(1,e-1))),disabled:1===b,className:"flex items-center gap-2",children:[s.jsx(X,{className:"h-4 w-4"}),"Anterior"]}),s.jsx("div",{className:"flex items-center gap-2",children:Array.from({length:B},((e,s)=>s+1)).map((e=>s.jsx(N,{variant:b===e?"default":"outline",size:"sm",onClick:()=>F(e),className:"w-10 h-10 "+(b===e?"bg-gradient-to-r from-blue-600 to-purple-600 text-white":""),children:e},e)))}),s.jsxs(N,{variant:"outline",size:"sm",onClick:()=>F((e=>Math.min(B,e+1))),disabled:b===B,className:"flex items-center gap-2",children:["Próxima",s.jsx(E,{className:"h-4 w-4"})]})]}),s.jsxs("div",{className:"text-center text-sm text-gray-600 dark:text-gray-400 mt-4",children:["Página ",b," de ",B," • ",f.total," feedbacks no total"]})]}),s.jsx(C,{value:"growth",children:s.jsx(de,{})})]})]})}te.displayName=se.displayName;export{ie as default};
