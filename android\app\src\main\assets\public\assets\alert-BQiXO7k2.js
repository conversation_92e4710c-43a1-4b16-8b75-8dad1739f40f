import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{j as r,aF as t}from"./index-CrSshpOb.js";const s=t("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=a.forwardRef((({className:a,variant:t,...i},d)=>e.jsx("div",{ref:d,role:"alert",className:r(s({variant:t}),a),...i})));i.displayName="Alert";const d=a.forwardRef((({className:a,...t},s)=>e.jsx("h5",{ref:s,className:r("mb-1 font-medium leading-none tracking-tight",a),...t})));d.displayName="AlertTitle";const l=a.forwardRef((({className:a,...t},s)=>e.jsx("div",{ref:s,className:r("text-sm [&_p]:leading-relaxed",a),...t})));l.displayName="AlertDescription";export{i as A,d as a,l as b};
