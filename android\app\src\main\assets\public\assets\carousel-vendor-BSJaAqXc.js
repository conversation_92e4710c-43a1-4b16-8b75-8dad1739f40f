import{r as n}from"./critical-DVX9Inzy.js";function t(n){return function(n){return"[object Object]"===Object.prototype.toString.call(n)}(n)||Array.isArray(n)}function e(n,r){const o=Object.keys(n),i=Object.keys(r);return o.length===i.length&&(JSON.stringify(Object.keys(n.breakpoints||{}))===JSON.stringify(Object.keys(r.breakpoints||{}))&&o.every((o=>{const i=n[o],c=r[o];return"function"==typeof i?`${i}`==`${c}`:t(i)&&t(c)?e(i,c):i===c})))}function r(n){return n.concat().sort(((n,t)=>n.name>t.name?1:-1)).map((n=>n.options))}function o(n){return"number"==typeof n}function i(n){return"string"==typeof n}function c(n){return"boolean"==typeof n}function u(n){return"[object Object]"===Object.prototype.toString.call(n)}function s(n){return Math.abs(n)}function a(n){return Math.sign(n)}function l(n,t){return s(n-t)}function d(n){return h(n).map(Number)}function f(n){return n[p(n)]}function p(n){return Math.max(0,n.length-1)}function g(n,t){return t===p(n)}function m(n,t=0){return Array.from(Array(n),((n,e)=>t+e))}function h(n){return Object.keys(n)}function y(n,t){return[n,t].reduce(((n,t)=>(h(t).forEach((e=>{const r=n[e],o=t[e],i=u(r)&&u(o);n[e]=i?y(r,o):o})),n)),{})}function x(n,t){return void 0!==t.MouseEvent&&n instanceof t.MouseEvent}function b(){let n=[];const t={add:function(e,r,o,i={passive:!0}){let c;if("addEventListener"in e)e.addEventListener(r,o,i),c=()=>e.removeEventListener(r,o,i);else{const n=e;n.addListener(o),c=()=>n.removeListener(o)}return n.push(c),t},clear:function(){n=n.filter((n=>n()))}};return t}function v(n=0,t=0){const e=s(n-t);function r(t){return t<n}function o(n){return n>t}function i(n){return r(n)||o(n)}return{length:e,max:t,min:n,constrain:function(e){return i(e)?r(e)?n:t:e},reachedAny:i,reachedMax:o,reachedMin:r,removeOffset:function(n){return e?n-e*Math.ceil((n-t)/e):n}}}function S(n,t,e){const{constrain:r}=v(0,n),o=n+1;let i=c(t);function c(n){return e?s((o+n)%o):r(n)}function u(){return i}function a(){return S(n,u(),e)}const l={get:u,set:function(n){return i=c(n),l},add:function(n){return a().set(u()+n)},clone:a};return l}function w(n,t,e,r,o,i,u,d,f,p,g,m,h,y,S,w,E,O,L){const{cross:D,direction:I}=n,A=["INPUT","SELECT","TEXTAREA"],F={passive:!1},M=b(),k=b(),T=v(50,225).constrain(y.measure(20)),P={mouse:300,touch:400},z={mouse:500,touch:600},H=S?43:25;let j=!1,V=0,B=0,N=!1,R=!1,C=!1,G=!1;function q(n){if(!x(n,r)&&n.touches.length>=2)return $(n);const t=i.readPoint(n),e=i.readPoint(n,D),c=l(t,V),u=l(e,B);if(!R&&!G){if(!n.cancelable)return $(n);if(R=c>u,!R)return $(n)}const s=i.pointerMove(n);c>w&&(C=!0),p.useFriction(.3).useDuration(.75),d.start(),o.add(I(s)),n.preventDefault()}function $(n){const t=g.byDistance(0,!1).index!==m.get(),e=i.pointerUp(n)*(S?z:P)[G?"mouse":"touch"],r=function(n,t){const e=m.add(-1*a(n)),r=g.byDistance(n,!S).distance;return S||s(n)<T?r:E&&t?.5*r:g.byIndex(e.get(),0).distance}(I(e),t),o=function(n,t){if(0===n||0===t)return 0;if(s(n)<=s(t))return 0;const e=l(s(n),s(t));return s(e/n)}(e,r),c=H-10*o,u=O+o/50;R=!1,N=!1,k.clear(),p.useDuration(c).useFriction(u),f.distance(r,!S),G=!1,h.emit("pointerUp")}function U(n){C&&(n.stopPropagation(),n.preventDefault(),C=!1)}return{init:function(n){if(!L)return;function s(s){(c(L)||L(n,s))&&function(n){const c=x(n,r);G=c,C=S&&c&&!n.buttons&&j,j=l(o.get(),u.get())>=2,c&&0!==n.button||function(n){const t=n.nodeName||"";return A.includes(t)}(n.target)||(N=!0,i.pointerDown(n),p.useFriction(0).useDuration(0),o.set(u),function(){const n=G?e:t;k.add(n,"touchmove",q,F).add(n,"touchend",$).add(n,"mousemove",q,F).add(n,"mouseup",$)}(),V=i.readPoint(n),B=i.readPoint(n,D),h.emit("pointerDown"))}(s)}const a=t;M.add(a,"dragstart",(n=>n.preventDefault()),F).add(a,"touchmove",(()=>{}),F).add(a,"touchend",(()=>{})).add(a,"touchstart",s).add(a,"mousedown",s).add(a,"touchcancel",$).add(a,"contextmenu",$).add(a,"click",U,!0)},destroy:function(){M.clear(),k.clear()},pointerDown:function(){return N}}}function E(n,t){let e,r;function o(n){return n.timeStamp}function i(e,r){const o="client"+("x"===(r||n.scroll)?"X":"Y");return(x(e,t)?e:e.touches[0])[o]}return{pointerDown:function(n){return e=n,r=n,i(n)},pointerMove:function(n){const t=i(n)-i(r),c=o(n)-o(e)>170;return r=n,c&&(e=n),t},pointerUp:function(n){if(!e||!r)return 0;const t=i(r)-i(e),c=o(n)-o(e),u=o(n)-o(r)>170,a=t/c;return c&&!u&&s(a)>.1?a:0},readPoint:i}}function O(n,t,e,r,o,i,u){const a=[n].concat(r);let l,d,f=[],p=!1;function g(n){return o.measureSize(u.measure(n))}return{init:function(o){i&&(d=g(n),f=r.map(g),l=new ResizeObserver((e=>{(c(i)||i(o,e))&&function(e){for(const i of e){if(p)return;const e=i.target===n,c=r.indexOf(i.target),u=e?d:f[c];if(s(g(e?n:r[c])-u)>=.5){o.reInit(),t.emit("resize");break}}}(e)})),e.requestAnimationFrame((()=>{a.forEach((n=>l.observe(n)))})))},destroy:function(){p=!0,l&&l.disconnect()}}}function L(n,t,e,r,o){const i=o.measure(10),c=o.measure(50),u=v(.1,.99);let a=!1;function l(){return!a&&!!n.reachedAny(e.get())&&!!n.reachedAny(t.get())}return{shouldConstrain:l,constrain:function(o){if(!l())return;const a=n.reachedMin(t.get())?"min":"max",d=s(n[a]-t.get()),f=e.get()-t.get(),p=u.constrain(d/c);e.subtract(f*p),!o&&s(f)<i&&(e.set(n.constrain(e.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(n){a=!n}}}function D(n,t,e,r){const o=t.min+.1,i=t.max+.1,{reachedMin:c,reachedMax:u}=v(o,i);return{loop:function(t){if(!function(n){return 1===n?u(e.get()):-1===n&&c(e.get())}(t))return;const o=n*(-1*t);r.forEach((n=>n.add(o)))}}}function I(n){let t=n;function e(n){return o(n)?n:n.get()}return{get:function(){return t},set:function(n){t=e(n)},add:function(n){t+=e(n)},subtract:function(n){t-=e(n)}}}function A(n,t){const e="x"===n.scroll?function(n){return`translate3d(${n}px,0px,0px)`}:function(n){return`translate3d(0px,${n}px,0px)`},r=t.style;let o=!1;return{clear:function(){o||(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))},to:function(t){o||(r.transform=e(n.direction(t)))},toggleActive:function(n){o=!n}}}function F(n,t,e,r,o,i,c,u,s){const a=d(o),l=d(o).reverse(),f=function(){const n=c[0];return m(g(l,n),e,!1)}().concat(function(){const n=t-c[0]-1;return m(g(a,n),-e,!0)}());function p(n,t){return n.reduce(((n,t)=>n-o[t]),t)}function g(n,t){return n.reduce(((n,e)=>p(n,t)>0?n.concat([e]):n),[])}function m(o,c,a){const l=function(n){return i.map(((e,o)=>({start:e-r[o]+.5+n,end:e+t-.5+n})))}(c);return o.map((t=>{const r=a?0:-e,o=a?e:0,i=a?"end":"start",c=l[t][i];return{index:t,loopPoint:c,slideLocation:I(-1),translate:A(n,s[t]),target:()=>u.get()>c?r:o}}))}return{canLoop:function(){return f.every((({index:n})=>p(a.filter((t=>t!==n)),t)<=.1))},clear:function(){f.forEach((n=>n.translate.clear()))},loop:function(){f.forEach((n=>{const{target:t,translate:e,slideLocation:r}=n,o=t();o!==r.get()&&(e.to(o),r.set(o))}))},loopPoints:f}}function M(n,t,e){let r,o=!1;return{init:function(i){e&&(r=new MutationObserver((n=>{o||(c(e)||e(i,n))&&function(n){for(const e of n)if("childList"===e.type){i.reInit(),t.emit("slidesChanged");break}}(n)})),r.observe(n,{childList:!0}))},destroy:function(){r&&r.disconnect(),o=!0}}}function k(n,t,e,r,u,y,x){const{align:k,axis:T,direction:P,startIndex:z,loop:H,duration:j,dragFree:V,dragThreshold:B,inViewThreshold:N,slidesToScroll:R,skipSnaps:C,containScroll:G,watchResize:q,watchSlides:$,watchDrag:U,watchFocus:W}=y,J={measure:function(n){const{offsetTop:t,offsetLeft:e,offsetWidth:r,offsetHeight:o}=n;return{top:t,right:e+r,bottom:t+o,left:e,width:r,height:o}}},Q=J.measure(t),X=e.map(J.measure),Y=function(n,t){const e="rtl"===t,r="y"===n,o=!r&&e?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":e?"right":"left",endEdge:r?"bottom":e?"left":"right",measureSize:function(n){const{height:t,width:e}=n;return r?t:e},direction:function(n){return n*o}}}(T,P),K=Y.measureSize(Q),Z=function(n){return{measure:function(t){return n*(t/100)}}}(K),_=function(n,t){const e={start:function(){return 0},center:function(n){return r(n)/2},end:r};function r(n){return t-n}return{measure:function(r,o){return i(n)?e[n](r):n(t,r,o)}}}(k,K),nn=!H&&!!G,tn=H||!!G,{slideSizes:en,slideSizesWithGaps:rn,startGap:on,endGap:cn}=function(n,t,e,r,o,i){const{measureSize:c,startEdge:u,endEdge:a}=n,l=e[0]&&o,d=function(){if(!l)return 0;const n=e[0];return s(t[u]-n[u])}(),p=function(){if(!l)return 0;const n=i.getComputedStyle(f(r));return parseFloat(n.getPropertyValue(`margin-${a}`))}(),m=e.map(c),h=e.map(((n,t,e)=>{const r=!t,o=g(e,t);return r?m[t]+d:o?m[t]+p:e[t+1][u]-n[u]})).map(s);return{slideSizes:m,slideSizesWithGaps:h,startGap:d,endGap:p}}(Y,Q,X,e,tn,u),un=function(n,t,e,r,i,c,u,a,l){const{startEdge:g,endEdge:m,direction:h}=n,y=o(e);return{groupSlides:function(n){return y?function(n,t){return d(n).filter((n=>n%t===0)).map((e=>n.slice(e,e+t)))}(n,e):function(n){return n.length?d(n).reduce(((e,o,d)=>{const y=f(e)||0,x=0===y,b=o===p(n),v=i[g]-c[y][g],S=i[g]-c[o][m],w=!r&&x?h(u):0,E=s(S-(!r&&b?h(a):0)-(v+w));return d&&E>t+l&&e.push(o),b&&e.push(n.length),e}),[]).map(((t,e,r)=>{const o=Math.max(r[e-1]||0);return n.slice(o,t)})):[]}(n)}}}(Y,K,R,H,Q,X,on,cn,2),{snaps:sn,snapsAligned:an}=function(n,t,e,r,o){const{startEdge:i,endEdge:c}=n,{groupSlides:u}=o,a=u(r).map((n=>f(n)[c]-n[0][i])).map(s).map(t.measure),l=r.map((n=>e[i]-n[i])).map((n=>-s(n))),d=u(l).map((n=>n[0])).map(((n,t)=>n+a[t]));return{snaps:l,snapsAligned:d}}(Y,_,Q,X,un),ln=-f(sn)+f(rn),{snapsContained:dn,scrollContainLimit:fn}=function(n,t,e,r){const o=v(-t+n,0),i=e.map(((n,t)=>{const{min:r,max:i}=o,c=o.constrain(n),s=!t,a=g(e,t);return s?i:a||u(r,c)?r:u(i,c)?i:c})).map((n=>parseFloat(n.toFixed(3)))),c=function(){const n=i[0],t=f(i);return v(i.lastIndexOf(n),i.indexOf(t)+1)}();function u(n,t){return l(n,t)<1}return{snapsContained:function(){if(t<=n+2)return[o.max];if("keepSnaps"===r)return i;const{min:e,max:u}=c;return i.slice(e,u)}(),scrollContainLimit:c}}(K,ln,an,G),pn=nn?dn:an,{limit:gn}=function(n,t,e){const r=t[0];return{limit:v(e?r-n:f(t),r)}}(ln,pn,H),mn=S(p(pn),z,H),hn=mn.clone(),yn=d(e),xn=function(n,t,e,r){const o=b(),i=1e3/60;let c=null,u=0,s=0;function a(n){if(!s)return;c||(c=n);const o=n-c;for(c=n,u+=o;u>=i;)e(i),u-=i;r(u/i),s&&t.requestAnimationFrame(a)}function l(){t.cancelAnimationFrame(s),c=null,u=0,s=0}return{init:function(){o.add(n,"visibilitychange",(()=>{n.hidden&&(c=null,u=0)}))},destroy:function(){l(),o.clear()},start:function(){s||(s=t.requestAnimationFrame(a))},stop:l,update:()=>e(i),render:r}}(r,u,(n=>(({dragHandler:n,scrollBody:t,scrollBounds:e,options:{loop:r}},o)=>{r||e.constrain(n.pointerDown()),t.seek(o)})(Tn,n)),(n=>(({scrollBody:n,translate:t,location:e,offsetLocation:r,scrollLooper:o,slideLooper:i,dragHandler:c,animation:u,eventHandler:s,scrollBounds:a,options:{loop:l}},d)=>{const f=n.settled(),p=!a.shouldConstrain(),g=l?f:f&&p;g&&!c.pointerDown()&&(u.stop(),s.emit("settle")),g||s.emit("scroll");const m=e.get()*d+Sn.get()*(1-d);r.set(m),l&&(o.loop(n.direction()),i.loop()),t.to(r.get())})(Tn,n))),bn=pn[mn.get()],vn=I(bn),Sn=I(bn),wn=I(bn),En=I(bn),On=function(n,t,e,r,o){let i=0,c=0,u=o,l=.68,d=n.get(),f=0;function p(n){return u=n,m}function g(n){return l=n,m}const m={direction:function(){return c},duration:function(){return u},velocity:function(){return i},seek:function(t){const o=t/1e3,s=u*o,p=r.get()-n.get();let g=0;return u?(e.set(n),i+=p/s,i*=l,d+=i,n.add(i*o),g=d-f):(i=0,e.set(r),n.set(r),g=p),c=a(g),f=d,m},settled:function(){return s(r.get()-t.get())<.001},useBaseFriction:function(){return g(.68)},useBaseDuration:function(){return p(o)},useFriction:g,useDuration:p};return m}(vn,wn,Sn,En,j),Ln=function(n,t,e,r,o){const{reachedAny:i,removeOffset:c,constrain:u}=r;function l(n){return n.concat().sort(((n,t)=>s(n)-s(t)))[0]}function d(t,r){const o=[t,t+e,t-e];if(!n)return t;if(!r)return l(o);const i=o.filter((n=>a(n)===r));return i.length?l(i):f(o)-e}return{byDistance:function(e,r){const a=o.get()+e,{index:l,distance:f}=function(e){const r=n?c(e):u(e),o=t.map(((n,t)=>({diff:d(n-r,0),index:t}))).sort(((n,t)=>s(n.diff)-s(t.diff))),{index:i}=o[0];return{index:i,distance:r}}(a),p=!n&&i(a);return!r||p?{index:l,distance:e}:{index:l,distance:e+d(t[l]-f,0)}},byIndex:function(n,e){return{index:n,distance:d(t[n]-o.get(),e)}},shortcut:d}}(H,pn,ln,gn,En),Dn=function(n,t,e,r,o,i,c){function u(o){const u=o.distance,s=o.index!==t.get();i.add(u),u&&(r.duration()?n.start():(n.update(),n.render(1),n.update())),s&&(e.set(t.get()),t.set(o.index),c.emit("select"))}return{distance:function(n,t){u(o.byDistance(n,t))},index:function(n,e){const r=t.clone().set(n);u(o.byIndex(r.get(),e))}}}(xn,mn,hn,On,Ln,En,x),In=function(n){const{max:t,length:e}=n;return{get:function(n){return e?(n-t)/-e:0}}}(gn),An=b(),Fn=function(n,t,e,r){const o={};let i,c=null,u=null,s=!1;return{init:function(){i=new IntersectionObserver((n=>{s||(n.forEach((n=>{const e=t.indexOf(n.target);o[e]=n})),c=null,u=null,e.emit("slidesInView"))}),{root:n.parentElement,threshold:r}),t.forEach((n=>i.observe(n)))},destroy:function(){i&&i.disconnect(),s=!0},get:function(n=!0){if(n&&c)return c;if(!n&&u)return u;const t=function(n){return h(o).reduce(((t,e)=>{const r=parseInt(e),{isIntersecting:i}=o[r];return(n&&i||!n&&!i)&&t.push(r),t}),[])}(n);return n&&(c=t),n||(u=t),t}}}(t,e,x,N),{slideRegistry:Mn}=function(n,t,e,r,o,i){const{groupSlides:c}=o,{min:u,max:s}=r;return{slideRegistry:function(){const r=c(i),o=!n||"keepSnaps"===t;return 1===e.length?[i]:o?r:r.slice(u,s).map(((n,t,e)=>{const r=!t,o=g(e,t);return r?m(f(e[0])+1):o?m(p(i)-f(e)[0]+1,f(e)[0]):n}))}()}}(nn,G,pn,fn,un,yn),kn=function(n,t,e,r,i,u,s,a){const l={passive:!0,capture:!0};let d=0;function f(n){"Tab"===n.code&&(d=(new Date).getTime())}return{init:function(p){a&&(u.add(document,"keydown",f,!1),t.forEach(((t,f)=>{u.add(t,"focus",(t=>{(c(a)||a(p,t))&&function(t){if((new Date).getTime()-d>10)return;s.emit("slideFocusStart"),n.scrollLeft=0;const c=e.findIndex((n=>n.includes(t)));o(c)&&(i.useDuration(0),r.index(c,0),s.emit("slideFocus"))}(f)}),l)})))}}}(n,e,Mn,Dn,On,An,x,W),Tn={ownerDocument:r,ownerWindow:u,eventHandler:x,containerRect:Q,slideRects:X,animation:xn,axis:Y,dragHandler:w(Y,n,r,u,En,E(Y,u),vn,xn,Dn,On,Ln,mn,x,Z,V,B,C,.68,U),eventStore:An,percentOfView:Z,index:mn,indexPrevious:hn,limit:gn,location:vn,offsetLocation:wn,previousLocation:Sn,options:y,resizeHandler:O(t,x,u,e,Y,q,J),scrollBody:On,scrollBounds:L(gn,wn,En,On,Z),scrollLooper:D(ln,gn,wn,[vn,wn,Sn,En]),scrollProgress:In,scrollSnapList:pn.map(In.get),scrollSnaps:pn,scrollTarget:Ln,scrollTo:Dn,slideLooper:F(Y,K,ln,en,rn,sn,pn,wn,e),slideFocus:kn,slidesHandler:M(t,x,$),slidesInView:Fn,slideIndexes:yn,slideRegistry:Mn,slidesToScroll:un,target:En,translate:A(Y,t)};return Tn}const T={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function P(n){function t(n,t){return y(n,t||{})}return{mergeOptions:t,optionsAtMedia:function(e){const r=e.breakpoints||{},o=h(r).filter((t=>n.matchMedia(t).matches)).map((n=>r[n])).reduce(((n,e)=>t(n,e)),{});return t(e,o)},optionsMediaQueries:function(t){return t.map((n=>h(n.breakpoints||{}))).reduce(((n,t)=>n.concat(t)),[]).map(n.matchMedia)}}}function z(n,t,e){const r=n.ownerDocument,o=r.defaultView,c=P(o),u=function(n){let t=[];return{init:function(e,r){return t=r.filter((({options:t})=>!1!==n.optionsAtMedia(t).active)),t.forEach((t=>t.init(e,n))),r.reduce(((n,t)=>Object.assign(n,{[t.name]:t})),{})},destroy:function(){t=t.filter((n=>n.destroy()))}}}(c),s=b(),a=function(){let n,t={};function e(n){return t[n]||[]}const r={init:function(t){n=t},emit:function(t){return e(t).forEach((e=>e(n,t))),r},off:function(n,o){return t[n]=e(n).filter((n=>n!==o)),r},on:function(n,o){return t[n]=e(n).concat([o]),r},clear:function(){t={}}};return r}(),{mergeOptions:l,optionsAtMedia:d,optionsMediaQueries:f}=c,{on:p,off:g,emit:m}=a,h=A;let y,x,v,S,w=!1,E=l(T,z.globalOptions),O=l(E),L=[];function D(t){const e=k(n,v,S,r,o,t,a);return t.loop&&!e.slideLooper.canLoop()?D(Object.assign({},t,{loop:!1})):e}function I(t,e){w||(E=l(E,t),O=d(E),L=e||L,function(){const{container:t,slides:e}=O,r=i(t)?n.querySelector(t):t;v=r||n.children[0];const o=i(e)?v.querySelectorAll(e):e;S=[].slice.call(o||v.children)}(),y=D(O),f([E,...L.map((({options:n})=>n))]).forEach((n=>s.add(n,"change",A))),O.active&&(y.translate.to(y.location.get()),y.animation.init(),y.slidesInView.init(),y.slideFocus.init(j),y.eventHandler.init(j),y.resizeHandler.init(j),y.slidesHandler.init(j),y.options.loop&&y.slideLooper.loop(),v.offsetParent&&S.length&&y.dragHandler.init(j),x=u.init(j,L)))}function A(n,t){const e=H();F(),I(l({startIndex:e},n),t),a.emit("reInit")}function F(){y.dragHandler.destroy(),y.eventStore.clear(),y.translate.clear(),y.slideLooper.clear(),y.resizeHandler.destroy(),y.slidesHandler.destroy(),y.slidesInView.destroy(),y.animation.destroy(),u.destroy(),s.clear()}function M(n,t,e){O.active&&!w&&(y.scrollBody.useBaseFriction().useDuration(!0===t?0:O.duration),y.scrollTo.index(n,e||0))}function H(){return y.index.get()}const j={canScrollNext:function(){return y.index.add(1).get()!==H()},canScrollPrev:function(){return y.index.add(-1).get()!==H()},containerNode:function(){return v},internalEngine:function(){return y},destroy:function(){w||(w=!0,s.clear(),F(),a.emit("destroy"),a.clear())},off:g,on:p,emit:m,plugins:function(){return x},previousScrollSnap:function(){return y.indexPrevious.get()},reInit:h,rootNode:function(){return n},scrollNext:function(n){M(y.index.add(1).get(),n,-1)},scrollPrev:function(n){M(y.index.add(-1).get(),n,1)},scrollProgress:function(){return y.scrollProgress.get(y.location.get())},scrollSnapList:function(){return y.scrollSnapList},scrollTo:M,selectedScrollSnap:H,slideNodes:function(){return S},slidesInView:function(){return y.slidesInView.get()},slidesNotInView:function(){return y.slidesInView.get(!1)}};return I(t,e),setTimeout((()=>a.emit("init")),0),j}function H(t={},o=[]){const i=n.useRef(t),c=n.useRef(o),[u,s]=n.useState(),[a,l]=n.useState(),d=n.useCallback((()=>{u&&u.reInit(i.current,c.current)}),[u]);return n.useEffect((()=>{e(i.current,t)||(i.current=t,d())}),[t,d]),n.useEffect((()=>{(function(n,t){if(n.length!==t.length)return!1;const o=r(n),i=r(t);return o.every(((n,t)=>e(n,i[t])))})(c.current,o)||(c.current=o,d())}),[o,d]),n.useEffect((()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&a){z.globalOptions=H.globalOptions;const n=z(a,i.current,c.current);return s(n),()=>n.destroy()}s(void 0)}),[a,s]),[l,u]}z.globalOptions=void 0,H.globalOptions=void 0;export{H as u};
