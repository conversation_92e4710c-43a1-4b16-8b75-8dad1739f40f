import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{c as i,B as o,I as a,Q as n}from"./index-CNG-Xj2g.js";import{B as s}from"./bug-BVNgNMVk.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r=i("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]]),l=i("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),d=()=>{const[i,d]=t.useState(!1),c=()=>{const e=document.body,t=document.documentElement,i=Math.max(e.scrollHeight,e.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight),o=window.innerHeight,a=t.scrollHeight,n=a>o,s=parseInt(getComputedStyle(t).getPropertyValue("--safe-area-inset-top")||"0px"),r=parseInt(getComputedStyle(t).getPropertyValue("--safe-area-inset-bottom")||"0px"),l=o-s-r;return{pageHeight:i,viewportHeight:o,scrollHeight:a,hasVerticalScroll:n,contentOverflow:i>l,safeAreaTop:s,safeAreaBottom:r,availableHeight:l}},h=e=>{try{const t=document.querySelector(e);if(!t)return{error:`Element not found: ${e}`};const i=t.getBoundingClientRect(),o=getComputedStyle(t);return{dimensions:{width:i.width,height:i.height,top:i.top,bottom:i.bottom,scrollHeight:t.scrollHeight,scrollTop:t.scrollTop,clientHeight:t.clientHeight,offsetHeight:t.offsetHeight},css:{height:o.height,maxHeight:o.maxHeight,minHeight:o.minHeight,overflow:o.overflow,overflowY:o.overflowY,display:o.display,flexDirection:o.flexDirection,flex:o.flex,position:o.position,padding:o.padding,margin:o.margin,backgroundColor:o.backgroundColor,background:o.background,zIndex:o.zIndex},classes:t.className,hasScroll:t.scrollHeight>t.clientHeight,elementInfo:{tagName:t.tagName,id:t.id,dataAttributes:(()=>{const e={};for(let i=0;i<t.attributes.length;i++){const o=t.attributes[i];o.name.startsWith("data-")&&(e[o.name]=o.value)}return e})()}}}catch(t){return{error:`Error analyzing ${e}: ${t}`}}},m=()=>({main:h('[data-debug="optimized-main"]'),content:h('[data-debug="optimized-content"]'),tabs:h('[data-debug="optimized-tabs"]'),tabContent:h('[data-debug="optimized-tab-content"]'),prose:h('[data-debug="optimized-prose"]'),viewport:{width:window.innerWidth,height:window.innerHeight},document:{scrollHeight:document.documentElement.scrollHeight,clientHeight:document.documentElement.clientHeight,scrollTop:document.documentElement.scrollTop},body:{scrollHeight:document.body.scrollHeight,clientHeight:document.body.clientHeight,scrollTop:document.body.scrollTop},suspiciousElements:(()=>{const e=[];try{document.querySelectorAll("*").forEach(((t,i)=>{const o=t,a=getComputedStyle(o),n=o.getBoundingClientRect();let s=[];if("hidden"!==a.overflow&&"hidden"!==a.overflowY||s.push(`overflow: ${a.overflow}, overflowY: ${a.overflowY}`),"flex"!==a.display&&"block"!==a.display||n.height>200&&n.height<800&&n.width>300&&s.push(`container: ${n.width}x${n.height}px`),"auto"!==a.height&&"100%"!==a.height&&""!==a.height&&!a.height.includes("calc")){const e=parseFloat(a.height);e>200&&e<800&&s.push(`fixed height: ${a.height}`)}if("absolute"!==a.position&&"fixed"!==a.position||n.height>100&&n.width>200&&s.push(`positioned: ${a.position}`),s.length>0){const t=`${o.tagName.toLowerCase()}${o.id?"#"+o.id:""}${o.className?"."+o.className.split(" ").slice(0,3).join("."):""}`;try{const i=h(`${o.tagName.toLowerCase()}:nth-of-type(${Array.from(o.parentNode?.children||[]).filter((e=>e.tagName===o.tagName)).indexOf(o)+1})`);"dimensions"in i&&e.push({selector:t,element:i,reason:s.join(", ")})}catch(r){}}}))}catch(t){}return e.sort(((e,t)=>e.reason.includes("overflow:")&&!t.reason.includes("overflow:")?-1:!e.reason.includes("overflow:")&&t.reason.includes("overflow:")?1:0)).slice(0,10)})()});return i?e.jsxs("div",{className:"fixed bottom-20 left-4 z-50 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-4 max-w-xs",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"font-semibold text-sm",children:"Debug Global"}),e.jsx(o,{onClick:()=>d(!1),variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:"×"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(o,{onClick:()=>{const e=Array.from(document.querySelectorAll("img")).map((e=>{const t=e.getBoundingClientRect(),i=getComputedStyle(e),o=t.width/(e.naturalWidth||1),a=t.height/(e.naturalHeight||1);return{src:e.src,alt:e.alt||"No alt text",naturalWidth:e.naturalWidth,naturalHeight:e.naturalHeight,displayWidth:t.width,displayHeight:t.height,scaleX:o,scaleY:a,isOversized:o>2||a>2,css:{objectFit:i.objectFit,width:i.width,height:i.height,transform:i.transform}}})),t=c(),i={timestamp:(new Date).toISOString(),page:"Image Analysis",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},images:e,layout:t};navigator.clipboard.writeText(JSON.stringify(i,null,2)).then((()=>{n({title:"Debug copiado!",description:`Análise de ${e.length} imagens copiada para clipboard`})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[e.jsx(a,{className:"w-3 h-3 mr-1"}),"Analisar Imagens"]}),e.jsxs(o,{onClick:()=>{const e=c(),t={timestamp:(new Date).toISOString(),page:"Layout Analysis",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},layout:e};navigator.clipboard.writeText(JSON.stringify(t,null,2)).then((()=>{n({title:"Debug copiado!",description:"Análise de layout copiada para clipboard"})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[e.jsx(r,{className:"w-3 h-3 mr-1"}),"Analisar Layout"]}),e.jsxs(o,{onClick:()=>{const e=m(),t=c(),i={timestamp:(new Date).toISOString(),page:"OptimizedConductsView Analysis",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},layout:t,optimizedLayout:e};navigator.clipboard.writeText(JSON.stringify(i,null,2)).then((()=>{n({title:"Debug OptimizedConductsView copiado!",description:"Análise detalhada do componente copiada para clipboard"})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[e.jsx(l,{className:"w-3 h-3 mr-1"}),"OptimizedConducts"]}),e.jsxs(o,{onClick:()=>{const e=c(),t=m(),i={timestamp:(new Date).toISOString(),page:"Full Analysis (Layout Only)",url:window.location.pathname,viewport:{width:window.innerWidth,height:window.innerHeight,scrollY:window.scrollY},layout:e,optimizedLayout:t};navigator.clipboard.writeText(JSON.stringify(i,null,2)).then((()=>{n({title:"Debug completo copiado!",description:"Análise completa da página copiada para clipboard"})}))},className:"w-full text-xs",size:"sm",variant:"outline",children:[e.jsx(l,{className:"w-3 h-3 mr-1"}),"Análise Completa"]})]})]}):e.jsx(o,{onClick:()=>d(!0),className:"fixed bottom-20 left-4 z-50 bg-red-500 hover:bg-red-600 text-white shadow-lg",size:"sm",children:e.jsx(s,{className:"w-4 h-4"})})};
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */export{d as GlobalDebugger};
