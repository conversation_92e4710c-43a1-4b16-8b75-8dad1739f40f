import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-Cs3LGnoz.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-CFnD44mG.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-BQ6Dqsd-.js";import"./FeedbackTrigger-ot4XwGkJ.js";import"./rocket-BOvZdSbI.js";import"./target-BX0FTOgV.js";import"./zap-DhWHOaH4.js";import"./book-open-CpPiu5Sl.js";import"./star-ShS_gnKj.js";import"./circle-help-B8etwa8R.js";import"./instagram-X0NDZon3.js";import"./collapsible-B6HfSnGs.js";import"./accordion-DNR0EX5j.js";import"./PatientInfoSection-CfauXpeW.js";import"./scale-DoQjTjLO.js";import"./useAgeInput-CLfpowYq.js";import"./calendar--wSGgBN5.js";import"./user-C_sBRWqs.js";import"./DosageDisplay-CH9SNtU2.js";import"./alert-DRNmYOQS.js";import"./plus-OCVKT3ar.js";import"./lightbulb-CgAthiiR.js";import"./external-link-WEoosaxx.js";import"./stethoscope-DLVdj0oT.js";import"./syringe-BNnDekth.js";import"./wind-DaL1n_Qn.js";import"./bug-BEClVQf1.js";import"./chevron-left-CRTmHBdc.js";import"./house-5Ue8MCCY.js";function t(){return r.jsx(o,{})}export{t as default};
