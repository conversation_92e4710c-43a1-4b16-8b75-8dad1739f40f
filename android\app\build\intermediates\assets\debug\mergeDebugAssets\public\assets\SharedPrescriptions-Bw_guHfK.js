import{j as e}from"./radix-core-6kBL75b5.js";import{d as r,s as i,m as s,j as a,R as t,B as o,ap as n,L as c,D as d,e as l,f as m,g as p,a5 as u,ac as x,aL as y,aH as h,aw as g,aM as j,aa as f,a7 as b}from"./index-DwBJcqzE.js";import v from"./Footer-DXia2SVU.js";import{d as _}from"./supabase-vendor-qi_Ptfv-.js";import{r as w}from"./critical-DVX9Inzy.js";import{a as N,c as k,u as q}from"./query-vendor-B-7l6Nb3.js";import{P as C}from"./PrescriptionStatusIndicator-Q56tjNWD.js";import{T as P}from"./thumbs-up-BYwbHFdf.js";import{T as S}from"./thumbs-down-BvKqmc9j.js";import{E as T}from"./eye-DuOKeOhs.js";import{P as A}from"./plus-DseG_YPL.js";import{T as F}from"./trash-2-DVslkler.js";import{A as I}from"./index-HNF4q-pX.js";import{a as K}from"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-BC_TYgH3.js";import"./rocket-7eidEF9E.js";import"./target-D2iN3abh.js";import"./zap-BpuRFf_b.js";import"./book-open-C1lgvyyJ.js";import"./star-BwJL-OtG.js";import"./circle-help-OcogOqeH.js";import"./instagram-Dc9Ip0W1.js";const z=({prescriptionId:t,userId:o,initialLikes:n=0,initialDislikes:c=0})=>{const[d,l]=w.useState(!1),{toast:m}=r(),{prescription:p,userReaction:u}=((e,r)=>{const{data:s}=q({queryKey:["prescription-reactions",e],queryFn:async()=>{const{data:r,error:s}=await i.from("pedbook_prescriptions").select("likes_count, dislikes_count").eq("id",e).maybeSingle();if(s&&"PGRST116"!==s.code)throw s;return r??{likes_count:0,dislikes_count:0}}}),{data:a}=q({queryKey:["user-reaction",e,r],queryFn:async()=>{if(!r)return null;const{data:s,error:a}=await i.from("pedbook_prescription_user_reactions").select("reaction_type").eq("prescription_id",e).eq("user_id",r).maybeSingle();if(a&&"PGRST116"!==a.code)throw a;return s},enabled:!!r});return{prescription:s,userReaction:a}})(t,o),x=(e=>{const{toast:s}=r(),a=N();return k({mutationFn:async({prescriptionId:r,type:s})=>{if(!e)throw new Error("User not authenticated");const{error:a}=await i.rpc("handle_prescription_reaction",{p_user_id:e,p_prescription_id:r,p_reaction_type:s});if(a)throw a;return{success:!0,type:s}},onSuccess:(e,r)=>{a.invalidateQueries({queryKey:["prescription-reactions"]}),a.invalidateQueries({queryKey:["user-reaction"]}),a.invalidateQueries({queryKey:["shared-prescriptions"]}),s({title:"Sucesso",description:("like"===r.type?"Like":"Dislike")+" registrado!"})},onError:e=>{s({title:"Erro",description:"Não foi possível registrar sua reação. Por favor, tente novamente.",variant:"destructive"})}})})(o),y=async e=>{if(o){if(!d)try{l(!0),await x.mutateAsync({prescriptionId:t,type:e})}finally{l(!1)}}else m({title:"Erro",description:"Você precisa estar logado para reagir a uma prescrição",variant:"destructive"})},h=p?.likes_count??n,g=p?.dislikes_count??c;return e.jsxs(s.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"flex items-center gap-1.5 bg-white/50 backdrop-blur-sm rounded-full px-2 py-1 shadow-sm",children:[e.jsxs("button",{onClick:()=>y("like"),disabled:d,className:a("flex items-center gap-1 text-xs font-medium transition-all duration-200","like"===u?.reaction_type?"text-green-500":"text-gray-400 hover:text-green-500"),children:[e.jsx(P,{className:"h-3.5 w-3.5"}),e.jsx("span",{children:h})]}),e.jsx("div",{className:"w-px h-3 bg-gray-200"}),e.jsxs("button",{onClick:()=>y("dislike"),disabled:d,className:a("flex items-center gap-1 text-xs font-medium transition-all duration-200","dislike"===u?.reaction_type?"text-red-500":"text-gray-400 hover:text-red-500"),children:[e.jsx(S,{className:"h-3.5 w-3.5"}),e.jsx("span",{children:g})]})]})},E=({prescription:s,userId:a,onViewDetails:c,onAdd:d,onRemove:l})=>{const{toast:m}=r(),p=N(),{data:u}=q({queryKey:["prescription-added",s.id,a],queryFn:async()=>{if(!a)return!1;const{data:e}=await i.from("pedbook_prescriptions").select("id").eq("user_id",a).eq("name",s.name).maybeSingle();return!!e},enabled:!!a&&s.user_id!==a});return e.jsx(t,{className:"bg-white",children:e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-start justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s.name}),s.description&&e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s.description})]}),e.jsx(C,{isPublic:s.is_public})]}),s.profiles&&e.jsxs("div",{className:"text-sm text-muted-foreground",children:["por ",s.profiles.full_name]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs(o,{variant:"outline",size:"sm",className:"w-full justify-center",onClick:e=>{e.preventDefault(),s.is_public&&m({title:"Prescrição Compartilhada",description:"Esta prescrição está disponível publicamente para todos os usuários."}),c(s)},children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Ver detalhes"]}),a&&s.user_id!==a&&d&&(u?e.jsxs(o,{variant:"outline",size:"sm",className:"w-full justify-center bg-muted cursor-not-allowed",disabled:!0,children:[e.jsx(n,{className:"h-4 w-4 mr-2"}),"Já adicionada"]}):e.jsxs(o,{variant:"outline",size:"sm",className:"w-full justify-center",onClick:async()=>{d&&(await d(s),p.invalidateQueries({queryKey:["prescription-added",s.id,a]}))},children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),"Adicionar"]})),a&&s.user_id===a&&l&&e.jsxs(o,{variant:"outline",size:"sm",className:"w-full justify-center text-destructive hover:text-destructive",onClick:()=>l(s.id),children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Remover"]})]}),s.is_public&&e.jsx(z,{prescriptionId:s.id,userId:a,initialLikes:s.likes_count,initialDislikes:s.dislikes_count})]})})},D=({prescriptions:r,userId:i,isLoading:s,onViewDetails:a,onAdd:t,onRemove:o})=>s?e.jsx("div",{className:"col-span-full flex justify-center items-center py-12",children:e.jsx(c,{className:"h-8 w-8 animate-spin text-primary"})}):r?.length?e.jsx(e.Fragment,{children:r.map((r=>e.jsx(E,{prescription:r,userId:i,onViewDetails:()=>a(r),onAdd:()=>t(r),onRemove:()=>o(r.id)},r.id)))}):e.jsx("div",{className:"col-span-full text-center py-8 bg-muted/20 rounded-lg backdrop-blur-sm",children:e.jsx("p",{className:"text-muted-foreground",children:"Nenhuma prescrição encontrada"})}),R=({prescription:r,onClose:i})=>r?e.jsx(d,{open:!!r,onOpenChange:i,children:e.jsxs(l,{className:"max-w-3xl max-h-[80vh] overflow-y-auto",children:[e.jsx(m,{children:e.jsx(p,{children:r.name})}),e.jsxs("div",{className:"space-y-4",children:[r.profiles&&e.jsxs("div",{className:"text-sm text-gray-500",children:["Criado por: ",r.profiles.full_name," (",r.profiles.formation_area,")"]}),r.description&&e.jsxs("div",{className:"bg-primary/5 rounded-lg p-4 border border-primary/10",children:[e.jsx("h3",{className:"text-sm font-medium mb-2",children:"Descrição"}),e.jsx("p",{className:"text-sm text-gray-600",children:r.description})]}),e.jsx("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg mb-6",children:e.jsx("p",{className:"text-sm text-yellow-800",children:"Os cálculos automáticos de dosagem estão disponíveis apenas na aba de prescrições. Aqui você pode visualizar o modelo da prescrição."})}),e.jsx("div",{className:"space-y-6",children:r.pedbook_prescription_medications?.map((r=>{return e.jsxs("div",{className:"border-l-4 border-primary p-4 bg-primary/5 rounded-r-lg",children:[e.jsx("h3",{className:"font-medium text-lg",children:r.pedbook_medications.name}),r.pedbook_medications.brands&&e.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Marcas: ",r.pedbook_medications.brands]}),r.pedbook_medication_dosages&&e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"font-medium text-sm",children:r.pedbook_medication_dosages.name}),e.jsx("p",{className:"text-sm text-gray-600",children:(i=r.pedbook_medication_dosages.dosage_template,i.replace(/\(\([^)]+\)\)/g,"___"))}),r.pedbook_medication_dosages.summary&&e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:r.pedbook_medication_dosages.summary})]})]},r.id);var i}))})]})]})}):null,L=({searchTerm:r,onSearchChange:i,selectedCategory:a,onCategorySelect:t,categories:n})=>{const[c,d]=w.useState(!1);return e.jsx(s.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"w-full lg:w-72 space-y-4 md:space-y-6",children:e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-primary/10 shadow-lg shadow-primary/5",children:[e.jsx("h2",{className:"text-lg font-semibold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent mb-4 text-center md:text-left",children:"Filtros"}),e.jsxs("div",{className:"space-y-4 md:space-y-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx(u,{type:"search",placeholder:"Pesquisar...",className:"pl-10 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center md:text-left",value:r,onChange:e=>i(e.target.value)}),e.jsx(x,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-primary/40",size:18})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(o,{variant:"outline",className:"w-full flex items-center justify-between border border-primary/20 hover:border-primary/40 transition-colors",onClick:()=>d(!c),children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600",children:"Categorias"}),c?e.jsx(y,{className:"h-4 w-4 text-primary"}):e.jsx(h,{className:"h-4 w-4 text-primary"})]}),c&&e.jsx(g,{className:"h-[300px] pr-4",children:e.jsxs(s.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"space-y-2",children:[e.jsx(o,{variant:null===a?"default":"ghost",className:"w-full justify-center md:justify-start transition-all duration-300 "+(null===a?"bg-primary text-white shadow-md shadow-primary/20":"hover:bg-primary/5"),onClick:()=>t(null),children:"Todas as categorias"}),n?.map((r=>e.jsx(o,{variant:a===r.id?"default":"ghost",className:"w-full justify-center md:justify-start transition-all duration-300 "+(a===r.id?"bg-primary text-white shadow-md shadow-primary/20":"hover:bg-primary/5"),onClick:()=>t(r.id),children:r.name},r.id)))]})})]})]})]})})};function M({userId:a}){const[t,o]=w.useState(""),[n,c]=w.useState(null),[d,l]=w.useState(null),[m,p]=w.useState(0),{data:u}=q({queryKey:["prescription-categories"],queryFn:async()=>{const{data:e,error:r}=await i.from("pedbook_prescription_categories").select("*").order("name");if(r)throw r;return e}}),{data:x,isLoading:y}=(({selectedCategory:e,searchTerm:r,page:s,itemsPerPage:a})=>q({queryKey:["shared-prescriptions",e,r,s],queryFn:async()=>{let t=i.from("pedbook_prescriptions").select("\n          *,\n          pedbook_prescription_medications (\n            id,\n            medication_id,\n            dosage_id,\n            prescription_id,\n            notes,\n            pedbook_medications (\n              name,\n              brands,\n              description,\n              pedbook_medication_categories (\n                name\n              )\n            ),\n            pedbook_medication_dosages (\n              name,\n              dosage_template,\n              summary\n            )\n          ),\n          public_profiles (\n            full_name\n          )\n        ").eq("is_public",!0).order("created_at",{ascending:!1}).order("id");e&&(t=t.eq("category_id",e)),r&&(t=t.ilike("name",`%${r}%`)),t=t.range(s*a,(s+1)*a-1);const{data:o,error:n}=await t;if(n)throw n;return o},staleTime:0,refetchOnMount:!0,refetchOnWindowFocus:!0}))({selectedCategory:n,searchTerm:t,page:m,itemsPerPage:10}),{addPrescriptionMutation:h,removePrescriptionMutation:g}=(e=>{const s=N(),{toast:a}=r();return{addPrescriptionMutation:k({mutationFn:async r=>{if(!e)throw new Error("User not authenticated");const{data:s,error:a}=await i.from("pedbook_prescriptions").insert({name:r.name,description:r.description,patient_weight:r.patient_weight,patient_age:r.patient_age,user_id:e}).select().single();if(a)throw a;if(r.pedbook_prescription_medications?.length){const e=r.pedbook_prescription_medications.map(((e,r)=>({prescription_id:s.id,medication_id:e.medication_id,dosage_id:e.dosage_id,notes:e.notes,display_order:r+1}))),{error:a}=await i.from("pedbook_prescription_medications").insert(e);if(a)throw a}return s},onSuccess:()=>{s.invalidateQueries({queryKey:["prescription-categories"],exact:!1,refetchType:"all"}),s.invalidateQueries({queryKey:["uncategorized-prescriptions"],exact:!1,refetchType:"all"}),a({title:"Prescrição adicionada com sucesso!",description:"A prescrição foi copiada para sua biblioteca."})},onError:()=>{a({variant:"destructive",title:"Erro ao adicionar prescrição",description:"Ocorreu um erro ao tentar adicionar a prescrição."})}}),removePrescriptionMutation:k({mutationFn:async r=>{if(!e)throw new Error("User not authenticated");const{error:s}=await i.from("pedbook_prescriptions").delete().eq("id",r).eq("user_id",e);if(s)throw s},onSuccess:()=>{s.invalidateQueries({queryKey:["prescription-categories"],exact:!1,refetchType:"all"}),s.invalidateQueries({queryKey:["uncategorized-prescriptions"],exact:!1,refetchType:"all"}),a({title:"Prescrição removida",description:"A prescrição foi removida com sucesso."})},onError:()=>{a({variant:"destructive",title:"Erro ao remover prescrição",description:"Ocorreu um erro ao tentar remover a prescrição."})}})}})(a);return e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[e.jsxs("div",{className:"relative min-h-screen",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary-light/20 via-transparent to-accent-pink/20 -z-10"}),e.jsx("div",{className:"absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] -z-10"}),e.jsx("div",{className:"container mx-auto px-2 md:px-4 py-4 md:py-8",children:e.jsxs(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:"flex flex-col lg:flex-row gap-4 md:gap-8",children:[e.jsx(L,{searchTerm:t,onSearchChange:o,selectedCategory:n,onCategorySelect:c,categories:u||[]}),e.jsx("div",{className:"flex-1",children:e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"space-y-4 md:space-y-6",children:[e.jsx("div",{className:"flex items-center justify-center md:justify-between",children:e.jsx("h1",{className:"text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent text-center md:text-left",children:"Prescrições Compartilhadas"})}),e.jsx(I,{mode:"wait",children:e.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6",children:e.jsx(D,{prescriptions:x,userId:a,isLoading:y,onViewDetails:l,onAdd:async e=>{a&&await h.mutateAsync(e)},onRemove:async e=>{a&&await g.mutateAsync(e)}})},`prescriptions-${t}-${n}-${m}`)}),x&&x.length>=10&&e.jsxs(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"flex justify-center gap-3 md:gap-4 mt-4 md:mt-8",children:[e.jsx("button",{className:"px-4 md:px-6 py-2 text-sm font-medium text-primary border border-primary/20 rounded-full hover:bg-primary/5 transition-colors disabled:opacity-50",onClick:()=>p((e=>Math.max(0,e-1))),disabled:0===m,children:"Anterior"}),e.jsxs("span",{className:"flex items-center px-3 md:px-4 bg-white/50 backdrop-blur-sm rounded-full border border-primary/10 text-sm",children:["Página ",m+1]}),e.jsx("button",{className:"px-4 md:px-6 py-2 text-sm font-medium text-primary border border-primary/20 rounded-full hover:bg-primary/5 transition-colors disabled:opacity-50",onClick:()=>p((e=>e+1)),disabled:x.length<10,children:"Próxima"})]})]})})]})})]}),e.jsx(R,{prescription:d,onClose:()=>l(null)})]})}function Q(){const r=K(),i=_.useSession();return e.jsxs("div",{className:j.pageBackground(),children:[e.jsx(f,{}),e.jsxs("div",{className:"flex-1 container mx-auto py-8 px-4",children:[e.jsxs(o,{variant:"ghost",className:"mb-6 text-primary hover:text-primary/80 transition-colors dark:text-blue-400 dark:hover:text-blue-300",onClick:()=>r("/prescriptions"),children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Voltar para prescrições"]}),e.jsx(M,{userId:i?.user?.id})]}),e.jsx(v,{})]})}export{Q as default};
