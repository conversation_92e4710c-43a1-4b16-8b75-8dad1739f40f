import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{u as s}from"./query-vendor-B-7l6Nb3.js";import{s as r,R as t,W as c,Z as i,$ as n,U as d,B as o,L as l,an as m,a5 as h,aD as x,V as u,aE as p,ak as v,ay as g}from"./index-DwBJcqzE.js";import{A as _,b as j}from"./alert-XCiMnFRg.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const f=()=>{const[f,b]=a.useState(15),[C,A]=a.useState([]),[N,y]=a.useState(!1),[w,S]=a.useState(!1),[E,$]=a.useState([]),[D,I]=a.useState({current:0,total:0,item:"",details:""}),[B,V]=a.useState("with-class-not-validated"),{data:P,isLoading:R,refetch:L}=s({queryKey:["active-ingredients-validation",B],queryFn:()=>{switch(B){case"with-class-not-validated":default:return async function(){try{const{data:e,error:a}=await r.from("active_ingredients").select("\n        id,\n        name,\n        dcb_code,\n        cas_number,\n        cas_dcb_validated,\n        active_ingredient_therapeutic_classes!inner(\n          therapeutic_class_id\n        )\n      ").eq("cas_dcb_validated",!1).order("name").limit(1e3);if(a)throw a;return e?.map((e=>({id:e.id,name:e.name,dcb_code:e.dcb_code,cas_number:e.cas_number,cas_dcb_validated:e.cas_dcb_validated})))||[]}catch(e){throw e}}();case"with-class-validated":return async function(){try{const{data:e,error:a}=await r.from("active_ingredients").select("\n        id,\n        name,\n        dcb_code,\n        cas_number,\n        cas_dcb_validated,\n        active_ingredient_therapeutic_classes!inner(\n          therapeutic_class_id\n        )\n      ").eq("cas_dcb_validated",!0).order("name").limit(1e3);if(a)throw a;return e?.map((e=>({id:e.id,name:e.name,dcb_code:e.dcb_code,cas_number:e.cas_number,cas_dcb_validated:e.cas_dcb_validated})))||[]}catch(e){throw e}}();case"without-class":return async function(){try{const{data:e,error:a}=await r.from("active_ingredients").select("\n        id,\n        name,\n        dcb_code,\n        cas_number,\n        cas_dcb_validated\n      ").not("id","in","(\n        SELECT active_ingredient_id\n        FROM active_ingredient_therapeutic_classes\n        WHERE active_ingredient_id IS NOT NULL\n      )").order("name").limit(1e3);if(a)throw a;return e?.map((e=>({id:e.id,name:e.name,dcb_code:e.dcb_code,cas_number:e.cas_number,cas_dcb_validated:e.cas_dcb_validated})))||[]}catch(e){throw e}}()}},staleTime:3e5}),F=(e,a)=>{$((s=>[...s,{type:e,message:a,timestamp:new Date}]))},O=a=>"approved"===a.status?e.jsx(u,{className:"h-4 w-4 text-green-600"}):"rejected"===a.status?e.jsx(p,{className:"h-4 w-4 text-red-600"}):e.jsx(v,{className:"h-4 w-4 text-yellow-600"}),k=a=>"approved"===a.status?e.jsx(g,{variant:"default",className:"bg-green-600",children:"Aprovado"}):"rejected"===a.status?e.jsx(g,{variant:"destructive",children:"Rejeitado"}):e.jsx(g,{variant:"secondary",children:"Pendente"}),X=a=>"alta"===a?e.jsx(g,{className:"bg-green-100 text-green-800",children:"Alta Confiança"}):"média"===a?e.jsx(g,{className:"bg-yellow-100 text-yellow-800",children:"Média Confiança"}):e.jsx(g,{className:"bg-red-100 text-red-800",children:"Baixa Confiança"}),M=(e,a)=>"n.d."===e||!e||("DCB"===a?/^\d{5}$/.test(e):/^\d{3,4}-\d{2}-\d$/.test(e)),T=a=>{const s=M(a.correct_dcb,"DCB"),r=M(a.correct_cas,"CAS");return s&&r?null:e.jsxs("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700",children:["⚠️ ",e.jsx("strong",{children:"FORMATO INVÁLIDO:"}),!s&&" DCB deve ter 5 dígitos (ex: 02744)",!r&&" CAS deve ter formato XXX-XX-X (ex: 103-90-2)"]})};return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Validação CAS/DCB"}),e.jsx("p",{className:"text-muted-foreground",children:"Validação e correção de códigos CAS e DCB dos princípios ativos"})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(t,{children:[e.jsxs(c,{children:[e.jsx(i,{className:"flex items-center gap-2",children:"🧪 Validação CAS/DCB com IA"}),e.jsx(n,{children:"Esta ferramenta usa IA (Google Gemini 2.5 Flash) com PROCESSAMENTO PARALELO REAL (3 chaves API) para validar códigos CAS e DCB. A IA consulta bases oficiais da ANVISA (DCB) e Chemical Abstracts Service (CAS) para garantir precisão máxima. Processa 3x mais itens: se você escolher 15, validará 45 itens (15 por chave API simultaneamente)! Aprove/rejeite as correções antes de aplicar ao banco de dados."})]}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg",children:[e.jsx(o,{onClick:()=>V("with-class-not-validated"),variant:"with-class-not-validated"===B?"default":"outline",size:"sm",children:"🎯 Com Classe + Não Validado"}),e.jsx(o,{onClick:()=>V("with-class-validated"),variant:"with-class-validated"===B?"default":"outline",size:"sm",children:"✅ Com Classe + Já Validado"}),e.jsx(o,{onClick:()=>V("without-class"),variant:"without-class"===B?"default":"outline",size:"sm",children:"⚠️ Sem Classe Terapêutica"})]}),e.jsxs("div",{className:"flex flex-wrap gap-4 items-end",children:[e.jsx(o,{onClick:async()=>{try{await L(),F("success","Lista de princípios ativos recarregada")}catch(e){F("error","Erro ao recarregar lista")}},disabled:R||N||w,variant:"outline",children:R?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"mr-2 h-4 w-4 animate-spin"}),"Carregando..."]}):"with-class-not-validated"===B?"Carregar Não Validados":"with-class-validated"===B?"Carregar Já Validados":"Carregar Sem Classe"}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(m,{htmlFor:"batchSize",className:"text-xs",children:"Base (x3 = Total)"}),e.jsx(h,{id:"batchSize",type:"number",min:"5",max:"15",value:f,onChange:e=>b(Math.max(5,Math.min(15,parseInt(e.target.value)||15))),disabled:N||w,className:"w-20",title:`Validará ${3*f} itens total (${f} por chave API)`})]}),e.jsx(o,{onClick:async()=>{if(P&&0!==P.length){y(!0),A([]),I({current:0,total:0,item:"",details:""}),F("info",`Iniciando validação CAS/DCB de ${3*f} princípios ativos...`);try{const e=await async function(e,a=15,s){const t=Math.min(3*a,e.length),c=e.slice(0,t),i=[];s?.(0,1,"",`Preparando ${t} princípios ativos para validação CAS/DCB PARALELA (${a} x 3 chaves)...`);try{const e=[],a=Math.ceil(c.length/3);for(let s=0;s<3;s++){const r=s*a,t=Math.min(r+a,c.length);r<c.length&&e.push(c.slice(r,t))}e.forEach(((e,a)=>{})),s?.(1,1,`${c.length} itens total`,`🚀 Validando ${e.length} lotes paralelos: ${e.map((e=>e.length)).join(" + ")} = ${c.length} itens`);const t=e.map((async(e,a)=>{try{const s=await async function(e){return await async function(e){try{const a=e.map((e=>e.name)),s=e.map((e=>({dcb_code:e.dcb_code||"n.d.",cas_number:e.cas_number||"n.d."}))),{data:t,error:c}=await r.functions.invoke("validate-cas-dcb",{body:{active_principles:a,current_data:s}});if(c)throw c;if(!t?.success)throw new Error(t?.error||"Erro na validação CAS/DCB");return t.data.results}catch(a){throw a}}(e)}(e);if(!s.every((e=>e&&e.medication_name&&void 0!==e.correct_dcb&&void 0!==e.correct_cas&&"boolean"==typeof e.dcb_is_correct&&"boolean"==typeof e.cas_is_correct)))throw new Error(`Estrutura de resultados inválida do lote ${a+1}`);return e.map(((e,r)=>({ingredient:e,validation:s[r],batchIndex:a,success:!0})))}catch(s){return{batchIndex:a,success:!1,error:s instanceof Error?s.message:"Erro desconhecido",failedItems:e,isEmpty:!0}}})),n=await Promise.all(t),d=[],o=[];let l=0;if(n.forEach(((e,a)=>{Array.isArray(e)?d.push(...e):e.isEmpty&&(o.push(e),l+=e.failedItems.length)})),o.length>0){if(o.map((e=>e.batchIndex+1)).join(", "),s?.(1,1,`${d.length} de ${c.length} itens`,`⚠️ FALHA PARCIAL: ${d.length} validados, ${l} falharam (chaves API com problema)`),l>d.length)throw new Error(`Falha crítica: ${l} itens falharam vs ${d.length} sucessos. Verifique as chaves API.`)}else s?.(1,1,`${d.length} itens validados`,`✅ Validação CAS/DCB PARALELA concluída! ${d.length} códigos validados com sucesso`);const m=d;s?.(1,1,`${m.length} itens validados`,`✅ Validação CAS/DCB concluída! ${m.length} códigos validados`);for(const s of m){const{ingredient:e,validation:a}=s;i.push({id:e.id,original_name:e.name,current_dcb:a.current_dcb,current_cas:a.current_cas,correct_dcb:a.correct_dcb,correct_cas:a.correct_cas,dcb_is_correct:a.dcb_is_correct,cas_is_correct:a.cas_is_correct,needs_correction:a.needs_correction,confidence_level:a.confidence_level,status:"pending",validation:a})}s?.(1,1,"",`🎉 Validação CAS/DCB PARALELA concluída! ${i.length} itens prontos para revisão.`)}catch(n){const e=n instanceof Error?n.message:"Erro desconhecido";s?.(1,1,"",`❌ Erro na validação paralela: ${e}`);for(const a of c)i.push({id:a.id,original_name:a.name,current_dcb:a.dcb_code||"n.d.",current_cas:a.cas_number||"n.d.",correct_dcb:"ERRO",correct_cas:"ERRO",dcb_is_correct:!1,cas_is_correct:!1,needs_correction:!0,confidence_level:"baixa",status:"rejected",validation:{medication_name:a.name,current_dcb:a.dcb_code||"n.d.",current_cas:a.cas_number||"n.d.",correct_dcb:"ERRO",correct_cas:"ERRO",dcb_is_correct:!1,cas_is_correct:!1,needs_correction:!0,confidence_level:"baixa"}})}return i}(P,f,((e,a,s,r)=>{I({current:e,total:a,item:s,details:r}),F("info",`[${e}/${a}] ${s}: ${r}`)}));A(e),F("success",`Validação concluída! ${e.length} itens para revisão.`)}catch(e){const a=e instanceof Error?e.message:"Erro desconhecido";a.includes("chaves API")?(F("error",`❌ FALHA NAS CHAVES API: ${a}`),F("info","💡 Verifique se GEMINI_API_KEY2 e GEMINI_API_KEY3 estão configuradas corretamente no Supabase"),F("info","🔄 Tente novamente ou use um lote menor para validar apenas com a chave principal")):F("error",`Erro durante a validação: ${a}`)}finally{y(!1),I({current:0,total:0,item:"",details:""})}}else F("error","Nenhum princípio ativo disponível para validação")},disabled:!P||0===P.length||N||w,children:N?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"mr-2 h-4 w-4 animate-spin"}),"Validando..."]}):`🚀 Validar ${3*f} Itens (${f}x3)`}),C.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(o,{onClick:()=>{const e=C.filter((e=>"pending"===e.status)).length;A((e=>e.map((e=>({...e,status:"approved"}))))),F("success",`${e} itens aprovados em lote`)},disabled:N||w,variant:"outline",children:"✅ Aprovar Todos"}),e.jsx(o,{onClick:async()=>{const e=C.filter((e=>"approved"===e.status));if(0!==e.length){S(!0),F("info",`Aplicando ${e.length} correções CAS/DCB aprovadas...`);try{const a=await async function(e){let a=0,s=0;const t=[];for(let o=0;o<e.length;o++){const l=e[o];try{c=o+1,i=e.length,n=l.original_name,F("info",`[${c}/${i}] Aplicando correção: ${n}`);const d={cas_dcb_validated:!0};l.dcb_is_correct||"n.d."===l.correct_dcb||(d.dcb_code=l.correct_dcb),l.cas_is_correct||"n.d."===l.correct_cas||(d.cas_number=l.correct_cas);const{error:m}=await r.from("active_ingredients").update(d).eq("id",l.id);m?(t.push(`${l.original_name}: ${m.message}`),s++):a++}catch(d){const e=d instanceof Error?d.message:"Erro desconhecido";t.push(`${l.original_name}: ${e}`),s++}}var c,i,n;return{success:a,failed:s,errors:t}}(e);a.success>0&&F("success",`✅ ${a.success} correções CAS/DCB aplicadas com sucesso!`),a.failed>0&&(F("error",`❌ ${a.failed} correções falharam`),a.errors.forEach((e=>F("error",e)))),A((e=>e.filter((e=>"approved"!==e.status)))),await L(),x({title:0===a.failed?"Sucesso":"Parcialmente Concluído",description:`${a.success} correções aplicadas${a.failed>0?`, ${a.failed} falharam`:""}`,variant:0===a.failed?"default":"destructive"})}catch(a){const e=a instanceof Error?a.message:"Erro desconhecido";F("error",`Erro ao aplicar correções: ${e}`),x({title:"Erro",description:"Erro ao aplicar correções CAS/DCB",variant:"destructive"})}finally{S(!1)}}else F("warning","Nenhum item aprovado para aplicar")},disabled:N||w||0===C.filter((e=>"approved"===e.status)).length,variant:"default",children:w?e.jsxs(e.Fragment,{children:[e.jsx(l,{className:"mr-2 h-4 w-4 animate-spin"}),"Aplicando..."]}):`💾 Aplicar ${C.filter((e=>"approved"===e.status)).length} Aprovados`})]})]}),N&&D.total>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:D.item}),e.jsxs("span",{children:[D.current,"/",D.total]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:D.current/D.total*100+"%"}})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:D.details})]}),P&&e.jsx(_,{children:e.jsxs(j,{children:["📊 ",e.jsx("strong",{children:P.length})," princípios ativos disponíveis para validação CAS/DCB","with-class-not-validated"===B&&e.jsxs("div",{className:"mt-2 text-sm text-green-700",children:["🎯 ",e.jsx("strong",{children:"IDEAL:"})," Estes princípios têm classe terapêutica mas ainda não foram validados para CAS/DCB"]}),"with-class-validated"===B&&e.jsxs("div",{className:"mt-2 text-sm text-blue-700",children:["✅ ",e.jsx("strong",{children:"Já Processados:"})," Estes princípios já foram validados para CAS/DCB"]}),"without-class"===B&&e.jsxs("div",{className:"mt-2 text-sm text-yellow-700",children:["⚠️ ",e.jsx("strong",{children:"Atenção:"})," Estes princípios ainda não têm classe terapêutica definida"]})]})})]})]}),C.length>0&&e.jsxs(t,{children:[e.jsxs(c,{children:[e.jsx(i,{children:"Resultados da Validação CAS/DCB"}),e.jsx(n,{children:"Revise as correções sugeridas pela IA antes de aplicar"})]}),e.jsx(d,{children:e.jsx("div",{className:"space-y-4",children:C.map(((a,s)=>{return e.jsxs("div",{className:"border rounded-lg p-4 space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[O(a),e.jsx("h3",{className:"font-medium",children:a.original_name}),(r=a.dcb_is_correct,t=a.cas_is_correct,c=a.confidence_level,r&&t?"baixa"===c?e.jsx(g,{variant:"outline",className:"border-yellow-500 text-yellow-700",children:"⚠ Corretos (Baixa Confiança)"}):e.jsx(g,{className:"bg-green-600",children:"✓ Ambos Corretos"}):r||t?e.jsx(g,{variant:"secondary",children:"⚠ Parcialmente Correto"}):e.jsx(g,{variant:"destructive",children:"✗ Ambos Incorretos"}))]}),e.jsxs("div",{className:"flex items-center gap-2",children:[k(a),X(a.confidence_level)]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"DCB:"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:a.dcb_is_correct?"text-green-600":"text-red-600",children:a.current_dcb}),!a.dcb_is_correct&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"→"}),e.jsx("span",{className:"text-green-600 font-medium",children:a.correct_dcb})]})]})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"CAS:"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:a.cas_is_correct?"text-green-600":"text-red-600",children:a.current_cas}),!a.cas_is_correct&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"→"}),e.jsx("span",{className:"text-green-600 font-medium",children:a.correct_cas})]})]})]})]}),T(a),"pending"===a.status&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(o,{size:"sm",onClick:()=>(e=>{A((a=>a.map(((a,s)=>s===e?{...a,status:"approved"}:a)))),F("info",`Item aprovado: ${C[e].original_name}`)})(s),variant:"outline",className:"text-green-600 border-green-600 hover:bg-green-50",children:"✅ Aprovar"}),e.jsx(o,{size:"sm",onClick:()=>(e=>{A((a=>a.map(((a,s)=>s===e?{...a,status:"rejected"}:a)))),F("info",`Item rejeitado: ${C[e].original_name}`)})(s),variant:"outline",className:"text-red-600 border-red-600 hover:bg-red-50",children:"❌ Rejeitar"})]})]},a.id);var r,t,c}))})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(t,{children:[e.jsx(c,{children:e.jsx(i,{className:"text-lg",children:"📚 Códigos Oficiais Conhecidos"})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-2 text-xs",children:[e.jsx("div",{className:"font-medium text-green-700",children:"✅ Códigos Verificados (ANVISA/CAS):"}),e.jsxs("div",{className:"space-y-1 text-gray-600",children:[e.jsx("div",{children:"• Paracetamol: DCB=02744, CAS=103-90-2"}),e.jsx("div",{children:"• Dipirona Sódica: DCB=02123, CAS=68-89-3"}),e.jsx("div",{children:"• Ácido Acetilsalicílico: DCB=02471, CAS=50-78-2"}),e.jsx("div",{children:"• Ibuprofeno: DCB=02674, CAS=15687-27-1"}),e.jsx("div",{children:"• Amoxicilina: DCB=02345, CAS=26787-78-0"}),e.jsx("div",{children:"• Diclofenaco: DCB=02555, CAS=15307-79-6"}),e.jsx("div",{children:"• Omeprazol: DCB=02890, CAS=73590-58-6"})]}),e.jsxs("div",{className:"mt-3 p-2 bg-blue-50 rounded text-blue-700",children:[e.jsx("strong",{children:"Formatos:"})," DCB = 5 dígitos | CAS = XXX-XX-X"]})]})})]}),e.jsxs(t,{children:[e.jsx(c,{children:e.jsx(i,{className:"text-lg",children:"Logs do Sistema"})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:[E.slice(-20).reverse().map(((a,s)=>e.jsxs("div",{className:"text-xs p-2 rounded "+("error"===a.type?"bg-red-50 text-red-700":"success"===a.type?"bg-green-50 text-green-700":"warning"===a.type?"bg-yellow-50 text-yellow-700":"bg-blue-50 text-blue-700"),children:[e.jsx("div",{className:"font-medium",children:a.timestamp.toLocaleTimeString()}),e.jsx("div",{children:a.message})]},s))),0===E.length&&e.jsx("div",{className:"text-xs text-muted-foreground text-center py-4",children:"Nenhum log ainda"})]})})]})]})]})]})};export{f as default};
