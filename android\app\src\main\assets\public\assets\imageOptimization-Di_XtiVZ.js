const e=async(e,a={})=>{const{maxWidth:i=1920,maxHeight:r=1080,quality:n=.8,format:o="webp",maintainAspectRatio:h=!0}=a;return new Promise(((a,m)=>{const g=document.createElement("canvas"),c=g.getContext("2d"),d=new Image;d.onload=()=>{try{let{width:s,height:w}=t(d.width,d.height,i,r,h);g.width=s,g.height=w,c?.drawImage(d,0,0,s,w),g.toBlob((t=>{if(!t)return void m(new Error("Falha ao comprimir imagem"));const i=new File([t],`${e.name.split(".")[0]}.${o}`,{type:`image/${o}`,lastModified:Date.now()});a(i)}),`image/${o}`,n)}catch(s){m(s)}},d.onerror=()=>{m(new Error("Falha ao carregar imagem"))},d.src=URL.createObjectURL(e)}))},t=(e,t,a,i,r=!0)=>{if(!r)return{width:Math.min(e,a),height:Math.min(t,i)};const n=e/t;let o=e,h=t;return o>a&&(o=a,h=o/n),h>i&&(h=i,o=h*n),{width:Math.round(o),height:Math.round(h)}},a=e=>["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type),i=(e,t)=>Math.round((e-t)/e*100);export{i as a,e as c,a as i};
