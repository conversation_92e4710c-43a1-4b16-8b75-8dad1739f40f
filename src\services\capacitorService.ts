/**
 * Serviço para gerenciar funcionalidades nativas do Capacitor
 */

import { StatusBar, Style } from '@capacitor/status-bar';
import { SplashScreen } from '@capacitor/splash-screen';
import { Keyboard } from '@capacitor/keyboard';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Filesystem, Directory } from '@capacitor/filesystem';
import { isCapacitor, isAndroid, isIOS, getPlatformConfig } from '@/utils/capacitorUtils';

class CapacitorService {
  private initialized = false;

  /**
   * Inicializa todos os plugins nativos
   */
  async initialize(): Promise<void> {
    if (!isCapacitor() || this.initialized) return;

    try {
      await this.setupStatusBar();
      await this.setupSplashScreen();
      await this.setupKeyboard();
      
      this.initialized = true;
      console.log('✅ Capacitor plugins initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing Capacitor plugins:', error);
    }
  }

  /**
   * Configura a status bar
   */
  private async setupStatusBar(): Promise<void> {
    if (!isCapacitor()) return;

    try {
      const config = getPlatformConfig();
      
      if (isAndroid()) {
        await StatusBar.setStyle({ style: Style.Dark });
        await StatusBar.setBackgroundColor({ color: '#2563eb' });
      } else if (isIOS()) {
        await StatusBar.setStyle({ style: Style.Light });
      }

      await StatusBar.show();
    } catch (error) {
      console.warn('Status bar setup failed:', error);
    }
  }

  /**
   * Configura a splash screen
   */
  private async setupSplashScreen(): Promise<void> {
    if (!isCapacitor()) return;

    try {
      // Aguardar um pouco para garantir que o app carregou
      setTimeout(async () => {
        await SplashScreen.hide();
      }, 2000);
    } catch (error) {
      console.warn('Splash screen setup failed:', error);
    }
  }

  /**
   * Configura o teclado
   */
  private async setupKeyboard(): Promise<void> {
    if (!isCapacitor()) return;

    try {
      // Listener para quando o teclado aparece
      Keyboard.addListener('keyboardWillShow', (info) => {
        document.body.style.paddingBottom = `${info.keyboardHeight}px`;
      });

      // Listener para quando o teclado desaparece
      Keyboard.addListener('keyboardWillHide', () => {
        document.body.style.paddingBottom = '0px';
      });
    } catch (error) {
      console.warn('Keyboard setup failed:', error);
    }
  }

  /**
   * Feedback háptico
   */
  async hapticFeedback(style: 'light' | 'medium' | 'heavy' = 'light'): Promise<void> {
    if (!isCapacitor()) return;

    try {
      const impactStyle = {
        light: ImpactStyle.Light,
        medium: ImpactStyle.Medium,
        heavy: ImpactStyle.Heavy
      }[style];

      await Haptics.impact({ style: impactStyle });
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  /**
   * Capturar foto
   */
  async capturePhoto(): Promise<string | null> {
    if (!isCapacitor()) return null;

    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera
      });

      return image.dataUrl || null;
    } catch (error) {
      console.error('Photo capture failed:', error);
      return null;
    }
  }

  /**
   * Salvar arquivo
   */
  async saveFile(fileName: string, data: string): Promise<boolean> {
    if (!isCapacitor()) return false;

    try {
      await Filesystem.writeFile({
        path: fileName,
        data: data,
        directory: Directory.Documents
      });

      return true;
    } catch (error) {
      console.error('File save failed:', error);
      return false;
    }
  }

  /**
   * Ler arquivo
   */
  async readFile(fileName: string): Promise<string | null> {
    if (!isCapacitor()) return null;

    try {
      const result = await Filesystem.readFile({
        path: fileName,
        directory: Directory.Documents
      });

      return result.data as string;
    } catch (error) {
      console.error('File read failed:', error);
      return null;
    }
  }

  /**
   * Verificar se arquivo existe
   */
  async fileExists(fileName: string): Promise<boolean> {
    if (!isCapacitor()) return false;

    try {
      await Filesystem.stat({
        path: fileName,
        directory: Directory.Documents
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Atualizar cor da status bar baseada no tema
   */
  async updateStatusBarForTheme(isDark: boolean): Promise<void> {
    if (!isCapacitor()) return;

    try {
      if (isAndroid()) {
        await StatusBar.setStyle({ 
          style: isDark ? Style.Dark : Style.Light 
        });
        await StatusBar.setBackgroundColor({ 
          color: isDark ? '#1f2937' : '#2563eb' 
        });
      } else if (isIOS()) {
        await StatusBar.setStyle({ 
          style: isDark ? Style.Dark : Style.Light 
        });
      }
    } catch (error) {
      console.warn('Status bar theme update failed:', error);
    }
  }
}

// Instância singleton
export const capacitorService = new CapacitorService();
