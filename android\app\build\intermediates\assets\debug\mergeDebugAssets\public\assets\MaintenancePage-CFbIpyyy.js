import{j as e}from"./radix-core-6kBL75b5.js";import{c as s,b9 as a,d as r,R as i,W as n,Z as t,$ as o,U as d,aE as c,V as l,m,an as x,av as u,T as j,a5 as h,ak as p,aj as g}from"./index-Dq2DDcRF.js";import{r as v,b as N}from"./critical-DVX9Inzy.js";import{S as f}from"./switch-COmgW4TX.js";import{A as b,h as y,a as w,b as M,c as A,d as S,e as C,f as T,g as k}from"./alert-dialog-LAt5xMzV.js";import{W as D}from"./wrench-Ch8OtR2m.js";import{C as q}from"./clock-BOr3Qjov.js";import{A as z,b as V}from"./alert-Dq2jwNFL.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=s("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]]),_=()=>{const{maintenanceStatus:s,isMaintenanceActive:g,isSuperAdmin:z,toggleMaintenance:V}=a(),{toast:_}=r(),[E,F]=v.useState(!1),[U,I]=v.useState(s?.message||""),[O,P]=v.useState(s?.estimated_duration||"");return N.useEffect((()=>{s&&(I(s.message||""),P(s.estimated_duration||""))}),[s]),z?e.jsxs(i,{className:"w-full max-w-2xl",children:[e.jsxs(n,{children:[e.jsxs(t,{className:"flex items-center gap-2",children:[e.jsx(D,{className:"h-5 w-5"}),"Modo de Manutenção"]}),e.jsx(o,{children:"Controle o acesso ao site durante manutenções e atualizações"})]}),e.jsxs(d,{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-muted/50",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 rounded-full "+(g?"bg-red-100 text-red-600":"bg-green-100 text-green-600"),children:g?e.jsx(c,{className:"h-4 w-4"}):e.jsx(l,{className:"h-4 w-4"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:g?"Site em Manutenção":"Site Funcionando"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:g?"Usuários estão sendo redirecionados":"Todos os usuários têm acesso normal"})]})]}),e.jsx(m.div,{animate:{scale:g?[1,1.1,1]:1},transition:{duration:2,repeat:g?1/0:0},children:e.jsx(R,{className:"h-6 w-6 "+(g?"text-red-500":"text-green-500")})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(x,{htmlFor:"message",className:"flex items-center gap-2",children:[e.jsx(u,{className:"h-4 w-4"}),"Mensagem para usuários"]}),e.jsx(j,{id:"message",placeholder:"Digite a mensagem que será exibida na página de manutenção...",value:U,onChange:e=>I(e.target.value),rows:3})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(x,{htmlFor:"duration",className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-4 w-4"}),"Duração estimada (opcional)"]}),e.jsx(h,{id:"duration",placeholder:"Ex: 2 horas, 30 minutos, etc.",value:O,onChange:e=>P(e.target.value)})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 rounded-lg border",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"font-medium",children:"Ativar Modo de Manutenção"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Redireciona todos os usuários (exceto você) para a página de manutenção"})]}),e.jsxs(b,{children:[e.jsx(y,{asChild:!0,children:e.jsx(f,{checked:g,disabled:E})}),e.jsxs(w,{children:[e.jsxs(M,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5 text-amber-500"}),g?"Desativar":"Ativar"," Modo de Manutenção"]}),e.jsx(S,{children:g?"Tem certeza que deseja desativar o modo de manutenção? O site voltará ao funcionamento normal.":"Tem certeza que deseja ativar o modo de manutenção? Todos os usuários serão redirecionados para a página de manutenção."})]}),e.jsxs(C,{children:[e.jsx(T,{children:"Cancelar"}),e.jsx(k,{onClick:()=>(async e=>{if(z){F(!0);try{await V(e,U||"Site em manutenção. Voltaremos em breve!",O),_({title:e?"Manutenção ativada":"Manutenção desativada",description:e?"Todos os usuários serão redirecionados para a página de manutenção":"O site voltou ao funcionamento normal",variant:e?"destructive":"default"})}catch(s){_({title:"Erro",description:"Não foi possível alterar o modo de manutenção",variant:"destructive"})}finally{F(!1)}}else _({title:"Acesso negado",description:"Apenas super admin pode alterar o modo de manutenção",variant:"destructive"})})(!g),className:g?"bg-green-600 hover:bg-green-700":"bg-red-600 hover:bg-red-700",children:g?"Desativar":"Ativar"})]})]})]})]}),s?.activated_at&&e.jsx("div",{className:"text-sm text-muted-foreground p-3 bg-muted/30 rounded-lg",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Última alteração:"})," ",new Date(s.activated_at).toLocaleString("pt-BR")]})})]})]}):null},E=()=>{const{isSuperAdmin:s,isMaintenanceActive:r}=a();return s?e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-4xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-orange-100 rounded-lg",children:e.jsx(D,{className:"h-6 w-6 text-orange-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Modo de Manutenção"}),e.jsx("p",{className:"text-gray-600",children:"Controle o acesso ao site durante manutenções e atualizações"})]})]}),r&&e.jsxs(z,{variant:"destructive",className:"mb-6",children:[e.jsx(p,{className:"h-4 w-4"}),e.jsxs(V,{children:[e.jsx("strong",{children:"Site em Manutenção:"})," Todos os usuários (exceto você) estão sendo redirecionados para a página de manutenção."]})]})]}),e.jsxs("div",{className:"grid gap-6 mb-8",children:[e.jsxs(i,{className:"border-blue-200 bg-blue-50/50",children:[e.jsx(n,{children:e.jsxs(t,{className:"flex items-center gap-2 text-blue-800",children:[e.jsx(g,{className:"h-5 w-5"}),"Como Funciona"]})}),e.jsxs(d,{className:"text-blue-700 space-y-2",children:[e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Ativado:"})," Todos os usuários são redirecionados para a página de manutenção"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Super Admin:"})," Você continua com acesso total ao site"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Verificação:"})," O sistema verifica o status a cada 30 segundos"]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Desativado:"})," Usuários voltam automaticamente ao site normal"]})]})]}),e.jsxs(i,{className:"border-amber-200 bg-amber-50/50",children:[e.jsx(n,{children:e.jsxs(t,{className:"flex items-center gap-2 text-amber-800",children:[e.jsx(p,{className:"h-5 w-5"}),"Cuidados Importantes"]})}),e.jsxs(d,{className:"text-amber-700 space-y-2",children:[e.jsx("p",{children:"• Use apenas durante manutenções reais ou atualizações críticas"}),e.jsx("p",{children:"• Informe uma mensagem clara e tempo estimado para os usuários"}),e.jsx("p",{children:"• Teste sempre em ambiente de desenvolvimento primeiro"}),e.jsx("p",{children:"• Mantenha comunicação com a equipe durante a manutenção"})]})]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx(_,{})}),e.jsx("div",{className:"mt-8",children:e.jsxs(i,{children:[e.jsxs(n,{children:[e.jsx(t,{children:"Informações Técnicas"}),e.jsx(o,{children:"Detalhes sobre o funcionamento do sistema de manutenção"})]}),e.jsx(d,{className:"space-y-4",children:e.jsxs("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Banco de Dados"}),e.jsxs("p",{className:"text-gray-600",children:["Status armazenado na tabela ",e.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"site_maintenance"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Verificação"}),e.jsx("p",{className:"text-gray-600",children:"Polling automático a cada 30 segundos via React Query"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Redirecionamento"}),e.jsxs("p",{className:"text-gray-600",children:["Automático via hook ",e.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"useMaintenanceMode"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Permissões"}),e.jsx("p",{className:"text-gray-600",children:"Apenas super admin (<EMAIL>) pode alterar"})]})]})})]})})]}):e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsxs(z,{variant:"destructive",children:[e.jsx(p,{className:"h-4 w-4"}),e.jsx(V,{children:"Acesso negado. Apenas super administradores podem acessar esta página."})]})})};export{E as default};
