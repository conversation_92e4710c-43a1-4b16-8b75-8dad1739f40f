import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{ad as s,a6 as i,ae as t,af as r,ag as o,ah as n,aj as c,ax as d,U as l,B as m,s as p,X as h,ao as x,D as u,e as g,T as y,d as f,f as j,g as b,z as v,aH as _,m as w,ac as N,aq as k,aM as C,a9 as S,ab as P,a8 as q}from"./index-D89HBjcn.js";import{u as T,a as M,c as F}from"./query-vendor-B-7l6Nb3.js";import E from"./Footer-CliqTtAT.js";import{D as A,f as $,c as z}from"./DosageDisplay-CuTo0RSr.js";import{P as O}from"./plus-7ON-wNau.js";import{a as I}from"./router-BAzpOxbo.js";import{S as D}from"./stethoscope-C94gb3Zp.js";import{C as W}from"./clipboard-list-BexXoRpK.js";import{P as K}from"./PrescriptionStatusIndicator-DWTWvet0.js";import{u as B}from"./useAgeInput-CLfpowYq.js";import{C as L}from"./copy-QoO_C4a_.js";import{P as U}from"./pencil-B9pAnueM.js";import{T as Q}from"./trash-2-DldO7Ekc.js";import{a as R}from"./date-vendor-BOcTQe0E.js";import{d as V}from"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CnktnpVq.js";import"./rocket-BrzzCnNA.js";import"./target-jx5HoNTw.js";import"./zap-3JoB1_vc.js";import"./book-open-JvVCwLVv.js";import"./star-DPJhgPy1.js";import"./circle-help-Bt3O9hrS.js";import"./instagram-BUkbqcNN.js";import"./alert-B4jfnKuF.js";const H={neonatal:"Dosagem neonatal",pediatric:"Dosagem pediátrica",adult:"Dosagem adulta"},G=({onSelect:h,weight:x,age:u})=>{const[g,y]=a.useState(""),[f,j]=a.useState(""),[b,v]=a.useState(""),[_,w]=a.useState("all"),{data:N}=T({queryKey:["medications"],queryFn:async()=>{const{data:e,error:a}=await p.from("pedbook_medications").select("\n          *,\n          pedbook_medication_categories (\n            id,\n            name\n          ),\n          pedbook_medication_dosages (\n            id,\n            name,\n            dosage_template,\n            summary,\n            age_group\n          )\n        ");if(a)throw a;return e}}),{data:k}=T({queryKey:["medication-categories"],queryFn:async()=>{const{data:e,error:a}=await p.from("pedbook_medication_categories").select("*");if(a)throw a;return e}}),C=N?.filter((e=>{const a=e.name.toLowerCase().includes(b.toLowerCase()),s="all"===_||e.category_id===_;return a&&s})),S=N?.find((e=>e.id===g));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(i,{placeholder:"Buscar medicamento...",value:b,onChange:e=>v(e.target.value),className:"pl-10"})]}),e.jsxs(t,{value:_,onValueChange:w,children:[e.jsx(r,{className:"w-[200px] bg-white",children:e.jsx(o,{placeholder:"Todas categorias"})}),e.jsxs(n,{className:"bg-white",children:[e.jsx(c,{value:"all",children:"Todas categorias"}),k?.map((a=>e.jsx(c,{value:a.id,children:a.name},a.id)))]})]})]}),e.jsx(d,{className:"h-[200px] rounded-md border",children:e.jsx("div",{className:"p-4 space-y-2",children:C?.map((a=>e.jsxs("div",{className:"p-4 rounded-lg cursor-pointer transition-colors border "+(g===a.id?"bg-primary text-primary-foreground border-primary":"hover:bg-gray-50 border-border"),onClick:()=>{y(a.id),j("")},children:[e.jsx("div",{className:"font-medium",children:a.name}),a.pedbook_medication_categories&&e.jsx("div",{className:"text-sm mt-1 opacity-80",children:a.pedbook_medication_categories.name})]},a.id)))})}),S&&e.jsxs(l,{className:"p-4 space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-lg",children:S.name}),S.pedbook_medication_categories?.name&&e.jsx("span",{className:"text-sm text-muted-foreground",children:S.pedbook_medication_categories.name}),S.description&&e.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:S.description})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Selecione a dosagem:"}),e.jsx(d,{className:"h-[200px] rounded-md border",children:e.jsx("div",{className:"p-4 space-y-2",children:S.pedbook_medication_dosages?.map((a=>e.jsxs("div",{className:"p-4 rounded-lg cursor-pointer transition-colors border "+(f===a.id?"bg-primary text-primary-foreground border-primary":"hover:bg-gray-50 border-border"),onClick:()=>j(a.id),children:[e.jsx("div",{className:"font-medium",children:a.name}),e.jsx("div",{className:"text-sm mt-1 opacity-90",children:H[a.age_group]})]},a.id)))})})]}),e.jsx(m,{type:"button",onClick:()=>h(g,f),disabled:!f,className:"w-full",children:"Adicionar Medicamento"})]})]})},J=({selectedMedications:a,medications:s,onRemove:t,onUpdateSectionTitle:r,onUpdateQuantity:o,weight:n,age:c})=>e.jsx(d,{className:"h-[300px] rounded-md border p-4",children:e.jsx("div",{className:"space-y-4",children:a.map(((a,d)=>{const l=s?.find((e=>e.id===a.medicationId)),p=l?.pedbook_medication_dosages?.find((e=>e.id===a.dosageId));return l&&p?e.jsxs("div",{className:"relative border rounded-lg p-4 space-y-3",children:[e.jsx(m,{variant:"ghost",size:"icon",className:"absolute top-2 right-2",onClick:()=>t(d),children:e.jsx(h,{className:"h-4 w-4"})}),e.jsx(i,{placeholder:"Título da seção (opcional)",value:a.sectionTitle||"",onChange:e=>r(d,e.target.value),className:"max-w-sm"}),e.jsx(i,{placeholder:"Quantidade (ex: 1 FR, 20 Comprimidos)",value:a.quantity||"",onChange:e=>o(d,e.target.value),className:"max-w-sm"}),e.jsxs("div",{className:"pt-2",children:[e.jsx("h4",{className:"font-semibold mb-2",children:l.name}),e.jsx(A,{dosage:{...p,medication_id:l.id},weight:n,age:c})]})]},d):null}))})});function X({selectedMedications:s,onSelectMedication:i,weight:t,age:r}){const[o,n]=a.useState(!1),{data:c}=T({queryKey:["medications"],queryFn:async()=>{const{data:e,error:a}=await p.from("pedbook_medications").select("\n          *,\n          pedbook_medication_dosages (\n            id,\n            name,\n            dosage_template,\n            summary\n          )\n        ");if(a)throw a;return e}});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(x,{children:"Medicamentos"}),e.jsxs(m,{type:"button",variant:"default",onClick:()=>n(!0),className:"w-full max-w-[200px]",children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Adicionar Medicamento"]})]}),e.jsx(J,{selectedMedications:s,medications:c||[],onRemove:e=>{const a=[...s];a.splice(e,1),i(a)},onUpdateSectionTitle:(e,a)=>{const t=[...s];t[e]={...t[e],sectionTitle:a||void 0},i(t)},onUpdateQuantity:(e,a)=>{const t=[...s];t[e]={...t[e],quantity:a||void 0},i(t)},weight:t,age:r}),e.jsx(u,{open:o,onOpenChange:n,children:e.jsx(g,{className:"max-w-3xl max-h-[80vh] overflow-y-auto",children:e.jsx(G,{onSelect:(e,a)=>{i([...s,{medicationId:e,dosageId:a}]),n(!1)},weight:t,age:r})})})]})}function Y({name:a,description:s,notes:t,onNameChange:r,onDescriptionChange:o,onNotesChange:n}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(x,{htmlFor:"name",children:"Nome da Prescrição"}),e.jsx(i,{id:"name",value:a,onChange:e=>r(e.target.value),placeholder:"Ex: IVAS",required:!0})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"description",children:"Descrição"}),e.jsx(y,{id:"description",value:s,onChange:e=>o(e.target.value),placeholder:"Descrição da prescrição (opcional)"})]}),e.jsxs("div",{children:[e.jsx(x,{htmlFor:"notes",children:"Observações"}),e.jsx(y,{id:"notes",value:t,onChange:e=>n(e.target.value),placeholder:"Observações adicionais (opcional)"})]})]})}function Z({isEditing:a,onCancel:s}){return e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(m,{type:"button",variant:"outline",onClick:s,children:"Cancelar"}),e.jsx(m,{type:"submit",children:a?"Atualizar Prescrição":"Criar Prescrição"})]})}function ee({onSuccess:s,session:i,prescription:t,onClose:r}){const[o,n]=a.useState(t?.name||""),[c,d]=a.useState(t?.description||""),[l,m]=a.useState(t?.notes||""),[h,x]=a.useState(t?.pedbook_prescription_medications?.map((e=>({medicationId:e.medication_id,dosageId:e.dosage_id,sectionTitle:e.section_title||void 0,quantity:e.quantity||void 0})))||[]),{toast:u}=f(),g=I();return e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{if(t){const{error:e}=await p.from("pedbook_prescriptions").update({name:o,description:c,notes:l,category_id:null}).eq("id",t.id);if(e)throw e;const{error:a}=await p.from("pedbook_prescription_medications").delete().eq("prescription_id",t.id);if(a)throw a}else{const{data:e,error:a}=await p.from("pedbook_prescriptions").insert({name:o,description:c,notes:l,category_id:null,user_id:i.user.id}).select().single();if(a)throw a;t=e}if(h.length>0){const e=h.map(((e,a)=>({prescription_id:t.id,medication_id:e.medicationId,dosage_id:e.dosageId,section_title:e.sectionTitle||null,quantity:e.quantity||null,display_order:a+1}))),{error:a}=await p.from("pedbook_prescription_medications").insert(e);if(a)throw a}u({title:t?"Prescrição atualizada com sucesso!":"Prescrição criada com sucesso!",description:"Você será redirecionado para a página de prescrições."}),s?s():g("/prescriptions")}catch(a){u({variant:"destructive",title:t?"Erro ao atualizar prescrição":"Erro ao criar prescrição",description:a.message})}},className:"space-y-6",children:[e.jsx(Y,{name:o,description:c,notes:l,onNameChange:n,onDescriptionChange:d,onNotesChange:m}),e.jsx(X,{selectedMedications:h,onSelectMedication:x,weight:t?.patient_weight||0,age:t?.patient_age||0}),e.jsx(Z,{isEditing:!!t,onCancel:r})]})}function ae({session:s,onSuccess:i}){const[t,r]=a.useState(!1),{toast:o}=f(),n=M();return e.jsxs(u,{open:t,onOpenChange:r,children:[e.jsxs(m,{onClick:()=>r(!0),children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Nova Prescrição"]}),e.jsxs(g,{className:"max-w-3xl max-h-[90vh] overflow-y-auto rounded-2xl",children:[e.jsxs(j,{children:[e.jsx(b,{children:"Nova Prescrição"}),e.jsx(v,{children:"Crie uma nova prescrição preenchendo os campos abaixo."})]}),e.jsx(ee,{session:s,onSuccess:()=>{r(!1),n.invalidateQueries({queryKey:["prescription-categories"],exact:!1,refetchType:"all"}),n.invalidateQueries({queryKey:["uncategorized-prescriptions"],exact:!1,refetchType:"all"}),o({title:"Prescrição criada com sucesso!",description:"A prescrição foi adicionada à sua lista."}),i?.(),setTimeout((()=>{window.location.reload()}),500)},onClose:()=>r(!1)})]})]})}const se=({searchTerm:a,onSearchChange:t})=>e.jsxs("div",{className:"relative",children:[e.jsx(s,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(i,{type:"search",placeholder:"Pesquisar prescrições...",className:"pl-10 bg-white/50 border-gray-200",value:a,onChange:e=>t(e.target.value)})]}),ie=({categories:a,uncategorizedPrescriptions:s,selectedPrescription:i,searchTerm:t,onPrescriptionSelect:r})=>{const o=a?.filter((e=>e.pedbook_prescriptions?.some((e=>e.name.toLowerCase().includes(t.toLowerCase()))))),n=s?.filter((e=>e.name.toLowerCase().includes(t.toLowerCase())));return e.jsx(l,{className:"p-2",children:e.jsx(d,{className:"h-[300px]",children:e.jsxs("div",{className:"space-y-4 pr-4",children:[o?.map((a=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-sm text-gray-500 uppercase tracking-wider",children:a.name}),e.jsx("div",{className:"space-y-1",children:a.pedbook_prescriptions?.filter((e=>e.name.toLowerCase().includes(t.toLowerCase()))).map((a=>e.jsx(m,{variant:i===a.id?"default":"ghost",className:"w-full justify-start text-left transition-all duration-200 "+(i===a.id?"bg-primary text-white shadow-md":"hover:bg-primary/5"),onClick:()=>r(a.id),children:e.jsx("span",{className:"font-medium line-clamp-1",children:a.name})},a.id)))})]},a.id))),n&&n.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-sm text-gray-500 uppercase tracking-wider",children:"Sem categoria"}),e.jsx("div",{className:"space-y-1",children:n.map((a=>e.jsx(m,{variant:i===a.id?"default":"ghost",className:"w-full justify-start text-left transition-all duration-200 "+(i===a.id?"bg-primary text-white shadow-md":"hover:bg-primary/5"),onClick:()=>r(a.id),children:e.jsx("span",{className:"font-medium line-clamp-1",children:a.name})},a.id)))})]})]})})})},te=({selectedPrescription:s,onPrescriptionSelect:i,searchTerm:t,onSearchChange:r,session:o})=>{const n=M(),[c,d]=a.useState(!1),{data:l,isLoading:h}=T({queryKey:["prescription-categories",o?.user?.id],queryFn:async()=>{if(!o?.user?.id)throw new Error("Not authenticated");const{data:e,error:a}=await p.from("pedbook_prescription_categories").select("*").eq("user_id",o.user.id).order("name");if(a)throw a;const{data:s,error:i}=await p.from("pedbook_prescriptions").select("*").eq("user_id",o.user.id).not("category_id","is",null).order("created_at",{ascending:!1});if(i)throw i;return e.map((e=>({...e,pedbook_prescriptions:s.filter((a=>a.category_id===e.id))})))},enabled:!!o?.user?.id,staleTime:0,refetchOnMount:!0,refetchOnWindowFocus:!0}),{data:x,isLoading:u}=T({queryKey:["uncategorized-prescriptions",o?.user?.id],queryFn:async()=>{if(!o?.user?.id)throw new Error("Not authenticated");const{data:e,error:a}=await p.from("pedbook_prescriptions").select("*").eq("user_id",o.user.id).is("category_id",null).order("created_at",{ascending:!1});if(a)throw a;return e},enabled:!!o?.user?.id,staleTime:0,refetchOnMount:!0,refetchOnWindowFocus:!0});return e.jsx("aside",{className:"lg:w-80 bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 lg:h-[calc(100vh-8rem)] flex flex-col",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex flex-col items-center justify-center space-y-4",children:o&&e.jsx(ae,{session:o,onSuccess:()=>{n.invalidateQueries({queryKey:["prescription-categories"],exact:!1,refetchType:"all"}),n.invalidateQueries({queryKey:["uncategorized-prescriptions"],exact:!1,refetchType:"all"})}})}),e.jsxs("div",{className:"block lg:hidden",children:[e.jsxs(m,{variant:"outline",className:"w-full flex items-center justify-between p-4",onClick:()=>d(!c),children:[e.jsx("span",{className:"font-medium",children:(()=>{if(!s)return"Selecione uma prescrição";for(const a of l||[]){const e=a.pedbook_prescriptions?.find((e=>e.id===s));if(e)return e.name}const e=x?.find((e=>e.id===s));return e?e.name:"Selecione uma prescrição"})()}),e.jsx(_,{className:"h-4 w-4 transition-transform "+(c?"transform rotate-180":"")})]}),c&&e.jsx("div",{className:"mt-2",children:e.jsx(se,{searchTerm:t,onSearchChange:r})})]}),e.jsx("div",{className:"hidden lg:block",children:e.jsx(se,{searchTerm:t,onSearchChange:r})}),e.jsx("div",{className:(c?"block":"hidden")+" lg:block",children:e.jsx(ie,{categories:l,uncategorizedPrescriptions:x,selectedPrescription:s,searchTerm:t,onPrescriptionSelect:e=>{i(e),d(!1)}})})]})})},re=async(e,a,s,i,t)=>{if(!e)return{text:""};let r=e,o=!0;try{let e,n,c=t;if(!c){const{data:e,error:a}=await p.from("pedbook_medication_tags").select("name, multiplier, max_value, type, start_month, end_month, start_weight, end_weight, round_result").eq("medication_id",i).eq("is_user_medication",!1);if(a)throw a;c=e||[]}if(c&&c.length>0){const i=c.filter((e=>("age"===e.type||"multiplier_by_fixed_age"===e.type)&&null!==e.start_month&&e.start_month>0)),t=c.filter((e=>"fixed_by_weight"===e.type&&null!==e.start_weight&&e.start_weight>0));i.length>0&&(e=Math.min(...i.map((e=>e.start_month||1/0))),s<e&&(o=!0)),t.length>0&&(n=Math.min(...t.map((e=>e.start_weight||1/0))),a<n&&(o=!0));const d=new Map;for(const e of c){let i;if("fixed_by_weight"===e.type){const s=c.find((s=>s.name===e.name&&"fixed_by_weight"===s.type&&a>=(s.start_weight||0)&&a<=(s.end_weight||1/0)));s?(i=s.multiplier||0,i>0&&(o=!1),s.round_result&&(i=Math.round(i))):i=0}else if("age"===e.type){const a=c.find((a=>a.name===e.name&&"age"===a.type&&s>=(a.start_month||0)&&s<=(a.end_month||1/0)));a?(i=a.multiplier||0,i>0&&(o=!1),a.round_result&&(i=Math.round(i))):i=0}else if("multiplier_by_fixed_age"===e.type){const t=c.find((a=>a.name===e.name&&"multiplier_by_fixed_age"===a.type&&s>=(a.start_month||0)&&s<=(a.end_month||1/0)));t?(i=a*(t.multiplier||0),i>0&&(o=!1),t.max_value&&i>t.max_value&&(i=t.max_value),t.round_result&&(i=Math.round(i))):i=0}else if("fixed"===e.type)i=e.multiplier||0,i>0&&(o=!1),e.round_result&&(i=Math.round(i));else{const s=e.multiplier||0;i=a*s,i>0&&(o=!1),e.max_value&&i>e.max_value&&(i=e.max_value),e.round_result&&(i=Math.round(i))}if(void 0!==i){const a=d.get(e.name);(void 0===a||0!==i&&0===a||0===a&&0===i)&&d.set(e.name,i)}}for(const[e,a]of d.entries()){const s=new RegExp(`\\(\\(${e}\\)\\)`,"g");let i;i=0===a?"0":a>0&&a<.001?a.toFixed(4).replace(".",","):a>0&&a<.01?a.toFixed(3).replace(".",","):$(a),r=r.replace(s,i)}const l=/\(\(([^)]+)\)\)/g;r=r.replace(l,"0")}r=r.replace("{weight}",$(a)).replace("{age}",s.toString());const d={text:r};return o&&(e||n)&&(e&&n?d.restrictions={type:"both",minAge:e,minWeight:n}:e?d.restrictions={type:"age",minAge:e}:n&&(d.restrictions={type:"weight",minWeight:n})),d}catch(n){return{text:e||"Erro ao calcular dosagem"}}},oe=()=>e.jsxs(e.Fragment,{children:[e.jsx("h2",{className:"text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent dark:from-blue-400 dark:to-blue-600",children:"Bem-vindo ao Sistema de Prescrições PedBook!"}),e.jsxs("div",{className:"space-y-4 text-gray-600 dark:text-gray-300",children:[e.jsx("p",{className:"text-base md:text-lg",children:"O PedBook é uma plataforma especializada para profissionais de saúde gerenciarem suas prescrições médicas de forma eficiente e segura."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 py-4",children:[e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"flex flex-col items-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg shadow-sm",children:[e.jsx("div",{className:"h-10 w-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3",children:e.jsx(O,{className:"h-6 w-6 text-primary dark:text-blue-400"})}),e.jsx("h3",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Passo 1"}),e.jsx("p",{className:"text-sm text-center dark:text-gray-300",children:'Clique no botão "Nova Prescrição" abaixo'})]}),e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"flex flex-col items-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg shadow-sm",children:[e.jsx("div",{className:"h-10 w-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3",children:e.jsx(D,{className:"h-6 w-6 text-primary dark:text-blue-400"})}),e.jsx("h3",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Passo 2"}),e.jsx("p",{className:"text-sm text-center dark:text-gray-300",children:"Selecione os medicamentos e configure as dosagens"})]}),e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex flex-col items-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg shadow-sm",children:[e.jsx("div",{className:"h-10 w-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3",children:e.jsx(W,{className:"h-6 w-6 text-primary dark:text-blue-400"})}),e.jsx("h3",{className:"font-medium text-gray-800 dark:text-gray-200 mb-2",children:"Passo 3"}),e.jsx("p",{className:"text-sm text-center dark:text-gray-300",children:"Revise e salve sua prescrição"})]})]}),e.jsxs("ul",{className:"space-y-2 text-center list-none text-sm md:text-base dark:text-gray-300",children:[e.jsx("li",{children:"Crie e organize suas prescrições personalizadas"}),e.jsx("li",{children:"Mantenha um histórico organizado de suas prescrições"})]})]})]}),ne=({prescriptions:a,onSelectPrescription:s})=>a&&0!==a.length?e.jsxs("div",{className:"mt-8 pt-8 border-t border-primary/10 dark:border-primary/30",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-200 mb-4",children:"Suas Prescrições Recentes"}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 max-w-xl mx-auto",children:a.map((a=>e.jsx(w.div,{whileHover:{scale:1.02},className:"bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md transition-all cursor-pointer border border-primary/10 dark:border-primary/30",onClick:()=>s(a.id),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-4 w-4 text-primary/60 dark:text-blue-400/80 flex-shrink-0"}),e.jsx("h4",{className:"font-medium text-gray-800 dark:text-gray-200 text-sm truncate",children:a.name})]})},a.id)))})]}):null,ce=({isOpen:a,onOpenChange:s,session:i,onSuccess:t})=>i?e.jsx(u,{open:a,onOpenChange:s,children:e.jsxs(g,{className:"max-w-3xl max-h-[90vh] overflow-y-auto",children:[e.jsxs(j,{children:[e.jsx(b,{children:"Nova Prescrição"}),e.jsx(v,{children:"Crie uma nova prescrição preenchendo os campos abaixo."})]}),e.jsx(ee,{session:i,onSuccess:t})]})}):null,de=({prescriptionsCount:s,session:i,onSelectPrescription:t})=>{const[r,o]=a.useState(!1),{toast:n}=f(),{data:c}=T({queryKey:["recent-prescriptions",i?.user?.id],queryFn:async()=>{if(!i?.user?.id)return[];const{data:e,error:a}=await p.from("pedbook_prescriptions").select("\n          id,\n          name,\n          created_at\n        ").eq("user_id",i.user.id).order("created_at",{ascending:!1}).limit(5);if(a)throw a;return e},enabled:!!i?.user?.id}),d=()=>{o(!1),n({title:"Prescrição criada com sucesso!",description:"A prescrição foi adicionada à sua lista."}),setTimeout((()=>{window.location.reload()}),500)};return 0===s?e.jsxs(e.Fragment,{children:[e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-2xl mx-auto space-y-4 text-center px-4 pt-2",children:[e.jsx(oe,{}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-3 justify-center pt-2",children:e.jsxs(m,{size:"lg",onClick:()=>o(!0),className:"w-full sm:w-auto bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/30 hover:-translate-y-0.5 dark:shadow-primary/10 dark:hover:shadow-primary/20",children:[e.jsx(O,{className:"h-5 w-5 mr-2"}),"Nova Prescrição"]})}),e.jsx(ne,{prescriptions:c||[],onSelectPrescription:t})]}),e.jsx(ce,{isOpen:r,onOpenChange:o,session:i,onSuccess:d})]}):e.jsxs(e.Fragment,{children:[e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-2xl mx-auto space-y-4 text-center pt-2",children:[e.jsx("h2",{className:"text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent dark:from-blue-400 dark:to-blue-600",children:"Selecione uma Prescrição"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Escolha uma prescrição no menu lateral para visualizar ou editar seus detalhes."}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:e.jsxs(m,{variant:"outline",onClick:()=>o(!0),className:"border-primary/20 hover:bg-primary/5 transition-all duration-300 dark:border-primary/30 dark:hover:bg-primary/10",children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Nova Prescrição"]})})]}),e.jsx(ce,{isOpen:r,onOpenChange:o,session:i,onSuccess:d})]})},le=({weight:s,age:d,isUpdating:l,onWeightChange:m,onAgeChange:p})=>{const[h,u]=a.useState(!1),[g,y]=a.useState(!1),[f,j]=a.useState(""),{unit:b,inputValue:v,handleUnitChange:_,handleChange:w,handleBlur:N}=B({ageInMonths:parseInt(d)||0,onChange:e=>p(e.toString()),onCommit:e=>p(e.toString())});return a.useEffect((()=>{const e=parseInt(d)||0;j("years"===b?`${Math.floor(e/12)} anos`:`${e} meses`)}),[d,b]),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 bg-gradient-to-br from-primary/5 via-primary/10 to-transparent p-4 rounded-xl backdrop-blur-sm border border-primary/10",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(x,{htmlFor:"weight",className:"text-sm font-medium text-primary/80",children:"Peso (kg)"}),e.jsx("div",{className:"h-4 w-4",children:l?e.jsx(K,{isPublic:!1}):h&&e.jsx(k,{className:"h-4 w-4 text-green-500 animate-in fade-in"})}),s&&!l&&e.jsxs("span",{className:"text-xs text-primary/60 ml-auto",children:[s,"kg aplicado"]})]}),e.jsx(i,{id:"weight",type:"number",value:s,onChange:e=>(e=>{const a=parseFloat(e);if(isNaN(a))m(e);else{const e=Math.min(a,100);m(e.toString()),u(!0),setTimeout((()=>u(!1)),2e3)}})(e.target.value),min:0,max:100,step:.1,className:"bg-white/50 border-primary/20 focus:border-primary/40 transition-colors"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(x,{htmlFor:"age",className:"text-sm font-medium text-primary/80",children:"Idade"}),e.jsx("div",{className:"h-4 w-4",children:l?e.jsx(K,{isPublic:!1}):g&&e.jsx(k,{className:"h-4 w-4 text-green-500 animate-in fade-in"})}),d&&!l&&e.jsxs("span",{className:"text-xs text-primary/60 ml-auto",children:[f," aplicado"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(i,{id:"age",type:"number",value:v,onChange:e=>{return a=e.target.value,w(a),y(!0),void setTimeout((()=>y(!1)),2e3);var a},onBlur:N,min:0,max:"years"===b?100:1200,className:"flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors"}),e.jsxs(t,{value:b,onValueChange:e=>_(e),children:[e.jsx(r,{className:"w-[110px] bg-white/50 border-primary/20",children:e.jsx(o,{})}),e.jsxs(n,{children:[e.jsx(c,{value:"months",children:"meses"}),e.jsx(c,{value:"years",children:"anos"})]})]})]})]})]})},me=({prescription:a,calculatedDosages:s})=>{const{toast:i}=f();let t="";return e.jsxs("div",{className:"space-y-4",children:[a.pedbook_prescription_medications.map(((a,i)=>{const r=a.is_custom_medication?a.medication_name:a.pedbook_medications?.name,o=a.pedbook_medication_dosages?.name||a.dosage_name;if(!r)return null;const n=a.section_title&&a.section_title!==t;n&&(t=a.section_title);const c=s[a.id],d="string"==typeof c?c:c?.text||"",l="object"==typeof c&&c?.restrictions?"age"===(m=c.restrictions).type&&m.minAge?`Esta apresentação é indicada para pacientes a partir de ${pe(m.minAge)} de idade.`:"weight"===m.type&&m.minWeight?`Esta apresentação é indicada para pacientes com peso mínimo de ${m.minWeight}kg.`:"both"===m.type&&m.minAge&&m.minWeight?`Esta apresentação é indicada para pacientes a partir de ${pe(m.minAge)} de idade e com peso mínimo de ${m.minWeight}kg.`:"":null;var m;return e.jsxs("div",{children:[n&&e.jsx("h3",{className:"text-lg font-semibold text-primary mt-6 mb-4",children:t}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-base",children:[e.jsxs("span",{className:"mr-2",children:[i+1,")"]}),e.jsxs("span",{className:"text-primary font-medium",children:[r," ",o]}),a.quantity&&e.jsxs("span",{className:"text-gray-600 ml-1",children:["- ",a.quantity]})]}),l?e.jsx("div",{className:"text-orange-600 text-sm font-medium",children:l}):e.jsx("div",{className:"text-gray-600 text-sm",children:d})]})]},a.id)})),e.jsx("div",{className:"flex gap-2 pt-4 border-t border-gray-200",children:e.jsxs(m,{variant:"outline",size:"sm",onClick:async()=>{const e=(()=>{let e=`Prescrição: ${a.name}\n\n`,i="";return a.pedbook_prescription_medications.forEach(((a,t)=>{a.section_title&&a.section_title!==i&&(i=a.section_title,e+=`\n${i}\n`);const r=a.is_custom_medication?a.medication_name:a.pedbook_medications?.name,o=a.pedbook_medication_dosages?.name||a.dosage_name,n=s[a.id],c="string"==typeof n?n:n?.text,d=a.quantity?` - ${a.quantity}`:"";e+=`${t+1}) ${r} ${o}${d}\n`,e+=`${c}\n\n`})),e})();try{await navigator.clipboard.writeText(e),i({title:"Prescrição copiada",description:"O texto da prescrição foi copiado para a área de transferência"})}catch(t){i({variant:"destructive",title:"Erro ao copiar",description:"Não foi possível copiar a prescrição para a área de transferência"})}},className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-4 w-4"}),"Copiar"]})})]})};function pe(e){if(e<12)return`${e} ${1===e?"mês":"meses"}`;{const a=Math.floor(e/12),s=e%12;return 0===s?`${a} ${1===a?"ano":"anos"}`:`${a} ${1===a?"ano":"anos"} e ${s} ${1===s?"mês":"meses"}`}}const he=({prescription:s,calculatedDosages:i,isUpdating:t,onWeightChange:r,onAgeChange:o})=>{const[n,c]=a.useState(s.patient_weight?.toString()||""),[d,l]=a.useState(i),[h,x]=a.useState(!1),{toast:y}=f();return a.useEffect((()=>{(async()=>{const e={},a=Math.min(parseFloat(n)||0,100),i=s.patient_age||0;for(const r of s.pedbook_prescription_medications||[]){const s=r.pedbook_medication_dosages?.dosage_template||r.dosage_template;if(s)try{const t=await z(s,a,i,r.medication_id);e[r.id]=t}catch(t){e[r.id]={text:"Erro ao calcular dosagem"}}}l(e)})()}),[s.pedbook_prescription_medications,n,s.patient_age]),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:C.glassContainer("space-y-4 p-6 from-primary/5 via-primary/10 to-transparent backdrop-blur-sm border border-primary/10 dark:border-primary/20"),children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h1",{className:C.gradientHeading("text-2xl"),children:s.name}),e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Criada em: ",s.created_at?R(new Date(s.created_at),"dd/MM/yyyy"):""]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{variant:"outline",size:"icon",onClick:()=>x(!0),className:"hover:bg-primary/10 dark:hover:bg-blue-900/30",children:e.jsx(U,{className:"h-4 w-4"})}),e.jsx(m,{variant:"ghost",size:"icon",onClick:async()=>{try{const{error:e}=await p.from("pedbook_prescription_user_reactions").delete().eq("prescription_id",s.id);if(e)throw e;const{error:a}=await p.from("pedbook_prescription_medications").delete().eq("prescription_id",s.id);if(a)throw a;const{error:i}=await p.from("pedbook_prescriptions").delete().eq("id",s.id);if(i)throw i;y({title:"Prescrição removida",description:"A prescrição foi removida com sucesso."}),window.location.href="/prescriptions"}catch(e){y({variant:"destructive",title:"Erro ao remover prescrição",description:e.message})}},className:"hover:bg-destructive/10 dark:hover:bg-red-900/30",children:e.jsx(Q,{className:"h-4 w-4"})})]})]})}),e.jsx(le,{weight:n,age:s.patient_age?.toString()||"",isUpdating:t,onWeightChange:e=>{c(e),r(e)},onAgeChange:o}),e.jsx(me,{prescription:s,calculatedDosages:d}),e.jsx(u,{open:h,onOpenChange:x,children:e.jsx(g,{className:"max-w-3xl max-h-[80vh] overflow-y-auto",children:e.jsx(ee,{session:{user:{id:s.user_id}},onSuccess:()=>{x(!1),window.location.reload()},prescription:s,onClose:()=>x(!1)})})})]})},xe=({prescriptionId:s,onWeightChange:i,onAgeChange:t,onSelectPrescription:r})=>{const[o,n]=a.useState({}),[c,d]=a.useState(!1),l=V.useSession(),{toast:m}=f(),{data:h,error:x}=T({queryKey:["prescription",s],queryFn:async()=>{if(!s)return null;const{data:e,error:a}=await p.from("pedbook_prescriptions").select("\n          *,\n          pedbook_prescription_medications (\n            id,\n            medication_id,\n            dosage_id,\n            prescription_id,\n            notes,\n            section_title,\n            display_order,\n            created_at,\n            updated_at,\n            quantity,\n            pedbook_medications (\n              name,\n              brands,\n              description\n            ),\n            pedbook_medication_dosages (\n              name,\n              dosage_template,\n              summary\n            )\n          )\n        ").eq("id",s).maybeSingle();if(a)throw m({variant:"destructive",title:"Erro ao carregar prescrição",description:"Não foi possível carregar os detalhes da prescrição."}),a;return e?(e.pedbook_prescription_medications&&e.pedbook_prescription_medications.sort(((e,a)=>(e.display_order||0)-(a.display_order||0))),e):(m({variant:"destructive",title:"Prescrição não encontrada",description:"A prescrição solicitada não foi encontrada ou você não tem permissão para acessá-la."}),null)},enabled:!!s});return a.useEffect((()=>{(async()=>{if(!h?.pedbook_prescription_medications)return;d(!0);const e={};for(const s of h.pedbook_prescription_medications){const i=s.pedbook_medication_dosages?.dosage_template||s.dosage_template;if(i)try{const a=await re(i,h.patient_weight||0,h.patient_age||0,s.medication_id);e[s.id]=a}catch(a){e[s.id]={text:"Erro ao calcular dosagem"}}}n(e),d(!1)})()}),[h]),s?x?e.jsx(w.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-gradient-to-br from-white/80 to-primary/5 dark:from-slate-900/90 dark:to-slate-800/80 backdrop-blur-sm rounded-xl shadow-lg p-4 md:p-8 min-h-[calc(100vh-8rem)] flex flex-col items-center justify-center text-center space-y-6",children:e.jsxs("div",{className:"text-destructive",children:[e.jsx("p",{children:"Erro ao carregar a prescrição."}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Por favor, tente novamente mais tarde."})]})}):e.jsx(w.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-gradient-to-br from-white/80 to-primary/5 dark:from-slate-900/90 dark:to-slate-800/80 backdrop-blur-sm rounded-xl shadow-lg p-4 md:p-8 min-h-[calc(100vh-8rem)] flex flex-col",children:h&&e.jsx(he,{prescription:h,calculatedDosages:o,isUpdating:c,onWeightChange:i,onAgeChange:t})}):e.jsx(w.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-gradient-to-br from-white/80 to-primary/5 dark:from-slate-900/90 dark:to-slate-800/80 backdrop-blur-sm rounded-xl shadow-lg p-4 md:p-8 min-h-[calc(100vh-8rem)] flex flex-col items-center justify-center text-center space-y-6",children:e.jsx(de,{prescriptionsCount:0,session:l,onSelectPrescription:r})})};function ue(){const s=I(),{toast:i}=f(),[t,r]=a.useState(null),[o,n]=a.useState(null),[c,d]=a.useState(""),l=M();a.useEffect((()=>{p.auth.getSession().then((({data:{session:e}})=>{e?r(e):s("/")}));const{data:{subscription:e}}=p.auth.onAuthStateChange(((e,a)=>{a?r(a):s("/")}));return()=>e.unsubscribe()}),[s,i]);const h=F({mutationFn:async e=>{const{data:a}=await p.from("pedbook_prescriptions").select("patient_weight, patient_age").eq("id",e.id).single(),s={patient_weight:e.weight??a?.patient_weight??0,patient_age:e.age??a?.patient_age??0},{error:i}=await p.from("pedbook_prescriptions").update(s).eq("id",e.id);if(i)throw i},onSuccess:()=>{l.invalidateQueries({queryKey:["prescription",o]})}}),x=e=>{n(e)};return e.jsxs("div",{className:C.pageBackground(),children:[e.jsxs(S,{children:[e.jsx("title",{children:"PedBook | Prescrições"}),e.jsx("meta",{name:"description",content:"Gerencie suas prescrições pediátricas de forma eficiente."})]}),e.jsx(P,{}),e.jsxs("div",{className:"flex-1 container mx-auto py-2 px-2 md:py-4 md:px-6",children:[e.jsx("div",{className:"flex flex-col space-y-2 mb-4",children:e.jsxs(m,{variant:"ghost",className:"text-primary hover:text-primary/80 transition-colors w-fit dark:text-blue-400 dark:hover:text-blue-300",onClick:()=>s("/"),children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Voltar para o menu"]})}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4 md:gap-6",children:[t&&e.jsx("div",{className:"w-full lg:w-80 flex-shrink-0",children:e.jsx(te,{selectedPrescription:o,onPrescriptionSelect:x,searchTerm:c,onSearchChange:d,session:t})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx("div",{className:"max-w-full px-0 md:px-6",children:e.jsx(xe,{prescriptionId:o||"",onWeightChange:e=>{if(!o)return;const a=parseFloat(e);isNaN(a)||h.mutate({id:o,weight:a})},onAgeChange:e=>{if(!o)return;const a=parseInt(e);isNaN(a)||h.mutate({id:o,age:a})},onSelectPrescription:x})})})]})]}),e.jsx(E,{})]})}export{ue as default};
