import{j as r}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{j as o}from"./radix-layout-CC8mXA4O.js";import{j as i}from"./index-CFnD44mG.js";const s=a.forwardRef((({className:a,orientation:s="horizontal",decorative:e=!0,...t},l)=>r.jsx(o,{ref:l,decorative:e,orientation:s,className:i("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",a),...t})));s.displayName=o.displayName;export{s as S};
