import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{K as o,N as r}from"./index-BGVWLj2Q.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";let n=null,i=0;const s=async()=>{if(!o())return{top:0,bottom:0,left:0,right:0};const e=window.innerHeight;if(n&&i===e)return n;try{if((()=>{if("undefined"==typeof window)return!1;const e=document.createElement("div");e.style.paddingTop="env(safe-area-inset-top)",document.body.appendChild(e);const t="0px"!==getComputedStyle(e).paddingTop;return document.body.removeChild(e),t})()){const t=a();if(t.top>0||t.bottom>0)return n=t,i=e,t}const t={top:(await r.getInfo()).height||0,bottom:d(),left:0,right:0};return n=t,i=e,t}catch(t){const o={top:24,bottom:48,left:0,right:0};return n=o,i=e,o}},a=()=>{const e=document.createElement("div");e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.width="1px",e.style.height="1px",e.style.paddingTop="env(safe-area-inset-top)",e.style.paddingBottom="env(safe-area-inset-bottom)",e.style.paddingLeft="env(safe-area-inset-left)",e.style.paddingRight="env(safe-area-inset-right)",document.body.appendChild(e);const t=getComputedStyle(e),o={top:parseInt(t.paddingTop)||0,bottom:parseInt(t.paddingBottom)||0,left:parseInt(t.paddingLeft)||0,right:parseInt(t.paddingRight)||0};return document.body.removeChild(e),o},d=()=>{const e=navigator.userAgent.toLowerCase();if(/android/i.test(e)){const t=e.match(/android (\d+)/);return t&&parseInt(t[1]),48}return 0},p=()=>{n=null,i=0},c=({children:r})=>{const{cssVars:n,isLoading:i}=(()=>{const{safeAreas:e,isLoading:r}=(()=>{const[e,r]=t.useState({top:0,bottom:0,left:0,right:0}),[n,i]=t.useState(!0);return t.useEffect((()=>{if(!o())return void i(!1);const e=async()=>{try{const e=await s();r(e)}catch(e){}finally{i(!1)}};e();const t=()=>{p(),setTimeout(e,100)},n=()=>{p(),e()};return window.addEventListener("orientationchange",t),window.addEventListener("resize",n),()=>{window.removeEventListener("orientationchange",t),window.removeEventListener("resize",n)}}),[]),{safeAreas:e,isLoading:n,refresh:async()=>{p();const e=await s();r(e)}}})();return{cssVars:{"--safe-area-inset-top":`${e.top}px`,"--safe-area-inset-bottom":`${e.bottom}px`,"--safe-area-inset-left":`${e.left}px`,"--safe-area-inset-right":`${e.right}px`,"--safe-area-height":`calc(100vh - ${e.top}px - ${e.bottom}px)`,"--safe-area-width":`calc(100vw - ${e.left}px - ${e.right}px)`},isLoading:r,safeAreas:e}})();return t.useEffect((()=>{if(o()&&document.body.classList.add("capacitor"),!o()||i)return;const e=document.documentElement;Object.entries(n).forEach((([t,o])=>{e.style.setProperty(t,o)}))}),[n,i]),t.useEffect((()=>{if(!o())return;const e=()=>{(window.visualViewport?.height||window.innerHeight)<.75*window.screen.height?document.body.classList.add("keyboard-open"):document.body.classList.remove("keyboard-open")};return window.addEventListener("resize",e),window.visualViewport?.addEventListener("resize",e),()=>{window.removeEventListener("resize",e),window.visualViewport?.removeEventListener("resize",e)}}),[]),e.jsx(e.Fragment,{children:r})};export{c as SafeAreaProvider};
