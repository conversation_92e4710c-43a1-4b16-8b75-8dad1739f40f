import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Bug, Copy, Image, Layout, Smartphone } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface ImageInfo {
  src: string;
  alt: string;
  naturalWidth: number;
  naturalHeight: number;
  displayWidth: number;
  displayHeight: number;
  scaleX: number;
  scaleY: number;
  isOversized: boolean;
  css: {
    objectFit: string;
    width: string;
    height: string;
    transform: string;
  };
}

interface LayoutInfo {
  pageHeight: number;
  viewportHeight: number;
  scrollHeight: number;
  hasVerticalScroll: boolean;
  contentOverflow: boolean;
  safeAreaTop: number;
  safeAreaBottom: number;
  availableHeight: number;
}

interface OptimizedContainerInfo {
  dimensions: {
    width: number;
    height: number;
    top: number;
    bottom: number;
    scrollHeight: number;
    scrollTop: number;
    clientHeight: number;
    offsetHeight: number;
  };
  css: {
    height: string;
    maxHeight: string;
    minHeight: string;
    overflow: string;
    overflowY: string;
    display: string;
    flexDirection: string;
    flex: string;
    position: string;
    padding: string;
    margin: string;
    backgroundColor: string;
    background: string;
    zIndex: string;
  };
  classes: string;
  hasScroll: boolean;
  elementInfo?: {
    tagName: string;
    id: string;
    dataAttributes: Record<string, string>;
  };
}

interface OptimizedLayoutInfo {
  main?: OptimizedContainerInfo | { error: string };
  content?: OptimizedContainerInfo | { error: string };
  tabs?: OptimizedContainerInfo | { error: string };
  tabContent?: OptimizedContainerInfo | { error: string };
  prose?: OptimizedContainerInfo | { error: string };
  viewport: {
    width: number;
    height: number;
  };
  document: {
    scrollHeight: number;
    clientHeight: number;
    scrollTop: number;
  };
  body: {
    scrollHeight: number;
    clientHeight: number;
    scrollTop: number;
  };
  suspiciousElements?: Array<{
    selector: string;
    element: OptimizedContainerInfo;
    reason: string;
  }>;
  allWhiteBackgroundElements?: Array<{
    selector: string;
    element: OptimizedContainerInfo;
  }>;
}

interface DebugData {
  timestamp: string;
  page: string;
  url: string;
  viewport: {
    width: number;
    height: number;
    scrollY: number;
  };
  images?: ImageInfo[];
  layout?: LayoutInfo;
  optimizedLayout?: OptimizedLayoutInfo;
}

export const GlobalDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  const analyzeImages = (): ImageInfo[] => {
    const images = Array.from(document.querySelectorAll('img'));
    
    return images.map(img => {
      const rect = img.getBoundingClientRect();
      const computedStyle = getComputedStyle(img);
      
      const scaleX = rect.width / (img.naturalWidth || 1);
      const scaleY = rect.height / (img.naturalHeight || 1);
      
      return {
        src: img.src,
        alt: img.alt || 'No alt text',
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight,
        displayWidth: rect.width,
        displayHeight: rect.height,
        scaleX,
        scaleY,
        isOversized: scaleX > 2 || scaleY > 2,
        css: {
          objectFit: computedStyle.objectFit,
          width: computedStyle.width,
          height: computedStyle.height,
          transform: computedStyle.transform
        }
      };
    });
  };

  const analyzeLayout = (): LayoutInfo => {
    const body = document.body;
    const html = document.documentElement;
    
    const pageHeight = Math.max(
      body.scrollHeight,
      body.offsetHeight,
      html.clientHeight,
      html.scrollHeight,
      html.offsetHeight
    );
    
    const viewportHeight = window.innerHeight;
    const scrollHeight = html.scrollHeight;
    const hasVerticalScroll = scrollHeight > viewportHeight;
    
    // Safe areas
    const safeAreaTop = parseInt(getComputedStyle(html).getPropertyValue('--safe-area-inset-top') || '0px');
    const safeAreaBottom = parseInt(getComputedStyle(html).getPropertyValue('--safe-area-inset-bottom') || '0px');
    const availableHeight = viewportHeight - safeAreaTop - safeAreaBottom;
    
    return {
      pageHeight,
      viewportHeight,
      scrollHeight,
      hasVerticalScroll,
      contentOverflow: pageHeight > availableHeight,
      safeAreaTop,
      safeAreaBottom,
      availableHeight
    };
  };

  const analyzeOptimizedContainer = (selector: string): OptimizedContainerInfo | { error: string } => {
    try {
      const element = document.querySelector(selector) as HTMLElement;
      if (!element) {
        return { error: `Element not found: ${selector}` };
      }

      const rect = element.getBoundingClientRect();
      const computedStyle = getComputedStyle(element);

      return {
        dimensions: {
          width: rect.width,
          height: rect.height,
          top: rect.top,
          bottom: rect.bottom,
          scrollHeight: element.scrollHeight,
          scrollTop: element.scrollTop,
          clientHeight: element.clientHeight,
          offsetHeight: element.offsetHeight,
        },
        css: {
          height: computedStyle.height,
          maxHeight: computedStyle.maxHeight,
          minHeight: computedStyle.minHeight,
          overflow: computedStyle.overflow,
          overflowY: computedStyle.overflowY,
          display: computedStyle.display,
          flexDirection: computedStyle.flexDirection,
          flex: computedStyle.flex,
          position: computedStyle.position,
          padding: computedStyle.padding,
          margin: computedStyle.margin,
          backgroundColor: computedStyle.backgroundColor,
          background: computedStyle.background,
          zIndex: computedStyle.zIndex,
        },
        classes: element.className,
        hasScroll: element.scrollHeight > element.clientHeight,
        elementInfo: {
          tagName: element.tagName,
          id: element.id,
          dataAttributes: (() => {
            const dataAttributes: Record<string, string> = {};
            for (let i = 0; i < element.attributes.length; i++) {
              const attr = element.attributes[i];
              if (attr.name.startsWith('data-')) {
                dataAttributes[attr.name] = attr.value;
              }
            }
            return dataAttributes;
          })()
        },
      };
    } catch (error) {
      return { error: `Error analyzing ${selector}: ${error}` };
    }
  };

  const analyzeOptimizedLayout = (): OptimizedLayoutInfo => {
    // Função para encontrar elementos suspeitos
    const findSuspiciousElements = (): Array<{
      selector: string;
      element: OptimizedContainerInfo;
      reason: string;
    }> => {
      const suspicious: Array<{
        selector: string;
        element: OptimizedContainerInfo;
        reason: string;
      }> = [];

      try {
        // Procurar por elementos com background branco que possam estar sobrepondo
        const allElements = document.querySelectorAll('*');

        allElements.forEach((el, index) => {
          const element = el as HTMLElement;
          const computedStyle = getComputedStyle(element);
          const rect = element.getBoundingClientRect();

          // Verificar se está na área problemática (altura entre 100-500px)
          if (rect.top > 50 && rect.top < 500 && rect.height > 30) {
            const bgColor = computedStyle.backgroundColor;
            const bg = computedStyle.background;
            const position = computedStyle.position;
            const zIndex = computedStyle.zIndex;

            let reasons: string[] = [];

            // Elementos com background branco
            if (bgColor.includes('rgb(255, 255, 255)') || bgColor.includes('white') ||
                bg.includes('white') || bg.includes('rgb(255, 255, 255)')) {
              reasons.push('white background');
            }

            // Elementos posicionados
            if (position === 'absolute' || position === 'fixed') {
              reasons.push(`position: ${position}`);
            }

            // Elementos com z-index alto
            if (zIndex !== 'auto' && parseInt(zIndex) > 0) {
              reasons.push(`z-index: ${zIndex}`);
            }

            // Elementos grandes que podem estar mascarando
            if (rect.height > 100 && rect.width > 200) {
              reasons.push('large size');
            }

            if (reasons.length > 0) {
              const selector = `${element.tagName.toLowerCase()}${element.id ? '#' + element.id : ''}${element.className ? '.' + element.className.split(' ').slice(0, 2).join('.') : ''}[${index}]`;

              try {
                const elementAnalysis = analyzeOptimizedContainer(`${element.tagName.toLowerCase()}:nth-of-type(${Array.from(element.parentNode?.children || []).filter(child => child.tagName === element.tagName).indexOf(element) + 1})`);

                if ('dimensions' in elementAnalysis) {
                  suspicious.push({
                    selector,
                    element: elementAnalysis,
                    reason: reasons.join(', ')
                  });
                }
              } catch (e) {
                // Ignorar erros de análise individual
              }
            }
          }
        });
      } catch (error) {
        console.error('Error finding suspicious elements:', error);
      }

      return suspicious.slice(0, 15); // Limitar a 15 elementos
    };

    return {
      main: analyzeOptimizedContainer('[data-debug="optimized-main"]'),
      content: analyzeOptimizedContainer('[data-debug="optimized-content"]'),
      tabs: analyzeOptimizedContainer('[data-debug="optimized-tabs"]'),
      tabContent: analyzeOptimizedContainer('[data-debug="optimized-tab-content"]'),
      prose: analyzeOptimizedContainer('[data-debug="optimized-prose"]'),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      document: {
        scrollHeight: document.documentElement.scrollHeight,
        clientHeight: document.documentElement.clientHeight,
        scrollTop: document.documentElement.scrollTop,
      },
      body: {
        scrollHeight: document.body.scrollHeight,
        clientHeight: document.body.clientHeight,
        scrollTop: document.body.scrollTop,
      },
      suspiciousElements: findSuspiciousElements()
    };
  };

  const runImageAnalysis = () => {
    const images = analyzeImages();
    const layout = analyzeLayout();
    
    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'Image Analysis',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      images,
      layout
    };

    console.log('🖼️ IMAGE DEBUG DATA:', JSON.stringify(debugData, null, 2));
    
    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug copiado!",
        description: `Análise de ${images.length} imagens copiada para clipboard`,
      });
    });
  };

  const runLayoutAnalysis = () => {
    const layout = analyzeLayout();
    
    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'Layout Analysis',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      layout
    };

    console.log('📐 LAYOUT DEBUG DATA:', JSON.stringify(debugData, null, 2));
    
    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug copiado!",
        description: "Análise de layout copiada para clipboard",
      });
    });
  };

  const runOptimizedAnalysis = () => {
    const optimizedLayout = analyzeOptimizedLayout();
    const layout = analyzeLayout();

    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'OptimizedConductsView Analysis',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      layout,
      optimizedLayout
    };

    console.log('🎯 OPTIMIZED CONDUCTS DEBUG:', JSON.stringify(debugData, null, 2));

    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug OptimizedConductsView copiado!",
        description: "Análise detalhada do componente copiada para clipboard",
      });
    });
  };

  const runFullAnalysis = () => {
    const layout = analyzeLayout();
    const optimizedLayout = analyzeOptimizedLayout();

    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'Full Analysis (Layout Only)',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      layout,
      optimizedLayout
    };

    console.log('🔍 FULL DEBUG DATA:', JSON.stringify(debugData, null, 2));
    
    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug completo copiado!",
        description: `Análise completa da página copiada para clipboard`,
      });
    });
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 left-4 z-50 bg-red-500 hover:bg-red-600 text-white shadow-lg"
        size="sm"
      >
        <Bug className="w-4 h-4" />
      </Button>
    );
  }

  return (
    <div className="fixed bottom-20 left-4 z-50 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-4 max-w-xs">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-sm">Debug Global</h3>
        <Button
          onClick={() => setIsVisible(false)}
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
        >
          ×
        </Button>
      </div>
      
      <div className="space-y-2">
        <Button
          onClick={runImageAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Image className="w-3 h-3 mr-1" />
          Analisar Imagens
        </Button>
        
        <Button
          onClick={runLayoutAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Layout className="w-3 h-3 mr-1" />
          Analisar Layout
        </Button>

        <Button
          onClick={runOptimizedAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Smartphone className="w-3 h-3 mr-1" />
          OptimizedConducts
        </Button>

        <Button
          onClick={runFullAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Smartphone className="w-3 h-3 mr-1" />
          Análise Completa
        </Button>
      </div>
    </div>
  );
};
