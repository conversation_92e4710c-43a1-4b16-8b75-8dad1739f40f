import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Bug, Copy, Image, Layout, Smartphone } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface ImageInfo {
  src: string;
  alt: string;
  naturalWidth: number;
  naturalHeight: number;
  displayWidth: number;
  displayHeight: number;
  scaleX: number;
  scaleY: number;
  isOversized: boolean;
  css: {
    objectFit: string;
    width: string;
    height: string;
    transform: string;
  };
}

interface LayoutInfo {
  pageHeight: number;
  viewportHeight: number;
  scrollHeight: number;
  hasVerticalScroll: boolean;
  contentOverflow: boolean;
  safeAreaTop: number;
  safeAreaBottom: number;
  availableHeight: number;
}

interface OptimizedContainerInfo {
  dimensions: {
    width: number;
    height: number;
    top: number;
    bottom: number;
    scrollHeight: number;
    scrollTop: number;
    clientHeight: number;
    offsetHeight: number;
  };
  css: {
    height: string;
    maxHeight: string;
    minHeight: string;
    overflow: string;
    overflowY: string;
    display: string;
    flexDirection: string;
    flex: string;
    position: string;
    padding: string;
    margin: string;
    backgroundColor: string;
    background: string;
    zIndex: string;
  };
  classes: string;
  hasScroll: boolean;
  elementInfo?: {
    tagName: string;
    id: string;
    dataAttributes: Record<string, string>;
  };
}

interface OptimizedLayoutInfo {
  main?: OptimizedContainerInfo | { error: string };
  content?: OptimizedContainerInfo | { error: string };
  tabs?: OptimizedContainerInfo | { error: string };
  tabContent?: OptimizedContainerInfo | { error: string };
  prose?: OptimizedContainerInfo | { error: string };
  viewport: {
    width: number;
    height: number;
  };
  document: {
    scrollHeight: number;
    clientHeight: number;
    scrollTop: number;
  };
  body: {
    scrollHeight: number;
    clientHeight: number;
    scrollTop: number;
  };
  suspiciousElements?: Array<{
    selector: string;
    element: OptimizedContainerInfo;
    reason: string;
  }>;
  allWhiteBackgroundElements?: Array<{
    selector: string;
    element: OptimizedContainerInfo;
  }>;
}

interface DebugData {
  timestamp: string;
  page: string;
  url: string;
  viewport: {
    width: number;
    height: number;
    scrollY: number;
  };
  images?: ImageInfo[];
  layout?: LayoutInfo;
  optimizedLayout?: OptimizedLayoutInfo;
}

export const GlobalDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  const analyzeImages = (): ImageInfo[] => {
    const images = Array.from(document.querySelectorAll('img'));
    
    return images.map(img => {
      const rect = img.getBoundingClientRect();
      const computedStyle = getComputedStyle(img);
      
      const scaleX = rect.width / (img.naturalWidth || 1);
      const scaleY = rect.height / (img.naturalHeight || 1);
      
      return {
        src: img.src,
        alt: img.alt || 'No alt text',
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight,
        displayWidth: rect.width,
        displayHeight: rect.height,
        scaleX,
        scaleY,
        isOversized: scaleX > 2 || scaleY > 2,
        css: {
          objectFit: computedStyle.objectFit,
          width: computedStyle.width,
          height: computedStyle.height,
          transform: computedStyle.transform
        }
      };
    });
  };

  const analyzeLayout = (): LayoutInfo => {
    const body = document.body;
    const html = document.documentElement;
    
    const pageHeight = Math.max(
      body.scrollHeight,
      body.offsetHeight,
      html.clientHeight,
      html.scrollHeight,
      html.offsetHeight
    );
    
    const viewportHeight = window.innerHeight;
    const scrollHeight = html.scrollHeight;
    const hasVerticalScroll = scrollHeight > viewportHeight;
    
    // Safe areas
    const safeAreaTop = parseInt(getComputedStyle(html).getPropertyValue('--safe-area-inset-top') || '0px');
    const safeAreaBottom = parseInt(getComputedStyle(html).getPropertyValue('--safe-area-inset-bottom') || '0px');
    const availableHeight = viewportHeight - safeAreaTop - safeAreaBottom;
    
    return {
      pageHeight,
      viewportHeight,
      scrollHeight,
      hasVerticalScroll,
      contentOverflow: pageHeight > availableHeight,
      safeAreaTop,
      safeAreaBottom,
      availableHeight
    };
  };

  const analyzeOptimizedContainer = (selector: string): OptimizedContainerInfo | { error: string } => {
    try {
      const element = document.querySelector(selector) as HTMLElement;
      if (!element) {
        return { error: `Element not found: ${selector}` };
      }

      const rect = element.getBoundingClientRect();
      const computedStyle = getComputedStyle(element);

      return {
        dimensions: {
          width: rect.width,
          height: rect.height,
          top: rect.top,
          bottom: rect.bottom,
          scrollHeight: element.scrollHeight,
          scrollTop: element.scrollTop,
          clientHeight: element.clientHeight,
          offsetHeight: element.offsetHeight,
        },
        css: {
          height: computedStyle.height,
          maxHeight: computedStyle.maxHeight,
          minHeight: computedStyle.minHeight,
          overflow: computedStyle.overflow,
          overflowY: computedStyle.overflowY,
          display: computedStyle.display,
          flexDirection: computedStyle.flexDirection,
          flex: computedStyle.flex,
          position: computedStyle.position,
          padding: computedStyle.padding,
          margin: computedStyle.margin,
          backgroundColor: computedStyle.backgroundColor,
          background: computedStyle.background,
          zIndex: computedStyle.zIndex,
        },
        classes: element.className,
        hasScroll: element.scrollHeight > element.clientHeight,
        elementInfo: {
          tagName: element.tagName,
          id: element.id,
          dataAttributes: (() => {
            const dataAttributes: Record<string, string> = {};
            for (let i = 0; i < element.attributes.length; i++) {
              const attr = element.attributes[i];
              if (attr.name.startsWith('data-')) {
                dataAttributes[attr.name] = attr.value;
              }
            }
            return dataAttributes;
          })()
        },
      };
    } catch (error) {
      return { error: `Error analyzing ${selector}: ${error}` };
    }
  };

  const analyzeOptimizedLayout = (): OptimizedLayoutInfo => {
    // Função para encontrar elementos suspeitos
    const findSuspiciousElements = (): Array<{
      selector: string;
      element: OptimizedContainerInfo;
      reason: string;
    }> => {
      const suspicious: Array<{
        selector: string;
        element: OptimizedContainerInfo;
        reason: string;
      }> = [];

      try {
        const allElements = document.querySelectorAll('*');

        allElements.forEach((el, index) => {
          const element = el as HTMLElement;
          const computedStyle = getComputedStyle(element);
          const rect = element.getBoundingClientRect();

          let reasons: string[] = [];
          let priority = 0;

          // PRIORIDADE MÁXIMA: Elementos com altura fixa suspeita (200-400px)
          if (computedStyle.height !== 'auto' && computedStyle.height !== '100%' &&
              computedStyle.height !== '' && !computedStyle.height.includes('calc')) {
            const heightValue = parseFloat(computedStyle.height);
            if (heightValue >= 200 && heightValue <= 400) {
              reasons.push(`🚨 FIXED HEIGHT: ${computedStyle.height}`);
              priority = 10;
            } else if (heightValue > 400 && heightValue < 900) {
              reasons.push(`fixed height: ${computedStyle.height}`);
              priority = 8;
            }
          }

          // PRIORIDADE ALTA: Elementos com overflow hidden
          if (computedStyle.overflow === 'hidden' || computedStyle.overflowY === 'hidden') {
            reasons.push(`overflow: ${computedStyle.overflow}/${computedStyle.overflowY}`);
            priority = Math.max(priority, 9);
          }

          // PRIORIDADE ALTA: Containers flex/block com altura limitada suspeita
          if (computedStyle.display === 'flex' || computedStyle.display === 'block') {
            if (rect.height >= 200 && rect.height <= 400 && rect.width > 300) {
              reasons.push(`🔍 CONTAINER: ${Math.round(rect.width)}x${Math.round(rect.height)}px`);
              priority = Math.max(priority, 7);
            } else if (rect.height > 400 && rect.height < 900 && rect.width > 300) {
              reasons.push(`container: ${Math.round(rect.width)}x${Math.round(rect.height)}px`);
              priority = Math.max(priority, 5);
            }
          }

          // PRIORIDADE MÉDIA: MaxHeight limitado
          if (computedStyle.maxHeight !== 'none' && computedStyle.maxHeight !== '' &&
              !computedStyle.maxHeight.includes('calc')) {
            const maxHeightValue = parseFloat(computedStyle.maxHeight);
            if (maxHeightValue >= 200 && maxHeightValue <= 400) {
              reasons.push(`🚨 MAX-HEIGHT: ${computedStyle.maxHeight}`);
              priority = Math.max(priority, 6);
            }
          }

          // PRIORIDADE MÉDIA: Elementos posicionados que podem sobrepor
          if (computedStyle.position === 'absolute' || computedStyle.position === 'fixed') {
            if (rect.height > 100 && rect.width > 200) {
              reasons.push(`positioned: ${computedStyle.position}`);
              priority = Math.max(priority, 4);
            }
          }

          // PRIORIDADE BAIXA: Elementos com flex que podem estar limitando
          if (computedStyle.flex && computedStyle.flex !== '0 1 auto' && computedStyle.flex !== 'none') {
            reasons.push(`flex: ${computedStyle.flex}`);
            priority = Math.max(priority, 3);
          }

          // INCLUIR TODOS os elementos com data-debug (nossos containers de debug)
          if (element.hasAttribute('data-debug')) {
            const debugAttr = element.getAttribute('data-debug');
            reasons.push(`🎯 DEBUG: ${debugAttr}`);
            priority = Math.max(priority, 8);
          }

          // Só incluir se há razões relevantes OU é um elemento de debug
          if (reasons.length > 0) {
            const classes = element.className ? element.className.split(' ').slice(0, 4).join('.') : '';
            const selector = `${element.tagName.toLowerCase()}${element.id ? '#' + element.id : ''}${classes ? '.' + classes : ''}`;

            try {
              const elementAnalysis = analyzeOptimizedContainer(`${element.tagName.toLowerCase()}:nth-of-type(${Array.from(element.parentNode?.children || []).filter(child => child.tagName === element.tagName).indexOf(element) + 1})`);

              if ('dimensions' in elementAnalysis) {
                suspicious.push({
                  selector: `[P${priority}] ${selector}`,
                  element: elementAnalysis,
                  reason: reasons.join(', ')
                });
              }
            } catch (e) {
              // Ignorar erros de análise individual
            }
          }
        });
      } catch (error) {
        console.error('Error finding suspicious elements:', error);
      }

      // Ordenar por prioridade (maior prioridade primeiro)
      return suspicious
        .sort((a, b) => {
          const priorityA = parseInt(a.selector.match(/\[P(\d+)\]/)?.[1] || '0');
          const priorityB = parseInt(b.selector.match(/\[P(\d+)\]/)?.[1] || '0');
          return priorityB - priorityA;
        })
        .slice(0, 20); // Aumentar para 20 elementos para capturar mais detalhes
    };

    return {
      main: analyzeOptimizedContainer('[data-debug="optimized-main"]'),
      content: analyzeOptimizedContainer('[data-debug="optimized-content"]'),
      tabs: analyzeOptimizedContainer('[data-debug="optimized-tabs"]'),
      tabContent: analyzeOptimizedContainer('[data-debug="optimized-tab-content"]'),
      prose: analyzeOptimizedContainer('[data-debug="optimized-prose"]'),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      document: {
        scrollHeight: document.documentElement.scrollHeight,
        clientHeight: document.documentElement.clientHeight,
        scrollTop: document.documentElement.scrollTop,
      },
      body: {
        scrollHeight: document.body.scrollHeight,
        clientHeight: document.body.clientHeight,
        scrollTop: document.body.scrollTop,
      },
      // Análise detalhada da hierarquia de containers
      containerHierarchy: (() => {
        const mainElement = document.querySelector('[data-debug="optimized-main"]') as HTMLElement;
        if (!mainElement) return null;

        const hierarchy: any[] = [];
        let current = mainElement.parentElement;
        let level = 0;

        while (current && level < 10) {
          const computedStyle = getComputedStyle(current);
          const rect = current.getBoundingClientRect();

          hierarchy.push({
            level,
            tagName: current.tagName.toLowerCase(),
            classes: current.className || '',
            id: current.id || '',
            dimensions: {
              width: Math.round(rect.width),
              height: Math.round(rect.height),
              top: Math.round(rect.top),
              bottom: Math.round(rect.bottom)
            },
            css: {
              height: computedStyle.height,
              maxHeight: computedStyle.maxHeight,
              minHeight: computedStyle.minHeight,
              overflow: computedStyle.overflow,
              overflowY: computedStyle.overflowY,
              display: computedStyle.display,
              flex: computedStyle.flex,
              position: computedStyle.position
            }
          });

          current = current.parentElement;
          level++;
        }

        return hierarchy;
      })(),
      suspiciousElements: findSuspiciousElements()
    };
  };

  const runImageAnalysis = () => {
    const images = analyzeImages();
    const layout = analyzeLayout();
    
    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'Image Analysis',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      images,
      layout
    };

    console.log('🖼️ IMAGE DEBUG DATA:', JSON.stringify(debugData, null, 2));
    
    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug copiado!",
        description: `Análise de ${images.length} imagens copiada para clipboard`,
      });
    });
  };

  const runLayoutAnalysis = () => {
    const layout = analyzeLayout();
    
    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'Layout Analysis',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      layout
    };

    console.log('📐 LAYOUT DEBUG DATA:', JSON.stringify(debugData, null, 2));
    
    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug copiado!",
        description: "Análise de layout copiada para clipboard",
      });
    });
  };

  const runOptimizedAnalysis = () => {
    const optimizedLayout = analyzeOptimizedLayout();
    const layout = analyzeLayout();

    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'OptimizedConductsView Analysis',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      layout,
      optimizedLayout
    };

    console.log('🎯 OPTIMIZED CONDUCTS DEBUG:', JSON.stringify(debugData, null, 2));

    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug OptimizedConductsView copiado!",
        description: "Análise detalhada do componente copiada para clipboard",
      });
    });
  };

  const runFullAnalysis = () => {
    const layout = analyzeLayout();
    const optimizedLayout = analyzeOptimizedLayout();

    const debugData: DebugData = {
      timestamp: new Date().toISOString(),
      page: 'Full Analysis (Layout Only)',
      url: window.location.pathname,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        scrollY: window.scrollY
      },
      layout,
      optimizedLayout
    };

    console.log('🔍 FULL DEBUG DATA:', JSON.stringify(debugData, null, 2));
    
    // Copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2)).then(() => {
      toast({
        title: "Debug completo copiado!",
        description: `Análise completa da página copiada para clipboard`,
      });
    });
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 left-4 z-50 bg-red-500 hover:bg-red-600 text-white shadow-lg"
        size="sm"
      >
        <Bug className="w-4 h-4" />
      </Button>
    );
  }

  return (
    <div className="fixed bottom-20 left-4 z-50 bg-white dark:bg-gray-800 border rounded-lg shadow-lg p-4 max-w-xs">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-sm">Debug Global</h3>
        <Button
          onClick={() => setIsVisible(false)}
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
        >
          ×
        </Button>
      </div>
      
      <div className="space-y-2">
        <Button
          onClick={runImageAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Image className="w-3 h-3 mr-1" />
          Analisar Imagens
        </Button>
        
        <Button
          onClick={runLayoutAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Layout className="w-3 h-3 mr-1" />
          Analisar Layout
        </Button>

        <Button
          onClick={runOptimizedAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Smartphone className="w-3 h-3 mr-1" />
          OptimizedConducts
        </Button>

        <Button
          onClick={runFullAnalysis}
          className="w-full text-xs"
          size="sm"
          variant="outline"
        >
          <Smartphone className="w-3 h-3 mr-1" />
          Análise Completa
        </Button>
      </div>
    </div>
  );
};
