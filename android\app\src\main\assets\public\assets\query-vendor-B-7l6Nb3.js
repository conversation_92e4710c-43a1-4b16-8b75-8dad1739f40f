var t,e,s,i,n,r,a,o,h,u,c,l,d,f,p,y,v,m,b,g,w,O,M,C,k,R,S,P,E,q,F,Q,x,W,D,A,U,T,K,j,I,L,H,_,G,B,N,z,$,J,V,X,Y,Z,tt,et,st,it,nt,rt,at,ot,ht,ut,ct,lt,dt,ft,pt,yt,vt,mt,bt,gt=t=>{throw TypeError(t)},wt=(t,e,s)=>e.has(t)||gt("Cannot "+s),Ot=(t,e,s)=>(wt(t,e,"read from private field"),s?s.call(t):e.get(t)),Mt=(t,e,s)=>e.has(t)?gt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),Ct=(t,e,s,i)=>(wt(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),kt=(t,e,s)=>(wt(t,e,"access private method"),s),Rt=(t,e,s,i)=>({set _(i){Ct(t,e,i,s)},get _(){return Ot(t,e,i)}});import{r as St}from"./critical-DVX9Inzy.js";import{j as Pt}from"./radix-core-6kBL75b5.js";var Et=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},qt="undefined"==typeof window||"Deno"in globalThis;function Ft(){}function Qt(t){return"number"==typeof t&&t>=0&&t!==1/0}function xt(t,e){return Math.max(t+(e||0)-Date.now(),0)}function Wt(t,e){return"function"==typeof t?t(e):t}function Dt(t,e){return"function"==typeof t?t(e):t}function At(t,e){const{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==Tt(a,e.options))return!1}else if(!jt(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return!("boolean"==typeof o&&e.isStale()!==o||n&&n!==e.state.fetchStatus||r&&!r(e))}function Ut(t,e){const{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(Kt(e.options.mutationKey)!==Kt(r))return!1}else if(!jt(e.options.mutationKey,r))return!1}return!(i&&e.state.status!==i||n&&!n(e))}function Tt(t,e){return(e?.queryKeyHashFn||Kt)(t)}function Kt(t){return JSON.stringify(t,((t,e)=>_t(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function jt(t,e){return t===e||typeof t==typeof e&&!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((s=>!jt(t[s],e[s])))}function It(t,e){if(t===e)return t;const s=Ht(t)&&Ht(e);if(s||_t(t)&&_t(e)){const i=s?t:Object.keys(t),n=i.length,r=s?e:Object.keys(e),a=r.length,o=s?[]:{};let h=0;for(let u=0;u<a;u++){const n=s?u:r[u];(!s&&i.includes(n)||s)&&void 0===t[n]&&void 0===e[n]?(o[n]=void 0,h++):(o[n]=It(t[n],e[n]),o[n]===t[n]&&void 0!==t[n]&&h++)}return n===a&&h===n?t:o}return e}function Lt(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function Ht(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function _t(t){if(!Gt(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!Gt(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function Gt(t){return"[object Object]"===Object.prototype.toString.call(t)}function Bt(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?It(t,e):e}function Nt(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function zt(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var $t=Symbol();function Jt(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==$t?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var Vt=new(i=class extends Et{constructor(){super(),Mt(this,t),Mt(this,e),Mt(this,s),Ct(this,s,(t=>{if(!qt&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){Ot(this,e)||this.setEventListener(Ot(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=Ot(this,e))||t.call(this),Ct(this,e,void 0))}setEventListener(t){var i;Ct(this,s,t),null==(i=Ot(this,e))||i.call(this),Ct(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){Ot(this,t)!==e&&(Ct(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){return"boolean"==typeof Ot(this,t)?Ot(this,t):"hidden"!==globalThis.document?.visibilityState}},t=new WeakMap,e=new WeakMap,s=new WeakMap,i),Xt=new(o=class extends Et{constructor(){super(),Mt(this,n,!0),Mt(this,r),Mt(this,a),Ct(this,a,(t=>{if(!qt&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}))}onSubscribe(){Ot(this,r)||this.setEventListener(Ot(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=Ot(this,r))||t.call(this),Ct(this,r,void 0))}setEventListener(t){var e;Ct(this,a,t),null==(e=Ot(this,r))||e.call(this),Ct(this,r,t(this.setOnline.bind(this)))}setOnline(t){Ot(this,n)!==t&&(Ct(this,n,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return Ot(this,n)}},n=new WeakMap,r=new WeakMap,a=new WeakMap,o);function Yt(){let t,e;const s=new Promise(((s,i)=>{t=s,e=i}));function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}function Zt(t){return Math.min(1e3*2**t,3e4)}function te(t){return"online"!==(t??"online")||Xt.isOnline()}var ee=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function se(t){return t instanceof ee}function ie(t){let e,s=!1,i=0,n=!1;const r=Yt(),a=()=>Vt.isFocused()&&("always"===t.networkMode||Xt.isOnline())&&t.canRun(),o=()=>te(t.networkMode)&&t.canRun(),h=s=>{n||(n=!0,t.onSuccess?.(s),e?.(),r.resolve(s))},u=s=>{n||(n=!0,t.onError?.(s),e?.(),r.reject(s))},c=()=>new Promise((s=>{e=t=>{(n||a())&&s(t)},t.onPause?.()})).then((()=>{e=void 0,n||t.onContinue?.()})),l=()=>{if(n)return;let e;const r=0===i?t.initialPromise:void 0;try{e=r??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(h).catch((e=>{if(n)return;const r=t.retry??(qt?0:3),o=t.retryDelay??Zt,h="function"==typeof o?o(i,e):o,d=!0===r||"number"==typeof r&&i<r||"function"==typeof r&&r(i,e);var f;!s&&d?(i++,t.onFail?.(i,e),(f=h,new Promise((t=>{setTimeout(t,f)}))).then((()=>a()?void 0:c())).then((()=>{s?u(e):l()}))):u(e)}))};return{promise:r,cancel:e=>{n||(u(new ee(e)),t.abort?.())},continue:()=>(e?.(),r),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:o,start:()=>(o()?l():c().then(l),r)}}var ne=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=t=>setTimeout(t,0);const r=i=>{e?t.push(i):n((()=>{s(i)}))};return{batch:r=>{let a;e++;try{a=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),re=(u=class{constructor(){Mt(this,h)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Qt(this.gcTime)&&Ct(this,h,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(qt?1/0:3e5))}clearGcTimeout(){Ot(this,h)&&(clearTimeout(Ot(this,h)),Ct(this,h,void 0))}},h=new WeakMap,u),ae=(b=class extends re{constructor(t){super(),Mt(this,v),Mt(this,c),Mt(this,l),Mt(this,d),Mt(this,f),Mt(this,p),Mt(this,y),Ct(this,y,!1),Ct(this,p,t.defaultOptions),this.setOptions(t.options),this.observers=[],Ct(this,d,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,Ct(this,c,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??Ot(this,c),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return Ot(this,f)?.promise}setOptions(t){this.options={...Ot(this,p),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||Ot(this,d).remove(this)}setData(t,e){const s=Bt(this.state.data,t,this.options);return kt(this,v,m).call(this,{data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){kt(this,v,m).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){const e=Ot(this,f)?.promise;return Ot(this,f)?.cancel(t),e?e.then(Ft).catch(Ft):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(Ot(this,c))}isActive(){return this.observers.some((t=>!1!==Dt(t.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===$t||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!xt(this.state.dataUpdatedAt,t)}onFocus(){const t=this.observers.find((t=>t.shouldFetchOnWindowFocus()));t?.refetch({cancelRefetch:!1}),Ot(this,f)?.continue()}onOnline(){const t=this.observers.find((t=>t.shouldFetchOnReconnect()));t?.refetch({cancelRefetch:!1}),Ot(this,f)?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),Ot(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(Ot(this,f)&&(Ot(this,y)?Ot(this,f).cancel({revert:!0}):Ot(this,f).cancelRetry()),this.scheduleGc()),Ot(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||kt(this,v,m).call(this,{type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(Ot(this,f))return Ot(this,f).continueRetry(),Ot(this,f).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const s=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(Ct(this,y,!0),s.signal)})},n={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const t=Jt(this.options,e),s={queryKey:this.queryKey,meta:this.meta};return i(s),Ct(this,y,!1),this.options.persister?this.options.persister(t,s,this):t(s)}};i(n),this.options.behavior?.onFetch(n,this),Ct(this,l,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||kt(this,v,m).call(this,{type:"fetch",meta:n.fetchOptions?.meta});const r=t=>{se(t)&&t.silent||kt(this,v,m).call(this,{type:"error",error:t}),se(t)||(Ot(this,d).config.onError?.(t,this),Ot(this,d).config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return Ct(this,f,ie({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(e){return void r(e)}Ot(this,d).config.onSuccess?.(t,this),Ot(this,d).config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else r(new Error(`${this.queryHash} data is undefined`))},onError:r,onFail:(t,e)=>{kt(this,v,m).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{kt(this,v,m).call(this,{type:"pause"})},onContinue:()=>{kt(this,v,m).call(this,{type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0})),Ot(this,f).start()}},c=new WeakMap,l=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,y=new WeakMap,v=new WeakSet,m=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...oe(e.data,this.options),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return se(s)&&s.revert&&Ot(this,l)?{...Ot(this,l),fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),ne.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),Ot(this,d).notify({query:this,type:"updated",action:t})}))},b);function oe(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:te(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var he=(w=class extends Et{constructor(t={}){super(),Mt(this,g),this.config=t,Ct(this,g,new Map)}build(t,e,s){const i=e.queryKey,n=e.queryHash??Tt(i,e);let r=this.get(n);return r||(r=new ae({cache:this,queryKey:i,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(r)),r}add(t){Ot(this,g).has(t.queryHash)||(Ot(this,g).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=Ot(this,g).get(t.queryHash);e&&(t.destroy(),e===t&&Ot(this,g).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){ne.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return Ot(this,g).get(t)}getAll(){return[...Ot(this,g).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>At(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>At(t,e))):e}notify(t){ne.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){ne.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){ne.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},g=new WeakMap,w),ue=(S=class extends re{constructor(t){super(),Mt(this,k),Mt(this,O),Mt(this,M),Mt(this,C),this.mutationId=t.mutationId,Ct(this,M,t.mutationCache),Ct(this,O,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){Ot(this,O).includes(t)||(Ot(this,O).push(t),this.clearGcTimeout(),Ot(this,M).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){Ct(this,O,Ot(this,O).filter((e=>e!==t))),this.scheduleGc(),Ot(this,M).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){Ot(this,O).length||("pending"===this.state.status?this.scheduleGc():Ot(this,M).remove(this))}continue(){return Ot(this,C)?.continue()??this.execute(this.state.variables)}async execute(t){Ct(this,C,ie({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{kt(this,k,R).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{kt(this,k,R).call(this,{type:"pause"})},onContinue:()=>{kt(this,k,R).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>Ot(this,M).canRun(this)}));const e="pending"===this.state.status,s=!Ot(this,C).canStart();try{if(!e){kt(this,k,R).call(this,{type:"pending",variables:t,isPaused:s}),await(Ot(this,M).config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&kt(this,k,R).call(this,{type:"pending",context:e,variables:t,isPaused:s})}const i=await Ot(this,C).start();return await(Ot(this,M).config.onSuccess?.(i,t,this.state.context,this)),await(this.options.onSuccess?.(i,t,this.state.context)),await(Ot(this,M).config.onSettled?.(i,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(i,null,t,this.state.context)),kt(this,k,R).call(this,{type:"success",data:i}),i}catch(i){try{throw await(Ot(this,M).config.onError?.(i,t,this.state.context,this)),await(this.options.onError?.(i,t,this.state.context)),await(Ot(this,M).config.onSettled?.(void 0,i,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,i,t,this.state.context)),i}finally{kt(this,k,R).call(this,{type:"error",error:i})}}finally{Ot(this,M).runNext(this)}}},O=new WeakMap,M=new WeakMap,C=new WeakMap,k=new WeakSet,R=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),ne.batch((()=>{Ot(this,O).forEach((e=>{e.onMutationUpdate(t)})),Ot(this,M).notify({mutation:this,type:"updated",action:t})}))},S),ce=(q=class extends Et{constructor(t={}){super(),Mt(this,P),Mt(this,E),this.config=t,Ct(this,P,new Map),Ct(this,E,Date.now())}build(t,e,s){const i=new ue({mutationCache:this,mutationId:++Rt(this,E)._,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){const e=le(t),s=Ot(this,P).get(e)??[];s.push(t),Ot(this,P).set(e,s),this.notify({type:"added",mutation:t})}remove(t){const e=le(t);if(Ot(this,P).has(e)){const s=Ot(this,P).get(e)?.filter((e=>e!==t));s&&(0===s.length?Ot(this,P).delete(e):Ot(this,P).set(e,s))}this.notify({type:"removed",mutation:t})}canRun(t){const e=Ot(this,P).get(le(t))?.find((t=>"pending"===t.state.status));return!e||e===t}runNext(t){const e=Ot(this,P).get(le(t))?.find((e=>e!==t&&e.state.isPaused));return e?.continue()??Promise.resolve()}clear(){ne.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}getAll(){return[...Ot(this,P).values()].flat()}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Ut(e,t)))}findAll(t={}){return this.getAll().filter((e=>Ut(t,e)))}notify(t){ne.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return ne.batch((()=>Promise.all(t.map((t=>t.continue().catch(Ft))))))}},P=new WeakMap,E=new WeakMap,q);function le(t){return t.options.scope?.id??String(t.mutationId)}function de(t){return{onFetch:(e,s)=>{const i=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,r=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},h=0;const u=async()=>{let s=!1;const u=Jt(e.options,e.fetchOptions),c=async(t,i,n)=>{if(s)return Promise.reject();if(null==i&&t.pages.length)return Promise.resolve(t);const r={queryKey:e.queryKey,pageParam:i,direction:n?"backward":"forward",meta:e.options.meta};var a;a=r,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",(()=>{s=!0})),e.signal)});const o=await u(r),{maxPages:h}=e.options,c=n?zt:Nt;return{pages:c(t.pages,o,h),pageParams:c(t.pageParams,i,h)}};if(n&&r.length){const t="backward"===n,e={pages:r,pageParams:a},s=(t?pe:fe)(i,e);o=await c(e,s,t)}else{const e=t??r.length;do{const t=0===h?a[0]??i.initialPageParam:fe(i,o);if(h>0&&null==t)break;o=await c(o,t),h++}while(h<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(u,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=u}}}function fe(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function pe(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}var ye=(K=class{constructor(t={}){Mt(this,F),Mt(this,Q),Mt(this,x),Mt(this,W),Mt(this,D),Mt(this,A),Mt(this,U),Mt(this,T),Ct(this,F,t.queryCache||new he),Ct(this,Q,t.mutationCache||new ce),Ct(this,x,t.defaultOptions||{}),Ct(this,W,new Map),Ct(this,D,new Map),Ct(this,A,0)}mount(){Rt(this,A)._++,1===Ot(this,A)&&(Ct(this,U,Vt.subscribe((async t=>{t&&(await this.resumePausedMutations(),Ot(this,F).onFocus())}))),Ct(this,T,Xt.subscribe((async t=>{t&&(await this.resumePausedMutations(),Ot(this,F).onOnline())}))))}unmount(){var t,e;Rt(this,A)._--,0===Ot(this,A)&&(null==(t=Ot(this,U))||t.call(this),Ct(this,U,void 0),null==(e=Ot(this,T))||e.call(this),Ct(this,T,void 0))}isFetching(t){return Ot(this,F).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return Ot(this,Q).findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return Ot(this,F).get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);if(void 0===e)return this.fetchQuery(t);{const s=this.defaultQueryOptions(t),i=Ot(this,F).build(this,s);return t.revalidateIfStale&&i.isStaleByTime(Wt(s.staleTime,i))&&this.prefetchQuery(s),Promise.resolve(e)}}getQueriesData(t){return Ot(this,F).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),n=Ot(this,F).get(i.queryHash),r=n?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,r);if(void 0!==a)return Ot(this,F).build(this,i).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return ne.batch((()=>Ot(this,F).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return Ot(this,F).get(e.queryHash)?.state}removeQueries(t){const e=Ot(this,F);ne.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=Ot(this,F),i={type:"active",...t};return ne.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(i,e))))}cancelQueries(t={},e={}){const s={revert:!0,...e},i=ne.batch((()=>Ot(this,F).findAll(t).map((t=>t.cancel(s)))));return Promise.all(i).then(Ft).catch(Ft)}invalidateQueries(t={},e={}){return ne.batch((()=>{if(Ot(this,F).findAll(t).forEach((t=>{t.invalidate()})),"none"===t.refetchType)return Promise.resolve();const s={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(s,e)}))}refetchQueries(t={},e){const s={...e,cancelRefetch:e?.cancelRefetch??!0},i=ne.batch((()=>Ot(this,F).findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(Ft)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(Ft)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=Ot(this,F).build(this,e);return s.isStaleByTime(Wt(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Ft).catch(Ft)}fetchInfiniteQuery(t){return t.behavior=de(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Ft).catch(Ft)}ensureInfiniteQueryData(t){return t.behavior=de(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Xt.isOnline()?Ot(this,Q).resumePausedMutations():Promise.resolve()}getQueryCache(){return Ot(this,F)}getMutationCache(){return Ot(this,Q)}getDefaultOptions(){return Ot(this,x)}setDefaultOptions(t){Ct(this,x,t)}setQueryDefaults(t,e){Ot(this,W).set(Kt(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...Ot(this,W).values()];let s={};return e.forEach((e=>{jt(t,e.queryKey)&&(s={...s,...e.defaultOptions})})),s}setMutationDefaults(t,e){Ot(this,D).set(Kt(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...Ot(this,D).values()];let s={};return e.forEach((e=>{jt(t,e.mutationKey)&&(s={...s,...e.defaultOptions})})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...Ot(this,x).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=Tt(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),!0!==e.enabled&&e.queryFn===$t&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...Ot(this,x).mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){Ot(this,F).clear(),Ot(this,Q).clear()}},F=new WeakMap,Q=new WeakMap,x=new WeakMap,W=new WeakMap,D=new WeakMap,A=new WeakMap,U=new WeakMap,T=new WeakMap,K),ve=(ct=class extends Et{constructor(t,e){super(),Mt(this,tt),Mt(this,j),Mt(this,I),Mt(this,L),Mt(this,H),Mt(this,_),Mt(this,G),Mt(this,B),Mt(this,N),Mt(this,z),Mt(this,$),Mt(this,J),Mt(this,V),Mt(this,X),Mt(this,Y),Mt(this,Z,new Set),this.options=e,Ct(this,j,t),Ct(this,N,null),Ct(this,B,Yt()),this.options.experimental_prefetchInRender||Ot(this,B).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(Ot(this,I).addObserver(this),me(Ot(this,I),this.options)?kt(this,tt,et).call(this):this.updateResult(),kt(this,tt,rt).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return be(Ot(this,I),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return be(Ot(this,I),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,kt(this,tt,at).call(this),kt(this,tt,ot).call(this),Ot(this,I).removeObserver(this)}setOptions(t,e){const s=this.options,i=Ot(this,I);if(this.options=Ot(this,j).defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof Dt(this.options.enabled,Ot(this,I)))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");kt(this,tt,ht).call(this),Ot(this,I).setOptions(this.options),s._defaulted&&!Lt(this.options,s)&&Ot(this,j).getQueryCache().notify({type:"observerOptionsUpdated",query:Ot(this,I),observer:this});const n=this.hasListeners();n&&ge(Ot(this,I),i,this.options,s)&&kt(this,tt,et).call(this),this.updateResult(e),!n||Ot(this,I)===i&&Dt(this.options.enabled,Ot(this,I))===Dt(s.enabled,Ot(this,I))&&Wt(this.options.staleTime,Ot(this,I))===Wt(s.staleTime,Ot(this,I))||kt(this,tt,st).call(this);const r=kt(this,tt,it).call(this);!n||Ot(this,I)===i&&Dt(this.options.enabled,Ot(this,I))===Dt(s.enabled,Ot(this,I))&&r===Ot(this,Y)||kt(this,tt,nt).call(this,r)}getOptimisticResult(t){const e=Ot(this,j).getQueryCache().build(Ot(this,j),t),s=this.createResult(e,t);return i=s,!Lt(this.getCurrentResult(),i)&&(Ct(this,H,s),Ct(this,G,this.options),Ct(this,_,Ot(this,I).state)),s;var i}getCurrentResult(){return Ot(this,H)}trackResult(t,e){const s={};return Object.keys(t).forEach((i=>{Object.defineProperty(s,i,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(i),e?.(i),t[i])})})),s}trackProp(t){Ot(this,Z).add(t)}getCurrentQuery(){return Ot(this,I)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=Ot(this,j).defaultQueryOptions(t),s=Ot(this,j).getQueryCache().build(Ot(this,j),e);return s.fetch().then((()=>this.createResult(s,e)))}fetch(t){return kt(this,tt,et).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then((()=>(this.updateResult(),Ot(this,H))))}createResult(t,e){const s=Ot(this,I),i=this.options,n=Ot(this,H),r=Ot(this,_),a=Ot(this,G),o=t!==s?t.state:Ot(this,L),{state:h}=t;let u,c={...h},l=!1;if(e._optimisticResults){const n=this.hasListeners(),r=!n&&me(t,e),a=n&&ge(t,s,e,i);(r||a)&&(c={...c,...oe(h.data,t.options)}),"isRestoring"===e._optimisticResults&&(c.fetchStatus="idle")}let{error:d,errorUpdatedAt:f,status:p}=c;if(e.select&&void 0!==c.data)if(n&&c.data===r?.data&&e.select===Ot(this,z))u=Ot(this,$);else try{Ct(this,z,e.select),u=e.select(c.data),u=Bt(n?.data,u,e),Ct(this,$,u),Ct(this,N,null)}catch(O){Ct(this,N,O)}else u=c.data;if(void 0!==e.placeholderData&&void 0===u&&"pending"===p){let t;if(n?.isPlaceholderData&&e.placeholderData===a?.placeholderData)t=n.data;else if(t="function"==typeof e.placeholderData?e.placeholderData(Ot(this,J)?.state.data,Ot(this,J)):e.placeholderData,e.select&&void 0!==t)try{t=e.select(t),Ct(this,N,null)}catch(O){Ct(this,N,O)}void 0!==t&&(p="success",u=Bt(n?.data,t,e),l=!0)}Ot(this,N)&&(d=Ot(this,N),u=Ot(this,$),f=Date.now(),p="error");const y="fetching"===c.fetchStatus,v="pending"===p,m="error"===p,b=v&&y,g=void 0!==u,w={status:p,fetchStatus:c.fetchStatus,isPending:v,isSuccess:"success"===p,isError:m,isInitialLoading:b,isLoading:b,data:u,dataUpdatedAt:c.dataUpdatedAt,error:d,errorUpdatedAt:f,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>o.dataUpdateCount||c.errorUpdateCount>o.errorUpdateCount,isFetching:y,isRefetching:y&&!v,isLoadingError:m&&!g,isPaused:"paused"===c.fetchStatus,isPlaceholderData:l,isRefetchError:m&&g,isStale:we(t,e),refetch:this.refetch,promise:Ot(this,B)};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===w.status?t.reject(w.error):void 0!==w.data&&t.resolve(w.data)},i=()=>{const t=Ct(this,B,w.promise=Yt());e(t)},n=Ot(this,B);switch(n.status){case"pending":t.queryHash===s.queryHash&&e(n);break;case"fulfilled":"error"!==w.status&&w.data===n.value||i();break;case"rejected":"error"===w.status&&w.error===n.reason||i()}}return w}updateResult(t){const e=Ot(this,H),s=this.createResult(Ot(this,I),this.options);if(Ct(this,_,Ot(this,I).state),Ct(this,G,this.options),void 0!==Ot(this,_).data&&Ct(this,J,Ot(this,I)),Lt(s,e))return;Ct(this,H,s);const i={};!1!==t?.listeners&&(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!Ot(this,Z).size)return!0;const i=new Set(s??Ot(this,Z));return this.options.throwOnError&&i.add("error"),Object.keys(Ot(this,H)).some((t=>{const s=t;return Ot(this,H)[s]!==e[s]&&i.has(s)}))})()&&(i.listeners=!0),kt(this,tt,ut).call(this,{...i,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&kt(this,tt,rt).call(this)}},j=new WeakMap,I=new WeakMap,L=new WeakMap,H=new WeakMap,_=new WeakMap,G=new WeakMap,B=new WeakMap,N=new WeakMap,z=new WeakMap,$=new WeakMap,J=new WeakMap,V=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,tt=new WeakSet,et=function(t){kt(this,tt,ht).call(this);let e=Ot(this,I).fetch(this.options,t);return t?.throwOnError||(e=e.catch(Ft)),e},st=function(){kt(this,tt,at).call(this);const t=Wt(this.options.staleTime,Ot(this,I));if(qt||Ot(this,H).isStale||!Qt(t))return;const e=xt(Ot(this,H).dataUpdatedAt,t);Ct(this,V,setTimeout((()=>{Ot(this,H).isStale||this.updateResult()}),e+1))},it=function(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(Ot(this,I)):this.options.refetchInterval)??!1},nt=function(t){kt(this,tt,ot).call(this),Ct(this,Y,t),!qt&&!1!==Dt(this.options.enabled,Ot(this,I))&&Qt(Ot(this,Y))&&0!==Ot(this,Y)&&Ct(this,X,setInterval((()=>{(this.options.refetchIntervalInBackground||Vt.isFocused())&&kt(this,tt,et).call(this)}),Ot(this,Y)))},rt=function(){kt(this,tt,st).call(this),kt(this,tt,nt).call(this,kt(this,tt,it).call(this))},at=function(){Ot(this,V)&&(clearTimeout(Ot(this,V)),Ct(this,V,void 0))},ot=function(){Ot(this,X)&&(clearInterval(Ot(this,X)),Ct(this,X,void 0))},ht=function(){const t=Ot(this,j).getQueryCache().build(Ot(this,j),this.options);if(t===Ot(this,I))return;const e=Ot(this,I);Ct(this,I,t),Ct(this,L,t.state),this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))},ut=function(t){ne.batch((()=>{t.listeners&&this.listeners.forEach((t=>{t(Ot(this,H))})),Ot(this,j).getQueryCache().notify({query:Ot(this,I),type:"observerResultsUpdated"})}))},ct);function me(t,e){return function(t,e){return!1!==Dt(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&be(t,e,e.refetchOnMount)}function be(t,e,s){if(!1!==Dt(e.enabled,t)){const i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&we(t,e)}return!1}function ge(t,e,s,i){return(t!==e||!1===Dt(i.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&we(t,s)}function we(t,e){return!1!==Dt(e.enabled,t)&&t.isStaleByTime(Wt(e.staleTime,t))}var Oe=(bt=class extends Et{constructor(t,e){super(),Mt(this,yt),Mt(this,lt),Mt(this,dt),Mt(this,ft),Mt(this,pt),Ct(this,lt,t),this.setOptions(e),this.bindMethods(),kt(this,yt,vt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const e=this.options;this.options=Ot(this,lt).defaultMutationOptions(t),Lt(this.options,e)||Ot(this,lt).getMutationCache().notify({type:"observerOptionsUpdated",mutation:Ot(this,ft),observer:this}),e?.mutationKey&&this.options.mutationKey&&Kt(e.mutationKey)!==Kt(this.options.mutationKey)?this.reset():"pending"===Ot(this,ft)?.state.status&&Ot(this,ft).setOptions(this.options)}onUnsubscribe(){this.hasListeners()||Ot(this,ft)?.removeObserver(this)}onMutationUpdate(t){kt(this,yt,vt).call(this),kt(this,yt,mt).call(this,t)}getCurrentResult(){return Ot(this,dt)}reset(){Ot(this,ft)?.removeObserver(this),Ct(this,ft,void 0),kt(this,yt,vt).call(this),kt(this,yt,mt).call(this)}mutate(t,e){return Ct(this,pt,e),Ot(this,ft)?.removeObserver(this),Ct(this,ft,Ot(this,lt).getMutationCache().build(Ot(this,lt),this.options)),Ot(this,ft).addObserver(this),Ot(this,ft).execute(t)}},lt=new WeakMap,dt=new WeakMap,ft=new WeakMap,pt=new WeakMap,yt=new WeakSet,vt=function(){const t=Ot(this,ft)?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};Ct(this,dt,{...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset})},mt=function(t){ne.batch((()=>{if(Ot(this,pt)&&this.hasListeners()){const e=Ot(this,dt).variables,s=Ot(this,dt).context;"success"===t?.type?(Ot(this,pt).onSuccess?.(t.data,e,s),Ot(this,pt).onSettled?.(t.data,null,e,s)):"error"===t?.type&&(Ot(this,pt).onError?.(t.error,e,s),Ot(this,pt).onSettled?.(void 0,t.error,e,s))}this.listeners.forEach((t=>{t(Ot(this,dt))}))}))},bt),Me=St.createContext(void 0),Ce=t=>{const e=St.useContext(Me);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},ke=({client:t,children:e})=>(St.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),Pt.jsx(Me.Provider,{value:t,children:e})),Re=St.createContext(!1);Re.Provider;var Se=St.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}());function Pe(t,e){return"function"==typeof t?t(...e):!!t}function Ee(){}var qe=(t,e,s)=>e.fetchOptimistic(t).catch((()=>{s.clearReset()}));function Fe(t,e){return function(t,e){const s=Ce(),i=St.useContext(Re),n=St.useContext(Se),r=s.defaultQueryOptions(t);s.getDefaultOptions().queries?._experimental_beforeQuery?.(r),r._optimisticResults=i?"isRestoring":"optimistic",(t=>{t.suspense&&(void 0===t.staleTime&&(t.staleTime=1e3),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3)))})(r),((t,e)=>{(t.suspense||t.throwOnError)&&(e.isReset()||(t.retryOnMount=!1))})(r,n),(t=>{St.useEffect((()=>{t.clearReset()}),[t])})(n);const a=!s.getQueryCache().get(r.queryHash),[o]=St.useState((()=>new e(s,r))),h=o.getOptimisticResult(r);if(St.useSyncExternalStore(St.useCallback((t=>{const e=i?()=>{}:o.subscribe(ne.batchCalls(t));return o.updateResult(),e}),[o,i]),(()=>o.getCurrentResult()),(()=>o.getCurrentResult())),St.useEffect((()=>{o.setOptions(r,{listeners:!1})}),[r,o]),((t,e)=>t?.suspense&&e.isPending)(r,h))throw qe(r,o,n);if((({result:t,errorResetBoundary:e,throwOnError:s,query:i})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&Pe(s,[t.error,i]))({result:h,errorResetBoundary:n,throwOnError:r.throwOnError,query:s.getQueryCache().get(r.queryHash)}))throw h.error;if(s.getDefaultOptions().queries?._experimental_afterQuery?.(r,h),r.experimental_prefetchInRender&&!qt&&((t,e)=>t.isLoading&&t.isFetching&&!e)(h,i)){const t=a?qe(r,o,n):s.getQueryCache().get(r.queryHash)?.promise;t?.catch(Ee).finally((()=>{o.updateResult()}))}return r.notifyOnChangeProps?h:o.trackResult(h)}(t,ve)}function Qe(t,e){const s=Ce(),[i]=St.useState((()=>new Oe(s,t)));St.useEffect((()=>{i.setOptions(t)}),[i,t]);const n=St.useSyncExternalStore(St.useCallback((t=>i.subscribe(ne.batchCalls(t))),[i]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),r=St.useCallback(((t,e)=>{i.mutate(t,e).catch(Ee)}),[i]);if(n.error&&Pe(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:r,mutateAsync:n.mutate}}export{ye as Q,Ce as a,ke as b,Qe as c,Fe as u};
