import{j as r}from"./radix-core-6kBL75b5.js";import o from"./MedicationDetails-hugfMUoS.js";import"./critical-DVX9Inzy.js";import"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./index-D89HBjcn.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./useWeight-CatlFLFx.js";import"./useAge-C_36_Zbj.js";import"./Footer-CliqTtAT.js";import"./FeedbackTrigger-CnktnpVq.js";import"./rocket-BrzzCnNA.js";import"./target-jx5HoNTw.js";import"./zap-3JoB1_vc.js";import"./book-open-JvVCwLVv.js";import"./star-DPJhgPy1.js";import"./circle-help-Bt3O9hrS.js";import"./instagram-BUkbqcNN.js";import"./collapsible-B6HfSnGs.js";import"./accordion-DlrGn5T8.js";import"./PatientInfoSection-Dti7R4KL.js";import"./scale-C0VY02RJ.js";import"./useAgeInput-CLfpowYq.js";import"./calendar-Bey2EULq.js";import"./user-B_6Qs7t0.js";import"./DosageDisplay-CuTo0RSr.js";import"./alert-B4jfnKuF.js";import"./plus-7ON-wNau.js";import"./lightbulb-BNaGkaNP.js";import"./external-link-B-0JBg3h.js";import"./stethoscope-C94gb3Zp.js";import"./syringe-CfMhlReq.js";import"./wind-Di0Li-OL.js";import"./bug-Cvma6Un5.js";import"./chevron-left-CUbzR6vp.js";import"./house-BhFW6iO4.js";function t(){return r.jsx(o,{})}export{t as default};
