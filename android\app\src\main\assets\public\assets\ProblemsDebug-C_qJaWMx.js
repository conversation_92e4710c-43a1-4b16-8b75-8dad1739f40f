import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{u as t}from"./query-vendor-B-7l6Nb3.js";import{b1 as r,B as a,a7 as i,R as l,W as d,Z as n,n as c,U as o,ay as m,ak as x,s as h}from"./index-CR7o3nEo.js";import{a as j}from"./router-BAzpOxbo.js";import{R as g}from"./refresh-cw-CMhthn0W.js";import{B as p}from"./bug-VyH6d1R-.js";import{T as u}from"./trending-up-edIxspQk.js";import{Z as N}from"./zap-BgS9hP5n.js";import{a as f}from"./date-vendor-BOcTQe0E.js";import{p as b}from"./pt-BR-a_BmBHfW.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";function v(){const v=j(),{getQueueStats:w,clearQueue:y,testCircuitBreaker:I}=r(),[C,R]=s.useState({pending:0,processing:!1});s.useEffect((()=>{const e=()=>{const e=w();R((s=>s.pending!==e.pending||s.processing!==e.processing?e:s))};e();const s=setInterval(e,5e3);return()=>clearInterval(s)}),[]);const{data:T,isLoading:_,refetch:M}=t({queryKey:["system-problems"],queryFn:async()=>{const{data:e,error:s}=await h.from("site_analytics").select("*").eq("action_type","system_problem").order("created_at",{ascending:!1}).limit(100);if(s)throw s;return e},refetchInterval:3e4}),L=T?.reduce(((e,s)=>{const t=s.metadata?.problem_type||"UNKNOWN";return e[t]||(e[t]=[]),e[t].push(s),e}),{})||{},k={total:T?.length||0,critical:T?.filter((e=>"CRITICAL"===e.metadata?.severity)).length||0,high:T?.filter((e=>"HIGH"===e.metadata?.severity)).length||0,medium:T?.filter((e=>"MEDIUM"===e.metadata?.severity)).length||0,last24h:T?.filter((e=>new Date(e.created_at).getTime()>Date.now()-864e5)).length||0},E=e=>{switch(e){case"CRITICAL":return"bg-red-100 text-red-800 border-red-200";case"HIGH":return"bg-orange-100 text-orange-800 border-orange-200";case"MEDIUM":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"LOW":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},A=s=>{switch(s){case"CIRCUIT_BREAKER":return e.jsx(N,{className:"h-4 w-4"});case"RATE_LIMIT":return e.jsx(c,{className:"h-4 w-4"});case"BANDWIDTH_ALERT":return e.jsx(u,{className:"h-4 w-4"});case"SYSTEM_ERROR":return e.jsx(p,{className:"h-4 w-4"});default:return e.jsx(x,{className:"h-4 w-4"})}};return e.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 rounded-2xl p-8 text-white shadow-xl",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(a,{variant:"ghost",size:"sm",onClick:()=>v("/admin/dashboard"),className:"flex items-center gap-2 hover:bg-white/20 text-white border-white/20",children:[e.jsx(i,{className:"h-4 w-4"}),"Voltar"]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-4xl font-bold mb-2",children:"🚨 Debug de Problemas"}),e.jsx("p",{className:"text-orange-100 text-lg",children:"Monitoramento e debug de problemas do sistema"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(a,{variant:"ghost",size:"sm",onClick:()=>M(),className:"text-white border-white/20 hover:bg-white/20",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Atualizar"]}),C.pending>0&&e.jsxs(a,{variant:"ghost",size:"sm",onClick:()=>{y(),R({pending:0,processing:!1})},className:"text-white border-white/20 hover:bg-white/20",children:["Limpar Fila (",C.pending,")"]}),e.jsx(a,{variant:"ghost",size:"sm",onClick:I,className:"text-white border-white/20 hover:bg-white/20",children:"🧪 Testar Circuit Breaker"})]})]})}),e.jsxs(l,{children:[e.jsx(d,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[e.jsx(c,{className:"h-5 w-5"}),"Status da Fila de Logs"]})}),e.jsx(o,{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C.pending}),e.jsx("div",{className:"text-sm text-blue-600",children:"Logs Pendentes"})]}),e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:C.processing?"SIM":"NÃO"}),e.jsx("div",{className:"text-sm text-green-600",children:"Processando"})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsx(l,{children:e.jsxs(o,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold",children:k.total}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Total"})]})}),e.jsx(l,{children:e.jsxs(o,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:k.critical}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Críticos"})]})}),e.jsx(l,{children:e.jsxs(o,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:k.high}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Altos"})]})}),e.jsx(l,{children:e.jsxs(o,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:k.medium}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Médios"})]})}),e.jsx(l,{children:e.jsxs(o,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:k.last24h}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Últimas 24h"})]})})]}),Object.entries(L).map((([s,t])=>e.jsxs(l,{children:[e.jsx(d,{children:e.jsxs(n,{className:"flex items-center gap-2",children:[A(s),s.replace("_"," ")," (",t.length,")"]})}),e.jsx(o,{children:e.jsxs("div",{className:"space-y-3",children:[t.slice(0,10).map((s=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(m,{className:E(s.metadata?.severity),children:s.metadata?.severity}),e.jsx("span",{className:"font-medium",children:s.metadata?.component})]}),e.jsx("span",{className:"text-sm text-muted-foreground",children:f(new Date(s.created_at),"dd/MM/yyyy HH:mm:ss",{locale:b})})]}),e.jsx("p",{className:"text-sm mb-2",children:s.metadata?.message}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[e.jsx("strong",{children:"URL:"})," ",s.page_url]}),s.metadata&&Object.keys(s.metadata).length>0&&e.jsxs("details",{className:"mt-2",children:[e.jsx("summary",{className:"text-xs cursor-pointer text-blue-600",children:"Ver detalhes técnicos"}),e.jsx("pre",{className:"text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto",children:JSON.stringify(s.metadata,null,2)})]})]},s.id))),t.length>10&&e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["... e mais ",t.length-10," problemas deste tipo"]})]})})]},s))),_&&e.jsx(l,{children:e.jsx(o,{className:"p-8 text-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Carregando problemas..."})})}),!_&&0===T?.length&&e.jsx(l,{children:e.jsx(o,{className:"p-8 text-center",children:e.jsx("div",{className:"text-green-600 font-medium",children:"🎉 Nenhum problema registrado! Sistema funcionando perfeitamente."})})})]})}export{v as default};
