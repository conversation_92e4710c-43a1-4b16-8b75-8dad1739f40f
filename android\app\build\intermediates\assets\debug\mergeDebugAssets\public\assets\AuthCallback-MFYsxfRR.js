import{j as e}from"./radix-core-6kBL75b5.js";import{r}from"./critical-DVX9Inzy.js";import{s,i as a,u as t,aE as o,V as i,L as n}from"./index-D9amGMlQ.js";import{a as c}from"./router-BAzpOxbo.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const l=async(e,r,a)=>{try{const t={updated_at:(new Date).toISOString()};let o=!1;if(!r.avatar_url||a?.avatar_url&&a.avatar_url===r.avatar_url||(t.avatar_url=r.avatar_url,o=!0),!r.full_name||a?.full_name&&a.full_name===r.full_name||(t.full_name=r.full_name,o=!0),!o)return{success:!0,profile:a,isNewUser:!1};const{data:i,error:n}=await s.from("profiles").update(t).eq("id",e.id).select().single();if(n)throw n;return{success:!0,profile:i,isNewUser:!1}}catch(t){return{success:!1,error:t.message||"Erro ao atualizar perfil",isNewUser:!1}}},u=()=>{const u=c(),{showNotification:d}=a(),{refreshProfile:m}=t(),[p,f]=r.useState("loading"),[_,x]=r.useState("Processando autenticação..."),[g,h]=r.useState(!1);return r.useEffect((()=>{g||(async()=>{h(!0);try{const{data:r,error:a}=await s.auth.getSession();if(a)throw a;if(!r.session)throw new Error("Nenhuma sessão encontrada");{const t=r.session.access_token;try{const e=t.split(".");if((JSON.parse(atob(e[1])).amr||[]).some((e=>"recovery"===e.method)))return sessionStorage.setItem("recovery_login_detected","true"),sessionStorage.setItem("recovery_user_id",r.session.user.id),void u("/reset-password")}catch(e){}f("success"),x("Login realizado com sucesso!");const o=r.session.user,i=await(async e=>{try{const r=(e=>{const r=e.user_metadata||{},s=r.full_name||r.name||"",a=r.avatar_url||r.picture||"";return{id:e.id,full_name:s,avatar_url:a,is_student:!1,is_professional:!0,formation_area:"Não informado",graduation_year:"Não informado",updated_at:(new Date).toISOString()}})(e),{data:a,error:t}=await s.from("profiles").select("\n        id, full_name, avatar_url, formation_area, graduation_year,\n        is_student, is_professional, is_admin, specialty, preparation_type,\n        theme_preference, premium, created_at, updated_at\n      ").eq("id",e.id).single();if(t&&"PGRST116"!==t.code)throw t;if(a)return l(e,r,a);{const{data:e,error:a}=await s.from("profiles").insert(r).select().single();if(a)throw a;return{success:!0,profile:e,isNewUser:!0}}}catch(a){return{success:!1,error:a.message||"Erro desconhecido",isNewUser:!1}}})(o);i.success,await m(o.id,o.email);const n=((e,r)=>{const s=e.user_metadata?.full_name?.split(" ")[0]||e.user_metadata?.name?.split(" ")[0]||"",a=(new Date).getHours();let t="Bem-vindo";return t=a>=5&&a<12?"Bom dia":a>=12&&a<18?"Boa tarde":"Boa noite",r?{title:`${t}${s?`, ${s}`:""}!`,description:"Sua conta foi criada com sucesso. Complete seu perfil quando desejar para uma experiência personalizada."}:{title:`${t}${s?`, ${s}`:""} de volta!`,description:"Login realizado com sucesso."}})(o,i.isNewUser);d({title:n.title,description:n.description,type:"success",buttonText:"Continuar"}),setTimeout((()=>{u("/",{replace:!0})}),2e3)}}catch(r){f("error");let e="Erro ao processar autenticação.";r.message?.includes("access_denied")?e="Acesso negado. Você cancelou o login ou negou as permissões.":r.message?.includes("invalid_request")?e="Solicitação inválida. Tente fazer login novamente.":r.message?.includes("network")&&(e="Erro de conexão. Verifique sua internet."),x(e),d({title:"Erro na autenticação",description:e,type:"error",buttonText:"Tentar novamente"}),setTimeout((()=>{u("/",{replace:!0})}),3e3)}})()}),[u,d,g,m]),e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e.jsx("div",{className:"max-w-md w-full mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[e.jsx("div",{className:"flex justify-center mb-4",children:(()=>{switch(p){case"loading":return e.jsx(n,{className:"h-8 w-8 animate-spin text-blue-500"});case"success":return e.jsx(i,{className:"h-8 w-8 text-green-500"});case"error":return e.jsx(o,{className:"h-8 w-8 text-red-500"})}})()}),e.jsxs("h1",{className:`text-xl font-semibold mb-2 ${(()=>{switch(p){case"loading":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600"}})()}`,children:["loading"===p&&"Processando...","success"===p&&"Sucesso!","error"===p&&"Erro"]}),e.jsx("p",{className:"text-gray-600 mb-4",children:_}),"loading"===p&&e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-500 h-2 rounded-full animate-pulse",style:{width:"60%"}})}),"error"===p&&e.jsx("button",{onClick:()=>u("/",{replace:!0}),className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"Voltar ao início"})]})})})};export{u as default};
