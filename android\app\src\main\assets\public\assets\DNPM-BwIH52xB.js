import{j as e}from"./radix-core-6kBL75b5.js";import{u as r}from"./query-vendor-B-7l6Nb3.js";import{aa as a,a7 as s,B as t,C as o,s as l}from"./index-DwykrzWu.js";import d from"./Footer-CEErUVD6.js";import{r as i}from"./critical-DVX9Inzy.js";import{L as n}from"./router-BAzpOxbo.js";import{C as m,a as c}from"./childcareSEOData-D3dGewWg.js";import{C as x}from"./chevron-left-UiG5RJWe.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-Cce_pfRl.js";import"./rocket-BVKdk9NC.js";import"./target-Bq7BybE4.js";import"./zap-sktuObTW.js";import"./book-open-vsXyzIQN.js";import"./star-CVUfjIpQ.js";import"./circle-help-DkV0sebI.js";import"./instagram-BgC8q_0n.js";const g=()=>{const g=c.dnpm,[u,h]=i.useState(1),{data:b,isLoading:p}=r({queryKey:["dnpm-milestones"],queryFn:async()=>{const{data:e,error:r}=await l.from("pedbook_dnpm_milestones").select("*").order("age_months");if(r)throw r;return e}});if(p)return e.jsxs("div",{className:"min-h-screen flex flex-col dark:bg-slate-900",children:[e.jsx(a,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-12",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto"}),e.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mx-auto"}),e.jsx("div",{className:"h-96 bg-gray-200 dark:bg-gray-700 rounded"})]})}),e.jsx(d,{})]});const j=b?Math.ceil(b.length/1):0,f=b?b[u-1]:null;return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900",children:[e.jsx(m,{...g}),e.jsx(a,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-12",children:[e.jsxs(n,{to:"/puericultura",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(s,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Puericultura"})]}),e.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 border border-blue-100 dark:border-slate-700 transition-all duration-500 hover:shadow-xl animate-fade-in-up",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[e.jsxs("div",{className:"flex-1 space-y-6",children:[e.jsxs("h2",{className:"text-2xl font-semibold flex items-center gap-2 text-blue-700 dark:text-blue-400",children:[f.age_months," Meses","years"===f.age_type&&e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["(",f.age_years," anos)"]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 rounded-lg bg-gradient-to-r from-blue-50 to-white border border-blue-100 transition-all duration-300 hover:shadow-md dark:from-blue-900/20 dark:to-slate-800 dark:border-blue-900/30",children:[e.jsxs("h3",{className:"font-semibold text-blue-600 dark:text-blue-400 flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-400"}),"Social e Emocional"]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 mt-2",children:f.social_emotional})]}),e.jsxs("div",{className:"p-4 rounded-lg bg-gradient-to-r from-purple-50 to-white border border-purple-100 transition-all duration-300 hover:shadow-md dark:from-purple-900/20 dark:to-slate-800 dark:border-purple-900/30",children:[e.jsxs("h3",{className:"font-semibold text-purple-600 dark:text-purple-400 flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-purple-400"}),"Linguagem e Comunicação"]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 mt-2",children:f.language_communication})]}),e.jsxs("div",{className:"p-4 rounded-lg bg-gradient-to-r from-green-50 to-white border border-green-100 transition-all duration-300 hover:shadow-md dark:from-green-900/20 dark:to-slate-800 dark:border-green-900/30",children:[e.jsxs("h3",{className:"font-semibold text-green-600 dark:text-green-400 flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-green-400"}),"Cognição"]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 mt-2",children:f.cognition})]}),e.jsxs("div",{className:"p-4 rounded-lg bg-gradient-to-r from-orange-50 to-white border border-orange-100 transition-all duration-300 hover:shadow-md dark:from-orange-900/20 dark:to-slate-800 dark:border-orange-900/30",children:[e.jsxs("h3",{className:"font-semibold text-orange-600 dark:text-orange-400 flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-orange-400"}),"Motora/Física"]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 mt-2",children:f.motor_physical})]})]})]}),f.image_url&&e.jsx("div",{className:"lg:w-96",children:e.jsx("img",{src:f.image_url,alt:`Desenvolvimento ${f.age_months} meses`,className:"rounded-lg w-full h-auto shadow-lg transition-transform duration-300 hover:scale-105"})})]})}),e.jsxs("div",{className:"flex justify-between gap-4",children:[e.jsxs(t,{variant:"outline",onClick:()=>{h((e=>Math.max(e-1,1)))},disabled:1===u,className:"w-full sm:w-auto bg-white hover:bg-blue-50 border-blue-200 text-blue-700 transition-all duration-300 hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-slate-700 dark:text-blue-400",children:[e.jsx(x,{className:"w-4 h-4 mr-2"}),"Anterior"]}),e.jsxs(t,{variant:"outline",onClick:()=>{h((e=>Math.min(e+1,j)))},disabled:u===j,className:"w-full sm:w-auto bg-white hover:bg-blue-50 border-blue-200 text-blue-700 transition-all duration-300 hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-slate-700 dark:text-blue-400",children:["Próximo",e.jsx(o,{className:"w-4 h-4 ml-2"})]})]}),e.jsx("p",{className:"text-center text-sm text-gray-500 dark:text-gray-400",children:"Referência: Cartilha de Desenvolvimento 2m-5anos, CDC/SBP"})]}),e.jsx(d,{})]})};export{g as default};
