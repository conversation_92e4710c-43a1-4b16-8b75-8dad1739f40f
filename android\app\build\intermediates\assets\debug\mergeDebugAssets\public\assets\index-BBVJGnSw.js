import{j as e}from"./radix-core-6kBL75b5.js";import{r as t}from"./critical-DVX9Inzy.js";import{aN as n,aO as s,aP as o,aQ as r,aR as i,aS as c}from"./index-Dq2DDcRF.js";class u extends t.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function a({children:s,isPresent:o}){const r=t.useId(),i=t.useRef(null),c=t.useRef({width:0,height:0,top:0,left:0}),{nonce:a}=t.useContext(n);return t.useInsertionEffect((()=>{const{width:e,height:t,top:n,left:s}=c.current;if(o||!i.current||!e||!t)return;i.current.dataset.motionPopId=r;const u=document.createElement("style");return a&&(u.nonce=a),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`\n          [data-motion-pop-id="${r}"] {\n            position: absolute !important;\n            width: ${e}px !important;\n            height: ${t}px !important;\n            top: ${n}px !important;\n            left: ${s}px !important;\n          }\n        `),()=>{document.head.removeChild(u)}}),[o]),e.jsx(u,{isPresent:o,childRef:i,sizeRef:c,children:t.cloneElement(s,{ref:i})})}const l=({children:n,initial:r,isPresent:i,onExitComplete:c,custom:u,presenceAffectsLayout:l,mode:p})=>{const f=s(d),h=t.useId(),m=t.useCallback((e=>{f.set(e,!0);for(const t of f.values())if(!t)return;c&&c()}),[f,c]),x=t.useMemo((()=>({id:h,initial:r,isPresent:i,custom:u,onExitComplete:m,register:e=>(f.set(e,!1),()=>f.delete(e))})),l?[Math.random(),m]:[i,m]);return t.useMemo((()=>{f.forEach(((e,t)=>f.set(t,!1)))}),[i]),t.useEffect((()=>{!i&&!f.size&&c&&c()}),[i]),"popLayout"===p&&(n=e.jsx(a,{isPresent:i,children:n})),e.jsx(o.Provider,{value:x,children:n})};function d(){return new Map}const p=e=>e.key||"";function f(e){const n=[];return t.Children.forEach(e,(e=>{t.isValidElement(e)&&n.push(e)})),n}const h=({children:n,custom:o,initial:u=!0,onExitComplete:a,presenceAffectsLayout:d=!0,mode:h="sync",propagate:m=!1})=>{const[x,g]=r(m),E=t.useMemo((()=>f(n)),[n]),C=m&&!x?[]:E.map(p),P=t.useRef(!0),R=t.useRef(E),j=s((()=>new Map)),[v,w]=t.useState(E),[y,M]=t.useState(E);i((()=>{P.current=!1,R.current=E;for(let e=0;e<y.length;e++){const t=p(y[e]);C.includes(t)?j.delete(t):!0!==j.get(t)&&j.set(t,!1)}}),[y,C.length,C.join("-")]);const L=[];if(E!==v){let e=[...E];for(let t=0;t<y.length;t++){const n=y[t],s=p(n);C.includes(s)||(e.splice(t,0,n),L.push(n))}return"wait"===h&&L.length&&(e=L),M(f(e)),void w(E)}const{forceRender:$}=t.useContext(c);return e.jsx(e.Fragment,{children:y.map((t=>{const n=p(t),s=!(m&&!x)&&(E===y||C.includes(n));return e.jsx(l,{isPresent:s,initial:!(P.current&&!u)&&void 0,custom:s?void 0:o,presenceAffectsLayout:d,mode:h,onExitComplete:s?void 0:()=>{if(!j.has(n))return;j.set(n,!0);let e=!0;j.forEach((t=>{t||(e=!1)})),e&&(null==$||$(),M(R.current),m&&(null==g||g()),a&&a())},children:t},n)}))})};export{h as A};
