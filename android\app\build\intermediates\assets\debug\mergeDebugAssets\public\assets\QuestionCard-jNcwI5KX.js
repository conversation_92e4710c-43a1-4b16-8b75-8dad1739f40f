import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,j as r,X as t,d as n,T as i,B as l,ba as o,s as d,D as c,aK as m,e as u,f as x,g,av as h,ak as p,aJ as f,R as b,U as v,W as j,Z as y,V as k,aE as N,C as w,aL as _,aH as A}from"./index-D9amGMlQ.js";import{A as S,b as C}from"./alert-DF0vYpCj.js";import{e as I}from"./ensureUserId-C446r2qh.js";import{B as q}from"./bug-DnXwJCtk.js";import{S as E}from"./send-KEFbDwrh.js";import{E as T}from"./eye-BHsZrBlz.js";import{C as R}from"./circle-help-DFUIKtE9.js";import{C as z}from"./chevron-left-Cmyma3W5.js";import{T as O}from"./thumbs-up-nwkNMwpF.js";import{T as L}from"./thumbs-down-CRDlSs7S.js";import{L as M,P as B}from"./LazyImage-jHXozEpV.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=s("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]),F=({alternatives:s,selectedAnswer:n,setSelectedAnswer:i,hasAnswered:l,correct_answer:o,statistics:d,alternativeComments:c,questionId:m="default"})=>{const[u,x]=a.useState({}),[g,h]=a.useState(m),p=u[m]||[];a.useEffect((()=>{g!==m&&("default"!==g&&x((e=>{const a={...e};return delete a[g],a})),h(m))}),[m,g,l]);const f=e=>e.replace(/\r\n/g,"\n").replace(/\r/g,"\n").replace(/\n/g,"<br />"),b="number"==typeof o?o:parseInt(String(o));return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Alternativas:"}),e.jsx("div",{className:"space-y-3",children:s.map(((a,s)=>{const o=s===b,u=n===String(s+1),g=l&&u&&!o,h=p.includes(s);return e.jsxs("div",{className:r("p-4 rounded-lg border transition-all",l?o?"bg-green-50 dark:bg-green-900/30 border-green-300 dark:border-green-700":g?"bg-red-50 dark:bg-red-900/30 border-red-300 dark:border-red-700":"bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600":u?"bg-blue-50 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700":"bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20",i&&!l?"cursor-pointer":"cursor-default"),onClick:()=>{i&&!l&&i(String(s+1))},children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:r("flex items-center justify-center w-6 h-6 rounded-full text-sm font-medium mt-0.5",l?o?"bg-green-500 text-white":g?"bg-red-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300":u?"bg-blue-500 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"),children:String.fromCharCode(65+s)}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:r("text-gray-700 dark:text-gray-300 whitespace-pre-line",h&&"line-through text-gray-400 dark:text-gray-500"),dangerouslySetInnerHTML:{__html:f(a)}})}),!l&&e.jsx("button",{type:"button",className:r("p-1 ml-2 rounded-full transition-colors",h?"bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-400":"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),onClick:e=>{e.stopPropagation(),(e=>{x((a=>{const s=a[m]||[];let r;return r=s.includes(e)?s.filter((a=>a!==e)):[...s,e],{...a,[m]:r}}))})(s)},children:e.jsx(t,{size:16})})]}),l&&c&&c[s+1]&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:e.jsx("div",{className:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line",dangerouslySetInnerHTML:{__html:f(c[s+1])}})}),l&&d&&d[s]&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Escolhida por ",d[s].percentage.toFixed(1),"% (",d[s].count," pessoas)"]}),e.jsx("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 h-2 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-blue-500",style:{width:`${d[s].percentage}%`}})})]})})]},s)}))})]})},V=({value:a,onChange:s,onSubmit:r,onAnalyze:t,readOnly:o=!1,hasAnswered:d,isAnalyzing:c=!1})=>{const{toast:m}=n();return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{value:a,onChange:e=>s(e.target.value),placeholder:"Digite sua resposta aqui...",className:"min-h-[150px]",readOnly:o||d}),!d&&e.jsx("div",{className:"flex justify-end",children:e.jsx(l,{onClick:()=>{a.trim()?r():m({title:"Resposta vazia",description:"Por favor, escreva sua resposta antes de confirmar",variant:"destructive"})},disabled:!a.trim()||o,className:"w-full md:w-auto",children:"Confirmar Resposta"})})]}),d&&e.jsxs("div",{className:"mt-4",children:[e.jsx("div",{className:"font-medium text-gray-700 mb-2",children:"Sua Resposta:"}),e.jsx("div",{className:"p-4 rounded-lg border bg-gray-50 whitespace-pre-wrap",children:a}),t&&e.jsx("div",{className:"flex justify-end mt-4",children:e.jsx(l,{onClick:t,disabled:c,className:"w-full md:w-auto bg-blue-600 hover:bg-blue-700",children:c?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Analisando..."]}):"Gerar análise da minha resposta"})})]})]})},G=({questionId:s,userId:r})=>{const[t,o]=a.useState(!1),[f,b]=a.useState("error"),[v,j]=a.useState(""),[y,k]=a.useState(!1),{toast:N}=n();return e.jsxs(c,{open:t,onOpenChange:e=>{o(e),e||(j(""),b("error"))},children:[e.jsx(m,{asChild:!0,children:e.jsx(l,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 group shadow-sm",title:"Reportar problema na análise",children:e.jsx(D,{className:"h-3.5 w-3.5 text-gray-500 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors"})})}),e.jsxs(u,{className:"max-w-[95vw] max-h-[90vh] w-full sm:max-w-lg rounded-xl overflow-y-auto",children:[e.jsxs(x,{className:"pb-4",children:[e.jsxs(g,{className:"flex items-center gap-2 text-lg",children:[e.jsx(D,{className:"h-5 w-5 text-red-600"}),"Reportar Problema na Análise"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-2",children:"Ajude-nos a melhorar a qualidade das análises reportando problemas ou sugerindo melhorias."})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-3 text-sm",children:"Tipo de feedback:"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-2",children:[e.jsxs(l,{variant:"error"===f?"default":"outline",onClick:()=>b("error"),className:"flex items-center gap-2 justify-start h-auto p-3 text-left",children:[e.jsx(q,{className:"h-4 w-4 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Erro"}),e.jsx("div",{className:"text-xs opacity-70",children:"Informação incorreta"})]})]}),e.jsxs(l,{variant:"suggestion"===f?"default":"outline",onClick:()=>b("suggestion"),className:"flex items-center gap-2 justify-start h-auto p-3 text-left",children:[e.jsx(h,{className:"h-4 w-4 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Sugestão"}),e.jsx("div",{className:"text-xs opacity-70",children:"Melhoria possível"})]})]}),e.jsxs(l,{variant:"problem"===f?"default":"outline",onClick:()=>b("problem"),className:"flex items-center gap-2 justify-start h-auto p-3 text-left",children:[e.jsx(p,{className:"h-4 w-4 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Problema"}),e.jsx("div",{className:"text-xs opacity-70",children:"Outro problema"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-3 text-sm",children:"Descreva o problema:"}),e.jsx(i,{placeholder:"error"===f?"Ex: A explicação da alternativa B está incorreta porque...":"suggestion"===f?"Ex: Seria útil adicionar mais detalhes sobre...":"Ex: A análise não aborda o ponto principal da questão...",value:v,onChange:e=>j(e.target.value),className:"min-h-[120px] resize-none"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"Seja específico para nos ajudar a melhorar a análise."})]}),e.jsxs("div",{className:"flex gap-3 pt-2",children:[e.jsx(l,{variant:"outline",onClick:()=>o(!1),className:"flex-1",children:"Cancelar"}),e.jsxs(l,{onClick:async()=>{if(v.trim())if(s){k(!0);try{const e=await I(),{error:a}=await d.from("medevo_feedbacks").insert({question_id:s,user_id:e,feedback_type:f,message:v.trim()});if(a)throw a;N({title:"Feedback enviado",description:"Obrigado por nos ajudar a melhorar!"}),j(""),o(!1)}catch(e){N({title:"Erro ao enviar feedback",description:"Tente novamente mais tarde.",variant:"destructive"})}finally{k(!1)}}else N({title:"Erro",description:"ID da questão não disponível. Tente novamente mais tarde.",variant:"destructive"});else N({title:"Erro",description:"Por favor, escreva um feedback detalhado.",variant:"destructive"})},disabled:y||!v.trim(),className:"flex-1 flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4"}),y?"Enviando...":"Enviar Feedback"]})]})]})]})]})},J=e=>{if(!e||"string"!=typeof e)return e;if(e.includes("```json")||e.includes('"alternativas"')&&e.includes('"comentario_final"'))try{let a=e;a=a.replace(/```json\s*/g,"").replace(/```\s*/g,""),a=a.replace(/<[^>]*>/g,"");const s=JSON.parse(a);if(s.comentario_final&&"string"==typeof s.comentario_final)return s.comentario_final;if(s.alternativas&&Array.isArray(s.alternativas))return s.alternativas.map((e=>e.comentario)).filter(Boolean).join("<br><br>")||"Análise não disponível."}catch(a){return e.replace(/```json/g,"").replace(/```/g,"").replace(/<[^>]*>/g,"").replace(/\{[\s\S]*"comentario_final":\s*"([^"]*)"[\s\S]*\}/g,"$1").trim()||"Análise não disponível."}return e},P=()=>e.jsxs(c,{children:[e.jsx(m,{asChild:!0,children:e.jsx(l,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-gray-100",children:e.jsx(R,{className:"h-4 w-4 text-gray-500"})})}),e.jsxs(u,{className:"max-w-[90vw] max-h-[80vh] w-full sm:max-w-md rounded-lg overflow-y-auto",children:[e.jsx(x,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5"}),"Como funciona a Análise da IA"]})}),e.jsxs("div",{className:"space-y-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"📝 Gerar Análise vs Ver Análise"}),e.jsxs("p",{className:"text-gray-600 leading-relaxed",children:[e.jsx("strong",{children:'"Gerar Análise"'})," aparece quando a questão ainda não possui uma análise da IA. Ao clicar, nossa IA criará uma explicação detalhada para você."]}),e.jsxs("p",{className:"text-gray-600 leading-relaxed mt-2",children:[e.jsx("strong",{children:'"Ver Análise"'})," aparece quando a questão já possui uma análise pronta. Isso significa que outro usuário já gerou a análise anteriormente."]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"🤝 Análise Compartilhada"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Quando você gera uma análise, ela fica disponível para todos os outros usuários. Isso economiza tempo e garante que todos tenham acesso às mesmas explicações de qualidade."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"🔄 Melhoria Contínua"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:"As análises são constantemente aprimoradas com base no feedback dos usuários. Se você encontrar algo incorreto ou pouco relevante, pode nos ajudar!"})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-200 dark:border-blue-700",children:[e.jsxs("h4",{className:"font-semibold mb-2 flex items-center gap-2 text-blue-800 dark:text-blue-300",children:[e.jsx(D,{className:"h-4 w-4"}),"Como reportar problemas"]}),e.jsxs("p",{className:"text-blue-700 dark:text-blue-300 text-sm leading-relaxed",children:["Se uma análise não estiver correta ou relevante, clique no ícone de bandeira",e.jsx(D,{className:"h-3 w-3 inline mx-1"})," no canto superior direito da análise para deixar seu feedback. Isso nos ajuda a melhorar continuamente."]})]})]})]})]}),$=({question:s,onCommentaryGenerated:r,existingCommentary:t,sessionId:i})=>{const{generateCommentary:c,isLoading:m,error:u,resetCommentary:x}=(()=>{const[e,s]=a.useState(!1),[r,t]=a.useState(null),[i,l]=a.useState(null),c=a.useRef(null),m=a.useRef(null),u=a.useRef(new Map),{toast:x}=n(),{lockNavigation:g,unlockNavigation:h}=o(),p=()=>{m.current&&(m.current.abort(),m.current=null),t(null),l(null)};return{generateCommentary:async(e,a,r,n,i,o)=>{m.current&&m.current.abort(),m.current=new AbortController,c.current!==e&&(p(),c.current=e);const f=u.current.get(e);if(f)return t(f),s(!1),f;s(!0),l(null),g(`Gerando análise da IA para questão ${e.substring(0,8)}...`);try{const s=a.replace(/<[^>]+>/g,""),l=r.map((e=>e.replace(/<[^>]+>/g,""))),{data:m,error:g}=await d.functions.invoke("question-commentary",{body:{statement:s,alternatives:l,correctAnswer:n,specialty:o||"Medicina Geral"}});if(g)throw new Error("AI_SERVICE_ERROR");if(!m)throw new Error("AI_NO_DATA");const h=function(e,a){if(!e||"object"!=typeof e)return function(e){return{alternativas:e.map(((e,a)=>({texto:e,comentario:"Análise temporariamente indisponível.",correta:!1}))),comentario_final:"Análise completa temporariamente indisponível. Tente novamente em alguns instantes.",possivel_erro_no_gabarito:!1,justificativa_erro_gabarito:""}}(a);let s=[];for(s=e.alternativas&&Array.isArray(e.alternativas)?e.alternativas.map(((e,s)=>({texto:e?.texto&&"string"==typeof e.texto?e.texto.trim():a[s]||`Alternativa ${s+1}`,comentario:e?.comentario&&"string"==typeof e.comentario?e.comentario.trim():"Análise não disponível.",correta:Boolean(e?.correta)}))):a.map(((e,a)=>({texto:e,comentario:"Análise não disponível.",correta:!1})));s.length<a.length;){const e=s.length;s.push({texto:a[e]||`Alternativa ${e+1}`,comentario:"Análise não disponível.",correta:!1})}let r="";return r=e.comentario_final&&"string"==typeof e.comentario_final?e.comentario_final.trim():"Análise completa não disponível.",{alternativas:s,comentario_final:r,possivel_erro_no_gabarito:Boolean(e.possivel_erro_no_gabarito),justificativa_erro_gabarito:e.justificativa_erro_gabarito&&"string"==typeof e.justificativa_erro_gabarito?e.justificativa_erro_gabarito.trim():""}}(m,r);if(c.current===e&&(t(h),u.current.set(e,h)),i)try{const{data:a,error:s}=await d.from("user_answers").select("id").eq("question_id",e).eq("session_id",i);if(s)throw s;if(a&&a.length>0)for(const e of a){const{error:a}=await d.from("user_answers").update({ai_commentary:h}).eq("id",e.id)}}catch(b){}return x({title:"Análise gerada",description:"A análise da questão foi gerada com sucesso."}),h}catch(b){if("AbortError"===b.name||b.message?.includes("aborted"))return null;let a="Não foi possível gerar a análise no momento.";return"AI_SERVICE_ERROR"===b.message||b.message?.includes("Edge Function")||b.message?.includes("non-2xx")?a="Nosso sistema de IA está temporariamente indisponível.":"AI_NO_DATA"===b.message?a="A IA não conseguiu processar esta questão.":b.message?.includes("timeout")||b.message?.includes("network")?a="Problema de conexão detectado.":(b.message?.includes("rate limit")||b.message?.includes("quota"))&&(a="Muitas solicitações simultâneas. Aguarde um momento."),c.current===e&&(l(a),t(null)),x({title:"Análise temporariamente indisponível",description:a,variant:"destructive"}),null}finally{c.current===e&&s(!1),h()}},commentary:r,isLoading:e,error:i,resetCommentary:p,currentQuestionId:c.current}})(),[g,h]=a.useState(null),p=a.useRef(s.id),[w,_]=a.useState(!1),[A,I]=a.useState(!1),[q,E]=a.useState(0),[R,z]=a.useState(!0),O=a.useRef(""),L=a.useRef(null),M=a.useCallback((e=>{if(!e)return!1;if("object"!=typeof e)return!1;if(!e.alternativas||!Array.isArray(e.alternativas))return!1;if(0===e.alternativas.length)return!1;const a=e.alternativas[0];return"object"==typeof a&&"texto"in a&&"correta"in a}),[]),B=a.useCallback((e=>{const a=JSON.stringify(e),t=`${s.id}-${a}`;O.current!==t&&(L.current&&clearTimeout(L.current),L.current=setTimeout((()=>{O.current=t,r(e)}),50))}),[s.id,r]),D=["Analisando questão médica...","Consultando literatura científica...","Identificando conceitos-chave...","Elaborando explicação detalhada...","Verificando evidências clínicas..."];a.useEffect((()=>{let e;return m&&(e=setInterval((()=>{E((e=>(e+1)%D.length))}),3e3)),()=>clearInterval(e)}),[m,D.length]),a.useEffect((()=>()=>{L.current&&clearTimeout(L.current)}),[]),a.useEffect((()=>{p.current!==s.id&&(_(!0),z(!0),h(null),I(!1),p.current=s.id,setTimeout((()=>{_(!1)}),50))}),[s.id]),a.useEffect((()=>{(async()=>{if(w)return;const e=s.ai_commentary||null,a=M(e),r=M(t);if(a){const a=e;g&&JSON.stringify(g)===JSON.stringify(a)||(h(a),B(a))}else if(r){const e=t;g&&JSON.stringify(g)===JSON.stringify(e)||(h(e),B(e))}else null!==g&&h(null);z(!1)})()}),[s.id,s.ai_commentary,t,w]);const F=a.useCallback((async()=>{if(m)return;const e=s.response_choices||s.alternatives;if(!e||0===e.length)return;const a=await c(s.id,s.question_content||s.statement,e,parseInt((s.correct_choice||s.correct_answer).toString()),i,s.specialty?.name||"Medicina Geral");a&&p.current===s.id&&(g&&JSON.stringify(g)===JSON.stringify(a)||(h(a),B(a)),I(!0),await(async e=>{try{const{error:a}=await d.from("questions").update({ai_commentary:e}).eq("id",s.id)}catch(a){}})(a))}),[m,s.id,s.response_choices,s.alternatives,s.question_content,s.statement,s.correct_choice,s.correct_answer,s.specialty?.name,i,c]),{activeCommentary:V,hasExistingCommentary:$,activeCommentarySource:Q}=a.useMemo((()=>{const e=g||t;return{activeCommentary:e,hasExistingCommentary:M(e),activeCommentarySource:g?"saved":t?"existing":"none"}}),[g,t,M]);return"DISSERTATIVA"===(s.question_format||s.answer_type)?null:w?e.jsx("div",{className:"mt-8 relative",children:e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"mx-auto flex items-center justify-center gap-2 max-w-[240px] h-12 bg-gray-100 rounded-md animate-pulse",children:[e.jsx("div",{className:"w-4 h-4 bg-gray-300 rounded"}),e.jsx("div",{className:"w-32 h-4 bg-gray-300 rounded"})]})})}):e.jsxs("div",{className:"mt-8 relative",children:[R&&e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"mx-auto flex items-center justify-center gap-2 max-w-[240px] h-12 bg-gray-100 rounded-md animate-pulse",children:[e.jsx("div",{className:"w-4 h-4 bg-gray-300 rounded"}),e.jsx("div",{className:"w-32 h-4 bg-gray-300 rounded"})]})}),!R&&!$&&e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsxs(l,{onClick:F,disabled:m,variant:"hackYellow",size:"hack",className:"flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]",children:[e.jsx(f,{className:"h-4 w-4"}),m?"Gerando análise...":"Gerar análise"]}),e.jsx(P,{})]}),m&&e.jsx(b,{className:"border-2 border-black bg-hackathon-lightBg shadow-card-light max-w-md mx-auto",children:e.jsx(v,{className:"p-6 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-4",children:[e.jsx("div",{className:"animate-pulse",children:e.jsx(f,{className:"h-6 w-6 text-hackathon-black dark:text-blue-400"})}),e.jsx("div",{className:"text-lg font-semibold text-hackathon-black dark:text-gray-200",children:"Análise Inteligente"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-hackathon-black dark:text-gray-200 font-medium text-base",children:D[q]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Aguarde aproximadamente 15 segundos para uma análise precisa"})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-hackathon-black to-gray-600 dark:from-blue-500 dark:to-blue-700 h-2 rounded-full animate-pulse relative",children:e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white dark:via-gray-300 to-transparent opacity-30 animate-ping"})})}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-hackathon-black dark:bg-blue-400 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-hackathon-black dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-hackathon-black dark:bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})})})]}),!R&&$&&!A&&e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsxs(l,{onClick:()=>{I(!0)},variant:"hackGreen",size:"hack",className:"flex items-center justify-center gap-2 transform hover:scale-[1.02] transition-all duration-200 max-w-[240px]",children:[e.jsx(T,{className:"h-4 w-4"}),"Ver Análise da IA"]}),e.jsx(P,{})]})}),!R&&$&&A&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsxs(l,{onClick:()=>{I(!1)},variant:"outline",size:"hack",className:"flex items-center justify-center gap-2 border-2 border-black dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 max-w-[240px]",children:[e.jsx(T,{className:"h-4 w-4"}),"Ocultar Análise da IA"]}),e.jsx(P,{})]}),e.jsxs(b,{className:"animate-fade-in border-2 border-black shadow-card",children:[e.jsx(j,{className:"border-b-2 border-black dark:border-gray-600 bg-hackathon-lightBg dark:bg-gray-800 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1"}),e.jsxs(y,{className:"flex items-center justify-center gap-2 text-lg",children:[e.jsx(f,{className:"h-5 w-5 text-hackathon-black dark:text-blue-400"}),"Análise da Questão"]}),e.jsx("div",{className:"flex-1 flex justify-end",children:e.jsx(G,{questionId:s.id,userId:s.owner||""})})]})}),e.jsxs(v,{className:"space-y-4 p-4",children:[V?.alternativas?.map(((a,s)=>e.jsx("div",{className:"rounded-lg p-4 border-2 transition-all duration-200 transform hover:translate-y-[-2px] "+(a.correta?"border-hackathon-green dark:border-green-600 bg-green-50/50 dark:bg-green-900/30 hover:bg-green-50 dark:hover:bg-green-900/50":"border-hackathon-red dark:border-red-600 bg-red-50/50 dark:bg-red-900/30 hover:bg-red-50 dark:hover:bg-red-900/50"),children:e.jsxs("div",{className:"flex items-start gap-3",children:[a.correta?e.jsx(k,{className:"h-5 w-5 text-hackathon-green dark:text-green-400 mt-1 flex-shrink-0"}):e.jsx(N,{className:"h-5 w-5 text-hackathon-red dark:text-red-400 mt-1 flex-shrink-0"}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-bold mb-2 text-gray-900 dark:text-gray-100",children:[a.texto,a.correta&&" (Correta)"]}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",dangerouslySetInnerHTML:{__html:J(a.comentario)}})]})]})},s))),V?.comentario_final&&e.jsxs("div",{className:"rounded-lg p-4 border-2 border-black dark:border-gray-600 bg-hackathon-lightBg dark:bg-gray-800 hover:bg-hackathon-lightBg/80 dark:hover:bg-gray-700 transition-all duration-200 transform hover:translate-y-[-2px]",children:[e.jsxs("h4",{className:"font-bold mb-2 flex items-center gap-2 text-gray-900 dark:text-gray-100",children:[e.jsx(f,{className:"h-4 w-4 text-hackathon-black dark:text-blue-400"}),"Comentário Final"]}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",dangerouslySetInnerHTML:{__html:J(V.comentario_final)}})]}),V?.possivel_erro_no_gabarito&&V?.justificativa_erro_gabarito&&e.jsx(S,{variant:"destructive",className:"border-2 border-orange-500 dark:border-orange-600 bg-orange-50 dark:bg-orange-900/30",children:e.jsxs(C,{className:"flex items-start gap-2",children:[e.jsx("span",{className:"font-semibold text-orange-700 dark:text-orange-300",children:"⚠️ Possível erro no gabarito:"}),e.jsx("span",{className:"text-orange-700 dark:text-orange-300",dangerouslySetInnerHTML:{__html:V.justificativa_erro_gabarito}})]})})]})]})]}),u&&e.jsx(b,{className:"border-2 border-orange-400 dark:border-orange-600 bg-orange-50 dark:bg-orange-900/30 max-w-md mx-auto",children:e.jsx(v,{className:"p-6 text-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900/50 rounded-full",children:e.jsx(f,{className:"h-6 w-6 text-orange-600 dark:text-orange-400"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold text-orange-800",children:"Análise temporariamente indisponível"}),e.jsx("p",{className:"text-sm text-orange-700 leading-relaxed",children:u}),e.jsx("p",{className:"text-xs text-orange-600",children:"Você pode tentar novamente em alguns minutos ou continuar estudando normalmente."})]}),e.jsx(l,{onClick:()=>{x(),F()},variant:"outline",size:"sm",className:"border-orange-300 text-orange-700 hover:bg-orange-100",children:"Tentar novamente"})]})})})]})},Q=({question:s,selectedAnswer:r,onNext:t,onPrevious:n,isLastQuestion:i,isFirstQuestion:c=!1,discursiveAnswer:m,sessionId:u,allQuestionsAnswered:x=!1})=>{const[g,h]=a.useState(null),p=a.useRef(s.id),{isNavigationLocked:f}=o();a.useEffect((()=>{p.current!==s.id&&(p.current=s.id,h(null))}),[s.id]),a.useEffect((()=>{g||s.ai_commentary||s.id!==p.current||(async()=>{if(u&&s.id===p.current)try{const{data:e}=await d.auth.getUser();if(!e.user?.id)return;const{data:a,error:r}=await d.from("user_answers").select("ai_commentary").match({question_id:s.id,session_id:u,user_id:e.user.id}).maybeSingle();if(r)throw r;if(a?.ai_commentary){const e=a.ai_commentary;h(e)}}catch(e){}})()}),[s.id,u,g,s.ai_commentary]);const b=a.useCallback((e=>{s.id===p.current&&(g&&JSON.stringify(g)===JSON.stringify(e)||h(e))}),[s.id,g]);return e.jsxs("div",{className:"space-y-6",children:[e.jsx($,{question:s,onCommentaryGenerated:b,existingCommentary:g,sessionId:u}),(t||n)&&e.jsxs("div",{className:"flex justify-between mt-6",children:[!c&&n&&e.jsxs(l,{onClick:n,variant:"outline",disabled:f,className:"flex items-center gap-2 rounded-full px-6",children:[e.jsx(z,{className:"h-4 w-4"}),"Anterior"]}),e.jsx("div",{className:c?"":"ml-auto",children:t&&e.jsx(e.Fragment,{children:!(i&&!x)&&e.jsxs(l,{onClick:t,disabled:f,className:"flex items-center gap-2 rounded-full px-6 bg-blue-500 hover:bg-blue-600",style:{color:"white"},children:[i?"Finalizar":"Próxima",e.jsx(w,{className:"h-4 w-4"})]})})})]})]})},H=({question:s,className:t})=>{const[n,i]=a.useState(!1);return e.jsxs("div",{className:r("space-y-2",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex flex-wrap items-center gap-2",children:s.question_number&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary",children:["#",s.question_number]}),s.question_type&&e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-100",children:(o=s.question_type,o?{"teorica-1":"Teórica I","teorica-2":"Teórica II","teorico-pratica":"Teórico-Prática"}[o]||o:"")})]})}),e.jsx(l,{variant:"ghost",size:"sm",onClick:()=>{i((e=>!e))},className:"text-xs px-2 h-7",children:n?e.jsxs("span",{className:"flex items-center gap-1",children:["Ocultar detalhes ",e.jsx(_,{size:14})]}):e.jsxs("span",{className:"flex items-center gap-1",children:["Mostrar detalhes ",e.jsx(A,{size:14})]})})]}),n&&e.jsxs("div",{className:"flex flex-wrap gap-2 text-sm text-muted-foreground",children:[s.specialty&&e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300",children:s.specialty.name}),s.theme&&e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-300",children:s.theme.name}),s.focus&&s.focus.name!==s.theme?.name&&e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-300",children:s.focus.name}),s.location&&e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300",children:s.location.name}),s.year&&e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300",children:s.year})]})]});var o},U=({questionId:s,userId:r,initialLikes:t,initialDislikes:i,likedBy:o=[],dislikedBy:c=[]})=>{const{toast:m}=n(),[u,x]=a.useState(t),[g,h]=a.useState(i),[p,f]=a.useState(o),[b,v]=a.useState(c),[j,y]=a.useState(!1);a.useEffect((()=>{(async()=>{const{data:e,error:a}=await d.from("questions").select("likes, dislikes, liked_by, disliked_by").eq("id",s).single();a||e&&(x(e.likes||0),h(e.dislikes||0),f(e.liked_by||[]),v(e.disliked_by||[]))})()}),[s]);const k=p.includes(r),N=b.includes(r),w=async e=>{if(!j)try{y(!0);const{data:a,error:t}=await d.from("questions").select("likes, dislikes, liked_by, disliked_by").eq("id",s).single();if(t)throw t;let n=[...a.liked_by||[]],i=[...a.disliked_by||[]],l=a.likes||0,o=a.dislikes||0;n.includes(r)&&(n=n.filter((e=>e!==r)),l--),i.includes(r)&&(i=i.filter((e=>e!==r)),o--),e&&!n.includes(r)?(n.push(r),l++):e||i.includes(r)||(i.push(r),o++);const{error:c}=await d.from("questions").update({likes:l,dislikes:o,liked_by:n,disliked_by:i}).eq("id",s);if(c)throw c;x(l),h(o),f(n),v(i)}catch(a){m({title:"Erro ao registrar avaliação",description:a.message,variant:"destructive"})}finally{y(!1)}};return e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>w(!0),disabled:j,className:"flex items-center gap-2 transition-colors duration-200\n          "+(k?"bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50":""),children:[e.jsx(O,{className:"h-4 w-4 "+(k?"text-green-600 dark:text-green-400":"")}),e.jsx("span",{children:u})]}),e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>w(!1),disabled:j,className:"flex items-center gap-2 transition-colors duration-200\n          "+(N?"bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50":""),children:[e.jsx(L,{className:"h-4 w-4 "+(N?"text-red-600 dark:text-red-400":"")}),e.jsx("span",{children:g})]})]})},W=({images:s,className:t})=>{const[n,i]=a.useState(null);return s&&0!==s.length?e.jsx("div",{className:r("mt-4 space-y-4",t),children:e.jsx("div",{className:"flex flex-wrap gap-4 justify-center",children:s.map(((a,r)=>e.jsxs(c,{children:[e.jsx(m,{asChild:!0,children:e.jsx("div",{className:"relative cursor-pointer overflow-hidden rounded-md border border-gray-200 hover:border-primary transition-all",onClick:()=>i(r),children:e.jsx(M,{src:a,alt:`Imagem ${r+1} da questão`,className:"h-32 w-auto object-cover hover:scale-105 transition-transform",priority:0===r})})}),e.jsx(u,{className:"max-w-4xl",children:e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(B,{src:a,alt:`Imagem ${r+1} da questão (ampliada)`,className:"max-h-[calc(100vh-200px)] w-auto object-contain"}),e.jsx("div",{className:"mt-4 text-sm text-gray-500",children:`Imagem ${r+1} de ${s.length}`})]})})]},r)))})}):null},Y=({content:s})=>{const[r,t]=a.useState("");return a.useEffect((()=>{if(!s)return void t("");s.includes("\n"),s.includes("\r\n");const e=K(s);t(e)}),[s]),e.jsx("div",{dangerouslySetInnerHTML:{__html:r}})},K=e=>{if(!e)return"";let a=e.replace(/\r\n/g,"<br>").replace(/\n/g,"<br>");return a=a.replace(/  /g," &nbsp;"),a},Z=({question:s,selectedAnswer:r,hasAnswered:t,onSelectAnswer:i,onSubmitAnswer:o,onNext:c,userId:m,sessionId:u,isAnswered:x=!1,timeSpent:g})=>{n();const[h,p]=a.useState(s),[f,v]=a.useState(""),[j,y]=a.useState(!1),[k,N]=a.useState(!1),[w,_]=a.useState(!1);a.useEffect((()=>{(t||x)&&y(!0)}),[t,x]),a.useEffect((()=>{p(s),y(!1),v(""),N(!1)}),[s.id]);const A=async()=>{if(w)return;const e=s.question_format||s.answer_type;if("DISCURSIVE"!==e||f.trim()){_(!0);try{let a=!1;if("DISCURSIVE"===e){if(!s.specialty?.id)throw new Error("Especialidade é obrigatória para salvar a resposta");const{error:e}=await d.from("user_answers").insert({user_id:m,question_id:s.id,session_id:u,text_answer:f,is_correct:!0,time_spent:g,specialty_id:s.specialty.id,theme_id:s.theme?.id,focus_id:s.focus?.id,year:s.exam_year||s.year||(new Date).getFullYear()});if(e)throw e;a=!0,y(!0),N(!0)}else o&&(await o(g),a=!0,y(!0),N(!0))}catch(a){}finally{_(!1)}}},S=j||t||x;return e.jsx(b,{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-0",children:e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx(H,{question:s})}),e.jsx("div",{className:"prose max-w-none mb-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg","data-highlighter":"statement",children:e.jsx(Y,{content:s.question_content||s.statement})}),(s.media_attachments||s.images)&&(s.media_attachments||s.images)?.length>0&&e.jsx(W,{images:s.media_attachments||s.images}),"DISSERTATIVA"===(s.question_format||s.answer_type)?e.jsxs(e.Fragment,{children:[e.jsx(V,{value:f,onChange:v,onSubmit:A,hasAnswered:S,readOnly:S}),S&&e.jsx(Q,{question:{...s,discursiveAnswer:f},selectedAnswer:null,onNext:c,isLastQuestion:!1,sessionId:u,discursiveAnswer:f})]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{alternatives:s.response_choices&&s.response_choices.length>0?s.response_choices:s.alternatives&&s.alternatives.length>0?s.alternatives:[],selectedAnswer:r,setSelectedAnswer:S?void 0:i,hasAnswered:S,correct_answer:parseInt((s.correct_choice||s.correct_answer).toString()),statistics:s.statistics,alternativeComments:s.alternativeComments,questionId:s.id}),!S&&e.jsx("div",{className:"flex justify-end gap-4",children:e.jsx(l,{onClick:A,disabled:!r||w,className:"w-full md:w-auto",children:w?"Enviando...":"Confirmar Resposta"})}),S&&e.jsx(Q,{question:s,selectedAnswer:r,onNext:c,isLastQuestion:!1,sessionId:u})]}),e.jsx("div",{className:"flex flex-col md:flex-row items-center justify-center md:justify-between gap-4",children:e.jsx(U,{questionId:s.id,userId:m,initialLikes:s.likes||0,initialDislikes:s.dislikes||0,likedBy:s.liked_by||[],dislikedBy:s.disliked_by||[]})})]})})};export{D as F,Z as Q};
