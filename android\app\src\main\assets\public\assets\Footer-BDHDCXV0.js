import{j as e}from"./radix-core-6kBL75b5.js";import{B as r,av as a,aG as s,ab as t,D as o,e as i,f as d,g as l,F as m}from"./index-DIVKaOkK.js";import{L as n}from"./router-BAzpOxbo.js";import{F as c}from"./FeedbackTrigger-Bbh1ZkR8.js";import{r as x}from"./critical-DVX9Inzy.js";import{I as p}from"./instagram-C9X9J-Hm.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./rocket-coAqOqu0.js";import"./target-JnessAn2.js";import"./zap-cv85ZGQL.js";import"./book-open-HpWivqt7.js";import"./star-D6Dh7eqH.js";import"./circle-help-DamQHiY3.js";const j=()=>{const[j,h]=x.useState(!1);return e.jsxs("footer",{className:"hidden sm:block bg-blue-50/90 dark:bg-blue-900/30 backdrop-blur-md shadow-md mt-16 border-t border-blue-100 dark:border-blue-800/50",children:[e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsxs("div",{className:"text-center space-y-5",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center items-center",children:[e.jsxs(r,{variant:"outline",className:"border-blue-200 hover:bg-blue-50 dark:border-blue-700 dark:hover:bg-blue-900/30",onClick:()=>h(!0),children:[e.jsx(a,{className:"h-4 w-4 mr-2"}),e.jsx("span",{children:"Feedback Técnico"})]}),e.jsx(c,{variant:"button",className:"shadow-md hover:shadow-lg transition-all duration-300"})]}),e.jsx("p",{className:"max-w-2xl mx-auto text-sm text-gray-600 dark:text-gray-300",children:"Plataforma para profissionais de saúde com foco em pediatria, oferecendo informações atualizadas e práticas."}),e.jsxs("div",{className:"flex justify-center gap-5",children:[e.jsx("a",{href:"mailto:<EMAIL>",className:"text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors",children:e.jsx(s,{size:20})}),e.jsx("a",{href:"https://www.instagram.com/pedbookmed/",target:"_blank",rel:"noopener noreferrer",className:"text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors",children:e.jsx(p,{size:20})}),e.jsx(n,{to:"/terms",className:"text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors",children:e.jsx(t,{size:20})}),e.jsx(n,{to:"/politica-privacidade",className:"text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors",children:e.jsx(t,{size:20})})]}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 space-y-1",children:[e.jsx("p",{children:"© 2025 PedBook. Todos os direitos reservados."}),e.jsxs("p",{className:"text-gray-500 dark:text-gray-400",children:["Desenvolvimento por"," ",e.jsx("a",{href:"https://medunity.com.br/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary/80 dark:text-blue-400 dark:hover:text-blue-300 transition-colors",children:"Med Unity"})]})]})]})}),e.jsx(o,{open:j,onOpenChange:e=>{h(e),e||(document.body.style.pointerEvents="auto")},children:e.jsxs(i,{className:"max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800",children:[e.jsx(d,{children:e.jsx(l,{children:"Feedback"})}),e.jsx(m,{})]})})]})};export{j as default};
