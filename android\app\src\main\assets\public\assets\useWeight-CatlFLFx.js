import{a as t,u as e}from"./query-vendor-B-7l6Nb3.js";import{r as a}from"./critical-DVX9Inzy.js";const r="patient-weight",s=20,o=()=>{const o=t(),[i,l]=a.useState(null),{data:n=s}=e({queryKey:[r],queryFn:()=>{const t=localStorage.getItem(r);return t?parseFloat(t):s},staleTime:1/0}),u=t=>{l(null),localStorage.setItem(r,t.toString()),o.setQueryData([r],t)};return{weight:n,setWeight:u,displayWeight:i??n,setTempWeight:t=>{l(t)},commitWeight:u}};export{o as u};
