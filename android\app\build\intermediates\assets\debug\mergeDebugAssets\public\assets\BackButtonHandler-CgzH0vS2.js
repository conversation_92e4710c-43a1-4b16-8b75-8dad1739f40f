const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-8GM9Ipfp.js","assets/index-DwBJcqzE.js","assets/radix-core-6kBL75b5.js","assets/critical-DVX9Inzy.js","assets/query-vendor-B-7l6Nb3.js","assets/supabase-vendor-qi_Ptfv-.js","assets/router-BAzpOxbo.js","assets/form-vendor-rYZw_ur7.js","assets/radix-forms-DX-owj97.js","assets/radix-interactive-DJo-0Sg_.js","assets/radix-toast-1_gbKn9f.js","assets/radix-feedback-dpGNY8wJ.js","assets/radix-popover-DQqTw7_-.js","assets/radix-layout-CC8mXA4O.js"])))=>i.map(i=>d[i]);
import{r as e}from"./critical-DVX9Inzy.js";import{_ as t}from"./supabase-vendor-qi_Ptfv-.js";import{J as n,K as r}from"./index-DwBJcqzE.js";import{a,u as o}from"./router-BAzpOxbo.js";import"./radix-core-6kBL75b5.js";import"./query-vendor-B-7l6Nb3.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const i=n("App",{web:()=>t((()=>import("./web-8GM9Ipfp.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13])).then((e=>new e.AppWeb))}),s=()=>((()=>{const t=a(),n=o();e.useEffect((()=>{if(!r())return;const e=["/","/medicamentos","/medicamentos/painel","/puericultura","/dr-will","/calculadoras","/flowcharts","/condutas","/bulas","/interacoes","/cid","/plataformadeestudos","/feedback","/perfil","/busca","/configuracoes","/newsletters","/pedidrop"],a=i.addListener("backButton",(()=>{if("/"!==n.pathname)if(e.includes(n.pathname)&&"/"!==n.pathname)t("/");else{for(const r of e)if("/"!==r&&n.pathname.startsWith(r+"/"))return void t(r);t(-1)}else i.exitApp()}));return()=>{a.remove()}}),[n.pathname,t])})(),(()=>{const t=a(),n=o();e.useEffect((()=>{if(!(()=>{const e=navigator.userAgent.toLowerCase(),t=/android/i.test(e),n=/wv/.test(e)||/webview/.test(e),r="Android"in window||"AndroidInterface"in window,a=/chrome/.test(e)&&!/edg/.test(e),o=/samsungbrowser/.test(e);return t&&(n||r||!a&&!o)})())return;const e=()=>{if("/"===n.pathname)return!1;const e=["/medicamentos","/medicamentos/painel","/puericultura","/dr-will","/calculadoras","/flowcharts","/condutas","/bulas","/interacoes","/cid","/plataformadeestudos","/feedback","/perfil","/busca","/configuracoes","/newsletters","/pedidrop"];if(e.includes(n.pathname))return t("/"),!0;for(const r of e)if(n.pathname.startsWith(r+"/"))return t(r),!0;return t(-1),!0},r=[(()=>{const t=()=>{for(let e=0;e<3;e++)window.history.pushState({webview:!0,index:e},"",n.pathname+(e>0?`#webview-${e}`:""))};t();const r=n=>{n.preventDefault(),n.state&&n.state.webview&&(e(),setTimeout(t,100))};return window.addEventListener("popstate",r),()=>window.removeEventListener("popstate",r)})(),(()=>{const t=t=>{if("Escape"===t.key||"Backspace"===t.key||27===t.keyCode||8===t.keyCode){const n=t.target;"INPUT"!==n.tagName&&"TEXTAREA"!==n.tagName&&(t.preventDefault(),t.stopPropagation(),e())}};return document.addEventListener("keydown",t,!0),()=>document.removeEventListener("keydown",t,!0)})(),(window.webViewBackHandler={onBackPressed:()=>e(),canGoBack:()=>"/"!==n.pathname,getCurrentPath:()=>n.pathname},window.handleBackButton=()=>e(),()=>{delete window.webViewBackHandler,delete window.handleBackButton}),(()=>{const t=t=>{e()},n=e=>{};return document.addEventListener("webview-back-button",t),document.addEventListener("webview-ready",n),()=>{document.removeEventListener("webview-back-button",t),document.removeEventListener("webview-ready",n)}})(),(()=>{let e=!0;const t=()=>{document.hidden&&e?e=!1:document.hidden||e||(e=!0)};return document.addEventListener("visibilitychange",t),()=>document.removeEventListener("visibilitychange",t)})()];return()=>{r.forEach((e=>e&&e()))}}),[n.pathname,t])})(),null);export{s as default};
