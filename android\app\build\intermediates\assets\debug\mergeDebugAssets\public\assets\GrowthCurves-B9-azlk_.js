import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as r,u as s}from"./query-vendor-B-7l6Nb3.js";import{L as t}from"./router-BAzpOxbo.js";import{m as l,B as i,ac as n,D as o,e as c,f as d,g as m,ad as x,ae as g,af as h,ag as u,ai as p,X as j,j as y,aV as b,C as v,aX as f,aa as w,a7 as N,s as k}from"./index-DV3Span9.js";import{S as C}from"./slider-DZGSefFp.js";import{E as P}from"./pdf-vendor-C6iMwFa1.js";import{D as M}from"./download-BbEua-No.js";import{C as G}from"./chevron-left-2OWC_ZeG.js";import{C as S,a as T}from"./childcareSEOData-Ph524jO1.js";import A from"./Footer-DaWfQ4Sj.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-OUR0fGD_.js";import"./rocket-DWbYpez3.js";import"./target-qP-p0xEy.js";import"./zap-xHziMQfW.js";import"./book-open-Ca6sFj94.js";import"./star-CdRVV6QC.js";import"./circle-help-psGgvKcv.js";import"./instagram-BdLPZF9i.js";function _({curve:a,onViewCurve:r}){return e.jsx(l.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},whileHover:{y:-4},className:"group bg-white dark:bg-slate-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-md overflow-hidden hover:shadow-lg transition-all duration-300",children:e.jsxs("div",{className:"p-6",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 dark:text-gray-100",children:a.title}),a.description&&e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm mb-4",children:a.description}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.jsx("span",{className:"px-3 py-1 rounded-full text-xs bg-accent-blue dark:bg-blue-800 dark:text-blue-100 text-blue-900",children:"male"===a.gender?"Menino":"Menina"}),e.jsx("span",{className:"px-3 py-1 rounded-full text-xs bg-accent-yellow dark:bg-yellow-800 dark:text-yellow-100 text-yellow-900",children:"term"===a.gestational_age?"A Termo":"Pré-termo"}),e.jsx("span",{className:"px-3 py-1 rounded-full text-xs bg-accent-green dark:bg-green-800 dark:text-green-100 text-green-900",children:"healthy"===a.growth_type?"Saudável":"Transtorno"})]}),e.jsxs(i,{onClick:()=>r(a),className:"w-full relative group dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-white",children:[e.jsx("span",{children:"Visualizar Curva"}),e.jsx("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center",children:e.jsx(n,{className:"w-6 h-6 text-white"})})]})]})})}function q({curve:r,isOpen:s,onOpenChange:t}){const[l,n]=a.useState(1),[x,g]=a.useState(!1),[h,u]=a.useState({x:0,y:0}),[p,j]=a.useState({x:0,y:0}),y=a.useRef(null),b=()=>{g(!1)};return e.jsx(o,{open:s,onOpenChange:t,children:e.jsxs(c,{className:"max-w-4xl max-h-[90vh] overflow-hidden",children:[e.jsx(d,{children:e.jsx(m,{children:r?.title})}),r?.image_url&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{ref:y,className:"relative overflow-hidden cursor-grab h-[calc(90vh-200px)] no-swipe",onMouseDown:e=>{g(!0),j({x:e.clientX-h.x,y:e.clientY-h.y})},onMouseMove:e=>{if(x&&y.current){const a=e.clientX-p.x,r=e.clientY-p.y,s=y.current,t=s.getBoundingClientRect(),i=s.querySelector("img");if(i){const e=i.getBoundingClientRect(),s=(e.width*l-t.width)/2,n=(e.height*l-t.height)/2;u({x:Math.max(Math.min(a,s),-s),y:Math.max(Math.min(r,n),-n)})}}},onMouseUp:b,onMouseLeave:b,"data-zoom":"true","data-draggable":"true",children:e.jsx("img",{src:r.image_url,alt:r.title,className:"w-full h-full object-contain transition-transform duration-300",style:{transform:`scale(${l}) translate(${h.x}px, ${h.y}px)`,transformOrigin:"center center",cursor:x?"grabbing":"grab"},draggable:!1})}),e.jsxs("div",{className:"flex items-center gap-4 px-2",children:[e.jsx("span",{className:"text-sm text-gray-500 whitespace-nowrap",children:"Zoom:"}),e.jsx(C,{value:[l],onValueChange:e=>{n(e[0]),1===e[0]&&u({x:0,y:0})},min:1,max:2,step:.1,className:"flex-1"}),e.jsxs(i,{variant:"outline",onClick:async()=>{if(r?.image_url)try{const e=await fetch(r.image_url),a=await e.blob(),s=URL.createObjectURL(a),t=new P({orientation:"landscape",unit:"px",format:[800,600]});t.addImage(s,"JPEG",0,0,800,600),t.save(`${r.title}.pdf`),URL.revokeObjectURL(s)}catch(e){}},className:"whitespace-nowrap flex items-center gap-2",children:[e.jsx(M,{className:"h-4 w-4"}),"Baixar PDF"]})]})]})]})})}function F({selectedGender:a,selectedGestationalAge:r,selectedGrowthType:s,onGenderChange:t,onGestationalAgeChange:l,onGrowthTypeChange:n,onClearFilters:o}){return e.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 mb-8 max-w-3xl mx-auto border border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-center gap-4",children:[e.jsxs(x,{value:a||"",onValueChange:t,children:[e.jsx(g,{className:"w-auto min-w-[140px] dark:bg-slate-700 dark:border-gray-600 dark:text-gray-100",children:e.jsx(h,{placeholder:"Filtrar por gênero"})}),e.jsxs(u,{className:"bg-white dark:bg-slate-800 dark:border-gray-700",children:[e.jsx(p,{value:"male",className:"dark:text-gray-100",children:"Menino"}),e.jsx(p,{value:"female",className:"dark:text-gray-100",children:"Menina"})]})]}),e.jsxs(x,{value:r||"",onValueChange:l,children:[e.jsx(g,{className:"w-auto min-w-[140px] dark:bg-slate-700 dark:border-gray-600 dark:text-gray-100",children:e.jsx(h,{placeholder:"Idade gestacional"})}),e.jsxs(u,{className:"bg-white dark:bg-slate-800 dark:border-gray-700",children:[e.jsx(p,{value:"term",className:"dark:text-gray-100",children:"A Termo"}),e.jsx(p,{value:"preterm",className:"dark:text-gray-100",children:"Pré-termo"})]})]}),e.jsxs(x,{value:s||"",onValueChange:n,children:[e.jsx(g,{className:"w-auto min-w-[140px] dark:bg-slate-700 dark:border-gray-600 dark:text-gray-100",children:e.jsx(h,{placeholder:"Tipo de crescimento"})}),e.jsxs(u,{className:"bg-white dark:bg-slate-800 dark:border-gray-700",children:[e.jsx(p,{value:"healthy",className:"dark:text-gray-100",children:"Saudável"}),e.jsx(p,{value:"disorder",className:"dark:text-gray-100",children:"Transtorno"})]})]}),(a||r||s)&&e.jsxs(i,{variant:"ghost",onClick:o,className:"gap-2 dark:text-gray-300 dark:hover:bg-slate-700",children:[e.jsx(j,{className:"h-4 w-4"}),"Limpar filtros"]})]})})}const z=({className:a,...r})=>e.jsx("nav",{role:"navigation","aria-label":"pagination",className:y("mx-auto flex w-full justify-center",a),...r});z.displayName="Pagination";const L=a.forwardRef((({className:a,...r},s)=>e.jsx("ul",{ref:s,className:y("flex flex-row items-center gap-1",a),...r})));L.displayName="PaginationContent";const O=a.forwardRef((({className:a,...r},s)=>e.jsx("li",{ref:s,className:y("",a),...r})));O.displayName="PaginationItem";const R=({className:a,isActive:r,size:s="icon",...t})=>e.jsx("a",{"aria-current":r?"page":void 0,className:y(b({variant:r?"outline":"ghost",size:s}),a),...t});R.displayName="PaginationLink";const V=({className:a,...r})=>e.jsxs(R,{"aria-label":"Go to previous page",size:"default",className:y("gap-1 pl-2.5",a),...r,children:[e.jsx(G,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]});V.displayName="PaginationPrevious";const B=({className:a,...r})=>e.jsxs(R,{"aria-label":"Go to next page",size:"default",className:y("gap-1 pr-2.5",a),...r,children:[e.jsx("span",{children:"Next"}),e.jsx(v,{className:"h-4 w-4"})]});function D({currentPage:a,totalPages:r,onPageChange:s}){return r<=1?null:e.jsx("div",{className:"mt-8",children:e.jsx(z,{children:e.jsxs(L,{children:[e.jsx(O,{children:e.jsx(V,{onClick:()=>s(Math.max(a-1,1)),className:y(1===a&&"pointer-events-none opacity-50","dark:text-gray-300 dark:hover:bg-slate-700 dark:border-gray-700")})}),Array.from({length:r},((e,a)=>a+1)).map((r=>e.jsx(O,{children:e.jsx(R,{onClick:()=>s(r),isActive:a===r,className:"dark:text-gray-300 dark:border-gray-700 dark:data-[active]:bg-primary dark:data-[active]:text-white",children:r})},r))),e.jsx(O,{children:e.jsx(B,{onClick:()=>s(Math.min(a+1,r)),className:y(a===r&&"pointer-events-none opacity-50","dark:text-gray-300 dark:hover:bg-slate-700 dark:border-gray-700")})})]})})})}B.displayName="PaginationNext";const U=()=>{const l=T["curva-de-crescimento"],[i,n]=a.useState(null),[o,c]=a.useState(!1),[d,m]=a.useState(1),[x,g]=a.useState(null),[h,u]=a.useState(null),[p,j]=a.useState(null),y=r(),{theme:b}=f();a.useEffect((()=>{(async()=>{await y.prefetchQuery({queryKey:["growth-curves",null,null,null],queryFn:v,staleTime:864e5,gcTime:1728e5})})()}),[y]);const v=async()=>{let e=k.from("pedbook_growth_curves").select("*").order("created_at");x&&(e=e.eq("gender",x)),h&&(e=e.eq("gestational_age",h)),p&&(e=e.eq("growth_type",p));const{data:a,error:r}=await e;if(r)throw r;return a},{data:C=[],isLoading:P}=s({queryKey:["growth-curves",x,h,p],queryFn:v,staleTime:864e5,gcTime:1728e5}),M=C||[],G=Math.ceil(M.length/8),z=M.slice(8*(d-1),8*d);return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-white dark:from-slate-900 dark:via-blue-900/5 dark:to-slate-900",children:[e.jsx(S,{...l}),e.jsx(w,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-8",children:[e.jsxs(t,{to:"/puericultura",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(N,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Puericultura"})]}),e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4 gradient-text dark:text-blue-300",children:"Curvas de Crescimento"}),e.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto dark:text-gray-300",children:"Consulte as curvas de crescimento padronizadas para acompanhamento do desenvolvimento infantil"})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsx(F,{selectedGender:x,selectedGestationalAge:h,selectedGrowthType:p,onGenderChange:g,onGestationalAgeChange:u,onGrowthTypeChange:j,onClearFilters:()=>{g(null),u(null),j(null),m(1)}}),P?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[...Array(4)].map(((a,r)=>e.jsx("div",{className:"bg-white dark:bg-slate-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-md h-64 animate-pulse"},r)))}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 growth-curves-container no-swipe",children:z.map((a=>e.jsx(_,{curve:a,onViewCurve:e=>{n(e),c(!0)}},a.id)))}),e.jsx(D,{currentPage:d,totalPages:G,onPageChange:m})]}),e.jsx(q,{curve:i,isOpen:o,onOpenChange:c})]})]}),e.jsx(A,{})]})};export{U as default};
