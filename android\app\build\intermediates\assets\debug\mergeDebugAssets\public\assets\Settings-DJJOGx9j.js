import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{c as s,R as r,W as t,Z as i,$ as o,U as l,an as n,a5 as d,aG as c,E as m,ad as u,ae as x,af as h,ag as p,ai as f,b2 as j,B as g,al as v,am as w,u as N,_ as b,s as y,aq as _,ar as k,as as C,at as E,aM as S,aa as F,a8 as z,D as P,e as U,f as A,g as q,F as B}from"./index-D9amGMlQ.js";import{d as R}from"./supabase-vendor-qi_Ptfv-.js";import{a as V,L as T}from"./router-BAzpOxbo.js";import I from"./Footer-BkFd5qSK.js";import{S as M}from"./switch-UJd64E7P.js";import{U as O}from"./user-GwV7rf-d.js";import{B as $}from"./building-2-cmkJXfJO.js";import{A as D,h as G,a as L,b as W,c as H,d as Z,e as J,f as K,g as Q}from"./alert-dialog-DKHxUe7X.js";import{T as X}from"./trash-2-CPhvjlA_.js";import{C as Y}from"./chevron-left-Cmyma3W5.js";import"./query-vendor-B-7l6Nb3.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./target-Cj27UDYs.js";import"./zap-DULtmWB8.js";import"./book-open-CzUd5kBy.js";import"./star-BlLX_9hT.js";import"./circle-help-DFUIKtE9.js";import"./instagram-CuaDlQAQ.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=s("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),ae=({profile:a,setProfile:s,handleProfileUpdate:v})=>e.jsx("form",{onSubmit:v,className:"space-y-6",children:e.jsxs(r,{className:"border shadow-sm",children:[e.jsx(t,{className:"px-4 sm:px-6 py-4 sm:py-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 rounded-lg bg-primary/10 text-primary",children:e.jsx(O,{className:"h-5 w-5"})}),e.jsxs("div",{children:[e.jsx(i,{className:"text-lg sm:text-xl",children:"Informações Pessoais"}),e.jsx(o,{className:"text-sm sm:text-base",children:"Mantenha seus dados atualizados"})]})]})}),e.jsxs(l,{className:"space-y-6 px-4 sm:px-6 pb-6",children:[e.jsx("div",{className:"grid grid-cols-1 gap-4 sm:gap-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"full_name",className:"flex items-center gap-2 text-sm font-medium",children:[e.jsx(O,{className:"h-4 w-4"}),"Nome completo"]}),e.jsx(d,{id:"full_name",value:a?.full_name||"",onChange:e=>s({...a,full_name:e.target.value}),placeholder:"Seu nome completo",className:"h-11"})]})}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"email",className:"flex items-center gap-2 text-sm font-medium",children:[e.jsx(c,{className:"h-4 w-4"}),"Email profissional"]}),e.jsx(d,{id:"email",type:"email",value:a?.professional_email||"",onChange:e=>s({...a,professional_email:e.target.value}),placeholder:"<EMAIL>",className:"h-11"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"phone",className:"flex items-center gap-2 text-sm font-medium",children:[e.jsx(m,{className:"h-4 w-4"}),"Telefone"]}),e.jsx(d,{id:"phone",type:"tel",value:a?.phone||"",onChange:e=>s({...a,phone:e.target.value}),placeholder:"(00) 00000-0000",className:"h-11"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"formation_area",className:"flex items-center gap-2 text-sm font-medium",children:[e.jsx($,{className:"h-4 w-4"}),"Área de formação"]}),e.jsxs(u,{value:a?.formation_area||"",onValueChange:e=>s({...a,formation_area:e}),children:[e.jsx(x,{className:"h-11",children:e.jsx(h,{placeholder:"Selecione sua área"})}),e.jsxs(p,{children:[e.jsx(f,{value:"medicina",children:"Medicina"}),e.jsx(f,{value:"enfermagem",children:"Enfermagem"}),e.jsx(f,{value:"farmacia",children:"Farmácia"}),e.jsx(f,{value:"fisioterapia",children:"Fisioterapia"}),e.jsx(f,{value:"nutricao",children:"Nutrição"}),e.jsx(f,{value:"outro",children:"Outro"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"graduation_year",className:"flex items-center gap-2 text-sm font-medium",children:[e.jsx(j,{className:"h-4 w-4"}),"Ano de formação"]}),e.jsx(d,{id:"graduation_year",type:"text",value:a?.graduation_year||"",onChange:e=>s({...a,graduation_year:e.target.value}),placeholder:"Ex: 2024",className:"h-11"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(n,{htmlFor:"registration_number",className:"flex items-center gap-2 text-sm font-medium",children:[e.jsx($,{className:"h-4 w-4"}),"Número de registro profissional"]}),e.jsx(d,{id:"registration_number",value:a?.registration_number||"",onChange:e=>s({...a,registration_number:e.target.value}),placeholder:"CRM/COREN/etc",className:"h-11"})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-muted/30",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(j,{className:"h-4 w-4 text-muted-foreground"}),e.jsx(n,{htmlFor:"is_student",className:"text-sm font-medium cursor-pointer",children:"Sou estudante"})]}),e.jsx(M,{id:"is_student",checked:a?.is_student||!1,onCheckedChange:e=>s({...a,is_student:e})})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-muted/30",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-4 w-4 text-muted-foreground"}),e.jsx(n,{htmlFor:"is_professional",className:"text-sm font-medium cursor-pointer",children:"Sou profissional"})]}),e.jsx(M,{id:"is_professional",checked:a?.is_professional||!1,onCheckedChange:e=>s({...a,is_professional:e})})]})]}),e.jsx("div",{className:"pt-6 border-t",children:e.jsxs(g,{type:"submit",className:"w-full h-11",children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Salvar alterações"]})})]})]})}),se=()=>{const s=v(),c=w(),m=a.useRef(null),{user:u}=N(),[x,h]=a.useState(!1);return e.jsxs(r,{className:"bg-white dark:bg-slate-800 border dark:border-slate-700",children:[e.jsxs(t,{children:[e.jsxs(i,{className:"flex items-center gap-2 dark:text-gray-100",children:[e.jsx(b,{className:"h-5 w-5"}),"Alterar Senha"]}),e.jsx(o,{className:"dark:text-gray-400",children:"Atualize sua senha de acesso aqui."})]}),e.jsx(l,{children:e.jsxs("form",{ref:m,onSubmit:async e=>{e.preventDefault(),h(!0);const a=new FormData(e.currentTarget);a.get("current_password");const r=a.get("new_password");if(r!==a.get("confirm_password"))return c("Erro","As senhas não coincidem"),void h(!1);try{const{error:e}=await y.auth.updateUser({password:r});if(e)throw e;s("Sucesso","Senha atualizada com sucesso"),m.current?.reset()}catch(t){c("Erro ao atualizar senha",t.message||"Ocorreu um erro ao atualizar a senha")}finally{h(!1)}},className:"space-y-4",children:[e.jsx(d,{type:"text",id:"username",name:"username",autoComplete:"username",defaultValue:u?.email||"",className:"hidden","aria-hidden":"true"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"current_password",className:"dark:text-gray-300",children:"Senha atual"}),e.jsx(d,{id:"current_password",name:"current_password",type:"password",autoComplete:"current-password",className:"bg-white dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200",required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"new_password",className:"dark:text-gray-300",children:"Nova senha"}),e.jsx(d,{id:"new_password",name:"new_password",type:"password",autoComplete:"new-password",className:"bg-white dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200",required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"confirm_password",className:"dark:text-gray-300",children:"Confirme a nova senha"}),e.jsx(d,{id:"confirm_password",name:"confirm_password",type:"password",autoComplete:"new-password",className:"bg-white dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200",required:!0})]}),e.jsx(g,{type:"submit",variant:"outline",className:"w-full sm:w-auto dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-gray-200 dark:border-slate-600",disabled:x,children:x?"Atualizando...":"Atualizar senha"})]})})]})},re=({profile:a,setProfile:s,handleProfileUpdate:r})=>e.jsxs(_,{defaultValue:"personal",className:"space-y-6",children:[e.jsxs(k,{className:"grid w-full grid-cols-2 gap-1 bg-muted p-1 rounded-lg h-auto",children:[e.jsxs(C,{value:"personal",className:"flex items-center justify-center gap-2 h-10 sm:h-11 text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=inactive]:text-muted-foreground rounded-md transition-all",children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Informações"}),e.jsx("span",{className:"sm:hidden",children:"Info"})]}),e.jsxs(C,{value:"password",className:"flex items-center justify-center gap-2 h-10 sm:h-11 text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=inactive]:text-muted-foreground rounded-md transition-all",children:[e.jsx(b,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Senha"}),e.jsx("span",{className:"sm:hidden",children:"Senha"})]})]}),e.jsx(E,{value:"personal",children:e.jsx(ae,{profile:a,setProfile:s,handleProfileUpdate:r})}),e.jsx(E,{value:"password",children:e.jsx(se,{})})]}),te=({profile:s,session:r,uploading:t,handleAvatarUpload:i})=>{const o=a.useRef(null),l=()=>{o.current?.click()};return e.jsxs("div",{className:"relative w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6",children:[e.jsx("div",{className:"w-20 h-20 sm:w-24 sm:h-24 rounded-full overflow-hidden cursor-pointer transition-transform hover:scale-105",onClick:l,children:e.jsx("img",{src:s?.avatar_url||`https://www.gravatar.com/avatar/${r?.user?.email}?d=mp`,alt:s?.full_name,className:"w-full h-full object-cover",onError:e=>{e.target.src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png"}})}),e.jsx("button",{onClick:l,className:"absolute bottom-0 right-0 p-1.5 sm:p-2 rounded-full bg-primary text-white shadow-lg hover:bg-primary/90 transition-colors",disabled:t,title:"Alterar foto",children:e.jsx(ee,{className:"h-3 w-3 sm:h-4 sm:w-4"})}),e.jsx(d,{ref:o,type:"file",accept:".jpg,.jpeg,.png,.gif,.webp",onChange:i,disabled:t,className:"hidden"})]})},ie=()=>{const s=v(),n=w(),d=V(),[c,m]=a.useState(!1);return e.jsxs(r,{className:"border-destructive/50",children:[e.jsxs(t,{children:[e.jsxs(i,{className:"flex items-center gap-2 text-destructive",children:[e.jsx(X,{className:"h-5 w-5"}),"Excluir Conta"]}),e.jsx(o,{children:"Exclua permanentemente sua conta e todos os dados associados."})]}),e.jsx(l,{children:e.jsxs(D,{children:[e.jsx(G,{asChild:!0,children:e.jsx(g,{variant:"destructive",className:"w-full sm:w-auto",children:"Excluir minha conta"})}),e.jsxs(L,{children:[e.jsxs(W,{children:[e.jsx(H,{children:"Você tem certeza?"}),e.jsx(Z,{children:"Esta ação não pode ser desfeita. Isso excluirá permanentemente sua conta e removerá seus dados de nossos servidores."})]}),e.jsxs(J,{children:[e.jsx(K,{children:"Cancelar"}),e.jsx(Q,{onClick:async()=>{try{m(!0);const{data:{user:e}}=await y.auth.getUser();if(!e?.id)throw new Error("Usuário não encontrado");const{data:a,error:r}=await y.rpc("delete_user_account",{user_id:e.id});if(r)throw r;s("Conta excluída","Sua conta foi excluída com sucesso.","Entendi",(()=>{y.auth.signOut().then((()=>{d("/")}))}))}catch(e){n("Erro ao excluir conta",e.message)}finally{m(!1)}},className:"bg-destructive hover:bg-destructive/90",disabled:c,children:c?"Excluindo...":"Sim, excluir minha conta"})]})]})]})})]})},oe=()=>{const s=R.useSession(),i=v(),o=w(),[n,d]=a.useState(null),[c,m]=a.useState(!0),[u,x]=a.useState(!1),[h,p]=a.useState(!1);a.useEffect((()=>{s?.user?f():(d(null),m(!1))}),[s]);const f=async()=>{try{m(!0);const{data:e,error:a}=await y.from("secure_profiles").select("*").eq("id",s?.user?.id).single();if(a){if("PGRST116"===a.code)return void(await j());throw a}if(e.security_message)return void o("Acesso negado",e.security_message);d(e)}catch(e){o("Erro ao carregar perfil","Não foi possível carregar suas informações.")}finally{m(!1)}},j=async()=>{try{const{error:e}=await y.from("profiles").upsert({id:s?.user?.id,full_name:s?.user?.user_metadata?.full_name||"Usuário",formation_area:s?.user?.user_metadata?.formation_area||"Não informado",graduation_year:s?.user?.user_metadata?.graduation_year||"Não informado",is_student:s?.user?.user_metadata?.is_student||!1,is_professional:s?.user?.user_metadata?.is_professional||!1,avatar_url:s?.user?.user_metadata?.avatar_url||null,professional_email:"",phone:"",registration_number:""},{onConflict:"id"});if(e)throw e;await new Promise((e=>setTimeout(e,500)));const{data:a,error:r}=await y.from("secure_profiles").select("*").eq("id",s?.user?.id).single();if(r)throw r;d(a)}catch(e){o("Erro ao criar perfil","Não foi possível criar seu perfil. Tente novamente ou entre em contato com o suporte.")}};return c?e.jsxs("div",{className:S.pageBackground(),children:[e.jsx(F,{}),e.jsx("div",{className:"container mx-auto px-4 py-8 text-center dark:text-gray-300",children:"Carregando..."}),e.jsx(I,{})]}):e.jsxs("div",{className:S.pageBackground(),children:[e.jsxs(z,{children:[e.jsx("title",{children:"PedBook | Configurações"}),e.jsx("meta",{name:"description",content:"Gerencie suas configurações e perfil no PedBook."})]}),e.jsx(F,{}),e.jsxs("main",{className:"container mx-auto px-2 sm:px-4 py-4 sm:py-8 relative space-y-4 sm:space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs(T,{to:"/",className:"flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors",children:[e.jsx(Y,{className:"h-5 w-5"}),e.jsx("span",{className:"text-sm sm:text-base",children:"Voltar"})]}),e.jsxs("div",{className:"text-center flex-1 mx-4",children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold",children:"Configurações"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground mt-1",children:"Gerencie suas informações"})]}),e.jsx("div",{className:"w-16"})," "]}),e.jsxs(r,{className:"w-full max-w-2xl mx-auto",children:[e.jsxs(t,{className:"text-center px-4 sm:px-6 py-6",children:[e.jsx(te,{profile:n,session:s,uploading:u,handleAvatarUpload:async e=>{try{if(x(!0),!e.target.files||0===e.target.files.length)throw new Error("Você precisa selecionar uma imagem para fazer upload.");const a=e.target.files[0],r=await(async e=>new Promise(((a,s)=>{const r=new Image;r.onload=()=>{const e=document.createElement("canvas");e.width=r.width,e.height=r.height;const t=e.getContext("2d");t?(t.drawImage(r,0,0),e.toBlob((e=>{e?a(e):s(new Error("Failed to convert image to WebP"))}),"image/webp",.8)):s(new Error("Failed to get canvas context"))},r.onerror=()=>s(new Error("Failed to load image")),r.src=URL.createObjectURL(e)})))(a),t=new File([r],`${a.name.split(".")[0]}.webp`,{type:"image/webp"}),o=`${s?.user?.id}-${Math.random()}.webp`,{error:l}=await y.storage.from("avatars").upload(o,t);if(l)throw l;const{data:{publicUrl:c}}=y.storage.from("avatars").getPublicUrl(o),{error:m}=await y.from("profiles").update({avatar_url:c}).eq("id",s?.user?.id);if(m)throw m;d({...n,avatar_url:c}),i("Avatar atualizado","Sua foto de perfil foi atualizada com sucesso.")}catch(a){o("Erro ao fazer upload","Não foi possível atualizar sua foto de perfil.")}finally{x(!1)}}}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-semibold",children:n?.full_name||"Usuário"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:n?.formation_area||"Área não informada"})]})]}),e.jsx(l,{className:"space-y-6 sm:space-y-8 px-4 sm:px-6 pb-6 sm:pb-8",children:e.jsx(re,{profile:n,setProfile:d,handleProfileUpdate:async e=>{e.preventDefault();try{const e={full_name:n.full_name,formation_area:n.formation_area,graduation_year:n.graduation_year,is_student:n.is_student,is_professional:n.is_professional,professional_email:n.professional_email,phone:n.phone,registration_number:n.registration_number},{error:a}=await y.from("profiles").update(e).eq("id",s?.user?.id);if(a)throw a;i("Perfil atualizado","Suas informações foram atualizadas com sucesso."),await f()}catch(a){o("Erro ao atualizar perfil","Não foi possível atualizar suas informações.")}}})})]}),e.jsx("div",{className:"w-full max-w-2xl mx-auto",children:e.jsx(ie,{})})]}),e.jsx(I,{}),e.jsx(P,{open:h,onOpenChange:e=>{p(e),e||(document.body.style.pointerEvents="auto")},children:e.jsxs(U,{className:"max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800",children:[e.jsx(A,{children:e.jsx(q,{children:"Feedback"})}),e.jsx(B,{})]})})]})};export{oe as default};
