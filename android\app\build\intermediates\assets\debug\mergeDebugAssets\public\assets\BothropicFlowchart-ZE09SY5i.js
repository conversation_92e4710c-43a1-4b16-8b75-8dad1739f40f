import{j as e}from"./radix-core-6kBL75b5.js";import{L as r}from"./router-BAzpOxbo.js";import{m as a,B as s,D as t,aK as n,e as o,f as i,g as d,z as l,aa as c,a7 as m,aw as x}from"./index-D9amGMlQ.js";import g from"./Footer-BkFd5qSK.js";import{F as p,a as u}from"./flowchartSEOData-B7-hWfhR.js";import{r as h}from"./critical-DVX9Inzy.js";import{A as b}from"./arrow-down-CRHiKBGF.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-hfK2c1c1.js";import"./rocket-D1lrdyWq.js";import"./target-Cj27UDYs.js";import"./zap-DULtmWB8.js";import"./book-open-CzUd5kBy.js";import"./star-BlLX_9hT.js";import"./circle-help-DFUIKtE9.js";import"./instagram-CuaDlQAQ.js";const v=({question:r,onAnswer:c,selectedAnswer:m})=>{const[x,g]=h.useState(!1),p="O paciente apresenta clínica de envenenamento botrópico na admissão?"===r,u="A evolução apresenta clínica de envenenamento?"===r;return e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:e.jsxs("div",{className:"p-6 rounded-xl bg-green-50 border border-green-200 glass-card relative overflow-hidden dark:bg-green-900/30 dark:border-green-800/50",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none dark:from-green-800/10"}),u&&e.jsx("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-900/30 dark:border-yellow-800/50",children:e.jsx("p",{className:"text-yellow-800 dark:text-yellow-300 font-medium",children:"Manter o paciente em observação mínima de 12h"})}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6 text-center relative z-10",children:r}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 relative z-10 mb-8",children:[e.jsx(s,{onClick:()=>c(!0),variant:!0===m?"default":"outline",className:"flex-1 transition-all duration-300 "+(!0===m?"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 dark:from-green-600 dark:to-emerald-600 dark:hover:from-green-700 dark:hover:to-emerald-700":"hover:bg-green-50 dark:hover:bg-green-900/40 border-green-200 dark:border-green-700/50 dark:text-gray-200"),children:"Com clínica de envenenamento"}),e.jsx(s,{onClick:()=>c(!1),variant:!1===m?"default":"outline",className:"flex-1 transition-all duration-300 "+(!1===m?"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 dark:from-green-600 dark:to-emerald-600 dark:hover:from-green-700 dark:hover:to-emerald-700":"hover:bg-green-50 dark:hover:bg-green-900/40 border-green-200 dark:border-green-700/50 dark:text-gray-200"),children:"Sem clínica de envenenamento"})]}),p&&e.jsxs("div",{className:"space-y-4 bg-white/50 rounded-lg p-4 backdrop-blur-sm border border-green-100 dark:bg-slate-800/50 dark:border-green-900/30",children:[e.jsx("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-300",children:"Sinais de envenenamento botrópico incluem:"}),e.jsxs("div",{className:"space-y-3 text-gray-700 dark:text-gray-300",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-700 dark:text-green-300",children:"Manifestações locais:"}),e.jsx("p",{children:"Dor, edema, equimose, sangramentos na região da picada."})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-700 dark:text-green-300",children:"Manifestações sistêmicas leves:"}),e.jsx("p",{children:"Náusea, vômito."})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-700 dark:text-green-300",children:"Manifestações graves:"}),e.jsx("p",{children:"Hemorragias, insuficiência renal, choque."})]})]}),e.jsxs(t,{open:x,onOpenChange:g,children:[e.jsx(n,{asChild:!0,children:e.jsx(s,{variant:"outline",className:"w-full mt-4 bg-green-100 hover:bg-green-200 border-green-300 dark:bg-green-900/30 dark:hover:bg-green-900/50 dark:border-green-800/50 dark:text-green-300",children:"Saiba Mais"})}),e.jsx(o,{className:"max-w-2xl bg-white dark:bg-slate-900 dark:border-green-900/50",children:e.jsxs(i,{children:[e.jsx(d,{className:"dark:text-white",children:"Detalhamento dos Sinais de Envenenamento Botrópico"}),e.jsxs(l,{className:"space-y-4 pt-4 dark:text-gray-300",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-700 dark:text-green-300 mb-2",children:"Manifestações Locais"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1 dark:text-gray-300",children:[e.jsx("li",{children:"Dor local intensa e imediata"}),e.jsx("li",{children:"Edema progressivo"}),e.jsx("li",{children:"Equimose"}),e.jsx("li",{children:"Sangramento local"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-700 dark:text-green-300 mb-2",children:"Manifestações Sistêmicas"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1 dark:text-gray-300",children:[e.jsx("li",{children:"Náusea e vômitos"}),e.jsx("li",{children:"Sangramentos em mucosas"}),e.jsx("li",{children:"Hipotensão arterial"}),e.jsx("li",{children:"Choque"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-700 dark:text-green-300 mb-2",children:"Manifestações Graves"}),e.jsxs("ul",{className:"list-disc pl-5 space-y-1 dark:text-gray-300",children:[e.jsx("li",{children:"Hemorragias"}),e.jsx("li",{children:"Insuficiência renal"}),e.jsx("li",{children:"Choque"})]})]})]})]})})]})]})]})})},j=({group:r,color:s,instructions:t,nextQuestion:n,nextStep:o,onContinue:i})=>{const d=s.split("-")[1],l={green:"dark:bg-green-900/30 dark:border-green-800/30 dark:text-green-300",red:"dark:bg-red-900/30 dark:border-red-800/30 dark:text-red-300",yellow:"dark:bg-yellow-900/30 dark:border-yellow-800/30 dark:text-yellow-300",orange:"dark:bg-orange-900/30 dark:border-orange-800/30 dark:text-orange-300",blue:"dark:bg-blue-900/30 dark:border-blue-800/30 dark:text-blue-300",purple:"dark:bg-purple-900/30 dark:border-purple-800/30 dark:text-purple-300"},c=l[d]||l.green;return e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[e.jsxs("div",{className:`p-6 rounded-xl ${s} border border-${d}-200 glass-card relative overflow-hidden ${c}`,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none dark:from-slate-800/10"}),e.jsx("h3",{className:`text-xl font-bold text-${d}-800 dark:text-${d}-300 mb-4`,children:r}),e.jsx("div",{className:`space-y-3 text-${d}-700 dark:text-${d}-200`,children:t.map(((r,a)=>e.jsx("p",{className:"relative z-10",children:r},a)))}),(n||o)&&e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-6 flex flex-col items-center",children:[e.jsx(b,{className:"w-8 h-8 text-primary dark:text-blue-400 animate-bounce"}),e.jsx("p",{className:"text-sm text-primary dark:text-blue-400 mt-2",children:"Continue o manejo abaixo"})]})]}),n&&e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex justify-center",children:e.jsx("div",{onClick:()=>i?.(n),className:"p-4 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 \r\n                     hover:from-primary/20 hover:to-primary/10 transition-all cursor-pointer backdrop-blur-sm\r\n                     dark:from-blue-900/30 dark:to-blue-900/20 dark:border-blue-800/30 dark:hover:from-blue-900/40 dark:hover:to-blue-900/30",children:e.jsx("p",{className:"text-primary dark:text-blue-400 font-medium text-center",children:"Próxima Avaliação"})})}),o&&e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex justify-center",children:e.jsx("div",{onClick:()=>i?.(o),className:"p-4 rounded-lg bg-gradient-to-r from-green-500/10 to-green-500/5 border border-green-500/20 \r\n                     hover:from-green-500/20 hover:to-green-500/10 transition-all cursor-pointer backdrop-blur-sm\r\n                     dark:from-green-900/30 dark:to-green-900/20 dark:border-green-800/30 dark:hover:from-green-900/40 dark:hover:to-green-900/30",children:e.jsx("p",{className:"text-green-600 dark:text-green-400 font-medium text-center",children:"Próxima Etapa"})})})]})},k=()=>e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-4",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"Observação e Reavaliação"}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:e.jsxs("ul",{className:"space-y-4 text-gray-700",children:[e.jsxs("li",{className:"flex gap-2",children:[e.jsx("span",{className:"text-blue-600",children:"•"}),e.jsx("span",{children:"Após administração inicial de soro em qualquer quadro clínico:"})]}),e.jsxs("li",{className:"flex gap-2",children:[e.jsx("span",{className:"text-blue-600",children:"•"}),e.jsx("span",{children:"Monitorar continuamente a evolução clínica do paciente."})]}),e.jsxs("li",{className:"flex gap-2",children:[e.jsx("span",{className:"text-blue-600",children:"•"}),e.jsx("span",{children:"Detectar e tratar precocemente complicações como insuficiência renal ou distúrbios de coagulação."})]}),e.jsxs("li",{className:"flex gap-2",children:[e.jsx("span",{className:"text-blue-600",children:"•"}),e.jsx("span",{children:"Reclassificar o quadro clínico, se necessário, e ajustar o tratamento."})]})]})}),e.jsx("div",{className:"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-600 italic",children:"Referência: BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024."})})]}),f={initial:"O paciente apresenta clínica de envenenamento botrópico na admissão?",observation:"A evolução apresenta clínica de envenenamento?",severity:"Classifique a gravidade do quadro:"},y={noSigns:{group:"Sem Clínica de Envenenamento",color:"bg-green-50",instructions:["Características na Admissão: Marca da mordida presente ou ausente, dor e edema discretos ou ausentes (devido apenas à lesão da mordida).","Manter o paciente em observação por um período mínimo de 12 horas.","Durante o período de observação:","- Monitorar sinais vitais regularmente","- Realizar exames complementares, como coagulograma, se necessário"],nextQuestion:"observation"},discharge:{group:"Alta Hospitalar - Picada Seca",color:"bg-green-50",instructions:["Uma picada seca é uma picada de um animal venenoso na qual nenhum veneno é liberado.","Paciente está estável e apto para alta.","Retorne imediatamente ao serviço de saúde caso surjam novos sintomas ou piora do quadro."]},regularDischarge:{group:"Alta Hospitalar",color:"bg-green-50",instructions:["Paciente está estável e apto para alta.","Retorne imediatamente ao serviço de saúde caso surjam novos sintomas ou piora do quadro."]},mild:{group:"Quadro Leve",color:"bg-green-50",instructions:["Manifestações locais como dor, edema limitado a um segmento anatômico.","Hemorragia discreta ou ausente.","Conduta:","- Administrar SAB (3 ampolas IV)","- Observar por 12 horas","Monitorar continuamente a evolução clínica do paciente.","Detectar e tratar precocemente complicações.","Reclassificar o quadro clínico, se necessário, e ajustar o tratamento."],nextStep:"regularDischarge"},moderate:{group:"Quadro Moderado",color:"bg-orange-50",instructions:["Dor intensa e edema envolvendo mais de um segmento anatômico.","Hemorragia discreta ou ausente.","Conduta:","- Administrar SAB (6 ampolas IV)","- Internar para monitoramento, analgesia e hidratação venosa","Monitorar continuamente a evolução clínica do paciente.","Detectar e tratar precocemente complicações.","Reclassificar o quadro clínico, se necessário, e ajustar o tratamento."],nextStep:"regularDischarge"},severe:{group:"Quadro Grave",color:"bg-red-50",instructions:["Manifestações graves como hemorragias, sudorese intensa, sialorréia, agitação/sonolência, priaprismo","alterações da FC e/ou PA, insuficiência cardíaca ou edema pulmonar agudo.","Conduta:","- Administrar SAB (12 ampolas IV)","- Internar em UTI para suporte intensivo","Monitorar continuamente a evolução clínica do paciente.","Detectar e tratar precocemente complicações.","Reclassificar o quadro clínico, se necessário, e ajustar o tratamento."],nextStep:"regularDischarge"}},N=()=>{const{currentStep:a,answers:s,handleAnswer:t,handleContinue:n,resetFlow:o,getCurrentQuestion:i,getCurrentResult:d}=(()=>{const[e,r]=h.useState("initial"),[a,s]=h.useState({});return{currentStep:e,answers:a,handleAnswer:t=>{const n={...a,[e]:t};s(n),"initial"===e?r(!1===t?"observation":"severity"):"observation"===e?r(!1===t?"discharge":"severity"):"severity"===e&&r(t)},handleContinue:e=>{r(e)},resetFlow:()=>{r("initial"),s({})},getCurrentQuestion:()=>f[e],getCurrentResult:()=>"observation"===e&&!1===a.initial?y.noSigns:"discharge"===e&&!1===a.observation?y.discharge:y[e]}})(),l=u.bothropic;return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-green-50 via-white to-green-50 dark:from-green-900/20 dark:via-slate-900 dark:to-green-900/10",children:[e.jsx(p,{...l}),e.jsx(c,{}),e.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-5xl mx-auto space-y-8",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs(r,{to:"/flowcharts/venomous",className:"hidden sm:inline-flex items-center gap-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors",children:[e.jsx(m,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Animais Peçonhentos"})]})}),e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("h1",{className:"text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400",children:"Acidente Botrópico"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto",children:"Fluxograma para manejo de acidentes botrópicos em pediatria"})]}),e.jsx(x,{className:"h-[calc(100vh-300px)] pr-4",children:e.jsxs("div",{className:"space-y-6",children:[(()=>{const r=i(),l=d();return r?"severity"===a?e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-center mb-6 text-gray-800 dark:text-gray-100",children:r}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[e.jsxs("div",{onClick:()=>t("mild"),className:"p-6 rounded-lg bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700/50 \r\n                         hover:bg-green-100 dark:hover:bg-green-800/40 transition-all cursor-pointer space-y-2",children:[e.jsx("h3",{className:"font-semibold text-green-800 dark:text-green-300",children:"Leve"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Manifestações apenas locais"})]}),e.jsxs("div",{onClick:()=>t("moderate"),className:"p-6 rounded-lg bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700/50 \r\n                         hover:bg-orange-100 dark:hover:bg-orange-800/40 transition-all cursor-pointer space-y-2",children:[e.jsx("h3",{className:"font-semibold text-orange-800 dark:text-orange-300",children:"Moderado"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Manifestações locais extensas"})]}),e.jsxs("div",{onClick:()=>t("severe"),className:"p-6 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700/50 \r\n                         hover:bg-red-100 dark:hover:bg-red-800/40 transition-all cursor-pointer space-y-2",children:[e.jsx("h3",{className:"font-semibold text-red-800 dark:text-red-300",children:"Grave"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Manifestações sistêmicas graves"})]})]})]}):e.jsx(v,{question:r,onAnswer:t,selectedAnswer:s[a]}):l?e.jsx(j,{...l,onReset:o,nextQuestion:"observation"===a?"initial":void 0,onContinue:n}):null})(),e.jsx(k,{})]})})]})}),e.jsx(g,{})]})};export{N as default};
