import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{s as a,aB as r,Y as i,R as t,W as n,Z as o,$ as c,U as l,B as d,L as m,an as p,a5 as h,ao as u,ay as g,aw as v,V as x}from"./index-D9amGMlQ.js";import{A as f,b as _}from"./alert-DF0vYpCj.js";import{u as j}from"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const b=[{id:1,name:"Analgésicos não-opioides & Antitérmicos"},{id:2,name:"Opioides & Hipnoanalgésicos"},{id:3,name:"AINEs (Anti-inflamatórios não esteroidais)"},{id:4,name:"Corticosteroides — Sistêmicos"},{id:5,name:"Corticosteroides / Anti-inflamatórios — Tópicos"},{id:6,name:"Antibacterianos Sistêmicos"},{id:7,name:"Antifúngicos Sistêmicos"},{id:8,name:"Antivirais Sistêmicos"},{id:9,name:"Antissépticos & Antimicrobianos Tópicos"},{id:10,name:"Antifúngicos Tópicos"},{id:11,name:"Antiparasitários"},{id:12,name:"Anti-histamínicos & Antialérgicos"},{id:13,name:"Broncodilatadores"},{id:14,name:"Antitussígenos & Expectorantes"},{id:15,name:"Gastroprotetores & Antiácidos"},{id:16,name:"Antieméticos & Procinéticos"},{id:17,name:"Antidiarreicos & Antiflatulentes"},{id:18,name:"Laxativos & Catárticos"},{id:19,name:"Anti-hipertensivos & Diuréticos"},{id:20,name:"Antiarrítmicos & Insuficiência Cardíaca"},{id:21,name:"Anticoagulantes, Hemostáticos & Sangue"},{id:22,name:"Neurológicos & Psicotrópicos"},{id:23,name:"Endócrino & Metabólico"},{id:24,name:"Vitaminas, Minerais & Suplementos"},{id:25,name:"Antídotos, Quelantes & Emergência"}];async function $(){try{const{data:e,error:s}=await a.from("active_ingredient_therapeutic_classes").select("active_ingredient_id");if(s)throw new Error(`Failed to fetch active ingredients with class: ${s.message}`);const r=e?.map((e=>e.active_ingredient_id))||[];let i=a.from("active_ingredients").select("id, name, dcb_code, cas_number").order("name");r.length>0&&(i=i.not("id","in",`(${r.map((e=>`'${e}'`)).join(",")})`));const{data:t,error:n}=await i;if(n)throw new Error(`Failed to fetch active ingredients without class: ${n.message}`);return t||[]}catch(e){throw e}}async function N(e,s){try{const{error:r}=await a.from("active_ingredients").update({name:s}).eq("id",e);if(r)throw new Error(`Failed to update active ingredient: ${r.message}`)}catch(r){throw r}}async function w(e){try{const{data:s,error:r}=await a.from("therapeutic_classes").select("id").eq("class_number",e).single();return r?null:s?.id||null}catch(s){return null}}async function E(e,s){try{const{data:r}=await a.from("active_ingredient_therapeutic_classes").select("id").eq("active_ingredient_id",e).eq("therapeutic_class_id",s).single();if(r)return;const{error:i}=await a.from("active_ingredient_therapeutic_classes").insert({active_ingredient_id:e,therapeutic_class_id:s});if(i)throw new Error(`Failed to link active ingredient to therapeutic class: ${i.message}`)}catch(r){throw r}}const A=()=>{const[A,y]=s.useState(!1),[C,I]=s.useState([]),[P,S]=s.useState([]),[R,L]=s.useState([]),[q,M]=s.useState(!1),[k,T]=s.useState(15),[V,F]=s.useState({current:0,total:0,item:"",details:""}),[O,z]=s.useState(null),[D,B]=s.useState("therapeutic_classes"),{data:G,isLoading:H,refetch:U}=j({queryKey:["active-ingredients-to-improve",D],queryFn:()=>"therapeutic_classes"===D?async function(){try{const{data:e,error:s}=await a.rpc("get_active_ingredients_without_class");return s?await $():e?.map((e=>({id:e.id,name:e.name,dcb_code:e.dcb_code,cas_number:e.cas_number})))||[]}catch(e){return await $()}}():async function(){try{const{data:e,error:s}=await a.rpc("get_active_ingredient_combinations");if(s)throw s;const r=e?.map((e=>({id:e.combination_id,name:e.combination_name,dcb_code:e.dcb_codes?.[0]||null,cas_number:e.cas_numbers?.[0]||null})))||[];return r.length>0&&r.slice(0,5).forEach(((e,s)=>{})),r}catch(e){throw e}}(),enabled:!1}),K=(e,s)=>{I((a=>[...a,{type:e,message:s,timestamp:new Date}]))},Y=e=>{"therapeutic_classes"===D?S((s=>s.map(((s,a)=>a===e?{...s,status:"approved"}:s)))):L((s=>s.map(((s,a)=>a===e?{...s,status:"approved"}:s))))},Z=e=>{"therapeutic_classes"===D?S((s=>s.map(((s,a)=>a===e?{...s,status:"rejected"}:s)))):L((s=>s.map(((s,a)=>a===e?{...s,status:"rejected"}:s))))},Q=()=>{"therapeutic_classes"===D?S((e=>e.map((e=>e.improvement?.is_invalid_medication?e:{...e,status:"approved"})))):L((e=>e.map((e=>({...e,status:"approved"})))))},W=()=>{"therapeutic_classes"===D?S((e=>e.map((e=>({...e,status:"rejected"}))))):L((e=>e.map((e=>({...e,status:"rejected"})))))},J=s=>{switch(s){case"success":return e.jsx(x,{className:"h-4 w-4 text-green-500"});case"error":return e.jsx(i,{className:"h-4 w-4 text-red-500"});default:return e.jsx(m,{className:"h-4 w-4 text-blue-500"})}},X=e=>{switch(e){case"success":return"text-green-700";case"error":return"text-red-700";default:return"text-blue-700"}};return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{className:"h-6 w-6 text-purple-500"}),e.jsx("h1",{className:"text-3xl font-bold",children:"Melhoria de Medicamentos com IA"})]}),e.jsxs(f,{children:[e.jsx(i,{className:"h-4 w-4"}),e.jsx(_,{children:"Esta ferramenta usa IA (Google Gemini 2.5 Flash) com PROCESSAMENTO PARALELO REAL (3 chaves API) para melhorar princípios ativos. Processa 3x mais itens: se você escolher 15, processará 45 itens (15 por chave API simultaneamente)! Aprove/rejeite as sugestões antes de aplicar ao banco de dados."})]}),e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{children:"Modo de Processamento"}),e.jsx(c,{children:"Escolha o que deseja processar com IA"})]}),e.jsxs(l,{children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsx(d,{onClick:()=>B("therapeutic_classes"),variant:"therapeutic_classes"===D?"default":"outline",className:"flex-1",children:"🏥 Classes Terapêuticas"}),e.jsx(d,{onClick:()=>B("commercial_names"),variant:"commercial_names"===D?"default":"outline",className:"flex-1",children:"💊 Nomes Comerciais"})]}),e.jsx("div",{className:"mt-3 text-sm text-muted-foreground",children:"therapeutic_classes"===D?"🎯 Busca e classifica princípios ativos SEM classe terapêutica em 25 categorias":"🔍 Busca nomes comerciais (marcas) dos princípios ativos no mercado brasileiro"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{children:"Controle do Processamento"}),e.jsx(c,{children:"Carregue e processe os princípios ativos para melhoria"})]}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{onClick:async()=>{K("info","Verificando classes terapêuticas...");try{const e=await async function(){try{const{data:e,error:s}=await a.from("therapeutic_classes").select("class_number").order("class_number");if(s)return{isValid:!1,message:`Erro ao verificar classes: ${s.message}`,classesCount:0};const r=e?.map((e=>e.class_number)).filter((e=>null!==e))||[],i=Array.from({length:25},((e,s)=>s+1)),t=25===r.length&&i.every((e=>r.includes(e)));return{isValid:t,message:t?"✅ Todas as 25 classes terapêuticas estão configuradas corretamente":`⚠️ Classes incompletas: encontradas ${r.length}/25`,classesCount:r.length}}catch(e){return{isValid:!1,message:`Erro inesperado: ${e instanceof Error?e.message:"Erro desconhecido"}`,classesCount:0}}}();K(e.isValid?"success":"error",e.message),e.isValid||(K("info","As classes terapêuticas já foram configuradas diretamente no banco de dados."),K("info","Se houver problemas, entre em contato com o administrador do sistema."))}catch(e){K("error",`Erro ao verificar classes: ${e instanceof Error?e.message:"Erro desconhecido"}`)}},disabled:A||q,variant:"outline",size:"sm",children:"🔍 Verificar Classes (25)"}),e.jsx(d,{onClick:async()=>{try{K("info","Verificando princípios ativos duplicados...");const e=await async function(){try{const{data:e,error:s}=await a.from("active_ingredients").select("id, name").order("name");if(s)throw new Error(`Failed to fetch active ingredients: ${s.message}`);const r=(e||[]).reduce(((e,s)=>{const a=s.name.toLowerCase().trim();return e[a]||(e[a]={name:s.name,count:0,ids:[]}),e[a].count++,e[a].ids.push(s.id),e}),{}),i=Object.values(r).filter((e=>e.count>1));return i.forEach((e=>{})),{duplicates:i,totalDuplicates:i.reduce(((e,s)=>e+s.count),0)}}catch(e){throw e}}();0===e.duplicates.length?K("success","✅ Nenhum princípio ativo duplicado encontrado"):(K("error",`⚠️ Encontrados ${e.duplicates.length} nomes duplicados (${e.totalDuplicates} registros total)`),e.duplicates.slice(0,5).forEach((e=>{K("info",`  - "${e.name}": ${e.count} ocorrências`)})),e.duplicates.length>5&&K("info",`  ... e mais ${e.duplicates.length-5} duplicados`))}catch(e){K("error",`Erro ao verificar duplicatas: ${e instanceof Error?e.message:"Erro desconhecido"}`)}},disabled:A||q,variant:"outline",size:"sm",children:"🔍 Verificar Duplicatas"})]}),e.jsxs("div",{className:"flex gap-2 items-end",children:[e.jsx(d,{onClick:async()=>{K("info","therapeutic_classes"===D?"Carregando princípios ativos SEM classe terapêutica...":"Carregando TODAS as combinações de princípios ativos (únicos + associações)..."),await U(),K("success","therapeutic_classes"===D?`${G?.length||0} princípios ativos SEM classe carregados`:`${G?.length||0} combinações de princípios ativos carregadas (únicos + associações)`)},disabled:H||A||q,variant:"outline",children:H?e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"mr-2 h-4 w-4 animate-spin"}),"Carregando..."]}):"therapeutic_classes"===D?"Carregar Sem Classe":"Carregar Combinações"}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(p,{htmlFor:"batchSize",className:"text-xs",children:"Base (x3 = Total)"}),e.jsx(h,{id:"batchSize",type:"number",min:"5",max:"15",value:k,onChange:e=>T(Math.max(5,Math.min(15,parseInt(e.target.value)||15))),disabled:A||q,className:"w-20",title:`Processará ${3*k} itens total (${k} por chave API)`})]}),e.jsx(d,{onClick:async()=>{if(G&&0!==G.length){y(!0),S([]),L([]),z(null),F({current:0,total:0,item:"",details:""}),K("info","therapeutic_classes"===D?`Processando lote de ${k} princípios ativos para CLASSES TERAPÊUTICAS...`:`Processando lote de ${k} princípios ativos para NOMES COMERCIAIS...`);try{if("therapeutic_classes"===D){const e=await async function(e,s=15,r){const i=Math.min(3*s,e.length),t=e.slice(0,i),n=[];r?.(0,1,"",`Preparando ${i} princípios ativos para processamento PARALELO REAL (${s} x 3 chaves)...`);try{const e=[],s=Math.ceil(t.length/3);for(let a=0;a<3;a++){const r=a*s,i=Math.min(r+s,t.length);r<t.length&&e.push(t.slice(r,i))}e.forEach(((e,s)=>{})),r?.(1,1,`${t.length} itens total`,`🚀 Processando ${e.length} lotes paralelos: ${e.map((e=>e.length)).join(" + ")} = ${t.length} itens`);const i=e.map((async(e,s)=>{const r=e.map((e=>e.name));try{const i=await async function(e){try{const{data:s,error:r}=await a.functions.invoke("improve-medication",{body:{active_principles:e,current_data:void 0}});if(r)throw new Error(`Failed to improve medications batch: ${r.message}`);if(!s.success)throw new Error(s.error||"Unknown error occurred");return s.data.results}catch(s){throw s}}(r);if(!i.every((e=>e&&e.improved_name&&e.therapeutic_class&&"number"==typeof e.therapeutic_class_number&&e.therapeutic_class_number>=0)))throw new Error(`Estrutura de resultados inválida do lote ${s+1}`);return e.map(((e,a)=>({ingredient:e,improvement:i[a],batchIndex:s,success:!0})))}catch(i){return{batchIndex:s,success:!1,error:i instanceof Error?i.message:"Erro desconhecido",failedItems:e,isEmpty:!0}}})),o=await Promise.all(i),c=[],l=[];let d=0;if(o.forEach(((e,s)=>{Array.isArray(e)?c.push(...e):e.isEmpty&&(l.push(e),d+=e.failedItems.length)})),l.length>0){if(l.map((e=>e.batchIndex+1)).join(", "),r?.(1,1,`${c.length} de ${t.length} itens`,`⚠️ FALHA PARCIAL: ${c.length} processados, ${d} falharam (chaves API com problema)`),d>c.length)throw new Error(`Falha crítica: ${d} itens falharam vs ${c.length} sucessos. Verifique as chaves API.`)}else r?.(1,1,`${c.length} itens processados`,`✅ Processamento PARALELO concluído! ${c.length} medicamentos processados com sucesso`);const m=c;for(const a of m){const{ingredient:e,improvement:s}=a;n.push({id:e.id,original_name:e.name,improved_name:s.improved_name,therapeutic_class:s.therapeutic_class,therapeutic_class_number:s.therapeutic_class_number,status:"pending",dcb_code:e.dcb_code,cas_number:e.cas_number,improvement:s})}r?.(1,1,"",`🎉 Processamento PARALELO REAL concluído! ${n.length} itens prontos para revisão.`)}catch(o){const e=o instanceof Error?o.message:"Erro desconhecido";r?.(1,1,"",`❌ Erro no processamento paralelo: ${e}`);for(const s of t)n.push({id:s.id,original_name:s.name,improved_name:s.name,therapeutic_class:`ERRO - ${e}`,therapeutic_class_number:1,status:"rejected",dcb_code:s.dcb_code,cas_number:s.cas_number})}return n}(G,k,((e,s,a,r)=>{F({current:e,total:s,item:a,details:r}),K("info",`[${e}/${s}] ${a}: ${r}`)}));S(e),K("success",`Lote processado! ${e.length} itens para revisão.`)}else{const e=await async function(e,s=15,r){const i=Math.min(3*s,e.length),t=e.slice(0,i),n=[];r?.(0,1,"",`Preparando ${i} princípios ativos para busca de nomes comerciais PARALELA (${s} x 3 chaves)...`);try{const e=[],i=Math.ceil(t.length/3);for(let s=0;s<3;s++){const a=s*i,r=Math.min(a+i,t.length);a<t.length&&e.push(t.slice(a,r))}e.forEach(((e,s)=>{})),r?.(1,1,`${t.length} itens total`,`🚀 Processando ${e.length} lotes paralelos: ${e.map((e=>e.length)).join(" + ")} = ${t.length} itens`);const o=e.map((async(e,r)=>{const i=e.map((e=>e.name));try{const t=await async function(e,s=15){try{const{data:r,error:i}=await a.functions.invoke("get-commercial-names",{body:{active_ingredients:e,batch_size:s}});if(i)throw new Error(`Failed to get commercial names: ${i.message}`);if(!r.success)throw new Error(r.error||"Unknown error occurred");return r.results||[]}catch(r){throw r}}(i,s);return e.map(((e,s)=>({ingredient:e,commercialName:t[s],batchIndex:r,success:!0})))}catch(t){return{batchIndex:r,success:!1,error:t instanceof Error?t.message:"Erro desconhecido",failedItems:e,isEmpty:!0}}})),c=await Promise.all(o),l=[],d=[];let m=0;c.forEach(((e,s)=>{Array.isArray(e)?l.push(...e):e.isEmpty&&(d.push(e),m+=e.failedItems.length)}));for(const s of l){const{ingredient:e,commercialName:a}=s;a&&n.push({id:e.id,active_ingredient_name:e.name,commercial_names:a.commercial_names||[],confidence_level:a.confidence_level||"baixa",source_info:a.source_info||"IA Gemini",status:"pending"})}r?.(1,1,"",`🎉 Busca de nomes comerciais concluída! ${n.length} itens prontos para revisão.`)}catch(o){const e=o instanceof Error?o.message:"Erro desconhecido";throw r?.(1,1,"",`❌ Erro na busca de nomes comerciais: ${e}`),o}return n}(G,k,((e,s,a,r)=>{F({current:e,total:s,item:a,details:r}),K("info",`[${e}/${s}] ${a}: ${r}`)}));L(e),K("success",`Nomes comerciais encontrados! ${e.length} itens para revisão.`)}}catch(e){const s=e instanceof Error?e.message:"Erro desconhecido";s.includes("chaves API")?(K("error",`❌ FALHA NAS CHAVES API: ${s}`),K("info","💡 Verifique se GEMINI_API_KEY2 e GEMINI_API_KEY3 estão configuradas corretamente no Supabase"),K("info","🔄 Tente novamente ou use um lote menor para processar apenas com a chave principal")):K("error",`Erro durante o processamento: ${s}`)}finally{y(!1),F({current:0,total:0,item:"",details:""})}}else K("error","Nenhum princípio ativo carregado")},disabled:!G||0===G.length||A||q,children:A?e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"mr-2 h-4 w-4 animate-spin"}),"Processando..."]}):"therapeutic_classes"===D?`🚀 Processar ${3*k} Itens (${k}x3)`:`🔍 Buscar Nomes ${3*k} Itens (${k}x3)`}),(P.length>0||R.length>0)&&e.jsx(d,{onClick:async()=>{if("therapeutic_classes"===D){const s=P.filter((e=>"approved"===e.status));if(0===s.length)return void K("error","Nenhum item aprovado para aplicar");M(!0),F({current:0,total:0,item:"",details:""}),K("info",`Aplicando ${s.length} melhorias aprovadas...`);try{const e=await async function(e,s){const a={applied:0,errors:[]},r=e.filter((e=>"approved"===e.status));s?.(0,r.length,"",`Iniciando aplicação de ${r.length} melhorias aprovadas...`);for(let t=0;t<r.length;t++){const e=r[t];try{s?.(t+1,r.length,e.original_name,`Aplicando melhorias: ${e.original_name} → ${e.improved_name}`),e.improved_name!==e.original_name&&(s?.(t+1,r.length,e.original_name,`📝 Atualizando nome no banco: "${e.improved_name}"`),await N(e.id,e.improved_name)),s?.(t+1,r.length,e.original_name,`🔍 Buscando classe terapêutica ${e.therapeutic_class_number}`);const i=await w(e.therapeutic_class_number);if(!i)throw new Error(`Classe terapêutica ${e.therapeutic_class_number} não encontrada`);s?.(t+1,r.length,e.original_name,`🔗 Vinculando à classe: ${e.therapeutic_class}`),await E(e.id,i),s?.(t+1,r.length,e.original_name,`✅ Concluído: ${e.improved_name}`),a.applied++}catch(i){const n=i instanceof Error?i.message:"Erro desconhecido";s?.(t+1,r.length,e.original_name,`❌ Erro: ${n}`),a.errors.push({id:e.id,name:e.original_name,error:n})}}return s?.(r.length,r.length,"",`🎉 Aplicação concluída! ${a.applied} melhorias aplicadas, ${a.errors.length} erros`),a}(P,((e,s,a,r)=>{F({current:e,total:s,item:a,details:r}),K("info",`[${e}/${s}] ${a}: ${r}`)}));z(e),K("success",`${e.applied} melhorias aplicadas com sucesso!`),e.errors.length>0&&K("error",`${e.errors.length} erros durante a aplicação`),await U(),S([])}catch(e){K("error",`Erro ao aplicar melhorias: ${e instanceof Error?e.message:"Erro desconhecido"}`)}finally{M(!1),F({current:0,total:0,item:"",details:""})}}else{const s=R.filter((e=>"approved"===e.status));if(0===s.length)return void K("error","Nenhum nome comercial aprovado para aplicar");M(!0),F({current:0,total:0,item:"",details:""}),K("info",`Aplicando ${s.length} nomes comerciais aprovados...`);try{const s=await async function(s,r){const i=s.filter((e=>"approved"===e.status)),t={applied:0,errors:[]};for(let n=0;n<i.length;n++){const s=i[n];r?.(n+1,i.length,s.active_ingredient_name,`Salvando ${s.commercial_names.length} nomes comerciais...`);try{for(const e of s.commercial_names){const{error:r}=await a.from("active_ingredient_commercial_names").insert({active_ingredient_combination:s.active_ingredient_name,commercial_name:e,confidence_level:s.confidence_level,source_info:s.source_info,is_verified:!0});if(r&&!r.message.includes("duplicate")&&!r.message.includes("unique"))throw r}t.applied++}catch(e){const r=e instanceof Error?e.message:"Erro desconhecido";t.errors.push({id:s.id,name:s.active_ingredient_name,error:r})}}return t}(R,((e,s,a,r)=>{F({current:e,total:s,item:a,details:r}),K("info",`[${e}/${s}] ${a}: ${r}`)}));z(s),K("success",`${s.applied} nomes comerciais aplicados com sucesso!`),s.errors.length>0&&K("error",`${s.errors.length} erros durante a aplicação`),L([])}catch(e){K("error",`Erro ao aplicar nomes comerciais: ${e instanceof Error?e.message:"Erro desconhecido"}`)}finally{M(!1),F({current:0,total:0,item:"",details:""})}}},disabled:q||A||("therapeutic_classes"===D?0===P.filter((e=>"approved"===e.status)).length:0===R.filter((e=>"approved"===e.status)).length),variant:"default",children:q?e.jsxs(e.Fragment,{children:[e.jsx(m,{className:"mr-2 h-4 w-4 animate-spin"}),"Aplicando..."]}):"therapeutic_classes"===D?`Aplicar Aprovados (${P.filter((e=>"approved"===e.status)).length})`:`Aplicar Aprovados (${R.filter((e=>"approved"===e.status)).length})`})]}),(A||q)&&V.total>0&&e.jsxs("div",{className:"space-y-2 p-3 bg-blue-50 rounded-lg border",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"font-medium",children:A?"Processando com IA":"Aplicando ao Banco"}),e.jsxs("span",{children:[V.current,"/",V.total]})]}),e.jsx(u,{value:V.current/V.total*100,className:"w-full"}),V.item&&e.jsx("p",{className:"text-xs text-muted-foreground",children:e.jsx("strong",{children:V.item})}),V.details&&e.jsx("p",{className:"text-xs text-blue-700",children:V.details})]})]}),G&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:[e.jsx("strong",{children:G.length})," ","therapeutic_classes"===D?"princípios ativos sem classe terapêutica":"combinações de princípios ativos (únicos + associações)"]}),"commercial_names"===D&&G.length>0&&e.jsxs("div",{className:"text-xs text-blue-600 bg-blue-50 p-2 rounded",children:["💡 ",e.jsx("strong",{children:"Exemplos:"})," ",G.slice(0,3).map((e=>e.name)).join(", "),G.length>3&&"..."]})]}),O&&e.jsxs("div",{className:"space-y-2 p-4 bg-muted rounded-lg",children:[e.jsx("h4",{className:"font-semibold",children:"Resultados Aplicados:"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(g,{variant:"default",children:[O.applied," aplicados"]}),e.jsxs(g,{variant:O.errors.length>0?"destructive":"secondary",children:[O.errors.length," erros"]})]})]})]})]}),e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{children:"Classes Terapêuticas"}),e.jsx(c,{children:"25 classes definidas para classificação"})]}),e.jsx(l,{children:e.jsx(v,{className:"h-[400px]",children:e.jsx("div",{className:"space-y-2",children:b.map((s=>e.jsxs("div",{className:"flex items-center gap-2 p-2 rounded border",children:[e.jsx(g,{variant:"outline",children:s.id}),e.jsx("span",{className:"text-sm",children:s.name})]},s.id)))})})})]})]}),R.length>0&&e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{children:"Nomes Comerciais Encontrados"}),e.jsx(c,{children:"Revise e aprove/rejeite os nomes comerciais encontrados pela IA"}),e.jsxs("div",{className:"flex gap-2 pt-4",children:[e.jsxs(d,{onClick:Q,disabled:q||0===R.filter((e=>"approved"!==e.status)).length,variant:"default",size:"sm",children:["✅ Aprovar Todos (",R.filter((e=>"approved"!==e.status)).length,")"]}),e.jsx(d,{onClick:W,disabled:q,variant:"outline",size:"sm",children:"❌ Rejeitar Todos"})]})]}),e.jsx(l,{children:e.jsx("div",{className:"space-y-4",children:R.map(((s,a)=>e.jsx("div",{className:"border rounded-lg p-4 space-y-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-lg",children:s.active_ingredient_name}),e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Nomes Comerciais:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.commercial_names.length>0?s.commercial_names.map(((s,a)=>e.jsx(g,{variant:"outline",className:"text-sm",children:s},a))):e.jsx(g,{variant:"secondary",className:"text-sm",children:"Nenhum nome comercial encontrado"})})]}),e.jsxs("div",{className:"mt-2 flex gap-2",children:[e.jsxs(g,{variant:"alta"===s.confidence_level?"default":"média"===s.confidence_level?"secondary":"outline",children:["Confiança: ",s.confidence_level]}),e.jsx(g,{variant:"outline",className:"text-xs",children:s.source_info})]}),(()=>{const a=["Brondilat","Filinar","Bronquivita","Zero-dor","Cancidas","Pharmaton","Gerilon","Vitergan","Belara","Allurene","Polaramine"];return s.commercial_names.some((e=>a.includes(e)))?e.jsxs("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700",children:["⚠️ ",e.jsx("strong",{children:"NOMES SUSPEITOS:"})," Alguns nomes podem estar incorretos. Verifique cuidadosamente antes de aprovar."]}):null})()]}),e.jsxs("div",{className:"flex gap-2 ml-4",children:[e.jsx(d,{onClick:()=>Y(a),disabled:"approved"===s.status||q,variant:"approved"===s.status?"default":"outline",size:"sm",children:"approved"===s.status?"✅ Aprovado":"Aprovar"}),e.jsx(d,{onClick:()=>Z(a),disabled:"rejected"===s.status||q,variant:"rejected"===s.status?"destructive":"outline",size:"sm",children:"rejected"===s.status?"❌ Rejeitado":"Rejeitar"})]})]})},`commercial-${a}`)))})})]}),P.length>0&&e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{children:"Revisão do Lote Processado"}),e.jsx(c,{children:"Revise e aprove/rejeite as melhorias sugeridas pela IA"}),e.jsxs("div",{className:"flex gap-2 pt-4",children:[e.jsxs(d,{onClick:Q,disabled:q||0===P.filter((e=>!e.improvement?.is_invalid_medication)).length,variant:"default",size:"sm",children:["✅ Aprovar Todos Válidos (",P.filter((e=>!e.improvement?.is_invalid_medication&&"approved"!==e.status)).length,")"]}),e.jsx(d,{onClick:W,disabled:q,variant:"outline",size:"sm",children:"❌ Rejeitar Todos"})]})]}),e.jsx(l,{children:e.jsx("div",{className:"space-y-4",children:P.map(((s,r)=>e.jsxs("div",{className:"border rounded-lg p-4 space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Nome Original:"}),e.jsx("p",{className:"text-sm",children:s.original_name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Nome Melhorado:"}),e.jsx("p",{className:"text-sm font-semibold text-green-700",children:s.improved_name})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Classe Terapêutica:"}),e.jsxs("div",{className:"text-sm flex items-center gap-2",children:[e.jsx(g,{variant:s.improvement?.is_invalid_medication?"destructive":"outline",children:s.therapeutic_class_number}),e.jsx("span",{className:s.improvement?.is_invalid_medication?"text-red-600 font-medium":"",children:s.therapeutic_class}),s.improvement?.is_invalid_medication&&e.jsx(g,{variant:"destructive",className:"text-xs",children:"⚠️ INVÁLIDO"})]})]})]})}),e.jsx("div",{className:"flex gap-2 ml-4",children:s.improvement?.is_invalid_medication?e.jsx(d,{size:"sm",variant:"destructive",onClick:()=>(async e=>{const s=P[e];if(s.improvement?.is_invalid_medication)if(P[e]){K("info",`🗑️ INICIANDO deleção: "${s.original_name}" (ID: ${s.id})`);try{const r=await async function(e,s){try{s?.(`🗑️ Deletando princípio ativo inválido (ID: ${e})...`);const{data:i,error:t}=await a.from("active_ingredients").select("id, name").eq("id",e).single();if(t||!i){if("PGRST116"===t?.code)return s?.("✅ Princípio ativo já foi deletado anteriormente"),{success:!0};throw new Error(`Princípio ativo não encontrado: ${t?.message}`)}s?.(`📋 Encontrado: "${i.name}"`);const{data:n,error:o}=await a.from("active_ingredient_therapeutic_classes").delete().eq("active_ingredient_id",e).select();s?.(o?`⚠️ Erro ao remover relacionamentos com classes: ${o.message}`:`✅ Removidos ${n?.length||0} relacionamentos com classes`);const{data:c,error:l}=await a.from("presentation_active_ingredients").delete().eq("active_ingredient_id",e).select();s?.(l?`⚠️ Erro ao remover relacionamentos com apresentações: ${l.message}`:`✅ Removidos ${c?.length||0} relacionamentos com apresentações`);const{data:d}=await a.from("active_ingredient_therapeutic_classes").select("id").eq("active_ingredient_id",e);d&&d.length>0&&s?.(`⚠️ Ainda existem ${d.length} relacionamentos com classes`);const{data:m}=await a.from("presentation_active_ingredients").select("id").eq("active_ingredient_id",e);if(m&&m.length>0){s?.(`⚠️ Ainda existem ${m.length} relacionamentos com apresentações`);const{error:r}=await a.from("presentation_active_ingredients").delete().eq("active_ingredient_id",e)}try{const{data:r,error:i}=await a.rpc("delete_active_ingredient_by_id",{ingredient_id:e});if(i){const{data:r,error:i}=await a.from("active_ingredients").delete().eq("id",e).select();if(i){if(s?.(`❌ Erro detalhado: ${i.message} (Código: ${i.code})`),"PGRST116"!==i.code&&!i.message.includes("RLS"))throw i;{s?.("🔄 Contornando políticas RLS...");const{error:r}=await a.from("active_ingredients").delete().eq("id",e);if(r)throw r;s?.("✅ Deleção bem-sucedida via contorno RLS")}}else if(r&&0!==r.length)s?.("✅ Deleção bem-sucedida");else{const{data:r}=await a.from("active_ingredients").select("id, name").eq("id",e).single();if(r)throw s?.(`❌ O princípio ativo "${r.name}" ainda existe no banco`),new Error(`Princípio ativo "${r.name}" não pôde ser deletado - pode estar protegido por políticas RLS ou outras restrições`);s?.("✅ Princípio ativo deletado com sucesso")}}else s?.("✅ Deleção bem-sucedida via RPC")}catch(r){throw r}return s?.(`✅ Princípio ativo "${i.name}" deletado com sucesso`),{success:!0}}catch(r){const e=r instanceof Error?r.message:"Erro desconhecido";return s?.(`❌ Erro ao deletar: ${e}`),{success:!1,error:e}}}(s.id,(e=>{K("info",e)}));r.success?(S((s=>s.filter(((s,a)=>a!==e)))),K("success",`✅ Princípio ativo "${s.original_name}" deletado com sucesso`),await U(),setTimeout((async()=>{await U(),K("info",`🔄 Dados sincronizados - "${s.original_name}" removido permanentemente`)}),1e3)):K("error",`❌ Erro ao deletar: ${r.error}`)}catch(r){K("error",`❌ Erro ao deletar: ${r instanceof Error?r.message:"Erro desconhecido"}`)}}else K("info","Item já foi removido da lista");else K("error","Este item não está marcado como medicamento inválido")})(r),disabled:q,children:"🗑️ Deletar Inválido"}):e.jsxs(e.Fragment,{children:[e.jsx(d,{size:"sm",variant:"approved"===s.status?"default":"outline",onClick:()=>Y(r),disabled:"approved"===s.status,children:"✅ Aprovar"}),e.jsx(d,{size:"sm",variant:"rejected"===s.status?"destructive":"outline",onClick:()=>Z(r),disabled:"rejected"===s.status,children:"❌ Rejeitar"})]})})]}),"pending"!==s.status&&e.jsx("div",{className:"pt-2 border-t",children:e.jsx(g,{variant:"approved"===s.status?"default":"destructive",children:"approved"===s.status?"✅ Aprovado":"❌ Rejeitado"})})]},s.id)))})})]}),e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{children:"Logs do Processamento"}),e.jsx(c,{children:"Acompanhe o progresso em tempo real"})]}),e.jsx(l,{children:e.jsx(v,{className:"h-[300px]",children:e.jsxs("div",{className:"space-y-2",children:[C.map(((s,a)=>e.jsxs("div",{className:"flex items-start gap-2 p-2 rounded border",children:[J(s.type),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm ${X(s.type)}`,children:s.message}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.timestamp.toLocaleTimeString()})]})]},a))),0===C.length&&e.jsx("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Nenhum log ainda. Inicie o processamento para ver os logs."})]})})})]}),O&&O.errors.length>0&&e.jsxs(t,{children:[e.jsxs(n,{children:[e.jsx(o,{className:"text-red-600",children:"Erros na Aplicação"}),e.jsx(c,{children:"Itens que falharam durante a aplicação das melhorias"})]}),e.jsx(l,{children:e.jsx(v,{className:"h-[200px]",children:e.jsx("div",{className:"space-y-2",children:O.errors.map(((s,a)=>e.jsxs("div",{className:"p-3 border border-red-200 rounded bg-red-50",children:[e.jsx("p",{className:"font-medium text-red-800",children:s.name}),e.jsx("p",{className:"text-sm text-red-600",children:s.error})]},a)))})})})]})]})};export{A as default};
