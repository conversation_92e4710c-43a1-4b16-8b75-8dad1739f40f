import{j as e}from"./radix-core-6kBL75b5.js";import{L as a}from"./router-BAzpOxbo.js";import{c as r,an as s,k as t,a5 as i,ad as l,ae as d,af as n,ag as o,ai as c,R as m,ak as x,aL as u,aH as p,az as h,B as b,l as g,aZ as j,P as f,ay as v,aa as N,a7 as k}from"./index-CrSshpOb.js";import y from"./Footer-ClHMSbsi.js";import{r as w}from"./critical-DVX9Inzy.js";import{S as C}from"./switch-Blnykg1v.js";import{S}from"./scale-zgGFIpV3.js";import{S as A,c as _}from"./supplementationCalculator-rBZFyqUM.js";import{C as P,a as W}from"./childcareSEOData-BzPfjXOt.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";import"./accordion-R4Om_XcG.js";
/**
 * @license lucide-react v0.451.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=r("Droplet",[["path",{d:"M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z",key:"c7niix"}]]),B=[{id:"prematurity",label:"Prematuridade",description:"Nascimento antes de 37 semanas"},{id:"low_birth_weight",label:"Baixo peso ao nascer",description:"Peso ao nascer < 2500g"},{id:"poor_iron_diet",label:"Alimentação pobre em ferro",description:"Introdução alimentar inadequada"},{id:"exclusive_breastfeeding_gt_6m_without_supplement",label:"AME prolongado sem suplementação",description:"Além de 6 meses sem ferro"},{id:"multiple_gestation",label:"Gestação múltipla",description:"Gêmeos, trigêmeos, etc."},{id:"maternal_anemia",label:"Anemia materna",description:"Durante gestação ou lactação"},{id:"frequent_infections",label:"Infecções frequentes",description:"Processos infecciosos recorrentes"},{id:"early_cow_milk_exposure",label:"Leite de vaca precoce",description:"Antes dos 12 meses"},{id:"low_socioeconomic_status",label:"Baixo nível socioeconômico",description:"Vulnerabilidade social"}],I=({onCalculate:a})=>{const[r,j]=w.useState({ageInDays:"",currentWeight:"",birthWeight:"",maturity:"Term",exclusiveBreastfeeding:!1}),[f,v]=w.useState([]),[N,k]=w.useState(!1);return e.jsxs("form",{onSubmit:e=>{e.preventDefault();const s={ageInDays:parseInt(r.ageInDays),currentWeight:parseInt(r.currentWeight),birthWeight:parseInt(r.birthWeight),maturity:r.maturity,exclusiveBreastfeeding:r.exclusiveBreastfeeding,riskFactors:f};a(s)},className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(s,{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4"}),"Idade (dias)"]}),e.jsx(i,{type:"number",placeholder:"Em dias",value:r.ageInDays,onChange:e=>j({...r,ageInDays:e.target.value}),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(s,{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4"}),"Peso Atual (g)"]}),e.jsx(i,{type:"number",placeholder:"Em gramas",value:r.currentWeight,onChange:e=>j({...r,currentWeight:e.target.value}),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(s,{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4"}),"Peso ao Nascer (g)"]}),e.jsx(i,{type:"number",placeholder:"Em gramas",value:r.birthWeight,onChange:e=>j({...r,birthWeight:e.target.value}),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(s,{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4"}),"Maturidade"]}),e.jsxs(l,{value:r.maturity,onValueChange:e=>j({...r,maturity:e}),children:[e.jsx(d,{children:e.jsx(n,{placeholder:"Selecione a maturidade"})}),e.jsxs(o,{children:[e.jsx(c,{value:"Term",children:"A termo"}),e.jsx(c,{value:"Pre-term",children:"Prematuro"})]})]})]}),e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(s,{htmlFor:"exclusiveBreastfeeding",className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 text-purple-500 dark:text-purple-400"}),e.jsx("span",{className:"text-gray-800 dark:text-gray-200",children:"Aleitamento Materno Exclusivo"})]}),e.jsx(C,{id:"exclusiveBreastfeeding",checked:r.exclusiveBreastfeeding,onCheckedChange:e=>j({...r,exclusiveBreastfeeding:e})})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>k(!N),children:[e.jsxs(s,{className:"flex items-center gap-2 cursor-pointer",children:[e.jsx(x,{className:"h-4 w-4 text-yellow-500 dark:text-yellow-400"}),e.jsxs("span",{className:"text-gray-800 dark:text-gray-200",children:["Fatores de Risco ",f.length>0&&`(${f.length} selecionados)`]})]}),N?e.jsx(u,{className:"h-4 w-4 text-gray-500"}):e.jsx(p,{className:"h-4 w-4 text-gray-500"})]}),N&&e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg",children:B.map((a=>e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx(h,{id:a.id,checked:f.includes(a.id),onCheckedChange:e=>((e,a)=>{v(a?[...f,e]:f.filter((a=>a!==e)))})(a.id,e),className:"mt-1"}),e.jsxs("div",{className:"grid gap-1.5 leading-none",children:[e.jsx(s,{htmlFor:a.id,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer",children:a.label}),a.description&&e.jsx("p",{className:"text-xs text-muted-foreground",children:a.description})]})]},a.id)))})]})]})})]}),e.jsxs(b,{type:"submit",className:"w-full",children:[e.jsx(g,{className:"mr-2 h-4 w-4"}),"Calcular Suplementação"]})]})},z=[{name:"Sulfato ferroso gotas",concentration:1.25,note:"Em geral disponível no Posto de Saúde"},{name:"Combiron gotas",concentration:2.5,note:"glicinato"},{name:"Noripurum gotas",concentration:2.5,note:"polimaltosado"},{name:"Neutrofer gotas",concentration:2.5,note:"glicinato"},{name:"Endofer gotas",concentration:2.5,note:"polimaltosado"}],D=({result:a,isVisible:r,patientWeight:s,isEndemicArea:t,onEndemicAreaChange:i})=>{const[l,d]=w.useState(!1),[n,o]=w.useState(z[0]);if(!r)return e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-center space-y-4 p-8",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-blue-500 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Preencha os campos com as informações necessárias para calcularmos a suplementação adequada para a criança"})]})});const c=(e=>{const a=e.match(/(\d+(?:\.\d+)?)\s*mg.*?ferro.*?dia/i);return a?parseFloat(a[1]):0})(a.iron),x=e=>0===c?0:Math.round(c/e.concentration*10)/10;return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(m,{className:"p-4 bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800/50",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(j,{className:"h-5 w-5 text-purple-600 dark:text-purple-400"}),e.jsx("h3",{className:"font-semibold text-purple-700 dark:text-purple-300",children:"Vitamina D"})]}),e.jsx("p",{className:"text-purple-600 dark:text-purple-200/90",children:a.vitaminD})]}),e.jsxs(m,{className:"p-4 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),e.jsx("h3",{className:"font-semibold text-red-700 dark:text-red-300",children:"Ferro"})]}),c>0&&e.jsxs(b,{variant:"outline",size:"sm",onClick:()=>d(!l),className:"text-red-600 border-red-200 hover:bg-red-100 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/20",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),l?"Ocultar":"Prescrição",l?e.jsx(u,{className:"h-4 w-4 ml-2"}):e.jsx(p,{className:"h-4 w-4 ml-2"})]})]}),e.jsx("p",{className:"text-red-600 dark:text-red-200/90 mb-3",children:a.iron}),l&&c>0&&e.jsxs("div",{className:"mt-4 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-red-200 dark:border-red-700",children:[e.jsxs("h4",{className:"font-semibold text-red-700 dark:text-red-300 mb-3 flex items-center gap-2",children:[e.jsx(g,{className:"h-4 w-4"}),"Cálculo de Prescrição (",c," mg/dia)"]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-red-600 dark:text-red-300 mb-2",children:"Selecione o medicamento:"}),e.jsx("div",{className:"grid gap-2",children:z.map(((a,r)=>e.jsx("button",{onClick:()=>o(a),className:"p-3 text-left rounded-lg border transition-all "+(n.name===a.name?"border-red-400 bg-red-100 dark:bg-red-900/30 dark:border-red-600":"border-gray-200 hover:border-red-300 hover:bg-red-50 dark:border-gray-600 dark:hover:border-red-700 dark:hover:bg-red-900/20"),children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-red-700 dark:text-red-300",children:a.name}),e.jsxs("p",{className:"text-sm text-red-600 dark:text-red-400",children:[a.concentration," mg/gota (",a.note,")"]})]}),e.jsxs(v,{variant:n.name===a.name?"default":"secondary",children:[x(a)," gotas/dia"]})]})},r)))})]}),e.jsxs("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg border border-red-300 dark:border-red-600",children:[e.jsx("h5",{className:"font-semibold text-red-800 dark:text-red-200 mb-2",children:"📋 Prescrição:"}),e.jsxs("p",{className:"text-red-700 dark:text-red-300",children:[e.jsx("strong",{children:n.name}),e.jsx("br",{}),e.jsxs("strong",{children:[x(n)," gotas por dia"]}),e.jsx("br",{}),e.jsxs("span",{className:"text-sm",children:["(",n.concentration," mg/gota × ",x(n)," gotas = ",c," mg ferro elementar/dia)"]})]})]})]})]}),e.jsxs(m,{className:"p-4 bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800/50",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(E,{className:"h-5 w-5 text-amber-600 dark:text-amber-400"}),e.jsx("h3",{className:"font-semibold text-amber-700 dark:text-amber-300",children:"Vitamina A"})]}),a.vitaminA.includes("6 meses")&&null===t&&e.jsxs("div",{className:"mb-4 p-3 bg-amber-100 dark:bg-amber-900/30 rounded-lg border border-amber-200 dark:border-amber-700",children:[e.jsx("p",{className:"text-amber-700 dark:text-amber-300 mb-3 font-medium",children:"A criança vive em área endêmica para deficiência de vitamina A?"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{size:"sm",onClick:()=>i?.(!0),className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Sim"}),e.jsx(b,{size:"sm",variant:"outline",onClick:()=>i?.(!1),className:"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-600 dark:text-amber-300",children:"Não"})]})]}),null!==t?e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(v,{variant:t?"default":"secondary",children:t?"Área Endêmica":"Área Não-Endêmica"}),e.jsx(b,{size:"sm",variant:"ghost",onClick:()=>i?.(null),className:"text-amber-600 hover:text-amber-700 dark:text-amber-400",children:"Reavaliar"})]}),e.jsx("p",{className:"text-amber-600 dark:text-amber-200/90",children:t?"Suplementação de Vitamina A indicada: 100.000 UI (6-11 meses) ou 200.000 UI (12-59 meses) a cada 6 meses.":"Suplementação de Vitamina A não está rotineiramente indicada em áreas não-endêmicas, exceto em casos de risco aumentado."})]}):e.jsx("p",{className:"text-amber-600 dark:text-amber-200/90",children:a.vitaminA})]})]})},q=()=>{const r=W["suplementacao-infantil"],[s,t]=w.useState(null),[i,l]=w.useState(),[d,n]=w.useState(null);return e.jsxs("div",{className:"min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900",children:[e.jsx(P,{...r}),e.jsx(N,{}),e.jsxs("main",{className:"flex-1 container mx-auto px-4 py-12",children:[e.jsxs(a,{to:"/puericultura",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 hover:translate-x-1 transform duration-200 dark:text-blue-400 dark:hover:text-blue-300",children:[e.jsx(k,{className:"h-5 w-5"}),e.jsx("span",{children:"Voltar para Puericultura"})]}),e.jsxs("div",{className:"text-center space-y-4 mb-12",children:[e.jsxs("div",{className:"relative inline-block perspective preserve-3d group",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-400 relative z-10 transform transition-transform duration-300 group-hover:scale-105",children:"Suplementação"}),e.jsx("div",{className:"absolute -inset-4 bg-blue-100/50 blur-xl rounded-full -z-10 group-hover:blur-2xl transition-all duration-300 dark:bg-blue-900/30"})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 max-w-2xl mx-auto animate-fade-in-up",children:"Descubra os nutrientes recomendados para seu paciente."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto mb-16",children:[e.jsx("div",{className:"space-y-6 glass-card backdrop-blur-sm p-6 rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl bg-white/80 dark:bg-slate-800/80 dark:border dark:border-slate-700",children:e.jsx(I,{onCalculate:e=>{const a=_(e);t(a),l(e.currentWeight)}})}),e.jsx("div",{className:"space-y-4",children:s?e.jsx(D,{result:s,isVisible:!0,patientWeight:i,isEndemicArea:d,onEndemicAreaChange:n}):e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-center space-y-4 p-8 glass-card backdrop-blur-sm rounded-lg animate-fade-in-up bg-white/80 dark:bg-slate-800/80 dark:border dark:border-slate-700",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900 flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-blue-500 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Preencha os campos com as informações necessárias para calcularmos a suplementação adequada para a criança"})]})})})]}),e.jsx("div",{className:"max-w-4xl mx-auto transform transition-all duration-300 hover:translate-y-[-4px]",children:e.jsx(A,{})})]}),e.jsx(y,{})]})};export{q as default};
