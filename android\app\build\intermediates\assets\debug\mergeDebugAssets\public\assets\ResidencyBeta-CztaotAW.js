import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{aa as s,R as r,W as t,Z as i,U as o,b2 as l,an as d,a5 as c,ad as n,ae as m,af as x,ag as p,ai as u,B as j}from"./index-DwBJcqzE.js";import h from"./Footer-DXia2SVU.js";import{C as g}from"./clock-uMZQcnNP.js";import{T as v}from"./target-D2iN3abh.js";import{B as b}from"./book-open-C1lgvyyJ.js";import{U as f}from"./users-Bktio_NS.js";import{A as y}from"./award-CtslkwK6.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-BC_TYgH3.js";import"./rocket-7eidEF9E.js";import"./zap-BpuRFf_b.js";import"./star-BwJL-OtG.js";import"./circle-help-OcogOqeH.js";import"./instagram-Dc9Ip0W1.js";const N=()=>{const[N,k]=a.useState({name:"",email:"",phone:"",currentStatus:"",medicalSchoolYear:"",targetResidency:"",studyPreferences:[],timeAvailable:"",howFoundPedbook:""}),w=[{icon:v,title:"Simulados Personalizados",description:"Questões adaptadas ao seu nível e especialidade desejada"},{icon:b,title:"Conteúdo Exclusivo",description:"Material atualizado com as últimas diretrizes e protocolos"},{icon:f,title:"Mentoria Especializada",description:"Acompanhamento com residentes aprovados e especialistas"},{icon:y,title:"Ranking Nacional",description:"Compare seu desempenho com outros candidatos"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-b from-blue-50 to-white dark:from-slate-900 dark:to-slate-800",children:[e.jsx(s,{}),e.jsxs("main",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-full text-sm font-medium mb-6",children:[e.jsx(g,{className:"h-4 w-4"}),"Lançamento em breve - Seja um dos primeiros!"]}),e.jsx("h1",{className:"text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text mb-6",children:"Plataforma de Residência"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8",children:"A mais completa plataforma de preparação para residência médica. Desenvolvida por quem já passou e sabe o que funciona."}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400",children:[e.jsx("span",{children:"✅ +10.000 questões atualizadas"}),e.jsx("span",{children:"✅ Simulados personalizados"}),e.jsx("span",{children:"✅ Mentoria especializada"}),e.jsx("span",{children:"✅ Conteúdo exclusivo"})]})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:w.map(((a,s)=>e.jsxs(r,{className:"text-center hover:shadow-lg transition-shadow",children:[e.jsxs(t,{children:[e.jsx(a.icon,{className:"h-12 w-12 mx-auto text-blue-600 dark:text-blue-400 mb-4"}),e.jsx(i,{className:"text-lg",children:a.title})]}),e.jsx(o,{children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:a.description})})]},s)))}),e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs(r,{className:"shadow-xl",children:[e.jsxs(t,{className:"text-center",children:[e.jsxs(i,{className:"text-2xl flex items-center justify-center gap-2",children:[e.jsx(l,{className:"h-6 w-6"}),"Cadastre-se para o Beta"]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Seja um dos primeiros a testar nossa plataforma exclusiva"})]}),e.jsxs(o,{className:"space-y-6",children:[e.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:[e.jsx("h3",{className:"font-semibold text-green-800 dark:text-green-300 mb-3",children:"🎁 Benefícios exclusivos para beta testers:"}),e.jsx("ul",{className:"space-y-1 text-sm",children:["🎯 Acesso antecipado GRATUITO por 3 meses","📚 E-book: 'Guia Completo de Residência Médica 2024'","🏆 Simulados ilimitados personalizados","💡 Mentoria individual com aprovados","📊 Relatórios detalhados de performance","🎪 Webinars exclusivos mensais"].map(((a,s)=>e.jsx("li",{className:"text-green-700 dark:text-green-300",children:a},s)))})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"name",children:"Nome completo *"}),e.jsx(c,{id:"name",placeholder:"Seu nome completo",value:N.name,onChange:e=>k({...N,name:e.target.value})})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"email",children:"E-mail *"}),e.jsx(c,{id:"email",type:"email",placeholder:"<EMAIL>",value:N.email,onChange:e=>k({...N,email:e.target.value})})]})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"status",children:"Situação atual *"}),e.jsxs(n,{value:N.currentStatus,onValueChange:e=>k({...N,currentStatus:e}),children:[e.jsx(m,{children:e.jsx(x,{placeholder:"Selecione sua situação atual"})}),e.jsxs(p,{children:[e.jsx(u,{value:"student",children:"Estudante de medicina"}),e.jsx(u,{value:"recent_graduate",children:"Recém-formado"}),e.jsx(u,{value:"resident",children:"Já sou residente"}),e.jsx(u,{value:"graduated",children:"Médico formado"})]})]})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"target",children:"Especialidade de interesse *"}),e.jsxs(n,{value:N.targetResidency,onValueChange:e=>k({...N,targetResidency:e}),children:[e.jsx(m,{children:e.jsx(x,{placeholder:"Qual especialidade você quer?"})}),e.jsxs(p,{children:[e.jsx(u,{value:"pediatrics",children:"Pediatria"}),e.jsx(u,{value:"internal_medicine",children:"Clínica Médica"}),e.jsx(u,{value:"surgery",children:"Cirurgia Geral"}),e.jsx(u,{value:"gynecology",children:"Ginecologia e Obstetrícia"}),e.jsx(u,{value:"psychiatry",children:"Psiquiatria"}),e.jsx(u,{value:"other",children:"Outra especialidade"})]})]})]}),e.jsx(j,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3",children:"🚀 Quero participar do Beta!"}),e.jsx("p",{className:"text-xs text-center text-gray-500 dark:text-gray-400",children:"Ao se cadastrar, você concorda em receber atualizações sobre a plataforma. Seus dados estão seguros conosco."})]})]})})]}),e.jsx(h,{})]})};export{N as default};
