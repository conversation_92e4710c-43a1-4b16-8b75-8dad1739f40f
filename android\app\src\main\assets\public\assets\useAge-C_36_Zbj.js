import{a as e,u as t}from"./query-vendor-B-7l6Nb3.js";import{r as a}from"./critical-DVX9Inzy.js";const r="patient-age",s=12,o=()=>{const o=e(),[n,l]=a.useState(null),{data:g=s}=t({queryKey:[r],queryFn:()=>{const e=localStorage.getItem(r);return e?parseFloat(e):s},staleTime:1/0}),u=e=>{l(null),localStorage.setItem(r,e.toString()),o.setQueryData([r],e)};return{age:g,setAge:u,displayAge:n??g,setTempAge:e=>{l(e)},ageInMonths:g,commitAge:u}};export{o as u};
