import{r as n}from"./critical-DVX9Inzy.js";const e=({checkInterval:e=30,enabled:t=!0})=>{const[r,o]=n.useState(!1);return n.useEffect((()=>{if(!t)return;let n,a=null;const i=async()=>{if(!r){o(!0);try{const n=await(async()=>{try{const n=await fetch("/",{cache:"no-cache",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(!n.ok)return null;const e=await n.text(),t=e.match(/src="[^"]*\.js[^"]*"/g)||[],r=e.match(/href="[^"]*\.css[^"]*"/g)||[],o=[...t,...r].join("");let a=0;for(let i=0;i<o.length;i++)a=(a<<5)-a+o.charCodeAt(i),a&=a;return a.toString()}catch(n){return null}})();if(null===n)return void o(!1);if(null===a)return a=n,void o(!1);if(n!==a){const n=document.createElement("div");n.style.cssText="\n            position: fixed;\n            bottom: 20px;\n            right: 20px;\n            background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n            color: white;\n            padding: 16px 20px;\n            border-radius: 12px;\n            font-size: 14px;\n            z-index: 10000;\n            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);\n            max-width: 320px;\n            animation: slideInUp 0.4s ease-out;\n            backdrop-filter: blur(10px);\n            border: 1px solid rgba(255, 255, 255, 0.1);\n          ",n.innerHTML='\n            <div style="display: flex; align-items: center; gap: 12px;">\n              <div style="\n                width: 40px; \n                height: 40px; \n                background: rgba(255, 255, 255, 0.2); \n                border-radius: 50%; \n                display: flex; \n                align-items: center; \n                justify-content: center;\n                font-size: 18px;\n              ">🚀</div>\n              <div style="flex: 1;">\n                <div style="font-weight: 600; margin-bottom: 4px;">Nova versão disponível!</div>\n                <div style="font-size: 12px; opacity: 0.9;">Atualize para ter acesso às melhorias mais recentes.</div>\n              </div>\n            </div>\n            <div style="margin-top: 12px; display: flex; gap: 8px;">\n              <button onclick="window.location.reload()" style="\n                background: rgba(255, 255, 255, 0.9);\n                color: #1d4ed8;\n                border: none;\n                padding: 8px 16px;\n                border-radius: 6px;\n                cursor: pointer;\n                font-size: 12px;\n                font-weight: 600;\n                flex: 1;\n              ">Atualizar Agora</button>\n              <button onclick="this.parentElement.parentElement.remove()" style="\n                background: rgba(255, 255, 255, 0.1);\n                color: white;\n                border: 1px solid rgba(255, 255, 255, 0.2);\n                padding: 8px 12px;\n                border-radius: 6px;\n                cursor: pointer;\n                font-size: 12px;\n              ">Depois</button>\n            </div>\n          ';const e=document.createElement("style");e.textContent="\n            @keyframes slideInUp {\n              from { \n                transform: translateY(100px); \n                opacity: 0; \n              }\n              to { \n                transform: translateY(0); \n                opacity: 1; \n              }\n            }\n          ",document.head.appendChild(e),document.body.appendChild(n),setTimeout((()=>{n.parentNode&&(n.style.animation="slideInUp 0.4s ease-out reverse",setTimeout((()=>{n.remove(),e.remove()}),400))}),3e4)}}catch(n){}finally{o(!1)}}},s=setTimeout((()=>{i()}),5e3);return n=setInterval((()=>{i()}),60*e*1e3),()=>{clearTimeout(s),clearInterval(n)}}),[e,t,r]),null};export{e as ChunkVersionChecker,e as default};
