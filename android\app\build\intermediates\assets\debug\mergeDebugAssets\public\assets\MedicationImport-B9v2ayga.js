import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{R as s,W as t,Z as o,$ as i,U as r,B as n,L as c,ab as d,T as l,ao as m,Y as p,s as u}from"./index-DwykrzWu.js";import{A as g,b as _}from"./alert-47AuHV0Y.js";import{J as f}from"./index-k36xMONP.js";import{T as h}from"./trash-2-DV0_UZFC.js";import{U as $}from"./upload-CiyvbGrG.js";import{D as x}from"./database-C4RHCG06.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const v=()=>{const[v,j]=a.useState(""),[N,b]=a.useState(!1),[w,E]=a.useState(!1),[A,y]=a.useState(!1),[C,S]=a.useState(null),[D,O]=a.useState([]),I=[],M=(e,a=!1)=>{const s=`[${(new Date).toLocaleTimeString()}] ${e}`;a?O((e=>[...e,s])):(I.push(s),I.length>=10&&(O((e=>[...e,...I])),I.length=0))},P=()=>{I.length>0&&(O((e=>[...e,...I])),I.length=0)},T=e=>{const a=new Map;return e.forEach(((e,s)=>{const t=`${e.forma_farmaceutica||"null"}_${e.concentracao||"null"}_${e.tarja||"null"}_${e.receituario||"null"}_${e.controlado||!1}_${e.alto_custo||!1}`;if(a.has(t)){const o=a.get(t);1!==s&&o._merged||(M(`        🔄 Mesclando apresentação duplicada: ${e.forma_farmaceutica} - ${e.concentracao}`),o._merged=!0);const i=o.classificacao_terapeutica||[],r=e.classificacao_terapeutica||[],n=[...new Set([...i,...r])];o.classificacao_terapeutica=n;const c=o.classe||"",d=e.classe||"";if(d&&c!==d){const e=new Set([c,d].filter((e=>e)));o.classe=Array.from(e).join(", ")}else!c&&d&&(o.classe=d);if(o._logged&&n.length===(o._lastClassCount||0)||(M(`        📋 Classes individuais: "${o.classe}"`),M(`        📋 Classes terapêuticas: [${n.join(", ")}]`),M(`        ✅ Classes mescladas: ${n.length} classe(s) terapêutica(s)`),o._logged=!0,o._lastClassCount=n.length),e.principios_ativos&&e.principios_ativos.length>0&&(o.principios_ativos=o.principios_ativos||e.principios_ativos),e.via_administracao&&e.via_administracao.length>0){const a=o.via_administracao||[],s=e.via_administracao||[];o.via_administracao=[...new Set([...a,...s])]}if(e.posologias&&e.posologias.length>0){const a=o.posologias||[],s=e.posologias||[];o.posologias=[...a,...s]}}else a.set(t,{...e})})),Array.from(a.values()).map((e=>{const{_merged:a,_logged:s,_lastClassCount:t,...o}=e;return o}))};return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Importar Medicamentos"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Importar nova estrutura de medicamentos com consolidação automática"})]})}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[e.jsxs(s,{children:[e.jsxs(t,{children:[e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5 text-red-500"}),"Limpar Dados Atuais"]}),e.jsx(i,{children:"Remove todos os medicamentos e estruturas relacionadas do banco de dados"})]}),e.jsx(r,{children:e.jsx(n,{variant:"destructive",onClick:async()=>{E(!0),M("🗑️ Iniciando limpeza dos dados atuais...");try{const e=["presentation_administration_routes","presentation_therapeutic_classes","presentation_active_ingredients","drug_presentations","drug_active_ingredients","drug_therapeutic_classes","drug_administration_routes","drugs"];for(const a of e){M(`🧹 Limpando tabela: ${a}`);const{error:e}=await u.from(a).delete().neq("id","00000000-0000-0000-0000-000000000000");if(e)throw M(`❌ Erro ao limpar ${a}: ${e.message}`),e}M("✅ Limpeza concluída com sucesso!"),f.success("Dados atuais removidos com sucesso")}catch(e){M(`❌ Erro na limpeza: ${e.message}`),f.error("Erro ao limpar dados atuais")}finally{E(!1)}},disabled:w||N,className:"w-full",children:w?e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"h-4 w-4 mr-2 animate-spin"}),"Limpando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Limpar Dados"]})})})]}),e.jsxs(s,{children:[e.jsxs(t,{children:[e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5 text-orange-500"}),"Deletar Dados Recentes"]}),e.jsx(i,{children:"Remove medicamentos adicionados nos últimos 30 minutos"})]}),e.jsx(r,{children:e.jsx(n,{variant:"outline",onClick:async()=>{y(!0),M("🗑️ Iniciando deleção de dados dos últimos 30 minutos...");try{const e=new Date;e.setMinutes(e.getMinutes()-30);const a=e.toISOString();M(`⏰ Deletando dados criados após: ${e.toLocaleString("pt-BR")}`);const{data:s}=await u.from("drugs").select("id, brand_name, created_at").gte("created_at",a);if(!s||0===s.length)return M("ℹ️ Nenhum medicamento encontrado nos últimos 30 minutos"),void f.success("Nenhum dado recente encontrado para deletar");M(`📊 Encontrados ${s.length} medicamento(s) para deletar:`),s.forEach(((e,a)=>{M(`  ${a+1}. ${e.brand_name} (${new Date(e.created_at).toLocaleString("pt-BR")})`)}));const t=["presentation_administration_routes","presentation_therapeutic_classes","presentation_active_ingredients","drug_presentations","drug_active_ingredients","drug_therapeutic_classes","drug_administration_routes","drugs"];let o=0;for(const i of t){let e;if(M(`🧹 Deletando registros recentes da tabela: ${i}`),"drugs"===i)e=u.from(i).delete().gte("created_at",a);else{const a=s.map((e=>e.id));if(i.startsWith("drug_"))e=u.from(i).delete().in("drug_id",a);else{if(!i.startsWith("presentation_")){M(`  ⚠️ Tabela ${i} não reconhecida, pulando...`);continue}{const{data:s}=await u.from("drug_presentations").select("id").in("drug_id",a);if(!(s&&s.length>0)){M(`  ⚠️ Nenhuma apresentação encontrada para deletar em ${i}`);continue}{const a=s.map((e=>e.id));e=u.from(i).delete().in("presentation_id",a)}}}}const{data:t,error:r,count:n}=await e;if(r)throw M(`❌ Erro ao deletar ${i}: ${r.message}`),r;const c=n||0;o+=c,M(`  ✅ ${c} registro(s) deletado(s) de ${i}`)}M(`🎉 Deleção concluída! Total de ${o} registro(s) deletado(s)`),M(`📊 ${s.length} medicamento(s) e suas estruturas relacionadas foram removidos`),f.success(`${s.length} medicamentos recentes deletados com sucesso!`)}catch(e){M(`❌ Erro geral na deleção: ${e.message}`),f.error("Erro na deleção de dados recentes")}finally{y(!1)}},disabled:A||N||w,className:"w-full border-orange-500 text-orange-500 hover:bg-orange-50",children:A?e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"h-4 w-4 mr-2 animate-spin"}),"Deletando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Deletar Recentes"]})})})]}),e.jsxs(s,{children:[e.jsxs(t,{children:[e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-green-500"}),"Importar Medicamentos"]}),e.jsx(i,{children:"Importa a nova estrutura de medicamentos com consolidação automática"})]}),e.jsx(r,{children:e.jsx(n,{onClick:async()=>{if(v.trim()){b(!0),S(null),O([]),M("🚀 Iniciando importação otimizada de medicamentos...");try{let o;try{o=JSON.parse(v),M(`📊 JSON parseado com sucesso: ${o.length} medicamentos encontrados`)}catch(a){return M("❌ Erro ao fazer parse do JSON"),void f.error("JSON inválido")}const i=new Map,r=new Map,n=new Map,c=new Map;M("🔄 Carregando dados existentes para cache...");const[d,l,m,p]=await Promise.all([u.from("drug_dosage_forms").select("id, name"),u.from("active_ingredients").select("id, name"),u.from("therapeutic_classes").select("id, name"),u.from("administration_routes").select("id, name")]);d.data?.forEach((e=>i.set(e.name,e.id))),l.data?.forEach((e=>r.set(e.name,e.id))),m.data?.forEach((e=>n.set(e.name,e.id))),p.data?.forEach((e=>c.set(e.name,e.id))),M(`✅ Cache carregado: ${i.size} formas, ${r.size} princípios, ${n.size} classes, ${c.size} vias`);const g={totalMedications:o.length,processedMedications:0,consolidatedGroups:o.length,createdPresentations:0,errors:[],warnings:[]},_=new Map;o.forEach((e=>{const a=e.nome_comercial;_.has(a)||_.set(a,[]),_.get(a).push(e)})),M(`🔗 Medicamentos agrupados: ${_.size} medicamentos únicos com ${o.length} apresentações`),g.consolidatedGroups=_.size;const h=50,$=Array.from(_.entries()),x=[];for(let e=0;e<$.length;e+=h)x.push($.slice(e,e+h));M(`📦 Dividido em ${x.length} lotes OTIMIZADOS de ${h} medicamentos`,!0);const j=async(e,a,s,t={})=>{if(e.has(s))return e.get(s);const o={name:s,...t},{data:i,error:r}=await u.from(a).insert(o).select("id").single();return r?(M(`⚠️ Erro ao criar ${a}: ${r.message}`),null):(e.set(s,i.id),i.id)},N=async(e,a,s,t="")=>{const o=new Map,i=[];for(const r of s)e.has(r.name)?o.set(r.name,e.get(r.name)):i.push({name:r.name,...r.additionalFields});if(i.length>0){const{data:s,error:r}=await u.from(a).insert(i).select("id, name");if(r)M(`${t}❌ Erro ao criar ${a} em lote: ${r.message}`);else if(s){for(const a of s)e.set(a.name,a.id),o.set(a.name,a.id);M(`${t}✅ ${s.length} ${a} criados em lote`)}}return o};let b=0;for(let a=0;a<x.length;a++){const o=x[a];M(`📦 Processando lote ${a+1}/${x.length} (${o.length} medicamentos)`);for(const[a,l]of o){try{M(`  📦 Processando: ${a} (${l.length} apresentações)`);const t=T(l);M(`    🔄 Consolidadas para ${t.length} apresentações únicas`);const o=t[0],d=o.principios_ativos&&o.principios_ativos.length>1,m=o.classificacao_terapeutica&&o.classificacao_terapeutica.some((e=>"associacoes"===e.toLowerCase().replace(/[áàâãä]/g,"a").replace(/[éèêë]/g,"e").replace(/[íìîï]/g,"i").replace(/[óòôõö]/g,"o").replace(/[úùûü]/g,"u").replace(/ç/g,"c").replace(/\s+/g," ").trim()));M(`    ${d?"🔗":"💊"} ${d?"Associação":"Princípio único"}: ${o.principios_ativos?.map((e=>e.nome)).join(" + ")||"N/A"}`);const p={brand_name:a,tipo:o.tipo||"white-label",descricao:o.descricao||a,fabricante:o.fabricante||"",composicao:o.composicao||"",titularidade:o.titularidade||null,adult_use:o.uso_em?.adulto||!0,pediatric_use:o.uso_em?.pediatrico||!0,pregnancy_info:"string"==typeof o.uso_em?.gestante?o.uso_em.gestante:null,breastfeeding_info:"string"==typeof o.uso_em?.lactante?o.uso_em.lactante:null,patient_instructions:o.recomendacoes_ao_paciente||[],status:o.status||"ativo",is_association:d||m,is_controlled:o.controlado||!1,is_high_cost:o.alto_custo||!1,eans:o.eans||[]},_=e=>e.toLowerCase().replace(/[áàâãä]/g,"a").replace(/[éèêë]/g,"e").replace(/[íìîï]/g,"i").replace(/[óòôõö]/g,"o").replace(/[úùûü]/g,"u").replace(/ç/g,"c").replace(/\s+/g," ").trim();if(!e){var e=new Map;const{data:a}=await u.from("drugs").select("id, brand_name, fabricante");if(a){for(const s of a)e.set(`exact:${s.brand_name}`,s),e.set(`normalized:${_(s.brand_name)}`,s);M(`🚀 Cache de duplicatas carregado: ${a.length} medicamentos`)}}const f=async()=>{let s=e.get(`exact:${a}`);if(s)return M(`    🎯 Encontrado por nome exato (cache): ${s.brand_name}`),s;const t=_(a);if(s=e.get(`normalized:${t}`),s)return M(`    🎯 Encontrado por nome normalizado (cache): ${s.brand_name} → ${a}`),s;if(o.principios_ativos&&o.principios_ativos.length>0){const a=o.principios_ativos[0],t=`dcb:${a.dcb}:${o.concentracao}`;if(s=e.get(t),s)return M(`    🎯 Encontrado por DCB + concentração (cache): ${s.brand_name}`),s;const{data:i}=await u.from("drugs").select("\n                    id,\n                    brand_name,\n                    drug_presentations!inner(strength),\n                    presentation_active_ingredients!inner(\n                      active_ingredients!inner(name, dcb_code)\n                    )\n                  ").eq("presentation_active_ingredients.active_ingredients.dcb_code",a.dcb).limit(5);for(const s of i||[])if(s.drug_presentations.some((e=>e.strength===(o.concentracao||""))))return e.set(t,s),M(`    🎯 Encontrado por DCB + concentração: ${s.brand_name} (DCB: ${a.dcb})`),s}return M(`    ✨ Medicamento novo confirmado: ${a}`),null},h=await f();let $;if(h)M(`    ⚠️ Medicamento já existe, preservando dados existentes: ${a} (ID: ${h.id})`),M("    📊 Dados preservados: vias de administração, relacionamentos existentes"),$=h;else{M("    📝 Criando novo medicamento com dados:"),M(`       • Nome: ${p.brand_name}`),M(`       • Fabricante: ${p.fabricante||"N/A"}`),M(`       • Classe: ${o.classe||"N/A"}`),M(`       • Tipo: ${p.tipo}`),M(`       • Titularidade: ${p.titularidade||"N/A"}`),M("       • Controlado: "+(p.is_controlled?"Sim":"Não")),M(`       • EANs: ${p.eans?.length||0} código(s)`);const{data:e,error:s}=await u.from("drugs").insert(p).select().single();if(s){M(`    ❌ Erro ao criar medicamento ${a}: ${s.message}`),g.errors.push(`Erro ao criar medicamento ${a}: ${s.message}`);continue}$=e,M(`    ✅ Medicamento criado: ${a} (ID: ${$.id})`)}for(let e=0;e<t.length;e++){const o=t[e];try{let s=function(e){if(!e)return null;const a=e.toLowerCase();return a.includes("comprimido")||a.includes("capsula")||a.includes("solução oral")||a.includes("xarope")||a.includes("pastilha")||a.includes("colutório")||a.includes("suspensão")||a.includes("gotas")?"oral":a.includes("pomada")||a.includes("creme")||a.includes("gel")||a.includes("pomada bucal")?"topica":a.includes("injetavel")||a.includes("ampola")||a.includes("solução injetável")?"intravenosa":null};M(`      📦 Apresentação ${e+1}/${t.length}: ${o.forma_farmaceutica} - ${o.concentracao}`),M("         📋 Dados da apresentação:"),M(`            • Tarja: ${o.tarja||"N/A"}`),M(`            • Receituário: ${o.receituario||"N/A"}`),M(`            • Receita controlada: ${o.receita_controlada||"N/A"}`),M("            • Controlado: "+(o.controlado?"Sim":"Não")),M("            • Alto custo: "+(o.alto_custo?"Sim":"Não")),M(`            • Subtítulo: ${o.subtitulo||"N/A"}`),M(`            • Titularidade: ${o.titularidade||"N/A"}`),M(`            • EANs: ${o.eans?.length||0} código(s) ${o.eans?.length?`[${o.eans.join(", ")}]`:""}`),o.principios_ativos?.length?(M(`            • Princípios ativos: ${o.principios_ativos.length}`),o.principios_ativos.forEach(((e,a)=>{M(`              ${a+1}. ${e.nome} (DCB: ${e.dcb||"N/A"}, CAS: ${e.cas||"N/A"}) - ${e.concentracao}${e.medida||""}`)}))):M("            • ⚠️ Princípios ativos: NENHUM"),o.classificacao_terapeutica?.length?(M(`            • Classes terapêuticas: ${o.classificacao_terapeutica.length}`),o.classificacao_terapeutica.forEach(((e,a)=>{M(`              ${a+1}. ${e}`)}))):M("            • ⚠️ Classes terapêuticas: NENHUMA"),o.via_administracao?.length?M(`            • Vias de administração: ${o.via_administracao.length} [${o.via_administracao.join(", ")}]`):M("            • ⚠️ Vias de administração: VAZIO (será preservado se medicamento existir ou inferido se novo)");const d=[];o.posologias?.length&&d.push(`posologias (${o.posologias.length})`),o.cards&&Object.keys(o.cards).length&&d.push(`cards (${Object.keys(o.cards).length} campos)`),o.preco&&d.push("preços"),o.bula_pdf&&d.push("bula_pdf"),(o.psp||o.ads)&&d.push("psp/ads"),d.length>0&&M(`            • 🚫 Campos ignorados: ${d.join(", ")}`);let l=null;o.forma_farmaceutica?(M(`         🔍 Processando forma farmacêutica: ${o.forma_farmaceutica}`),l=await j(i,"drug_dosage_forms",o.forma_farmaceutica,{singular_form:o.forma_unidade?.singular||o.forma_farmaceutica,plural_form:o.forma_unidade?.plural||o.forma_farmaceutica+"s"}),M(`         ✅ Forma farmacêutica processada: ID ${l}`)):M("         ⚠️ Forma farmacêutica não informada");const m={drug_id:$.id,dosage_form_id:l,strength:o.concentracao||"",prescription_category:o.tarja||"livre",prescription_type:o.receituario||"simples",receita_controlada:o.receita_controlada||null,subtitulo:o.subtitulo||null,is_controlled:o.controlado||!1,is_high_cost:o.alto_custo||!1,is_primary:!1,eans:o.eans||[]},{data:p}=await u.from("drug_presentations").select("id").eq("drug_id",$.id).eq("dosage_form_id",l).eq("strength",o.concentracao||"").eq("prescription_category",o.tarja||"livre").eq("prescription_type",o.receituario||"simples").eq("is_controlled",o.controlado||!1).eq("is_high_cost",o.alto_custo||!1).single();let _;if(p)M(`        ⚠️ Apresentação já existe, usando existente: ${o.forma_farmaceutica} - ${o.concentracao}`),_=p;else{const{data:e,error:s}=await u.from("drug_presentations").insert(m).select().single();if(s){M(`        ❌ Erro ao criar apresentação: ${s.message}`),g.errors.push(`Erro ao criar apresentação ${o.forma_farmaceutica} para ${a}: ${s.message}`);continue}_=e,g.createdPresentations++,M(`        ✅ Apresentação criada: ${o.forma_farmaceutica} - ${o.concentracao}`)}if(o.principios_ativos&&Array.isArray(o.principios_ativos)){const{data:e}=await u.from("presentation_active_ingredients").select("active_ingredient_id").eq("presentation_id",_.id),a=new Set(e?.map((e=>e.active_ingredient_id))||[]),s=o.principios_ativos.map((e=>({name:e.nome,additionalFields:{dcb_code:e.dcb||null,cas_number:e.cas||null}}))),t=await N(r,"active_ingredients",s,"        "),i=[];for(const[o,r]of t)a.has(r)||i.push({presentation_id:_.id,active_ingredient_id:r});if(i.length>0){const{error:e}=await u.from("presentation_active_ingredients").insert(i);M(e?`        ❌ Erro ao relacionar princípios ativos: ${e.message}`:`        ✅ ${i.length} princípio(s) ativo(s) relacionado(s) em lote`)}else M("        ℹ️ Todos os princípios ativos já estão relacionados")}if(o.classificacao_terapeutica&&Array.isArray(o.classificacao_terapeutica)){const{data:e}=await u.from("presentation_therapeutic_classes").select("therapeutic_class_id").eq("presentation_id",_.id),a=new Set(e?.map((e=>e.therapeutic_class_id))||[]),s=o.classificacao_terapeutica.map((e=>({name:e}))),t=await N(n,"therapeutic_classes",s,"        "),i=[];for(const[o,r]of t)a.has(r)||i.push({presentation_id:_.id,therapeutic_class_id:r});if(i.length>0){const{error:e}=await u.from("presentation_therapeutic_classes").insert(i);M(e?`        ❌ Erro ao relacionar classes terapêuticas: ${e.message}`:`        ✅ ${i.length} classe(s) terapêutica(s) relacionada(s) em lote`)}else M("        ℹ️ Todas as classes terapêuticas já estão relacionadas")}if(o.via_administracao&&Array.isArray(o.via_administracao)&&o.via_administracao.length>0){M(`        🛣️ Processando vias de administração: [${o.via_administracao.join(", ")}]`);const{data:e}=await u.from("presentation_administration_routes").select("administration_route_id").eq("presentation_id",_.id),a=new Set(e?.map((e=>e.administration_route_id))||[]),s=o.via_administracao.map((e=>({name:e}))),t=await N(c,"administration_routes",s,"        "),i=[];for(const[o,r]of t)a.has(r)||i.push({presentation_id:_.id,administration_route_id:r});if(i.length>0){const{error:e}=await u.from("presentation_administration_routes").insert(i);M(e?`        ❌ Erro ao relacionar vias de administração: ${e.message}`:`        ✅ ${i.length} nova(s) via(s) de administração relacionada(s) em lote`)}else M("        ℹ️ Todas as vias de administração já estão relacionadas")}else if(h&&h.id!==$.id)M("        🔄 Vias de administração vazias - preservando vias existentes do medicamento");else{const{data:e}=await u.from("presentation_administration_routes").select("administration_route_id").eq("presentation_id",_.id);if(e&&e.length>0)M(`        ℹ️ Apresentação já possui ${e.length} via(s) de administração`);else{const e=s(o.forma_farmaceutica);if(e){M(`        🧠 Inferindo via de administração: ${e} (baseado em: ${o.forma_farmaceutica})`);const a=await j(c,"administration_routes",e);if(a){const{error:s}=await u.from("presentation_administration_routes").insert({presentation_id:_.id,administration_route_id:a});M(s?`        ❌ Erro ao relacionar via inferida: ${s.message}`:`        ✅ Via de administração inferida relacionada: ${e}`)}}else M(`        ⚠️ Não foi possível inferir via de administração para: ${o.forma_farmaceutica}`)}}o.posologias&&Array.isArray(o.posologias)&&o.posologias.length>0&&M(`        📋 ${o.posologias.length} posologia(s) disponível(eis)`)}catch(s){M(`        ❌ Erro ao processar apresentação: ${s.message}`),g.errors.push(`Erro ao processar apresentação ${o.forma_farmaceutica}: ${s.message}`)}}}catch(t){g.errors.push(`Erro ao processar medicamento ${a}: ${t.message}`),M(`    ❌ Erro no medicamento ${a}: ${t.message}`)}b++}const d={...g,processedMedications:b};S(d),P(),M(`📊 Lote ${a+1}/${x.length} concluído - ${b}/${_.size} medicamentos processados`,!0)}P(),M("🎉 Importação OTIMIZADA concluída!",!0),M("📊 Estatísticas finais:",!0),M(`  - ${g.consolidatedGroups} medicamentos únicos criados`,!0),M(`  - ${g.createdPresentations} apresentações criadas`,!0),M(`  - ${g.errors.length} erros encontrados`,!0),M("⚡ Performance: Processamento em lotes otimizados",!0),M("🔗 Estrutura: Cada medicamento pode ter múltiplas apresentações",!0),M("\n📋 RESUMO DOS CAMPOS IMPORTADOS:"),M("✅ CAMPOS IMPORTADOS COM SUCESSO:"),M("   • nome_comercial → drugs.brand_name"),M("   • classe → (removido - agora usa classes terapêuticas)"),M("   • fabricante → drugs.fabricante"),M("   • tipo → drugs.tipo"),M("   • status → drugs.status"),M("   • titularidade → drugs.titularidade ✨ NOVO"),M("   • controlado → drugs.is_controlled (boolean)"),M("   • alto_custo → drugs.is_high_cost"),M("   • composicao → drugs.composicao"),M("   • descricao → drugs.descricao"),M("   • uso_em.* → drugs.adult_use, pediatric_use, pregnancy_info, breastfeeding_info"),M("   • eans[] → drugs.eans + drug_presentations.eans"),M("   • forma_farmaceutica → drug_dosage_forms → drug_presentations.dosage_form_id"),M("   • concentracao → drug_presentations.strength"),M("   • tarja → drug_presentations.prescription_category"),M("   • receituario → drug_presentations.prescription_type"),M("   • receita_controlada → drug_presentations.receita_controlada ✨ NOVO"),M("   • subtitulo → drug_presentations.subtitulo ✨ NOVO"),M("   • principios_ativos[] → active_ingredients + relacionamentos"),M("   • classificacao_terapeutica[] → therapeutic_classes + relacionamentos"),M("   • via_administracao[] → administration_routes + relacionamentos (com inferência)"),M("\n🚫 CAMPOS IGNORADOS (CONFORME SOLICITADO):"),M("   • posologias[] → Ignorado"),M("   • cards → Ignorado"),M("   • preco → Ignorado"),M("   • bula_pdf → Ignorado"),M("   • psp, ads → Ignorado"),M("\n🔍 DETECÇÃO DE DUPLICATAS:"),M("   • Por nome exato"),M("   • Por nome normalizado (sem acentos)"),M("   • Por princípio ativo + concentração"),M("   • Por código DCB + concentração"),M("\n🛡️ PRESERVAÇÃO DE DADOS:"),M("   • Vias de administração existentes são preservadas"),M("   • Medicamentos existentes são reutilizados"),M("   • Dados são enriquecidos (fabricante, EANs, formatação)"),M("\n🧠 INFERÊNCIA INTELIGENTE:"),M("   • Vias de administração inferidas para medicamentos novos"),M("   • Baseada na forma farmacêutica"),M("   • oral: comprimidos, cápsulas, soluções orais, xaropes, pastilhas"),M("   • tópica: pomadas, cremes, géis"),M("   • intravenosa: injetáveis, ampolas"),M("\n🛡️ PREVENÇÃO DE DUPLICATAS:"),M("   • Verificação de relacionamentos existentes antes de inserir"),M("   • Evita erros de constraint de chave única"),M("   • Logs informativos para relacionamentos já existentes"),M("   • Processamento otimizado com menos repetições"),S(g),f.success("Importação concluída com sucesso!")}catch(t){M(`❌ Erro geral na importação: ${t.message}`),f.error("Erro na importação")}finally{b(!1)}}else f.error("Por favor, cole os dados JSON")},disabled:N||w||!v.trim(),className:"w-full",children:N?e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"h-4 w-4 mr-2 animate-spin"}),"Importando..."]}):e.jsxs(e.Fragment,{children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Importar"]})})})]})]}),e.jsxs(s,{children:[e.jsxs(t,{children:[e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(d,{className:"h-5 w-5"}),"Dados JSON"]}),e.jsx(i,{children:"Cole aqui o JSON com a nova estrutura de medicamentos"})]}),e.jsx(r,{children:e.jsx(l,{placeholder:"Cole aqui o JSON com os medicamentos...",value:v,onChange:e=>j(e.target.value),className:"min-h-[200px] font-mono text-sm",disabled:N||w||A})})]}),C&&e.jsxs(s,{children:[e.jsx(t,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-5 w-5"}),"Estatísticas da Importação"]})}),e.jsxs(r,{children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C.consolidatedGroups}),e.jsx("div",{className:"text-sm text-gray-600",children:"Grupos Consolidados"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:C.createdPresentations}),e.jsx("div",{className:"text-sm text-gray-600",children:"Apresentações Criadas"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:C.processedMedications}),e.jsx("div",{className:"text-sm text-gray-600",children:"Medicamentos Processados"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:C.errors.length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Erros"})]})]}),N&&e.jsx(m,{value:C.processedMedications/C.totalMedications*100,className:"mb-4"}),C.errors.length>0&&e.jsxs(g,{className:"mb-4",children:[e.jsx(p,{className:"h-4 w-4"}),e.jsxs(_,{children:[e.jsx("div",{className:"font-semibold mb-2",children:"Erros encontrados:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[C.errors.slice(0,5).map(((a,s)=>e.jsx("li",{children:a},s))),C.errors.length>5&&e.jsxs("li",{children:["... e mais ",C.errors.length-5," erros"]})]})]})]})]})]}),D.length>0&&e.jsxs(s,{children:[e.jsx(t,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(d,{className:"h-5 w-5"}),"Logs da Importação"]})}),e.jsx(r,{children:e.jsx("div",{className:"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto",children:D.map(((a,s)=>e.jsx("div",{className:"mb-1",children:a},s)))})})]})]})};export{v as default};
