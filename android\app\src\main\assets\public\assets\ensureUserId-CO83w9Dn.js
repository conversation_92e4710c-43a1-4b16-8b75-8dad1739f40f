var e=Object.defineProperty;import{s as t}from"./index-DV3Span9.js";const r=new class{constructor(){var t;((t,r,a)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a})(this,"symbol"!=typeof(t="cache")?t+"":t,new Map)}async executeOnce(e,t){if(this.cache.has(e))return this.cache.get(e);const r=t().finally((()=>{this.cache.delete(e)}));return this.cache.set(e,r),r}clear(){this.cache.clear()}},a=async()=>r.executeOnce("user-id",(async()=>{const{data:{user:e},error:r}=await t.auth.getUser();if(r||!e)throw new Error("Usuário não autenticado");return e.id}));export{a as e};
