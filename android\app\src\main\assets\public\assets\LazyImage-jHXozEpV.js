import{j as e}from"./radix-core-6kBL75b5.js";import{b as r,r as t}from"./critical-DVX9Inzy.js";import{j as a}from"./index-D9amGMlQ.js";const s=r.memo((({src:r,alt:s,className:o,fallback:n="/faviconx.webp",blur:i=!0,priority:d=!1,onLoad:l,onError:c,width:u,height:m,...g})=>{const[x,b]=t.useState(!1),[f,h]=t.useState(!1),[p,j]=t.useState(""),y=t.useRef(null),v=((e,r={})=>{const[a,s]=t.useState(!1);return t.useEffect((()=>{const t=e.current;if(!t||a)return;const o=new IntersectionObserver((([e])=>{e.isIntersecting&&(s(!0),o.unobserve(t))}),{rootMargin:"50px",threshold:.1,...r});return o.observe(t),()=>{o.unobserve(t)}}),[e,a]),a})(y,{rootMargin:"100px"}),w=d||v;return t.useEffect((()=>{if(!w||f)return;const e=new Image;e.onload=()=>{j(r),b(!0),l?.()},e.onerror=()=>{h(!0),j(n),c?.()};const t=((e,r,t)=>{if(!e.includes("supabase.co/storage/v1/object/public/"))return e;const a=new URLSearchParams;if(r&&a.append("width",Math.min(r,800).toString()),t&&a.append("height",Math.min(t,600).toString()),a.append("quality","85"),a.append("format","webp"),""===a.toString())return e;const s=e.includes("?")?"&":"?";return`${e}${s}${a.toString()}`})(r,u,m);return e.src=t,()=>{e.onload=null,e.onerror=null}}),[w,r,n,l,c,f]),e.jsxs("div",{ref:y,className:a("relative overflow-hidden bg-gray-100 dark:bg-gray-800",o),style:{...u&&{width:u},...m&&{height:m},maxWidth:"100%"},...g,children:[!x&&!f&&e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-10 h-10 border-3 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-3 mx-auto"}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:"Carregando..."}),e.jsx("div",{className:"w-20 h-1 bg-gray-200 dark:bg-gray-600 rounded-full mt-2 mx-auto overflow-hidden",children:e.jsx("div",{className:"h-full bg-blue-500 rounded-full animate-pulse w-3/4"})})]})}),p&&e.jsx("img",{src:p,alt:s,className:a("w-full h-full object-cover transition-opacity duration-300",x?"opacity-100":"opacity-0"),loading:d?"eager":"lazy",decoding:"async",...u&&{width:u},...m&&{height:m},onLoad:()=>{b(!0),l?.()},onError:()=>{f||(h(!0),j(n))}}),f&&p===n&&e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 flex items-center justify-center border-2 border-dashed border-red-200 dark:border-red-700",children:e.jsxs("div",{className:"text-center text-red-600 dark:text-red-400",children:[e.jsx("div",{className:"text-3xl mb-2",children:"⚠️"}),e.jsx("div",{className:"text-sm font-medium",children:"Erro ao carregar"}),e.jsx("div",{className:"text-xs mt-1 opacity-75",children:"Verifique a conexão"})]})})]})})),o=r=>e.jsx(s,{...r,priority:!0});export{s as L,o as P};
