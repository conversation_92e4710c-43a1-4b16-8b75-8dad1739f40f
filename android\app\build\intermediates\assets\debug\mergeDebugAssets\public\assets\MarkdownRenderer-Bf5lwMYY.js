import{j as r}from"./radix-core-6kBL75b5.js";import{E as e}from"./index-tjFZaLw_.js";import"./critical-DVX9Inzy.js";function n({content:n,className:o=""}){return n?r.jsx("div",{className:`markdown-content ${o}`,children:r.jsx(e.Markdown,{source:n,style:{backgroundColor:"transparent",color:"inherit",fontSize:"inherit",lineHeight:"inherit"}})}):r.jsx("span",{className:"text-muted-foreground",children:"Sem descrição disponível"})}export{n as M};
