import{j as e}from"./radix-core-6kBL75b5.js";import{b as a}from"./critical-DVX9Inzy.js";import{bi as t,bj as s,az as r,B as i,M as l,A as n,X as o,P as d,l as c,L as x,bk as m,av as g,al as u,n as p,k as h,ac as j}from"./index-CNG-Xj2g.js";import{Z as b}from"./zap-C4mKju26.js";import{d as f}from"./supabase-vendor-qi_Ptfv-.js";import{a as y}from"./router-BAzpOxbo.js";import{B as N}from"./bot-BYA-FVo1.js";import{B as v}from"./book-open-EV5sJdXr.js";import{S as k}from"./scroll-text-DUfFh5nv.js";import"./query-vendor-B-7l6Nb3.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const w=({title:a,results:i,onSelect:l,formatBrands:n,icon:o})=>0===i.length?null:e.jsx(t,{heading:a,className:"px-0",headingClassName:"sticky top-0 z-10 bg-gray-100/80 dark:bg-slate-700/80 backdrop-blur-md px-4 py-2 text-xs font-medium text-gray-700 dark:text-gray-300 border-b border-gray-200/50 dark:border-gray-700/50",children:e.jsx("div",{className:"flex flex-col",children:i.map((a=>e.jsxs(s,{onSelect:()=>l(a),className:"flex items-start px-4 py-3 hover:bg-gray-50/80 dark:hover:bg-slate-700/50 active:bg-gray-100 dark:active:bg-slate-600/50 cursor-pointer transition-colors rounded-md m-1",value:`${a.name}${a.brands||""}${a.category?.name||""}${a.code_range||""}`,children:[e.jsx("div",{className:"flex-shrink-0 mr-3 mt-0.5 bg-gray-100/80 dark:bg-slate-700/80 p-1.5 rounded-md",children:o}),e.jsxs("div",{className:"flex flex-col flex-1 min-w-0",children:[e.jsxs("div",{className:"font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2",children:["icd10"===a.type&&a.code_range?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"inline-flex items-center justify-center bg-cyan-100 dark:bg-cyan-900/40 text-cyan-800 dark:text-cyan-300 text-xs font-medium px-1.5 py-0.5 rounded-md",children:a.code_range}),a.name]}):a.name,a.isFuzzyMatch&&e.jsxs("span",{className:"inline-flex items-center gap-1 bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-300 text-xs font-medium px-1.5 py-0.5 rounded-md",title:"Você quis dizer este resultado?",children:[e.jsx(b,{className:"h-3 w-3"}),"Você quis dizer?"]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-x-2 mt-1",children:["leaflet"===a.type?e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate max-w-[70%]",children:"Bula profissional"}):e.jsxs(e.Fragment,{children:[n&&a.brands&&e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate max-w-[70%]",children:n(a.brands)}),!a.brands&&a.description&&e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate max-w-[70%]",children:a.description})]}),a.category&&e.jsx(r,{variant:"outline",className:"bg-gray-100/80 dark:bg-slate-700/80 text-gray-700 dark:text-gray-300 border-none text-[10px] px-2 py-0.5 h-auto font-medium whitespace-nowrap rounded-md",children:a.category.name})]})]})]},a.id)))})}),S=({searchTerm:t})=>{const s=y(),r=f.useSession(),[o,d]=a.useState(!1);return t&&t.length<3?e.jsx("div",{className:"p-8 text-center",children:e.jsx("div",{className:"bg-gray-100/80 dark:bg-slate-700/80 rounded-lg p-4 inline-block mb-2",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Digite pelo menos 3 caracteres para buscar"})})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"p-8 text-center",children:e.jsxs("div",{className:"bg-gray-100/80 dark:bg-slate-700/80 rounded-lg p-6 max-w-sm mx-auto",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-2",children:"Nenhum resultado encontrado para:"}),t&&e.jsxs("p",{className:"text-sm font-medium text-gray-800 dark:text-gray-200 mb-4 bg-gray-200/50 dark:bg-slate-600/50 px-3 py-1 rounded-md",children:['"',t,'"']}),e.jsx("p",{className:"text-sm font-medium text-primary dark:text-blue-400 mb-4",children:"Que tal perguntar ao Dr. Will sobre isso?"}),e.jsxs(i,{variant:"default",onClick:()=>{r?s("/dr-will",{state:{initialMessage:t}}):d(!0)},className:"bg-indigo-600 hover:bg-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-300 rounded-md",children:[e.jsx(l,{className:"mr-2 h-4 w-4"}),"Perguntar ao Will"]})]})}),e.jsx(n,{open:o,onOpenChange:d,hidden:!0})]})},C=({searchTerm:a,onSelect:r})=>{const i=y();return e.jsxs(t,{className:"px-0",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 backdrop-blur-md px-4 py-2 text-xs font-medium text-indigo-700 dark:text-indigo-300 border-b border-indigo-200/50 dark:border-indigo-700/50",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-4 w-4"}),"Perguntar ao Dr. Will"]})}),e.jsx("div",{className:"flex flex-col",children:e.jsxs(s,{onSelect:()=>{r&&r(),i("/dr-will",{state:{initialMessage:`Olá Dr. Will! Estava procurando por "${a}" no PedBook mas não encontrei exatamente o que preciso. Pode me ajudar?`}})},className:"flex items-start px-4 py-4 hover:bg-indigo-50/80 dark:hover:bg-indigo-900/20 active:bg-indigo-100 dark:active:bg-indigo-800/30 cursor-pointer transition-colors rounded-md m-1 border border-indigo-200/30 dark:border-indigo-700/30",children:[e.jsx("div",{className:"flex-shrink-0 mr-3 mt-0.5 bg-indigo-100/80 dark:bg-indigo-900/40 p-1.5 rounded-md",children:e.jsx(l,{className:"h-4 w-4 text-indigo-600 dark:text-indigo-400"})}),e.jsxs("div",{className:"flex flex-col flex-1 min-w-0",children:[e.jsx("div",{className:"font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2",children:"Não encontrou o que procurava?"}),e.jsx("div",{className:"flex flex-wrap items-center gap-x-2 mt-1",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:["Pergunte ao Dr. Will sobre ",e.jsxs("span",{className:"font-medium text-indigo-600 dark:text-indigo-400",children:['"',a,'"']})]})}),e.jsx("div",{className:"mt-2",children:e.jsxs("span",{className:"inline-flex items-center gap-1 bg-indigo-100 dark:bg-indigo-900/40 text-indigo-800 dark:text-indigo-300 text-xs font-medium px-2 py-1 rounded-md",children:[e.jsx(N,{className:"h-3 w-3"}),"Assistente IA"]})})]})]})})]})},B=a.memo((({results:a,onSelect:t,formatBrands:s,isLoading:r,isFetching:i,showEmptyMessage:l,searchTerm:n,currentInput:b,onClose:f})=>{const y=a.length>0&&a.some((e=>e.isFuzzyMatch))&&n&&n.length>=3;if(b&&b.length>0&&b.length<3)return e.jsxs("div",{className:"p-4 text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 text-muted-foreground",children:[e.jsx("div",{className:"w-2 h-2 bg-primary/60 rounded-full animate-pulse"}),e.jsxs("p",{className:"text-sm",children:["Digite mais ",3-b.length," caractere",3-b.length>1?"s":""," para buscar..."]})]}),e.jsxs("div",{className:"mt-2 text-xs text-muted-foreground",children:['Buscando por: "',e.jsx("span",{className:"font-medium",children:b}),'"']})]});if(l)return e.jsxs("div",{className:"p-4 space-y-4 relative",children:[f&&e.jsx("button",{onClick:f,className:"absolute top-2 right-2 h-6 w-6 rounded-full bg-gray-100/80 dark:bg-slate-700/80 hover:bg-gray-200 dark:hover:bg-slate-600 flex items-center justify-center transition-colors",title:"Fechar busca",children:e.jsx(o,{className:"h-3 w-3 text-gray-500 dark:text-gray-400"})}),e.jsx("div",{className:"text-center text-muted-foreground mb-4",children:e.jsx("p",{className:"text-sm",children:"Digite pelo menos 3 caracteres para começar a buscar..."})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"text-xs font-medium text-muted-foreground px-2",children:"Sugestões populares:"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("button",{onClick:()=>t({id:"paracetamol",name:"Paracetamol",type:"medication",slug:"paracetamol"}),className:"flex items-center gap-2 p-2 text-left text-xs bg-primary/5 hover:bg-primary/10 rounded-lg transition-colors",children:[e.jsx(d,{className:"h-3 w-3 text-primary"}),"Paracetamol"]}),e.jsxs("button",{onClick:()=>t({id:"dipirona",name:"Dipirona",type:"medication",slug:"dipirona"}),className:"flex items-center gap-2 p-2 text-left text-xs bg-primary/5 hover:bg-primary/10 rounded-lg transition-colors",children:[e.jsx(d,{className:"h-3 w-3 text-primary"}),"Dipirona"]}),e.jsxs("button",{onClick:()=>t({id:"apgar",name:"Calculadora de Apgar",type:"calculator",path:"/calculadoras/apgar"}),className:"flex items-center gap-2 p-2 text-left text-xs bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsx(c,{className:"h-3 w-3 text-blue-500"}),"Apgar"]}),e.jsxs("button",{onClick:()=>t({id:"glasgow",name:"Calculadora de Glasgow",type:"calculator",path:"/calculadoras/glasgow"}),className:"flex items-center gap-2 p-2 text-left text-xs bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsx(c,{className:"h-3 w-3 text-blue-500"}),"Glasgow"]})]})]}),e.jsx("div",{className:"text-center pt-2 border-t border-border/50",children:e.jsx("p",{className:"text-xs text-muted-foreground",children:"💡 Busque por medicamentos, bulas, calculadoras, condutas ou CIDs"})})]});if(r)return e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(x,{className:"h-6 w-6 animate-spin text-primary/60 mx-auto"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Buscando resultados..."})]});if(!r&&!i&&n&&n.length>=3&&0===a.length&&n===b)return e.jsx(S,{searchTerm:n});if(n!==b&&b&&b.length>=3||i&&n&&n.length>=3)return e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(x,{className:"h-6 w-6 animate-spin text-primary/60 mx-auto"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Buscando resultados..."})]});const N=a.filter((e=>"medication"===e.type)),B=a.filter((e=>"category"===e.type)),D=a.filter((e=>"calculator"===e.type)),z=a.filter((e=>"flowchart"===e.type)),P=a.filter((e=>"childcare"===e.type)),F=a.filter((e=>"toxidrome"===e.type)),M=a.filter((e=>"icd10"===e.type)),q=a.filter((e=>"conduct"===e.type)),A=a.filter((e=>"vaccine"===e.type)),I=a.filter((e=>"leaflet"===e.type));return e.jsxs(m,{className:"max-h-[60vh] overflow-y-auto rounded-lg bg-transparent",children:[e.jsx(w,{title:"Condutas e Manejos",results:q,onSelect:t,icon:e.jsx(v,{className:"h-4 w-4 text-emerald-600"})}),e.jsx(w,{title:"Vacinas",results:A,onSelect:t,icon:e.jsx(g,{className:"h-4 w-4 text-purple-500"})}),e.jsx(w,{title:"Medicamentos",results:N,onSelect:t,formatBrands:s,icon:e.jsx(d,{className:"h-4 w-4 text-primary"})}),e.jsx(w,{title:"Bulas",results:I,onSelect:t,icon:e.jsx(k,{className:"h-4 w-4 text-orange-500"})}),e.jsx(w,{title:"Intoxicações",results:F,onSelect:t,icon:e.jsx(u,{className:"h-4 w-4 text-amber-500"})}),e.jsx(w,{title:"Calculadoras",results:D,onSelect:t,icon:e.jsx(c,{className:"h-4 w-4 text-blue-500"})}),e.jsx(w,{title:"Fluxogramas",results:z,onSelect:t,icon:e.jsx(p,{className:"h-4 w-4 text-indigo-500"})}),e.jsx(w,{title:"Puericultura",results:P,onSelect:t,icon:e.jsx(h,{className:"h-4 w-4 text-pink-500"})}),e.jsx(w,{title:"Categorias",results:B,onSelect:t,icon:e.jsx(j,{className:"h-4 w-4 text-gray-600"})}),e.jsx(w,{title:"CID-10",results:M,onSelect:t,icon:e.jsx(j,{className:"h-4 w-4 text-cyan-500"})}),y&&e.jsx(C,{searchTerm:n,onSelect:f})]})}));export{B as SearchResults};
