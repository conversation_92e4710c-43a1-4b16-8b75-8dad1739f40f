import{j as a}from"./radix-core-6kBL75b5.js";import{r as e}from"./critical-DVX9Inzy.js";import{aM as s,aa as r,B as l,R as o,an as t,ad as i,ae as d,af as m,ag as c,ai as n}from"./index-CrSshpOb.js";import p from"./Footer-ClHMSbsi.js";import{L as u}from"./router-BAzpOxbo.js";import{C as v,a as x}from"./calculatorSEOData-DU9NV5AY.js";import{C as j}from"./chevron-left-CuYzwBha.js";import"./query-vendor-B-7l6Nb3.js";import"./supabase-vendor-qi_Ptfv-.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./FeedbackTrigger-CJimmo1j.js";import"./rocket-Bte4lXB7.js";import"./target-Cn5InUof.js";import"./zap-CpxW8g4N.js";import"./book-open-xrBK01RW.js";import"./star-DsgxKBIV.js";import"./circle-help-C80RLJKB.js";import"./instagram-BDU9Wbeo.js";const b=()=>{const b=x.capurro,[h,g]=e.useState({mamilo:null,pregas:null,pele:null,orelha:null,glandula:null}),f=(()=>{const a=Object.values(h).reduce(((a,e)=>a+(e||0)),0)+204;return{total:a,weeks:Math.floor(a/7),days:a%7}})(),y=Object.values(h).every((a=>null!==a));return a.jsxs("div",{className:s.gradientBackground("min-h-screen flex flex-col"),children:[a.jsx(v,{...b}),a.jsx(r,{}),a.jsx("main",{className:"flex-1 container mx-auto px-4 py-8",children:a.jsxs("div",{className:"max-w-3xl mx-auto space-y-8",children:[a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx(u,{to:"/calculadoras",children:a.jsx(l,{variant:"ghost",size:"icon",className:"hover:bg-primary/10 dark:hover:bg-primary/20",children:a.jsx(j,{className:"h-5 w-5"})})}),a.jsx("h1",{className:s.gradientHeading("text-3xl"),children:"Capurro Somático"})]}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Avaliação da idade gestacional do recém-nascido com base em características clínicas observáveis"}),a.jsxs(o,{className:s.card("p-6 space-y-6"),children:[Object.entries({mamilo:[{value:0,label:"Apenas visível, sem aréola"},{value:5,label:"Aréola bem definida - diâmetro menor que 0,75 cm"},{value:10,label:"Aréola bem definida, borda não elevada - diâmetro maior que 0,75 cm"},{value:15,label:"Borda elevada - diâmetro maior que 0,75 cm"}],pregas:[{value:0,label:"Sem pregas plantares"},{value:5,label:"Marcas mal definidas na metade anterior"},{value:10,label:"Marcas bem definidas na metade anterior e sulcos bem definidos no terço anterior"},{value:15,label:"Sulcos na metade anterior da planta"},{value:20,label:"Sulcos em mais da metade anterior da planta"}],pele:[{value:0,label:"Muito fina, gelatinosa"},{value:5,label:"Fina e lisa"},{value:10,label:"Algo mais grossa, discreta descamação superficial"},{value:15,label:"Grossa, sulcos superficiais, descamação nas mãos e pés"},{value:20,label:"Grossa apergaminhada, com sulcos profundos"}],orelha:[{value:0,label:"Chata disforme, pavilhão não encurvado"},{value:8,label:"Pavilhão parcialmente encurvado na borda"},{value:16,label:"Pavilhão parcialmente encurvado em toda parte superior"},{value:24,label:"Pavilhão totalmente encurvado"}],glandula:[{value:0,label:"Não palpável"},{value:5,label:"Palpável, menos de 0,5 cm"},{value:10,label:"Entre 0,5 e 1 cm"},{value:15,label:"Maior de 1 cm"}]}).map((([e,r])=>a.jsxs("div",{className:"space-y-2",children:[a.jsx(t,{className:"text-base font-medium text-gray-800 dark:text-gray-200",children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")}),a.jsxs(i,{value:h[e]?.toString()||"",onValueChange:a=>((a,e)=>{g((s=>({...s,[a]:parseInt(e,10)})))})(e,a),children:[a.jsx(d,{className:s.select("w-full"),children:a.jsx(m,{placeholder:"Selecione uma opção"})}),a.jsx(c,{children:r.map((e=>a.jsx(n,{value:e.value.toString(),children:e.label},e.value)))})]})]},e))),y&&a.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:a.jsxs("div",{className:"text-center space-y-4",children:[a.jsxs("div",{className:"text-4xl font-bold text-primary dark:text-blue-400",children:[f.weeks," semanas e ",f.days," dias"]}),a.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Total de pontos: ",f.total]}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto",children:"Nota: Este método pode apresentar variações em comparação com outros métodos de avaliação da idade gestacional."})]})})]})]})}),a.jsx(p,{})]})};export{b as default};
