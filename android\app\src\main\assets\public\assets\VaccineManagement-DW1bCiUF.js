import{j as e}from"./radix-core-6kBL75b5.js";import{r as s}from"./critical-DVX9Inzy.js";import{a,u as n}from"./query-vendor-B-7l6Nb3.js";import{az as i,an as r,a5 as o,ad as c,ae as t,af as d,ag as l,ai as m,d as h,s as p,D as x,e as u,f as v,g as j,z as f,aw as y,B as g,aT as _,aU as N,T as b,R as w,W as C,Z as S,aj as D,U as k}from"./index-Dq2DDcRF.js";import{E as q}from"./index-Bsdbnpgm.js";import{A as V,a as A,b as I,c as T,d as E,e as F,f as $,g as R}from"./alert-dialog-LAt5xMzV.js";import{M as z}from"./MarkdownRenderer-BqxKrNWM.js";import{P as M}from"./plus-IZJFu_xe.js";import{P as K}from"./pencil-C1TCRll_.js";import{T as O}from"./trash-2-CQoB25P5.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";import"./markdown-vendor-C57yw7YK.js";import"./index-Bf1cTgQT.js";function U({vaccineId:s,vaccineName:a,isSelected:n,relatedVaccine:h,onSelectionChange:p,onDoseNumberChange:x,onDoseTypeChange:u}){return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(i,{id:s,checked:n,onCheckedChange:e=>p(s,e)}),e.jsx("label",{htmlFor:s,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:a})]}),n&&e.jsxs("div",{className:"ml-6 space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:`dose-${s}`,children:"Número da dose"}),e.jsx(o,{id:`dose-${s}`,type:"number",min:"1",value:h?.doseNumber||"",onChange:e=>x(s,parseInt(e.target.value)),className:"w-24",required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:`type-${s}`,children:"Tipo"}),e.jsxs(c,{value:h?.doseType||"dose",onValueChange:e=>u(s,e),children:[e.jsx(t,{className:"w-32",children:e.jsx(d,{placeholder:"Selecione o tipo"})}),e.jsxs(l,{children:[e.jsx(m,{value:"dose",children:"Dose"}),e.jsx(m,{value:"reforço",children:"Reforço"})]})]})]})]})]})}function P({open:i,onOpenChange:c,vaccine:t}){const{name:d,setName:l,description:m,setDescription:_,selectedVaccines:N,setSelectedVaccines:b,handleSubmit:w,handleVaccineSelection:C,handleDoseNumberChange:S,handleDoseTypeChange:D}=((e,n)=>{const[i,r]=s.useState(e?.name||""),[o,c]=s.useState(e?.description||""),[t,d]=s.useState(new Map),{toast:l}=h(),m=a();return s.useEffect((()=>{e?(r(e.name||""),c(e.description||"")):(r(""),c(""))}),[e]),{name:i,setName:r,description:o,setDescription:c,selectedVaccines:t,setSelectedVaccines:d,handleSubmit:async s=>{s.preventDefault();try{let s=e?.id;if(e){const{error:s}=await p.from("pedbook_vaccines").update({name:i,description:o}).eq("id",e.id);if(s)throw s}else{const{data:e,error:a}=await p.from("pedbook_vaccines").insert([{name:i,description:o}]).select().single();if(a)throw a;s=e.id}if(s){const{error:e}=await p.from("pedbook_vaccine_relationships").delete().eq("parent_vaccine_id",s);if(e)throw e;if(t.size>0){const e=Array.from(t.values()).map((e=>({parent_vaccine_id:s,child_vaccine_id:e.id,dose_number:e.doseNumber||null,dose_type:e.doseType||"dose"}))),{error:a}=await p.from("pedbook_vaccine_relationships").insert(e);if(a)throw a}}l({title:e?"Vacina atualizada com sucesso!":"Vacina criada com sucesso!",description:`A vacina ${i} foi ${e?"atualizada":"adicionada"}.`}),m.invalidateQueries({queryKey:["vaccines"]}),n(!1)}catch(a){l({variant:"destructive",title:"Erro ao salvar vacina",description:a.message})}},handleVaccineSelection:(e,s)=>{const a=new Map(t);s?a.set(e,{id:e,doseType:"dose"}):a.delete(e),d(a)},handleDoseNumberChange:(e,s)=>{const a=new Map(t),n=a.get(e);n&&(a.set(e,{...n,doseNumber:s}),d(a))},handleDoseTypeChange:(e,s)=>{const a=new Map(t),n=a.get(e);n&&(a.set(e,{...n,doseType:s}),d(a))}}})(t,c),{data:k}=(V=t?.id,n({queryKey:["available-vaccines",V],queryFn:async()=>{const{data:e,error:s}=await p.from("pedbook_vaccines").select("id, name").order("name");if(s)throw s;return e.filter((e=>e.id!==V))},enabled:i}));var V;const{data:A}=((e,s)=>n({queryKey:["vaccine-relationships",e],queryFn:async()=>{if(!e)return[];const{data:s,error:a}=await p.from("pedbook_vaccine_relationships").select("child_vaccine_id, dose_number, dose_type").eq("parent_vaccine_id",e).returns();if(a)throw a;return(s||[]).map((e=>({child_vaccine_id:e.child_vaccine_id,dose_number:e.dose_number,dose_type:e.dose_type||"dose"})))},enabled:!!e&&s}))(t?.id,i);return s.useEffect((()=>{if(A){const e=new Map;A.forEach((s=>{e.set(s.child_vaccine_id,{id:s.child_vaccine_id,doseNumber:s.dose_number,doseType:s.dose_type||"dose"})})),b(e)}else b(new Map)}),[A,b]),e.jsx(x,{open:i,onOpenChange:c,children:e.jsxs(u,{className:"sm:max-w-[425px]",children:[e.jsxs(v,{children:[e.jsx(j,{children:t?"Editar Vacina":"Nova Vacina"}),e.jsx(f,{children:"Preencha os dados da vacina e selecione as vacinas relacionadas."})]}),e.jsxs("form",{onSubmit:w,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"name",children:"Nome da Vacina"}),e.jsx(o,{id:"name",value:d,onChange:e=>l(e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"description",children:"Descrição"}),e.jsx("div",{className:"mt-2",children:e.jsx(q,{value:m,onChange:e=>_(e||""),preview:"edit",hideToolbar:!1,visibleDragBar:!1,textareaProps:{placeholder:"Digite a descrição da vacina usando Markdown...\n\n**Negrito** para destacar\n*Itálico* para ênfase\n- Lista com marcadores\n1. Lista numerada"},height:200})})]}),e.jsxs("div",{children:[e.jsx(r,{children:"Vacinas Relacionadas"}),e.jsx(y,{className:"h-[200px] w-full border rounded-md p-4",children:e.jsx("div",{className:"space-y-4",children:k?.map((s=>e.jsx(U,{vaccineId:s.id,vaccineName:s.name,isSelected:N.has(s.id),relatedVaccine:N.get(s.id),onSelectionChange:C,onDoseNumberChange:S,onDoseTypeChange:D},s.id)))})})]}),e.jsxs(g,{type:"submit",className:"w-full",children:[t?"Atualizar":"Criar"," Vacina"]})]})]})})}function L({open:n,onOpenChange:i,vaccineId:f}){const[y,w]=s.useState(""),[C,S]=s.useState("months"),[D,k]=s.useState(""),[q,V]=s.useState(""),[A,I]=s.useState(""),[T,E]=s.useState("SUS"),[F,$]=s.useState("dose"),{toast:R}=h(),z=a();return e.jsx(x,{open:n,onOpenChange:i,children:e.jsxs(u,{className:"sm:max-w-[425px]",children:[e.jsx(v,{children:e.jsx(j,{children:"Nova Dose"})}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{const e="birth"===C?"0":"years"===C?(12*parseInt(D)+(parseInt(q)||0)).toString():q,{error:s}=await p.from("pedbook_vaccine_doses").insert([{vaccine_id:f,dose_number:parseInt(y),age_recommendation:e,description:A,type:T,dose_type:F}]);if(s)throw s;R({title:"Dose adicionada com sucesso!",description:`A ${y}ª ${F} foi adicionada.`,className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"}),z.invalidateQueries({queryKey:["vaccines"]}),i(!1)}catch(s){R({variant:"destructive",title:"Erro ao adicionar dose",description:s.message,className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})}},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(r,{children:"Tipo"}),e.jsxs(_,{value:F,onValueChange:e=>$(e),className:"flex space-x-4 mt-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{value:"dose",id:"dose"}),e.jsx(r,{htmlFor:"dose",children:"Dose"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{value:"reforço",id:"reforco"}),e.jsx(r,{htmlFor:"reforco",children:"Reforço"})]})]})]}),e.jsxs("div",{children:[e.jsxs(r,{htmlFor:"doseNumber",children:["Número da ",F]}),e.jsx(o,{id:"doseNumber",type:"number",value:y,onChange:e=>w(e.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Idade Recomendada"}),e.jsxs(c,{value:C,onValueChange:e=>{S(e),k(""),V("")},children:[e.jsx(t,{className:"w-[140px]",children:e.jsx(d,{placeholder:"Selecione"})}),e.jsxs(l,{children:[e.jsx(m,{value:"birth",children:"Ao nascer"}),e.jsx(m,{value:"months",children:"Meses"}),e.jsx(m,{value:"years",children:"Anos"})]})]}),"years"===C&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(r,{children:"Anos"}),e.jsx(o,{type:"number",value:D,onChange:e=>k(e.target.value),min:"1",max:"18",required:!0})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx(r,{children:"Meses"}),e.jsx(o,{type:"number",value:q,onChange:e=>V(e.target.value),min:"0",max:"11"})]})]}),"months"===C&&e.jsx(o,{type:"number",value:q,onChange:e=>V(e.target.value),min:"1",max:"11",placeholder:"1-11",required:!0})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"type",children:"Tipo de Serviço"}),e.jsxs(c,{value:T,onValueChange:e=>E(e),children:[e.jsx(t,{children:e.jsx(d,{placeholder:"Selecione o tipo"})}),e.jsxs(l,{children:[e.jsx(m,{value:"SUS",children:"SUS"}),e.jsx(m,{value:"PARTICULAR",children:"PARTICULAR"})]})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"description",children:"Descrição"}),e.jsx(b,{id:"description",value:A,onChange:e=>I(e.target.value)})]}),e.jsxs(g,{type:"submit",className:"w-full",children:["Adicionar ","dose"===F?"Dose":"Reforço"]})]})]})})}function Q({vaccine:s,vaccineRelationships:a,onAddDose:n,onEdit:i,onDelete:r,formatDoseNumber:o,handleDeleteDose:c}){const{toast:t}=h();return e.jsxs(w,{className:"hover:shadow-lg transition-shadow",children:[e.jsxs(C,{className:"flex flex-row items-center justify-between",children:[e.jsx(S,{className:"text-xl",children:s.name}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(g,{variant:"ghost",size:"icon",onClick:()=>n(s.id),children:e.jsx(M,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"icon",onClick:()=>i(s),children:e.jsx(K,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"icon",onClick:()=>r(s),children:e.jsx(O,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"icon",onClick:()=>{t({title:s.name,description:e.jsx(z,{content:s.description||"Sem descrição disponível",className:"compact"}),className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",duration:8e3})},children:e.jsx(D,{className:"h-4 w-4"})})]})]}),e.jsxs(k,{children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:e.jsx(z,{content:s.description||"Sem descrição disponível",className:"compact"})}),a?.[s.id]?.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Vacinas Relacionadas:"}),e.jsx("ul",{className:"space-y-1",children:a[s.id].map((s=>e.jsxs("li",{className:"text-sm text-muted-foreground",children:["• ",s.name]},s.id)))})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Doses:"}),e.jsx("ul",{className:"space-y-2",children:s.pedbook_vaccine_doses?.sort(((e,s)=>parseInt(e.age_recommendation)-parseInt(s.age_recommendation))).map((s=>e.jsxs("li",{className:"text-sm flex items-center justify-between",children:[e.jsxs("span",{children:[o(s.dose_number,s.dose_type)," - ","0"===s.age_recommendation?"Ao nascer":parseInt(s.age_recommendation)>=12?`${Math.floor(parseInt(s.age_recommendation)/12)} ano(s) e ${parseInt(s.age_recommendation)%12} mese(s)`:`${s.age_recommendation} meses`]}),e.jsx(g,{variant:"ghost",size:"icon",onClick:()=>c(s.id,o(s.dose_number,s.dose_type)),children:e.jsx(O,{className:"h-3 w-3"})})]},s.id)))})]})]})]})}function B({vaccines:i}){const[r,o]=s.useState(null),[c,t]=s.useState(null),[d,l]=s.useState(null),{toast:m}=h(),x=a(),{data:u}=n({queryKey:["vaccine-relationships"],queryFn:async()=>{const{data:e,error:s}=await p.from("pedbook_vaccine_relationships").select("\n          parent_vaccine_id,\n          child_vaccine:pedbook_vaccines!child_vaccine_id (\n            id,\n            name\n          )\n        ");if(s)throw s;const a={};return e.forEach((e=>{a[e.parent_vaccine_id]||(a[e.parent_vaccine_id]=[]),a[e.parent_vaccine_id].push(e.child_vaccine)})),a}}),{data:v,error:j}=n({queryKey:["vaccines"],queryFn:async()=>{const{data:e,error:s}=await p.from("pedbook_vaccines").select("\n          id,\n          name,\n          description,\n          pedbook_vaccine_doses (\n            id,\n            dose_number,\n            age_recommendation,\n            description,\n            type,\n            dose_type\n          )\n        ");if(s)throw s;return e}});if(j)return e.jsx("div",{children:"Error fetching vaccines"});const f=async(e,s)=>{try{const{error:a}=await p.from("pedbook_vaccine_doses").delete().eq("id",e);if(a)throw a;m({title:"Dose removida com sucesso!",description:`A dose ${s} foi removida.`,className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"}),x.invalidateQueries({queryKey:["vaccines"]})}catch(a){m({variant:"destructive",title:"Erro ao remover dose",description:a.message,className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})}},y=(e,s)=>"dose"===s?`${e}ª dose`:"reforço"===s?`${e}º reforço`:`${e}ª dose`;return e.jsxs("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[v?.map((s=>e.jsx(Q,{vaccine:s,vaccineRelationships:u||{},onAddDose:e=>o(e),onEdit:e=>t(e),onDelete:e=>l(e),formatDoseNumber:y,handleDeleteDose:f},s.id))),e.jsx(L,{open:!!r,onOpenChange:()=>o(null),vaccineId:r||""}),e.jsx(P,{open:!!c,onOpenChange:()=>t(null),vaccine:c}),e.jsx(V,{open:!!d,onOpenChange:()=>l(null),children:e.jsxs(A,{children:[e.jsxs(I,{children:[e.jsx(T,{children:"Você tem certeza?"}),e.jsxs(E,{children:["Esta ação não pode ser desfeita. Isso excluirá permanentemente a vacina",d?.name," e todas as suas doses."]})]}),e.jsxs(F,{children:[e.jsx($,{children:"Cancelar"}),e.jsx(R,{onClick:async()=>{try{const{error:e}=await p.from("pedbook_vaccines").delete().eq("id",d.id);if(e)throw e;m({title:"Vacina removida com sucesso!",description:`A vacina ${d.name} foi removida.`,className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"}),x.invalidateQueries({queryKey:["vaccines"]})}catch(e){m({variant:"destructive",title:"Erro ao remover vacina",description:e.message,className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})}finally{l(null)}},children:"Continuar"})]})]})})]})}function G(){const[a,i]=s.useState(!1);h();const{data:r,isLoading:o}=n({queryKey:["vaccines"],queryFn:async()=>{const{data:e,error:s}=await p.from("pedbook_vaccines").select("\n          id,\n          name,\n          description,\n          pedbook_vaccine_doses (\n            id,\n            dose_number,\n            age_recommendation,\n            description,\n            type\n          )\n        ").order("name");if(s)throw s;return e}});return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Gerenciamento de Vacinas"}),e.jsx(g,{onClick:()=>i(!0),children:"Adicionar Vacina"})]}),o?e.jsx("div",{className:"flex justify-center",children:e.jsx("p",{children:"Carregando vacinas..."})}):e.jsx(B,{vaccines:r||[]}),e.jsx(P,{open:a,onOpenChange:i})]})}export{G as default};
