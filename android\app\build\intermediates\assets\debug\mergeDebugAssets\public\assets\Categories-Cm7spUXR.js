import{j as e}from"./radix-core-6kBL75b5.js";import{r as a}from"./critical-DVX9Inzy.js";import{a as r,u as s}from"./query-vendor-B-7l6Nb3.js";import{d as t,D as i,e as o,f as c,g as n,an as l,a5 as d,T as m,B as u,s as p,ac as x}from"./index-BGVWLj2Q.js";import{A as g,a as h,b as j,c as f,d as v,e as y,f as b,g as w}from"./alert-dialog-DT5Ljgh1.js";import{U as C}from"./upload-CzCSlwqr.js";import{T as N}from"./trash-2-DCVA5SE5.js";import{P as _}from"./plus-P3v-LB0m.js";import{P as k}from"./pencil-Dc2kPNd2.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const q=["from-blue-500/20 to-blue-600/10 text-blue-700","from-red-500/20 to-red-600/10 text-red-700","from-green-500/20 to-green-600/10 text-green-700","from-purple-500/20 to-purple-600/10 text-purple-700","from-yellow-500/20 to-yellow-600/10 text-yellow-700","from-pink-500/20 to-pink-600/10 text-pink-700","from-cyan-500/20 to-cyan-600/10 text-cyan-700","from-orange-500/20 to-orange-600/10 text-orange-700"];function S({category:s,isOpen:x,onClose:_}){const[k,S]=a.useState(s?.name||""),[E,F]=a.useState(s?.description||""),[$,O]=a.useState(s?.color||q[0]),[z,A]=a.useState(null),[L,D]=a.useState(s?.icon_url||null),[P,U]=a.useState(!1),{toast:K}=t(),T=r();a.useEffect((()=>{x&&(S(s?.name||""),F(s?.description||""),O(s?.color||q[0]),D(s?.icon_url||null),A(null))}),[x,s]);const Q=async e=>{if(!z)return null;const a=`${e}.${z.name.split(".").pop()}`,{error:r}=await p.storage.from("category-icons").upload(a,z,{upsert:!0});if(r)throw r;const{data:s}=p.storage.from("category-icons").getPublicUrl(a);return s.publicUrl};return e.jsxs(e.Fragment,{children:[e.jsx(i,{open:x,onOpenChange:_,children:e.jsxs(o,{children:[e.jsx(c,{children:e.jsx(n,{children:s?"Editar Categoria":"Nova Categoria"})}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{let e=s?.icon_url;if(s){z&&(e=await Q(s.id));const{error:a}=await p.from("pedbook_medication_categories").update({name:k,description:E,color:$,icon_url:e}).eq("id",s.id);if(a)throw a;K({title:"Categoria atualizada com sucesso!",description:`A categoria ${k} foi atualizada.`})}else{const{data:a,error:r}=await p.from("pedbook_medication_categories").insert([{name:k,description:E,color:$}]).select().single();if(r)throw r;if(z&&a){e=await Q(a.id);const{error:r}=await p.from("pedbook_medication_categories").update({icon_url:e}).eq("id",a.id);if(r)throw r}K({title:"Categoria criada com sucesso!",description:`A categoria ${k} foi adicionada.`})}T.invalidateQueries({queryKey:["medication-categories"]}),_()}catch(a){K({variant:"destructive",title:"Erro ao salvar categoria",description:a.message||"Ocorreu um erro ao salvar a categoria."})}},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"name",children:"Nome"}),e.jsx(d,{id:"name",value:k,onChange:e=>S(e.target.value),required:!0})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"description",children:"Descrição"}),e.jsx(m,{id:"description",value:E,onChange:e=>F(e.target.value)})]}),e.jsxs("div",{children:[e.jsx(l,{children:"Cor do Gradiente"}),e.jsx("div",{className:"grid grid-cols-4 gap-2 mt-1",children:q.map((a=>e.jsx("button",{type:"button",className:`h-8 rounded-md bg-gradient-to-r ${a} ${$===a?"ring-2 ring-primary":""}`,onClick:()=>O(a)},a)))})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"icon",children:"Ícone"}),e.jsxs("div",{className:"mt-1 flex items-center gap-4",children:[L&&e.jsx("img",{src:L,alt:"Category icon preview",className:"h-12 w-12 object-cover rounded-lg"}),e.jsxs(l,{htmlFor:"icon-upload",className:"cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Escolher arquivo"]}),e.jsx(d,{id:"icon-upload",type:"file",accept:"image/*",onChange:e=>{const a=e.target.files?.[0];if(a){A(a);const e=new FileReader;e.onloadend=()=>{D(e.result)},e.readAsDataURL(a)}},className:"hidden"})]})]}),e.jsxs("div",{className:"flex justify-between",children:[s&&e.jsxs(u,{type:"button",variant:"destructive",onClick:()=>U(!0),children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Excluir"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(u,{type:"button",variant:"outline",onClick:_,children:"Cancelar"}),e.jsx(u,{type:"submit",children:"Salvar"})]})]})]})]})}),e.jsx(g,{open:P,onOpenChange:U,children:e.jsxs(h,{children:[e.jsxs(j,{children:[e.jsx(f,{children:"Confirmar exclusão"}),e.jsxs(v,{children:['Tem certeza que deseja excluir a categoria "',s?.name,'"? Esta ação não pode ser desfeita.']})]}),e.jsxs(y,{children:[e.jsx(b,{children:"Cancelar"}),e.jsx(w,{onClick:async()=>{try{if(s?.icon_url){const e=s.icon_url.split("/").pop();e&&await p.storage.from("category-icons").remove([e])}const{error:e}=await p.from("pedbook_medication_categories").delete().eq("id",s?.id);if(e)throw e;K({title:"Categoria excluída com sucesso!",description:`A categoria ${s?.name} foi excluída.`}),T.invalidateQueries({queryKey:["medication-categories"]}),U(!1),_()}catch(e){K({variant:"destructive",title:"Erro ao excluir categoria",description:e.message||"Ocorreu um erro ao excluir a categoria."})}},className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Excluir"})]})]})})]})}function E(){const[r,t]=a.useState(""),[i,o]=a.useState(null),[c,n]=a.useState(!1),{data:l}=s({queryKey:["medication-categories"],queryFn:async()=>{const{data:e,error:a}=await p.from("pedbook_medication_categories").select("*").order("name");if(a)throw a;return e}}),m=l?.filter((e=>e.name.toLowerCase().includes(r.toLowerCase())||e.description?.toLowerCase().includes(r.toLowerCase())));return e.jsxs("div",{className:"container mx-auto py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Categorias de Medicamentos"}),e.jsxs(u,{onClick:()=>{o(null),n(!0)},children:[e.jsx(_,{className:"mr-2 h-4 w-4"}),"Nova Categoria"]})]}),e.jsxs("div",{className:"relative mb-6",children:[e.jsx(x,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",size:18}),e.jsx(d,{type:"search",placeholder:"Pesquisar categorias...",value:r,onChange:e=>t(e.target.value),className:"pl-10"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m?.map((a=>e.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6 space-y-2",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold",children:a.name}),a.description&&e.jsx("p",{className:"text-gray-600 mt-1",children:a.description})]}),e.jsx(u,{variant:"ghost",size:"icon",onClick:()=>{o(a),n(!0)},children:e.jsx(k,{className:"h-4 w-4"})})]})},a.id)))}),e.jsx(S,{category:i,isOpen:c,onClose:()=>{n(!1),o(null)}})]})}export{E as default};
