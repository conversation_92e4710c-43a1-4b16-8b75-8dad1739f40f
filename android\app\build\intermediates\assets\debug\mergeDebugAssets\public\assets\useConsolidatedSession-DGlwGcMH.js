import{u as s,a as e}from"./query-vendor-B-7l6Nb3.js";import{u as t,s as i}from"./index-DV3Span9.js";import"./critical-DVX9Inzy.js";import"./radix-core-6kBL75b5.js";import"./supabase-vendor-qi_Ptfv-.js";import"./router-BAzpOxbo.js";import"./form-vendor-rYZw_ur7.js";import"./radix-forms-DX-owj97.js";import"./radix-interactive-DJo-0Sg_.js";import"./radix-toast-1_gbKn9f.js";import"./radix-feedback-dpGNY8wJ.js";import"./radix-popover-DQqTw7_-.js";import"./radix-layout-CC8mXA4O.js";const n=e=>{const{user:n}=t();return s({queryKey:["consolidated-session-data",e,n?.id],queryFn:async()=>{if(!e||!n?.id)return{session:null,sessionEvents:[],userAnswers:[],activeSession:null};const[s,t,r,a]=await Promise.allSettled([i.from("study_sessions").select("\n            id,\n            title,\n            user_id,\n            status,\n            total_questions,\n            current_question_index,\n            questions,\n            stats,\n            started_at,\n            completed_at,\n            specialty_id,\n            theme_id,\n            focus_id,\n            knowledge_domain,\n            question_times,\n            total_time_spent\n          ").eq("id",e).single(),i.from("session_events").select("question_id, response_status, response").eq("session_id",e),i.from("user_answers").select("question_id, is_correct, text_answer, selected_answer, ai_analyzed").eq("session_id",e).eq("user_id",n.id),i.from("study_sessions").select("*").eq("user_id",n.id).eq("status","in_progress").order("started_at",{ascending:!1}).limit(1)]);return{session:"fulfilled"===s.status?s.value.data:null,sessionEvents:"fulfilled"===t.status&&t.value.data||[],userAnswers:"fulfilled"===r.status&&r.value.data||[],activeSession:"fulfilled"===a.status&&a.value.data?.[0]||null}},enabled:!!e&&!!n?.id,staleTime:3e4,gcTime:12e4})},r=s=>{const{data:e,isLoading:t,error:i}=n(s);return{session:e?.session,isLoading:t,error:i}},a=()=>{const{user:e}=t();return s({queryKey:["active-session",e?.id],queryFn:async()=>{if(!e?.id)return null;const{data:s,error:t}=await i.from("study_sessions").select("*").eq("user_id",e.id).eq("status","in_progress").order("started_at",{ascending:!1}).limit(1).maybeSingle();if(t)throw t;return s},enabled:!!e?.id,staleTime:3e4,gcTime:12e4})},o=()=>{const s=e();return{invalidateSessionCaches:e=>{s.invalidateQueries({predicate:s=>{const t=s.queryKey[0];return"consolidated-session-data"===t||"active-session"===t||"study-sessions-consolidated"===t||e&&s.queryKey.includes(e)}})}}};export{a as useActiveSession,n as useConsolidatedSession,r as useSessionBasicData,o as useSessionCacheInvalidation};
